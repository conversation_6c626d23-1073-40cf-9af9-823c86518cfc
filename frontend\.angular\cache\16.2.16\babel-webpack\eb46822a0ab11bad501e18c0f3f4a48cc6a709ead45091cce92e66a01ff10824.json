{"ast": null, "code": "export function iterateObserversSafely(observers, method, argument) {\n  // In case observers is modified during iteration, we need to commit to the\n  // original elements, which also provides an opportunity to filter them down\n  // to just the observers with the given method.\n  var observersWithMethod = [];\n  observers.forEach(function (obs) {\n    return obs[method] && observersWithMethod.push(obs);\n  });\n  observersWithMethod.forEach(function (obs) {\n    return obs[method](argument);\n  });\n}", "map": {"version": 3, "names": ["iterateObserversSafely", "observers", "method", "argument", "observersWithMethod", "for<PERSON>ach", "obs", "push"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/observables/iteration.js"], "sourcesContent": ["export function iterateObserversSafely(observers, method, argument) {\n    // In case observers is modified during iteration, we need to commit to the\n    // original elements, which also provides an opportunity to filter them down\n    // to just the observers with the given method.\n    var observersWithMethod = [];\n    observers.forEach(function (obs) { return obs[method] && observersWithMethod.push(obs); });\n    observersWithMethod.forEach(function (obs) { return obs[method](argument); });\n}\n"], "mappings": "AAAA,OAAO,SAASA,sBAAsBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAChE;EACA;EACA;EACA,IAAIC,mBAAmB,GAAG,EAAE;EAC5BH,SAAS,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;IAAE,OAAOA,GAAG,CAACJ,MAAM,CAAC,IAAIE,mBAAmB,CAACG,IAAI,CAACD,GAAG,CAAC;EAAE,CAAC,CAAC;EAC1FF,mBAAmB,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;IAAE,OAAOA,GAAG,CAACJ,MAAM,CAAC,CAACC,QAAQ,CAAC;EAAE,CAAC,CAAC;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}