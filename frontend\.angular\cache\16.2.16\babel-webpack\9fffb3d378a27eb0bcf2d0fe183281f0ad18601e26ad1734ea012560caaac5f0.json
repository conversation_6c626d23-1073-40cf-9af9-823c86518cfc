{"ast": null, "code": "import { millisecondsInMinute } from \"./constants.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMinutes} function options.\n */\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n * @param options - An object with options\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  _date.setTime(_date.getTime() + amount * millisecondsInMinute);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;", "map": {"version": 3, "names": ["millisecondsInMinute", "toDate", "addMinutes", "date", "amount", "options", "_date", "in", "setTime", "getTime"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/addMinutes.js"], "sourcesContent": ["import { millisecondsInMinute } from \"./constants.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMinutes} function options.\n */\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n * @param options - An object with options\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  _date.setTime(_date.getTime() + amount * millisecondsInMinute);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAChD,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGL,MAAM,GAAGJ,oBAAoB,CAAC;EAC9D,OAAOM,KAAK;AACd;;AAEA;AACA,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}