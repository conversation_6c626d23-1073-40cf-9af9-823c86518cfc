import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from 'src/app/services/auth.service';
import { finalize } from 'rxjs/operators';
import { AuthuserService } from 'src/app/services/authuser.service';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { User } from 'src/app/models/user.model';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
})
export class ProfileComponent implements OnInit {
  user: any;
  selectedImage: File | null = null;
  previewUrl: string | null = null;
  message = '';
  error = '';
  uploadLoading = false;
  removeLoading = false;

  // Edit profile functionality
  isEditMode = false;
  editForm: FormGroup;
  editLoading = false;
  progressPercentage = 0;

  constructor(
    private authService: AuthService,
    private authuserService: AuthuserService,
    private dataService: DataService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.editForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      dateOfBirth: [''],
      phoneNumber: ['', [Validators.pattern(/^[0-9+\-\s()]+$/)]],
      department: [''],
      position: [''],
      bio: ['', [Validators.minLength(10)]],
      address: [''],
      skills: ['']
    });
  }

  ngOnInit(): void {
    // Load user profile using DataService
    this.dataService.getProfile().subscribe({
      next: (res) => {
        this.user = res;

        // Ensure image properties are consistent
        if (!this.user.profileImage && this.user.image) {
          this.user.profileImage = this.user.image;
        } else if (!this.user.image && this.user.profileImage) {
          this.user.image = this.user.profileImage;
        }

        // If no image is available, use default
        if (
          !this.user.profileImage ||
          this.user.profileImage === 'null' ||
          this.user.profileImage.trim() === ''
        ) {
          this.user.profileImage = 'assets/images/default-profile.png';
          this.user.image = 'assets/images/default-profile.png';
        }

        // Ensure profileImageURL is also set for backward compatibility
        if (!this.user.profileImageURL) {
          this.user.profileImageURL = this.user.profileImage || this.user.image;
        }

        // Calculate profile completion percentage
        this.calculateProfileCompletion();

        // Populate edit form with current user data
        this.populateEditForm();
      },
      error: () => {
        this.error = 'Failed to load profile.';
      },
    });
  }

  calculateProfileCompletion(): void {
    if (!this.user) return;

    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];
    const optionalFields = ['position', 'address', 'skills'];

    let completedRequired = 0;
    let completedOptional = 0;

    // Check required fields
    requiredFields.forEach(field => {
      const value = this.user[field];
      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {
        completedRequired++;
      }
    });

    // Check optional fields
    optionalFields.forEach(field => {
      const value = this.user[field];
      if (value && value.toString().trim() !== '') {
        completedOptional++;
      }
    });

    // Check profile image
    let hasProfileImage = 0;
    if (this.user.profileImage &&
        this.user.profileImage !== 'uploads/default.png' &&
        this.user.profileImage !== 'assets/images/default-profile.png' &&
        this.user.profileImage.trim() !== '') {
      hasProfileImage = 1;
    }

    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)
    const requiredPercentage = (completedRequired / requiredFields.length) * 60;
    const optionalPercentage = (completedOptional / optionalFields.length) * 30;
    const imagePercentage = hasProfileImage * 10;

    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);
  }

  populateEditForm(): void {
    if (!this.user) return;

    this.editForm.patchValue({
      firstName: this.user.firstName || '',
      lastName: this.user.lastName || '',
      fullName: this.user.fullName || '',
      email: this.user.email || '',
      dateOfBirth: this.user.dateOfBirth || '',
      phoneNumber: this.user.phoneNumber || '',
      department: this.user.department || '',
      position: this.user.position || '',
      bio: this.user.bio || '',
      address: this.user.address || '',
      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : (this.user.skills || '')
    });
  }

  /**
   * Returns the appropriate profile image URL based on available properties
   * Uses the same logic as in front-layout component for consistency
   */
  getProfileImageUrl(): string {
    if (!this.user) return 'assets/images/default-profile.png';

    // Check profileImage first
    if (
      this.user.profileImage &&
      this.user.profileImage !== 'null' &&
      this.user.profileImage.trim() !== ''
    ) {
      return this.user.profileImage;
    }

    // Then check image
    if (
      this.user.image &&
      this.user.image !== 'null' &&
      this.user.image.trim() !== ''
    ) {
      return this.user.image;
    }

    // Then check profileImageURL (for backward compatibility)
    if (
      this.user.profileImageURL &&
      this.user.profileImageURL !== 'null' &&
      this.user.profileImageURL.trim() !== ''
    ) {
      return this.user.profileImageURL;
    }

    // Default fallback
    return 'assets/images/default-profile.png';
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];

      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';
        this.resetFileInput();
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        this.error = "L'image ne doit pas dépasser 2MB";
        this.resetFileInput();
        return;
      }

      this.selectedImage = file;
      this.error = '';

      const reader = new FileReader();
      reader.onload = (e) => {
        this.previewUrl = (e.target?.result as string) || null;
      };
      reader.readAsDataURL(file);
    }
  }

  onUpload(): void {
    if (!this.selectedImage) return;

    this.uploadLoading = true; // Activer l'état de chargement
    this.message = '';
    this.error = '';

    console.log('Upload started, uploadLoading:', this.uploadLoading);

    this.dataService
      .uploadProfileImage(this.selectedImage)
      .pipe(
        finalize(() => {
          this.uploadLoading = false;
          console.log('Upload finished, uploadLoading:', this.uploadLoading);
        })
      )
      .subscribe({
        next: (response: any) => {
          this.message = response.message || 'Profile updated successfully';

          // Update all image properties to ensure consistency across the application
          this.user.profileImageURL = response.imageUrl;
          this.user.profileImage = response.imageUrl;
          this.user.image = response.imageUrl;

          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout
          this.dataService.updateCurrentUser({
            profileImage: response.imageUrl,
            image: response.imageUrl,
          });

          // Also update in AuthUserService to ensure all components are updated
          this.authuserService.setCurrentUser({
            ...this.user,
            profileImage: response.imageUrl,
            image: response.imageUrl,
          });

          this.selectedImage = null;
          this.previewUrl = null;
          this.resetFileInput();

          if (response.token) {
            localStorage.setItem('token', response.token);
          }

          // Auto-hide message after 3 seconds
          setTimeout(() => {
            this.message = '';
          }, 3000);
        },
        error: (err: { error: { message: string } }) => {
          this.error = err.error?.message || 'Upload failed';
          // Auto-hide error after 3 seconds
          setTimeout(() => {
            this.error = '';
          }, 3000);
        },
      });
  }

  removeProfileImage(): void {
    if (!confirm('Are you sure you want to remove your profile picture?'))
      return;

    this.removeLoading = true;
    this.message = '';
    this.error = '';

    this.dataService
      .removeProfileImage()
      .pipe(finalize(() => (this.removeLoading = false)))
      .subscribe({
        next: (response: any) => {
          this.message =
            response.message || 'Profile picture removed successfully';

          // Update all image properties to ensure consistency across the application
          this.user.profileImageURL = null;
          this.user.profileImage = null;
          this.user.image = null;

          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout
          this.dataService.updateCurrentUser({
            profileImage: 'assets/images/default-profile.png',
            image: 'assets/images/default-profile.png',
          });

          // Also update in AuthUserService to ensure all components are updated
          this.authuserService.setCurrentUser({
            ...this.user,
            profileImage: 'assets/images/default-profile.png',
            image: 'assets/images/default-profile.png',
          });

          if (response.token) {
            localStorage.setItem('token', response.token);
          }

          // Auto-hide message after 3 seconds
          setTimeout(() => {
            this.message = '';
          }, 3000);
        },
        error: (err: { error: { message: string } }) => {
          this.error = err.error?.message || 'Removal failed';
          // Auto-hide error after 3 seconds
          setTimeout(() => {
            this.error = '';
          }, 3000);
        },
      });
  }

  private resetFileInput(): void {
    this.selectedImage = null;
    this.previewUrl = null;
    const fileInput = document.getElementById(
      'profile-upload'
    ) as HTMLInputElement;
    if (fileInput) fileInput.value = '';
  }

  navigateTo(path: string): void {
    this.router.navigate([path]);
  }

  // Edit profile methods
  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    if (this.isEditMode) {
      this.populateEditForm();
    }
    this.message = '';
    this.error = '';
  }

  onEditSubmit(): void {
    if (this.editForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.editLoading = true;
    this.error = '';
    this.message = '';

    const formData = new FormData();

    // Add form fields
    Object.keys(this.editForm.value).forEach(key => {
      const value = this.editForm.value[key];
      if (key === 'skills' && value) {
        // Convert skills string to array
        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);
        formData.append(key, JSON.stringify(skillsArray));
      } else if (value) {
        formData.append(key, value);
      }
    });

    // Add profile image if selected
    if (this.selectedImage) {
      formData.append('image', this.selectedImage);
    }

    this.dataService.completeProfile(formData).subscribe({
      next: (response: any) => {
        this.editLoading = false;
        this.message = 'Profile updated successfully!';

        // Update current user data
        this.user = { ...this.user, ...response.user };
        this.authuserService.setCurrentUser(this.user);

        // Recalculate progress
        this.calculateProfileCompletion();

        // Exit edit mode
        this.isEditMode = false;

        // Clear selected image
        this.selectedImage = null;
        this.previewUrl = null;
        this.resetFileInput();

        // Auto-hide message after 3 seconds
        setTimeout(() => {
          this.message = '';
        }, 3000);
      },
      error: (err) => {
        this.editLoading = false;
        this.error = err.error?.message || 'An error occurred while updating your profile.';

        // Auto-hide error after 5 seconds
        setTimeout(() => {
          this.error = '';
        }, 5000);
      }
    });
  }

  cancelEdit(): void {
    this.isEditMode = false;
    this.populateEditForm(); // Reset form to original values
    this.selectedImage = null;
    this.previewUrl = null;
    this.resetFileInput();
    this.message = '';
    this.error = '';
  }

  private markFormGroupTouched(): void {
    Object.keys(this.editForm.controls).forEach(key => {
      this.editForm.get(key)?.markAsTouched();
    });
  }

  // Helper methods for template
  getFieldError(fieldName: string): string {
    const field = this.editForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['minlength']) return `${fieldName} is too short`;
      if (field.errors['email']) return `Invalid email format`;
      if (field.errors['pattern']) return `${fieldName} format is invalid`;
    }
    return '';
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.editForm.get(fieldName);
    return !!(field?.invalid && field.touched);
  }

  getProgressColor(): string {
    if (this.progressPercentage < 25) return '#ef4444'; // red
    if (this.progressPercentage < 50) return '#f97316'; // orange
    if (this.progressPercentage < 75) return '#eab308'; // yellow
    if (this.progressPercentage < 100) return '#22c55e'; // green
    return '#10b981'; // emerald
  }

  getMotivationalMessage(): string {
    if (this.progressPercentage < 25) {
      return "Let's complete your profile to unlock all features! 🚀";
    } else if (this.progressPercentage < 50) {
      return "You're making great progress! Keep going! 💪";
    } else if (this.progressPercentage < 75) {
      return "Excellent! You're more than halfway there! 🌟";
    } else if (this.progressPercentage < 100) {
      return "Almost perfect! Just a few more details! 🎯";
    } else {
      return "Perfect! Your profile is complete! ✨";
    }
  }

  logout(): void {
    this.authuserService.logout().subscribe({
      next: () => {
        this.authuserService.clearAuthData();
        setTimeout(() => {
          this.router.navigate(['/login'], {
            queryParams: { message: 'Déconnexion réussie' },
            replaceUrl: true,
          });
        }, 100);
      },
      error: (err: any) => {
        console.error('Logout error:', err);
        this.authuserService.clearAuthData();
        setTimeout(() => {
          this.router.navigate(['/login'], {});
        }, 100);
      },
    });
  }
}
