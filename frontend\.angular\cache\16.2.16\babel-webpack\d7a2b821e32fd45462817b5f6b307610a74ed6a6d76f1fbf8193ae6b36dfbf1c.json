{"ast": null, "code": "'use strict';\n\nconst ReactNativeFile = require('./ReactNativeFile.js');\n\n/**\n * Checks if a value is an [extractable file]{@link ExtractableFile}.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an [extractable file]{@link ExtractableFile}.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from 'extract-files';\n * ```\n *\n * ```js\n * import isExtractableFile from 'extract-files/public/isExtractableFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require('extract-files');\n * ```\n *\n * ```js\n * const isExtractableFile = require('extract-files/public/isExtractableFile.js');\n * ```\n */\nmodule.exports = function isExtractableFile(value) {\n  return typeof File !== 'undefined' && value instanceof File || typeof Blob !== 'undefined' && value instanceof Blob || value instanceof ReactNativeFile;\n};", "map": {"version": 3, "names": ["ReactNativeFile", "require", "module", "exports", "isExtractableFile", "value", "File", "Blob"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/extract-files/public/isExtractableFile.js"], "sourcesContent": ["'use strict';\n\nconst ReactNativeFile = require('./ReactNativeFile.js');\n\n/**\n * Checks if a value is an [extractable file]{@link ExtractableFile}.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an [extractable file]{@link ExtractableFile}.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from 'extract-files';\n * ```\n *\n * ```js\n * import isExtractableFile from 'extract-files/public/isExtractableFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require('extract-files');\n * ```\n *\n * ```js\n * const isExtractableFile = require('extract-files/public/isExtractableFile.js');\n * ```\n */\nmodule.exports = function isExtractableFile(value) {\n  return (\n    (typeof File !== 'undefined' && value instanceof File) ||\n    (typeof Blob !== 'undefined' && value instanceof Blob) ||\n    value instanceof ReactNativeFile\n  );\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,eAAe,GAAGC,OAAO,CAAC,sBAAsB,CAAC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACjD,OACG,OAAOC,IAAI,KAAK,WAAW,IAAID,KAAK,YAAYC,IAAI,IACpD,OAAOC,IAAI,KAAK,WAAW,IAAIF,KAAK,YAAYE,IAAK,IACtDF,KAAK,YAAYL,eAAe;AAEpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}