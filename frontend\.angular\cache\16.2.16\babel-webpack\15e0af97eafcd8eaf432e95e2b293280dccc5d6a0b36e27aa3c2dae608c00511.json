{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { responseIterator } from \"./responseIterator.js\";\nimport { throwServerError } from \"../utils/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { isApolloPayloadResult } from \"../../utilities/common/incrementalResult.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function readMultipartBody(response, nextValue) {\n  return __awaiter(this, void 0, void 0, function () {\n    var decoder, contentType, delimiter, boundaryVal, boundary, buffer, iterator, running, _a, value, done, chunk, searchFrom, bi, message, i, headers, contentType_1, body, result, next;\n    var _b, _c;\n    var _d;\n    return __generator(this, function (_e) {\n      switch (_e.label) {\n        case 0:\n          if (TextDecoder === undefined) {\n            throw new Error(\"TextDecoder must be defined in the environment: please import a polyfill.\");\n          }\n          decoder = new TextDecoder(\"utf-8\");\n          contentType = (_d = response.headers) === null || _d === void 0 ? void 0 : _d.get(\"content-type\");\n          delimiter = \"boundary=\";\n          boundaryVal = (contentType === null || contentType === void 0 ? void 0 : contentType.includes(delimiter)) ? contentType === null || contentType === void 0 ? void 0 : contentType.substring((contentType === null || contentType === void 0 ? void 0 : contentType.indexOf(delimiter)) + delimiter.length).replace(/['\"]/g, \"\").replace(/\\;(.*)/gm, \"\").trim() : \"-\";\n          boundary = \"\\r\\n--\".concat(boundaryVal);\n          buffer = \"\";\n          iterator = responseIterator(response);\n          running = true;\n          _e.label = 1;\n        case 1:\n          if (!running) return [3 /*break*/, 3];\n          return [4 /*yield*/, iterator.next()];\n        case 2:\n          _a = _e.sent(), value = _a.value, done = _a.done;\n          chunk = typeof value === \"string\" ? value : decoder.decode(value);\n          searchFrom = buffer.length - boundary.length + 1;\n          running = !done;\n          buffer += chunk;\n          bi = buffer.indexOf(boundary, searchFrom);\n          while (bi > -1) {\n            message = void 0;\n            _b = [buffer.slice(0, bi), buffer.slice(bi + boundary.length)], message = _b[0], buffer = _b[1];\n            i = message.indexOf(\"\\r\\n\\r\\n\");\n            headers = parseHeaders(message.slice(0, i));\n            contentType_1 = headers[\"content-type\"];\n            if (contentType_1 && contentType_1.toLowerCase().indexOf(\"application/json\") === -1) {\n              throw new Error(\"Unsupported patch content type: application/json is required.\");\n            }\n            body = message.slice(i);\n            if (body) {\n              result = parseJsonBody(response, body);\n              if (Object.keys(result).length > 1 || \"data\" in result || \"incremental\" in result || \"errors\" in result || \"payload\" in result) {\n                if (isApolloPayloadResult(result)) {\n                  next = {};\n                  if (\"payload\" in result) {\n                    if (Object.keys(result).length === 1 && result.payload === null) {\n                      return [2 /*return*/];\n                    }\n\n                    next = __assign({}, result.payload);\n                  }\n                  if (\"errors\" in result) {\n                    next = __assign(__assign({}, next), {\n                      extensions: __assign(__assign({}, \"extensions\" in next ? next.extensions : null), (_c = {}, _c[PROTOCOL_ERRORS_SYMBOL] = result.errors, _c))\n                    });\n                  }\n                  nextValue(next);\n                } else {\n                  // for the last chunk with only `hasNext: false`\n                  // we don't need to call observer.next as there is no data/errors\n                  nextValue(result);\n                }\n              } else if (\n              // If the chunk contains only a \"hasNext: false\", we can call\n              // observer.complete() immediately.\n              Object.keys(result).length === 1 && \"hasNext\" in result && !result.hasNext) {\n                return [2 /*return*/];\n              }\n            }\n\n            bi = buffer.indexOf(boundary);\n          }\n          return [3 /*break*/, 1];\n        case 3:\n          return [2 /*return*/];\n      }\n    });\n  });\n}\n\nexport function parseHeaders(headerText) {\n  var headersInit = {};\n  headerText.split(\"\\n\").forEach(function (line) {\n    var i = line.indexOf(\":\");\n    if (i > -1) {\n      // normalize headers to lowercase\n      var name_1 = line.slice(0, i).trim().toLowerCase();\n      var value = line.slice(i + 1).trim();\n      headersInit[name_1] = value;\n    }\n  });\n  return headersInit;\n}\nexport function parseJsonBody(response, bodyText) {\n  if (response.status >= 300) {\n    // Network error\n    var getResult = function () {\n      try {\n        return JSON.parse(bodyText);\n      } catch (err) {\n        return bodyText;\n      }\n    };\n    throwServerError(response, getResult(), \"Response not successful: Received status code \".concat(response.status));\n  }\n  try {\n    return JSON.parse(bodyText);\n  } catch (err) {\n    var parseError = err;\n    parseError.name = \"ServerParseError\";\n    parseError.response = response;\n    parseError.statusCode = response.status;\n    parseError.bodyText = bodyText;\n    throw parseError;\n  }\n}\nexport function handleError(err, observer) {\n  // if it is a network error, BUT there is graphql result info fire\n  // the next observer before calling error this gives apollo-client\n  // (and react-apollo) the `graphqlErrors` and `networkErrors` to\n  // pass to UI this should only happen if we *also* have data as\n  // part of the response key per the spec\n  if (err.result && err.result.errors && err.result.data) {\n    // if we don't call next, the UI can only show networkError\n    // because AC didn't get any graphqlErrors this is graphql\n    // execution result info (i.e errors and possibly data) this is\n    // because there is no formal spec how errors should translate to\n    // http status codes. So an auth error (401) could have both data\n    // from a public field, errors from a private field, and a status\n    // of 401\n    // {\n    //  user { // this will have errors\n    //    firstName\n    //  }\n    //  products { // this is public so will have data\n    //    cost\n    //  }\n    // }\n    //\n    // the result of above *could* look like this:\n    // {\n    //   data: { products: [{ cost: \"$10\" }] },\n    //   errors: [{\n    //      message: 'your session has timed out',\n    //      path: []\n    //   }]\n    // }\n    // status code of above would be a 401\n    // in the UI you want to show data where you can, errors as data where you can\n    // and use correct http status codes\n    observer.next(err.result);\n  }\n  observer.error(err);\n}\nexport function parseAndCheckHttpResponse(operations) {\n  return function (response) {\n    return response.text().then(function (bodyText) {\n      return parseJsonBody(response, bodyText);\n    }).then(function (result) {\n      if (!Array.isArray(result) && !hasOwnProperty.call(result, \"data\") && !hasOwnProperty.call(result, \"errors\")) {\n        // Data error\n        throwServerError(response, result, \"Server response was missing for query '\".concat(Array.isArray(operations) ? operations.map(function (op) {\n          return op.operationName;\n        }) : operations.operationName, \"'.\"));\n      }\n      return result;\n    });\n  };\n}", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "responseIterator", "throwServerError", "PROTOCOL_ERRORS_SYMBOL", "isApolloPayloadResult", "hasOwnProperty", "Object", "prototype", "readMultipartBody", "response", "nextValue", "decoder", "contentType", "delimiter", "boundaryVal", "boundary", "buffer", "iterator", "running", "_a", "value", "done", "chunk", "searchFrom", "bi", "message", "i", "headers", "contentType_1", "body", "result", "next", "_b", "_c", "_d", "_e", "label", "TextDecoder", "undefined", "Error", "get", "includes", "substring", "indexOf", "length", "replace", "trim", "concat", "sent", "decode", "slice", "parseHeaders", "toLowerCase", "parseJsonBody", "keys", "payload", "extensions", "errors", "hasNext", "headerText", "headersInit", "split", "for<PERSON>ach", "line", "name_1", "bodyText", "status", "getResult", "JSON", "parse", "err", "parseError", "name", "statusCode", "handleError", "observer", "data", "error", "parseAndCheckHttpResponse", "operations", "text", "then", "Array", "isArray", "call", "map", "op", "operationName"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/parseAndCheckHttpResponse.js"], "sourcesContent": ["import { __assign, __awaiter, __generator } from \"tslib\";\nimport { responseIterator } from \"./responseIterator.js\";\nimport { throwServerError } from \"../utils/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { isApolloPayloadResult } from \"../../utilities/common/incrementalResult.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function readMultipartBody(response, nextValue) {\n    return __awaiter(this, void 0, void 0, function () {\n        var decoder, contentType, delimiter, boundaryVal, boundary, buffer, iterator, running, _a, value, done, chunk, searchFrom, bi, message, i, headers, contentType_1, body, result, next;\n        var _b, _c;\n        var _d;\n        return __generator(this, function (_e) {\n            switch (_e.label) {\n                case 0:\n                    if (TextDecoder === undefined) {\n                        throw new Error(\"TextDecoder must be defined in the environment: please import a polyfill.\");\n                    }\n                    decoder = new TextDecoder(\"utf-8\");\n                    contentType = (_d = response.headers) === null || _d === void 0 ? void 0 : _d.get(\"content-type\");\n                    delimiter = \"boundary=\";\n                    boundaryVal = (contentType === null || contentType === void 0 ? void 0 : contentType.includes(delimiter)) ?\n                        contentType === null || contentType === void 0 ? void 0 : contentType.substring((contentType === null || contentType === void 0 ? void 0 : contentType.indexOf(delimiter)) + delimiter.length).replace(/['\"]/g, \"\").replace(/\\;(.*)/gm, \"\").trim()\n                        : \"-\";\n                    boundary = \"\\r\\n--\".concat(boundaryVal);\n                    buffer = \"\";\n                    iterator = responseIterator(response);\n                    running = true;\n                    _e.label = 1;\n                case 1:\n                    if (!running) return [3 /*break*/, 3];\n                    return [4 /*yield*/, iterator.next()];\n                case 2:\n                    _a = _e.sent(), value = _a.value, done = _a.done;\n                    chunk = typeof value === \"string\" ? value : decoder.decode(value);\n                    searchFrom = buffer.length - boundary.length + 1;\n                    running = !done;\n                    buffer += chunk;\n                    bi = buffer.indexOf(boundary, searchFrom);\n                    while (bi > -1) {\n                        message = void 0;\n                        _b = [\n                            buffer.slice(0, bi),\n                            buffer.slice(bi + boundary.length),\n                        ], message = _b[0], buffer = _b[1];\n                        i = message.indexOf(\"\\r\\n\\r\\n\");\n                        headers = parseHeaders(message.slice(0, i));\n                        contentType_1 = headers[\"content-type\"];\n                        if (contentType_1 &&\n                            contentType_1.toLowerCase().indexOf(\"application/json\") === -1) {\n                            throw new Error(\"Unsupported patch content type: application/json is required.\");\n                        }\n                        body = message.slice(i);\n                        if (body) {\n                            result = parseJsonBody(response, body);\n                            if (Object.keys(result).length > 1 ||\n                                \"data\" in result ||\n                                \"incremental\" in result ||\n                                \"errors\" in result ||\n                                \"payload\" in result) {\n                                if (isApolloPayloadResult(result)) {\n                                    next = {};\n                                    if (\"payload\" in result) {\n                                        if (Object.keys(result).length === 1 && result.payload === null) {\n                                            return [2 /*return*/];\n                                        }\n                                        next = __assign({}, result.payload);\n                                    }\n                                    if (\"errors\" in result) {\n                                        next = __assign(__assign({}, next), { extensions: __assign(__assign({}, (\"extensions\" in next ? next.extensions : null)), (_c = {}, _c[PROTOCOL_ERRORS_SYMBOL] = result.errors, _c)) });\n                                    }\n                                    nextValue(next);\n                                }\n                                else {\n                                    // for the last chunk with only `hasNext: false`\n                                    // we don't need to call observer.next as there is no data/errors\n                                    nextValue(result);\n                                }\n                            }\n                            else if (\n                            // If the chunk contains only a \"hasNext: false\", we can call\n                            // observer.complete() immediately.\n                            Object.keys(result).length === 1 &&\n                                \"hasNext\" in result &&\n                                !result.hasNext) {\n                                return [2 /*return*/];\n                            }\n                        }\n                        bi = buffer.indexOf(boundary);\n                    }\n                    return [3 /*break*/, 1];\n                case 3: return [2 /*return*/];\n            }\n        });\n    });\n}\nexport function parseHeaders(headerText) {\n    var headersInit = {};\n    headerText.split(\"\\n\").forEach(function (line) {\n        var i = line.indexOf(\":\");\n        if (i > -1) {\n            // normalize headers to lowercase\n            var name_1 = line.slice(0, i).trim().toLowerCase();\n            var value = line.slice(i + 1).trim();\n            headersInit[name_1] = value;\n        }\n    });\n    return headersInit;\n}\nexport function parseJsonBody(response, bodyText) {\n    if (response.status >= 300) {\n        // Network error\n        var getResult = function () {\n            try {\n                return JSON.parse(bodyText);\n            }\n            catch (err) {\n                return bodyText;\n            }\n        };\n        throwServerError(response, getResult(), \"Response not successful: Received status code \".concat(response.status));\n    }\n    try {\n        return JSON.parse(bodyText);\n    }\n    catch (err) {\n        var parseError = err;\n        parseError.name = \"ServerParseError\";\n        parseError.response = response;\n        parseError.statusCode = response.status;\n        parseError.bodyText = bodyText;\n        throw parseError;\n    }\n}\nexport function handleError(err, observer) {\n    // if it is a network error, BUT there is graphql result info fire\n    // the next observer before calling error this gives apollo-client\n    // (and react-apollo) the `graphqlErrors` and `networkErrors` to\n    // pass to UI this should only happen if we *also* have data as\n    // part of the response key per the spec\n    if (err.result && err.result.errors && err.result.data) {\n        // if we don't call next, the UI can only show networkError\n        // because AC didn't get any graphqlErrors this is graphql\n        // execution result info (i.e errors and possibly data) this is\n        // because there is no formal spec how errors should translate to\n        // http status codes. So an auth error (401) could have both data\n        // from a public field, errors from a private field, and a status\n        // of 401\n        // {\n        //  user { // this will have errors\n        //    firstName\n        //  }\n        //  products { // this is public so will have data\n        //    cost\n        //  }\n        // }\n        //\n        // the result of above *could* look like this:\n        // {\n        //   data: { products: [{ cost: \"$10\" }] },\n        //   errors: [{\n        //      message: 'your session has timed out',\n        //      path: []\n        //   }]\n        // }\n        // status code of above would be a 401\n        // in the UI you want to show data where you can, errors as data where you can\n        // and use correct http status codes\n        observer.next(err.result);\n    }\n    observer.error(err);\n}\nexport function parseAndCheckHttpResponse(operations) {\n    return function (response) {\n        return response\n            .text()\n            .then(function (bodyText) { return parseJsonBody(response, bodyText); })\n            .then(function (result) {\n            if (!Array.isArray(result) &&\n                !hasOwnProperty.call(result, \"data\") &&\n                !hasOwnProperty.call(result, \"errors\")) {\n                // Data error\n                throwServerError(response, result, \"Server response was missing for query '\".concat(Array.isArray(operations) ?\n                    operations.map(function (op) { return op.operationName; })\n                    : operations.operationName, \"'.\"));\n            }\n            return result;\n        });\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,IAAIC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACpD,OAAO,SAASG,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACnD,OAAOX,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IAC/C,IAAIY,OAAO,EAAEC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,EAAE,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,EAAE,EAAEC,OAAO,EAAEC,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI;IACrL,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAIC,EAAE;IACN,OAAOlC,WAAW,CAAC,IAAI,EAAE,UAAUmC,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UACF,IAAIC,WAAW,KAAKC,SAAS,EAAE;YAC3B,MAAM,IAAIC,KAAK,CAAC,2EAA2E,CAAC;UAChG;UACA5B,OAAO,GAAG,IAAI0B,WAAW,CAAC,OAAO,CAAC;UAClCzB,WAAW,GAAG,CAACsB,EAAE,GAAGzB,QAAQ,CAACkB,OAAO,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,GAAG,CAAC,cAAc,CAAC;UACjG3B,SAAS,GAAG,WAAW;UACvBC,WAAW,GAAG,CAACF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC6B,QAAQ,CAAC5B,SAAS,CAAC,IACpGD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC8B,SAAS,CAAC,CAAC9B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC+B,OAAO,CAAC9B,SAAS,CAAC,IAAIA,SAAS,CAAC+B,MAAM,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,GAChP,GAAG;UACT/B,QAAQ,GAAG,QAAQ,CAACgC,MAAM,CAACjC,WAAW,CAAC;UACvCE,MAAM,GAAG,EAAE;UACXC,QAAQ,GAAGhB,gBAAgB,CAACQ,QAAQ,CAAC;UACrCS,OAAO,GAAG,IAAI;UACdiB,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,CAAClB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;UACrC,OAAO,CAAC,CAAC,CAAC,WAAWD,QAAQ,CAACc,IAAI,CAAC,CAAC,CAAC;QACzC,KAAK,CAAC;UACFZ,EAAE,GAAGgB,EAAE,CAACa,IAAI,CAAC,CAAC,EAAE5B,KAAK,GAAGD,EAAE,CAACC,KAAK,EAAEC,IAAI,GAAGF,EAAE,CAACE,IAAI;UAChDC,KAAK,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGT,OAAO,CAACsC,MAAM,CAAC7B,KAAK,CAAC;UACjEG,UAAU,GAAGP,MAAM,CAAC4B,MAAM,GAAG7B,QAAQ,CAAC6B,MAAM,GAAG,CAAC;UAChD1B,OAAO,GAAG,CAACG,IAAI;UACfL,MAAM,IAAIM,KAAK;UACfE,EAAE,GAAGR,MAAM,CAAC2B,OAAO,CAAC5B,QAAQ,EAAEQ,UAAU,CAAC;UACzC,OAAOC,EAAE,GAAG,CAAC,CAAC,EAAE;YACZC,OAAO,GAAG,KAAK,CAAC;YAChBO,EAAE,GAAG,CACDhB,MAAM,CAACkC,KAAK,CAAC,CAAC,EAAE1B,EAAE,CAAC,EACnBR,MAAM,CAACkC,KAAK,CAAC1B,EAAE,GAAGT,QAAQ,CAAC6B,MAAM,CAAC,CACrC,EAAEnB,OAAO,GAAGO,EAAE,CAAC,CAAC,CAAC,EAAEhB,MAAM,GAAGgB,EAAE,CAAC,CAAC,CAAC;YAClCN,CAAC,GAAGD,OAAO,CAACkB,OAAO,CAAC,UAAU,CAAC;YAC/BhB,OAAO,GAAGwB,YAAY,CAAC1B,OAAO,CAACyB,KAAK,CAAC,CAAC,EAAExB,CAAC,CAAC,CAAC;YAC3CE,aAAa,GAAGD,OAAO,CAAC,cAAc,CAAC;YACvC,IAAIC,aAAa,IACbA,aAAa,CAACwB,WAAW,CAAC,CAAC,CAACT,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;cAChE,MAAM,IAAIJ,KAAK,CAAC,+DAA+D,CAAC;YACpF;YACAV,IAAI,GAAGJ,OAAO,CAACyB,KAAK,CAACxB,CAAC,CAAC;YACvB,IAAIG,IAAI,EAAE;cACNC,MAAM,GAAGuB,aAAa,CAAC5C,QAAQ,EAAEoB,IAAI,CAAC;cACtC,IAAIvB,MAAM,CAACgD,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC,IAC9B,MAAM,IAAId,MAAM,IAChB,aAAa,IAAIA,MAAM,IACvB,QAAQ,IAAIA,MAAM,IAClB,SAAS,IAAIA,MAAM,EAAE;gBACrB,IAAI1B,qBAAqB,CAAC0B,MAAM,CAAC,EAAE;kBAC/BC,IAAI,GAAG,CAAC,CAAC;kBACT,IAAI,SAAS,IAAID,MAAM,EAAE;oBACrB,IAAIxB,MAAM,CAACgD,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,KAAK,CAAC,IAAId,MAAM,CAACyB,OAAO,KAAK,IAAI,EAAE;sBAC7D,OAAO,CAAC,CAAC,CAAC,WAAW;oBACzB;;oBACAxB,IAAI,GAAGjC,QAAQ,CAAC,CAAC,CAAC,EAAEgC,MAAM,CAACyB,OAAO,CAAC;kBACvC;kBACA,IAAI,QAAQ,IAAIzB,MAAM,EAAE;oBACpBC,IAAI,GAAGjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiC,IAAI,CAAC,EAAE;sBAAEyB,UAAU,EAAE1D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAG,YAAY,IAAIiC,IAAI,GAAGA,IAAI,CAACyB,UAAU,GAAG,IAAK,CAAC,GAAGvB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAAC9B,sBAAsB,CAAC,GAAG2B,MAAM,CAAC2B,MAAM,EAAExB,EAAE,CAAC;oBAAE,CAAC,CAAC;kBAC3L;kBACAvB,SAAS,CAACqB,IAAI,CAAC;gBACnB,CAAC,MACI;kBACD;kBACA;kBACArB,SAAS,CAACoB,MAAM,CAAC;gBACrB;cACJ,CAAC,MACI;cACL;cACA;cACAxB,MAAM,CAACgD,IAAI,CAACxB,MAAM,CAAC,CAACc,MAAM,KAAK,CAAC,IAC5B,SAAS,IAAId,MAAM,IACnB,CAACA,MAAM,CAAC4B,OAAO,EAAE;gBACjB,OAAO,CAAC,CAAC,CAAC,WAAW;cACzB;YACJ;;YACAlC,EAAE,GAAGR,MAAM,CAAC2B,OAAO,CAAC5B,QAAQ,CAAC;UACjC;UACA,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC3B,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC,WAAW;MACjC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;;AACA,OAAO,SAASoC,YAAYA,CAACQ,UAAU,EAAE;EACrC,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpBD,UAAU,CAACE,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC3C,IAAIrC,CAAC,GAAGqC,IAAI,CAACpB,OAAO,CAAC,GAAG,CAAC;IACzB,IAAIjB,CAAC,GAAG,CAAC,CAAC,EAAE;MACR;MACA,IAAIsC,MAAM,GAAGD,IAAI,CAACb,KAAK,CAAC,CAAC,EAAExB,CAAC,CAAC,CAACoB,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MAClD,IAAIhC,KAAK,GAAG2C,IAAI,CAACb,KAAK,CAACxB,CAAC,GAAG,CAAC,CAAC,CAACoB,IAAI,CAAC,CAAC;MACpCc,WAAW,CAACI,MAAM,CAAC,GAAG5C,KAAK;IAC/B;EACJ,CAAC,CAAC;EACF,OAAOwC,WAAW;AACtB;AACA,OAAO,SAASP,aAAaA,CAAC5C,QAAQ,EAAEwD,QAAQ,EAAE;EAC9C,IAAIxD,QAAQ,CAACyD,MAAM,IAAI,GAAG,EAAE;IACxB;IACA,IAAIC,SAAS,GAAG,SAAAA,CAAA,EAAY;MACxB,IAAI;QACA,OAAOC,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;MAC/B,CAAC,CACD,OAAOK,GAAG,EAAE;QACR,OAAOL,QAAQ;MACnB;IACJ,CAAC;IACD/D,gBAAgB,CAACO,QAAQ,EAAE0D,SAAS,CAAC,CAAC,EAAE,gDAAgD,CAACpB,MAAM,CAACtC,QAAQ,CAACyD,MAAM,CAAC,CAAC;EACrH;EACA,IAAI;IACA,OAAOE,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;EAC/B,CAAC,CACD,OAAOK,GAAG,EAAE;IACR,IAAIC,UAAU,GAAGD,GAAG;IACpBC,UAAU,CAACC,IAAI,GAAG,kBAAkB;IACpCD,UAAU,CAAC9D,QAAQ,GAAGA,QAAQ;IAC9B8D,UAAU,CAACE,UAAU,GAAGhE,QAAQ,CAACyD,MAAM;IACvCK,UAAU,CAACN,QAAQ,GAAGA,QAAQ;IAC9B,MAAMM,UAAU;EACpB;AACJ;AACA,OAAO,SAASG,WAAWA,CAACJ,GAAG,EAAEK,QAAQ,EAAE;EACvC;EACA;EACA;EACA;EACA;EACA,IAAIL,GAAG,CAACxC,MAAM,IAAIwC,GAAG,CAACxC,MAAM,CAAC2B,MAAM,IAAIa,GAAG,CAACxC,MAAM,CAAC8C,IAAI,EAAE;IACpD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAD,QAAQ,CAAC5C,IAAI,CAACuC,GAAG,CAACxC,MAAM,CAAC;EAC7B;EACA6C,QAAQ,CAACE,KAAK,CAACP,GAAG,CAAC;AACvB;AACA,OAAO,SAASQ,yBAAyBA,CAACC,UAAU,EAAE;EAClD,OAAO,UAAUtE,QAAQ,EAAE;IACvB,OAAOA,QAAQ,CACVuE,IAAI,CAAC,CAAC,CACNC,IAAI,CAAC,UAAUhB,QAAQ,EAAE;MAAE,OAAOZ,aAAa,CAAC5C,QAAQ,EAAEwD,QAAQ,CAAC;IAAE,CAAC,CAAC,CACvEgB,IAAI,CAAC,UAAUnD,MAAM,EAAE;MACxB,IAAI,CAACoD,KAAK,CAACC,OAAO,CAACrD,MAAM,CAAC,IACtB,CAACzB,cAAc,CAAC+E,IAAI,CAACtD,MAAM,EAAE,MAAM,CAAC,IACpC,CAACzB,cAAc,CAAC+E,IAAI,CAACtD,MAAM,EAAE,QAAQ,CAAC,EAAE;QACxC;QACA5B,gBAAgB,CAACO,QAAQ,EAAEqB,MAAM,EAAE,yCAAyC,CAACiB,MAAM,CAACmC,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,GACzGA,UAAU,CAACM,GAAG,CAAC,UAAUC,EAAE,EAAE;UAAE,OAAOA,EAAE,CAACC,aAAa;QAAE,CAAC,CAAC,GACxDR,UAAU,CAACQ,aAAa,EAAE,IAAI,CAAC,CAAC;MAC1C;MACA,OAAOzD,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}