{"ast": null, "code": "import { GoogleGenerativeAI } from '@google/generative-ai';\nimport { Observable, from, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class AiService {\n  constructor() {\n    this.apiAvailable = true;\n    // Utiliser une clé API de l'environnement ou une clé de démonstration\n    // Pour une utilisation en production, obtenez une clé API sur https://makersuite.google.com/\n    const apiKey = environment.geminiApiKey || 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88';\n    try {\n      // Initialiser l'API Gemini\n      this.genAI = new GoogleGenerativeAI(apiKey);\n      // Utiliser le modèle gemini-1.5-pro qui est disponible dans la version actuelle de l'API\n      this.model = this.genAI.getGenerativeModel({\n        model: 'gemini-1.5-pro'\n      });\n      console.log('Service AI initialisé avec succès');\n    } catch (error) {\n      console.error('Erreur lors de l\\'initialisation du service AI:', error);\n      // Créer des objets factices pour éviter les erreurs null\n      // Ces objets ne feront rien mais éviteront les erreurs d'exécution\n      this.genAI = {};\n      this.model = {\n        generateContent: () => Promise.resolve({\n          response: {\n            text: () => 'Service AI non disponible'\n          }\n        })\n      };\n      // Marquer l'API comme non disponible\n      this.apiAvailable = false;\n    }\n  }\n  /**\n   * Génère des tâches pour un projet en fonction du nombre de membres\n   * @param projectTitle Titre du projet\n   * @param memberCount Nombre de membres dans l'équipe\n   * @param teamMembers Liste des membres de l'équipe (optionnel)\n   * @returns Observable contenant les tâches générées\n   */\n  generateProjectTasks(projectTitle, memberCount, teamMembers) {\n    // Si le nombre de membres est trop petit, utiliser au moins 3 entités\n    const effectiveMemberCount = Math.max(memberCount, 3);\n    // Données de démonstration à utiliser en cas d'erreur ou si l'API n'est pas disponible\n    const fallbackData = this.createFallbackTaskData(projectTitle, effectiveMemberCount, teamMembers);\n    // Si nous savons déjà que l'API n'est pas disponible, retourner directement les données de démonstration\n    if (!this.isApiAvailable()) {\n      console.log('API Gemini non disponible, utilisation des données de démonstration');\n      return new Observable(observer => {\n        setTimeout(() => {\n          observer.next(fallbackData);\n          observer.complete();\n        }, 1000); // Simuler un délai d'API\n      });\n    }\n    // Préparer les informations sur les membres de l'équipe pour le prompt\n    let teamMembersInfo = '';\n    if (teamMembers && teamMembers.length > 0) {\n      teamMembersInfo = `\n      Membres de l'équipe:\n      ${teamMembers.map((member, index) => {\n        const memberName = member.name || member.firstName || member.lastName || `Membre ${index + 1}`;\n        const memberRole = member.role || 'membre';\n        return `- ${memberName} (${memberRole})`;\n      }).join('\\n')}\n      `;\n    }\n    const prompt = `\n      Agis comme un expert en gestion de projet. Je travaille sur un projet intitulé \"${projectTitle}\"\n      avec une équipe de ${effectiveMemberCount} membres.\n      ${teamMembersInfo}\n\n      Divise ce projet en exactement ${effectiveMemberCount} entités ou modules principaux qui peuvent être travaillés en parallèle par chaque membre de l'équipe.\n\n      IMPORTANT: Chaque entité doit être simple, claire et concise (maximum 3-4 mots).\n      Exemples d'entités pour un site e-commerce avec 3 membres:\n      - CRUD des produits\n      - Interface utilisateur\n      - Déploiement\n\n      Pour chaque entité/module:\n      1. Donne un nom très court et concis (maximum 3-4 mots)\n      2. Fournis une brève description (1 phrase maximum)\n      3. Liste 2-3 tâches spécifiques avec leur priorité (haute, moyenne, basse)\n\n      Réponds au format JSON suivant sans aucun texte supplémentaire:\n      {\n        \"projectTitle\": \"${projectTitle}\",\n        \"entities\": [\n          {\n            \"name\": \"Nom court de l'entité\",\n            \"description\": \"Description très brève de l'entité\",\n            \"assignedTo\": \"Nom du membre (optionnel)\",\n            \"tasks\": [\n              {\n                \"title\": \"Titre court de la tâche\",\n                \"description\": \"Description brève de la tâche\",\n                \"priority\": \"high|medium|low\",\n                \"status\": \"todo\"\n              }\n            ]\n          }\n        ]\n      }\n    `;\n    try {\n      return from(this.model.generateContent(prompt)).pipe(map(result => {\n        try {\n          const textResult = result.response.text();\n          // Extraire le JSON de la réponse\n          const jsonMatch = textResult.match(/\\{[\\s\\S]*\\}/);\n          if (jsonMatch) {\n            return JSON.parse(jsonMatch[0]);\n          } else {\n            console.warn('Format JSON non trouvé dans la réponse, utilisation des données de démonstration');\n            return fallbackData;\n          }\n        } catch (error) {\n          console.error('Erreur lors du parsing de la réponse:', error);\n          return fallbackData;\n        }\n      }),\n      // Capturer les erreurs au niveau de l'Observable\n      catchError(error => {\n        console.error('Erreur lors de la génération de contenu:', error);\n        // Marquer l'API comme non disponible pour les prochains appels\n        this.markApiAsUnavailable();\n        return of(fallbackData);\n      }));\n    } catch (error) {\n      console.error('Erreur lors de l\\'appel à l\\'API:', error);\n      this.markApiAsUnavailable();\n      return of(fallbackData);\n    }\n  }\n  // Méthode pour créer des données de démonstration\n  createFallbackTaskData(projectTitle, memberCount, teamMembers) {\n    // Préparer les informations sur les membres pour l'assignation\n    const memberNames = [];\n    if (teamMembers && teamMembers.length > 0) {\n      teamMembers.forEach((member, index) => {\n        const memberName = member.name || member.firstName || (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : null) || `Membre ${index + 1}`;\n        memberNames.push(memberName);\n      });\n    }\n    // Si pas assez de noms de membres, compléter avec des noms génériques\n    while (memberNames.length < memberCount) {\n      memberNames.push(`Membre ${memberNames.length + 1}`);\n    }\n    // Données de démonstration pour un site e-commerce\n    if (projectTitle.toLowerCase().includes('ecommerce') || projectTitle.toLowerCase().includes('e-commerce') || projectTitle.toLowerCase().includes('boutique')) {\n      const ecommerceEntities = [{\n        name: \"CRUD des produits\",\n        description: \"Gestion des produits dans la base de données\",\n        assignedTo: memberNames[0] || \"Non assigné\",\n        tasks: [{\n          title: \"Créer API produits\",\n          description: \"Développer les endpoints pour créer, lire, modifier et supprimer des produits\",\n          priority: \"high\",\n          status: \"todo\"\n        }, {\n          title: \"Modèle de données\",\n          description: \"Concevoir le schéma de la base de données pour les produits\",\n          priority: \"medium\",\n          status: \"todo\"\n        }]\n      }, {\n        name: \"Interface utilisateur\",\n        description: \"Développement du frontend de l'application\",\n        assignedTo: memberNames[1] || \"Non assigné\",\n        tasks: [{\n          title: \"Page d'accueil\",\n          description: \"Créer la page d'accueil avec la liste des produits\",\n          priority: \"high\",\n          status: \"todo\"\n        }, {\n          title: \"Panier d'achat\",\n          description: \"Implémenter la fonctionnalité du panier d'achat\",\n          priority: \"medium\",\n          status: \"todo\"\n        }]\n      }, {\n        name: \"Déploiement\",\n        description: \"Mise en production de l'application\",\n        assignedTo: memberNames[2] || \"Non assigné\",\n        tasks: [{\n          title: \"Configuration serveur\",\n          description: \"Configurer le serveur pour l'hébergement\",\n          priority: \"medium\",\n          status: \"todo\"\n        }, {\n          title: \"Tests d'intégration\",\n          description: \"Effectuer des tests d'intégration avant le déploiement\",\n          priority: \"high\",\n          status: \"todo\"\n        }]\n      }, {\n        name: \"Gestion utilisateurs\",\n        description: \"Système d'authentification et profils\",\n        assignedTo: memberNames[3] || \"Non assigné\",\n        tasks: [{\n          title: \"Authentification\",\n          description: \"Implémenter le système de connexion et d'inscription\",\n          priority: \"high\",\n          status: \"todo\"\n        }, {\n          title: \"Profils utilisateurs\",\n          description: \"Créer les pages de profil et de gestion des informations personnelles\",\n          priority: \"medium\",\n          status: \"todo\"\n        }]\n      }, {\n        name: \"Paiement en ligne\",\n        description: \"Intégration des systèmes de paiement\",\n        assignedTo: memberNames[4] || \"Non assigné\",\n        tasks: [{\n          title: \"API de paiement\",\n          description: \"Intégrer une passerelle de paiement comme Stripe ou PayPal\",\n          priority: \"high\",\n          status: \"todo\"\n        }, {\n          title: \"Sécurité transactions\",\n          description: \"Mettre en place les mesures de sécurité pour les transactions\",\n          priority: \"high\",\n          status: \"todo\"\n        }]\n      }, {\n        name: \"SEO & Analytics\",\n        description: \"Optimisation pour les moteurs de recherche\",\n        assignedTo: memberNames[5] || \"Non assigné\",\n        tasks: [{\n          title: \"Balises méta\",\n          description: \"Optimiser les balises méta et la structure du site\",\n          priority: \"medium\",\n          status: \"todo\"\n        }, {\n          title: \"Google Analytics\",\n          description: \"Intégrer des outils d'analyse du trafic\",\n          priority: \"low\",\n          status: \"todo\"\n        }]\n      }];\n      // Limiter au nombre de membres\n      return {\n        projectTitle: projectTitle,\n        entities: ecommerceEntities.slice(0, memberCount)\n      };\n    }\n    // Données génériques pour tout autre type de projet\n    const moduleTypes = [{\n      name: \"Backend\",\n      description: \"Développement du backend de l'application\"\n    }, {\n      name: \"Frontend\",\n      description: \"Développement de l'interface utilisateur\"\n    }, {\n      name: \"Base de données\",\n      description: \"Conception et gestion de la base de données\"\n    }, {\n      name: \"Tests\",\n      description: \"Tests et assurance qualité\"\n    }, {\n      name: \"Déploiement\",\n      description: \"Configuration et déploiement de l'application\"\n    }, {\n      name: \"Documentation\",\n      description: \"Rédaction de la documentation technique\"\n    }];\n    return {\n      projectTitle: projectTitle,\n      entities: Array.from({\n        length: memberCount\n      }, (_, i) => ({\n        name: moduleTypes[i % moduleTypes.length].name,\n        description: moduleTypes[i % moduleTypes.length].description,\n        assignedTo: memberNames[i] || \"Non assigné\",\n        tasks: [{\n          title: `Conception ${moduleTypes[i % moduleTypes.length].name}`,\n          description: `Planifier l'architecture du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n          priority: 'high',\n          status: 'todo'\n        }, {\n          title: `Implémentation ${moduleTypes[i % moduleTypes.length].name}`,\n          description: `Développer les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n          priority: 'medium',\n          status: 'todo'\n        }, {\n          title: `Tests ${moduleTypes[i % moduleTypes.length].name}`,\n          description: `Tester les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\n          priority: 'medium',\n          status: 'todo'\n        }]\n      }))\n    };\n  }\n  // Méthode pour vérifier si l'API est disponible\n  isApiAvailable() {\n    return this.apiAvailable;\n  }\n  // Méthode pour marquer l'API comme non disponible\n  markApiAsUnavailable() {\n    this.apiAvailable = false;\n    console.warn('API Gemini marquée comme non disponible pour les prochains appels');\n  }\n  /**\n   * Génère une réponse à une question sur le projet\n   * @param question Question posée par l'utilisateur\n   * @param projectContext Contexte du projet (titre, description, etc.)\n   * @returns Observable contenant la réponse générée\n   */\n  askProjectQuestion(question, projectContext) {\n    // Réponses de secours en cas d'erreur\n    const fallbackResponses = [`Pour votre projet \"${projectContext.title || 'en cours'}\", je recommande de commencer par définir clairement les objectifs et les livrables attendus.`, `La gestion efficace d'un projet comme \"${projectContext.title || 'celui-ci'}\" nécessite une bonne planification et une communication claire entre les membres de l'équipe.`, `Pour répondre à votre question sur \"${question}\", je vous suggère de diviser le travail en tâches plus petites et de les assigner aux membres de l'équipe en fonction de leurs compétences.`, `Dans le cadre de votre projet, il est important de définir des jalons clairs et de suivre régulièrement l'avancement des travaux.`];\n    // Sélectionner une réponse aléatoire de secours\n    const getRandomFallbackResponse = () => {\n      const randomIndex = Math.floor(Math.random() * fallbackResponses.length);\n      return fallbackResponses[randomIndex];\n    };\n    // Si l'API n'est pas disponible, retourner directement une réponse de secours\n    if (!this.isApiAvailable()) {\n      console.log('API Gemini non disponible, utilisation d\\'une réponse de secours');\n      return of(getRandomFallbackResponse());\n    }\n    const prompt = `\n      Contexte du projet:\n      Titre: ${projectContext.title || 'Non spécifié'}\n      Description: ${projectContext.description || 'Non spécifiée'}\n\n      Question: ${question}\n\n      Réponds de manière concise et professionnelle en tant qu'assistant de gestion de projet.\n    `;\n    try {\n      return from(this.model.generateContent(prompt)).pipe(map(result => {\n        try {\n          return result.response.text();\n        } catch (error) {\n          console.error('Erreur lors de la récupération de la réponse:', error);\n          return getRandomFallbackResponse();\n        }\n      }), catchError(error => {\n        console.error('Erreur lors de la génération de contenu:', error);\n        this.markApiAsUnavailable();\n        return of(getRandomFallbackResponse());\n      }));\n    } catch (error) {\n      console.error('Erreur lors de la génération de contenu:', error);\n      this.markApiAsUnavailable();\n      return of(getRandomFallbackResponse());\n    }\n  }\n  static {\n    this.ɵfac = function AiService_Factory(t) {\n      return new (t || AiService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AiService,\n      factory: AiService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["GoogleGenerativeAI", "Observable", "from", "of", "map", "catchError", "environment", "AiService", "constructor", "apiAvailable", "<PERSON><PERSON><PERSON><PERSON>", "geminiApiKey", "genAI", "model", "getGenerativeModel", "console", "log", "error", "generateContent", "Promise", "resolve", "response", "text", "generateProjectTasks", "projectTitle", "memberCount", "teamMembers", "effectiveMemberCount", "Math", "max", "fallbackD<PERSON>", "createFallbackTaskData", "isApiAvailable", "observer", "setTimeout", "next", "complete", "teamMembersInfo", "length", "member", "index", "memberName", "name", "firstName", "lastName", "memberRole", "role", "join", "prompt", "pipe", "result", "textResult", "jsonMatch", "match", "JSON", "parse", "warn", "markApiAsUnavailable", "memberNames", "for<PERSON>ach", "push", "toLowerCase", "includes", "ecommerceEntities", "description", "assignedTo", "tasks", "title", "priority", "status", "entities", "slice", "moduleTypes", "Array", "_", "i", "askProjectQuestion", "question", "projectContext", "fallbackResponses", "getRandomFallbackResponse", "randomIndex", "floor", "random", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\ai.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';\r\nimport { Observable, from, of } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment'\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AiService {\r\n  private genAI!: GoogleGenerativeAI;\r\n  private model!: GenerativeModel;\r\n  private apiAvailable: boolean = true;\r\n\r\n  constructor() {\r\n    // Utiliser une clé API de l'environnement ou une clé de démonstration\r\n    // Pour une utilisation en production, obtenez une clé API sur https://makersuite.google.com/\r\n    const apiKey = environment.geminiApiKey || 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88';\r\n\r\n    try {\r\n      // Initialiser l'API Gemini\r\n      this.genAI = new GoogleGenerativeAI(apiKey);\r\n      // Utiliser le modèle gemini-1.5-pro qui est disponible dans la version actuelle de l'API\r\n      this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });\r\n      console.log('Service AI initialisé avec succès');\r\n    } catch (error) {\r\n      console.error('Erreur lors de l\\'initialisation du service AI:', error);\r\n\r\n      // Créer des objets factices pour éviter les erreurs null\r\n      // Ces objets ne feront rien mais éviteront les erreurs d'exécution\r\n      this.genAI = {} as GoogleGenerativeAI;\r\n      this.model = {\r\n        generateContent: () => Promise.resolve({\r\n          response: { text: () => 'Service AI non disponible' }\r\n        })\r\n      } as unknown as GenerativeModel;\r\n\r\n      // Marquer l'API comme non disponible\r\n      this.apiAvailable = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Génère des tâches pour un projet en fonction du nombre de membres\r\n   * @param projectTitle Titre du projet\r\n   * @param memberCount Nombre de membres dans l'équipe\r\n   * @param teamMembers Liste des membres de l'équipe (optionnel)\r\n   * @returns Observable contenant les tâches générées\r\n   */\r\n  generateProjectTasks(projectTitle: string, memberCount: number, teamMembers?: any[]): Observable<any> {\r\n    // Si le nombre de membres est trop petit, utiliser au moins 3 entités\r\n    const effectiveMemberCount = Math.max(memberCount, 3);\r\n\r\n    // Données de démonstration à utiliser en cas d'erreur ou si l'API n'est pas disponible\r\n    const fallbackData = this.createFallbackTaskData(projectTitle, effectiveMemberCount, teamMembers);\r\n\r\n    // Si nous savons déjà que l'API n'est pas disponible, retourner directement les données de démonstration\r\n    if (!this.isApiAvailable()) {\r\n      console.log('API Gemini non disponible, utilisation des données de démonstration');\r\n      return new Observable(observer => {\r\n        setTimeout(() => {\r\n          observer.next(fallbackData);\r\n          observer.complete();\r\n        }, 1000); // Simuler un délai d'API\r\n      });\r\n    }\r\n\r\n    // Préparer les informations sur les membres de l'équipe pour le prompt\r\n    let teamMembersInfo = '';\r\n    if (teamMembers && teamMembers.length > 0) {\r\n      teamMembersInfo = `\r\n      Membres de l'équipe:\r\n      ${teamMembers.map((member, index) => {\r\n        const memberName = member.name || member.firstName || member.lastName || `Membre ${index + 1}`;\r\n        const memberRole = member.role || 'membre';\r\n        return `- ${memberName} (${memberRole})`;\r\n      }).join('\\n')}\r\n      `;\r\n    }\r\n\r\n    const prompt = `\r\n      Agis comme un expert en gestion de projet. Je travaille sur un projet intitulé \"${projectTitle}\"\r\n      avec une équipe de ${effectiveMemberCount} membres.\r\n      ${teamMembersInfo}\r\n\r\n      Divise ce projet en exactement ${effectiveMemberCount} entités ou modules principaux qui peuvent être travaillés en parallèle par chaque membre de l'équipe.\r\n\r\n      IMPORTANT: Chaque entité doit être simple, claire et concise (maximum 3-4 mots).\r\n      Exemples d'entités pour un site e-commerce avec 3 membres:\r\n      - CRUD des produits\r\n      - Interface utilisateur\r\n      - Déploiement\r\n\r\n      Pour chaque entité/module:\r\n      1. Donne un nom très court et concis (maximum 3-4 mots)\r\n      2. Fournis une brève description (1 phrase maximum)\r\n      3. Liste 2-3 tâches spécifiques avec leur priorité (haute, moyenne, basse)\r\n\r\n      Réponds au format JSON suivant sans aucun texte supplémentaire:\r\n      {\r\n        \"projectTitle\": \"${projectTitle}\",\r\n        \"entities\": [\r\n          {\r\n            \"name\": \"Nom court de l'entité\",\r\n            \"description\": \"Description très brève de l'entité\",\r\n            \"assignedTo\": \"Nom du membre (optionnel)\",\r\n            \"tasks\": [\r\n              {\r\n                \"title\": \"Titre court de la tâche\",\r\n                \"description\": \"Description brève de la tâche\",\r\n                \"priority\": \"high|medium|low\",\r\n                \"status\": \"todo\"\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    `;\r\n\r\n    try {\r\n      return from(this.model.generateContent(prompt))\r\n        .pipe(\r\n          map(result => {\r\n            try {\r\n              const textResult = result.response.text();\r\n              // Extraire le JSON de la réponse\r\n              const jsonMatch = textResult.match(/\\{[\\s\\S]*\\}/);\r\n              if (jsonMatch) {\r\n                return JSON.parse(jsonMatch[0]);\r\n              } else {\r\n                console.warn('Format JSON non trouvé dans la réponse, utilisation des données de démonstration');\r\n                return fallbackData;\r\n              }\r\n            } catch (error) {\r\n              console.error('Erreur lors du parsing de la réponse:', error);\r\n              return fallbackData;\r\n            }\r\n          }),\r\n          // Capturer les erreurs au niveau de l'Observable\r\n          catchError(error => {\r\n            console.error('Erreur lors de la génération de contenu:', error);\r\n            // Marquer l'API comme non disponible pour les prochains appels\r\n            this.markApiAsUnavailable();\r\n            return of(fallbackData);\r\n          })\r\n        );\r\n    } catch (error) {\r\n      console.error('Erreur lors de l\\'appel à l\\'API:', error);\r\n      this.markApiAsUnavailable();\r\n      return of(fallbackData);\r\n    }\r\n  }\r\n\r\n  // Méthode pour créer des données de démonstration\r\n  private createFallbackTaskData(projectTitle: string, memberCount: number, teamMembers?: any[]): any {\r\n    // Préparer les informations sur les membres pour l'assignation\r\n    const memberNames: string[] = [];\r\n    if (teamMembers && teamMembers.length > 0) {\r\n      teamMembers.forEach((member, index) => {\r\n        const memberName = member.name || member.firstName ||\r\n                          (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : null) ||\r\n                          `Membre ${index + 1}`;\r\n        memberNames.push(memberName);\r\n      });\r\n    }\r\n\r\n    // Si pas assez de noms de membres, compléter avec des noms génériques\r\n    while (memberNames.length < memberCount) {\r\n      memberNames.push(`Membre ${memberNames.length + 1}`);\r\n    }\r\n\r\n    // Données de démonstration pour un site e-commerce\r\n    if (projectTitle.toLowerCase().includes('ecommerce') || projectTitle.toLowerCase().includes('e-commerce') || projectTitle.toLowerCase().includes('boutique')) {\r\n      const ecommerceEntities = [\r\n        {\r\n          name: \"CRUD des produits\",\r\n          description: \"Gestion des produits dans la base de données\",\r\n          assignedTo: memberNames[0] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Créer API produits\",\r\n              description: \"Développer les endpoints pour créer, lire, modifier et supprimer des produits\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Modèle de données\",\r\n              description: \"Concevoir le schéma de la base de données pour les produits\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Interface utilisateur\",\r\n          description: \"Développement du frontend de l'application\",\r\n          assignedTo: memberNames[1] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Page d'accueil\",\r\n              description: \"Créer la page d'accueil avec la liste des produits\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Panier d'achat\",\r\n              description: \"Implémenter la fonctionnalité du panier d'achat\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Déploiement\",\r\n          description: \"Mise en production de l'application\",\r\n          assignedTo: memberNames[2] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Configuration serveur\",\r\n              description: \"Configurer le serveur pour l'hébergement\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Tests d'intégration\",\r\n              description: \"Effectuer des tests d'intégration avant le déploiement\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Gestion utilisateurs\",\r\n          description: \"Système d'authentification et profils\",\r\n          assignedTo: memberNames[3] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Authentification\",\r\n              description: \"Implémenter le système de connexion et d'inscription\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Profils utilisateurs\",\r\n              description: \"Créer les pages de profil et de gestion des informations personnelles\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Paiement en ligne\",\r\n          description: \"Intégration des systèmes de paiement\",\r\n          assignedTo: memberNames[4] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"API de paiement\",\r\n              description: \"Intégrer une passerelle de paiement comme Stripe ou PayPal\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Sécurité transactions\",\r\n              description: \"Mettre en place les mesures de sécurité pour les transactions\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"SEO & Analytics\",\r\n          description: \"Optimisation pour les moteurs de recherche\",\r\n          assignedTo: memberNames[5] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Balises méta\",\r\n              description: \"Optimiser les balises méta et la structure du site\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Google Analytics\",\r\n              description: \"Intégrer des outils d'analyse du trafic\",\r\n              priority: \"low\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // Limiter au nombre de membres\r\n      return {\r\n        projectTitle: projectTitle,\r\n        entities: ecommerceEntities.slice(0, memberCount)\r\n      };\r\n    }\r\n\r\n    // Données génériques pour tout autre type de projet\r\n    const moduleTypes = [\r\n      { name: \"Backend\", description: \"Développement du backend de l'application\" },\r\n      { name: \"Frontend\", description: \"Développement de l'interface utilisateur\" },\r\n      { name: \"Base de données\", description: \"Conception et gestion de la base de données\" },\r\n      { name: \"Tests\", description: \"Tests et assurance qualité\" },\r\n      { name: \"Déploiement\", description: \"Configuration et déploiement de l'application\" },\r\n      { name: \"Documentation\", description: \"Rédaction de la documentation technique\" }\r\n    ];\r\n\r\n    return {\r\n      projectTitle: projectTitle,\r\n      entities: Array.from({ length: memberCount }, (_, i) => ({\r\n        name: moduleTypes[i % moduleTypes.length].name,\r\n        description: moduleTypes[i % moduleTypes.length].description,\r\n        assignedTo: memberNames[i] || \"Non assigné\",\r\n        tasks: [\r\n          {\r\n            title: `Conception ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Planifier l'architecture du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'high',\r\n            status: 'todo'\r\n          },\r\n          {\r\n            title: `Implémentation ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Développer les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'medium',\r\n            status: 'todo'\r\n          },\r\n          {\r\n            title: `Tests ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Tester les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'medium',\r\n            status: 'todo'\r\n          }\r\n        ]\r\n      }))\r\n    };\r\n  }\r\n\r\n  // Méthode pour vérifier si l'API est disponible\r\n  private isApiAvailable(): boolean {\r\n    return this.apiAvailable;\r\n  }\r\n\r\n  // Méthode pour marquer l'API comme non disponible\r\n  private markApiAsUnavailable(): void {\r\n    this.apiAvailable = false;\r\n    console.warn('API Gemini marquée comme non disponible pour les prochains appels');\r\n  }\r\n\r\n  /**\r\n   * Génère une réponse à une question sur le projet\r\n   * @param question Question posée par l'utilisateur\r\n   * @param projectContext Contexte du projet (titre, description, etc.)\r\n   * @returns Observable contenant la réponse générée\r\n   */\r\n  askProjectQuestion(question: string, projectContext: any): Observable<string> {\r\n    // Réponses de secours en cas d'erreur\r\n    const fallbackResponses = [\r\n      `Pour votre projet \"${projectContext.title || 'en cours'}\", je recommande de commencer par définir clairement les objectifs et les livrables attendus.`,\r\n      `La gestion efficace d'un projet comme \"${projectContext.title || 'celui-ci'}\" nécessite une bonne planification et une communication claire entre les membres de l'équipe.`,\r\n      `Pour répondre à votre question sur \"${question}\", je vous suggère de diviser le travail en tâches plus petites et de les assigner aux membres de l'équipe en fonction de leurs compétences.`,\r\n      `Dans le cadre de votre projet, il est important de définir des jalons clairs et de suivre régulièrement l'avancement des travaux.`\r\n    ];\r\n\r\n    // Sélectionner une réponse aléatoire de secours\r\n    const getRandomFallbackResponse = () => {\r\n      const randomIndex = Math.floor(Math.random() * fallbackResponses.length);\r\n      return fallbackResponses[randomIndex];\r\n    };\r\n\r\n    // Si l'API n'est pas disponible, retourner directement une réponse de secours\r\n    if (!this.isApiAvailable()) {\r\n      console.log('API Gemini non disponible, utilisation d\\'une réponse de secours');\r\n      return of(getRandomFallbackResponse());\r\n    }\r\n\r\n    const prompt = `\r\n      Contexte du projet:\r\n      Titre: ${projectContext.title || 'Non spécifié'}\r\n      Description: ${projectContext.description || 'Non spécifiée'}\r\n\r\n      Question: ${question}\r\n\r\n      Réponds de manière concise et professionnelle en tant qu'assistant de gestion de projet.\r\n    `;\r\n\r\n    try {\r\n      return from(this.model.generateContent(prompt))\r\n        .pipe(\r\n          map(result => {\r\n            try {\r\n              return result.response.text();\r\n            } catch (error) {\r\n              console.error('Erreur lors de la récupération de la réponse:', error);\r\n              return getRandomFallbackResponse();\r\n            }\r\n          }),\r\n          catchError(error => {\r\n            console.error('Erreur lors de la génération de contenu:', error);\r\n            this.markApiAsUnavailable();\r\n            return of(getRandomFallbackResponse());\r\n          })\r\n        );\r\n    } catch (error) {\r\n      console.error('Erreur lors de la génération de contenu:', error);\r\n      this.markApiAsUnavailable();\r\n      return of(getRandomFallbackResponse());\r\n    }\r\n  }\r\n}"], "mappings": "AACA,SAASA,kBAAkB,QAAyB,uBAAuB;AAC3E,SAASC,UAAU,EAAEC,IAAI,EAAEC,EAAE,QAAQ,MAAM;AAC3C,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,8BAA8B;;AAK1D,OAAM,MAAOC,SAAS;EAKpBC,YAAA;IAFQ,KAAAC,YAAY,GAAY,IAAI;IAGlC;IACA;IACA,MAAMC,MAAM,GAAGJ,WAAW,CAACK,YAAY,IAAI,yCAAyC;IAEpF,IAAI;MACF;MACA,IAAI,CAACC,KAAK,GAAG,IAAIZ,kBAAkB,CAACU,MAAM,CAAC;MAC3C;MACA,IAAI,CAACG,KAAK,GAAG,IAAI,CAACD,KAAK,CAACE,kBAAkB,CAAC;QAAED,KAAK,EAAE;MAAgB,CAAE,CAAC;MACvEE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;KACjD,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MAEvE;MACA;MACA,IAAI,CAACL,KAAK,GAAG,EAAwB;MACrC,IAAI,CAACC,KAAK,GAAG;QACXK,eAAe,EAAEA,CAAA,KAAMC,OAAO,CAACC,OAAO,CAAC;UACrCC,QAAQ,EAAE;YAAEC,IAAI,EAAEA,CAAA,KAAM;UAA2B;SACpD;OAC4B;MAE/B;MACA,IAAI,CAACb,YAAY,GAAG,KAAK;;EAE7B;EAEA;;;;;;;EAOAc,oBAAoBA,CAACC,YAAoB,EAAEC,WAAmB,EAAEC,WAAmB;IACjF;IACA,MAAMC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAE,CAAC,CAAC;IAErD;IACA,MAAMK,YAAY,GAAG,IAAI,CAACC,sBAAsB,CAACP,YAAY,EAAEG,oBAAoB,EAAED,WAAW,CAAC;IAEjG;IACA,IAAI,CAAC,IAAI,CAACM,cAAc,EAAE,EAAE;MAC1BjB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;MAClF,OAAO,IAAIf,UAAU,CAACgC,QAAQ,IAAG;QAC/BC,UAAU,CAAC,MAAK;UACdD,QAAQ,CAACE,IAAI,CAACL,YAAY,CAAC;UAC3BG,QAAQ,CAACG,QAAQ,EAAE;QACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;IAGJ;IACA,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAIX,WAAW,IAAIA,WAAW,CAACY,MAAM,GAAG,CAAC,EAAE;MACzCD,eAAe,GAAG;;QAEhBX,WAAW,CAACtB,GAAG,CAAC,CAACmC,MAAM,EAAEC,KAAK,KAAI;QAClC,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,IAAI,UAAUJ,KAAK,GAAG,CAAC,EAAE;QAC9F,MAAMK,UAAU,GAAGN,MAAM,CAACO,IAAI,IAAI,QAAQ;QAC1C,OAAO,KAAKL,UAAU,KAAKI,UAAU,GAAG;MAC1C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;OACZ;;IAGH,MAAMC,MAAM,GAAG;wFACqExB,YAAY;2BACzEG,oBAAoB;QACvCU,eAAe;;uCAEgBV,oBAAoB;;;;;;;;;;;;;;;2BAehCH,YAAY;;;;;;;;;;;;;;;;;KAiBlC;IAED,IAAI;MACF,OAAOtB,IAAI,CAAC,IAAI,CAACW,KAAK,CAACK,eAAe,CAAC8B,MAAM,CAAC,CAAC,CAC5CC,IAAI,CACH7C,GAAG,CAAC8C,MAAM,IAAG;QACX,IAAI;UACF,MAAMC,UAAU,GAAGD,MAAM,CAAC7B,QAAQ,CAACC,IAAI,EAAE;UACzC;UACA,MAAM8B,SAAS,GAAGD,UAAU,CAACE,KAAK,CAAC,aAAa,CAAC;UACjD,IAAID,SAAS,EAAE;YACb,OAAOE,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;WAChC,MAAM;YACLrC,OAAO,CAACyC,IAAI,CAAC,kFAAkF,CAAC;YAChG,OAAO1B,YAAY;;SAEtB,CAAC,OAAOb,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,OAAOa,YAAY;;MAEvB,CAAC,CAAC;MACF;MACAzB,UAAU,CAACY,KAAK,IAAG;QACjBF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE;QACA,IAAI,CAACwC,oBAAoB,EAAE;QAC3B,OAAOtD,EAAE,CAAC2B,YAAY,CAAC;MACzB,CAAC,CAAC,CACH;KACJ,CAAC,OAAOb,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACwC,oBAAoB,EAAE;MAC3B,OAAOtD,EAAE,CAAC2B,YAAY,CAAC;;EAE3B;EAEA;EACQC,sBAAsBA,CAACP,YAAoB,EAAEC,WAAmB,EAAEC,WAAmB;IAC3F;IACA,MAAMgC,WAAW,GAAa,EAAE;IAChC,IAAIhC,WAAW,IAAIA,WAAW,CAACY,MAAM,GAAG,CAAC,EAAE;MACzCZ,WAAW,CAACiC,OAAO,CAAC,CAACpB,MAAM,EAAEC,KAAK,KAAI;QACpC,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACI,SAAS,KAC/BJ,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,GAAG,GAAGL,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,EAAE,GAAG,IAAI,CAAC,IACvF,UAAUJ,KAAK,GAAG,CAAC,EAAE;QACvCkB,WAAW,CAACE,IAAI,CAACnB,UAAU,CAAC;MAC9B,CAAC,CAAC;;IAGJ;IACA,OAAOiB,WAAW,CAACpB,MAAM,GAAGb,WAAW,EAAE;MACvCiC,WAAW,CAACE,IAAI,CAAC,UAAUF,WAAW,CAACpB,MAAM,GAAG,CAAC,EAAE,CAAC;;IAGtD;IACA,IAAId,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAItC,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAItC,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC5J,MAAMC,iBAAiB,GAAG,CACxB;QACErB,IAAI,EAAE,mBAAmB;QACzBsB,WAAW,EAAE,8CAA8C;QAC3DC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,oBAAoB;UAC3BH,WAAW,EAAE,+EAA+E;UAC5FI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,mBAAmB;UAC1BH,WAAW,EAAE,6DAA6D;UAC1EI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,uBAAuB;QAC7BsB,WAAW,EAAE,4CAA4C;QACzDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,gBAAgB;UACvBH,WAAW,EAAE,oDAAoD;UACjEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,gBAAgB;UACvBH,WAAW,EAAE,iDAAiD;UAC9DI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,aAAa;QACnBsB,WAAW,EAAE,qCAAqC;QAClDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,uBAAuB;UAC9BH,WAAW,EAAE,0CAA0C;UACvDI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,qBAAqB;UAC5BH,WAAW,EAAE,wDAAwD;UACrEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,sBAAsB;QAC5BsB,WAAW,EAAE,uCAAuC;QACpDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,kBAAkB;UACzBH,WAAW,EAAE,sDAAsD;UACnEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,sBAAsB;UAC7BH,WAAW,EAAE,uEAAuE;UACpFI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,mBAAmB;QACzBsB,WAAW,EAAE,sCAAsC;QACnDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,iBAAiB;UACxBH,WAAW,EAAE,4DAA4D;UACzEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,uBAAuB;UAC9BH,WAAW,EAAE,+DAA+D;UAC5EI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,iBAAiB;QACvBsB,WAAW,EAAE,4CAA4C;QACzDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,cAAc;UACrBH,WAAW,EAAE,oDAAoD;UACjEI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,kBAAkB;UACzBH,WAAW,EAAE,yCAAyC;UACtDI,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;SACT;OAEJ,CACF;MAED;MACA,OAAO;QACL7C,YAAY,EAAEA,YAAY;QAC1B8C,QAAQ,EAAEP,iBAAiB,CAACQ,KAAK,CAAC,CAAC,EAAE9C,WAAW;OACjD;;IAGH;IACA,MAAM+C,WAAW,GAAG,CAClB;MAAE9B,IAAI,EAAE,SAAS;MAAEsB,WAAW,EAAE;IAA2C,CAAE,EAC7E;MAAEtB,IAAI,EAAE,UAAU;MAAEsB,WAAW,EAAE;IAA0C,CAAE,EAC7E;MAAEtB,IAAI,EAAE,iBAAiB;MAAEsB,WAAW,EAAE;IAA6C,CAAE,EACvF;MAAEtB,IAAI,EAAE,OAAO;MAAEsB,WAAW,EAAE;IAA4B,CAAE,EAC5D;MAAEtB,IAAI,EAAE,aAAa;MAAEsB,WAAW,EAAE;IAA+C,CAAE,EACrF;MAAEtB,IAAI,EAAE,eAAe;MAAEsB,WAAW,EAAE;IAAyC,CAAE,CAClF;IAED,OAAO;MACLxC,YAAY,EAAEA,YAAY;MAC1B8C,QAAQ,EAAEG,KAAK,CAACvE,IAAI,CAAC;QAAEoC,MAAM,EAAEb;MAAW,CAAE,EAAE,CAACiD,CAAC,EAAEC,CAAC,MAAM;QACvDjC,IAAI,EAAE8B,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI;QAC9CsB,WAAW,EAAEQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAAC0B,WAAW;QAC5DC,UAAU,EAAEP,WAAW,CAACiB,CAAC,CAAC,IAAI,aAAa;QAC3CT,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,cAAcK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UAC/DsB,WAAW,EAAE,+BAA+BQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UACpGO,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,kBAAkBK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UACnEsB,WAAW,EAAE,qCAAqCQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UAC1GO,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,SAASK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UAC1DsB,WAAW,EAAE,iCAAiCQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UACtGO,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,CAAC;KACH;EACH;EAEA;EACQrC,cAAcA,CAAA;IACpB,OAAO,IAAI,CAACvB,YAAY;EAC1B;EAEA;EACQgD,oBAAoBA,CAAA;IAC1B,IAAI,CAAChD,YAAY,GAAG,KAAK;IACzBM,OAAO,CAACyC,IAAI,CAAC,mEAAmE,CAAC;EACnF;EAEA;;;;;;EAMAoB,kBAAkBA,CAACC,QAAgB,EAAEC,cAAmB;IACtD;IACA,MAAMC,iBAAiB,GAAG,CACxB,sBAAsBD,cAAc,CAACX,KAAK,IAAI,UAAU,+FAA+F,EACvJ,0CAA0CW,cAAc,CAACX,KAAK,IAAI,UAAU,gGAAgG,EAC5K,uCAAuCU,QAAQ,8IAA8I,EAC7L,mIAAmI,CACpI;IAED;IACA,MAAMG,yBAAyB,GAAGA,CAAA,KAAK;MACrC,MAAMC,WAAW,GAAGrD,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACuD,MAAM,EAAE,GAAGJ,iBAAiB,CAACzC,MAAM,CAAC;MACxE,OAAOyC,iBAAiB,CAACE,WAAW,CAAC;IACvC,CAAC;IAED;IACA,IAAI,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE;MAC1BjB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E,OAAOb,EAAE,CAAC6E,yBAAyB,EAAE,CAAC;;IAGxC,MAAMhC,MAAM,GAAG;;eAEJ8B,cAAc,CAACX,KAAK,IAAI,cAAc;qBAChCW,cAAc,CAACd,WAAW,IAAI,eAAe;;kBAEhDa,QAAQ;;;KAGrB;IAED,IAAI;MACF,OAAO3E,IAAI,CAAC,IAAI,CAACW,KAAK,CAACK,eAAe,CAAC8B,MAAM,CAAC,CAAC,CAC5CC,IAAI,CACH7C,GAAG,CAAC8C,MAAM,IAAG;QACX,IAAI;UACF,OAAOA,MAAM,CAAC7B,QAAQ,CAACC,IAAI,EAAE;SAC9B,CAAC,OAAOL,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,OAAO+D,yBAAyB,EAAE;;MAEtC,CAAC,CAAC,EACF3E,UAAU,CAACY,KAAK,IAAG;QACjBF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACwC,oBAAoB,EAAE;QAC3B,OAAOtD,EAAE,CAAC6E,yBAAyB,EAAE,CAAC;MACxC,CAAC,CAAC,CACH;KACJ,CAAC,OAAO/D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACwC,oBAAoB,EAAE;MAC3B,OAAOtD,EAAE,CAAC6E,yBAAyB,EAAE,CAAC;;EAE1C;;;uBA9YWzE,SAAS;IAAA;EAAA;;;aAATA,SAAS;MAAA6E,OAAA,EAAT7E,SAAS,CAAA8E,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}