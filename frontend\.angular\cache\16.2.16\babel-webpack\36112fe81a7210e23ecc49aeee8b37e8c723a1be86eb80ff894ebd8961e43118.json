{"ast": null, "code": "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n  return function (message) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (typeof message === \"number\") {\n      var arg0 = message;\n      message = getHandledErrorMsg(arg0);\n      if (!message) {\n        message = getFallbackErrorMsg(arg0, args);\n        args = [];\n      }\n    }\n    fn.apply(void 0, [message].concat(args));\n  };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  if (!condition) {\n    originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n  }\n}, {\n  debug: wrap(originalInvariant.debug),\n  log: wrap(originalInvariant.log),\n  warn: wrap(originalInvariant.warn),\n  error: wrap(originalInvariant.error)\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n  var optionalParams = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    optionalParams[_i - 1] = arguments[_i];\n  }\n  return new InvariantError(getHandledErrorMsg(message, optionalParams) || getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n  if (typeof arg == \"string\") {\n    return arg;\n  }\n  try {\n    return stringifyForDisplay(arg, 2).slice(0, 1000);\n  } catch (_a) {\n    return \"<non-serializable>\";\n  }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n  if (messageArgs === void 0) {\n    messageArgs = [];\n  }\n  if (!message) return;\n  return global[ApolloErrorMessageHandler] && global[ApolloErrorMessageHandler](message, messageArgs.map(stringify));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n  if (messageArgs === void 0) {\n    messageArgs = [];\n  }\n  if (!message) return;\n  return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n    version: version,\n    message: message,\n    args: messageArgs.map(stringify)\n  })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler };", "map": {"version": 3, "names": ["invariant", "originalInvariant", "InvariantError", "version", "global", "stringifyForDisplay", "wrap", "fn", "message", "args", "_i", "arguments", "length", "arg0", "getHandledErrorMsg", "getFallbackError<PERSON>g", "apply", "concat", "Object", "assign", "condition", "debug", "log", "warn", "error", "newInvariantError", "optionalParams", "ApolloErrorMessageHandler", "Symbol", "for", "stringify", "arg", "slice", "_a", "messageArgs", "map", "encodeURIComponent", "JSON"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/globals/invariantWrappers.js"], "sourcesContent": ["import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n    return function (message) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (typeof message === \"number\") {\n            var arg0 = message;\n            message = getHandledErrorMsg(arg0);\n            if (!message) {\n                message = getFallbackErrorMsg(arg0, args);\n                args = [];\n            }\n        }\n        fn.apply(void 0, [message].concat(args));\n    };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    if (!condition) {\n        originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n    }\n}, {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n    var optionalParams = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        optionalParams[_i - 1] = arguments[_i];\n    }\n    return new InvariantError(getHandledErrorMsg(message, optionalParams) ||\n        getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n    if (typeof arg == \"string\") {\n        return arg;\n    }\n    try {\n        return stringifyForDisplay(arg, 2).slice(0, 1000);\n    }\n    catch (_a) {\n        return \"<non-serializable>\";\n    }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return (global[ApolloErrorMessageHandler] &&\n        global[ApolloErrorMessageHandler](message, messageArgs.map(stringify)));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n        version: version,\n        message: message,\n        args: messageArgs.map(stringify),\n    })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler, };\n"], "mappings": "AAAA,SAASA,SAAS,IAAIC,iBAAiB,EAAEC,cAAc,QAAQ,cAAc;AAC7E,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,IAAIA,CAACC,EAAE,EAAE;EACd,OAAO,UAAUC,OAAO,EAAE;IACtB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAC7B,IAAIK,IAAI,GAAGL,OAAO;MAClBA,OAAO,GAAGM,kBAAkB,CAACD,IAAI,CAAC;MAClC,IAAI,CAACL,OAAO,EAAE;QACVA,OAAO,GAAGO,mBAAmB,CAACF,IAAI,EAAEJ,IAAI,CAAC;QACzCA,IAAI,GAAG,EAAE;MACb;IACJ;IACAF,EAAE,CAACS,KAAK,CAAC,KAAK,CAAC,EAAE,CAACR,OAAO,CAAC,CAACS,MAAM,CAACR,IAAI,CAAC,CAAC;EAC5C,CAAC;AACL;AACA,IAAIT,SAAS,GAAGkB,MAAM,CAACC,MAAM,CAAC,SAASnB,SAASA,CAACoB,SAAS,EAAEZ,OAAO,EAAE;EACjE,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAChC;EACA,IAAI,CAACU,SAAS,EAAE;IACZnB,iBAAiB,CAACmB,SAAS,EAAEN,kBAAkB,CAACN,OAAO,EAAEC,IAAI,CAAC,IAAIM,mBAAmB,CAACP,OAAO,EAAEC,IAAI,CAAC,CAAC;EACzG;AACJ,CAAC,EAAE;EACCY,KAAK,EAAEf,IAAI,CAACL,iBAAiB,CAACoB,KAAK,CAAC;EACpCC,GAAG,EAAEhB,IAAI,CAACL,iBAAiB,CAACqB,GAAG,CAAC;EAChCC,IAAI,EAAEjB,IAAI,CAACL,iBAAiB,CAACsB,IAAI,CAAC;EAClCC,KAAK,EAAElB,IAAI,CAACL,iBAAiB,CAACuB,KAAK;AACvC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACjB,OAAO,EAAE;EAChC,IAAIkB,cAAc,GAAG,EAAE;EACvB,KAAK,IAAIhB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CgB,cAAc,CAAChB,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC1C;EACA,OAAO,IAAIR,cAAc,CAACY,kBAAkB,CAACN,OAAO,EAAEkB,cAAc,CAAC,IACjEX,mBAAmB,CAACP,OAAO,EAAEkB,cAAc,CAAC,CAAC;AACrD;AACA,IAAIC,yBAAyB,GAAGC,MAAM,CAACC,GAAG,CAAC,4BAA4B,GAAG1B,OAAO,CAAC;AAClF,SAAS2B,SAASA,CAACC,GAAG,EAAE;EACpB,IAAI,OAAOA,GAAG,IAAI,QAAQ,EAAE;IACxB,OAAOA,GAAG;EACd;EACA,IAAI;IACA,OAAO1B,mBAAmB,CAAC0B,GAAG,EAAE,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;EACrD,CAAC,CACD,OAAOC,EAAE,EAAE;IACP,OAAO,oBAAoB;EAC/B;AACJ;AACA,SAASnB,kBAAkBA,CAACN,OAAO,EAAE0B,WAAW,EAAE;EAC9C,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,EAAE;EAAE;EAChD,IAAI,CAAC1B,OAAO,EACR;EACJ,OAAQJ,MAAM,CAACuB,yBAAyB,CAAC,IACrCvB,MAAM,CAACuB,yBAAyB,CAAC,CAACnB,OAAO,EAAE0B,WAAW,CAACC,GAAG,CAACL,SAAS,CAAC,CAAC;AAC9E;AACA,SAASf,mBAAmBA,CAACP,OAAO,EAAE0B,WAAW,EAAE;EAC/C,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,EAAE;EAAE;EAChD,IAAI,CAAC1B,OAAO,EACR;EACJ,OAAO,8FAA8F,CAACS,MAAM,CAACmB,kBAAkB,CAACC,IAAI,CAACP,SAAS,CAAC;IAC3I3B,OAAO,EAAEA,OAAO;IAChBK,OAAO,EAAEA,OAAO;IAChBC,IAAI,EAAEyB,WAAW,CAACC,GAAG,CAACL,SAAS;EACnC,CAAC,CAAC,CAAC,CAAC;AACR;AACA,SAAS9B,SAAS,EAAEE,cAAc,EAAEuB,iBAAiB,EAAEE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}