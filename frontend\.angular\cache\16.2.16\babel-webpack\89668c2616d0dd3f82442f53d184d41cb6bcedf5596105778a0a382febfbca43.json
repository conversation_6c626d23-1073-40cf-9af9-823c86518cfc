{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { adapterFactory as baseAdapterFactory } from 'calendar-utils/date-adapters/date-fns';\nimport { addWeeks, addMonths, subDays, subWeeks, subMonths, getISOWeek, setDate, setMonth, setYear, getDate, getYear } from 'date-fns';\nexport function adapterFactory() {\n  return __assign(__assign({}, baseAdapterFactory()), {\n    addWeeks: addWeeks,\n    addMonths: addMonths,\n    subDays: subDays,\n    subWeeks: subWeeks,\n    subMonths: subMonths,\n    getISOWeek: getISOWeek,\n    setDate: setDate,\n    setMonth: setMonth,\n    setYear: setYear,\n    getDate: getDate,\n    getYear: getYear\n  });\n}", "map": {"version": 3, "names": ["__assign", "adapterFactory", "baseAdapterFactory", "addWeeks", "addMonths", "subDays", "subWeeks", "subMonths", "getISOWeek", "setDate", "setMonth", "setYear", "getDate", "getYear"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/angular-calendar/date-adapters/esm/date-fns/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { adapterFactory as baseAdapterFactory } from 'calendar-utils/date-adapters/date-fns';\nimport { addWeeks, addMonths, subDays, subWeeks, subMonths, getISOWeek, setDate, setMonth, setYear, getDate, getYear, } from 'date-fns';\nexport function adapterFactory() {\n    return __assign(__assign({}, baseAdapterFactory()), { addWeeks: addWeeks, addMonths: addMonths, subDays: subDays, subWeeks: subWeeks, subMonths: subMonths, getISOWeek: getISOWeek, setDate: setDate, setMonth: setMonth, setYear: setYear, getDate: getDate, getYear: getYear });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,cAAc,IAAIC,kBAAkB,QAAQ,uCAAuC;AAC5F,SAASC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAS,UAAU;AACvI,OAAO,SAASZ,cAAcA,CAAA,EAAG;EAC7B,OAAOD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC,CAAC,CAAC,EAAE;IAAEC,QAAQ,EAAEA,QAAQ;IAAEC,SAAS,EAAEA,SAAS;IAAEC,OAAO,EAAEA,OAAO;IAAEC,QAAQ,EAAEA,QAAQ;IAAEC,SAAS,EAAEA,SAAS;IAAEC,UAAU,EAAEA,UAAU;IAAEC,OAAO,EAAEA,OAAO;IAAEC,QAAQ,EAAEA,QAAQ;IAAEC,OAAO,EAAEA,OAAO;IAAEC,OAAO,EAAEA,OAAO;IAAEC,OAAO,EAAEA;EAAQ,CAAC,CAAC;AACrR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}