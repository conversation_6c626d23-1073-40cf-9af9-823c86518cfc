{"ast": null, "code": "import \"../../utilities/globals/index.js\";\nexport { empty } from \"./empty.js\";\nexport { from } from \"./from.js\";\nexport { split } from \"./split.js\";\nexport { concat } from \"./concat.js\";\nexport { execute } from \"./execute.js\";\nexport { ApolloLink } from \"./ApolloLink.js\";", "map": {"version": 3, "names": ["empty", "from", "split", "concat", "execute", "ApolloLink"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/index.js"], "sourcesContent": ["import \"../../utilities/globals/index.js\";\nexport { empty } from \"./empty.js\";\nexport { from } from \"./from.js\";\nexport { split } from \"./split.js\";\nexport { concat } from \"./concat.js\";\nexport { execute } from \"./execute.js\";\nexport { ApolloLink } from \"./ApolloLink.js\";\n"], "mappings": "AAAA,OAAO,kCAAkC;AACzC,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,UAAU,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}