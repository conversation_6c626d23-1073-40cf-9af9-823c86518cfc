{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EquipeLayoutComponent,\n  children: [\n  // Liste des équipes\n  {\n    path: '',\n    component: EquipeComponent\n  }, {\n    path: 'liste',\n    component: EquipeListComponent\n  }, {\n    path: 'mes-equipes',\n    component: EquipeListComponent\n  },\n  // Formulaire pour ajouter une nouvelle équipe\n  {\n    path: 'ajouter',\n    component: EquipeFormComponent\n  }, {\n    path: 'nouveau',\n    component: EquipeFormComponent\n  },\n  // Formulaire pour modifier une équipe existante\n  {\n    path: 'modifier/:id',\n    component: EquipeFormComponent\n  },\n  // Détails d'une équipe spécifique\n  {\n    path: 'detail/:id',\n    component: EquipeDetailComponent\n  },\n  // Gestion des tâches d'une équipe\n  {\n    path: 'tasks/:id',\n    component: TaskListComponent\n  }]\n}];\nexport class EquipesRoutingModule {\n  static {\n    this.ɵfac = function EquipesRoutingModule_Factory(t) {\n      return new (t || EquipesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EquipesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EquipesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "EquipeListComponent", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "EquipeComponent", "EquipeLayoutComponent", "routes", "path", "component", "children", "EquipesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipes-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\r\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\r\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\r\nimport { TaskListComponent } from './task-list/task-list.component';\r\nimport { EquipeComponent } from './equipe/equipe.component';\r\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: EquipeLayoutComponent,\r\n    children: [\r\n      // Liste des équipes\r\n      { path: '', component: EquipeComponent },\r\n\r\n      { path: 'liste', component: EquipeListComponent },\r\n      { path: 'mes-equipes', component: EquipeListComponent },\r\n\r\n      // Formulaire pour ajouter une nouvelle équipe\r\n      { path: 'ajouter', component: EquipeFormComponent },\r\n      { path: 'nouveau', component: EquipeFormComponent },\r\n\r\n      // Formulaire pour modifier une équipe existante\r\n      { path: 'modifier/:id', component: EquipeFormComponent },\r\n\r\n      // Détails d'une équipe spécifique\r\n      { path: 'detail/:id', component: EquipeDetailComponent },\r\n\r\n      // Gestion des tâches d'une équipe\r\n      { path: 'tasks/:id', component: TaskListComponent },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class EquipesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,yCAAyC;;;AAE/E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qBAAqB;EAChCI,QAAQ,EAAE;EACR;EACA;IAAEF,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEJ;EAAe,CAAE,EAExC;IAAEG,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAmB,CAAE,EACjD;IAAEO,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAER;EAAmB,CAAE;EAEvD;EACA;IAAEO,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEP;EAAmB,CAAE,EACnD;IAAEM,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEP;EAAmB,CAAE;EAEnD;EACA;IAAEM,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEP;EAAmB,CAAE;EAExD;EACA;IAAEM,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEN;EAAqB,CAAE;EAExD;EACA;IAAEK,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEL;EAAiB,CAAE;CAEtD,CACF;AAMD,OAAM,MAAOO,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBX,YAAY,CAACY,QAAQ,CAACL,MAAM,CAAC,EAC7BP,YAAY;IAAA;EAAA;;;2EAEXW,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFrBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}