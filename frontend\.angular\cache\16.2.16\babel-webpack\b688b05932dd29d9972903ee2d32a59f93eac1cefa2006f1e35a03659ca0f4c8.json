{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction VerifyEmailComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵelement(3, \"i\", 41)(4, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"p\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction VerifyEmailComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 39)(2, \"div\", 46);\n    i0.ɵɵelement(3, \"i\", 47)(4, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"p\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction VerifyEmailComponent_i_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n}\nfunction VerifyEmailComponent_i_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction VerifyEmailComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.timer, \"s)\");\n  }\n}\nexport class VerifyEmailComponent {\n  constructor(route, router, authService, fb) {\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.fb = fb;\n    this.loading = false;\n    this.error = null;\n    this.message = null;\n    this.email = '';\n    this.timer = 60;\n    this.canResend = false;\n    this.verifyForm = this.fb.group({\n      code: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Get email from URL query params\n    this.email = this.route.snapshot.queryParamMap.get('email') || '';\n    // Check if we have an email to verify\n    if (!this.email) {\n      this.error = 'No email address provided for verification.';\n      return;\n    }\n    // Start the countdown for resend\n    this.startCountdown();\n  }\n  startCountdown() {\n    this.canResend = false;\n    this.timer = 60;\n    this.intervalId = setInterval(() => {\n      this.timer--;\n      if (this.timer === 0) {\n        this.canResend = true;\n        clearInterval(this.intervalId);\n      }\n    }, 1000);\n  }\n  onVerifySubmit() {\n    if (this.verifyForm.invalid) return;\n    this.loading = true;\n    this.error = null;\n    this.message = null;\n    const verifyData = {\n      email: this.email,\n      code: this.verifyForm.value.code\n    };\n    this.authService.verifyEmail(verifyData).subscribe({\n      next: res => {\n        this.loading = false;\n        this.message = res.message + ' Redirection vers la page de connexion...';\n        // Reset form\n        this.verifyForm.reset();\n        // Redirect after 1.5 seconds\n        setTimeout(() => {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              message: 'Votre compte a été vérifié avec succès. Vous pouvez maintenant vous connecter.'\n            }\n          });\n        }, 1500);\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.error?.message || 'Échec de la vérification';\n      }\n    });\n  }\n  resendCode() {\n    if (!this.canResend) return;\n    this.loading = true;\n    this.error = null;\n    this.message = null;\n    this.authService.resendCode(this.email).subscribe({\n      next: res => {\n        this.loading = false;\n        this.message = res.message || 'Code renvoyé avec succès.';\n        this.startCountdown();\n      },\n      error: err => {\n        this.loading = false;\n        this.error = err.error?.message || \"Erreur lors de l'envoi du code.\";\n      }\n    });\n  }\n  static {\n    this.ɵfac = function VerifyEmailComponent_Factory(t) {\n      return new (t || VerifyEmailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VerifyEmailComponent,\n      selectors: [[\"app-verify-email\"]],\n      decls: 56,\n      vars: 16,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-key\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"formControlName\", \"code\", \"placeholder\", \"123456\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-check mr-2\", 4, \"ngIf\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"type\", \"button\", 1, \"ml-1.5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1\", \"text-xs\"], [\"class\", \"ml-1\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"text-sm\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"fas\", \"fa-check\", \"mr-2\"], [1, \"ml-1\"]],\n      template: function VerifyEmailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵtext(23, \" Email Verification \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 13);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function VerifyEmailComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.onVerifySubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵtext(31, \" Verification Code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"div\", 21);\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(36, VerifyEmailComponent_div_36_Template, 8, 1, \"div\", 23);\n          i0.ɵɵtemplate(37, VerifyEmailComponent_div_37_Template, 8, 1, \"div\", 24);\n          i0.ɵɵelementStart(38, \"button\", 25);\n          i0.ɵɵelement(39, \"div\", 26)(40, \"div\", 27);\n          i0.ɵɵelementStart(41, \"span\", 28);\n          i0.ɵɵtemplate(42, VerifyEmailComponent_i_42_Template, 1, 0, \"i\", 29);\n          i0.ɵɵtemplate(43, VerifyEmailComponent_i_43_Template, 1, 0, \"i\", 30);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 31)(46, \"div\", 32)(47, \"span\");\n          i0.ɵɵtext(48, \"Didn't receive a code?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function VerifyEmailComponent_Template_button_click_49_listener() {\n            return ctx.resendCode();\n          });\n          i0.ɵɵelement(50, \"i\", 34);\n          i0.ɵɵtext(51, \" Resend \");\n          i0.ɵɵtemplate(52, VerifyEmailComponent_span_52_Template, 2, 1, \"span\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 36)(54, \"a\", 37);\n          i0.ɵɵtext(55, \" Back to Login \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\" A verification code has been sent to \", ctx.email, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.verifyForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.verifyForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"opacity-70\", ctx.loading || ctx.verifyForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"opacity-0\", ctx.loading || ctx.verifyForm.invalid);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.canResend || ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"animate-spin\", !ctx.canResend);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.canResend);\n        }\n      },\n      dependencies: [i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ2ZXJpZnktZW1haWwuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvdmVyaWZ5LWVtYWlsL3ZlcmlmeS1lbWFpbC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsd0tBQXdLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ctx_r1", "message", "ctx_r4", "timer", "VerifyEmailComponent", "constructor", "route", "router", "authService", "fb", "loading", "email", "canResend", "verifyForm", "group", "code", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "queryParamMap", "get", "startCountdown", "intervalId", "setInterval", "clearInterval", "onVerifySubmit", "invalid", "verifyData", "value", "verifyEmail", "subscribe", "next", "res", "reset", "setTimeout", "navigate", "queryParams", "err", "resendCode", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AuthService", "i3", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "VerifyEmailComponent_Template", "rf", "ctx", "ɵɵlistener", "VerifyEmailComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "VerifyEmailComponent_div_36_Template", "VerifyEmailComponent_div_37_Template", "VerifyEmailComponent_i_42_Template", "VerifyEmailComponent_i_43_Template", "VerifyEmailComponent_Template_button_click_49_listener", "VerifyEmailComponent_span_52_Template", "ɵɵproperty", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\verify-email\\verify-email.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\verify-email\\verify-email.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from '../../../services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-verify-email',\r\n  templateUrl: './verify-email.component.html',\r\n  styleUrls: ['./verify-email.component.css'],\r\n})\r\nexport class VerifyEmailComponent implements OnInit {\r\n  verifyForm: FormGroup;\r\n  loading = false;\r\n  error: string | null = null;\r\n  message: string | null = null;\r\n  email: string = '';\r\n  timer = 60;\r\n  canResend = false;\r\n  intervalId: any;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.verifyForm = this.fb.group({\r\n      code: [\r\n        '',\r\n        [Validators.required, Validators.minLength(6), Validators.maxLength(6)],\r\n      ],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get email from URL query params\r\n    this.email = this.route.snapshot.queryParamMap.get('email') || '';\r\n\r\n    // Check if we have an email to verify\r\n    if (!this.email) {\r\n      this.error = 'No email address provided for verification.';\r\n      return;\r\n    }\r\n\r\n    // Start the countdown for resend\r\n    this.startCountdown();\r\n  }\r\n\r\n  startCountdown() {\r\n    this.canResend = false;\r\n    this.timer = 60;\r\n\r\n    this.intervalId = setInterval(() => {\r\n      this.timer--;\r\n      if (this.timer === 0) {\r\n        this.canResend = true;\r\n        clearInterval(this.intervalId);\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  onVerifySubmit() {\r\n    if (this.verifyForm.invalid) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n    this.message = null;\r\n\r\n    const verifyData = {\r\n      email: this.email,\r\n      code: this.verifyForm.value.code,\r\n    };\r\n\r\n    this.authService.verifyEmail(verifyData).subscribe({\r\n      next: (res: any) => {\r\n        this.loading = false;\r\n        this.message =\r\n          res.message + ' Redirection vers la page de connexion...';\r\n\r\n        // Reset form\r\n        this.verifyForm.reset();\r\n\r\n        // Redirect after 1.5 seconds\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {\r\n            queryParams: {\r\n              message:\r\n                'Votre compte a été vérifié avec succès. Vous pouvez maintenant vous connecter.',\r\n            },\r\n          });\r\n        }, 1500);\r\n      },\r\n      error: (err) => {\r\n        this.loading = false;\r\n        this.error = err.error?.message || 'Échec de la vérification';\r\n      },\r\n    });\r\n  }\r\n\r\n  resendCode() {\r\n    if (!this.canResend) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n    this.message = null;\r\n\r\n    this.authService.resendCode(this.email).subscribe({\r\n      next: (res: any) => {\r\n        this.loading = false;\r\n        this.message = res.message || 'Code renvoyé avec succès.';\r\n        this.startCountdown();\r\n      },\r\n      error: (err) => {\r\n        this.loading = false;\r\n        this.error = err.error?.message || \"Erreur lors de l'envoi du code.\";\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div\r\n  class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] flex items-center justify-center p-4 relative futuristic-layout\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          Email Verification\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          A verification code has been sent to {{ email }}\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"p-6\">\r\n        <!-- Verification Form -->\r\n        <form\r\n          [formGroup]=\"verifyForm\"\r\n          (ngSubmit)=\"onVerifySubmit()\"\r\n          class=\"space-y-5\"\r\n        >\r\n          <!-- Code -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-key mr-1.5 text-xs\"></i>\r\n              Verification Code\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                formControlName=\"code\"\r\n                placeholder=\"123456\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            [disabled]=\"loading || verifyForm.invalid\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\r\n              [class.opacity-70]=\"loading || verifyForm.invalid\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n              [class.opacity-0]=\"loading || verifyForm.invalid\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <i *ngIf=\"loading\" class=\"fas fa-spinner fa-spin mr-2\"></i>\r\n              <i *ngIf=\"!loading\" class=\"fas fa-check mr-2\"></i>\r\n              {{ loading ? \"Verifying...\" : \"Verify\" }}\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Resend -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div class=\"flex items-center justify-center\">\r\n              <span>Didn't receive a code?</span>\r\n              <button\r\n                type=\"button\"\r\n                [disabled]=\"!canResend || loading\"\r\n                (click)=\"resendCode()\"\r\n                class=\"ml-1.5 text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n              >\r\n                <i\r\n                  class=\"fas fa-sync-alt mr-1 text-xs\"\r\n                  [class.animate-spin]=\"!canResend\"\r\n                ></i>\r\n                Resend\r\n                <span *ngIf=\"!canResend\" class=\"ml-1\">({{ timer }}s)</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Back to Login -->\r\n          <div class=\"text-center mt-4\">\r\n            <a\r\n              routerLink=\"/login\"\r\n              class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors text-sm\"\r\n            >\r\n              Back to Login\r\n            </a>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICsFzDC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;;;IAsBFV,EAAA,CAAAE,SAAA,YAA2D;;;;;IAC3DF,EAAA,CAAAE,SAAA,YAAkD;;;;;IAsBhDF,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAI,MAAA,GAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAArBH,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAM,kBAAA,MAAAK,MAAA,CAAAC,KAAA,OAAc;;;ADlKpE,OAAM,MAAOC,oBAAoB;EAU/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,EAAe;IAHf,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IAZZ,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAX,KAAK,GAAkB,IAAI;IAC3B,KAAAE,OAAO,GAAkB,IAAI;IAC7B,KAAAU,KAAK,GAAW,EAAE;IAClB,KAAAR,KAAK,GAAG,EAAE;IACV,KAAAS,SAAS,GAAG,KAAK;IASf,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC9BC,IAAI,EAAE,CACJ,EAAE,EACF,CAACzB,UAAU,CAAC0B,QAAQ,EAAE1B,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC,EAAE3B,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAAC;KAE1E,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,KAAK,GAAG,IAAI,CAACL,KAAK,CAACc,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;IAEjE;IACA,IAAI,CAAC,IAAI,CAACX,KAAK,EAAE;MACf,IAAI,CAACZ,KAAK,GAAG,6CAA6C;MAC1D;;IAGF;IACA,IAAI,CAACwB,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,CAACX,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,KAAK,GAAG,EAAE;IAEf,IAAI,CAACqB,UAAU,GAAGC,WAAW,CAAC,MAAK;MACjC,IAAI,CAACtB,KAAK,EAAE;MACZ,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;QACpB,IAAI,CAACS,SAAS,GAAG,IAAI;QACrBc,aAAa,CAAC,IAAI,CAACF,UAAU,CAAC;;IAElC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAG,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACd,UAAU,CAACe,OAAO,EAAE;IAE7B,IAAI,CAAClB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACX,KAAK,GAAG,IAAI;IACjB,IAAI,CAACE,OAAO,GAAG,IAAI;IAEnB,MAAM4B,UAAU,GAAG;MACjBlB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBI,IAAI,EAAE,IAAI,CAACF,UAAU,CAACiB,KAAK,CAACf;KAC7B;IAED,IAAI,CAACP,WAAW,CAACuB,WAAW,CAACF,UAAU,CAAC,CAACG,SAAS,CAAC;MACjDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACT,OAAO,GACViC,GAAG,CAACjC,OAAO,GAAG,2CAA2C;QAE3D;QACA,IAAI,CAACY,UAAU,CAACsB,KAAK,EAAE;QAEvB;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC/BC,WAAW,EAAE;cACXrC,OAAO,EACL;;WAEL,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDF,KAAK,EAAGwC,GAAG,IAAI;QACb,IAAI,CAAC7B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACX,KAAK,GAAGwC,GAAG,CAACxC,KAAK,EAAEE,OAAO,IAAI,0BAA0B;MAC/D;KACD,CAAC;EACJ;EAEAuC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC5B,SAAS,EAAE;IAErB,IAAI,CAACF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACX,KAAK,GAAG,IAAI;IACjB,IAAI,CAACE,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACO,WAAW,CAACgC,UAAU,CAAC,IAAI,CAAC7B,KAAK,CAAC,CAACqB,SAAS,CAAC;MAChDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACT,OAAO,GAAGiC,GAAG,CAACjC,OAAO,IAAI,2BAA2B;QACzD,IAAI,CAACsB,cAAc,EAAE;MACvB,CAAC;MACDxB,KAAK,EAAGwC,GAAG,IAAI;QACb,IAAI,CAAC7B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACX,KAAK,GAAGwC,GAAG,CAACxC,KAAK,EAAEE,OAAO,IAAI,iCAAiC;MACtE;KACD,CAAC;EACJ;;;uBA3GWG,oBAAoB,EAAAb,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAArD,EAAA,CAAAkD,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAvD,EAAA,CAAAkD,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB5C,oBAAoB;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjChE,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAI,MAAA,4BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,eAAiB;UAIbD,EAAA,CAAAkE,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAA7B,cAAA,EAAgB;UAAA,EAAC;UAI7BpC,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAAyC;UACzCF,EAAA,CAAAI,MAAA,2BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAIE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAoE,UAAA,KAAAC,oCAAA,kBAoBM;UAGNrE,EAAA,CAAAoE,UAAA,KAAAE,oCAAA,kBAoBM;UAGNtE,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAE,SAAA,eAGO;UAKPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAoE,UAAA,KAAAG,kCAAA,gBAA2D;UAC3DvE,EAAA,CAAAoE,UAAA,KAAAI,kCAAA,gBAAkD;UAClDxE,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAITH,EAAA,CAAAC,cAAA,eAEC;UAESD,EAAA,CAAAI,MAAA,8BAAsB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACnCH,EAAA,CAAAC,cAAA,kBAKC;UAFCD,EAAA,CAAAkE,UAAA,mBAAAO,uDAAA;YAAA,OAASR,GAAA,CAAAhB,UAAA,EAAY;UAAA,EAAC;UAGtBjD,EAAA,CAAAE,SAAA,aAGK;UACLF,EAAA,CAAAI,MAAA,gBACA;UAAAJ,EAAA,CAAAoE,UAAA,KAAAM,qCAAA,mBAA2D;UAC7D1E,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAA8B;UAK1BD,EAAA,CAAAI,MAAA,uBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;UAtINH,EAAA,CAAAK,SAAA,IACF;UADEL,EAAA,CAAAM,kBAAA,2CAAA2D,GAAA,CAAA7C,KAAA,MACF;UAOEpB,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA2E,UAAA,cAAAV,GAAA,CAAA3C,UAAA,CAAwB;UA8BrBtB,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA2E,UAAA,SAAAV,GAAA,CAAAzD,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2E,UAAA,SAAAV,GAAA,CAAAvD,OAAA,CAAa;UAwBdV,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAA2E,UAAA,aAAAV,GAAA,CAAA9C,OAAA,IAAA8C,GAAA,CAAA3C,UAAA,CAAAe,OAAA,CAA0C;UAKxCrC,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAA4E,WAAA,eAAAX,GAAA,CAAA9C,OAAA,IAAA8C,GAAA,CAAA3C,UAAA,CAAAe,OAAA,CAAkD;UAIlDrC,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAA4E,WAAA,cAAAX,GAAA,CAAA9C,OAAA,IAAA8C,GAAA,CAAA3C,UAAA,CAAAe,OAAA,CAAiD;UAK7CrC,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2E,UAAA,SAAAV,GAAA,CAAA9C,OAAA,CAAa;UACbnB,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAA2E,UAAA,UAAAV,GAAA,CAAA9C,OAAA,CAAc;UAClBnB,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAA2D,GAAA,CAAA9C,OAAA,kCACF;UAWInB,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAA2E,UAAA,cAAAV,GAAA,CAAA5C,SAAA,IAAA4C,GAAA,CAAA9C,OAAA,CAAkC;UAMhCnB,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAA4E,WAAA,kBAAAX,GAAA,CAAA5C,SAAA,CAAiC;UAG5BrB,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAA2E,UAAA,UAAAV,GAAA,CAAA5C,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}