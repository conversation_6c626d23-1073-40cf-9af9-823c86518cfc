{"ast": null, "code": "import { invariant } from \"../../utilities/globals/index.js\";\nimport { argumentsO<PERSON><PERSON><PERSON><PERSON><PERSON>, Deep<PERSON><PERSON>, isNonEmptyArray, isNonNullObject } from \"../../utilities/index.js\";\nimport { hasOwn, isArray } from \"./helpers.js\";\n// Mapping from JSON-encoded KeySpecifier strings to associated information.\nvar specifierInfoCache = Object.create(null);\nfunction lookupSpecifierInfo(spec) {\n  // It's safe to encode KeySpecifier arrays with JSON.stringify, since they're\n  // just arrays of strings or nested KeySpecifier arrays, and the order of the\n  // array elements is important (and suitably preserved by JSON.stringify).\n  var cacheKey = JSON.stringify(spec);\n  return specifierInfoCache[cacheKey] || (specifierInfoCache[cacheKey] = Object.create(null));\n}\nexport function keyFieldsFnFromSpecifier(specifier) {\n  var info = lookupSpecifierInfo(specifier);\n  return info.keyFieldsFn || (info.keyFieldsFn = function (object, context) {\n    var extract = function (from, key) {\n      return context.readField(key, from);\n    };\n    var keyObject = context.keyObject = collectSpecifierPaths(specifier, function (schemaKeyPath) {\n      var extracted = extractKeyPath(context.storeObject, schemaKeyPath,\n      // Using context.readField to extract paths from context.storeObject\n      // allows the extraction to see through Reference objects and respect\n      // custom read functions.\n      extract);\n      if (extracted === void 0 && object !== context.storeObject && hasOwn.call(object, schemaKeyPath[0])) {\n        // If context.storeObject fails to provide a value for the requested\n        // path, fall back to the raw result object, if it has a top-level key\n        // matching the first key in the path (schemaKeyPath[0]). This allows\n        // key fields included in the written data to be saved in the cache\n        // even if they are not selected explicitly in context.selectionSet.\n        // Not being mentioned by context.selectionSet is convenient here,\n        // since it means these extra fields cannot be affected by field\n        // aliasing, which is why we can use extractKey instead of\n        // context.readField for this extraction.\n        extracted = extractKeyPath(object, schemaKeyPath, extractKey);\n      }\n      invariant(extracted !== void 0, 5, schemaKeyPath.join(\".\"), object);\n      return extracted;\n    });\n    return \"\".concat(context.typename, \":\").concat(JSON.stringify(keyObject));\n  });\n}\n// The keyArgs extraction process is roughly analogous to keyFields extraction,\n// but there are no aliases involved, missing fields are tolerated (by merely\n// omitting them from the key), and drawing from field.directives or variables\n// is allowed (in addition to drawing from the field's arguments object).\n// Concretely, these differences mean passing a different key path extractor\n// function to collectSpecifierPaths, reusing the shared extractKeyPath helper\n// wherever possible.\nexport function keyArgsFnFromSpecifier(specifier) {\n  var info = lookupSpecifierInfo(specifier);\n  return info.keyArgsFn || (info.keyArgsFn = function (args, _a) {\n    var field = _a.field,\n      variables = _a.variables,\n      fieldName = _a.fieldName;\n    var collected = collectSpecifierPaths(specifier, function (keyPath) {\n      var firstKey = keyPath[0];\n      var firstChar = firstKey.charAt(0);\n      if (firstChar === \"@\") {\n        if (field && isNonEmptyArray(field.directives)) {\n          var directiveName_1 = firstKey.slice(1);\n          // If the directive appears multiple times, only the first\n          // occurrence's arguments will be used. TODO Allow repetition?\n          // TODO Cache this work somehow, a la aliasMap?\n          var d = field.directives.find(function (d) {\n            return d.name.value === directiveName_1;\n          });\n          // Fortunately argumentsObjectFromField works for DirectiveNode!\n          var directiveArgs = d && argumentsObjectFromField(d, variables);\n          // For directives without arguments (d defined, but directiveArgs ===\n          // null), the presence or absence of the directive still counts as\n          // part of the field key, so we return null in those cases. If no\n          // directive with this name was found for this field (d undefined and\n          // thus directiveArgs undefined), we return undefined, which causes\n          // this value to be omitted from the key object returned by\n          // collectSpecifierPaths.\n          return directiveArgs && extractKeyPath(directiveArgs,\n          // If keyPath.length === 1, this code calls extractKeyPath with an\n          // empty path, which works because it uses directiveArgs as the\n          // extracted value.\n          keyPath.slice(1));\n        }\n        // If the key started with @ but there was no corresponding directive,\n        // we want to omit this value from the key object, not fall through to\n        // treating @whatever as a normal argument name.\n        return;\n      }\n      if (firstChar === \"$\") {\n        var variableName = firstKey.slice(1);\n        if (variables && hasOwn.call(variables, variableName)) {\n          var varKeyPath = keyPath.slice(0);\n          varKeyPath[0] = variableName;\n          return extractKeyPath(variables, varKeyPath);\n        }\n        // If the key started with $ but there was no corresponding variable, we\n        // want to omit this value from the key object, not fall through to\n        // treating $whatever as a normal argument name.\n        return;\n      }\n      if (args) {\n        return extractKeyPath(args, keyPath);\n      }\n    });\n    var suffix = JSON.stringify(collected);\n    // If no arguments were passed to this field, and it didn't have any other\n    // field key contributions from directives or variables, hide the empty\n    // :{} suffix from the field key. However, a field passed no arguments can\n    // still end up with a non-empty :{...} suffix if its key configuration\n    // refers to directives or variables.\n    if (args || suffix !== \"{}\") {\n      fieldName += \":\" + suffix;\n    }\n    return fieldName;\n  });\n}\nexport function collectSpecifierPaths(specifier, extractor) {\n  // For each path specified by specifier, invoke the extractor, and repeatedly\n  // merge the results together, with appropriate ancestor context.\n  var merger = new DeepMerger();\n  return getSpecifierPaths(specifier).reduce(function (collected, path) {\n    var _a;\n    var toMerge = extractor(path);\n    if (toMerge !== void 0) {\n      // This path is not expected to contain array indexes, so the toMerge\n      // reconstruction will not contain arrays. TODO Fix this?\n      for (var i = path.length - 1; i >= 0; --i) {\n        toMerge = (_a = {}, _a[path[i]] = toMerge, _a);\n      }\n      collected = merger.merge(collected, toMerge);\n    }\n    return collected;\n  }, Object.create(null));\n}\nexport function getSpecifierPaths(spec) {\n  var info = lookupSpecifierInfo(spec);\n  if (!info.paths) {\n    var paths_1 = info.paths = [];\n    var currentPath_1 = [];\n    spec.forEach(function (s, i) {\n      if (isArray(s)) {\n        getSpecifierPaths(s).forEach(function (p) {\n          return paths_1.push(currentPath_1.concat(p));\n        });\n        currentPath_1.length = 0;\n      } else {\n        currentPath_1.push(s);\n        if (!isArray(spec[i + 1])) {\n          paths_1.push(currentPath_1.slice(0));\n          currentPath_1.length = 0;\n        }\n      }\n    });\n  }\n  return info.paths;\n}\nfunction extractKey(object, key) {\n  return object[key];\n}\nexport function extractKeyPath(object, path, extract) {\n  // For each key in path, extract the corresponding child property from obj,\n  // flattening arrays if encountered (uncommon for keyFields and keyArgs, but\n  // possible). The final result of path.reduce is normalized so unexpected leaf\n  // objects have their keys safely sorted. That final result is difficult to\n  // type as anything other than any. You're welcome to try to improve the\n  // return type, but keep in mind extractKeyPath is not a public function\n  // (exported only for testing), so the effort may not be worthwhile unless the\n  // limited set of actual callers (see above) pass arguments that TypeScript\n  // can statically type. If we know only that path is some array of strings\n  // (and not, say, a specific tuple of statically known strings), any (or\n  // possibly unknown) is the honest answer.\n  extract = extract || extractKey;\n  return normalize(path.reduce(function reducer(obj, key) {\n    return isArray(obj) ? obj.map(function (child) {\n      return reducer(child, key);\n    }) : obj && extract(obj, key);\n  }, object));\n}\nfunction normalize(value) {\n  // Usually the extracted value will be a scalar value, since most primary\n  // key fields are scalar, but just in case we get an object or an array, we\n  // need to do some normalization of the order of (nested) keys.\n  if (isNonNullObject(value)) {\n    if (isArray(value)) {\n      return value.map(normalize);\n    }\n    return collectSpecifierPaths(Object.keys(value).sort(), function (path) {\n      return extractKeyPath(value, path);\n    });\n  }\n  return value;\n}", "map": {"version": 3, "names": ["invariant", "argumentsObjectFromField", "DeepMerger", "isNonEmptyArray", "isNonNullObject", "hasOwn", "isArray", "specifierInfoCache", "Object", "create", "lookupSpecifierInfo", "spec", "cache<PERSON>ey", "JSON", "stringify", "keyFieldsFnFromSpecifier", "specifier", "info", "keyFieldsFn", "object", "context", "extract", "from", "key", "readField", "keyObject", "collectSpecifierPaths", "schemaKeyPath", "extracted", "extractKeyPath", "storeObject", "call", "extractKey", "join", "concat", "typename", "keyArgsFnFromSpecifier", "keyArgsFn", "args", "_a", "field", "variables", "fieldName", "collected", "keyP<PERSON>", "firstKey", "firstChar", "char<PERSON>t", "directives", "directiveName_1", "slice", "d", "find", "name", "value", "directiveArgs", "variableName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix", "extractor", "merger", "getSpecifierPaths", "reduce", "path", "toMerge", "i", "length", "merge", "paths", "paths_1", "currentPath_1", "for<PERSON>ach", "s", "p", "push", "normalize", "reducer", "obj", "map", "child", "keys", "sort"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/key-extractor.js"], "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport { argumentsO<PERSON><PERSON><PERSON><PERSON><PERSON>, DeepMerger, isNonEmptyArray, isNonNullObject, } from \"../../utilities/index.js\";\nimport { hasOwn, isArray } from \"./helpers.js\";\n// Mapping from JSON-encoded KeySpecifier strings to associated information.\nvar specifierInfoCache = Object.create(null);\nfunction lookupSpecifierInfo(spec) {\n    // It's safe to encode KeySpecifier arrays with JSON.stringify, since they're\n    // just arrays of strings or nested KeySpecifier arrays, and the order of the\n    // array elements is important (and suitably preserved by JSON.stringify).\n    var cacheKey = JSON.stringify(spec);\n    return (specifierInfoCache[cacheKey] ||\n        (specifierInfoCache[cacheKey] = Object.create(null)));\n}\nexport function keyFieldsFnFromSpecifier(specifier) {\n    var info = lookupSpecifierInfo(specifier);\n    return (info.keyFieldsFn || (info.keyFieldsFn = function (object, context) {\n            var extract = function (from, key) {\n                return context.readField(key, from);\n            };\n            var keyObject = (context.keyObject = collectSpecifierPaths(specifier, function (schemaKeyPath) {\n                var extracted = extractKeyPath(context.storeObject, schemaKeyPath, \n                // Using context.readField to extract paths from context.storeObject\n                // allows the extraction to see through Reference objects and respect\n                // custom read functions.\n                extract);\n                if (extracted === void 0 &&\n                    object !== context.storeObject &&\n                    hasOwn.call(object, schemaKeyPath[0])) {\n                    // If context.storeObject fails to provide a value for the requested\n                    // path, fall back to the raw result object, if it has a top-level key\n                    // matching the first key in the path (schemaKeyPath[0]). This allows\n                    // key fields included in the written data to be saved in the cache\n                    // even if they are not selected explicitly in context.selectionSet.\n                    // Not being mentioned by context.selectionSet is convenient here,\n                    // since it means these extra fields cannot be affected by field\n                    // aliasing, which is why we can use extractKey instead of\n                    // context.readField for this extraction.\n                    extracted = extractKeyPath(object, schemaKeyPath, extractKey);\n                }\n                invariant(extracted !== void 0, 5, schemaKeyPath.join(\".\"), object);\n                return extracted;\n            }));\n            return \"\".concat(context.typename, \":\").concat(JSON.stringify(keyObject));\n        }));\n}\n// The keyArgs extraction process is roughly analogous to keyFields extraction,\n// but there are no aliases involved, missing fields are tolerated (by merely\n// omitting them from the key), and drawing from field.directives or variables\n// is allowed (in addition to drawing from the field's arguments object).\n// Concretely, these differences mean passing a different key path extractor\n// function to collectSpecifierPaths, reusing the shared extractKeyPath helper\n// wherever possible.\nexport function keyArgsFnFromSpecifier(specifier) {\n    var info = lookupSpecifierInfo(specifier);\n    return (info.keyArgsFn ||\n        (info.keyArgsFn = function (args, _a) {\n            var field = _a.field, variables = _a.variables, fieldName = _a.fieldName;\n            var collected = collectSpecifierPaths(specifier, function (keyPath) {\n                var firstKey = keyPath[0];\n                var firstChar = firstKey.charAt(0);\n                if (firstChar === \"@\") {\n                    if (field && isNonEmptyArray(field.directives)) {\n                        var directiveName_1 = firstKey.slice(1);\n                        // If the directive appears multiple times, only the first\n                        // occurrence's arguments will be used. TODO Allow repetition?\n                        // TODO Cache this work somehow, a la aliasMap?\n                        var d = field.directives.find(function (d) { return d.name.value === directiveName_1; });\n                        // Fortunately argumentsObjectFromField works for DirectiveNode!\n                        var directiveArgs = d && argumentsObjectFromField(d, variables);\n                        // For directives without arguments (d defined, but directiveArgs ===\n                        // null), the presence or absence of the directive still counts as\n                        // part of the field key, so we return null in those cases. If no\n                        // directive with this name was found for this field (d undefined and\n                        // thus directiveArgs undefined), we return undefined, which causes\n                        // this value to be omitted from the key object returned by\n                        // collectSpecifierPaths.\n                        return (directiveArgs &&\n                            extractKeyPath(directiveArgs, \n                            // If keyPath.length === 1, this code calls extractKeyPath with an\n                            // empty path, which works because it uses directiveArgs as the\n                            // extracted value.\n                            keyPath.slice(1)));\n                    }\n                    // If the key started with @ but there was no corresponding directive,\n                    // we want to omit this value from the key object, not fall through to\n                    // treating @whatever as a normal argument name.\n                    return;\n                }\n                if (firstChar === \"$\") {\n                    var variableName = firstKey.slice(1);\n                    if (variables && hasOwn.call(variables, variableName)) {\n                        var varKeyPath = keyPath.slice(0);\n                        varKeyPath[0] = variableName;\n                        return extractKeyPath(variables, varKeyPath);\n                    }\n                    // If the key started with $ but there was no corresponding variable, we\n                    // want to omit this value from the key object, not fall through to\n                    // treating $whatever as a normal argument name.\n                    return;\n                }\n                if (args) {\n                    return extractKeyPath(args, keyPath);\n                }\n            });\n            var suffix = JSON.stringify(collected);\n            // If no arguments were passed to this field, and it didn't have any other\n            // field key contributions from directives or variables, hide the empty\n            // :{} suffix from the field key. However, a field passed no arguments can\n            // still end up with a non-empty :{...} suffix if its key configuration\n            // refers to directives or variables.\n            if (args || suffix !== \"{}\") {\n                fieldName += \":\" + suffix;\n            }\n            return fieldName;\n        }));\n}\nexport function collectSpecifierPaths(specifier, extractor) {\n    // For each path specified by specifier, invoke the extractor, and repeatedly\n    // merge the results together, with appropriate ancestor context.\n    var merger = new DeepMerger();\n    return getSpecifierPaths(specifier).reduce(function (collected, path) {\n        var _a;\n        var toMerge = extractor(path);\n        if (toMerge !== void 0) {\n            // This path is not expected to contain array indexes, so the toMerge\n            // reconstruction will not contain arrays. TODO Fix this?\n            for (var i = path.length - 1; i >= 0; --i) {\n                toMerge = (_a = {}, _a[path[i]] = toMerge, _a);\n            }\n            collected = merger.merge(collected, toMerge);\n        }\n        return collected;\n    }, Object.create(null));\n}\nexport function getSpecifierPaths(spec) {\n    var info = lookupSpecifierInfo(spec);\n    if (!info.paths) {\n        var paths_1 = (info.paths = []);\n        var currentPath_1 = [];\n        spec.forEach(function (s, i) {\n            if (isArray(s)) {\n                getSpecifierPaths(s).forEach(function (p) { return paths_1.push(currentPath_1.concat(p)); });\n                currentPath_1.length = 0;\n            }\n            else {\n                currentPath_1.push(s);\n                if (!isArray(spec[i + 1])) {\n                    paths_1.push(currentPath_1.slice(0));\n                    currentPath_1.length = 0;\n                }\n            }\n        });\n    }\n    return info.paths;\n}\nfunction extractKey(object, key) {\n    return object[key];\n}\nexport function extractKeyPath(object, path, extract) {\n    // For each key in path, extract the corresponding child property from obj,\n    // flattening arrays if encountered (uncommon for keyFields and keyArgs, but\n    // possible). The final result of path.reduce is normalized so unexpected leaf\n    // objects have their keys safely sorted. That final result is difficult to\n    // type as anything other than any. You're welcome to try to improve the\n    // return type, but keep in mind extractKeyPath is not a public function\n    // (exported only for testing), so the effort may not be worthwhile unless the\n    // limited set of actual callers (see above) pass arguments that TypeScript\n    // can statically type. If we know only that path is some array of strings\n    // (and not, say, a specific tuple of statically known strings), any (or\n    // possibly unknown) is the honest answer.\n    extract = extract || extractKey;\n    return normalize(path.reduce(function reducer(obj, key) {\n        return isArray(obj) ?\n            obj.map(function (child) { return reducer(child, key); })\n            : obj && extract(obj, key);\n    }, object));\n}\nfunction normalize(value) {\n    // Usually the extracted value will be a scalar value, since most primary\n    // key fields are scalar, but just in case we get an object or an array, we\n    // need to do some normalization of the order of (nested) keys.\n    if (isNonNullObject(value)) {\n        if (isArray(value)) {\n            return value.map(normalize);\n        }\n        return collectSpecifierPaths(Object.keys(value).sort(), function (path) {\n            return extractKeyPath(value, path);\n        });\n    }\n    return value;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,wBAAwB,EAAEC,UAAU,EAAEC,eAAe,EAAEC,eAAe,QAAS,0BAA0B;AAClH,SAASC,MAAM,EAAEC,OAAO,QAAQ,cAAc;AAC9C;AACA,IAAIC,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAC5C,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EAC/B;EACA;EACA;EACA,IAAIC,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;EACnC,OAAQJ,kBAAkB,CAACK,QAAQ,CAAC,KAC/BL,kBAAkB,CAACK,QAAQ,CAAC,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5D;AACA,OAAO,SAASM,wBAAwBA,CAACC,SAAS,EAAE;EAChD,IAAIC,IAAI,GAAGP,mBAAmB,CAACM,SAAS,CAAC;EACzC,OAAQC,IAAI,CAACC,WAAW,KAAKD,IAAI,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACnE,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEC,GAAG,EAAE;MAC/B,OAAOH,OAAO,CAACI,SAAS,CAACD,GAAG,EAAED,IAAI,CAAC;IACvC,CAAC;IACD,IAAIG,SAAS,GAAIL,OAAO,CAACK,SAAS,GAAGC,qBAAqB,CAACV,SAAS,EAAE,UAAUW,aAAa,EAAE;MAC3F,IAAIC,SAAS,GAAGC,cAAc,CAACT,OAAO,CAACU,WAAW,EAAEH,aAAa;MACjE;MACA;MACA;MACAN,OAAO,CAAC;MACR,IAAIO,SAAS,KAAK,KAAK,CAAC,IACpBT,MAAM,KAAKC,OAAO,CAACU,WAAW,IAC9BzB,MAAM,CAAC0B,IAAI,CAACZ,MAAM,EAAEQ,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,SAAS,GAAGC,cAAc,CAACV,MAAM,EAAEQ,aAAa,EAAEK,UAAU,CAAC;MACjE;MACAhC,SAAS,CAAC4B,SAAS,KAAK,KAAK,CAAC,EAAE,CAAC,EAAED,aAAa,CAACM,IAAI,CAAC,GAAG,CAAC,EAAEd,MAAM,CAAC;MACnE,OAAOS,SAAS;IACpB,CAAC,CAAE;IACH,OAAO,EAAE,CAACM,MAAM,CAACd,OAAO,CAACe,QAAQ,EAAE,GAAG,CAAC,CAACD,MAAM,CAACrB,IAAI,CAACC,SAAS,CAACW,SAAS,CAAC,CAAC;EAC7E,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,sBAAsBA,CAACpB,SAAS,EAAE;EAC9C,IAAIC,IAAI,GAAGP,mBAAmB,CAACM,SAAS,CAAC;EACzC,OAAQC,IAAI,CAACoB,SAAS,KACjBpB,IAAI,CAACoB,SAAS,GAAG,UAAUC,IAAI,EAAEC,EAAE,EAAE;IAClC,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;MAAEC,SAAS,GAAGF,EAAE,CAACE,SAAS;MAAEC,SAAS,GAAGH,EAAE,CAACG,SAAS;IACxE,IAAIC,SAAS,GAAGjB,qBAAqB,CAACV,SAAS,EAAE,UAAU4B,OAAO,EAAE;MAChE,IAAIC,QAAQ,GAAGD,OAAO,CAAC,CAAC,CAAC;MACzB,IAAIE,SAAS,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC;MAClC,IAAID,SAAS,KAAK,GAAG,EAAE;QACnB,IAAIN,KAAK,IAAIrC,eAAe,CAACqC,KAAK,CAACQ,UAAU,CAAC,EAAE;UAC5C,IAAIC,eAAe,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC;UACvC;UACA;UACA;UACA,IAAIC,CAAC,GAAGX,KAAK,CAACQ,UAAU,CAACI,IAAI,CAAC,UAAUD,CAAC,EAAE;YAAE,OAAOA,CAAC,CAACE,IAAI,CAACC,KAAK,KAAKL,eAAe;UAAE,CAAC,CAAC;UACxF;UACA,IAAIM,aAAa,GAAGJ,CAAC,IAAIlD,wBAAwB,CAACkD,CAAC,EAAEV,SAAS,CAAC;UAC/D;UACA;UACA;UACA;UACA;UACA;UACA;UACA,OAAQc,aAAa,IACjB1B,cAAc,CAAC0B,aAAa;UAC5B;UACA;UACA;UACAX,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB;QACA;QACA;QACA;QACA;MACJ;MACA,IAAIJ,SAAS,KAAK,GAAG,EAAE;QACnB,IAAIU,YAAY,GAAGX,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC;QACpC,IAAIT,SAAS,IAAIpC,MAAM,CAAC0B,IAAI,CAACU,SAAS,EAAEe,YAAY,CAAC,EAAE;UACnD,IAAIC,UAAU,GAAGb,OAAO,CAACM,KAAK,CAAC,CAAC,CAAC;UACjCO,UAAU,CAAC,CAAC,CAAC,GAAGD,YAAY;UAC5B,OAAO3B,cAAc,CAACY,SAAS,EAAEgB,UAAU,CAAC;QAChD;QACA;QACA;QACA;QACA;MACJ;MACA,IAAInB,IAAI,EAAE;QACN,OAAOT,cAAc,CAACS,IAAI,EAAEM,OAAO,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,IAAIc,MAAM,GAAG7C,IAAI,CAACC,SAAS,CAAC6B,SAAS,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA,IAAIL,IAAI,IAAIoB,MAAM,KAAK,IAAI,EAAE;MACzBhB,SAAS,IAAI,GAAG,GAAGgB,MAAM;IAC7B;IACA,OAAOhB,SAAS;EACpB,CAAC,CAAC;AACV;AACA,OAAO,SAAShB,qBAAqBA,CAACV,SAAS,EAAE2C,SAAS,EAAE;EACxD;EACA;EACA,IAAIC,MAAM,GAAG,IAAI1D,UAAU,CAAC,CAAC;EAC7B,OAAO2D,iBAAiB,CAAC7C,SAAS,CAAC,CAAC8C,MAAM,CAAC,UAAUnB,SAAS,EAAEoB,IAAI,EAAE;IAClE,IAAIxB,EAAE;IACN,IAAIyB,OAAO,GAAGL,SAAS,CAACI,IAAI,CAAC;IAC7B,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;MACpB;MACA;MACA,KAAK,IAAIC,CAAC,GAAGF,IAAI,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACvCD,OAAO,IAAIzB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACwB,IAAI,CAACE,CAAC,CAAC,CAAC,GAAGD,OAAO,EAAEzB,EAAE,CAAC;MAClD;MACAI,SAAS,GAAGiB,MAAM,CAACO,KAAK,CAACxB,SAAS,EAAEqB,OAAO,CAAC;IAChD;IACA,OAAOrB,SAAS;EACpB,CAAC,EAAEnC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B;AACA,OAAO,SAASoD,iBAAiBA,CAAClD,IAAI,EAAE;EACpC,IAAIM,IAAI,GAAGP,mBAAmB,CAACC,IAAI,CAAC;EACpC,IAAI,CAACM,IAAI,CAACmD,KAAK,EAAE;IACb,IAAIC,OAAO,GAAIpD,IAAI,CAACmD,KAAK,GAAG,EAAG;IAC/B,IAAIE,aAAa,GAAG,EAAE;IACtB3D,IAAI,CAAC4D,OAAO,CAAC,UAAUC,CAAC,EAAEP,CAAC,EAAE;MACzB,IAAI3D,OAAO,CAACkE,CAAC,CAAC,EAAE;QACZX,iBAAiB,CAACW,CAAC,CAAC,CAACD,OAAO,CAAC,UAAUE,CAAC,EAAE;UAAE,OAAOJ,OAAO,CAACK,IAAI,CAACJ,aAAa,CAACpC,MAAM,CAACuC,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC;QAC5FH,aAAa,CAACJ,MAAM,GAAG,CAAC;MAC5B,CAAC,MACI;QACDI,aAAa,CAACI,IAAI,CAACF,CAAC,CAAC;QACrB,IAAI,CAAClE,OAAO,CAACK,IAAI,CAACsD,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UACvBI,OAAO,CAACK,IAAI,CAACJ,aAAa,CAACpB,KAAK,CAAC,CAAC,CAAC,CAAC;UACpCoB,aAAa,CAACJ,MAAM,GAAG,CAAC;QAC5B;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAOjD,IAAI,CAACmD,KAAK;AACrB;AACA,SAASpC,UAAUA,CAACb,MAAM,EAAEI,GAAG,EAAE;EAC7B,OAAOJ,MAAM,CAACI,GAAG,CAAC;AACtB;AACA,OAAO,SAASM,cAAcA,CAACV,MAAM,EAAE4C,IAAI,EAAE1C,OAAO,EAAE;EAClD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,OAAO,GAAGA,OAAO,IAAIW,UAAU;EAC/B,OAAO2C,SAAS,CAACZ,IAAI,CAACD,MAAM,CAAC,SAASc,OAAOA,CAACC,GAAG,EAAEtD,GAAG,EAAE;IACpD,OAAOjB,OAAO,CAACuE,GAAG,CAAC,GACfA,GAAG,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAE;MAAE,OAAOH,OAAO,CAACG,KAAK,EAAExD,GAAG,CAAC;IAAE,CAAC,CAAC,GACvDsD,GAAG,IAAIxD,OAAO,CAACwD,GAAG,EAAEtD,GAAG,CAAC;EAClC,CAAC,EAAEJ,MAAM,CAAC,CAAC;AACf;AACA,SAASwD,SAASA,CAACrB,KAAK,EAAE;EACtB;EACA;EACA;EACA,IAAIlD,eAAe,CAACkD,KAAK,CAAC,EAAE;IACxB,IAAIhD,OAAO,CAACgD,KAAK,CAAC,EAAE;MAChB,OAAOA,KAAK,CAACwB,GAAG,CAACH,SAAS,CAAC;IAC/B;IACA,OAAOjD,qBAAqB,CAAClB,MAAM,CAACwE,IAAI,CAAC1B,KAAK,CAAC,CAAC2B,IAAI,CAAC,CAAC,EAAE,UAAUlB,IAAI,EAAE;MACpE,OAAOlC,cAAc,CAACyB,KAAK,EAAES,IAAI,CAAC;IACtC,CAAC,CAAC;EACN;EACA,OAAOT,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}