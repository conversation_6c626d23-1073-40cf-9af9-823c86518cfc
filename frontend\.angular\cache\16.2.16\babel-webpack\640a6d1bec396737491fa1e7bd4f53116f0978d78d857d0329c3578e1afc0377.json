{"ast": null, "code": "import { isPlainObject } from \"./objects.js\";\nexport function omitDeep(value, key) {\n  return __omitDeep(value, key);\n}\nfunction __omitDeep(value, key, known) {\n  if (known === void 0) {\n    known = new Map();\n  }\n  if (known.has(value)) {\n    return known.get(value);\n  }\n  var modified = false;\n  if (Array.isArray(value)) {\n    var array_1 = [];\n    known.set(value, array_1);\n    value.forEach(function (value, index) {\n      var result = __omitDeep(value, key, known);\n      modified || (modified = result !== value);\n      array_1[index] = result;\n    });\n    if (modified) {\n      return array_1;\n    }\n  } else if (isPlainObject(value)) {\n    var obj_1 = Object.create(Object.getPrototypeOf(value));\n    known.set(value, obj_1);\n    Object.keys(value).forEach(function (k) {\n      if (k === key) {\n        modified = true;\n        return;\n      }\n      var result = __omitDeep(value[k], key, known);\n      modified || (modified = result !== value[k]);\n      obj_1[k] = result;\n    });\n    if (modified) {\n      return obj_1;\n    }\n  }\n  return value;\n}", "map": {"version": 3, "names": ["isPlainObject", "omitDeep", "value", "key", "__omitDeep", "known", "Map", "has", "get", "modified", "Array", "isArray", "array_1", "set", "for<PERSON>ach", "index", "result", "obj_1", "Object", "create", "getPrototypeOf", "keys", "k"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/omitDeep.js"], "sourcesContent": ["import { isPlainObject } from \"./objects.js\";\nexport function omitDeep(value, key) {\n    return __omitDeep(value, key);\n}\nfunction __omitDeep(value, key, known) {\n    if (known === void 0) { known = new Map(); }\n    if (known.has(value)) {\n        return known.get(value);\n    }\n    var modified = false;\n    if (Array.isArray(value)) {\n        var array_1 = [];\n        known.set(value, array_1);\n        value.forEach(function (value, index) {\n            var result = __omitDeep(value, key, known);\n            modified || (modified = result !== value);\n            array_1[index] = result;\n        });\n        if (modified) {\n            return array_1;\n        }\n    }\n    else if (isPlainObject(value)) {\n        var obj_1 = Object.create(Object.getPrototypeOf(value));\n        known.set(value, obj_1);\n        Object.keys(value).forEach(function (k) {\n            if (k === key) {\n                modified = true;\n                return;\n            }\n            var result = __omitDeep(value[k], key, known);\n            modified || (modified = result !== value[k]);\n            obj_1[k] = result;\n        });\n        if (modified) {\n            return obj_1;\n        }\n    }\n    return value;\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjC,OAAOC,UAAU,CAACF,KAAK,EAAEC,GAAG,CAAC;AACjC;AACA,SAASC,UAAUA,CAACF,KAAK,EAAEC,GAAG,EAAEE,KAAK,EAAE;EACnC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EAAE;EAC3C,IAAID,KAAK,CAACE,GAAG,CAACL,KAAK,CAAC,EAAE;IAClB,OAAOG,KAAK,CAACG,GAAG,CAACN,KAAK,CAAC;EAC3B;EACA,IAAIO,QAAQ,GAAG,KAAK;EACpB,IAAIC,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,EAAE;IACtB,IAAIU,OAAO,GAAG,EAAE;IAChBP,KAAK,CAACQ,GAAG,CAACX,KAAK,EAAEU,OAAO,CAAC;IACzBV,KAAK,CAACY,OAAO,CAAC,UAAUZ,KAAK,EAAEa,KAAK,EAAE;MAClC,IAAIC,MAAM,GAAGZ,UAAU,CAACF,KAAK,EAAEC,GAAG,EAAEE,KAAK,CAAC;MAC1CI,QAAQ,KAAKA,QAAQ,GAAGO,MAAM,KAAKd,KAAK,CAAC;MACzCU,OAAO,CAACG,KAAK,CAAC,GAAGC,MAAM;IAC3B,CAAC,CAAC;IACF,IAAIP,QAAQ,EAAE;MACV,OAAOG,OAAO;IAClB;EACJ,CAAC,MACI,IAAIZ,aAAa,CAACE,KAAK,CAAC,EAAE;IAC3B,IAAIe,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,cAAc,CAAClB,KAAK,CAAC,CAAC;IACvDG,KAAK,CAACQ,GAAG,CAACX,KAAK,EAAEe,KAAK,CAAC;IACvBC,MAAM,CAACG,IAAI,CAACnB,KAAK,CAAC,CAACY,OAAO,CAAC,UAAUQ,CAAC,EAAE;MACpC,IAAIA,CAAC,KAAKnB,GAAG,EAAE;QACXM,QAAQ,GAAG,IAAI;QACf;MACJ;MACA,IAAIO,MAAM,GAAGZ,UAAU,CAACF,KAAK,CAACoB,CAAC,CAAC,EAAEnB,GAAG,EAAEE,KAAK,CAAC;MAC7CI,QAAQ,KAAKA,QAAQ,GAAGO,MAAM,KAAKd,KAAK,CAACoB,CAAC,CAAC,CAAC;MAC5CL,KAAK,CAACK,CAAC,CAAC,GAAGN,MAAM;IACrB,CAAC,CAAC;IACF,IAAIP,QAAQ,EAAE;MACV,OAAOQ,KAAK;IAChB;EACJ;EACA,OAAOf,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}