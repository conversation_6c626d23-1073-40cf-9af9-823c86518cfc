{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { visit } from \"graphql\";\nexport function filterOperationVariables(variables, query) {\n  var result = __assign({}, variables);\n  var unusedNames = new Set(Object.keys(variables));\n  visit(query, {\n    Variable: function (node, _key, parent) {\n      // A variable type definition at the top level of a query is not\n      // enough to silence server-side errors about the variable being\n      // unused, so variable definitions do not count as usage.\n      // https://spec.graphql.org/draft/#sec-All-Variables-Used\n      if (parent && parent.kind !== \"VariableDefinition\") {\n        unusedNames.delete(node.name.value);\n      }\n    }\n  });\n  unusedNames.forEach(function (name) {\n    delete result[name];\n  });\n  return result;\n}", "map": {"version": 3, "names": ["__assign", "visit", "filterOperationVariables", "variables", "query", "result", "unusedNames", "Set", "Object", "keys", "Variable", "node", "_key", "parent", "kind", "delete", "name", "value", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/utils/filterOperationVariables.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { visit } from \"graphql\";\nexport function filterOperationVariables(variables, query) {\n    var result = __assign({}, variables);\n    var unusedNames = new Set(Object.keys(variables));\n    visit(query, {\n        Variable: function (node, _key, parent) {\n            // A variable type definition at the top level of a query is not\n            // enough to silence server-side errors about the variable being\n            // unused, so variable definitions do not count as usage.\n            // https://spec.graphql.org/draft/#sec-All-Variables-Used\n            if (parent &&\n                parent.kind !== \"VariableDefinition\") {\n                unusedNames.delete(node.name.value);\n            }\n        },\n    });\n    unusedNames.forEach(function (name) {\n        delete result[name];\n    });\n    return result;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACvD,IAAIC,MAAM,GAAGL,QAAQ,CAAC,CAAC,CAAC,EAAEG,SAAS,CAAC;EACpC,IAAIG,WAAW,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAAC;EACjDF,KAAK,CAACG,KAAK,EAAE;IACTM,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;MACpC;MACA;MACA;MACA;MACA,IAAIA,MAAM,IACNA,MAAM,CAACC,IAAI,KAAK,oBAAoB,EAAE;QACtCR,WAAW,CAACS,MAAM,CAACJ,IAAI,CAACK,IAAI,CAACC,KAAK,CAAC;MACvC;IACJ;EACJ,CAAC,CAAC;EACFX,WAAW,CAACY,OAAO,CAAC,UAAUF,IAAI,EAAE;IAChC,OAAOX,MAAM,CAACW,IAAI,CAAC;EACvB,CAAC,CAAC;EACF,OAAOX,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}