{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nexport var DAYS_OF_WEEK;\n(function (DAYS_OF_WEEK) {\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"SUNDAY\"] = 0] = \"SUNDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"MONDAY\"] = 1] = \"MONDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"TUESDAY\"] = 2] = \"TUESDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"WEDNESDAY\"] = 3] = \"WEDNESDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"THURSDAY\"] = 4] = \"THURSDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"FRIDAY\"] = 5] = \"FRIDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"SATURDAY\"] = 6] = \"SATURDAY\";\n})(DAYS_OF_WEEK || (DAYS_OF_WEEK = {}));\nvar DEFAULT_WEEKEND_DAYS = [DAYS_OF_WEEK.SUNDAY, DAYS_OF_WEEK.SATURDAY];\nvar DAYS_IN_WEEK = 7;\nvar HOURS_IN_DAY = 24;\nvar MINUTES_IN_HOUR = 60;\nexport var SECONDS_IN_DAY = 60 * 60 * 24;\nfunction getExcludedSeconds(dateAdapter, _a) {\n  var startDate = _a.startDate,\n    seconds = _a.seconds,\n    excluded = _a.excluded,\n    precision = _a.precision;\n  if (excluded.length < 1) {\n    return 0;\n  }\n  var addSeconds = dateAdapter.addSeconds,\n    getDay = dateAdapter.getDay,\n    addDays = dateAdapter.addDays;\n  var endDate = addSeconds(startDate, seconds - 1);\n  var dayStart = getDay(startDate);\n  var dayEnd = getDay(endDate);\n  var result = 0; // Calculated in seconds\n  var current = startDate;\n  var _loop_1 = function () {\n    var day = getDay(current);\n    if (excluded.some(function (excludedDay) {\n      return excludedDay === day;\n    })) {\n      result += calculateExcludedSeconds(dateAdapter, {\n        dayStart: dayStart,\n        dayEnd: dayEnd,\n        day: day,\n        precision: precision,\n        startDate: startDate,\n        endDate: endDate\n      });\n    }\n    current = addDays(current, 1);\n  };\n  while (current < endDate) {\n    _loop_1();\n  }\n  return result;\n}\nfunction calculateExcludedSeconds(dateAdapter, _a) {\n  var precision = _a.precision,\n    day = _a.day,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    startDate = _a.startDate,\n    endDate = _a.endDate;\n  var differenceInSeconds = dateAdapter.differenceInSeconds,\n    endOfDay = dateAdapter.endOfDay,\n    startOfDay = dateAdapter.startOfDay;\n  if (precision === 'minutes') {\n    if (day === dayStart) {\n      return differenceInSeconds(endOfDay(startDate), startDate) + 1;\n    } else if (day === dayEnd) {\n      return differenceInSeconds(endDate, startOfDay(endDate)) + 1;\n    }\n  }\n  return SECONDS_IN_DAY;\n}\nfunction getWeekViewEventSpan(dateAdapter, _a) {\n  var event = _a.event,\n    offset = _a.offset,\n    startOfWeekDate = _a.startOfWeekDate,\n    excluded = _a.excluded,\n    precision = _a.precision,\n    totalDaysInView = _a.totalDaysInView;\n  var max = dateAdapter.max,\n    differenceInSeconds = dateAdapter.differenceInSeconds,\n    addDays = dateAdapter.addDays,\n    endOfDay = dateAdapter.endOfDay,\n    differenceInDays = dateAdapter.differenceInDays;\n  var span = SECONDS_IN_DAY;\n  var begin = max([event.start, startOfWeekDate]);\n  if (event.end) {\n    switch (precision) {\n      case 'minutes':\n        span = differenceInSeconds(event.end, begin);\n        break;\n      default:\n        span = differenceInDays(addDays(endOfDay(event.end), 1), begin) * SECONDS_IN_DAY;\n        break;\n    }\n  }\n  var offsetSeconds = offset * SECONDS_IN_DAY;\n  var totalLength = offsetSeconds + span;\n  // the best way to detect if an event is outside the week-view\n  // is to check if the total span beginning (from startOfWeekDay or event start) exceeds the total days in the view\n  var secondsInView = totalDaysInView * SECONDS_IN_DAY;\n  if (totalLength > secondsInView) {\n    span = secondsInView - offsetSeconds;\n  }\n  span -= getExcludedSeconds(dateAdapter, {\n    startDate: begin,\n    seconds: span,\n    excluded: excluded,\n    precision: precision\n  });\n  return span / SECONDS_IN_DAY;\n}\nfunction getWeekViewEventOffset(dateAdapter, _a) {\n  var event = _a.event,\n    startOfWeekDate = _a.startOfWeek,\n    excluded = _a.excluded,\n    precision = _a.precision;\n  var differenceInDays = dateAdapter.differenceInDays,\n    startOfDay = dateAdapter.startOfDay,\n    differenceInSeconds = dateAdapter.differenceInSeconds;\n  if (event.start < startOfWeekDate) {\n    return 0;\n  }\n  var offset = 0;\n  switch (precision) {\n    case 'days':\n      offset = differenceInDays(startOfDay(event.start), startOfWeekDate) * SECONDS_IN_DAY;\n      break;\n    case 'minutes':\n      offset = differenceInSeconds(event.start, startOfWeekDate);\n      break;\n  }\n  offset -= getExcludedSeconds(dateAdapter, {\n    startDate: startOfWeekDate,\n    seconds: offset,\n    excluded: excluded,\n    precision: precision\n  });\n  return Math.abs(offset / SECONDS_IN_DAY);\n}\nfunction isEventIsPeriod(dateAdapter, _a) {\n  var event = _a.event,\n    periodStart = _a.periodStart,\n    periodEnd = _a.periodEnd;\n  var isSameSecond = dateAdapter.isSameSecond;\n  var eventStart = event.start;\n  var eventEnd = event.end || event.start;\n  if (eventStart > periodStart && eventStart < periodEnd) {\n    return true;\n  }\n  if (eventEnd > periodStart && eventEnd < periodEnd) {\n    return true;\n  }\n  if (eventStart < periodStart && eventEnd > periodEnd) {\n    return true;\n  }\n  if (isSameSecond(eventStart, periodStart) || isSameSecond(eventStart, periodEnd)) {\n    return true;\n  }\n  if (isSameSecond(eventEnd, periodStart) || isSameSecond(eventEnd, periodEnd)) {\n    return true;\n  }\n  return false;\n}\nexport function getEventsInPeriod(dateAdapter, _a) {\n  var events = _a.events,\n    periodStart = _a.periodStart,\n    periodEnd = _a.periodEnd;\n  return events.filter(function (event) {\n    return isEventIsPeriod(dateAdapter, {\n      event: event,\n      periodStart: periodStart,\n      periodEnd: periodEnd\n    });\n  });\n}\nfunction getWeekDay(dateAdapter, _a) {\n  var date = _a.date,\n    _b = _a.weekendDays,\n    weekendDays = _b === void 0 ? DEFAULT_WEEKEND_DAYS : _b;\n  var startOfDay = dateAdapter.startOfDay,\n    isSameDay = dateAdapter.isSameDay,\n    getDay = dateAdapter.getDay;\n  var today = startOfDay(new Date());\n  var day = getDay(date);\n  return {\n    date: date,\n    day: day,\n    isPast: date < today,\n    isToday: isSameDay(date, today),\n    isFuture: date > today,\n    isWeekend: weekendDays.indexOf(day) > -1\n  };\n}\nexport function getWeekViewHeader(dateAdapter, _a) {\n  var viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _b = _a.excluded,\n    excluded = _b === void 0 ? [] : _b,\n    weekendDays = _a.weekendDays,\n    _c = _a.viewStart,\n    viewStart = _c === void 0 ? dateAdapter.startOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _c,\n    _d = _a.viewEnd,\n    viewEnd = _d === void 0 ? dateAdapter.addDays(viewStart, DAYS_IN_WEEK) : _d;\n  var addDays = dateAdapter.addDays,\n    getDay = dateAdapter.getDay;\n  var days = [];\n  var date = viewStart;\n  while (date < viewEnd) {\n    if (!excluded.some(function (e) {\n      return getDay(date) === e;\n    })) {\n      days.push(getWeekDay(dateAdapter, {\n        date: date,\n        weekendDays: weekendDays\n      }));\n    }\n    date = addDays(date, 1);\n  }\n  return days;\n}\nexport function getDifferenceInDaysWithExclusions(dateAdapter, _a) {\n  var date1 = _a.date1,\n    date2 = _a.date2,\n    excluded = _a.excluded;\n  var date = date1;\n  var diff = 0;\n  while (date < date2) {\n    if (excluded.indexOf(dateAdapter.getDay(date)) === -1) {\n      diff++;\n    }\n    date = dateAdapter.addDays(date, 1);\n  }\n  return diff;\n}\nexport function getAllDayWeekEvents(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.precision,\n    precision = _d === void 0 ? 'days' : _d,\n    _e = _a.absolutePositionedEvents,\n    absolutePositionedEvents = _e === void 0 ? false : _e,\n    viewStart = _a.viewStart,\n    viewEnd = _a.viewEnd;\n  viewStart = dateAdapter.startOfDay(viewStart);\n  viewEnd = dateAdapter.endOfDay(viewEnd);\n  var differenceInSeconds = dateAdapter.differenceInSeconds,\n    differenceInDays = dateAdapter.differenceInDays;\n  var maxRange = getDifferenceInDaysWithExclusions(dateAdapter, {\n    date1: viewStart,\n    date2: viewEnd,\n    excluded: excluded\n  });\n  var totalDaysInView = differenceInDays(viewEnd, viewStart) + 1;\n  var eventsMapped = events.filter(function (event) {\n    return event.allDay;\n  }).map(function (event) {\n    var offset = getWeekViewEventOffset(dateAdapter, {\n      event: event,\n      startOfWeek: viewStart,\n      excluded: excluded,\n      precision: precision\n    });\n    var span = getWeekViewEventSpan(dateAdapter, {\n      event: event,\n      offset: offset,\n      startOfWeekDate: viewStart,\n      excluded: excluded,\n      precision: precision,\n      totalDaysInView: totalDaysInView\n    });\n    return {\n      event: event,\n      offset: offset,\n      span: span\n    };\n  }).filter(function (e) {\n    return e.offset < maxRange;\n  }).filter(function (e) {\n    return e.span > 0;\n  }).map(function (entry) {\n    return {\n      event: entry.event,\n      offset: entry.offset,\n      span: entry.span,\n      startsBeforeWeek: entry.event.start < viewStart,\n      endsAfterWeek: (entry.event.end || entry.event.start) > viewEnd\n    };\n  }).sort(function (itemA, itemB) {\n    var startSecondsDiff = differenceInSeconds(itemA.event.start, itemB.event.start);\n    if (startSecondsDiff === 0) {\n      return differenceInSeconds(itemB.event.end || itemB.event.start, itemA.event.end || itemA.event.start);\n    }\n    return startSecondsDiff;\n  });\n  var allDayEventRows = [];\n  var allocatedEvents = [];\n  eventsMapped.forEach(function (event, index) {\n    if (allocatedEvents.indexOf(event) === -1) {\n      allocatedEvents.push(event);\n      var rowSpan_1 = event.span + event.offset;\n      var otherRowEvents = eventsMapped.slice(index + 1).filter(function (nextEvent) {\n        if (nextEvent.offset >= rowSpan_1 && rowSpan_1 + nextEvent.span <= totalDaysInView && allocatedEvents.indexOf(nextEvent) === -1) {\n          var nextEventOffset = nextEvent.offset - rowSpan_1;\n          if (!absolutePositionedEvents) {\n            nextEvent.offset = nextEventOffset;\n          }\n          rowSpan_1 += nextEvent.span + nextEventOffset;\n          allocatedEvents.push(nextEvent);\n          return true;\n        }\n      });\n      var weekEvents = __spreadArray([event], otherRowEvents, true);\n      var id = weekEvents.filter(function (weekEvent) {\n        return weekEvent.event.id;\n      }).map(function (weekEvent) {\n        return weekEvent.event.id;\n      }).join('-');\n      allDayEventRows.push(__assign({\n        row: weekEvents\n      }, id ? {\n        id: id\n      } : {}));\n    }\n  });\n  return allDayEventRows;\n}\nfunction getWeekViewHourGrid(dateAdapter, _a) {\n  var events = _a.events,\n    viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    weekStartsOn = _a.weekStartsOn,\n    excluded = _a.excluded,\n    weekendDays = _a.weekendDays,\n    segmentHeight = _a.segmentHeight,\n    viewStart = _a.viewStart,\n    viewEnd = _a.viewEnd,\n    minimumEventHeight = _a.minimumEventHeight;\n  var dayViewHourGrid = getDayViewHourGrid(dateAdapter, {\n    viewDate: viewDate,\n    hourSegments: hourSegments,\n    hourDuration: hourDuration,\n    dayStart: dayStart,\n    dayEnd: dayEnd\n  });\n  var weekDays = getWeekViewHeader(dateAdapter, {\n    viewDate: viewDate,\n    weekStartsOn: weekStartsOn,\n    excluded: excluded,\n    weekendDays: weekendDays,\n    viewStart: viewStart,\n    viewEnd: viewEnd\n  });\n  var setHours = dateAdapter.setHours,\n    setMinutes = dateAdapter.setMinutes,\n    getHours = dateAdapter.getHours,\n    getMinutes = dateAdapter.getMinutes;\n  return weekDays.map(function (day) {\n    var dayView = getDayView(dateAdapter, {\n      events: events,\n      viewDate: day.date,\n      hourSegments: hourSegments,\n      dayStart: dayStart,\n      dayEnd: dayEnd,\n      segmentHeight: segmentHeight,\n      eventWidth: 1,\n      hourDuration: hourDuration,\n      minimumEventHeight: minimumEventHeight\n    });\n    var hours = dayViewHourGrid.map(function (hour) {\n      var segments = hour.segments.map(function (segment) {\n        var date = setMinutes(setHours(day.date, getHours(segment.date)), getMinutes(segment.date));\n        return __assign(__assign({}, segment), {\n          date: date\n        });\n      });\n      return __assign(__assign({}, hour), {\n        segments: segments\n      });\n    });\n    function getColumnCount(allEvents, prevOverlappingEvents) {\n      var columnCount = Math.max.apply(Math, prevOverlappingEvents.map(function (iEvent) {\n        return iEvent.left + 1;\n      }));\n      var nextOverlappingEvents = allEvents.filter(function (iEvent) {\n        return iEvent.left >= columnCount;\n      }).filter(function (iEvent) {\n        return getOverLappingWeekViewEvents(prevOverlappingEvents, iEvent.top, iEvent.top + iEvent.height).length > 0;\n      });\n      if (nextOverlappingEvents.length > 0) {\n        return getColumnCount(allEvents, nextOverlappingEvents);\n      } else {\n        return columnCount;\n      }\n    }\n    var mappedEvents = dayView.events.map(function (event) {\n      var columnCount = getColumnCount(dayView.events, getOverLappingWeekViewEvents(dayView.events, event.top, event.top + event.height));\n      var width = 100 / columnCount;\n      return __assign(__assign({}, event), {\n        left: event.left * width,\n        width: width\n      });\n    });\n    return {\n      hours: hours,\n      date: day.date,\n      events: mappedEvents.map(function (event) {\n        var overLappingEvents = getOverLappingWeekViewEvents(mappedEvents.filter(function (otherEvent) {\n          return otherEvent.left > event.left;\n        }), event.top, event.top + event.height);\n        if (overLappingEvents.length > 0) {\n          return __assign(__assign({}, event), {\n            width: Math.min.apply(Math, overLappingEvents.map(function (otherEvent) {\n              return otherEvent.left;\n            })) - event.left\n          });\n        }\n        return event;\n      })\n    };\n  });\n}\nexport function getWeekView(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.precision,\n    precision = _d === void 0 ? 'days' : _d,\n    _e = _a.absolutePositionedEvents,\n    absolutePositionedEvents = _e === void 0 ? false : _e,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    weekendDays = _a.weekendDays,\n    segmentHeight = _a.segmentHeight,\n    minimumEventHeight = _a.minimumEventHeight,\n    _f = _a.viewStart,\n    viewStart = _f === void 0 ? dateAdapter.startOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _f,\n    _g = _a.viewEnd,\n    viewEnd = _g === void 0 ? dateAdapter.endOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _g;\n  if (!events) {\n    events = [];\n  }\n  var startOfDay = dateAdapter.startOfDay,\n    endOfDay = dateAdapter.endOfDay;\n  viewStart = startOfDay(viewStart);\n  viewEnd = endOfDay(viewEnd);\n  var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n    events: events,\n    periodStart: viewStart,\n    periodEnd: viewEnd\n  });\n  var header = getWeekViewHeader(dateAdapter, {\n    viewDate: viewDate,\n    weekStartsOn: weekStartsOn,\n    excluded: excluded,\n    weekendDays: weekendDays,\n    viewStart: viewStart,\n    viewEnd: viewEnd\n  });\n  return {\n    allDayEventRows: getAllDayWeekEvents(dateAdapter, {\n      events: eventsInPeriod,\n      excluded: excluded,\n      precision: precision,\n      absolutePositionedEvents: absolutePositionedEvents,\n      viewStart: viewStart,\n      viewEnd: viewEnd\n    }),\n    period: {\n      events: eventsInPeriod,\n      start: header[0].date,\n      end: endOfDay(header[header.length - 1].date)\n    },\n    hourColumns: getWeekViewHourGrid(dateAdapter, {\n      events: events,\n      viewDate: viewDate,\n      hourSegments: hourSegments,\n      hourDuration: hourDuration,\n      dayStart: dayStart,\n      dayEnd: dayEnd,\n      weekStartsOn: weekStartsOn,\n      excluded: excluded,\n      weekendDays: weekendDays,\n      segmentHeight: segmentHeight,\n      viewStart: viewStart,\n      viewEnd: viewEnd,\n      minimumEventHeight: minimumEventHeight\n    })\n  };\n}\nexport function getMonthView(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.viewStart,\n    viewStart = _d === void 0 ? dateAdapter.startOfMonth(viewDate) : _d,\n    _e = _a.viewEnd,\n    viewEnd = _e === void 0 ? dateAdapter.endOfMonth(viewDate) : _e,\n    weekendDays = _a.weekendDays;\n  if (!events) {\n    events = [];\n  }\n  var startOfWeek = dateAdapter.startOfWeek,\n    endOfWeek = dateAdapter.endOfWeek,\n    differenceInDays = dateAdapter.differenceInDays,\n    startOfDay = dateAdapter.startOfDay,\n    addHours = dateAdapter.addHours,\n    endOfDay = dateAdapter.endOfDay,\n    isSameMonth = dateAdapter.isSameMonth,\n    getDay = dateAdapter.getDay;\n  var start = startOfWeek(viewStart, {\n    weekStartsOn: weekStartsOn\n  });\n  var end = endOfWeek(viewEnd, {\n    weekStartsOn: weekStartsOn\n  });\n  var eventsInMonth = getEventsInPeriod(dateAdapter, {\n    events: events,\n    periodStart: start,\n    periodEnd: end\n  });\n  var initialViewDays = [];\n  var previousDate;\n  var _loop_2 = function (i) {\n    // hacky fix for https://github.com/mattlewis92/angular-calendar/issues/173\n    var date;\n    if (previousDate) {\n      date = startOfDay(addHours(previousDate, HOURS_IN_DAY));\n      if (previousDate.getTime() === date.getTime()) {\n        // DST change, so need to add 25 hours\n        /* istanbul ignore next */\n        date = startOfDay(addHours(previousDate, HOURS_IN_DAY + 1));\n      }\n      previousDate = date;\n    } else {\n      date = previousDate = start;\n    }\n    if (!excluded.some(function (e) {\n      return getDay(date) === e;\n    })) {\n      var day = getWeekDay(dateAdapter, {\n        date: date,\n        weekendDays: weekendDays\n      });\n      var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: eventsInMonth,\n        periodStart: startOfDay(date),\n        periodEnd: endOfDay(date)\n      });\n      day.inMonth = isSameMonth(date, viewDate);\n      day.events = eventsInPeriod;\n      day.badgeTotal = eventsInPeriod.length;\n      initialViewDays.push(day);\n    }\n  };\n  for (var i = 0; i < differenceInDays(end, start) + 1; i++) {\n    _loop_2(i);\n  }\n  var days = [];\n  var totalDaysVisibleInWeek = DAYS_IN_WEEK - excluded.length;\n  if (totalDaysVisibleInWeek < DAYS_IN_WEEK) {\n    for (var i = 0; i < initialViewDays.length; i += totalDaysVisibleInWeek) {\n      var row = initialViewDays.slice(i, i + totalDaysVisibleInWeek);\n      var isRowInMonth = row.some(function (day) {\n        return viewStart <= day.date && day.date < viewEnd;\n      });\n      if (isRowInMonth) {\n        days = __spreadArray(__spreadArray([], days, true), row, true);\n      }\n    }\n  } else {\n    days = initialViewDays;\n  }\n  var rows = Math.floor(days.length / totalDaysVisibleInWeek);\n  var rowOffsets = [];\n  for (var i = 0; i < rows; i++) {\n    rowOffsets.push(i * totalDaysVisibleInWeek);\n  }\n  return {\n    rowOffsets: rowOffsets,\n    totalDaysVisibleInWeek: totalDaysVisibleInWeek,\n    days: days,\n    period: {\n      start: days[0].date,\n      end: endOfDay(days[days.length - 1].date),\n      events: eventsInMonth\n    }\n  };\n}\nfunction getOverLappingWeekViewEvents(events, top, bottom) {\n  return events.filter(function (previousEvent) {\n    var previousEventTop = previousEvent.top;\n    var previousEventBottom = previousEvent.top + previousEvent.height;\n    if (top < previousEventBottom && previousEventBottom < bottom) {\n      return true;\n    } else if (top < previousEventTop && previousEventTop < bottom) {\n      return true;\n    } else if (previousEventTop <= top && bottom <= previousEventBottom) {\n      return true;\n    }\n    return false;\n  });\n}\nfunction getDayView(dateAdapter, _a) {\n  var events = _a.events,\n    viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    eventWidth = _a.eventWidth,\n    segmentHeight = _a.segmentHeight,\n    hourDuration = _a.hourDuration,\n    minimumEventHeight = _a.minimumEventHeight;\n  var setMinutes = dateAdapter.setMinutes,\n    setHours = dateAdapter.setHours,\n    startOfDay = dateAdapter.startOfDay,\n    startOfMinute = dateAdapter.startOfMinute,\n    endOfDay = dateAdapter.endOfDay,\n    differenceInMinutes = dateAdapter.differenceInMinutes;\n  var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n  var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n  endOfView.setSeconds(59, 999);\n  var previousDayEvents = [];\n  var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n    events: events.filter(function (event) {\n      return !event.allDay;\n    }),\n    periodStart: startOfView,\n    periodEnd: endOfView\n  });\n  var dayViewEvents = eventsInPeriod.sort(function (eventA, eventB) {\n    return eventA.start.valueOf() - eventB.start.valueOf();\n  }).map(function (event) {\n    var eventStart = event.start;\n    var eventEnd = event.end || eventStart;\n    var startsBeforeDay = eventStart < startOfView;\n    var endsAfterDay = eventEnd > endOfView;\n    var hourHeightModifier = hourSegments * segmentHeight / (hourDuration || MINUTES_IN_HOUR);\n    var top = 0;\n    if (eventStart > startOfView) {\n      // adjust the difference in minutes if the user's offset is different between the start of the day and the event (e.g. when going to or from DST)\n      var eventOffset = dateAdapter.getTimezoneOffset(eventStart);\n      var startOffset = dateAdapter.getTimezoneOffset(startOfView);\n      var diff = startOffset - eventOffset;\n      top += differenceInMinutes(eventStart, startOfView) + diff;\n    }\n    top *= hourHeightModifier;\n    top = Math.floor(top);\n    var startDate = startsBeforeDay ? startOfView : eventStart;\n    var endDate = endsAfterDay ? endOfView : eventEnd;\n    var timezoneOffset = dateAdapter.getTimezoneOffset(startDate) - dateAdapter.getTimezoneOffset(endDate);\n    var height = differenceInMinutes(endDate, startDate) + timezoneOffset;\n    if (!event.end) {\n      height = segmentHeight;\n    } else {\n      height *= hourHeightModifier;\n    }\n    if (minimumEventHeight && height < minimumEventHeight) {\n      height = minimumEventHeight;\n    }\n    height = Math.floor(height);\n    var bottom = top + height;\n    var overlappingPreviousEvents = getOverLappingWeekViewEvents(previousDayEvents, top, bottom);\n    var left = 0;\n    while (overlappingPreviousEvents.some(function (previousEvent) {\n      return previousEvent.left === left;\n    })) {\n      left += eventWidth;\n    }\n    var dayEvent = {\n      event: event,\n      height: height,\n      width: eventWidth,\n      top: top,\n      left: left,\n      startsBeforeDay: startsBeforeDay,\n      endsAfterDay: endsAfterDay\n    };\n    previousDayEvents.push(dayEvent);\n    return dayEvent;\n  });\n  var width = Math.max.apply(Math, dayViewEvents.map(function (event) {\n    return event.left + event.width;\n  }));\n  var allDayEvents = getEventsInPeriod(dateAdapter, {\n    events: events.filter(function (event) {\n      return event.allDay;\n    }),\n    periodStart: startOfDay(startOfView),\n    periodEnd: endOfDay(endOfView)\n  });\n  return {\n    events: dayViewEvents,\n    width: width,\n    allDayEvents: allDayEvents,\n    period: {\n      events: eventsInPeriod,\n      start: startOfView,\n      end: endOfView\n    }\n  };\n}\nfunction sanitiseHours(hours) {\n  return Math.max(Math.min(23, hours), 0);\n}\nfunction sanitiseMinutes(minutes) {\n  return Math.max(Math.min(59, minutes), 0);\n}\nfunction getDayViewHourGrid(dateAdapter, _a) {\n  var viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd;\n  var setMinutes = dateAdapter.setMinutes,\n    setHours = dateAdapter.setHours,\n    startOfDay = dateAdapter.startOfDay,\n    startOfMinute = dateAdapter.startOfMinute,\n    endOfDay = dateAdapter.endOfDay,\n    addMinutes = dateAdapter.addMinutes,\n    addDays = dateAdapter.addDays;\n  var hours = [];\n  var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n  var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n  var segmentDuration = (hourDuration || MINUTES_IN_HOUR) / hourSegments;\n  var startOfViewDay = startOfDay(viewDate);\n  var endOfViewDay = endOfDay(viewDate);\n  var dateAdjustment = function (d) {\n    return d;\n  };\n  // this means that we change from or to DST on this day and that's going to cause problems so we bump the date\n  if (dateAdapter.getTimezoneOffset(startOfViewDay) !== dateAdapter.getTimezoneOffset(endOfViewDay)) {\n    startOfViewDay = addDays(startOfViewDay, 1);\n    startOfView = addDays(startOfView, 1);\n    endOfView = addDays(endOfView, 1);\n    dateAdjustment = function (d) {\n      return addDays(d, -1);\n    };\n  }\n  var dayDuration = hourDuration ? HOURS_IN_DAY * 60 / hourDuration : MINUTES_IN_HOUR;\n  for (var i = 0; i < dayDuration; i++) {\n    var segments = [];\n    for (var j = 0; j < hourSegments; j++) {\n      var date = addMinutes(addMinutes(startOfView, i * (hourDuration || MINUTES_IN_HOUR)), j * segmentDuration);\n      if (date >= startOfView && date < endOfView) {\n        segments.push({\n          date: dateAdjustment(date),\n          displayDate: date,\n          isStart: j === 0\n        });\n      }\n    }\n    if (segments.length > 0) {\n      hours.push({\n        segments: segments\n      });\n    }\n  }\n  return hours;\n}\nexport var EventValidationErrorMessage;\n(function (EventValidationErrorMessage) {\n  EventValidationErrorMessage[\"NotArray\"] = \"Events must be an array\";\n  EventValidationErrorMessage[\"StartPropertyMissing\"] = \"Event is missing the `start` property\";\n  EventValidationErrorMessage[\"StartPropertyNotDate\"] = \"Event `start` property should be a javascript date object. Do `new Date(event.start)` to fix it.\";\n  EventValidationErrorMessage[\"EndPropertyNotDate\"] = \"Event `end` property should be a javascript date object. Do `new Date(event.end)` to fix it.\";\n  EventValidationErrorMessage[\"EndsBeforeStart\"] = \"Event `start` property occurs after the `end`\";\n})(EventValidationErrorMessage || (EventValidationErrorMessage = {}));\nexport function validateEvents(events, log) {\n  var isValid = true;\n  function isError(msg, event) {\n    log(msg, event);\n    isValid = false;\n  }\n  if (!Array.isArray(events)) {\n    log(EventValidationErrorMessage.NotArray, events);\n    return false;\n  }\n  events.forEach(function (event) {\n    if (!event.start) {\n      isError(EventValidationErrorMessage.StartPropertyMissing, event);\n    } else if (!(event.start instanceof Date)) {\n      isError(EventValidationErrorMessage.StartPropertyNotDate, event);\n    }\n    if (event.end) {\n      if (!(event.end instanceof Date)) {\n        isError(EventValidationErrorMessage.EndPropertyNotDate, event);\n      }\n      if (event.start > event.end) {\n        isError(EventValidationErrorMessage.EndsBeforeStart, event);\n      }\n    }\n  });\n  return isValid;\n}", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "Array", "slice", "concat", "DAYS_OF_WEEK", "DEFAULT_WEEKEND_DAYS", "SUNDAY", "SATURDAY", "DAYS_IN_WEEK", "HOURS_IN_DAY", "MINUTES_IN_HOUR", "SECONDS_IN_DAY", "getExcludedSeconds", "dateAdapter", "_a", "startDate", "seconds", "excluded", "precision", "addSeconds", "getDay", "addDays", "endDate", "dayStart", "dayEnd", "result", "current", "_loop_1", "day", "some", "excludedDay", "calculateExcludedSeconds", "differenceInSeconds", "endOfDay", "startOfDay", "getWeekViewEventSpan", "event", "offset", "startOfWeekDate", "totalDaysInView", "max", "differenceInDays", "span", "begin", "start", "end", "offsetSeconds", "totalLength", "secondsIn<PERSON><PERSON><PERSON>", "getWeekViewEventOffset", "startOfWeek", "Math", "abs", "isEventIsPeriod", "periodStart", "periodEnd", "isSameSecond", "eventStart", "eventEnd", "getEventsInPeriod", "events", "filter", "getWeekDay", "date", "_b", "weekendDays", "isSameDay", "today", "Date", "isPast", "isToday", "isFuture", "isWeekend", "indexOf", "getWeekViewHeader", "viewDate", "weekStartsOn", "_c", "viewStart", "_d", "viewEnd", "days", "e", "push", "getDifferenceInDaysWithExclusions", "date1", "date2", "diff", "getAllDayWeekEvents", "_e", "absolutePositionedEvents", "max<PERSON><PERSON><PERSON>", "eventsMapped", "allDay", "map", "entry", "startsBeforeWeek", "endsAfterWeek", "sort", "itemA", "itemB", "startSecondsDiff", "allDayEventRows", "allocatedEvents", "for<PERSON>ach", "index", "rowSpan_1", "otherRowEvents", "nextEvent", "nextEventOffset", "weekEvents", "id", "weekEvent", "join", "row", "getWeekViewHourGrid", "hourSegments", "hourDuration", "segmentHeight", "minimumEventHeight", "dayViewHourGrid", "getDayViewHourGrid", "weekDays", "setHours", "setMinutes", "getHours", "getMinutes", "<PERSON><PERSON><PERSON><PERSON>", "getDayView", "eventWidth", "hours", "hour", "segments", "segment", "getColumnCount", "allEvents", "prevOverlappingEvents", "columnCount", "iEvent", "left", "nextOverlappingEvents", "getOverLappingWeekViewEvents", "top", "height", "mappedEvents", "width", "overLappingEvents", "otherEvent", "min", "getWeekView", "_f", "_g", "endOfWeek", "eventsInPeriod", "header", "period", "hourColumns", "getMonthView", "startOfMonth", "endOfMonth", "addHours", "isSameMonth", "eventsInMonth", "initialViewDays", "previousDate", "_loop_2", "getTime", "inMonth", "badgeTotal", "totalDaysVisibleInWeek", "isRowInMonth", "rows", "floor", "rowOffsets", "bottom", "previousEvent", "previousEventTop", "previousEventBottom", "startOfMinute", "differenceInMinutes", "startOfView", "sanitiseHours", "sanitiseMinutes", "minute", "endOfView", "setSeconds", "previousDayEvents", "dayViewEvents", "eventA", "eventB", "valueOf", "startsBeforeDay", "endsAfterDay", "hourHeightModifier", "eventOffset", "getTimezoneOffset", "startOffset", "timezoneOffset", "overlappingPreviousEvents", "dayEvent", "allDayEvents", "minutes", "addMinutes", "segmentDuration", "startOfViewDay", "endOfViewDay", "dateAdjustment", "d", "dayDuration", "j", "displayDate", "isStart", "EventValidationErrorMessage", "validateEvents", "log", "<PERSON><PERSON><PERSON><PERSON>", "isError", "msg", "isArray", "NotArray", "StartPropertyMissing", "StartPropertyNotDate", "EndPropertyNotDate", "EndsBeforeStart"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/calendar-utils/calendar-utils.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nexport var DAYS_OF_WEEK;\n(function (DAYS_OF_WEEK) {\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"SUNDAY\"] = 0] = \"SUNDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"MONDAY\"] = 1] = \"MONDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"TUESDAY\"] = 2] = \"TUESDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"WEDNESDAY\"] = 3] = \"WEDNESDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"THURSDAY\"] = 4] = \"THURSDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"FRIDAY\"] = 5] = \"FRIDAY\";\n    DAYS_OF_WEEK[DAYS_OF_WEEK[\"SATURDAY\"] = 6] = \"SATURDAY\";\n})(DAYS_OF_WEEK || (DAYS_OF_WEEK = {}));\nvar DEFAULT_WEEKEND_DAYS = [\n    DAYS_OF_WEEK.SUNDAY,\n    DAYS_OF_WEEK.SATURDAY,\n];\nvar DAYS_IN_WEEK = 7;\nvar HOURS_IN_DAY = 24;\nvar MINUTES_IN_HOUR = 60;\nexport var SECONDS_IN_DAY = 60 * 60 * 24;\nfunction getExcludedSeconds(dateAdapter, _a) {\n    var startDate = _a.startDate, seconds = _a.seconds, excluded = _a.excluded, precision = _a.precision;\n    if (excluded.length < 1) {\n        return 0;\n    }\n    var addSeconds = dateAdapter.addSeconds, getDay = dateAdapter.getDay, addDays = dateAdapter.addDays;\n    var endDate = addSeconds(startDate, seconds - 1);\n    var dayStart = getDay(startDate);\n    var dayEnd = getDay(endDate);\n    var result = 0; // Calculated in seconds\n    var current = startDate;\n    var _loop_1 = function () {\n        var day = getDay(current);\n        if (excluded.some(function (excludedDay) { return excludedDay === day; })) {\n            result += calculateExcludedSeconds(dateAdapter, {\n                dayStart: dayStart,\n                dayEnd: dayEnd,\n                day: day,\n                precision: precision,\n                startDate: startDate,\n                endDate: endDate,\n            });\n        }\n        current = addDays(current, 1);\n    };\n    while (current < endDate) {\n        _loop_1();\n    }\n    return result;\n}\nfunction calculateExcludedSeconds(dateAdapter, _a) {\n    var precision = _a.precision, day = _a.day, dayStart = _a.dayStart, dayEnd = _a.dayEnd, startDate = _a.startDate, endDate = _a.endDate;\n    var differenceInSeconds = dateAdapter.differenceInSeconds, endOfDay = dateAdapter.endOfDay, startOfDay = dateAdapter.startOfDay;\n    if (precision === 'minutes') {\n        if (day === dayStart) {\n            return differenceInSeconds(endOfDay(startDate), startDate) + 1;\n        }\n        else if (day === dayEnd) {\n            return differenceInSeconds(endDate, startOfDay(endDate)) + 1;\n        }\n    }\n    return SECONDS_IN_DAY;\n}\nfunction getWeekViewEventSpan(dateAdapter, _a) {\n    var event = _a.event, offset = _a.offset, startOfWeekDate = _a.startOfWeekDate, excluded = _a.excluded, precision = _a.precision, totalDaysInView = _a.totalDaysInView;\n    var max = dateAdapter.max, differenceInSeconds = dateAdapter.differenceInSeconds, addDays = dateAdapter.addDays, endOfDay = dateAdapter.endOfDay, differenceInDays = dateAdapter.differenceInDays;\n    var span = SECONDS_IN_DAY;\n    var begin = max([event.start, startOfWeekDate]);\n    if (event.end) {\n        switch (precision) {\n            case 'minutes':\n                span = differenceInSeconds(event.end, begin);\n                break;\n            default:\n                span =\n                    differenceInDays(addDays(endOfDay(event.end), 1), begin) *\n                        SECONDS_IN_DAY;\n                break;\n        }\n    }\n    var offsetSeconds = offset * SECONDS_IN_DAY;\n    var totalLength = offsetSeconds + span;\n    // the best way to detect if an event is outside the week-view\n    // is to check if the total span beginning (from startOfWeekDay or event start) exceeds the total days in the view\n    var secondsInView = totalDaysInView * SECONDS_IN_DAY;\n    if (totalLength > secondsInView) {\n        span = secondsInView - offsetSeconds;\n    }\n    span -= getExcludedSeconds(dateAdapter, {\n        startDate: begin,\n        seconds: span,\n        excluded: excluded,\n        precision: precision,\n    });\n    return span / SECONDS_IN_DAY;\n}\nfunction getWeekViewEventOffset(dateAdapter, _a) {\n    var event = _a.event, startOfWeekDate = _a.startOfWeek, excluded = _a.excluded, precision = _a.precision;\n    var differenceInDays = dateAdapter.differenceInDays, startOfDay = dateAdapter.startOfDay, differenceInSeconds = dateAdapter.differenceInSeconds;\n    if (event.start < startOfWeekDate) {\n        return 0;\n    }\n    var offset = 0;\n    switch (precision) {\n        case 'days':\n            offset =\n                differenceInDays(startOfDay(event.start), startOfWeekDate) *\n                    SECONDS_IN_DAY;\n            break;\n        case 'minutes':\n            offset = differenceInSeconds(event.start, startOfWeekDate);\n            break;\n    }\n    offset -= getExcludedSeconds(dateAdapter, {\n        startDate: startOfWeekDate,\n        seconds: offset,\n        excluded: excluded,\n        precision: precision,\n    });\n    return Math.abs(offset / SECONDS_IN_DAY);\n}\nfunction isEventIsPeriod(dateAdapter, _a) {\n    var event = _a.event, periodStart = _a.periodStart, periodEnd = _a.periodEnd;\n    var isSameSecond = dateAdapter.isSameSecond;\n    var eventStart = event.start;\n    var eventEnd = event.end || event.start;\n    if (eventStart > periodStart && eventStart < periodEnd) {\n        return true;\n    }\n    if (eventEnd > periodStart && eventEnd < periodEnd) {\n        return true;\n    }\n    if (eventStart < periodStart && eventEnd > periodEnd) {\n        return true;\n    }\n    if (isSameSecond(eventStart, periodStart) ||\n        isSameSecond(eventStart, periodEnd)) {\n        return true;\n    }\n    if (isSameSecond(eventEnd, periodStart) ||\n        isSameSecond(eventEnd, periodEnd)) {\n        return true;\n    }\n    return false;\n}\nexport function getEventsInPeriod(dateAdapter, _a) {\n    var events = _a.events, periodStart = _a.periodStart, periodEnd = _a.periodEnd;\n    return events.filter(function (event) {\n        return isEventIsPeriod(dateAdapter, { event: event, periodStart: periodStart, periodEnd: periodEnd });\n    });\n}\nfunction getWeekDay(dateAdapter, _a) {\n    var date = _a.date, _b = _a.weekendDays, weekendDays = _b === void 0 ? DEFAULT_WEEKEND_DAYS : _b;\n    var startOfDay = dateAdapter.startOfDay, isSameDay = dateAdapter.isSameDay, getDay = dateAdapter.getDay;\n    var today = startOfDay(new Date());\n    var day = getDay(date);\n    return {\n        date: date,\n        day: day,\n        isPast: date < today,\n        isToday: isSameDay(date, today),\n        isFuture: date > today,\n        isWeekend: weekendDays.indexOf(day) > -1,\n    };\n}\nexport function getWeekViewHeader(dateAdapter, _a) {\n    var viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _b = _a.excluded, excluded = _b === void 0 ? [] : _b, weekendDays = _a.weekendDays, _c = _a.viewStart, viewStart = _c === void 0 ? dateAdapter.startOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _c, _d = _a.viewEnd, viewEnd = _d === void 0 ? dateAdapter.addDays(viewStart, DAYS_IN_WEEK) : _d;\n    var addDays = dateAdapter.addDays, getDay = dateAdapter.getDay;\n    var days = [];\n    var date = viewStart;\n    while (date < viewEnd) {\n        if (!excluded.some(function (e) { return getDay(date) === e; })) {\n            days.push(getWeekDay(dateAdapter, { date: date, weekendDays: weekendDays }));\n        }\n        date = addDays(date, 1);\n    }\n    return days;\n}\nexport function getDifferenceInDaysWithExclusions(dateAdapter, _a) {\n    var date1 = _a.date1, date2 = _a.date2, excluded = _a.excluded;\n    var date = date1;\n    var diff = 0;\n    while (date < date2) {\n        if (excluded.indexOf(dateAdapter.getDay(date)) === -1) {\n            diff++;\n        }\n        date = dateAdapter.addDays(date, 1);\n    }\n    return diff;\n}\nexport function getAllDayWeekEvents(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.precision, precision = _d === void 0 ? 'days' : _d, _e = _a.absolutePositionedEvents, absolutePositionedEvents = _e === void 0 ? false : _e, viewStart = _a.viewStart, viewEnd = _a.viewEnd;\n    viewStart = dateAdapter.startOfDay(viewStart);\n    viewEnd = dateAdapter.endOfDay(viewEnd);\n    var differenceInSeconds = dateAdapter.differenceInSeconds, differenceInDays = dateAdapter.differenceInDays;\n    var maxRange = getDifferenceInDaysWithExclusions(dateAdapter, {\n        date1: viewStart,\n        date2: viewEnd,\n        excluded: excluded,\n    });\n    var totalDaysInView = differenceInDays(viewEnd, viewStart) + 1;\n    var eventsMapped = events\n        .filter(function (event) { return event.allDay; })\n        .map(function (event) {\n        var offset = getWeekViewEventOffset(dateAdapter, {\n            event: event,\n            startOfWeek: viewStart,\n            excluded: excluded,\n            precision: precision,\n        });\n        var span = getWeekViewEventSpan(dateAdapter, {\n            event: event,\n            offset: offset,\n            startOfWeekDate: viewStart,\n            excluded: excluded,\n            precision: precision,\n            totalDaysInView: totalDaysInView,\n        });\n        return { event: event, offset: offset, span: span };\n    })\n        .filter(function (e) { return e.offset < maxRange; })\n        .filter(function (e) { return e.span > 0; })\n        .map(function (entry) { return ({\n        event: entry.event,\n        offset: entry.offset,\n        span: entry.span,\n        startsBeforeWeek: entry.event.start < viewStart,\n        endsAfterWeek: (entry.event.end || entry.event.start) > viewEnd,\n    }); })\n        .sort(function (itemA, itemB) {\n        var startSecondsDiff = differenceInSeconds(itemA.event.start, itemB.event.start);\n        if (startSecondsDiff === 0) {\n            return differenceInSeconds(itemB.event.end || itemB.event.start, itemA.event.end || itemA.event.start);\n        }\n        return startSecondsDiff;\n    });\n    var allDayEventRows = [];\n    var allocatedEvents = [];\n    eventsMapped.forEach(function (event, index) {\n        if (allocatedEvents.indexOf(event) === -1) {\n            allocatedEvents.push(event);\n            var rowSpan_1 = event.span + event.offset;\n            var otherRowEvents = eventsMapped\n                .slice(index + 1)\n                .filter(function (nextEvent) {\n                if (nextEvent.offset >= rowSpan_1 &&\n                    rowSpan_1 + nextEvent.span <= totalDaysInView &&\n                    allocatedEvents.indexOf(nextEvent) === -1) {\n                    var nextEventOffset = nextEvent.offset - rowSpan_1;\n                    if (!absolutePositionedEvents) {\n                        nextEvent.offset = nextEventOffset;\n                    }\n                    rowSpan_1 += nextEvent.span + nextEventOffset;\n                    allocatedEvents.push(nextEvent);\n                    return true;\n                }\n            });\n            var weekEvents = __spreadArray([event], otherRowEvents, true);\n            var id = weekEvents\n                .filter(function (weekEvent) { return weekEvent.event.id; })\n                .map(function (weekEvent) { return weekEvent.event.id; })\n                .join('-');\n            allDayEventRows.push(__assign({ row: weekEvents }, (id ? { id: id } : {})));\n        }\n    });\n    return allDayEventRows;\n}\nfunction getWeekViewHourGrid(dateAdapter, _a) {\n    var events = _a.events, viewDate = _a.viewDate, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd, weekStartsOn = _a.weekStartsOn, excluded = _a.excluded, weekendDays = _a.weekendDays, segmentHeight = _a.segmentHeight, viewStart = _a.viewStart, viewEnd = _a.viewEnd, minimumEventHeight = _a.minimumEventHeight;\n    var dayViewHourGrid = getDayViewHourGrid(dateAdapter, {\n        viewDate: viewDate,\n        hourSegments: hourSegments,\n        hourDuration: hourDuration,\n        dayStart: dayStart,\n        dayEnd: dayEnd,\n    });\n    var weekDays = getWeekViewHeader(dateAdapter, {\n        viewDate: viewDate,\n        weekStartsOn: weekStartsOn,\n        excluded: excluded,\n        weekendDays: weekendDays,\n        viewStart: viewStart,\n        viewEnd: viewEnd,\n    });\n    var setHours = dateAdapter.setHours, setMinutes = dateAdapter.setMinutes, getHours = dateAdapter.getHours, getMinutes = dateAdapter.getMinutes;\n    return weekDays.map(function (day) {\n        var dayView = getDayView(dateAdapter, {\n            events: events,\n            viewDate: day.date,\n            hourSegments: hourSegments,\n            dayStart: dayStart,\n            dayEnd: dayEnd,\n            segmentHeight: segmentHeight,\n            eventWidth: 1,\n            hourDuration: hourDuration,\n            minimumEventHeight: minimumEventHeight,\n        });\n        var hours = dayViewHourGrid.map(function (hour) {\n            var segments = hour.segments.map(function (segment) {\n                var date = setMinutes(setHours(day.date, getHours(segment.date)), getMinutes(segment.date));\n                return __assign(__assign({}, segment), { date: date });\n            });\n            return __assign(__assign({}, hour), { segments: segments });\n        });\n        function getColumnCount(allEvents, prevOverlappingEvents) {\n            var columnCount = Math.max.apply(Math, prevOverlappingEvents.map(function (iEvent) { return iEvent.left + 1; }));\n            var nextOverlappingEvents = allEvents\n                .filter(function (iEvent) { return iEvent.left >= columnCount; })\n                .filter(function (iEvent) {\n                return (getOverLappingWeekViewEvents(prevOverlappingEvents, iEvent.top, iEvent.top + iEvent.height).length > 0);\n            });\n            if (nextOverlappingEvents.length > 0) {\n                return getColumnCount(allEvents, nextOverlappingEvents);\n            }\n            else {\n                return columnCount;\n            }\n        }\n        var mappedEvents = dayView.events.map(function (event) {\n            var columnCount = getColumnCount(dayView.events, getOverLappingWeekViewEvents(dayView.events, event.top, event.top + event.height));\n            var width = 100 / columnCount;\n            return __assign(__assign({}, event), { left: event.left * width, width: width });\n        });\n        return {\n            hours: hours,\n            date: day.date,\n            events: mappedEvents.map(function (event) {\n                var overLappingEvents = getOverLappingWeekViewEvents(mappedEvents.filter(function (otherEvent) { return otherEvent.left > event.left; }), event.top, event.top + event.height);\n                if (overLappingEvents.length > 0) {\n                    return __assign(__assign({}, event), { width: Math.min.apply(Math, overLappingEvents.map(function (otherEvent) { return otherEvent.left; })) - event.left });\n                }\n                return event;\n            }),\n        };\n    });\n}\nexport function getWeekView(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.precision, precision = _d === void 0 ? 'days' : _d, _e = _a.absolutePositionedEvents, absolutePositionedEvents = _e === void 0 ? false : _e, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd, weekendDays = _a.weekendDays, segmentHeight = _a.segmentHeight, minimumEventHeight = _a.minimumEventHeight, _f = _a.viewStart, viewStart = _f === void 0 ? dateAdapter.startOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _f, _g = _a.viewEnd, viewEnd = _g === void 0 ? dateAdapter.endOfWeek(viewDate, { weekStartsOn: weekStartsOn }) : _g;\n    if (!events) {\n        events = [];\n    }\n    var startOfDay = dateAdapter.startOfDay, endOfDay = dateAdapter.endOfDay;\n    viewStart = startOfDay(viewStart);\n    viewEnd = endOfDay(viewEnd);\n    var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: events,\n        periodStart: viewStart,\n        periodEnd: viewEnd,\n    });\n    var header = getWeekViewHeader(dateAdapter, {\n        viewDate: viewDate,\n        weekStartsOn: weekStartsOn,\n        excluded: excluded,\n        weekendDays: weekendDays,\n        viewStart: viewStart,\n        viewEnd: viewEnd,\n    });\n    return {\n        allDayEventRows: getAllDayWeekEvents(dateAdapter, {\n            events: eventsInPeriod,\n            excluded: excluded,\n            precision: precision,\n            absolutePositionedEvents: absolutePositionedEvents,\n            viewStart: viewStart,\n            viewEnd: viewEnd,\n        }),\n        period: {\n            events: eventsInPeriod,\n            start: header[0].date,\n            end: endOfDay(header[header.length - 1].date),\n        },\n        hourColumns: getWeekViewHourGrid(dateAdapter, {\n            events: events,\n            viewDate: viewDate,\n            hourSegments: hourSegments,\n            hourDuration: hourDuration,\n            dayStart: dayStart,\n            dayEnd: dayEnd,\n            weekStartsOn: weekStartsOn,\n            excluded: excluded,\n            weekendDays: weekendDays,\n            segmentHeight: segmentHeight,\n            viewStart: viewStart,\n            viewEnd: viewEnd,\n            minimumEventHeight: minimumEventHeight,\n        }),\n    };\n}\nexport function getMonthView(dateAdapter, _a) {\n    var _b = _a.events, events = _b === void 0 ? [] : _b, viewDate = _a.viewDate, weekStartsOn = _a.weekStartsOn, _c = _a.excluded, excluded = _c === void 0 ? [] : _c, _d = _a.viewStart, viewStart = _d === void 0 ? dateAdapter.startOfMonth(viewDate) : _d, _e = _a.viewEnd, viewEnd = _e === void 0 ? dateAdapter.endOfMonth(viewDate) : _e, weekendDays = _a.weekendDays;\n    if (!events) {\n        events = [];\n    }\n    var startOfWeek = dateAdapter.startOfWeek, endOfWeek = dateAdapter.endOfWeek, differenceInDays = dateAdapter.differenceInDays, startOfDay = dateAdapter.startOfDay, addHours = dateAdapter.addHours, endOfDay = dateAdapter.endOfDay, isSameMonth = dateAdapter.isSameMonth, getDay = dateAdapter.getDay;\n    var start = startOfWeek(viewStart, { weekStartsOn: weekStartsOn });\n    var end = endOfWeek(viewEnd, { weekStartsOn: weekStartsOn });\n    var eventsInMonth = getEventsInPeriod(dateAdapter, {\n        events: events,\n        periodStart: start,\n        periodEnd: end,\n    });\n    var initialViewDays = [];\n    var previousDate;\n    var _loop_2 = function (i) {\n        // hacky fix for https://github.com/mattlewis92/angular-calendar/issues/173\n        var date;\n        if (previousDate) {\n            date = startOfDay(addHours(previousDate, HOURS_IN_DAY));\n            if (previousDate.getTime() === date.getTime()) {\n                // DST change, so need to add 25 hours\n                /* istanbul ignore next */\n                date = startOfDay(addHours(previousDate, HOURS_IN_DAY + 1));\n            }\n            previousDate = date;\n        }\n        else {\n            date = previousDate = start;\n        }\n        if (!excluded.some(function (e) { return getDay(date) === e; })) {\n            var day = getWeekDay(dateAdapter, {\n                date: date,\n                weekendDays: weekendDays,\n            });\n            var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n                events: eventsInMonth,\n                periodStart: startOfDay(date),\n                periodEnd: endOfDay(date),\n            });\n            day.inMonth = isSameMonth(date, viewDate);\n            day.events = eventsInPeriod;\n            day.badgeTotal = eventsInPeriod.length;\n            initialViewDays.push(day);\n        }\n    };\n    for (var i = 0; i < differenceInDays(end, start) + 1; i++) {\n        _loop_2(i);\n    }\n    var days = [];\n    var totalDaysVisibleInWeek = DAYS_IN_WEEK - excluded.length;\n    if (totalDaysVisibleInWeek < DAYS_IN_WEEK) {\n        for (var i = 0; i < initialViewDays.length; i += totalDaysVisibleInWeek) {\n            var row = initialViewDays.slice(i, i + totalDaysVisibleInWeek);\n            var isRowInMonth = row.some(function (day) { return viewStart <= day.date && day.date < viewEnd; });\n            if (isRowInMonth) {\n                days = __spreadArray(__spreadArray([], days, true), row, true);\n            }\n        }\n    }\n    else {\n        days = initialViewDays;\n    }\n    var rows = Math.floor(days.length / totalDaysVisibleInWeek);\n    var rowOffsets = [];\n    for (var i = 0; i < rows; i++) {\n        rowOffsets.push(i * totalDaysVisibleInWeek);\n    }\n    return {\n        rowOffsets: rowOffsets,\n        totalDaysVisibleInWeek: totalDaysVisibleInWeek,\n        days: days,\n        period: {\n            start: days[0].date,\n            end: endOfDay(days[days.length - 1].date),\n            events: eventsInMonth,\n        },\n    };\n}\nfunction getOverLappingWeekViewEvents(events, top, bottom) {\n    return events.filter(function (previousEvent) {\n        var previousEventTop = previousEvent.top;\n        var previousEventBottom = previousEvent.top + previousEvent.height;\n        if (top < previousEventBottom && previousEventBottom < bottom) {\n            return true;\n        }\n        else if (top < previousEventTop && previousEventTop < bottom) {\n            return true;\n        }\n        else if (previousEventTop <= top && bottom <= previousEventBottom) {\n            return true;\n        }\n        return false;\n    });\n}\nfunction getDayView(dateAdapter, _a) {\n    var events = _a.events, viewDate = _a.viewDate, hourSegments = _a.hourSegments, dayStart = _a.dayStart, dayEnd = _a.dayEnd, eventWidth = _a.eventWidth, segmentHeight = _a.segmentHeight, hourDuration = _a.hourDuration, minimumEventHeight = _a.minimumEventHeight;\n    var setMinutes = dateAdapter.setMinutes, setHours = dateAdapter.setHours, startOfDay = dateAdapter.startOfDay, startOfMinute = dateAdapter.startOfMinute, endOfDay = dateAdapter.endOfDay, differenceInMinutes = dateAdapter.differenceInMinutes;\n    var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n    var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n    endOfView.setSeconds(59, 999);\n    var previousDayEvents = [];\n    var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: events.filter(function (event) { return !event.allDay; }),\n        periodStart: startOfView,\n        periodEnd: endOfView,\n    });\n    var dayViewEvents = eventsInPeriod\n        .sort(function (eventA, eventB) {\n        return eventA.start.valueOf() - eventB.start.valueOf();\n    })\n        .map(function (event) {\n        var eventStart = event.start;\n        var eventEnd = event.end || eventStart;\n        var startsBeforeDay = eventStart < startOfView;\n        var endsAfterDay = eventEnd > endOfView;\n        var hourHeightModifier = (hourSegments * segmentHeight) / (hourDuration || MINUTES_IN_HOUR);\n        var top = 0;\n        if (eventStart > startOfView) {\n            // adjust the difference in minutes if the user's offset is different between the start of the day and the event (e.g. when going to or from DST)\n            var eventOffset = dateAdapter.getTimezoneOffset(eventStart);\n            var startOffset = dateAdapter.getTimezoneOffset(startOfView);\n            var diff = startOffset - eventOffset;\n            top += differenceInMinutes(eventStart, startOfView) + diff;\n        }\n        top *= hourHeightModifier;\n        top = Math.floor(top);\n        var startDate = startsBeforeDay ? startOfView : eventStart;\n        var endDate = endsAfterDay ? endOfView : eventEnd;\n        var timezoneOffset = dateAdapter.getTimezoneOffset(startDate) -\n            dateAdapter.getTimezoneOffset(endDate);\n        var height = differenceInMinutes(endDate, startDate) + timezoneOffset;\n        if (!event.end) {\n            height = segmentHeight;\n        }\n        else {\n            height *= hourHeightModifier;\n        }\n        if (minimumEventHeight && height < minimumEventHeight) {\n            height = minimumEventHeight;\n        }\n        height = Math.floor(height);\n        var bottom = top + height;\n        var overlappingPreviousEvents = getOverLappingWeekViewEvents(previousDayEvents, top, bottom);\n        var left = 0;\n        while (overlappingPreviousEvents.some(function (previousEvent) { return previousEvent.left === left; })) {\n            left += eventWidth;\n        }\n        var dayEvent = {\n            event: event,\n            height: height,\n            width: eventWidth,\n            top: top,\n            left: left,\n            startsBeforeDay: startsBeforeDay,\n            endsAfterDay: endsAfterDay,\n        };\n        previousDayEvents.push(dayEvent);\n        return dayEvent;\n    });\n    var width = Math.max.apply(Math, dayViewEvents.map(function (event) { return event.left + event.width; }));\n    var allDayEvents = getEventsInPeriod(dateAdapter, {\n        events: events.filter(function (event) { return event.allDay; }),\n        periodStart: startOfDay(startOfView),\n        periodEnd: endOfDay(endOfView),\n    });\n    return {\n        events: dayViewEvents,\n        width: width,\n        allDayEvents: allDayEvents,\n        period: {\n            events: eventsInPeriod,\n            start: startOfView,\n            end: endOfView,\n        },\n    };\n}\nfunction sanitiseHours(hours) {\n    return Math.max(Math.min(23, hours), 0);\n}\nfunction sanitiseMinutes(minutes) {\n    return Math.max(Math.min(59, minutes), 0);\n}\nfunction getDayViewHourGrid(dateAdapter, _a) {\n    var viewDate = _a.viewDate, hourSegments = _a.hourSegments, hourDuration = _a.hourDuration, dayStart = _a.dayStart, dayEnd = _a.dayEnd;\n    var setMinutes = dateAdapter.setMinutes, setHours = dateAdapter.setHours, startOfDay = dateAdapter.startOfDay, startOfMinute = dateAdapter.startOfMinute, endOfDay = dateAdapter.endOfDay, addMinutes = dateAdapter.addMinutes, addDays = dateAdapter.addDays;\n    var hours = [];\n    var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n    var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n    var segmentDuration = (hourDuration || MINUTES_IN_HOUR) / hourSegments;\n    var startOfViewDay = startOfDay(viewDate);\n    var endOfViewDay = endOfDay(viewDate);\n    var dateAdjustment = function (d) { return d; };\n    // this means that we change from or to DST on this day and that's going to cause problems so we bump the date\n    if (dateAdapter.getTimezoneOffset(startOfViewDay) !==\n        dateAdapter.getTimezoneOffset(endOfViewDay)) {\n        startOfViewDay = addDays(startOfViewDay, 1);\n        startOfView = addDays(startOfView, 1);\n        endOfView = addDays(endOfView, 1);\n        dateAdjustment = function (d) { return addDays(d, -1); };\n    }\n    var dayDuration = hourDuration\n        ? (HOURS_IN_DAY * 60) / hourDuration\n        : MINUTES_IN_HOUR;\n    for (var i = 0; i < dayDuration; i++) {\n        var segments = [];\n        for (var j = 0; j < hourSegments; j++) {\n            var date = addMinutes(addMinutes(startOfView, i * (hourDuration || MINUTES_IN_HOUR)), j * segmentDuration);\n            if (date >= startOfView && date < endOfView) {\n                segments.push({\n                    date: dateAdjustment(date),\n                    displayDate: date,\n                    isStart: j === 0,\n                });\n            }\n        }\n        if (segments.length > 0) {\n            hours.push({ segments: segments });\n        }\n    }\n    return hours;\n}\nexport var EventValidationErrorMessage;\n(function (EventValidationErrorMessage) {\n    EventValidationErrorMessage[\"NotArray\"] = \"Events must be an array\";\n    EventValidationErrorMessage[\"StartPropertyMissing\"] = \"Event is missing the `start` property\";\n    EventValidationErrorMessage[\"StartPropertyNotDate\"] = \"Event `start` property should be a javascript date object. Do `new Date(event.start)` to fix it.\";\n    EventValidationErrorMessage[\"EndPropertyNotDate\"] = \"Event `end` property should be a javascript date object. Do `new Date(event.end)` to fix it.\";\n    EventValidationErrorMessage[\"EndsBeforeStart\"] = \"Event `start` property occurs after the `end`\";\n})(EventValidationErrorMessage || (EventValidationErrorMessage = {}));\nexport function validateEvents(events, log) {\n    var isValid = true;\n    function isError(msg, event) {\n        log(msg, event);\n        isValid = false;\n    }\n    if (!Array.isArray(events)) {\n        log(EventValidationErrorMessage.NotArray, events);\n        return false;\n    }\n    events.forEach(function (event) {\n        if (!event.start) {\n            isError(EventValidationErrorMessage.StartPropertyMissing, event);\n        }\n        else if (!(event.start instanceof Date)) {\n            isError(EventValidationErrorMessage.StartPropertyNotDate, event);\n        }\n        if (event.end) {\n            if (!(event.end instanceof Date)) {\n                isError(EventValidationErrorMessage.EndPropertyNotDate, event);\n            }\n            if (event.start > event.end) {\n                isError(EventValidationErrorMessage.EndsBeforeStart, event);\n            }\n        }\n    });\n    return isValid;\n}\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAC3DN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IACnB;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AACD,IAAIO,aAAa,GAAI,IAAI,IAAI,IAAI,CAACA,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC1E,IAAIA,IAAI,IAAIV,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEa,CAAC,GAAGF,IAAI,CAACR,MAAM,EAAEW,EAAE,EAAEd,CAAC,GAAGa,CAAC,EAAEb,CAAC,EAAE,EAAE;IACjF,IAAIc,EAAE,IAAI,EAAEd,CAAC,IAAIW,IAAI,CAAC,EAAE;MACpB,IAAI,CAACG,EAAE,EAAEA,EAAE,GAAGC,KAAK,CAACV,SAAS,CAACW,KAAK,CAACT,IAAI,CAACI,IAAI,EAAE,CAAC,EAAEX,CAAC,CAAC;MACpDc,EAAE,CAACd,CAAC,CAAC,GAAGW,IAAI,CAACX,CAAC,CAAC;IACnB;EACJ;EACA,OAAOU,EAAE,CAACO,MAAM,CAACH,EAAE,IAAIC,KAAK,CAACV,SAAS,CAACW,KAAK,CAACT,IAAI,CAACI,IAAI,CAAC,CAAC;AAC5D,CAAC;AACD,OAAO,IAAIO,YAAY;AACvB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnDA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnDA,YAAY,CAACA,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrDA,YAAY,CAACA,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzDA,YAAY,CAACA,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACvDA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnDA,YAAY,CAACA,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AAC3D,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,IAAIC,oBAAoB,GAAG,CACvBD,YAAY,CAACE,MAAM,EACnBF,YAAY,CAACG,QAAQ,CACxB;AACD,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,YAAY,GAAG,EAAE;AACrB,IAAIC,eAAe,GAAG,EAAE;AACxB,OAAO,IAAIC,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC,SAASC,kBAAkBA,CAACC,WAAW,EAAEC,EAAE,EAAE;EACzC,IAAIC,SAAS,GAAGD,EAAE,CAACC,SAAS;IAAEC,OAAO,GAAGF,EAAE,CAACE,OAAO;IAAEC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IAAEC,SAAS,GAAGJ,EAAE,CAACI,SAAS;EACpG,IAAID,QAAQ,CAAC5B,MAAM,GAAG,CAAC,EAAE;IACrB,OAAO,CAAC;EACZ;EACA,IAAI8B,UAAU,GAAGN,WAAW,CAACM,UAAU;IAAEC,MAAM,GAAGP,WAAW,CAACO,MAAM;IAAEC,OAAO,GAAGR,WAAW,CAACQ,OAAO;EACnG,IAAIC,OAAO,GAAGH,UAAU,CAACJ,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC;EAChD,IAAIO,QAAQ,GAAGH,MAAM,CAACL,SAAS,CAAC;EAChC,IAAIS,MAAM,GAAGJ,MAAM,CAACE,OAAO,CAAC;EAC5B,IAAIG,MAAM,GAAG,CAAC,CAAC,CAAC;EAChB,IAAIC,OAAO,GAAGX,SAAS;EACvB,IAAIY,OAAO,GAAG,SAAAA,CAAA,EAAY;IACtB,IAAIC,GAAG,GAAGR,MAAM,CAACM,OAAO,CAAC;IACzB,IAAIT,QAAQ,CAACY,IAAI,CAAC,UAAUC,WAAW,EAAE;MAAE,OAAOA,WAAW,KAAKF,GAAG;IAAE,CAAC,CAAC,EAAE;MACvEH,MAAM,IAAIM,wBAAwB,CAAClB,WAAW,EAAE;QAC5CU,QAAQ,EAAEA,QAAQ;QAClBC,MAAM,EAAEA,MAAM;QACdI,GAAG,EAAEA,GAAG;QACRV,SAAS,EAAEA,SAAS;QACpBH,SAAS,EAAEA,SAAS;QACpBO,OAAO,EAAEA;MACb,CAAC,CAAC;IACN;IACAI,OAAO,GAAGL,OAAO,CAACK,OAAO,EAAE,CAAC,CAAC;EACjC,CAAC;EACD,OAAOA,OAAO,GAAGJ,OAAO,EAAE;IACtBK,OAAO,CAAC,CAAC;EACb;EACA,OAAOF,MAAM;AACjB;AACA,SAASM,wBAAwBA,CAAClB,WAAW,EAAEC,EAAE,EAAE;EAC/C,IAAII,SAAS,GAAGJ,EAAE,CAACI,SAAS;IAAEU,GAAG,GAAGd,EAAE,CAACc,GAAG;IAAEL,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IAAEC,MAAM,GAAGV,EAAE,CAACU,MAAM;IAAET,SAAS,GAAGD,EAAE,CAACC,SAAS;IAAEO,OAAO,GAAGR,EAAE,CAACQ,OAAO;EACtI,IAAIU,mBAAmB,GAAGnB,WAAW,CAACmB,mBAAmB;IAAEC,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;IAAEC,UAAU,GAAGrB,WAAW,CAACqB,UAAU;EAC/H,IAAIhB,SAAS,KAAK,SAAS,EAAE;IACzB,IAAIU,GAAG,KAAKL,QAAQ,EAAE;MAClB,OAAOS,mBAAmB,CAACC,QAAQ,CAAClB,SAAS,CAAC,EAAEA,SAAS,CAAC,GAAG,CAAC;IAClE,CAAC,MACI,IAAIa,GAAG,KAAKJ,MAAM,EAAE;MACrB,OAAOQ,mBAAmB,CAACV,OAAO,EAAEY,UAAU,CAACZ,OAAO,CAAC,CAAC,GAAG,CAAC;IAChE;EACJ;EACA,OAAOX,cAAc;AACzB;AACA,SAASwB,oBAAoBA,CAACtB,WAAW,EAAEC,EAAE,EAAE;EAC3C,IAAIsB,KAAK,GAAGtB,EAAE,CAACsB,KAAK;IAAEC,MAAM,GAAGvB,EAAE,CAACuB,MAAM;IAAEC,eAAe,GAAGxB,EAAE,CAACwB,eAAe;IAAErB,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IAAEC,SAAS,GAAGJ,EAAE,CAACI,SAAS;IAAEqB,eAAe,GAAGzB,EAAE,CAACyB,eAAe;EACtK,IAAIC,GAAG,GAAG3B,WAAW,CAAC2B,GAAG;IAAER,mBAAmB,GAAGnB,WAAW,CAACmB,mBAAmB;IAAEX,OAAO,GAAGR,WAAW,CAACQ,OAAO;IAAEY,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;IAAEQ,gBAAgB,GAAG5B,WAAW,CAAC4B,gBAAgB;EACjM,IAAIC,IAAI,GAAG/B,cAAc;EACzB,IAAIgC,KAAK,GAAGH,GAAG,CAAC,CAACJ,KAAK,CAACQ,KAAK,EAAEN,eAAe,CAAC,CAAC;EAC/C,IAAIF,KAAK,CAACS,GAAG,EAAE;IACX,QAAQ3B,SAAS;MACb,KAAK,SAAS;QACVwB,IAAI,GAAGV,mBAAmB,CAACI,KAAK,CAACS,GAAG,EAAEF,KAAK,CAAC;QAC5C;MACJ;QACID,IAAI,GACAD,gBAAgB,CAACpB,OAAO,CAACY,QAAQ,CAACG,KAAK,CAACS,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEF,KAAK,CAAC,GACpDhC,cAAc;QACtB;IACR;EACJ;EACA,IAAImC,aAAa,GAAGT,MAAM,GAAG1B,cAAc;EAC3C,IAAIoC,WAAW,GAAGD,aAAa,GAAGJ,IAAI;EACtC;EACA;EACA,IAAIM,aAAa,GAAGT,eAAe,GAAG5B,cAAc;EACpD,IAAIoC,WAAW,GAAGC,aAAa,EAAE;IAC7BN,IAAI,GAAGM,aAAa,GAAGF,aAAa;EACxC;EACAJ,IAAI,IAAI9B,kBAAkB,CAACC,WAAW,EAAE;IACpCE,SAAS,EAAE4B,KAAK;IAChB3B,OAAO,EAAE0B,IAAI;IACbzB,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA;EACf,CAAC,CAAC;EACF,OAAOwB,IAAI,GAAG/B,cAAc;AAChC;AACA,SAASsC,sBAAsBA,CAACpC,WAAW,EAAEC,EAAE,EAAE;EAC7C,IAAIsB,KAAK,GAAGtB,EAAE,CAACsB,KAAK;IAAEE,eAAe,GAAGxB,EAAE,CAACoC,WAAW;IAAEjC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IAAEC,SAAS,GAAGJ,EAAE,CAACI,SAAS;EACxG,IAAIuB,gBAAgB,GAAG5B,WAAW,CAAC4B,gBAAgB;IAAEP,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAEF,mBAAmB,GAAGnB,WAAW,CAACmB,mBAAmB;EAC/I,IAAII,KAAK,CAACQ,KAAK,GAAGN,eAAe,EAAE;IAC/B,OAAO,CAAC;EACZ;EACA,IAAID,MAAM,GAAG,CAAC;EACd,QAAQnB,SAAS;IACb,KAAK,MAAM;MACPmB,MAAM,GACFI,gBAAgB,CAACP,UAAU,CAACE,KAAK,CAACQ,KAAK,CAAC,EAAEN,eAAe,CAAC,GACtD3B,cAAc;MACtB;IACJ,KAAK,SAAS;MACV0B,MAAM,GAAGL,mBAAmB,CAACI,KAAK,CAACQ,KAAK,EAAEN,eAAe,CAAC;MAC1D;EACR;EACAD,MAAM,IAAIzB,kBAAkB,CAACC,WAAW,EAAE;IACtCE,SAAS,EAAEuB,eAAe;IAC1BtB,OAAO,EAAEqB,MAAM;IACfpB,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA;EACf,CAAC,CAAC;EACF,OAAOiC,IAAI,CAACC,GAAG,CAACf,MAAM,GAAG1B,cAAc,CAAC;AAC5C;AACA,SAAS0C,eAAeA,CAACxC,WAAW,EAAEC,EAAE,EAAE;EACtC,IAAIsB,KAAK,GAAGtB,EAAE,CAACsB,KAAK;IAAEkB,WAAW,GAAGxC,EAAE,CAACwC,WAAW;IAAEC,SAAS,GAAGzC,EAAE,CAACyC,SAAS;EAC5E,IAAIC,YAAY,GAAG3C,WAAW,CAAC2C,YAAY;EAC3C,IAAIC,UAAU,GAAGrB,KAAK,CAACQ,KAAK;EAC5B,IAAIc,QAAQ,GAAGtB,KAAK,CAACS,GAAG,IAAIT,KAAK,CAACQ,KAAK;EACvC,IAAIa,UAAU,GAAGH,WAAW,IAAIG,UAAU,GAAGF,SAAS,EAAE;IACpD,OAAO,IAAI;EACf;EACA,IAAIG,QAAQ,GAAGJ,WAAW,IAAII,QAAQ,GAAGH,SAAS,EAAE;IAChD,OAAO,IAAI;EACf;EACA,IAAIE,UAAU,GAAGH,WAAW,IAAII,QAAQ,GAAGH,SAAS,EAAE;IAClD,OAAO,IAAI;EACf;EACA,IAAIC,YAAY,CAACC,UAAU,EAAEH,WAAW,CAAC,IACrCE,YAAY,CAACC,UAAU,EAAEF,SAAS,CAAC,EAAE;IACrC,OAAO,IAAI;EACf;EACA,IAAIC,YAAY,CAACE,QAAQ,EAAEJ,WAAW,CAAC,IACnCE,YAAY,CAACE,QAAQ,EAAEH,SAAS,CAAC,EAAE;IACnC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASI,iBAAiBA,CAAC9C,WAAW,EAAEC,EAAE,EAAE;EAC/C,IAAI8C,MAAM,GAAG9C,EAAE,CAAC8C,MAAM;IAAEN,WAAW,GAAGxC,EAAE,CAACwC,WAAW;IAAEC,SAAS,GAAGzC,EAAE,CAACyC,SAAS;EAC9E,OAAOK,MAAM,CAACC,MAAM,CAAC,UAAUzB,KAAK,EAAE;IAClC,OAAOiB,eAAe,CAACxC,WAAW,EAAE;MAAEuB,KAAK,EAAEA,KAAK;MAAEkB,WAAW,EAAEA,WAAW;MAAEC,SAAS,EAAEA;IAAU,CAAC,CAAC;EACzG,CAAC,CAAC;AACN;AACA,SAASO,UAAUA,CAACjD,WAAW,EAAEC,EAAE,EAAE;EACjC,IAAIiD,IAAI,GAAGjD,EAAE,CAACiD,IAAI;IAAEC,EAAE,GAAGlD,EAAE,CAACmD,WAAW;IAAEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG3D,oBAAoB,GAAG2D,EAAE;EAChG,IAAI9B,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAEgC,SAAS,GAAGrD,WAAW,CAACqD,SAAS;IAAE9C,MAAM,GAAGP,WAAW,CAACO,MAAM;EACvG,IAAI+C,KAAK,GAAGjC,UAAU,CAAC,IAAIkC,IAAI,CAAC,CAAC,CAAC;EAClC,IAAIxC,GAAG,GAAGR,MAAM,CAAC2C,IAAI,CAAC;EACtB,OAAO;IACHA,IAAI,EAAEA,IAAI;IACVnC,GAAG,EAAEA,GAAG;IACRyC,MAAM,EAAEN,IAAI,GAAGI,KAAK;IACpBG,OAAO,EAAEJ,SAAS,CAACH,IAAI,EAAEI,KAAK,CAAC;IAC/BI,QAAQ,EAAER,IAAI,GAAGI,KAAK;IACtBK,SAAS,EAAEP,WAAW,CAACQ,OAAO,CAAC7C,GAAG,CAAC,GAAG,CAAC;EAC3C,CAAC;AACL;AACA,OAAO,SAAS8C,iBAAiBA,CAAC7D,WAAW,EAAEC,EAAE,EAAE;EAC/C,IAAI6D,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEC,YAAY,GAAG9D,EAAE,CAAC8D,YAAY;IAAEZ,EAAE,GAAGlD,EAAE,CAACG,QAAQ;IAAEA,QAAQ,GAAG+C,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEC,WAAW,GAAGnD,EAAE,CAACmD,WAAW;IAAEY,EAAE,GAAG/D,EAAE,CAACgE,SAAS;IAAEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGhE,WAAW,CAACqC,WAAW,CAACyB,QAAQ,EAAE;MAAEC,YAAY,EAAEA;IAAa,CAAC,CAAC,GAAGC,EAAE;IAAEE,EAAE,GAAGjE,EAAE,CAACkE,OAAO;IAAEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGlE,WAAW,CAACQ,OAAO,CAACyD,SAAS,EAAEtE,YAAY,CAAC,GAAGuE,EAAE;EACnW,IAAI1D,OAAO,GAAGR,WAAW,CAACQ,OAAO;IAAED,MAAM,GAAGP,WAAW,CAACO,MAAM;EAC9D,IAAI6D,IAAI,GAAG,EAAE;EACb,IAAIlB,IAAI,GAAGe,SAAS;EACpB,OAAOf,IAAI,GAAGiB,OAAO,EAAE;IACnB,IAAI,CAAC/D,QAAQ,CAACY,IAAI,CAAC,UAAUqD,CAAC,EAAE;MAAE,OAAO9D,MAAM,CAAC2C,IAAI,CAAC,KAAKmB,CAAC;IAAE,CAAC,CAAC,EAAE;MAC7DD,IAAI,CAACE,IAAI,CAACrB,UAAU,CAACjD,WAAW,EAAE;QAAEkD,IAAI,EAAEA,IAAI;QAAEE,WAAW,EAAEA;MAAY,CAAC,CAAC,CAAC;IAChF;IACAF,IAAI,GAAG1C,OAAO,CAAC0C,IAAI,EAAE,CAAC,CAAC;EAC3B;EACA,OAAOkB,IAAI;AACf;AACA,OAAO,SAASG,iCAAiCA,CAACvE,WAAW,EAAEC,EAAE,EAAE;EAC/D,IAAIuE,KAAK,GAAGvE,EAAE,CAACuE,KAAK;IAAEC,KAAK,GAAGxE,EAAE,CAACwE,KAAK;IAAErE,QAAQ,GAAGH,EAAE,CAACG,QAAQ;EAC9D,IAAI8C,IAAI,GAAGsB,KAAK;EAChB,IAAIE,IAAI,GAAG,CAAC;EACZ,OAAOxB,IAAI,GAAGuB,KAAK,EAAE;IACjB,IAAIrE,QAAQ,CAACwD,OAAO,CAAC5D,WAAW,CAACO,MAAM,CAAC2C,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACnDwB,IAAI,EAAE;IACV;IACAxB,IAAI,GAAGlD,WAAW,CAACQ,OAAO,CAAC0C,IAAI,EAAE,CAAC,CAAC;EACvC;EACA,OAAOwB,IAAI;AACf;AACA,OAAO,SAASC,mBAAmBA,CAAC3E,WAAW,EAAEC,EAAE,EAAE;EACjD,IAAIkD,EAAE,GAAGlD,EAAE,CAAC8C,MAAM;IAAEA,MAAM,GAAGI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEa,EAAE,GAAG/D,EAAE,CAACG,QAAQ;IAAEA,QAAQ,GAAG4D,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEE,EAAE,GAAGjE,EAAE,CAACI,SAAS;IAAEA,SAAS,GAAG6D,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,EAAE;IAAEU,EAAE,GAAG3E,EAAE,CAAC4E,wBAAwB;IAAEA,wBAAwB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAAEX,SAAS,GAAGhE,EAAE,CAACgE,SAAS;IAAEE,OAAO,GAAGlE,EAAE,CAACkE,OAAO;EAC/SF,SAAS,GAAGjE,WAAW,CAACqB,UAAU,CAAC4C,SAAS,CAAC;EAC7CE,OAAO,GAAGnE,WAAW,CAACoB,QAAQ,CAAC+C,OAAO,CAAC;EACvC,IAAIhD,mBAAmB,GAAGnB,WAAW,CAACmB,mBAAmB;IAAES,gBAAgB,GAAG5B,WAAW,CAAC4B,gBAAgB;EAC1G,IAAIkD,QAAQ,GAAGP,iCAAiC,CAACvE,WAAW,EAAE;IAC1DwE,KAAK,EAAEP,SAAS;IAChBQ,KAAK,EAAEN,OAAO;IACd/D,QAAQ,EAAEA;EACd,CAAC,CAAC;EACF,IAAIsB,eAAe,GAAGE,gBAAgB,CAACuC,OAAO,EAAEF,SAAS,CAAC,GAAG,CAAC;EAC9D,IAAIc,YAAY,GAAGhC,MAAM,CACpBC,MAAM,CAAC,UAAUzB,KAAK,EAAE;IAAE,OAAOA,KAAK,CAACyD,MAAM;EAAE,CAAC,CAAC,CACjDC,GAAG,CAAC,UAAU1D,KAAK,EAAE;IACtB,IAAIC,MAAM,GAAGY,sBAAsB,CAACpC,WAAW,EAAE;MAC7CuB,KAAK,EAAEA,KAAK;MACZc,WAAW,EAAE4B,SAAS;MACtB7D,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA;IACf,CAAC,CAAC;IACF,IAAIwB,IAAI,GAAGP,oBAAoB,CAACtB,WAAW,EAAE;MACzCuB,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdC,eAAe,EAAEwC,SAAS;MAC1B7D,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA,SAAS;MACpBqB,eAAe,EAAEA;IACrB,CAAC,CAAC;IACF,OAAO;MAAEH,KAAK,EAAEA,KAAK;MAAEC,MAAM,EAAEA,MAAM;MAAEK,IAAI,EAAEA;IAAK,CAAC;EACvD,CAAC,CAAC,CACGmB,MAAM,CAAC,UAAUqB,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC7C,MAAM,GAAGsD,QAAQ;EAAE,CAAC,CAAC,CACpD9B,MAAM,CAAC,UAAUqB,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACxC,IAAI,GAAG,CAAC;EAAE,CAAC,CAAC,CAC3CoD,GAAG,CAAC,UAAUC,KAAK,EAAE;IAAE,OAAQ;MAChC3D,KAAK,EAAE2D,KAAK,CAAC3D,KAAK;MAClBC,MAAM,EAAE0D,KAAK,CAAC1D,MAAM;MACpBK,IAAI,EAAEqD,KAAK,CAACrD,IAAI;MAChBsD,gBAAgB,EAAED,KAAK,CAAC3D,KAAK,CAACQ,KAAK,GAAGkC,SAAS;MAC/CmB,aAAa,EAAE,CAACF,KAAK,CAAC3D,KAAK,CAACS,GAAG,IAAIkD,KAAK,CAAC3D,KAAK,CAACQ,KAAK,IAAIoC;IAC5D,CAAC;EAAG,CAAC,CAAC,CACDkB,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC9B,IAAIC,gBAAgB,GAAGrE,mBAAmB,CAACmE,KAAK,CAAC/D,KAAK,CAACQ,KAAK,EAAEwD,KAAK,CAAChE,KAAK,CAACQ,KAAK,CAAC;IAChF,IAAIyD,gBAAgB,KAAK,CAAC,EAAE;MACxB,OAAOrE,mBAAmB,CAACoE,KAAK,CAAChE,KAAK,CAACS,GAAG,IAAIuD,KAAK,CAAChE,KAAK,CAACQ,KAAK,EAAEuD,KAAK,CAAC/D,KAAK,CAACS,GAAG,IAAIsD,KAAK,CAAC/D,KAAK,CAACQ,KAAK,CAAC;IAC1G;IACA,OAAOyD,gBAAgB;EAC3B,CAAC,CAAC;EACF,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,eAAe,GAAG,EAAE;EACxBX,YAAY,CAACY,OAAO,CAAC,UAAUpE,KAAK,EAAEqE,KAAK,EAAE;IACzC,IAAIF,eAAe,CAAC9B,OAAO,CAACrC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MACvCmE,eAAe,CAACpB,IAAI,CAAC/C,KAAK,CAAC;MAC3B,IAAIsE,SAAS,GAAGtE,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACC,MAAM;MACzC,IAAIsE,cAAc,GAAGf,YAAY,CAC5B1F,KAAK,CAACuG,KAAK,GAAG,CAAC,CAAC,CAChB5C,MAAM,CAAC,UAAU+C,SAAS,EAAE;QAC7B,IAAIA,SAAS,CAACvE,MAAM,IAAIqE,SAAS,IAC7BA,SAAS,GAAGE,SAAS,CAAClE,IAAI,IAAIH,eAAe,IAC7CgE,eAAe,CAAC9B,OAAO,CAACmC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;UAC3C,IAAIC,eAAe,GAAGD,SAAS,CAACvE,MAAM,GAAGqE,SAAS;UAClD,IAAI,CAAChB,wBAAwB,EAAE;YAC3BkB,SAAS,CAACvE,MAAM,GAAGwE,eAAe;UACtC;UACAH,SAAS,IAAIE,SAAS,CAAClE,IAAI,GAAGmE,eAAe;UAC7CN,eAAe,CAACpB,IAAI,CAACyB,SAAS,CAAC;UAC/B,OAAO,IAAI;QACf;MACJ,CAAC,CAAC;MACF,IAAIE,UAAU,GAAGnH,aAAa,CAAC,CAACyC,KAAK,CAAC,EAAEuE,cAAc,EAAE,IAAI,CAAC;MAC7D,IAAII,EAAE,GAAGD,UAAU,CACdjD,MAAM,CAAC,UAAUmD,SAAS,EAAE;QAAE,OAAOA,SAAS,CAAC5E,KAAK,CAAC2E,EAAE;MAAE,CAAC,CAAC,CAC3DjB,GAAG,CAAC,UAAUkB,SAAS,EAAE;QAAE,OAAOA,SAAS,CAAC5E,KAAK,CAAC2E,EAAE;MAAE,CAAC,CAAC,CACxDE,IAAI,CAAC,GAAG,CAAC;MACdX,eAAe,CAACnB,IAAI,CAACtG,QAAQ,CAAC;QAAEqI,GAAG,EAAEJ;MAAW,CAAC,EAAGC,EAAE,GAAG;QAAEA,EAAE,EAAEA;MAAG,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;IAC/E;EACJ,CAAC,CAAC;EACF,OAAOT,eAAe;AAC1B;AACA,SAASa,mBAAmBA,CAACtG,WAAW,EAAEC,EAAE,EAAE;EAC1C,IAAI8C,MAAM,GAAG9C,EAAE,CAAC8C,MAAM;IAAEe,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEyC,YAAY,GAAGtG,EAAE,CAACsG,YAAY;IAAEC,YAAY,GAAGvG,EAAE,CAACuG,YAAY;IAAE9F,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IAAEC,MAAM,GAAGV,EAAE,CAACU,MAAM;IAAEoD,YAAY,GAAG9D,EAAE,CAAC8D,YAAY;IAAE3D,QAAQ,GAAGH,EAAE,CAACG,QAAQ;IAAEgD,WAAW,GAAGnD,EAAE,CAACmD,WAAW;IAAEqD,aAAa,GAAGxG,EAAE,CAACwG,aAAa;IAAExC,SAAS,GAAGhE,EAAE,CAACgE,SAAS;IAAEE,OAAO,GAAGlE,EAAE,CAACkE,OAAO;IAAEuC,kBAAkB,GAAGzG,EAAE,CAACyG,kBAAkB;EAC9W,IAAIC,eAAe,GAAGC,kBAAkB,CAAC5G,WAAW,EAAE;IAClD8D,QAAQ,EAAEA,QAAQ;IAClByC,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1B9F,QAAQ,EAAEA,QAAQ;IAClBC,MAAM,EAAEA;EACZ,CAAC,CAAC;EACF,IAAIkG,QAAQ,GAAGhD,iBAAiB,CAAC7D,WAAW,EAAE;IAC1C8D,QAAQ,EAAEA,QAAQ;IAClBC,YAAY,EAAEA,YAAY;IAC1B3D,QAAQ,EAAEA,QAAQ;IAClBgD,WAAW,EAAEA,WAAW;IACxBa,SAAS,EAAEA,SAAS;IACpBE,OAAO,EAAEA;EACb,CAAC,CAAC;EACF,IAAI2C,QAAQ,GAAG9G,WAAW,CAAC8G,QAAQ;IAAEC,UAAU,GAAG/G,WAAW,CAAC+G,UAAU;IAAEC,QAAQ,GAAGhH,WAAW,CAACgH,QAAQ;IAAEC,UAAU,GAAGjH,WAAW,CAACiH,UAAU;EAC9I,OAAOJ,QAAQ,CAAC5B,GAAG,CAAC,UAAUlE,GAAG,EAAE;IAC/B,IAAImG,OAAO,GAAGC,UAAU,CAACnH,WAAW,EAAE;MAClC+C,MAAM,EAAEA,MAAM;MACde,QAAQ,EAAE/C,GAAG,CAACmC,IAAI;MAClBqD,YAAY,EAAEA,YAAY;MAC1B7F,QAAQ,EAAEA,QAAQ;MAClBC,MAAM,EAAEA,MAAM;MACd8F,aAAa,EAAEA,aAAa;MAC5BW,UAAU,EAAE,CAAC;MACbZ,YAAY,EAAEA,YAAY;MAC1BE,kBAAkB,EAAEA;IACxB,CAAC,CAAC;IACF,IAAIW,KAAK,GAAGV,eAAe,CAAC1B,GAAG,CAAC,UAAUqC,IAAI,EAAE;MAC5C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,CAACtC,GAAG,CAAC,UAAUuC,OAAO,EAAE;QAChD,IAAItE,IAAI,GAAG6D,UAAU,CAACD,QAAQ,CAAC/F,GAAG,CAACmC,IAAI,EAAE8D,QAAQ,CAACQ,OAAO,CAACtE,IAAI,CAAC,CAAC,EAAE+D,UAAU,CAACO,OAAO,CAACtE,IAAI,CAAC,CAAC;QAC3F,OAAOlF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwJ,OAAO,CAAC,EAAE;UAAEtE,IAAI,EAAEA;QAAK,CAAC,CAAC;MAC1D,CAAC,CAAC;MACF,OAAOlF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsJ,IAAI,CAAC,EAAE;QAAEC,QAAQ,EAAEA;MAAS,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,SAASE,cAAcA,CAACC,SAAS,EAAEC,qBAAqB,EAAE;MACtD,IAAIC,WAAW,GAAGtF,IAAI,CAACX,GAAG,CAAC9C,KAAK,CAACyD,IAAI,EAAEqF,qBAAqB,CAAC1C,GAAG,CAAC,UAAU4C,MAAM,EAAE;QAAE,OAAOA,MAAM,CAACC,IAAI,GAAG,CAAC;MAAE,CAAC,CAAC,CAAC;MAChH,IAAIC,qBAAqB,GAAGL,SAAS,CAChC1E,MAAM,CAAC,UAAU6E,MAAM,EAAE;QAAE,OAAOA,MAAM,CAACC,IAAI,IAAIF,WAAW;MAAE,CAAC,CAAC,CAChE5E,MAAM,CAAC,UAAU6E,MAAM,EAAE;QAC1B,OAAQG,4BAA4B,CAACL,qBAAqB,EAAEE,MAAM,CAACI,GAAG,EAAEJ,MAAM,CAACI,GAAG,GAAGJ,MAAM,CAACK,MAAM,CAAC,CAAC1J,MAAM,GAAG,CAAC;MAClH,CAAC,CAAC;MACF,IAAIuJ,qBAAqB,CAACvJ,MAAM,GAAG,CAAC,EAAE;QAClC,OAAOiJ,cAAc,CAACC,SAAS,EAAEK,qBAAqB,CAAC;MAC3D,CAAC,MACI;QACD,OAAOH,WAAW;MACtB;IACJ;IACA,IAAIO,YAAY,GAAGjB,OAAO,CAACnE,MAAM,CAACkC,GAAG,CAAC,UAAU1D,KAAK,EAAE;MACnD,IAAIqG,WAAW,GAAGH,cAAc,CAACP,OAAO,CAACnE,MAAM,EAAEiF,4BAA4B,CAACd,OAAO,CAACnE,MAAM,EAAExB,KAAK,CAAC0G,GAAG,EAAE1G,KAAK,CAAC0G,GAAG,GAAG1G,KAAK,CAAC2G,MAAM,CAAC,CAAC;MACnI,IAAIE,KAAK,GAAG,GAAG,GAAGR,WAAW;MAC7B,OAAO5J,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuD,KAAK,CAAC,EAAE;QAAEuG,IAAI,EAAEvG,KAAK,CAACuG,IAAI,GAAGM,KAAK;QAAEA,KAAK,EAAEA;MAAM,CAAC,CAAC;IACpF,CAAC,CAAC;IACF,OAAO;MACHf,KAAK,EAAEA,KAAK;MACZnE,IAAI,EAAEnC,GAAG,CAACmC,IAAI;MACdH,MAAM,EAAEoF,YAAY,CAAClD,GAAG,CAAC,UAAU1D,KAAK,EAAE;QACtC,IAAI8G,iBAAiB,GAAGL,4BAA4B,CAACG,YAAY,CAACnF,MAAM,CAAC,UAAUsF,UAAU,EAAE;UAAE,OAAOA,UAAU,CAACR,IAAI,GAAGvG,KAAK,CAACuG,IAAI;QAAE,CAAC,CAAC,EAAEvG,KAAK,CAAC0G,GAAG,EAAE1G,KAAK,CAAC0G,GAAG,GAAG1G,KAAK,CAAC2G,MAAM,CAAC;QAC9K,IAAIG,iBAAiB,CAAC7J,MAAM,GAAG,CAAC,EAAE;UAC9B,OAAOR,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuD,KAAK,CAAC,EAAE;YAAE6G,KAAK,EAAE9F,IAAI,CAACiG,GAAG,CAAC1J,KAAK,CAACyD,IAAI,EAAE+F,iBAAiB,CAACpD,GAAG,CAAC,UAAUqD,UAAU,EAAE;cAAE,OAAOA,UAAU,CAACR,IAAI;YAAE,CAAC,CAAC,CAAC,GAAGvG,KAAK,CAACuG;UAAK,CAAC,CAAC;QAChK;QACA,OAAOvG,KAAK;MAChB,CAAC;IACL,CAAC;EACL,CAAC,CAAC;AACN;AACA,OAAO,SAASiH,WAAWA,CAACxI,WAAW,EAAEC,EAAE,EAAE;EACzC,IAAIkD,EAAE,GAAGlD,EAAE,CAAC8C,MAAM;IAAEA,MAAM,GAAGI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEW,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEC,YAAY,GAAG9D,EAAE,CAAC8D,YAAY;IAAEC,EAAE,GAAG/D,EAAE,CAACG,QAAQ;IAAEA,QAAQ,GAAG4D,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEE,EAAE,GAAGjE,EAAE,CAACI,SAAS;IAAEA,SAAS,GAAG6D,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,EAAE;IAAEU,EAAE,GAAG3E,EAAE,CAAC4E,wBAAwB;IAAEA,wBAAwB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAAE2B,YAAY,GAAGtG,EAAE,CAACsG,YAAY;IAAEC,YAAY,GAAGvG,EAAE,CAACuG,YAAY;IAAE9F,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IAAEC,MAAM,GAAGV,EAAE,CAACU,MAAM;IAAEyC,WAAW,GAAGnD,EAAE,CAACmD,WAAW;IAAEqD,aAAa,GAAGxG,EAAE,CAACwG,aAAa;IAAEC,kBAAkB,GAAGzG,EAAE,CAACyG,kBAAkB;IAAE+B,EAAE,GAAGxI,EAAE,CAACgE,SAAS;IAAEA,SAAS,GAAGwE,EAAE,KAAK,KAAK,CAAC,GAAGzI,WAAW,CAACqC,WAAW,CAACyB,QAAQ,EAAE;MAAEC,YAAY,EAAEA;IAAa,CAAC,CAAC,GAAG0E,EAAE;IAAEC,EAAE,GAAGzI,EAAE,CAACkE,OAAO;IAAEA,OAAO,GAAGuE,EAAE,KAAK,KAAK,CAAC,GAAG1I,WAAW,CAAC2I,SAAS,CAAC7E,QAAQ,EAAE;MAAEC,YAAY,EAAEA;IAAa,CAAC,CAAC,GAAG2E,EAAE;EACvvB,IAAI,CAAC3F,MAAM,EAAE;IACTA,MAAM,GAAG,EAAE;EACf;EACA,IAAI1B,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAED,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;EACxE6C,SAAS,GAAG5C,UAAU,CAAC4C,SAAS,CAAC;EACjCE,OAAO,GAAG/C,QAAQ,CAAC+C,OAAO,CAAC;EAC3B,IAAIyE,cAAc,GAAG9F,iBAAiB,CAAC9C,WAAW,EAAE;IAChD+C,MAAM,EAAEA,MAAM;IACdN,WAAW,EAAEwB,SAAS;IACtBvB,SAAS,EAAEyB;EACf,CAAC,CAAC;EACF,IAAI0E,MAAM,GAAGhF,iBAAiB,CAAC7D,WAAW,EAAE;IACxC8D,QAAQ,EAAEA,QAAQ;IAClBC,YAAY,EAAEA,YAAY;IAC1B3D,QAAQ,EAAEA,QAAQ;IAClBgD,WAAW,EAAEA,WAAW;IACxBa,SAAS,EAAEA,SAAS;IACpBE,OAAO,EAAEA;EACb,CAAC,CAAC;EACF,OAAO;IACHsB,eAAe,EAAEd,mBAAmB,CAAC3E,WAAW,EAAE;MAC9C+C,MAAM,EAAE6F,cAAc;MACtBxI,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA,SAAS;MACpBwE,wBAAwB,EAAEA,wBAAwB;MAClDZ,SAAS,EAAEA,SAAS;MACpBE,OAAO,EAAEA;IACb,CAAC,CAAC;IACF2E,MAAM,EAAE;MACJ/F,MAAM,EAAE6F,cAAc;MACtB7G,KAAK,EAAE8G,MAAM,CAAC,CAAC,CAAC,CAAC3F,IAAI;MACrBlB,GAAG,EAAEZ,QAAQ,CAACyH,MAAM,CAACA,MAAM,CAACrK,MAAM,GAAG,CAAC,CAAC,CAAC0E,IAAI;IAChD,CAAC;IACD6F,WAAW,EAAEzC,mBAAmB,CAACtG,WAAW,EAAE;MAC1C+C,MAAM,EAAEA,MAAM;MACde,QAAQ,EAAEA,QAAQ;MAClByC,YAAY,EAAEA,YAAY;MAC1BC,YAAY,EAAEA,YAAY;MAC1B9F,QAAQ,EAAEA,QAAQ;MAClBC,MAAM,EAAEA,MAAM;MACdoD,YAAY,EAAEA,YAAY;MAC1B3D,QAAQ,EAAEA,QAAQ;MAClBgD,WAAW,EAAEA,WAAW;MACxBqD,aAAa,EAAEA,aAAa;MAC5BxC,SAAS,EAAEA,SAAS;MACpBE,OAAO,EAAEA,OAAO;MAChBuC,kBAAkB,EAAEA;IACxB,CAAC;EACL,CAAC;AACL;AACA,OAAO,SAASsC,YAAYA,CAAChJ,WAAW,EAAEC,EAAE,EAAE;EAC1C,IAAIkD,EAAE,GAAGlD,EAAE,CAAC8C,MAAM;IAAEA,MAAM,GAAGI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEW,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEC,YAAY,GAAG9D,EAAE,CAAC8D,YAAY;IAAEC,EAAE,GAAG/D,EAAE,CAACG,QAAQ;IAAEA,QAAQ,GAAG4D,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAAEE,EAAE,GAAGjE,EAAE,CAACgE,SAAS;IAAEA,SAAS,GAAGC,EAAE,KAAK,KAAK,CAAC,GAAGlE,WAAW,CAACiJ,YAAY,CAACnF,QAAQ,CAAC,GAAGI,EAAE;IAAEU,EAAE,GAAG3E,EAAE,CAACkE,OAAO;IAAEA,OAAO,GAAGS,EAAE,KAAK,KAAK,CAAC,GAAG5E,WAAW,CAACkJ,UAAU,CAACpF,QAAQ,CAAC,GAAGc,EAAE;IAAExB,WAAW,GAAGnD,EAAE,CAACmD,WAAW;EAC1W,IAAI,CAACL,MAAM,EAAE;IACTA,MAAM,GAAG,EAAE;EACf;EACA,IAAIV,WAAW,GAAGrC,WAAW,CAACqC,WAAW;IAAEsG,SAAS,GAAG3I,WAAW,CAAC2I,SAAS;IAAE/G,gBAAgB,GAAG5B,WAAW,CAAC4B,gBAAgB;IAAEP,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAE8H,QAAQ,GAAGnJ,WAAW,CAACmJ,QAAQ;IAAE/H,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;IAAEgI,WAAW,GAAGpJ,WAAW,CAACoJ,WAAW;IAAE7I,MAAM,GAAGP,WAAW,CAACO,MAAM;EACxS,IAAIwB,KAAK,GAAGM,WAAW,CAAC4B,SAAS,EAAE;IAAEF,YAAY,EAAEA;EAAa,CAAC,CAAC;EAClE,IAAI/B,GAAG,GAAG2G,SAAS,CAACxE,OAAO,EAAE;IAAEJ,YAAY,EAAEA;EAAa,CAAC,CAAC;EAC5D,IAAIsF,aAAa,GAAGvG,iBAAiB,CAAC9C,WAAW,EAAE;IAC/C+C,MAAM,EAAEA,MAAM;IACdN,WAAW,EAAEV,KAAK;IAClBW,SAAS,EAAEV;EACf,CAAC,CAAC;EACF,IAAIsH,eAAe,GAAG,EAAE;EACxB,IAAIC,YAAY;EAChB,IAAIC,OAAO,GAAG,SAAAA,CAAUnL,CAAC,EAAE;IACvB;IACA,IAAI6E,IAAI;IACR,IAAIqG,YAAY,EAAE;MACdrG,IAAI,GAAG7B,UAAU,CAAC8H,QAAQ,CAACI,YAAY,EAAE3J,YAAY,CAAC,CAAC;MACvD,IAAI2J,YAAY,CAACE,OAAO,CAAC,CAAC,KAAKvG,IAAI,CAACuG,OAAO,CAAC,CAAC,EAAE;QAC3C;QACA;QACAvG,IAAI,GAAG7B,UAAU,CAAC8H,QAAQ,CAACI,YAAY,EAAE3J,YAAY,GAAG,CAAC,CAAC,CAAC;MAC/D;MACA2J,YAAY,GAAGrG,IAAI;IACvB,CAAC,MACI;MACDA,IAAI,GAAGqG,YAAY,GAAGxH,KAAK;IAC/B;IACA,IAAI,CAAC3B,QAAQ,CAACY,IAAI,CAAC,UAAUqD,CAAC,EAAE;MAAE,OAAO9D,MAAM,CAAC2C,IAAI,CAAC,KAAKmB,CAAC;IAAE,CAAC,CAAC,EAAE;MAC7D,IAAItD,GAAG,GAAGkC,UAAU,CAACjD,WAAW,EAAE;QAC9BkD,IAAI,EAAEA,IAAI;QACVE,WAAW,EAAEA;MACjB,CAAC,CAAC;MACF,IAAIwF,cAAc,GAAG9F,iBAAiB,CAAC9C,WAAW,EAAE;QAChD+C,MAAM,EAAEsG,aAAa;QACrB5G,WAAW,EAAEpB,UAAU,CAAC6B,IAAI,CAAC;QAC7BR,SAAS,EAAEtB,QAAQ,CAAC8B,IAAI;MAC5B,CAAC,CAAC;MACFnC,GAAG,CAAC2I,OAAO,GAAGN,WAAW,CAAClG,IAAI,EAAEY,QAAQ,CAAC;MACzC/C,GAAG,CAACgC,MAAM,GAAG6F,cAAc;MAC3B7H,GAAG,CAAC4I,UAAU,GAAGf,cAAc,CAACpK,MAAM;MACtC8K,eAAe,CAAChF,IAAI,CAACvD,GAAG,CAAC;IAC7B;EACJ,CAAC;EACD,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,gBAAgB,CAACI,GAAG,EAAED,KAAK,CAAC,GAAG,CAAC,EAAE1D,CAAC,EAAE,EAAE;IACvDmL,OAAO,CAACnL,CAAC,CAAC;EACd;EACA,IAAI+F,IAAI,GAAG,EAAE;EACb,IAAIwF,sBAAsB,GAAGjK,YAAY,GAAGS,QAAQ,CAAC5B,MAAM;EAC3D,IAAIoL,sBAAsB,GAAGjK,YAAY,EAAE;IACvC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,eAAe,CAAC9K,MAAM,EAAEH,CAAC,IAAIuL,sBAAsB,EAAE;MACrE,IAAIvD,GAAG,GAAGiD,eAAe,CAACjK,KAAK,CAAChB,CAAC,EAAEA,CAAC,GAAGuL,sBAAsB,CAAC;MAC9D,IAAIC,YAAY,GAAGxD,GAAG,CAACrF,IAAI,CAAC,UAAUD,GAAG,EAAE;QAAE,OAAOkD,SAAS,IAAIlD,GAAG,CAACmC,IAAI,IAAInC,GAAG,CAACmC,IAAI,GAAGiB,OAAO;MAAE,CAAC,CAAC;MACnG,IAAI0F,YAAY,EAAE;QACdzF,IAAI,GAAGtF,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEsF,IAAI,EAAE,IAAI,CAAC,EAAEiC,GAAG,EAAE,IAAI,CAAC;MAClE;IACJ;EACJ,CAAC,MACI;IACDjC,IAAI,GAAGkF,eAAe;EAC1B;EACA,IAAIQ,IAAI,GAAGxH,IAAI,CAACyH,KAAK,CAAC3F,IAAI,CAAC5F,MAAM,GAAGoL,sBAAsB,CAAC;EAC3D,IAAII,UAAU,GAAG,EAAE;EACnB,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyL,IAAI,EAAEzL,CAAC,EAAE,EAAE;IAC3B2L,UAAU,CAAC1F,IAAI,CAACjG,CAAC,GAAGuL,sBAAsB,CAAC;EAC/C;EACA,OAAO;IACHI,UAAU,EAAEA,UAAU;IACtBJ,sBAAsB,EAAEA,sBAAsB;IAC9CxF,IAAI,EAAEA,IAAI;IACV0E,MAAM,EAAE;MACJ/G,KAAK,EAAEqC,IAAI,CAAC,CAAC,CAAC,CAAClB,IAAI;MACnBlB,GAAG,EAAEZ,QAAQ,CAACgD,IAAI,CAACA,IAAI,CAAC5F,MAAM,GAAG,CAAC,CAAC,CAAC0E,IAAI,CAAC;MACzCH,MAAM,EAAEsG;IACZ;EACJ,CAAC;AACL;AACA,SAASrB,4BAA4BA,CAACjF,MAAM,EAAEkF,GAAG,EAAEgC,MAAM,EAAE;EACvD,OAAOlH,MAAM,CAACC,MAAM,CAAC,UAAUkH,aAAa,EAAE;IAC1C,IAAIC,gBAAgB,GAAGD,aAAa,CAACjC,GAAG;IACxC,IAAImC,mBAAmB,GAAGF,aAAa,CAACjC,GAAG,GAAGiC,aAAa,CAAChC,MAAM;IAClE,IAAID,GAAG,GAAGmC,mBAAmB,IAAIA,mBAAmB,GAAGH,MAAM,EAAE;MAC3D,OAAO,IAAI;IACf,CAAC,MACI,IAAIhC,GAAG,GAAGkC,gBAAgB,IAAIA,gBAAgB,GAAGF,MAAM,EAAE;MAC1D,OAAO,IAAI;IACf,CAAC,MACI,IAAIE,gBAAgB,IAAIlC,GAAG,IAAIgC,MAAM,IAAIG,mBAAmB,EAAE;MAC/D,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC,CAAC;AACN;AACA,SAASjD,UAAUA,CAACnH,WAAW,EAAEC,EAAE,EAAE;EACjC,IAAI8C,MAAM,GAAG9C,EAAE,CAAC8C,MAAM;IAAEe,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEyC,YAAY,GAAGtG,EAAE,CAACsG,YAAY;IAAE7F,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IAAEC,MAAM,GAAGV,EAAE,CAACU,MAAM;IAAEyG,UAAU,GAAGnH,EAAE,CAACmH,UAAU;IAAEX,aAAa,GAAGxG,EAAE,CAACwG,aAAa;IAAED,YAAY,GAAGvG,EAAE,CAACuG,YAAY;IAAEE,kBAAkB,GAAGzG,EAAE,CAACyG,kBAAkB;EACpQ,IAAIK,UAAU,GAAG/G,WAAW,CAAC+G,UAAU;IAAED,QAAQ,GAAG9G,WAAW,CAAC8G,QAAQ;IAAEzF,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAEgJ,aAAa,GAAGrK,WAAW,CAACqK,aAAa;IAAEjJ,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;IAAEkJ,mBAAmB,GAAGtK,WAAW,CAACsK,mBAAmB;EAChP,IAAIC,WAAW,GAAGxD,UAAU,CAACD,QAAQ,CAACzF,UAAU,CAACyC,QAAQ,CAAC,EAAE0G,aAAa,CAAC9J,QAAQ,CAAC4G,IAAI,CAAC,CAAC,EAAEmD,eAAe,CAAC/J,QAAQ,CAACgK,MAAM,CAAC,CAAC;EAC5H,IAAIC,SAAS,GAAG5D,UAAU,CAACD,QAAQ,CAACuD,aAAa,CAACjJ,QAAQ,CAAC0C,QAAQ,CAAC,CAAC,EAAE0G,aAAa,CAAC7J,MAAM,CAAC2G,IAAI,CAAC,CAAC,EAAEmD,eAAe,CAAC9J,MAAM,CAAC+J,MAAM,CAAC,CAAC;EACnIC,SAAS,CAACC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC;EAC7B,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIjC,cAAc,GAAG9F,iBAAiB,CAAC9C,WAAW,EAAE;IAChD+C,MAAM,EAAEA,MAAM,CAACC,MAAM,CAAC,UAAUzB,KAAK,EAAE;MAAE,OAAO,CAACA,KAAK,CAACyD,MAAM;IAAE,CAAC,CAAC;IACjEvC,WAAW,EAAE8H,WAAW;IACxB7H,SAAS,EAAEiI;EACf,CAAC,CAAC;EACF,IAAIG,aAAa,GAAGlC,cAAc,CAC7BvD,IAAI,CAAC,UAAU0F,MAAM,EAAEC,MAAM,EAAE;IAChC,OAAOD,MAAM,CAAChJ,KAAK,CAACkJ,OAAO,CAAC,CAAC,GAAGD,MAAM,CAACjJ,KAAK,CAACkJ,OAAO,CAAC,CAAC;EAC1D,CAAC,CAAC,CACGhG,GAAG,CAAC,UAAU1D,KAAK,EAAE;IACtB,IAAIqB,UAAU,GAAGrB,KAAK,CAACQ,KAAK;IAC5B,IAAIc,QAAQ,GAAGtB,KAAK,CAACS,GAAG,IAAIY,UAAU;IACtC,IAAIsI,eAAe,GAAGtI,UAAU,GAAG2H,WAAW;IAC9C,IAAIY,YAAY,GAAGtI,QAAQ,GAAG8H,SAAS;IACvC,IAAIS,kBAAkB,GAAI7E,YAAY,GAAGE,aAAa,IAAKD,YAAY,IAAI3G,eAAe,CAAC;IAC3F,IAAIoI,GAAG,GAAG,CAAC;IACX,IAAIrF,UAAU,GAAG2H,WAAW,EAAE;MAC1B;MACA,IAAIc,WAAW,GAAGrL,WAAW,CAACsL,iBAAiB,CAAC1I,UAAU,CAAC;MAC3D,IAAI2I,WAAW,GAAGvL,WAAW,CAACsL,iBAAiB,CAACf,WAAW,CAAC;MAC5D,IAAI7F,IAAI,GAAG6G,WAAW,GAAGF,WAAW;MACpCpD,GAAG,IAAIqC,mBAAmB,CAAC1H,UAAU,EAAE2H,WAAW,CAAC,GAAG7F,IAAI;IAC9D;IACAuD,GAAG,IAAImD,kBAAkB;IACzBnD,GAAG,GAAG3F,IAAI,CAACyH,KAAK,CAAC9B,GAAG,CAAC;IACrB,IAAI/H,SAAS,GAAGgL,eAAe,GAAGX,WAAW,GAAG3H,UAAU;IAC1D,IAAInC,OAAO,GAAG0K,YAAY,GAAGR,SAAS,GAAG9H,QAAQ;IACjD,IAAI2I,cAAc,GAAGxL,WAAW,CAACsL,iBAAiB,CAACpL,SAAS,CAAC,GACzDF,WAAW,CAACsL,iBAAiB,CAAC7K,OAAO,CAAC;IAC1C,IAAIyH,MAAM,GAAGoC,mBAAmB,CAAC7J,OAAO,EAAEP,SAAS,CAAC,GAAGsL,cAAc;IACrE,IAAI,CAACjK,KAAK,CAACS,GAAG,EAAE;MACZkG,MAAM,GAAGzB,aAAa;IAC1B,CAAC,MACI;MACDyB,MAAM,IAAIkD,kBAAkB;IAChC;IACA,IAAI1E,kBAAkB,IAAIwB,MAAM,GAAGxB,kBAAkB,EAAE;MACnDwB,MAAM,GAAGxB,kBAAkB;IAC/B;IACAwB,MAAM,GAAG5F,IAAI,CAACyH,KAAK,CAAC7B,MAAM,CAAC;IAC3B,IAAI+B,MAAM,GAAGhC,GAAG,GAAGC,MAAM;IACzB,IAAIuD,yBAAyB,GAAGzD,4BAA4B,CAAC6C,iBAAiB,EAAE5C,GAAG,EAAEgC,MAAM,CAAC;IAC5F,IAAInC,IAAI,GAAG,CAAC;IACZ,OAAO2D,yBAAyB,CAACzK,IAAI,CAAC,UAAUkJ,aAAa,EAAE;MAAE,OAAOA,aAAa,CAACpC,IAAI,KAAKA,IAAI;IAAE,CAAC,CAAC,EAAE;MACrGA,IAAI,IAAIV,UAAU;IACtB;IACA,IAAIsE,QAAQ,GAAG;MACXnK,KAAK,EAAEA,KAAK;MACZ2G,MAAM,EAAEA,MAAM;MACdE,KAAK,EAAEhB,UAAU;MACjBa,GAAG,EAAEA,GAAG;MACRH,IAAI,EAAEA,IAAI;MACVoD,eAAe,EAAEA,eAAe;MAChCC,YAAY,EAAEA;IAClB,CAAC;IACDN,iBAAiB,CAACvG,IAAI,CAACoH,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACnB,CAAC,CAAC;EACF,IAAItD,KAAK,GAAG9F,IAAI,CAACX,GAAG,CAAC9C,KAAK,CAACyD,IAAI,EAAEwI,aAAa,CAAC7F,GAAG,CAAC,UAAU1D,KAAK,EAAE;IAAE,OAAOA,KAAK,CAACuG,IAAI,GAAGvG,KAAK,CAAC6G,KAAK;EAAE,CAAC,CAAC,CAAC;EAC1G,IAAIuD,YAAY,GAAG7I,iBAAiB,CAAC9C,WAAW,EAAE;IAC9C+C,MAAM,EAAEA,MAAM,CAACC,MAAM,CAAC,UAAUzB,KAAK,EAAE;MAAE,OAAOA,KAAK,CAACyD,MAAM;IAAE,CAAC,CAAC;IAChEvC,WAAW,EAAEpB,UAAU,CAACkJ,WAAW,CAAC;IACpC7H,SAAS,EAAEtB,QAAQ,CAACuJ,SAAS;EACjC,CAAC,CAAC;EACF,OAAO;IACH5H,MAAM,EAAE+H,aAAa;IACrB1C,KAAK,EAAEA,KAAK;IACZuD,YAAY,EAAEA,YAAY;IAC1B7C,MAAM,EAAE;MACJ/F,MAAM,EAAE6F,cAAc;MACtB7G,KAAK,EAAEwI,WAAW;MAClBvI,GAAG,EAAE2I;IACT;EACJ,CAAC;AACL;AACA,SAASH,aAAaA,CAACnD,KAAK,EAAE;EAC1B,OAAO/E,IAAI,CAACX,GAAG,CAACW,IAAI,CAACiG,GAAG,CAAC,EAAE,EAAElB,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3C;AACA,SAASoD,eAAeA,CAACmB,OAAO,EAAE;EAC9B,OAAOtJ,IAAI,CAACX,GAAG,CAACW,IAAI,CAACiG,GAAG,CAAC,EAAE,EAAEqD,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7C;AACA,SAAShF,kBAAkBA,CAAC5G,WAAW,EAAEC,EAAE,EAAE;EACzC,IAAI6D,QAAQ,GAAG7D,EAAE,CAAC6D,QAAQ;IAAEyC,YAAY,GAAGtG,EAAE,CAACsG,YAAY;IAAEC,YAAY,GAAGvG,EAAE,CAACuG,YAAY;IAAE9F,QAAQ,GAAGT,EAAE,CAACS,QAAQ;IAAEC,MAAM,GAAGV,EAAE,CAACU,MAAM;EACtI,IAAIoG,UAAU,GAAG/G,WAAW,CAAC+G,UAAU;IAAED,QAAQ,GAAG9G,WAAW,CAAC8G,QAAQ;IAAEzF,UAAU,GAAGrB,WAAW,CAACqB,UAAU;IAAEgJ,aAAa,GAAGrK,WAAW,CAACqK,aAAa;IAAEjJ,QAAQ,GAAGpB,WAAW,CAACoB,QAAQ;IAAEyK,UAAU,GAAG7L,WAAW,CAAC6L,UAAU;IAAErL,OAAO,GAAGR,WAAW,CAACQ,OAAO;EAC7P,IAAI6G,KAAK,GAAG,EAAE;EACd,IAAIkD,WAAW,GAAGxD,UAAU,CAACD,QAAQ,CAACzF,UAAU,CAACyC,QAAQ,CAAC,EAAE0G,aAAa,CAAC9J,QAAQ,CAAC4G,IAAI,CAAC,CAAC,EAAEmD,eAAe,CAAC/J,QAAQ,CAACgK,MAAM,CAAC,CAAC;EAC5H,IAAIC,SAAS,GAAG5D,UAAU,CAACD,QAAQ,CAACuD,aAAa,CAACjJ,QAAQ,CAAC0C,QAAQ,CAAC,CAAC,EAAE0G,aAAa,CAAC7J,MAAM,CAAC2G,IAAI,CAAC,CAAC,EAAEmD,eAAe,CAAC9J,MAAM,CAAC+J,MAAM,CAAC,CAAC;EACnI,IAAIoB,eAAe,GAAG,CAACtF,YAAY,IAAI3G,eAAe,IAAI0G,YAAY;EACtE,IAAIwF,cAAc,GAAG1K,UAAU,CAACyC,QAAQ,CAAC;EACzC,IAAIkI,YAAY,GAAG5K,QAAQ,CAAC0C,QAAQ,CAAC;EACrC,IAAImI,cAAc,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC;EAAE,CAAC;EAC/C;EACA,IAAIlM,WAAW,CAACsL,iBAAiB,CAACS,cAAc,CAAC,KAC7C/L,WAAW,CAACsL,iBAAiB,CAACU,YAAY,CAAC,EAAE;IAC7CD,cAAc,GAAGvL,OAAO,CAACuL,cAAc,EAAE,CAAC,CAAC;IAC3CxB,WAAW,GAAG/J,OAAO,CAAC+J,WAAW,EAAE,CAAC,CAAC;IACrCI,SAAS,GAAGnK,OAAO,CAACmK,SAAS,EAAE,CAAC,CAAC;IACjCsB,cAAc,GAAG,SAAAA,CAAUC,CAAC,EAAE;MAAE,OAAO1L,OAAO,CAAC0L,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,CAAC;EAC5D;EACA,IAAIC,WAAW,GAAG3F,YAAY,GACvB5G,YAAY,GAAG,EAAE,GAAI4G,YAAY,GAClC3G,eAAe;EACrB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8N,WAAW,EAAE9N,CAAC,EAAE,EAAE;IAClC,IAAIkJ,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,YAAY,EAAE6F,CAAC,EAAE,EAAE;MACnC,IAAIlJ,IAAI,GAAG2I,UAAU,CAACA,UAAU,CAACtB,WAAW,EAAElM,CAAC,IAAImI,YAAY,IAAI3G,eAAe,CAAC,CAAC,EAAEuM,CAAC,GAAGN,eAAe,CAAC;MAC1G,IAAI5I,IAAI,IAAIqH,WAAW,IAAIrH,IAAI,GAAGyH,SAAS,EAAE;QACzCpD,QAAQ,CAACjD,IAAI,CAAC;UACVpB,IAAI,EAAE+I,cAAc,CAAC/I,IAAI,CAAC;UAC1BmJ,WAAW,EAAEnJ,IAAI;UACjBoJ,OAAO,EAAEF,CAAC,KAAK;QACnB,CAAC,CAAC;MACN;IACJ;IACA,IAAI7E,QAAQ,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACrB6I,KAAK,CAAC/C,IAAI,CAAC;QAAEiD,QAAQ,EAAEA;MAAS,CAAC,CAAC;IACtC;EACJ;EACA,OAAOF,KAAK;AAChB;AACA,OAAO,IAAIkF,2BAA2B;AACtC,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAAC,UAAU,CAAC,GAAG,yBAAyB;EACnEA,2BAA2B,CAAC,sBAAsB,CAAC,GAAG,uCAAuC;EAC7FA,2BAA2B,CAAC,sBAAsB,CAAC,GAAG,kGAAkG;EACxJA,2BAA2B,CAAC,oBAAoB,CAAC,GAAG,8FAA8F;EAClJA,2BAA2B,CAAC,iBAAiB,CAAC,GAAG,+CAA+C;AACpG,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,OAAO,SAASC,cAAcA,CAACzJ,MAAM,EAAE0J,GAAG,EAAE;EACxC,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,OAAOA,CAACC,GAAG,EAAErL,KAAK,EAAE;IACzBkL,GAAG,CAACG,GAAG,EAAErL,KAAK,CAAC;IACfmL,OAAO,GAAG,KAAK;EACnB;EACA,IAAI,CAACtN,KAAK,CAACyN,OAAO,CAAC9J,MAAM,CAAC,EAAE;IACxB0J,GAAG,CAACF,2BAA2B,CAACO,QAAQ,EAAE/J,MAAM,CAAC;IACjD,OAAO,KAAK;EAChB;EACAA,MAAM,CAAC4C,OAAO,CAAC,UAAUpE,KAAK,EAAE;IAC5B,IAAI,CAACA,KAAK,CAACQ,KAAK,EAAE;MACd4K,OAAO,CAACJ,2BAA2B,CAACQ,oBAAoB,EAAExL,KAAK,CAAC;IACpE,CAAC,MACI,IAAI,EAAEA,KAAK,CAACQ,KAAK,YAAYwB,IAAI,CAAC,EAAE;MACrCoJ,OAAO,CAACJ,2BAA2B,CAACS,oBAAoB,EAAEzL,KAAK,CAAC;IACpE;IACA,IAAIA,KAAK,CAACS,GAAG,EAAE;MACX,IAAI,EAAET,KAAK,CAACS,GAAG,YAAYuB,IAAI,CAAC,EAAE;QAC9BoJ,OAAO,CAACJ,2BAA2B,CAACU,kBAAkB,EAAE1L,KAAK,CAAC;MAClE;MACA,IAAIA,KAAK,CAACQ,KAAK,GAAGR,KAAK,CAACS,GAAG,EAAE;QACzB2K,OAAO,CAACJ,2BAA2B,CAACW,eAAe,EAAE3L,KAAK,CAAC;MAC/D;IACJ;EACJ,CAAC,CAAC;EACF,OAAOmL,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}