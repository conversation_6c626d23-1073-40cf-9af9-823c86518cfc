{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ProfileRoutingModule } from './profile-routing.module';\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, ProfileRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, ProfileRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "ProfileRoutingModule", "ProfileComponent", "ProfileModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { ProfileRoutingModule } from './profile-routing.module';\r\nimport { ProfileComponent } from './profile.component';\r\n\r\n@NgModule({\r\n  declarations: [ProfileComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ProfileRoutingModule,\r\n  ],\r\n})\r\nexport class ProfileModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;;AAWtD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBANtBL,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,oBAAoB;IAAA;EAAA;;;2EAGXE,aAAa;IAAAC,YAAA,GARTF,gBAAgB;IAAAG,OAAA,GAE7BP,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,oBAAoB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}