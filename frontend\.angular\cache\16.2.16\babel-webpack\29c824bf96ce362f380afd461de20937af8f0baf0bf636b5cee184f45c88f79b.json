{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\nimport * as i0 from \"@angular/core\";\nexport class EquipesModule {\n  static {\n    this.ɵfac = function EquipesModule_Factory(t) {\n      return new (t || EquipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EquipesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EquipesModule, {\n    declarations: [EquipeListComponent, EquipeFormComponent, EquipeDetailComponent, TaskListComponent, AiChatComponent, EquipeComponent, NotificationComponent, EquipeLayoutComponent],\n    imports: [CommonModule, EquipesRoutingModule, FormsModule, DragDropModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "EquipesRoutingModule", "EquipeListComponent", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "AiChatComponent", "EquipeComponent", "NotificationComponent", "HttpClientModule", "FormsModule", "DragDropModule", "EquipeLayoutComponent", "EquipesModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { EquipesRoutingModule } from './equipes-routing.module';\r\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\r\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\r\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\r\nimport { TaskListComponent } from './task-list/task-list.component';\r\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\r\nimport { EquipeComponent } from './equipe/equipe.component';\r\nimport { NotificationComponent } from './notification/notification.component';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    EquipeListComponent,\r\n    EquipeFormComponent,\r\n    EquipeDetailComponent,\r\n    TaskListComponent,\r\n    AiChatComponent,\r\n    EquipeComponent,\r\n    NotificationComponent,\r\n    EquipeLayoutComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    EquipesRoutingModule,\r\n    FormsModule,\r\n    DragDropModule,\r\n    HttpClientModule,\r\n  ],\r\n  providers: [],\r\n})\r\nexport class EquipesModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,qBAAqB,QAAQ,yCAAyC;;AAsB/E,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBARtBb,YAAY,EACZC,oBAAoB,EACpBS,WAAW,EACXC,cAAc,EACdF,gBAAgB;IAAA;EAAA;;;2EAIPI,aAAa;IAAAC,YAAA,GAlBtBZ,mBAAmB,EACnBC,mBAAmB,EACnBC,qBAAqB,EACrBC,iBAAiB,EACjBC,eAAe,EACfC,eAAe,EACfC,qBAAqB,EACrBI,qBAAqB;IAAAG,OAAA,GAGrBf,YAAY,EACZC,oBAAoB,EACpBS,WAAW,EACXC,cAAc,EACdF,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}