{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { GraphqlStatusComponent } from './graphql-status.component';\nimport * as i0 from \"@angular/core\";\nexport class GraphqlStatusModule {\n  static {\n    this.ɵfac = function GraphqlStatusModule_Factory(t) {\n      return new (t || GraphqlStatusModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GraphqlStatusModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GraphqlStatusModule, {\n    declarations: [GraphqlStatusComponent],\n    imports: [CommonModule],\n    exports: [GraphqlStatusComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "GraphqlStatusComponent", "GraphqlStatusModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\graphql-status\\graphql-status.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { GraphqlStatusComponent } from './graphql-status.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    GraphqlStatusComponent\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    GraphqlStatusComponent\r\n  ]\r\n})\r\nexport class GraphqlStatusModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,4BAA4B;;AAanE,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAN5BF,YAAY;IAAA;EAAA;;;2EAMHE,mBAAmB;IAAAC,YAAA,GAT5BF,sBAAsB;IAAAG,OAAA,GAGtBJ,YAAY;IAAAK,OAAA,GAGZJ,sBAAsB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}