{"ast": null, "code": "export function createFulfilledPromise(value) {\n  var promise = Promise.resolve(value);\n  promise.status = \"fulfilled\";\n  promise.value = value;\n  return promise;\n}\nexport function createRejectedPromise(reason) {\n  var promise = Promise.reject(reason);\n  // prevent potential edge cases leaking unhandled error rejections\n  promise.catch(function () {});\n  promise.status = \"rejected\";\n  promise.reason = reason;\n  return promise;\n}\nexport function isStatefulPromise(promise) {\n  return \"status\" in promise;\n}\nexport function wrapPromiseWithState(promise) {\n  if (isStatefulPromise(promise)) {\n    return promise;\n  }\n  var pendingPromise = promise;\n  pendingPromise.status = \"pending\";\n  pendingPromise.then(function (value) {\n    if (pendingPromise.status === \"pending\") {\n      var fulfilledPromise = pendingPromise;\n      fulfilledPromise.status = \"fulfilled\";\n      fulfilledPromise.value = value;\n    }\n  }, function (reason) {\n    if (pendingPromise.status === \"pending\") {\n      var rejectedPromise = pendingPromise;\n      rejectedPromise.status = \"rejected\";\n      rejectedPromise.reason = reason;\n    }\n  });\n  return promise;\n}", "map": {"version": 3, "names": ["createFulfilledPromise", "value", "promise", "Promise", "resolve", "status", "createRejectedPromise", "reason", "reject", "catch", "isStatefulPromise", "wrapPromiseWithState", "pendingPromise", "then", "fulfilledPromise", "rejectedPromise"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/promises/decoration.js"], "sourcesContent": ["export function createFulfilledPromise(value) {\n    var promise = Promise.resolve(value);\n    promise.status = \"fulfilled\";\n    promise.value = value;\n    return promise;\n}\nexport function createRejectedPromise(reason) {\n    var promise = Promise.reject(reason);\n    // prevent potential edge cases leaking unhandled error rejections\n    promise.catch(function () { });\n    promise.status = \"rejected\";\n    promise.reason = reason;\n    return promise;\n}\nexport function isStatefulPromise(promise) {\n    return \"status\" in promise;\n}\nexport function wrapPromiseWithState(promise) {\n    if (isStatefulPromise(promise)) {\n        return promise;\n    }\n    var pendingPromise = promise;\n    pendingPromise.status = \"pending\";\n    pendingPromise.then(function (value) {\n        if (pendingPromise.status === \"pending\") {\n            var fulfilledPromise = pendingPromise;\n            fulfilledPromise.status = \"fulfilled\";\n            fulfilledPromise.value = value;\n        }\n    }, function (reason) {\n        if (pendingPromise.status === \"pending\") {\n            var rejectedPromise = pendingPromise;\n            rejectedPromise.status = \"rejected\";\n            rejectedPromise.reason = reason;\n        }\n    });\n    return promise;\n}\n"], "mappings": "AAAA,OAAO,SAASA,sBAAsBA,CAACC,KAAK,EAAE;EAC1C,IAAIC,OAAO,GAAGC,OAAO,CAACC,OAAO,CAACH,KAAK,CAAC;EACpCC,OAAO,CAACG,MAAM,GAAG,WAAW;EAC5BH,OAAO,CAACD,KAAK,GAAGA,KAAK;EACrB,OAAOC,OAAO;AAClB;AACA,OAAO,SAASI,qBAAqBA,CAACC,MAAM,EAAE;EAC1C,IAAIL,OAAO,GAAGC,OAAO,CAACK,MAAM,CAACD,MAAM,CAAC;EACpC;EACAL,OAAO,CAACO,KAAK,CAAC,YAAY,CAAE,CAAC,CAAC;EAC9BP,OAAO,CAACG,MAAM,GAAG,UAAU;EAC3BH,OAAO,CAACK,MAAM,GAAGA,MAAM;EACvB,OAAOL,OAAO;AAClB;AACA,OAAO,SAASQ,iBAAiBA,CAACR,OAAO,EAAE;EACvC,OAAO,QAAQ,IAAIA,OAAO;AAC9B;AACA,OAAO,SAASS,oBAAoBA,CAACT,OAAO,EAAE;EAC1C,IAAIQ,iBAAiB,CAACR,OAAO,CAAC,EAAE;IAC5B,OAAOA,OAAO;EAClB;EACA,IAAIU,cAAc,GAAGV,OAAO;EAC5BU,cAAc,CAACP,MAAM,GAAG,SAAS;EACjCO,cAAc,CAACC,IAAI,CAAC,UAAUZ,KAAK,EAAE;IACjC,IAAIW,cAAc,CAACP,MAAM,KAAK,SAAS,EAAE;MACrC,IAAIS,gBAAgB,GAAGF,cAAc;MACrCE,gBAAgB,CAACT,MAAM,GAAG,WAAW;MACrCS,gBAAgB,CAACb,KAAK,GAAGA,KAAK;IAClC;EACJ,CAAC,EAAE,UAAUM,MAAM,EAAE;IACjB,IAAIK,cAAc,CAACP,MAAM,KAAK,SAAS,EAAE;MACrC,IAAIU,eAAe,GAAGH,cAAc;MACpCG,eAAe,CAACV,MAAM,GAAG,UAAU;MACnCU,eAAe,CAACR,MAAM,GAAGA,MAAM;IACnC;EACJ,CAAC,CAAC;EACF,OAAOL,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}