{"ast": null, "code": "import { GraphQLError } from '../error/GraphQLError.mjs';\n\n/**\n * Extracts the root type of the operation from the schema.\n *\n * @deprecated Please use `GraphQLSchema.getRootType` instead. Will be removed in v17\n */\nexport function getOperationRootType(schema, operation) {\n  if (operation.operation === 'query') {\n    const queryType = schema.getQueryType();\n    if (!queryType) {\n      throw new GraphQLError('Schema does not define the required query root type.', {\n        nodes: operation\n      });\n    }\n    return queryType;\n  }\n  if (operation.operation === 'mutation') {\n    const mutationType = schema.getMutationType();\n    if (!mutationType) {\n      throw new GraphQLError('Schema is not configured for mutations.', {\n        nodes: operation\n      });\n    }\n    return mutationType;\n  }\n  if (operation.operation === 'subscription') {\n    const subscriptionType = schema.getSubscriptionType();\n    if (!subscriptionType) {\n      throw new GraphQLError('Schema is not configured for subscriptions.', {\n        nodes: operation\n      });\n    }\n    return subscriptionType;\n  }\n  throw new GraphQLError('Can only have query, mutation and subscription operations.', {\n    nodes: operation\n  });\n}", "map": {"version": 3, "names": ["GraphQLError", "getOperationRootType", "schema", "operation", "queryType", "getQueryType", "nodes", "mutationType", "getMutationType", "subscriptionType", "getSubscriptionType"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/utilities/getOperationRootType.mjs"], "sourcesContent": ["import { GraphQLError } from '../error/GraphQLError.mjs';\n\n/**\n * Extracts the root type of the operation from the schema.\n *\n * @deprecated Please use `GraphQLSchema.getRootType` instead. Will be removed in v17\n */\nexport function getOperationRootType(schema, operation) {\n  if (operation.operation === 'query') {\n    const queryType = schema.getQueryType();\n\n    if (!queryType) {\n      throw new GraphQLError(\n        'Schema does not define the required query root type.',\n        {\n          nodes: operation,\n        },\n      );\n    }\n\n    return queryType;\n  }\n\n  if (operation.operation === 'mutation') {\n    const mutationType = schema.getMutationType();\n\n    if (!mutationType) {\n      throw new GraphQLError('Schema is not configured for mutations.', {\n        nodes: operation,\n      });\n    }\n\n    return mutationType;\n  }\n\n  if (operation.operation === 'subscription') {\n    const subscriptionType = schema.getSubscriptionType();\n\n    if (!subscriptionType) {\n      throw new GraphQLError('Schema is not configured for subscriptions.', {\n        nodes: operation,\n      });\n    }\n\n    return subscriptionType;\n  }\n\n  throw new GraphQLError(\n    'Can only have query, mutation and subscription operations.',\n    {\n      nodes: operation,\n    },\n  );\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,2BAA2B;;AAExD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACtD,IAAIA,SAAS,CAACA,SAAS,KAAK,OAAO,EAAE;IACnC,MAAMC,SAAS,GAAGF,MAAM,CAACG,YAAY,CAAC,CAAC;IAEvC,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIJ,YAAY,CACpB,sDAAsD,EACtD;QACEM,KAAK,EAAEH;MACT,CACF,CAAC;IACH;IAEA,OAAOC,SAAS;EAClB;EAEA,IAAID,SAAS,CAACA,SAAS,KAAK,UAAU,EAAE;IACtC,MAAMI,YAAY,GAAGL,MAAM,CAACM,eAAe,CAAC,CAAC;IAE7C,IAAI,CAACD,YAAY,EAAE;MACjB,MAAM,IAAIP,YAAY,CAAC,yCAAyC,EAAE;QAChEM,KAAK,EAAEH;MACT,CAAC,CAAC;IACJ;IAEA,OAAOI,YAAY;EACrB;EAEA,IAAIJ,SAAS,CAACA,SAAS,KAAK,cAAc,EAAE;IAC1C,MAAMM,gBAAgB,GAAGP,MAAM,CAACQ,mBAAmB,CAAC,CAAC;IAErD,IAAI,CAACD,gBAAgB,EAAE;MACrB,MAAM,IAAIT,YAAY,CAAC,6CAA6C,EAAE;QACpEM,KAAK,EAAEH;MACT,CAAC,CAAC;IACJ;IAEA,OAAOM,gBAAgB;EACzB;EAEA,MAAM,IAAIT,YAAY,CACpB,4DAA4D,EAC5D;IACEM,KAAK,EAAEH;EACT,CACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}