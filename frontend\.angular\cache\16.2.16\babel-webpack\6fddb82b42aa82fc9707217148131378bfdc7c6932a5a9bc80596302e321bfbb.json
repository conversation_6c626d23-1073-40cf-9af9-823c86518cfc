{"ast": null, "code": "import { ApolloLink } from \"./ApolloLink.js\";\nexport var concat = ApolloLink.concat;", "map": {"version": 3, "names": ["ApolloLink", "concat"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/concat.js"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\nexport var concat = ApolloLink.concat;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,MAAM,GAAGD,UAAU,CAACC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}