{"ast": null, "code": "import { Observable } from \"./Observable.js\";\nimport { canUseSymbol } from \"../common/canUse.js\";\n// Generic implementations of Observable.prototype methods like map and\n// filter need to know how to create a new Observable from an Observable\n// subclass (like Concast or ObservableQuery). Those methods assume\n// (perhaps unwisely?) that they can call the subtype's constructor with a\n// Subscriber function, even though the subclass constructor might expect\n// different parameters. Defining this static Symbol.species property on\n// the subclass is a hint to generic Observable code to use the default\n// constructor instead of trying to do `new Subclass(observer => ...)`.\nexport function fixObservableSubclass(subclass) {\n  function set(key) {\n    // Object.defineProperty is necessary because the Symbol.species\n    // property is a getter by default in modern JS environments, so we\n    // can't assign to it with a normal assignment expression.\n    Object.defineProperty(subclass, key, {\n      value: Observable\n    });\n  }\n  if (canUseSymbol && Symbol.species) {\n    set(Symbol.species);\n  }\n  // The \"@@species\" string is used as a fake Symbol.species value in some\n  // polyfill systems (including the SymbolSpecies variable used by\n  // zen-observable), so we should set it as well, to be safe.\n  set(\"@@species\");\n  return subclass;\n}", "map": {"version": 3, "names": ["Observable", "canUseSymbol", "fixObservableSubclass", "subclass", "set", "key", "Object", "defineProperty", "value", "Symbol", "species"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/observables/subclassing.js"], "sourcesContent": ["import { Observable } from \"./Observable.js\";\nimport { canUseSymbol } from \"../common/canUse.js\";\n// Generic implementations of Observable.prototype methods like map and\n// filter need to know how to create a new Observable from an Observable\n// subclass (like Concast or ObservableQuery). Those methods assume\n// (perhaps unwisely?) that they can call the subtype's constructor with a\n// Subscriber function, even though the subclass constructor might expect\n// different parameters. Defining this static Symbol.species property on\n// the subclass is a hint to generic Observable code to use the default\n// constructor instead of trying to do `new Subclass(observer => ...)`.\nexport function fixObservableSubclass(subclass) {\n    function set(key) {\n        // Object.defineProperty is necessary because the Symbol.species\n        // property is a getter by default in modern JS environments, so we\n        // can't assign to it with a normal assignment expression.\n        Object.defineProperty(subclass, key, { value: Observable });\n    }\n    if (canUseSymbol && Symbol.species) {\n        set(Symbol.species);\n    }\n    // The \"@@species\" string is used as a fake Symbol.species value in some\n    // polyfill systems (including the SymbolSpecies variable used by\n    // zen-observable), so we should set it as well, to be safe.\n    set(\"@@species\");\n    return subclass;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,qBAAqB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,QAAQ,EAAE;EAC5C,SAASC,GAAGA,CAACC,GAAG,EAAE;IACd;IACA;IACA;IACAC,MAAM,CAACC,cAAc,CAACJ,QAAQ,EAAEE,GAAG,EAAE;MAAEG,KAAK,EAAER;IAAW,CAAC,CAAC;EAC/D;EACA,IAAIC,YAAY,IAAIQ,MAAM,CAACC,OAAO,EAAE;IAChCN,GAAG,CAACK,MAAM,CAACC,OAAO,CAAC;EACvB;EACA;EACA;EACA;EACAN,GAAG,CAAC,WAAW,CAAC;EAChB,OAAOD,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}