{"ast": null, "code": "import { invariant } from '../jsutils/invariant.mjs';\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || invariant(false);\n    if (match.index >= position) {\n      break;\n    }\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n  return {\n    line,\n    column: position + 1 - lastLineStart\n  };\n}", "map": {"version": 3, "names": ["invariant", "LineRegExp", "getLocation", "source", "position", "lastLineStart", "line", "match", "body", "matchAll", "index", "length", "column"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/language/location.mjs"], "sourcesContent": ["import { invariant } from '../jsutils/invariant.mjs';\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || invariant(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,MAAMC,UAAU,GAAG,cAAc;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,IAAI,GAAG,CAAC;EAEZ,KAAK,MAAMC,KAAK,IAAIJ,MAAM,CAACK,IAAI,CAACC,QAAQ,CAACR,UAAU,CAAC,EAAE;IACpD,OAAOM,KAAK,CAACG,KAAK,KAAK,QAAQ,IAAIV,SAAS,CAAC,KAAK,CAAC;IAEnD,IAAIO,KAAK,CAACG,KAAK,IAAIN,QAAQ,EAAE;MAC3B;IACF;IAEAC,aAAa,GAAGE,KAAK,CAACG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM;IAC7CL,IAAI,IAAI,CAAC;EACX;EAEA,OAAO;IACLA,IAAI;IACJM,MAAM,EAAER,QAAQ,GAAG,CAAC,GAAGC;EACzB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}