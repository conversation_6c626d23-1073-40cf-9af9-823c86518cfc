{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant, newInvariantError } from \"../globals/index.js\";\nimport { BREAK, visit } from \"graphql\";\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(document, fragmentName) {\n  var actualFragmentName = fragmentName;\n  // Build an array of all our fragment definitions that will be used for\n  // validations. We also do some validations on the other definitions in the\n  // document while building this list.\n  var fragments = [];\n  document.definitions.forEach(function (definition) {\n    // Throw an error if we encounter an operation definition because we will\n    // define our own operation definition later on.\n    if (definition.kind === \"OperationDefinition\") {\n      throw newInvariantError(85, definition.operation, definition.name ? \" named '\".concat(definition.name.value, \"'\") : \"\");\n    }\n    // Add our definition to the fragments array if it is a fragment\n    // definition.\n    if (definition.kind === \"FragmentDefinition\") {\n      fragments.push(definition);\n    }\n  });\n  // If the user did not give us a fragment name then let us try to get a\n  // name from a single fragment in the definition.\n  if (typeof actualFragmentName === \"undefined\") {\n    invariant(fragments.length === 1, 86, fragments.length);\n    actualFragmentName = fragments[0].name.value;\n  }\n  // Generate a query document with an operation that simply spreads the\n  // fragment inside of it.\n  var query = __assign(__assign({}, document), {\n    definitions: __spreadArray([{\n      kind: \"OperationDefinition\",\n      // OperationTypeNode is an enum\n      operation: \"query\",\n      selectionSet: {\n        kind: \"SelectionSet\",\n        selections: [{\n          kind: \"FragmentSpread\",\n          name: {\n            kind: \"Name\",\n            value: actualFragmentName\n          }\n        }]\n      }\n    }], document.definitions, true)\n  });\n  return query;\n}\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(fragments) {\n  if (fragments === void 0) {\n    fragments = [];\n  }\n  var symTable = {};\n  fragments.forEach(function (fragment) {\n    symTable[fragment.name.value] = fragment;\n  });\n  return symTable;\n}\nexport function getFragmentFromSelection(selection, fragmentMap) {\n  switch (selection.kind) {\n    case \"InlineFragment\":\n      return selection;\n    case \"FragmentSpread\":\n      {\n        var fragmentName = selection.name.value;\n        if (typeof fragmentMap === \"function\") {\n          return fragmentMap(fragmentName);\n        }\n        var fragment = fragmentMap && fragmentMap[fragmentName];\n        invariant(fragment, 87, fragmentName);\n        return fragment || null;\n      }\n    default:\n      return null;\n  }\n}\nexport function isFullyUnmaskedOperation(document) {\n  var isUnmasked = true;\n  visit(document, {\n    FragmentSpread: function (node) {\n      isUnmasked = !!node.directives && node.directives.some(function (directive) {\n        return directive.name.value === \"unmask\";\n      });\n      if (!isUnmasked) {\n        return BREAK;\n      }\n    }\n  });\n  return isUnmasked;\n}", "map": {"version": 3, "names": ["__assign", "__spread<PERSON><PERSON>y", "invariant", "newInvariantError", "BREAK", "visit", "getFragmentQueryDocument", "document", "fragmentName", "actualFragmentName", "fragments", "definitions", "for<PERSON>ach", "definition", "kind", "operation", "name", "concat", "value", "push", "length", "query", "selectionSet", "selections", "createFragmentMap", "symTable", "fragment", "getFragmentFromSelection", "selection", "fragmentMap", "isFullyUnmaskedOperation", "isUnmasked", "FragmentSpread", "node", "directives", "some", "directive"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/fragments.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { invariant, newInvariantError } from \"../globals/index.js\";\nimport { BREAK, visit } from \"graphql\";\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(document, fragmentName) {\n    var actualFragmentName = fragmentName;\n    // Build an array of all our fragment definitions that will be used for\n    // validations. We also do some validations on the other definitions in the\n    // document while building this list.\n    var fragments = [];\n    document.definitions.forEach(function (definition) {\n        // Throw an error if we encounter an operation definition because we will\n        // define our own operation definition later on.\n        if (definition.kind === \"OperationDefinition\") {\n            throw newInvariantError(\n                85,\n                definition.operation,\n                definition.name ? \" named '\".concat(definition.name.value, \"'\") : \"\"\n            );\n        }\n        // Add our definition to the fragments array if it is a fragment\n        // definition.\n        if (definition.kind === \"FragmentDefinition\") {\n            fragments.push(definition);\n        }\n    });\n    // If the user did not give us a fragment name then let us try to get a\n    // name from a single fragment in the definition.\n    if (typeof actualFragmentName === \"undefined\") {\n        invariant(fragments.length === 1, 86, fragments.length);\n        actualFragmentName = fragments[0].name.value;\n    }\n    // Generate a query document with an operation that simply spreads the\n    // fragment inside of it.\n    var query = __assign(__assign({}, document), { definitions: __spreadArray([\n            {\n                kind: \"OperationDefinition\",\n                // OperationTypeNode is an enum\n                operation: \"query\",\n                selectionSet: {\n                    kind: \"SelectionSet\",\n                    selections: [\n                        {\n                            kind: \"FragmentSpread\",\n                            name: {\n                                kind: \"Name\",\n                                value: actualFragmentName,\n                            },\n                        },\n                    ],\n                },\n            }\n        ], document.definitions, true) });\n    return query;\n}\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(fragments) {\n    if (fragments === void 0) { fragments = []; }\n    var symTable = {};\n    fragments.forEach(function (fragment) {\n        symTable[fragment.name.value] = fragment;\n    });\n    return symTable;\n}\nexport function getFragmentFromSelection(selection, fragmentMap) {\n    switch (selection.kind) {\n        case \"InlineFragment\":\n            return selection;\n        case \"FragmentSpread\": {\n            var fragmentName = selection.name.value;\n            if (typeof fragmentMap === \"function\") {\n                return fragmentMap(fragmentName);\n            }\n            var fragment = fragmentMap && fragmentMap[fragmentName];\n            invariant(fragment, 87, fragmentName);\n            return fragment || null;\n        }\n        default:\n            return null;\n    }\n}\nexport function isFullyUnmaskedOperation(document) {\n    var isUnmasked = true;\n    visit(document, {\n        FragmentSpread: function (node) {\n            isUnmasked =\n                !!node.directives &&\n                    node.directives.some(function (directive) { return directive.name.value === \"unmask\"; });\n            if (!isUnmasked) {\n                return BREAK;\n            }\n        },\n    });\n    return isUnmasked;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AAC/C,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,qBAAqB;AAClE,SAASC,KAAK,EAAEC,KAAK,QAAQ,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC7D,IAAIC,kBAAkB,GAAGD,YAAY;EACrC;EACA;EACA;EACA,IAAIE,SAAS,GAAG,EAAE;EAClBH,QAAQ,CAACI,WAAW,CAACC,OAAO,CAAC,UAAUC,UAAU,EAAE;IAC/C;IACA;IACA,IAAIA,UAAU,CAACC,IAAI,KAAK,qBAAqB,EAAE;MAC3C,MAAMX,iBAAiB,CACnB,EAAE,EACFU,UAAU,CAACE,SAAS,EACpBF,UAAU,CAACG,IAAI,GAAG,UAAU,CAACC,MAAM,CAACJ,UAAU,CAACG,IAAI,CAACE,KAAK,EAAE,GAAG,CAAC,GAAG,EACtE,CAAC;IACL;IACA;IACA;IACA,IAAIL,UAAU,CAACC,IAAI,KAAK,oBAAoB,EAAE;MAC1CJ,SAAS,CAACS,IAAI,CAACN,UAAU,CAAC;IAC9B;EACJ,CAAC,CAAC;EACF;EACA;EACA,IAAI,OAAOJ,kBAAkB,KAAK,WAAW,EAAE;IAC3CP,SAAS,CAACQ,SAAS,CAACU,MAAM,KAAK,CAAC,EAAE,EAAE,EAAEV,SAAS,CAACU,MAAM,CAAC;IACvDX,kBAAkB,GAAGC,SAAS,CAAC,CAAC,CAAC,CAACM,IAAI,CAACE,KAAK;EAChD;EACA;EACA;EACA,IAAIG,KAAK,GAAGrB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,QAAQ,CAAC,EAAE;IAAEI,WAAW,EAAEV,aAAa,CAAC,CAClE;MACIa,IAAI,EAAE,qBAAqB;MAC3B;MACAC,SAAS,EAAE,OAAO;MAClBO,YAAY,EAAE;QACVR,IAAI,EAAE,cAAc;QACpBS,UAAU,EAAE,CACR;UACIT,IAAI,EAAE,gBAAgB;UACtBE,IAAI,EAAE;YACFF,IAAI,EAAE,MAAM;YACZI,KAAK,EAAET;UACX;QACJ,CAAC;MAET;IACJ,CAAC,CACJ,EAAEF,QAAQ,CAACI,WAAW,EAAE,IAAI;EAAE,CAAC,CAAC;EACrC,OAAOU,KAAK;AAChB;AACA;AACA;AACA,OAAO,SAASG,iBAAiBA,CAACd,SAAS,EAAE;EACzC,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IAAEA,SAAS,GAAG,EAAE;EAAE;EAC5C,IAAIe,QAAQ,GAAG,CAAC,CAAC;EACjBf,SAAS,CAACE,OAAO,CAAC,UAAUc,QAAQ,EAAE;IAClCD,QAAQ,CAACC,QAAQ,CAACV,IAAI,CAACE,KAAK,CAAC,GAAGQ,QAAQ;EAC5C,CAAC,CAAC;EACF,OAAOD,QAAQ;AACnB;AACA,OAAO,SAASE,wBAAwBA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC7D,QAAQD,SAAS,CAACd,IAAI;IAClB,KAAK,gBAAgB;MACjB,OAAOc,SAAS;IACpB,KAAK,gBAAgB;MAAE;QACnB,IAAIpB,YAAY,GAAGoB,SAAS,CAACZ,IAAI,CAACE,KAAK;QACvC,IAAI,OAAOW,WAAW,KAAK,UAAU,EAAE;UACnC,OAAOA,WAAW,CAACrB,YAAY,CAAC;QACpC;QACA,IAAIkB,QAAQ,GAAGG,WAAW,IAAIA,WAAW,CAACrB,YAAY,CAAC;QACvDN,SAAS,CAACwB,QAAQ,EAAE,EAAE,EAAElB,YAAY,CAAC;QACrC,OAAOkB,QAAQ,IAAI,IAAI;MAC3B;IACA;MACI,OAAO,IAAI;EACnB;AACJ;AACA,OAAO,SAASI,wBAAwBA,CAACvB,QAAQ,EAAE;EAC/C,IAAIwB,UAAU,GAAG,IAAI;EACrB1B,KAAK,CAACE,QAAQ,EAAE;IACZyB,cAAc,EAAE,SAAAA,CAAUC,IAAI,EAAE;MAC5BF,UAAU,GACN,CAAC,CAACE,IAAI,CAACC,UAAU,IACbD,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,UAAUC,SAAS,EAAE;QAAE,OAAOA,SAAS,CAACpB,IAAI,CAACE,KAAK,KAAK,QAAQ;MAAE,CAAC,CAAC;MAChG,IAAI,CAACa,UAAU,EAAE;QACb,OAAO3B,KAAK;MAChB;IACJ;EACJ,CAAC,CAAC;EACF,OAAO2B,UAAU;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}