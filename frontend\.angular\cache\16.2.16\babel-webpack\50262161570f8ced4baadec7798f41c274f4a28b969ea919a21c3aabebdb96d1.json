{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { compact } from \"./compact.js\";\nexport function mergeOptions(defaults, options) {\n  return compact(defaults, options, options.variables && {\n    variables: compact(__assign(__assign({}, defaults && defaults.variables), options.variables))\n  });\n}", "map": {"version": 3, "names": ["__assign", "compact", "mergeOptions", "defaults", "options", "variables"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/mergeOptions.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { compact } from \"./compact.js\";\nexport function mergeOptions(defaults, options) {\n    return compact(defaults, options, options.variables && {\n        variables: compact(__assign(__assign({}, (defaults && defaults.variables)), options.variables)),\n    });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC5C,OAAOH,OAAO,CAACE,QAAQ,EAAEC,OAAO,EAAEA,OAAO,CAACC,SAAS,IAAI;IACnDA,SAAS,EAAEJ,OAAO,CAACD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAGG,QAAQ,IAAIA,QAAQ,CAACE,SAAU,CAAC,EAAED,OAAO,CAACC,SAAS,CAAC;EAClG,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}