{"ast": null, "code": "import { is<PERSON><PERSON><PERSON>nce, is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, resultKeyNameFromField, shouldInclude, isNonNullObject, compact, createFragmentMap, getFragmentDefinitions, isArray } from \"../../utilities/index.js\";\nexport var hasOwn = Object.prototype.hasOwnProperty;\nexport function isNullish(value) {\n  return value === null || value === void 0;\n}\nexport { isArray };\nexport function defaultDataIdFromObject(_a, context) {\n  var __typename = _a.__typename,\n    id = _a.id,\n    _id = _a._id;\n  if (typeof __typename === \"string\") {\n    if (context) {\n      context.keyObject = !isNullish(id) ? {\n        id: id\n      } : !isNullish(_id) ? {\n        _id: _id\n      } : void 0;\n    }\n    // If there is no object.id, fall back to object._id.\n    if (isNullish(id) && !isNullish(_id)) {\n      id = _id;\n    }\n    if (!isNullish(id)) {\n      return \"\".concat(__typename, \":\").concat(typeof id === \"number\" || typeof id === \"string\" ? id : JSON.stringify(id));\n    }\n  }\n}\nvar defaultConfig = {\n  dataIdFromObject: defaultDataIdFromObject,\n  addTypename: true,\n  resultCaching: true,\n  // Thanks to the shouldCanonizeResults helper, this should be the only line\n  // you have to change to reenable canonization by default in the future.\n  canonizeResults: false\n};\nexport function normalizeConfig(config) {\n  return compact(defaultConfig, config);\n}\nexport function shouldCanonizeResults(config) {\n  var value = config.canonizeResults;\n  return value === void 0 ? defaultConfig.canonizeResults : value;\n}\nexport function getTypenameFromStoreObject(store, objectOrReference) {\n  return isReference(objectOrReference) ? store.get(objectOrReference.__ref, \"__typename\") : objectOrReference && objectOrReference.__typename;\n}\nexport var TypeOrFieldNameRegExp = /^[_a-z][_0-9a-z]*/i;\nexport function fieldNameFromStoreName(storeFieldName) {\n  var match = storeFieldName.match(TypeOrFieldNameRegExp);\n  return match ? match[0] : storeFieldName;\n}\nexport function selectionSetMatchesResult(selectionSet, result, variables) {\n  if (isNonNullObject(result)) {\n    return isArray(result) ? result.every(function (item) {\n      return selectionSetMatchesResult(selectionSet, item, variables);\n    }) : selectionSet.selections.every(function (field) {\n      if (isField(field) && shouldInclude(field, variables)) {\n        var key = resultKeyNameFromField(field);\n        return hasOwn.call(result, key) && (!field.selectionSet || selectionSetMatchesResult(field.selectionSet, result[key], variables));\n      }\n      // If the selection has been skipped with @skip(true) or\n      // @include(false), it should not count against the matching. If\n      // the selection is not a field, it must be a fragment (inline or\n      // named). We will determine if selectionSetMatchesResult for that\n      // fragment when we get to it, so for now we return true.\n      return true;\n    });\n  }\n  return false;\n}\nexport function storeValueIsStoreObject(value) {\n  return isNonNullObject(value) && !isReference(value) && !isArray(value);\n}\nexport function makeProcessedFieldsMerger() {\n  return new DeepMerger();\n}\nexport function extractFragmentContext(document, fragments) {\n  // FragmentMap consisting only of fragments defined directly in document, not\n  // including other fragments registered in the FragmentRegistry.\n  var fragmentMap = createFragmentMap(getFragmentDefinitions(document));\n  return {\n    fragmentMap: fragmentMap,\n    lookupFragment: function (name) {\n      var def = fragmentMap[name];\n      if (!def && fragments) {\n        def = fragments.lookup(name);\n      }\n      return def || null;\n    }\n  };\n}", "map": {"version": 3, "names": ["isReference", "isField", "DeepMerger", "resultKeyNameFromField", "shouldInclude", "isNonNullObject", "compact", "createFragmentMap", "getFragmentDefinitions", "isArray", "hasOwn", "Object", "prototype", "hasOwnProperty", "<PERSON><PERSON><PERSON><PERSON>", "value", "defaultDataIdFromObject", "_a", "context", "__typename", "id", "_id", "keyObject", "concat", "JSON", "stringify", "defaultConfig", "dataIdFromObject", "addTypename", "resultCaching", "canon<PERSON><PERSON><PERSON><PERSON><PERSON>", "normalizeConfig", "config", "shouldCanonizeResults", "getTypenameFromStoreObject", "store", "objectOrReference", "get", "__ref", "TypeOrFieldNameRegExp", "fieldNameFromStoreName", "storeFieldName", "match", "selectionSetMatchesResult", "selectionSet", "result", "variables", "every", "item", "selections", "field", "key", "call", "storeValueIsStoreObject", "makeProcessedFieldsMerger", "extractFragmentContext", "document", "fragments", "fragmentMap", "lookupFragment", "name", "def", "lookup"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/helpers.js"], "sourcesContent": ["import { is<PERSON><PERSON><PERSON>nce, is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, resultKeyNameFromField, shouldInclude, isNonNullObject, compact, createFragmentMap, getFragmentDefinitions, isArray, } from \"../../utilities/index.js\";\nexport var hasOwn = Object.prototype.hasOwnProperty;\nexport function isNullish(value) {\n    return value === null || value === void 0;\n}\nexport { isArray };\nexport function defaultDataIdFromObject(_a, context) {\n    var __typename = _a.__typename, id = _a.id, _id = _a._id;\n    if (typeof __typename === \"string\") {\n        if (context) {\n            context.keyObject =\n                !isNullish(id) ? { id: id }\n                    : !isNullish(_id) ? { _id: _id }\n                        : void 0;\n        }\n        // If there is no object.id, fall back to object._id.\n        if (isNullish(id) && !isNullish(_id)) {\n            id = _id;\n        }\n        if (!isNullish(id)) {\n            return \"\".concat(__typename, \":\").concat(typeof id === \"number\" || typeof id === \"string\" ?\n                id\n                : JSON.stringify(id));\n        }\n    }\n}\nvar defaultConfig = {\n    dataIdFromObject: defaultDataIdFromObject,\n    addTypename: true,\n    resultCaching: true,\n    // Thanks to the shouldCanonizeResults helper, this should be the only line\n    // you have to change to reenable canonization by default in the future.\n    canonizeResults: false,\n};\nexport function normalizeConfig(config) {\n    return compact(defaultConfig, config);\n}\nexport function shouldCanonizeResults(config) {\n    var value = config.canonizeResults;\n    return value === void 0 ? defaultConfig.canonizeResults : value;\n}\nexport function getTypenameFromStoreObject(store, objectOrReference) {\n    return isReference(objectOrReference) ?\n        store.get(objectOrReference.__ref, \"__typename\")\n        : objectOrReference && objectOrReference.__typename;\n}\nexport var TypeOrFieldNameRegExp = /^[_a-z][_0-9a-z]*/i;\nexport function fieldNameFromStoreName(storeFieldName) {\n    var match = storeFieldName.match(TypeOrFieldNameRegExp);\n    return match ? match[0] : storeFieldName;\n}\nexport function selectionSetMatchesResult(selectionSet, result, variables) {\n    if (isNonNullObject(result)) {\n        return isArray(result) ?\n            result.every(function (item) {\n                return selectionSetMatchesResult(selectionSet, item, variables);\n            })\n            : selectionSet.selections.every(function (field) {\n                if (isField(field) && shouldInclude(field, variables)) {\n                    var key = resultKeyNameFromField(field);\n                    return (hasOwn.call(result, key) &&\n                        (!field.selectionSet ||\n                            selectionSetMatchesResult(field.selectionSet, result[key], variables)));\n                }\n                // If the selection has been skipped with @skip(true) or\n                // @include(false), it should not count against the matching. If\n                // the selection is not a field, it must be a fragment (inline or\n                // named). We will determine if selectionSetMatchesResult for that\n                // fragment when we get to it, so for now we return true.\n                return true;\n            });\n    }\n    return false;\n}\nexport function storeValueIsStoreObject(value) {\n    return isNonNullObject(value) && !isReference(value) && !isArray(value);\n}\nexport function makeProcessedFieldsMerger() {\n    return new DeepMerger();\n}\nexport function extractFragmentContext(document, fragments) {\n    // FragmentMap consisting only of fragments defined directly in document, not\n    // including other fragments registered in the FragmentRegistry.\n    var fragmentMap = createFragmentMap(getFragmentDefinitions(document));\n    return {\n        fragmentMap: fragmentMap,\n        lookupFragment: function (name) {\n            var def = fragmentMap[name];\n            if (!def && fragments) {\n                def = fragments.lookup(name);\n            }\n            return def || null;\n        },\n    };\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,OAAO,EAAEC,UAAU,EAAEC,sBAAsB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,OAAO,QAAS,0BAA0B;AACjM,OAAO,IAAIC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AACnD,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC;AAC7C;AACA,SAASN,OAAO;AAChB,OAAO,SAASO,uBAAuBA,CAACC,EAAE,EAAEC,OAAO,EAAE;EACjD,IAAIC,UAAU,GAAGF,EAAE,CAACE,UAAU;IAAEC,EAAE,GAAGH,EAAE,CAACG,EAAE;IAAEC,GAAG,GAAGJ,EAAE,CAACI,GAAG;EACxD,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;IAChC,IAAID,OAAO,EAAE;MACTA,OAAO,CAACI,SAAS,GACb,CAACR,SAAS,CAACM,EAAE,CAAC,GAAG;QAAEA,EAAE,EAAEA;MAAG,CAAC,GACrB,CAACN,SAAS,CAACO,GAAG,CAAC,GAAG;QAAEA,GAAG,EAAEA;MAAI,CAAC,GAC1B,KAAK,CAAC;IACxB;IACA;IACA,IAAIP,SAAS,CAACM,EAAE,CAAC,IAAI,CAACN,SAAS,CAACO,GAAG,CAAC,EAAE;MAClCD,EAAE,GAAGC,GAAG;IACZ;IACA,IAAI,CAACP,SAAS,CAACM,EAAE,CAAC,EAAE;MAChB,OAAO,EAAE,CAACG,MAAM,CAACJ,UAAU,EAAE,GAAG,CAAC,CAACI,MAAM,CAAC,OAAOH,EAAE,KAAK,QAAQ,IAAI,OAAOA,EAAE,KAAK,QAAQ,GACrFA,EAAE,GACAI,IAAI,CAACC,SAAS,CAACL,EAAE,CAAC,CAAC;IAC7B;EACJ;AACJ;AACA,IAAIM,aAAa,GAAG;EAChBC,gBAAgB,EAAEX,uBAAuB;EACzCY,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,IAAI;EACnB;EACA;EACAC,eAAe,EAAE;AACrB,CAAC;AACD,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACpC,OAAO1B,OAAO,CAACoB,aAAa,EAAEM,MAAM,CAAC;AACzC;AACA,OAAO,SAASC,qBAAqBA,CAACD,MAAM,EAAE;EAC1C,IAAIjB,KAAK,GAAGiB,MAAM,CAACF,eAAe;EAClC,OAAOf,KAAK,KAAK,KAAK,CAAC,GAAGW,aAAa,CAACI,eAAe,GAAGf,KAAK;AACnE;AACA,OAAO,SAASmB,0BAA0BA,CAACC,KAAK,EAAEC,iBAAiB,EAAE;EACjE,OAAOpC,WAAW,CAACoC,iBAAiB,CAAC,GACjCD,KAAK,CAACE,GAAG,CAACD,iBAAiB,CAACE,KAAK,EAAE,YAAY,CAAC,GAC9CF,iBAAiB,IAAIA,iBAAiB,CAACjB,UAAU;AAC3D;AACA,OAAO,IAAIoB,qBAAqB,GAAG,oBAAoB;AACvD,OAAO,SAASC,sBAAsBA,CAACC,cAAc,EAAE;EACnD,IAAIC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAACH,qBAAqB,CAAC;EACvD,OAAOG,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,cAAc;AAC5C;AACA,OAAO,SAASE,yBAAyBA,CAACC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAE;EACvE,IAAIzC,eAAe,CAACwC,MAAM,CAAC,EAAE;IACzB,OAAOpC,OAAO,CAACoC,MAAM,CAAC,GAClBA,MAAM,CAACE,KAAK,CAAC,UAAUC,IAAI,EAAE;MACzB,OAAOL,yBAAyB,CAACC,YAAY,EAAEI,IAAI,EAAEF,SAAS,CAAC;IACnE,CAAC,CAAC,GACAF,YAAY,CAACK,UAAU,CAACF,KAAK,CAAC,UAAUG,KAAK,EAAE;MAC7C,IAAIjD,OAAO,CAACiD,KAAK,CAAC,IAAI9C,aAAa,CAAC8C,KAAK,EAAEJ,SAAS,CAAC,EAAE;QACnD,IAAIK,GAAG,GAAGhD,sBAAsB,CAAC+C,KAAK,CAAC;QACvC,OAAQxC,MAAM,CAAC0C,IAAI,CAACP,MAAM,EAAEM,GAAG,CAAC,KAC3B,CAACD,KAAK,CAACN,YAAY,IAChBD,yBAAyB,CAACO,KAAK,CAACN,YAAY,EAAEC,MAAM,CAACM,GAAG,CAAC,EAAEL,SAAS,CAAC,CAAC;MAClF;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI;IACf,CAAC,CAAC;EACV;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASO,uBAAuBA,CAACtC,KAAK,EAAE;EAC3C,OAAOV,eAAe,CAACU,KAAK,CAAC,IAAI,CAACf,WAAW,CAACe,KAAK,CAAC,IAAI,CAACN,OAAO,CAACM,KAAK,CAAC;AAC3E;AACA,OAAO,SAASuC,yBAAyBA,CAAA,EAAG;EACxC,OAAO,IAAIpD,UAAU,CAAC,CAAC;AAC3B;AACA,OAAO,SAASqD,sBAAsBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACxD;EACA;EACA,IAAIC,WAAW,GAAGnD,iBAAiB,CAACC,sBAAsB,CAACgD,QAAQ,CAAC,CAAC;EACrE,OAAO;IACHE,WAAW,EAAEA,WAAW;IACxBC,cAAc,EAAE,SAAAA,CAAUC,IAAI,EAAE;MAC5B,IAAIC,GAAG,GAAGH,WAAW,CAACE,IAAI,CAAC;MAC3B,IAAI,CAACC,GAAG,IAAIJ,SAAS,EAAE;QACnBI,GAAG,GAAGJ,SAAS,CAACK,MAAM,CAACF,IAAI,CAAC;MAChC;MACA,OAAOC,GAAG,IAAI,IAAI;IACtB;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}