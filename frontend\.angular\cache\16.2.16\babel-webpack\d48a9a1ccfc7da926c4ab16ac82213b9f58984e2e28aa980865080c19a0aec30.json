{"ast": null, "code": "/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind;\n(function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n})(TokenKind || (TokenKind = {}));\nexport { TokenKind };\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */", "map": {"version": 3, "names": ["TokenKind"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/language/tokenKind.mjs"], "sourcesContent": ["/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind;\n\n(function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n})(TokenKind || (TokenKind = {}));\n\nexport { TokenKind };\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,IAAIA,SAAS;AAEb,CAAC,UAAUA,SAAS,EAAE;EACpBA,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO;EAC1BA,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO;EAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG;EACvBA,SAAS,CAAC,QAAQ,CAAC,GAAG,GAAG;EACzBA,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG;EACtBA,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG;EAC1BA,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG;EAC1BA,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK;EAC3BA,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG;EACxBA,SAAS,CAAC,QAAQ,CAAC,GAAG,GAAG;EACzBA,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG;EACrBA,SAAS,CAAC,WAAW,CAAC,GAAG,GAAG;EAC5BA,SAAS,CAAC,WAAW,CAAC,GAAG,GAAG;EAC5BA,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG;EAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG;EACvBA,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG;EAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1BA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK;EACxBA,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;EAC5BA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,cAAc,CAAC,GAAG,aAAa;EACzCA,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS;AAClC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjC,SAASA,SAAS;AAClB;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}