{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique directive names\n *\n * A GraphQL document is only valid if all defined directives have unique names.\n */\nexport function UniqueDirectiveNamesRule(context) {\n  const knownDirectiveNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    DirectiveDefinition(node) {\n      const directiveName = node.name.value;\n      if (schema !== null && schema !== void 0 && schema.getDirective(directiveName)) {\n        context.reportError(new GraphQLError(`Directive \"@${directiveName}\" already exists in the schema. It cannot be redefined.`, {\n          nodes: node.name\n        }));\n        return;\n      }\n      if (knownDirectiveNames[directiveName]) {\n        context.reportError(new GraphQLError(`There can be only one directive named \"@${directiveName}\".`, {\n          nodes: [knownDirectiveNames[directiveName], node.name]\n        }));\n      } else {\n        knownDirectiveNames[directiveName] = node.name;\n      }\n      return false;\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "UniqueDirectiveNamesRule", "context", "knownDirectiveNames", "Object", "create", "schema", "getSchema", "DirectiveDefinition", "node", "directiveName", "name", "value", "getDirective", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/UniqueDirectiveNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique directive names\n *\n * A GraphQL document is only valid if all defined directives have unique names.\n */\nexport function UniqueDirectiveNamesRule(context) {\n  const knownDirectiveNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    DirectiveDefinition(node) {\n      const directiveName = node.name.value;\n\n      if (\n        schema !== null &&\n        schema !== void 0 &&\n        schema.getDirective(directiveName)\n      ) {\n        context.reportError(\n          new GraphQLError(\n            `Directive \"@${directiveName}\" already exists in the schema. It cannot be redefined.`,\n            {\n              nodes: node.name,\n            },\n          ),\n        );\n        return;\n      }\n\n      if (knownDirectiveNames[directiveName]) {\n        context.reportError(\n          new GraphQLError(\n            `There can be only one directive named \"@${directiveName}\".`,\n            {\n              nodes: [knownDirectiveNames[directiveName], node.name],\n            },\n          ),\n        );\n      } else {\n        knownDirectiveNames[directiveName] = node.name;\n      }\n\n      return false;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC/C,MAAMC,MAAM,GAAGJ,OAAO,CAACK,SAAS,CAAC,CAAC;EAClC,OAAO;IACLC,mBAAmBA,CAACC,IAAI,EAAE;MACxB,MAAMC,aAAa,GAAGD,IAAI,CAACE,IAAI,CAACC,KAAK;MAErC,IACEN,MAAM,KAAK,IAAI,IACfA,MAAM,KAAK,KAAK,CAAC,IACjBA,MAAM,CAACO,YAAY,CAACH,aAAa,CAAC,EAClC;QACAR,OAAO,CAACY,WAAW,CACjB,IAAId,YAAY,CACb,eAAcU,aAAc,yDAAwD,EACrF;UACEK,KAAK,EAAEN,IAAI,CAACE;QACd,CACF,CACF,CAAC;QACD;MACF;MAEA,IAAIR,mBAAmB,CAACO,aAAa,CAAC,EAAE;QACtCR,OAAO,CAACY,WAAW,CACjB,IAAId,YAAY,CACb,2CAA0CU,aAAc,IAAG,EAC5D;UACEK,KAAK,EAAE,CAACZ,mBAAmB,CAACO,aAAa,CAAC,EAAED,IAAI,CAACE,IAAI;QACvD,CACF,CACF,CAAC;MACH,CAAC,MAAM;QACLR,mBAAmB,CAACO,aAAa,CAAC,GAAGD,IAAI,CAACE,IAAI;MAChD;MAEA,OAAO,KAAK;IACd;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}