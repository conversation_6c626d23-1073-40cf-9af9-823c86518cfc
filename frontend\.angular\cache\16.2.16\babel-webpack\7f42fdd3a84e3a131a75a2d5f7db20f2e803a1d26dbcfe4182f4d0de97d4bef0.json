{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/rendus.service\";\nimport * as i3 from \"@angular/common\";\nfunction EvaluationDetailsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EvaluationDetailsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Structure du code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.rendu.evaluation.scores.structure, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.rendu.evaluation.scores.structure / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.rendu.evaluation.scores.pratiques, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.rendu.evaluation.scores.pratiques / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Fonctionnalit\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.rendu.evaluation.scores.fonctionnalite, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r5.rendu.evaluation.scores.fonctionnalite / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r6.rendu.evaluation.scores.originalite, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r6.rendu.evaluation.scores.originalite / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h3\", 48);\n    i0.ɵɵtext(2, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"p\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r7.rendu.evaluation.commentaires);\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_41_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 52);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 53);\n    i0.ɵɵelement(2, \"path\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"href\", ctx_r10.getFileUrl(fichier_r11), i0.ɵɵsanitizeUrl)(\"download\", ctx_r10.getFileName(fichier_r11));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r10.getFileName(fichier_r11));\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h3\", 48);\n    i0.ɵɵtext(2, \"Fichiers soumis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵtemplate(4, EvaluationDetailsComponent_div_10_div_41_a_4_Template, 5, 3, \"a\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.rendu.fichiers);\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u00C9valu\\u00E9 le \", i0.ɵɵpipeBind2(2, 1, ctx_r9.rendu.evaluation.dateEvaluation, \"dd/MM/yyyy \\u00E0 HH:mm\"), \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"bg-green-600\": a0,\n    \"bg-blue-600\": a1,\n    \"bg-yellow-600\": a2,\n    \"bg-red-600\": a3\n  };\n};\nfunction EvaluationDetailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"div\")(4, \"h2\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 17);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"span\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"div\", 21);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 22)(16, \"p\", 23);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 24);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 25)(21, \"span\", 26);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 27)(24, \"h3\", 28);\n    i0.ɵɵtext(25, \"D\\u00E9tails des scores\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 29)(27, \"div\", 30);\n    i0.ɵɵtemplate(28, EvaluationDetailsComponent_div_10_div_28_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(29, EvaluationDetailsComponent_div_10_div_29_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(30, EvaluationDetailsComponent_div_10_div_30_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(31, EvaluationDetailsComponent_div_10_div_31_Template, 8, 3, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"div\", 33)(34, \"span\", 34);\n    i0.ɵɵtext(35, \"Score total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 35);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 36);\n    i0.ɵɵelement(39, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(40, EvaluationDetailsComponent_div_10_div_40_Template, 6, 1, \"div\", 38);\n    i0.ɵɵtemplate(41, EvaluationDetailsComponent_div_10_div_41_Template, 5, 1, \"div\", 38);\n    i0.ɵɵtemplate(42, EvaluationDetailsComponent_div_10_div_42_Template, 3, 4, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.projet == null ? null : ctx_r2.rendu.projet.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Soumis le \", i0.ɵɵpipeBind2(8, 24, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy \\u00E0 HH:mm\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.getScoreTotal(), \"/\", ctx_r2.getScoreMaximum(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom == null ? null : ctx_r2.rendu.etudiant.nom.charAt(0), \"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom == null ? null : ctx_r2.rendu.etudiant.prenom.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.groupe) || \"Groupe non sp\\u00E9cifi\\u00E9\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getScoreTotal(), \"/\", ctx_r2.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.getScorePercentage(), \"%\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(27, _c0, ctx_r2.getScorePercentage() >= 80, ctx_r2.getScorePercentage() >= 60 && ctx_r2.getScorePercentage() < 80, ctx_r2.getScorePercentage() >= 40 && ctx_r2.getScorePercentage() < 60, ctx_r2.getScorePercentage() < 40));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.commentaires);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.dateEvaluation);\n  }\n}\nexport class EvaluationDetailsComponent {\n  constructor(route, router, rendusService) {\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.error = '';\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    if (!this.rendu?.evaluation?.scores) return 0;\n    const scores = this.rendu.evaluation.scores;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  getScorePercentage() {\n    return this.getScoreTotal() / this.getScoreMaximum() * 100;\n  }\n  getScoreClass() {\n    const percentage = this.getScorePercentage();\n    if (percentage >= 80) return 'text-green-600';\n    if (percentage >= 60) return 'text-blue-600';\n    if (percentage >= 40) return 'text-yellow-600';\n    return 'text-red-600';\n  }\n  retourListe() {\n    this.router.navigate(['/admin/projects/rendus']);\n  }\n  // Méthodes pour gérer les fichiers\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function EvaluationDetailsComponent_Factory(t) {\n      return new (t || EvaluationDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EvaluationDetailsComponent,\n      selectors: [[\"app-evaluation-details\"]],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"flex\", \"items-center\", \"mb-6\"], [1, \"mr-4\", \"p-2\", \"rounded-full\", \"hover:bg-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"text-2xl\", \"md:text-3xl\", \"font-bold\", \"text-[#4f5fad]\"], [\"class\", \"flex justify-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"p-6\", \"border-b\", \"border-gray-200\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"justify-between\", \"items-start\", \"md:items-center\", \"mb-4\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"mt-2\", \"md:mt-0\"], [1, \"text-2xl\", \"font-bold\", 3, \"ngClass\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"h-10\", \"w-10\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-bold\"], [1, \"ml-4\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"ml-auto\"], [1, \"bg-[#f0f4f8]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\"], [1, \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"mb-4\"], [1, \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-6\", \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-base\", \"font-medium\", \"text-gray-700\"], [1, \"text-base\", \"font-bold\", 3, \"ngClass\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-3\"], [1, \"h-3\", \"rounded-full\", 3, \"ngClass\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 text-right\", 4, \"ngIf\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-bold\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-blue-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-green-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-purple-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-yellow-600\", \"h-2.5\", \"rounded-full\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\"], [1, \"text-gray-700\", \"whitespace-pre-line\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-3\"], [\"target\", \"_blank\", \"class\", \"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\", 3, \"href\", \"download\", 4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"p-3\", \"bg-gray-50\", \"rounded-lg\", \"hover:bg-gray-100\", \"transition-colors\", 3, \"href\", \"download\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"text-gray-700\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"text-right\"]],\n      template: function EvaluationDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function EvaluationDetailsComponent_Template_button_click_3_listener() {\n            return ctx.retourListe();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"path\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"h1\", 6);\n          i0.ɵɵtext(7, \"D\\u00E9tails de l'\\u00E9valuation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, EvaluationDetailsComponent_div_8_Template, 2, 0, \"div\", 7);\n          i0.ɵɵtemplate(9, EvaluationDetailsComponent_div_9_Template, 2, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, EvaluationDetailsComponent_div_10_Template, 43, 32, \"div\", 9);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.rendu);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe],\n      styles: [\"\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImV2YWx1YXRpb24tZGV0YWlscy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHNEQUFzRDtBQUN0RDtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxtQkFBbUI7QUFDckIiLCJmaWxlIjoiZXZhbHVhdGlvbi1kZXRhaWxzLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZCfDqXZhbHVhdGlvbiBkZXMgZMOpdGFpbHMgKi9cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufVxyXG5cclxuLmVycm9yLW1lc3NhZ2Uge1xyXG4gIGNvbG9yOiAjZGMzNTQ1O1xyXG4gIG1hcmdpbi10b3A6IDAuMjVyZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZXZhbHVhdGlvbi1kZXRhaWxzL2V2YWx1YXRpb24tZGV0YWlscy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHNEQUFzRDtBQUN0RDtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxtQkFBbUI7QUFDckI7QUFDQSx3cUJBQXdxQiIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkJ8ODwql2YWx1YXRpb24gZGVzIGTDg8KpdGFpbHMgKi9cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufVxyXG5cclxuLmVycm9yLW1lc3NhZ2Uge1xyXG4gIGNvbG9yOiAjZGMzNTQ1O1xyXG4gIG1hcmdpbi10b3A6IDAuMjVyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r3", "rendu", "evaluation", "scores", "structure", "ɵɵstyleProp", "ctx_r4", "pratiques", "ctx_r5", "fonctionnalite", "ctx_r6", "originalite", "ɵɵtextInterpolate", "ctx_r7", "commentaires", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵproperty", "ctx_r10", "getFileUrl", "fichier_r11", "ɵɵsanitizeUrl", "getFileName", "ɵɵtemplate", "EvaluationDetailsComponent_div_10_div_41_a_4_Template", "ctx_r8", "fichiers", "ɵɵpipeBind2", "ctx_r9", "dateEvaluation", "EvaluationDetailsComponent_div_10_div_28_Template", "EvaluationDetailsComponent_div_10_div_29_Template", "EvaluationDetailsComponent_div_10_div_30_Template", "EvaluationDetailsComponent_div_10_div_31_Template", "EvaluationDetailsComponent_div_10_div_40_Template", "EvaluationDetailsComponent_div_10_div_41_Template", "EvaluationDetailsComponent_div_10_div_42_Template", "ctx_r2", "projet", "titre", "dateSoumission", "getScoreClass", "ɵɵtextInterpolate2", "getScoreTotal", "getScoreMaximum", "etudiant", "nom", "char<PERSON>t", "prenom", "email", "groupe", "getScorePercentage", "ɵɵpureFunction4", "_c0", "length", "EvaluationDetailsComponent", "constructor", "route", "router", "rendusService", "renduId", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "percentage", "retourListe", "navigate", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "RendusService", "selectors", "decls", "vars", "consts", "template", "EvaluationDetailsComponent_Template", "rf", "ctx", "ɵɵlistener", "EvaluationDetailsComponent_Template_button_click_3_listener", "EvaluationDetailsComponent_div_8_Template", "EvaluationDetailsComponent_div_9_Template", "EvaluationDetailsComponent_div_10_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluation-details\\evaluation-details.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluation-details\\evaluation-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-evaluation-details',\r\n  templateUrl: './evaluation-details.component.html',\r\n  styleUrls: ['./evaluation-details.component.css']\r\n})\r\nexport class EvaluationDetailsComponent implements OnInit {\r\n  \r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  isLoading: boolean = true;\r\n  error: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    if (!this.rendu?.evaluation?.scores) return 0;\r\n    \r\n    const scores = this.rendu.evaluation.scores;\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  getScorePercentage(): number {\r\n    return (this.getScoreTotal() / this.getScoreMaximum()) * 100;\r\n  }\r\n\r\n  getScoreClass(): string {\r\n    const percentage = this.getScorePercentage();\r\n    if (percentage >= 80) return 'text-green-600';\r\n    if (percentage >= 60) return 'text-blue-600';\r\n    if (percentage >= 40) return 'text-yellow-600';\r\n    return 'text-red-600';\r\n  }\r\n\r\n  retourListe(): void {\r\n    this.router.navigate(['/admin/projects/rendus']);\r\n  }\r\n\r\n  // Méthodes pour gérer les fichiers\r\n  getFileUrl(filePath: string): string {\r\n    if (!filePath) return '';\r\n    \r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n    \r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n    \r\n    // Utiliser la route spécifique pour le téléchargement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'Fichier';\r\n    \r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n    \r\n    return filePath;\r\n  }\r\n}\r\n\r\n", "<div class=\"min-h-screen bg-[#edf1f4] p-4 md:p-6\">\r\n  <div class=\"max-w-4xl mx-auto\">\r\n    <!-- Header avec bouton retour -->\r\n    <div class=\"flex items-center mb-6\">\r\n      <button (click)=\"retourListe()\" class=\"mr-4 p-2 rounded-full hover:bg-gray-200 transition-colors\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-gray-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\r\n        </svg>\r\n      </button>\r\n      <h1 class=\"text-2xl md:text-3xl font-bold text-[#4f5fad]\">Détails de l'évaluation</h1>\r\n    </div>\r\n\r\n    <!-- Loading Spinner -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center py-12\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#4f5fad]\"></div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <!-- Contenu principal -->\r\n    <div *ngIf=\"!isLoading && !error && rendu\" class=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n      <!-- Informations sur le projet et l'étudiant -->\r\n      <div class=\"p-6 border-b border-gray-200\">\r\n        <div class=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-4\">\r\n          <div>\r\n            <h2 class=\"text-xl font-bold text-gray-800\">{{ rendu.projet?.titre }}</h2>\r\n            <p class=\"text-sm text-gray-500\">Soumis le {{ rendu.dateSoumission | date:'dd/MM/yyyy à HH:mm' }}</p>\r\n          </div>\r\n          <div class=\"mt-2 md:mt-0\">\r\n            <span [ngClass]=\"getScoreClass()\" class=\"text-2xl font-bold\">\r\n              {{ getScoreTotal() }}/{{ getScoreMaximum() }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"flex items-center mb-4\">\r\n          <div class=\"h-10 w-10 rounded-full bg-[#6C63FF] flex items-center justify-center text-white font-bold\">\r\n            {{ rendu.etudiant?.nom?.charAt(0) }}{{ rendu.etudiant?.prenom?.charAt(0) }}\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-900\">{{ rendu.etudiant?.nom }} {{ rendu.etudiant?.prenom }}</p>\r\n            <p class=\"text-xs text-gray-500\">{{ rendu.etudiant?.email }}</p>\r\n          </div>\r\n          <div class=\"ml-auto\">\r\n            <span class=\"bg-[#f0f4f8] px-3 py-1 rounded-full text-xs font-medium text-[#4f5fad]\">\r\n              {{ rendu.etudiant?.groupe || 'Groupe non spécifié' }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Détails de l'évaluation -->\r\n      <div class=\"p-6\">\r\n        <h3 class=\"text-lg font-semibold mb-4\">Détails des scores</h3>\r\n        \r\n        <!-- Graphique des scores -->\r\n        <div class=\"mb-6\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div *ngIf=\"rendu.evaluation?.scores\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <span class=\"text-sm font-medium text-gray-700\">Structure du code</span>\r\n                <span class=\"text-sm font-bold\">{{ rendu.evaluation.scores.structure }}/5</span>\r\n              </div>\r\n              <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n                <div class=\"bg-blue-600 h-2.5 rounded-full\" [style.width.%]=\"(rendu.evaluation.scores.structure / 5) * 100\"></div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div *ngIf=\"rendu.evaluation?.scores\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <span class=\"text-sm font-medium text-gray-700\">Bonnes pratiques</span>\r\n                <span class=\"text-sm font-bold\">{{ rendu.evaluation.scores.pratiques }}/5</span>\r\n              </div>\r\n              <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n                <div class=\"bg-green-600 h-2.5 rounded-full\" [style.width.%]=\"(rendu.evaluation.scores.pratiques / 5) * 100\"></div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div *ngIf=\"rendu.evaluation?.scores\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <span class=\"text-sm font-medium text-gray-700\">Fonctionnalités</span>\r\n                <span class=\"text-sm font-bold\">{{ rendu.evaluation.scores.fonctionnalite }}/5</span>\r\n              </div>\r\n              <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n                <div class=\"bg-purple-600 h-2.5 rounded-full\" [style.width.%]=\"(rendu.evaluation.scores.fonctionnalite / 5) * 100\"></div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div *ngIf=\"rendu.evaluation?.scores\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <span class=\"text-sm font-medium text-gray-700\">Originalité</span>\r\n                <span class=\"text-sm font-bold\">{{ rendu.evaluation.scores.originalite }}/5</span>\r\n              </div>\r\n              <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n                <div class=\"bg-yellow-600 h-2.5 rounded-full\" [style.width.%]=\"(rendu.evaluation.scores.originalite / 5) * 100\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- Score total -->\r\n          <div class=\"mt-6 bg-gray-50 p-4 rounded-lg\">\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <span class=\"text-base font-medium text-gray-700\">Score total</span>\r\n              <span class=\"text-base font-bold\" [ngClass]=\"getScoreClass()\">{{ getScoreTotal() }}/{{ getScoreMaximum() }}</span>\r\n            </div>\r\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\r\n              <div [ngClass]=\"{'bg-green-600': getScorePercentage() >= 80, 'bg-blue-600': getScorePercentage() >= 60 && getScorePercentage() < 80, 'bg-yellow-600': getScorePercentage() >= 40 && getScorePercentage() < 60, 'bg-red-600': getScorePercentage() < 40}\" \r\n                   class=\"h-3 rounded-full\" \r\n                   [style.width.%]=\"getScorePercentage()\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- Commentaires -->\r\n        <div *ngIf=\"rendu.evaluation?.commentaires\" class=\"mb-6\">\r\n          <h3 class=\"text-lg font-semibold mb-2\">Commentaires</h3>\r\n          <div class=\"bg-gray-50 p-4 rounded-lg\">\r\n            <p class=\"text-gray-700 whitespace-pre-line\">{{ rendu.evaluation.commentaires }}</p>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- Fichiers soumis - MODIFICATION IMPORTANTE -->\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mb-6\">\r\n          <h3 class=\"text-lg font-semibold mb-2\">Fichiers soumis</h3>\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n            <a *ngFor=\"let fichier of rendu.fichiers\" \r\n               [href]=\"getFileUrl(fichier)\" \r\n               [download]=\"getFileName(fichier)\"\r\n               target=\"_blank\"\r\n               class=\"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n              </svg>\r\n              <span class=\"text-sm text-gray-700 truncate\">{{ getFileName(fichier) }}</span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- Date d'évaluation -->\r\n        <div *ngIf=\"rendu.evaluation?.dateEvaluation\" class=\"text-sm text-gray-500 text-right\">\r\n          Évalué le {{ rendu.evaluation.dateEvaluation | date:'dd/MM/yyyy à HH:mm' }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;ICUtDC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAA8F;IAChGF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAyCQR,EAAA,CAAAC,cAAA,cAAwE;IAEpBD,EAAA,CAAAI,MAAA,wBAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAElFH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAkH;IACpHF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ4BH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,kBAAA,KAAAG,MAAA,CAAAC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAC,SAAA,OAAyC;IAG7Bb,EAAA,CAAAK,SAAA,GAA+D;IAA/DL,EAAA,CAAAc,WAAA,UAAAL,MAAA,CAAAC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAC,SAAA,gBAA+D;;;;;IAI/Gb,EAAA,CAAAC,cAAA,cAAwE;IAEpBD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAElFH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmH;IACrHF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ4BH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAM,kBAAA,KAAAS,MAAA,CAAAL,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAI,SAAA,OAAyC;IAG5BhB,EAAA,CAAAK,SAAA,GAA+D;IAA/DL,EAAA,CAAAc,WAAA,UAAAC,MAAA,CAAAL,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAI,SAAA,gBAA+D;;;;;IAIhHhB,EAAA,CAAAC,cAAA,cAAwE;IAEpBD,EAAA,CAAAI,MAAA,2BAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAA8C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEvFH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAyH;IAC3HF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ4BH,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAM,kBAAA,KAAAW,MAAA,CAAAP,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAM,cAAA,OAA8C;IAGhClB,EAAA,CAAAK,SAAA,GAAoE;IAApEL,EAAA,CAAAc,WAAA,UAAAG,MAAA,CAAAP,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAM,cAAA,gBAAoE;;;;;IAItHlB,EAAA,CAAAC,cAAA,cAAwE;IAEpBD,EAAA,CAAAI,MAAA,uBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEpFH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAsH;IACxHF,EAAA,CAAAG,YAAA,EAAM;;;;IAJ4BH,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAM,kBAAA,KAAAa,MAAA,CAAAT,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAQ,WAAA,OAA2C;IAG7BpB,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAc,WAAA,UAAAK,MAAA,CAAAT,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAQ,WAAA,gBAAiE;;;;;IAoBvHpB,EAAA,CAAAC,cAAA,cAAyD;IAChBD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,cAAuC;IACQD,EAAA,CAAAI,MAAA,GAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAvCH,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAqB,iBAAA,CAAAC,MAAA,CAAAZ,KAAA,CAAAC,UAAA,CAAAY,YAAA,CAAmC;;;;;IAQhFvB,EAAA,CAAAC,cAAA,YAI2F;IACzFD,EAAA,CAAAwB,cAAA,EAAiI;IAAjIxB,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAE,SAAA,eAAiM;IACnMF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAyB,eAAA,EAA6C;IAA7CzB,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAP7EH,EAAA,CAAA0B,UAAA,SAAAC,OAAA,CAAAC,UAAA,CAAAC,WAAA,GAAA7B,EAAA,CAAA8B,aAAA,CAA4B,aAAAH,OAAA,CAAAI,WAAA,CAAAF,WAAA;IAOgB7B,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAqB,iBAAA,CAAAM,OAAA,CAAAI,WAAA,CAAAF,WAAA,EAA0B;;;;;IAX7E7B,EAAA,CAAAC,cAAA,cAAsE;IAC7BD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAgC,UAAA,IAAAC,qDAAA,gBASI;IACNjC,EAAA,CAAAG,YAAA,EAAM;;;;IAVmBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA0B,UAAA,YAAAQ,MAAA,CAAAxB,KAAA,CAAAyB,QAAA,CAAiB;;;;;IAc5CnC,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,0BAAAN,EAAA,CAAAoC,WAAA,OAAAC,MAAA,CAAA3B,KAAA,CAAAC,UAAA,CAAA2B,cAAA,kCACF;;;;;;;;;;;;;IAzHJtC,EAAA,CAAAC,cAAA,cAAiG;IAK7CD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAI,MAAA,GAAgE;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEvGH,EAAA,CAAAC,cAAA,cAA0B;IAEtBD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkB;IAC6BD,EAAA,CAAAI,MAAA,IAAsD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACvGH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,IAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAElEH,EAAA,CAAAC,cAAA,eAAqB;IAEjBD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAMbH,EAAA,CAAAC,cAAA,eAAiB;IACwBD,EAAA,CAAAI,MAAA,+BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAG9DH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAgC,UAAA,KAAAO,iDAAA,kBAQM;IAENvC,EAAA,CAAAgC,UAAA,KAAAQ,iDAAA,kBAQM;IAENxC,EAAA,CAAAgC,UAAA,KAAAS,iDAAA,kBAQM;IAENzC,EAAA,CAAAgC,UAAA,KAAAU,iDAAA,kBAQM;IACR1C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA4C;IAEUD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,gBAA8D;IAAAD,EAAA,CAAAI,MAAA,IAA6C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEpHH,EAAA,CAAAC,cAAA,eAAiD;IAC/CD,EAAA,CAAAE,SAAA,eAEkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAgC,UAAA,KAAAW,iDAAA,kBAKM;IAGN3C,EAAA,CAAAgC,UAAA,KAAAY,iDAAA,kBAcM;IAGN5C,EAAA,CAAAgC,UAAA,KAAAa,iDAAA,kBAEM;IACR7C,EAAA,CAAAG,YAAA,EAAM;;;;IArH4CH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAqB,iBAAA,CAAAyB,MAAA,CAAApC,KAAA,CAAAqC,MAAA,kBAAAD,MAAA,CAAApC,KAAA,CAAAqC,MAAA,CAAAC,KAAA,CAAyB;IACpChD,EAAA,CAAAK,SAAA,GAAgE;IAAhEL,EAAA,CAAAM,kBAAA,eAAAN,EAAA,CAAAoC,WAAA,QAAAU,MAAA,CAAApC,KAAA,CAAAuC,cAAA,iCAAgE;IAG3FjD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,YAAAoB,MAAA,CAAAI,aAAA,GAA2B;IAC/BlD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAmD,kBAAA,MAAAL,MAAA,CAAAM,aAAA,SAAAN,MAAA,CAAAO,eAAA,QACF;IAMArD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAmD,kBAAA,MAAAL,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAC,GAAA,kBAAAT,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAC,GAAA,CAAAC,MAAA,SAAAV,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAG,MAAA,kBAAAX,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAG,MAAA,CAAAD,MAAA,SACF;IAE+CxD,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAmD,kBAAA,KAAAL,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAC,GAAA,OAAAT,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAG,MAAA,KAAsD;IAClEzD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAqB,iBAAA,CAAAyB,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAI,KAAA,CAA2B;IAI1D1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAwC,MAAA,CAAApC,KAAA,CAAA4C,QAAA,kBAAAR,MAAA,CAAApC,KAAA,CAAA4C,QAAA,CAAAK,MAAA,0CACF;IAYM3D,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAA8B;IAU9BZ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAA8B;IAU9BZ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAA8B;IAU9BZ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAA8B;IAeAZ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,YAAAoB,MAAA,CAAAI,aAAA,GAA2B;IAAClD,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAmD,kBAAA,KAAAL,MAAA,CAAAM,aAAA,SAAAN,MAAA,CAAAO,eAAA,OAA6C;IAKtGrD,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAc,WAAA,UAAAgC,MAAA,CAAAc,kBAAA,QAAsC;IAFtC5D,EAAA,CAAA0B,UAAA,YAAA1B,EAAA,CAAA6D,eAAA,KAAAC,GAAA,EAAAhB,MAAA,CAAAc,kBAAA,UAAAd,MAAA,CAAAc,kBAAA,YAAAd,MAAA,CAAAc,kBAAA,SAAAd,MAAA,CAAAc,kBAAA,YAAAd,MAAA,CAAAc,kBAAA,SAAAd,MAAA,CAAAc,kBAAA,SAAmP;IAQxP5D,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAAY,YAAA,CAAoC;IAQpCvB,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAyB,QAAA,IAAAW,MAAA,CAAApC,KAAA,CAAAyB,QAAA,CAAA4B,MAAA,KAAiD;IAiBjD/D,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAA0B,UAAA,SAAAoB,MAAA,CAAApC,KAAA,CAAAC,UAAA,kBAAAmC,MAAA,CAAApC,KAAA,CAAAC,UAAA,CAAA2B,cAAA,CAAsC;;;ADpIpD,OAAM,MAAO0B,0BAA0B;EAOrCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAF5B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IARvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA3D,KAAK,GAAQ,IAAI;IACjB,KAAA4D,SAAS,GAAY,IAAI;IACzB,KAAA9D,KAAK,GAAW,EAAE;EAMd;EAEJ+D,QAAQA,CAAA;IACN,IAAI,CAACF,OAAO,GAAG,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAChE,IAAI,IAAI,CAACL,OAAO,EAAE;MAChB,IAAI,CAACM,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAACnE,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAAC8D,SAAS,GAAG,KAAK;;EAE1B;EAEAK,SAASA,CAAA;IACP,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAACQ,YAAY,CAAC,IAAI,CAACP,OAAO,CAAC,CAACQ,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACrE,KAAK,GAAGqE,IAAI;QACjB,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB,CAAC;MACD9D,KAAK,EAAGwE,GAAQ,IAAI;QAClB,IAAI,CAACxE,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAAC8D,SAAS,GAAG,KAAK;QACtBW,OAAO,CAACzE,KAAK,CAACwE,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEA5B,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC1C,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAE,OAAO,CAAC;IAE7C,MAAMA,MAAM,GAAG,IAAI,CAACF,KAAK,CAACC,UAAU,CAACC,MAAM;IAC3C,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACI,SAAS,GAAGJ,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACQ,WAAW;EACzF;EAEAiC,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAO,kBAAkBA,CAAA;IAChB,OAAQ,IAAI,CAACR,aAAa,EAAE,GAAG,IAAI,CAACC,eAAe,EAAE,GAAI,GAAG;EAC9D;EAEAH,aAAaA,CAAA;IACX,MAAMgC,UAAU,GAAG,IAAI,CAACtB,kBAAkB,EAAE;IAC5C,IAAIsB,UAAU,IAAI,EAAE,EAAE,OAAO,gBAAgB;IAC7C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,eAAe;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,iBAAiB;IAC9C,OAAO,cAAc;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEA;EACAxD,UAAUA,CAACyD,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACzB,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGhE,WAAW,CAAC2F,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEAvD,WAAWA,CAACsD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACzB,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOsB,QAAQ;EACjB;;;uBA5FWrB,0BAA0B,EAAAhE,EAAA,CAAA2F,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7F,EAAA,CAAA2F,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9F,EAAA,CAAA2F,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BhC,0BAA0B;MAAAiC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCvG,EAAA,CAAAC,cAAA,aAAkD;UAIpCD,EAAA,CAAAyG,UAAA,mBAAAC,4DAAA;YAAA,OAASF,GAAA,CAAArB,WAAA,EAAa;UAAA,EAAC;UAC7BnF,EAAA,CAAAwB,cAAA,EAA4H;UAA5HxB,EAAA,CAAAC,cAAA,aAA4H;UAC1HD,EAAA,CAAAE,SAAA,cAAwG;UAC1GF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAyB,eAAA,EAA0D;UAA1DzB,EAAA,CAAAC,cAAA,YAA0D;UAAAD,EAAA,CAAAI,MAAA,wCAAuB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAIxFH,EAAA,CAAAgC,UAAA,IAAA2E,yCAAA,iBAEM;UAGN3G,EAAA,CAAAgC,UAAA,IAAA4E,yCAAA,iBAEM;UAGN5G,EAAA,CAAAgC,UAAA,KAAA6E,0CAAA,mBA2HM;UACR7G,EAAA,CAAAG,YAAA,EAAM;;;UAtIEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAA0B,UAAA,SAAA8E,GAAA,CAAAlC,SAAA,CAAe;UAKftE,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA0B,UAAA,SAAA8E,GAAA,CAAAhG,KAAA,CAAW;UAKXR,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAA0B,UAAA,UAAA8E,GAAA,CAAAlC,SAAA,KAAAkC,GAAA,CAAAhG,KAAA,IAAAgG,GAAA,CAAA9F,KAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}