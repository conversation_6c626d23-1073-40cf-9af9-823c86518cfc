{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VerifyEmailComponent } from './verify-email.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: VerifyEmailComponent\n}];\nexport class VerifyEmailRoutingModule {\n  static {\n    this.ɵfac = function VerifyEmailRoutingModule_Factory(t) {\n      return new (t || VerifyEmailRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VerifyEmailRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VerifyEmailRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VerifyEmailComponent", "routes", "path", "component", "VerifyEmailRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\verify-email\\verify-email-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VerifyEmailComponent } from './verify-email.component';\r\n\r\nconst routes: Routes = [{ path: '', component: VerifyEmailComponent }];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class VerifyEmailRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,0BAA0B;;;AAE/D,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAoB,CAAE,CAAC;AAMtE,OAAM,MAAOI,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAHzBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,wBAAwB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFzBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}