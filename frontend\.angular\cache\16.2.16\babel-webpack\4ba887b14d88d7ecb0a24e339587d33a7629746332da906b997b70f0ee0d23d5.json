{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { VerifyEmailRoutingModule } from './verify-email-routing.module';\nimport { VerifyEmailComponent } from './verify-email.component';\nimport * as i0 from \"@angular/core\";\nexport class VerifyEmailModule {\n  static {\n    this.ɵfac = function VerifyEmailModule_Factory(t) {\n      return new (t || VerifyEmailModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VerifyEmailModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, VerifyEmailRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VerifyEmailModule, {\n    declarations: [VerifyEmailComponent],\n    imports: [CommonModule, ReactiveFormsModule, VerifyEmailRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "VerifyEmailRoutingModule", "VerifyEmailComponent", "VerifyEmailModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\verify-email\\verify-email.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { VerifyEmailRoutingModule } from './verify-email-routing.module';\r\nimport { VerifyEmailComponent } from './verify-email.component';\r\n\r\n@NgModule({\r\n  declarations: [VerifyEmailComponent],\r\n  imports: [CommonModule, ReactiveFormsModule, VerifyEmailRoutingModule],\r\n})\r\nexport class VerifyEmailModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,oBAAoB,QAAQ,0BAA0B;;AAM/D,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAFlBJ,YAAY,EAAEC,mBAAmB,EAAEC,wBAAwB;IAAA;EAAA;;;2EAE1DE,iBAAiB;IAAAC,YAAA,GAHbF,oBAAoB;IAAAG,OAAA,GACzBN,YAAY,EAAEC,mBAAmB,EAAEC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}