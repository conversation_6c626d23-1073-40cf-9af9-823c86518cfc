{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ChangePasswordComponent } from './change-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ChangePasswordComponent\n}];\nexport class ChangePasswordRoutingModule {\n  static {\n    this.ɵfac = function ChangePasswordRoutingModule_Factory(t) {\n      return new (t || ChangePasswordRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ChangePasswordRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ChangePasswordRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ChangePasswordComponent", "routes", "path", "component", "ChangePasswordRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\change-password\\change-password-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ChangePasswordComponent } from './change-password.component';\r\n\r\nconst routes: Routes = [{ path: '', component: ChangePasswordComponent }];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ChangePasswordRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;;;AAErE,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAuB,CAAE,CAAC;AAMzE,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF5BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}