{"ast": null, "code": "import { Observable } from \"./Observable.js\";\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap(observable, mapFn, catchFn) {\n  return new Observable(function (observer) {\n    var promiseQueue = {\n      // Normally we would initialize promiseQueue to Promise.resolve(), but\n      // in this case, for backwards compatibility, we need to be careful to\n      // invoke the first callback synchronously.\n      then: function (callback) {\n        return new Promise(function (resolve) {\n          return resolve(callback());\n        });\n      }\n    };\n    function makeCallback(examiner, key) {\n      return function (arg) {\n        if (examiner) {\n          var both = function () {\n            // If the observer is closed, we don't want to continue calling the\n            // mapping function - it's result will be swallowed anyways.\n            return observer.closed ? /* will be swallowed */0 : examiner(arg);\n          };\n          promiseQueue = promiseQueue.then(both, both).then(function (result) {\n            return observer.next(result);\n          }, function (error) {\n            return observer.error(error);\n          });\n        } else {\n          observer[key](arg);\n        }\n      };\n    }\n    var handler = {\n      next: makeCallback(mapFn, \"next\"),\n      error: makeCallback(catchFn, \"error\"),\n      complete: function () {\n        // no need to reassign `promiseQueue`, after `observer.complete`,\n        // the observer will be closed and short-circuit everything anyways\n        /*promiseQueue = */\n        promiseQueue.then(function () {\n          return observer.complete();\n        });\n      }\n    };\n    var sub = observable.subscribe(handler);\n    return function () {\n      return sub.unsubscribe();\n    };\n  });\n}", "map": {"version": 3, "names": ["Observable", "asyncMap", "observable", "mapFn", "catchFn", "observer", "promiseQueue", "then", "callback", "Promise", "resolve", "makeCallback", "examiner", "key", "arg", "both", "closed", "result", "next", "error", "handler", "complete", "sub", "subscribe", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/observables/asyncMap.js"], "sourcesContent": ["import { Observable } from \"./Observable.js\";\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap(observable, mapFn, catchFn) {\n    return new Observable(function (observer) {\n        var promiseQueue = {\n            // Normally we would initialize promiseQueue to Promise.resolve(), but\n            // in this case, for backwards compatibility, we need to be careful to\n            // invoke the first callback synchronously.\n            then: function (callback) {\n                return new Promise(function (resolve) { return resolve(callback()); });\n            },\n        };\n        function makeCallback(examiner, key) {\n            return function (arg) {\n                if (examiner) {\n                    var both = function () {\n                        // If the observer is closed, we don't want to continue calling the\n                        // mapping function - it's result will be swallowed anyways.\n                        return observer.closed ?\n                            /* will be swallowed */ 0\n                            : examiner(arg);\n                    };\n                    promiseQueue = promiseQueue.then(both, both).then(function (result) { return observer.next(result); }, function (error) { return observer.error(error); });\n                }\n                else {\n                    observer[key](arg);\n                }\n            };\n        }\n        var handler = {\n            next: makeCallback(mapFn, \"next\"),\n            error: makeCallback(catchFn, \"error\"),\n            complete: function () {\n                // no need to reassign `promiseQueue`, after `observer.complete`,\n                // the observer will be closed and short-circuit everything anyways\n                /*promiseQueue = */ promiseQueue.then(function () { return observer.complete(); });\n            },\n        };\n        var sub = observable.subscribe(handler);\n        return function () { return sub.unsubscribe(); };\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACjD,OAAO,IAAIJ,UAAU,CAAC,UAAUK,QAAQ,EAAE;IACtC,IAAIC,YAAY,GAAG;MACf;MACA;MACA;MACAC,IAAI,EAAE,SAAAA,CAAUC,QAAQ,EAAE;QACtB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;UAAE,OAAOA,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC;MAC1E;IACJ,CAAC;IACD,SAASG,YAAYA,CAACC,QAAQ,EAAEC,GAAG,EAAE;MACjC,OAAO,UAAUC,GAAG,EAAE;QAClB,IAAIF,QAAQ,EAAE;UACV,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAY;YACnB;YACA;YACA,OAAOV,QAAQ,CAACW,MAAM,GAClB,uBAAwB,CAAC,GACvBJ,QAAQ,CAACE,GAAG,CAAC;UACvB,CAAC;UACDR,YAAY,GAAGA,YAAY,CAACC,IAAI,CAACQ,IAAI,EAAEA,IAAI,CAAC,CAACR,IAAI,CAAC,UAAUU,MAAM,EAAE;YAAE,OAAOZ,QAAQ,CAACa,IAAI,CAACD,MAAM,CAAC;UAAE,CAAC,EAAE,UAAUE,KAAK,EAAE;YAAE,OAAOd,QAAQ,CAACc,KAAK,CAACA,KAAK,CAAC;UAAE,CAAC,CAAC;QAC9J,CAAC,MACI;UACDd,QAAQ,CAACQ,GAAG,CAAC,CAACC,GAAG,CAAC;QACtB;MACJ,CAAC;IACL;IACA,IAAIM,OAAO,GAAG;MACVF,IAAI,EAAEP,YAAY,CAACR,KAAK,EAAE,MAAM,CAAC;MACjCgB,KAAK,EAAER,YAAY,CAACP,OAAO,EAAE,OAAO,CAAC;MACrCiB,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB;QACA;QACA;QAAoBf,YAAY,CAACC,IAAI,CAAC,YAAY;UAAE,OAAOF,QAAQ,CAACgB,QAAQ,CAAC,CAAC;QAAE,CAAC,CAAC;MACtF;IACJ,CAAC;IACD,IAAIC,GAAG,GAAGpB,UAAU,CAACqB,SAAS,CAACH,OAAO,CAAC;IACvC,OAAO,YAAY;MAAE,OAAOE,GAAG,CAACE,WAAW,CAAC,CAAC;IAAE,CAAC;EACpD,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}