{"ast": null, "code": "import { __assign, __rest as __rest_1, __spreadArray } from \"tslib\";\nimport { __rest } from \"tslib\";\nimport { mergeDeep } from \"../common/mergeDeep.js\";\n// A very basic pagination field policy that always concatenates new\n// results onto the existing array, without examining options.args.\nexport function concatPagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    merge: function (existing, incoming) {\n      return existing ? __spreadArray(__spreadArray([], existing, true), incoming, true) : incoming;\n    }\n  };\n}\n// A basic field policy that uses options.args.{offset,limit} to splice\n// the incoming data into the existing array. If your arguments are called\n// something different (like args.{start,count}), feel free to copy/paste\n// this implementation and make the appropriate changes.\nexport function offsetLimitPagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    merge: function (existing, incoming, _a) {\n      var args = _a.args;\n      var merged = existing ? existing.slice(0) : [];\n      if (incoming) {\n        if (args) {\n          // Assume an offset of 0 if args.offset omitted.\n          var _b = args.offset,\n            offset = _b === void 0 ? 0 : _b;\n          for (var i = 0; i < incoming.length; ++i) {\n            merged[offset + i] = incoming[i];\n          }\n        } else {\n          // It's unusual (probably a mistake) for a paginated field not\n          // to receive any arguments, so you might prefer to throw an\n          // exception here, instead of recovering by appending incoming\n          // onto the existing array.\n          merged.push.apply(merged, incoming);\n        }\n      }\n      return merged;\n    }\n  };\n}\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\nexport function relayStylePagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    read: function (existing, _a) {\n      var canRead = _a.canRead,\n        readField = _a.readField;\n      if (!existing) return existing;\n      var edges = [];\n      var firstEdgeCursor = \"\";\n      var lastEdgeCursor = \"\";\n      existing.edges.forEach(function (edge) {\n        // Edges themselves could be Reference objects, so it's important\n        // to use readField to access the edge.edge.node property.\n        if (canRead(readField(\"node\", edge))) {\n          edges.push(edge);\n          if (edge.cursor) {\n            firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n            lastEdgeCursor = edge.cursor || lastEdgeCursor;\n          }\n        }\n      });\n      if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n        firstEdgeCursor = \"\";\n      }\n      var _b = existing.pageInfo || {},\n        startCursor = _b.startCursor,\n        endCursor = _b.endCursor;\n      return __assign(__assign({}, getExtras(existing)), {\n        edges: edges,\n        pageInfo: __assign(__assign({}, existing.pageInfo), {\n          // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n          // to firstEdgeCursor and/or lastEdgeCursor.\n          startCursor: startCursor || firstEdgeCursor,\n          endCursor: endCursor || lastEdgeCursor\n        })\n      });\n    },\n    merge: function (existing, incoming, _a) {\n      var args = _a.args,\n        isReference = _a.isReference,\n        readField = _a.readField;\n      if (!existing) {\n        existing = makeEmptyData();\n      }\n      if (!incoming) {\n        return existing;\n      }\n      var incomingEdges = incoming.edges ? incoming.edges.map(function (edge) {\n        if (isReference(edge = __assign({}, edge))) {\n          // In case edge is a Reference, we read out its cursor field and\n          // store it as an extra property of the Reference object.\n          edge.cursor = readField(\"cursor\", edge);\n        }\n        return edge;\n      }) : [];\n      if (incoming.pageInfo) {\n        var pageInfo_1 = incoming.pageInfo;\n        var startCursor = pageInfo_1.startCursor,\n          endCursor = pageInfo_1.endCursor;\n        var firstEdge = incomingEdges[0];\n        var lastEdge = incomingEdges[incomingEdges.length - 1];\n        // In case we did not request the cursor field for edges in this\n        // query, we can still infer cursors from pageInfo.\n        if (firstEdge && startCursor) {\n          firstEdge.cursor = startCursor;\n        }\n        if (lastEdge && endCursor) {\n          lastEdge.cursor = endCursor;\n        }\n        // Cursors can also come from edges, so we default\n        // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n        var firstCursor = firstEdge && firstEdge.cursor;\n        if (firstCursor && !startCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              startCursor: firstCursor\n            }\n          });\n        }\n        var lastCursor = lastEdge && lastEdge.cursor;\n        if (lastCursor && !endCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              endCursor: lastCursor\n            }\n          });\n        }\n      }\n      var prefix = existing.edges;\n      var suffix = [];\n      if (args && args.after) {\n        // This comparison does not need to use readField(\"cursor\", edge),\n        // because we stored the cursor field of any Reference edges as an\n        // extra property of the Reference object.\n        var index = prefix.findIndex(function (edge) {\n          return edge.cursor === args.after;\n        });\n        if (index >= 0) {\n          prefix = prefix.slice(0, index + 1);\n          // suffix = []; // already true\n        }\n      } else if (args && args.before) {\n        var index = prefix.findIndex(function (edge) {\n          return edge.cursor === args.before;\n        });\n        suffix = index < 0 ? prefix : prefix.slice(index);\n        prefix = [];\n      } else if (incoming.edges) {\n        // If we have neither args.after nor args.before, the incoming\n        // edges cannot be spliced into the existing edges, so they must\n        // replace the existing edges. See #6592 for a motivating example.\n        prefix = [];\n      }\n      var edges = __spreadArray(__spreadArray(__spreadArray([], prefix, true), incomingEdges, true), suffix, true);\n      var pageInfo = __assign(__assign({}, incoming.pageInfo), existing.pageInfo);\n      if (incoming.pageInfo) {\n        var _b = incoming.pageInfo,\n          hasPreviousPage = _b.hasPreviousPage,\n          hasNextPage = _b.hasNextPage,\n          startCursor = _b.startCursor,\n          endCursor = _b.endCursor,\n          extras = __rest_1(_b, [\"hasPreviousPage\", \"hasNextPage\", \"startCursor\", \"endCursor\"]);\n        // If incoming.pageInfo had any extra non-standard properties,\n        // assume they should take precedence over any existing properties\n        // of the same name, regardless of where this page falls with\n        // respect to the existing data.\n        Object.assign(pageInfo, extras);\n        // Keep existing.pageInfo.has{Previous,Next}Page unless the\n        // placement of the incoming edges means incoming.hasPreviousPage\n        // or incoming.hasNextPage should become the new values for those\n        // properties in existing.pageInfo. Note that these updates are\n        // only permitted when the beginning or end of the incoming page\n        // coincides with the beginning or end of the existing data, as\n        // determined using prefix.length and suffix.length.\n        if (!prefix.length) {\n          if (void 0 !== hasPreviousPage) pageInfo.hasPreviousPage = hasPreviousPage;\n          if (void 0 !== startCursor) pageInfo.startCursor = startCursor;\n        }\n        if (!suffix.length) {\n          if (void 0 !== hasNextPage) pageInfo.hasNextPage = hasNextPage;\n          if (void 0 !== endCursor) pageInfo.endCursor = endCursor;\n        }\n      }\n      return __assign(__assign(__assign({}, getExtras(existing)), getExtras(incoming)), {\n        edges: edges,\n        pageInfo: pageInfo\n      });\n    }\n  };\n}\n// Returns any unrecognized properties of the given object.\nvar getExtras = function (obj) {\n  return __rest(obj, notExtras);\n};\nvar notExtras = [\"edges\", \"pageInfo\"];\nfunction makeEmptyData() {\n  return {\n    edges: [],\n    pageInfo: {\n      hasPreviousPage: false,\n      hasNextPage: true,\n      startCursor: \"\",\n      endCursor: \"\"\n    }\n  };\n}", "map": {"version": 3, "names": ["__assign", "__rest", "__rest_1", "__spread<PERSON><PERSON>y", "mergeDeep", "concatPagination", "keyArgs", "merge", "existing", "incoming", "offsetLimitPagination", "_a", "args", "merged", "slice", "_b", "offset", "i", "length", "push", "apply", "relayStylePagination", "read", "canRead", "readField", "edges", "firstEdgeCursor", "lastEdgeCursor", "for<PERSON>ach", "edge", "cursor", "pageInfo", "startCursor", "endCursor", "getExtras", "isReference", "makeEmptyData", "incomingEdges", "map", "pageInfo_1", "firstEdge", "lastEdge", "firstCursor", "lastCursor", "prefix", "suffix", "after", "index", "findIndex", "before", "hasPreviousPage", "hasNextPage", "extras", "Object", "assign", "obj", "notExtras"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/policies/pagination.js"], "sourcesContent": ["import { __assign, __rest as __rest_1, __spreadArray } from \"tslib\";\nimport { __rest } from \"tslib\";\nimport { mergeDeep } from \"../common/mergeDeep.js\";\n// A very basic pagination field policy that always concatenates new\n// results onto the existing array, without examining options.args.\nexport function concatPagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        merge: function (existing, incoming) {\n            return existing ? __spreadArray(__spreadArray([], existing, true), incoming, true) : incoming;\n        },\n    };\n}\n// A basic field policy that uses options.args.{offset,limit} to splice\n// the incoming data into the existing array. If your arguments are called\n// something different (like args.{start,count}), feel free to copy/paste\n// this implementation and make the appropriate changes.\nexport function offsetLimitPagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        merge: function (existing, incoming, _a) {\n            var args = _a.args;\n            var merged = existing ? existing.slice(0) : [];\n            if (incoming) {\n                if (args) {\n                    // Assume an offset of 0 if args.offset omitted.\n                    var _b = args.offset, offset = _b === void 0 ? 0 : _b;\n                    for (var i = 0; i < incoming.length; ++i) {\n                        merged[offset + i] = incoming[i];\n                    }\n                }\n                else {\n                    // It's unusual (probably a mistake) for a paginated field not\n                    // to receive any arguments, so you might prefer to throw an\n                    // exception here, instead of recovering by appending incoming\n                    // onto the existing array.\n                    merged.push.apply(merged, incoming);\n                }\n            }\n            return merged;\n        },\n    };\n}\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\nexport function relayStylePagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        read: function (existing, _a) {\n            var canRead = _a.canRead, readField = _a.readField;\n            if (!existing)\n                return existing;\n            var edges = [];\n            var firstEdgeCursor = \"\";\n            var lastEdgeCursor = \"\";\n            existing.edges.forEach(function (edge) {\n                // Edges themselves could be Reference objects, so it's important\n                // to use readField to access the edge.edge.node property.\n                if (canRead(readField(\"node\", edge))) {\n                    edges.push(edge);\n                    if (edge.cursor) {\n                        firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n                        lastEdgeCursor = edge.cursor || lastEdgeCursor;\n                    }\n                }\n            });\n            if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n                firstEdgeCursor = \"\";\n            }\n            var _b = existing.pageInfo || {}, startCursor = _b.startCursor, endCursor = _b.endCursor;\n            return __assign(__assign({}, getExtras(existing)), { edges: edges, pageInfo: __assign(__assign({}, existing.pageInfo), { \n                    // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n                    // to firstEdgeCursor and/or lastEdgeCursor.\n                    startCursor: startCursor || firstEdgeCursor, endCursor: endCursor || lastEdgeCursor }) });\n        },\n        merge: function (existing, incoming, _a) {\n            var args = _a.args, isReference = _a.isReference, readField = _a.readField;\n            if (!existing) {\n                existing = makeEmptyData();\n            }\n            if (!incoming) {\n                return existing;\n            }\n            var incomingEdges = incoming.edges ?\n                incoming.edges.map(function (edge) {\n                    if (isReference((edge = __assign({}, edge)))) {\n                        // In case edge is a Reference, we read out its cursor field and\n                        // store it as an extra property of the Reference object.\n                        edge.cursor = readField(\"cursor\", edge);\n                    }\n                    return edge;\n                })\n                : [];\n            if (incoming.pageInfo) {\n                var pageInfo_1 = incoming.pageInfo;\n                var startCursor = pageInfo_1.startCursor, endCursor = pageInfo_1.endCursor;\n                var firstEdge = incomingEdges[0];\n                var lastEdge = incomingEdges[incomingEdges.length - 1];\n                // In case we did not request the cursor field for edges in this\n                // query, we can still infer cursors from pageInfo.\n                if (firstEdge && startCursor) {\n                    firstEdge.cursor = startCursor;\n                }\n                if (lastEdge && endCursor) {\n                    lastEdge.cursor = endCursor;\n                }\n                // Cursors can also come from edges, so we default\n                // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n                var firstCursor = firstEdge && firstEdge.cursor;\n                if (firstCursor && !startCursor) {\n                    incoming = mergeDeep(incoming, {\n                        pageInfo: {\n                            startCursor: firstCursor,\n                        },\n                    });\n                }\n                var lastCursor = lastEdge && lastEdge.cursor;\n                if (lastCursor && !endCursor) {\n                    incoming = mergeDeep(incoming, {\n                        pageInfo: {\n                            endCursor: lastCursor,\n                        },\n                    });\n                }\n            }\n            var prefix = existing.edges;\n            var suffix = [];\n            if (args && args.after) {\n                // This comparison does not need to use readField(\"cursor\", edge),\n                // because we stored the cursor field of any Reference edges as an\n                // extra property of the Reference object.\n                var index = prefix.findIndex(function (edge) { return edge.cursor === args.after; });\n                if (index >= 0) {\n                    prefix = prefix.slice(0, index + 1);\n                    // suffix = []; // already true\n                }\n            }\n            else if (args && args.before) {\n                var index = prefix.findIndex(function (edge) { return edge.cursor === args.before; });\n                suffix = index < 0 ? prefix : prefix.slice(index);\n                prefix = [];\n            }\n            else if (incoming.edges) {\n                // If we have neither args.after nor args.before, the incoming\n                // edges cannot be spliced into the existing edges, so they must\n                // replace the existing edges. See #6592 for a motivating example.\n                prefix = [];\n            }\n            var edges = __spreadArray(__spreadArray(__spreadArray([], prefix, true), incomingEdges, true), suffix, true);\n            var pageInfo = __assign(__assign({}, incoming.pageInfo), existing.pageInfo);\n            if (incoming.pageInfo) {\n                var _b = incoming.pageInfo, hasPreviousPage = _b.hasPreviousPage, hasNextPage = _b.hasNextPage, startCursor = _b.startCursor, endCursor = _b.endCursor, extras = __rest_1(_b, [\"hasPreviousPage\", \"hasNextPage\", \"startCursor\", \"endCursor\"]);\n                // If incoming.pageInfo had any extra non-standard properties,\n                // assume they should take precedence over any existing properties\n                // of the same name, regardless of where this page falls with\n                // respect to the existing data.\n                Object.assign(pageInfo, extras);\n                // Keep existing.pageInfo.has{Previous,Next}Page unless the\n                // placement of the incoming edges means incoming.hasPreviousPage\n                // or incoming.hasNextPage should become the new values for those\n                // properties in existing.pageInfo. Note that these updates are\n                // only permitted when the beginning or end of the incoming page\n                // coincides with the beginning or end of the existing data, as\n                // determined using prefix.length and suffix.length.\n                if (!prefix.length) {\n                    if (void 0 !== hasPreviousPage)\n                        pageInfo.hasPreviousPage = hasPreviousPage;\n                    if (void 0 !== startCursor)\n                        pageInfo.startCursor = startCursor;\n                }\n                if (!suffix.length) {\n                    if (void 0 !== hasNextPage)\n                        pageInfo.hasNextPage = hasNextPage;\n                    if (void 0 !== endCursor)\n                        pageInfo.endCursor = endCursor;\n                }\n            }\n            return __assign(__assign(__assign({}, getExtras(existing)), getExtras(incoming)), { edges: edges, pageInfo: pageInfo });\n        },\n    };\n}\n// Returns any unrecognized properties of the given object.\nvar getExtras = function (obj) { return __rest(obj, notExtras); };\nvar notExtras = [\"edges\", \"pageInfo\"];\nfunction makeEmptyData() {\n    return {\n        edges: [],\n        pageInfo: {\n            hasPreviousPage: false,\n            hasNextPage: true,\n            startCursor: \"\",\n            endCursor: \"\",\n        },\n    };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AACnE,SAASF,MAAM,QAAQ,OAAO;AAC9B,SAASG,SAAS,QAAQ,wBAAwB;AAClD;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACtC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,KAAK;EAAE;EAC3C,OAAO;IACHA,OAAO,EAAEA,OAAO;IAChBC,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAE;MACjC,OAAOD,QAAQ,GAAGL,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEK,QAAQ,EAAE,IAAI,CAAC,EAAEC,QAAQ,EAAE,IAAI,CAAC,GAAGA,QAAQ;IACjG;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACJ,OAAO,EAAE;EAC3C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,KAAK;EAAE;EAC3C,OAAO;IACHA,OAAO,EAAEA,OAAO;IAChBC,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAEE,EAAE,EAAE;MACrC,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;MAClB,IAAIC,MAAM,GAAGL,QAAQ,GAAGA,QAAQ,CAACM,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;MAC9C,IAAIL,QAAQ,EAAE;QACV,IAAIG,IAAI,EAAE;UACN;UACA,IAAIG,EAAE,GAAGH,IAAI,CAACI,MAAM;YAAEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;UACrD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACS,MAAM,EAAE,EAAED,CAAC,EAAE;YACtCJ,MAAM,CAACG,MAAM,GAAGC,CAAC,CAAC,GAAGR,QAAQ,CAACQ,CAAC,CAAC;UACpC;QACJ,CAAC,MACI;UACD;UACA;UACA;UACA;UACAJ,MAAM,CAACM,IAAI,CAACC,KAAK,CAACP,MAAM,EAAEJ,QAAQ,CAAC;QACvC;MACJ;MACA,OAAOI,MAAM;IACjB;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA,OAAO,SAASQ,oBAAoBA,CAACf,OAAO,EAAE;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,KAAK;EAAE;EAC3C,OAAO;IACHA,OAAO,EAAEA,OAAO;IAChBgB,IAAI,EAAE,SAAAA,CAAUd,QAAQ,EAAEG,EAAE,EAAE;MAC1B,IAAIY,OAAO,GAAGZ,EAAE,CAACY,OAAO;QAAEC,SAAS,GAAGb,EAAE,CAACa,SAAS;MAClD,IAAI,CAAChB,QAAQ,EACT,OAAOA,QAAQ;MACnB,IAAIiB,KAAK,GAAG,EAAE;MACd,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,cAAc,GAAG,EAAE;MACvBnB,QAAQ,CAACiB,KAAK,CAACG,OAAO,CAAC,UAAUC,IAAI,EAAE;QACnC;QACA;QACA,IAAIN,OAAO,CAACC,SAAS,CAAC,MAAM,EAAEK,IAAI,CAAC,CAAC,EAAE;UAClCJ,KAAK,CAACN,IAAI,CAACU,IAAI,CAAC;UAChB,IAAIA,IAAI,CAACC,MAAM,EAAE;YACbJ,eAAe,GAAGA,eAAe,IAAIG,IAAI,CAACC,MAAM,IAAI,EAAE;YACtDH,cAAc,GAAGE,IAAI,CAACC,MAAM,IAAIH,cAAc;UAClD;QACJ;MACJ,CAAC,CAAC;MACF,IAAIF,KAAK,CAACP,MAAM,GAAG,CAAC,IAAIQ,eAAe,KAAKC,cAAc,EAAE;QACxDD,eAAe,GAAG,EAAE;MACxB;MACA,IAAIX,EAAE,GAAGP,QAAQ,CAACuB,QAAQ,IAAI,CAAC,CAAC;QAAEC,WAAW,GAAGjB,EAAE,CAACiB,WAAW;QAAEC,SAAS,GAAGlB,EAAE,CAACkB,SAAS;MACxF,OAAOjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkC,SAAS,CAAC1B,QAAQ,CAAC,CAAC,EAAE;QAAEiB,KAAK,EAAEA,KAAK;QAAEM,QAAQ,EAAE/B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEQ,QAAQ,CAACuB,QAAQ,CAAC,EAAE;UAC/G;UACA;UACAC,WAAW,EAAEA,WAAW,IAAIN,eAAe;UAAEO,SAAS,EAAEA,SAAS,IAAIN;QAAe,CAAC;MAAE,CAAC,CAAC;IACrG,CAAC;IACDpB,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAEE,EAAE,EAAE;MACrC,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;QAAEuB,WAAW,GAAGxB,EAAE,CAACwB,WAAW;QAAEX,SAAS,GAAGb,EAAE,CAACa,SAAS;MAC1E,IAAI,CAAChB,QAAQ,EAAE;QACXA,QAAQ,GAAG4B,aAAa,CAAC,CAAC;MAC9B;MACA,IAAI,CAAC3B,QAAQ,EAAE;QACX,OAAOD,QAAQ;MACnB;MACA,IAAI6B,aAAa,GAAG5B,QAAQ,CAACgB,KAAK,GAC9BhB,QAAQ,CAACgB,KAAK,CAACa,GAAG,CAAC,UAAUT,IAAI,EAAE;QAC/B,IAAIM,WAAW,CAAEN,IAAI,GAAG7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,IAAI,CAAE,CAAC,EAAE;UAC1C;UACA;UACAA,IAAI,CAACC,MAAM,GAAGN,SAAS,CAAC,QAAQ,EAAEK,IAAI,CAAC;QAC3C;QACA,OAAOA,IAAI;MACf,CAAC,CAAC,GACA,EAAE;MACR,IAAIpB,QAAQ,CAACsB,QAAQ,EAAE;QACnB,IAAIQ,UAAU,GAAG9B,QAAQ,CAACsB,QAAQ;QAClC,IAAIC,WAAW,GAAGO,UAAU,CAACP,WAAW;UAAEC,SAAS,GAAGM,UAAU,CAACN,SAAS;QAC1E,IAAIO,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC;QAChC,IAAII,QAAQ,GAAGJ,aAAa,CAACA,aAAa,CAACnB,MAAM,GAAG,CAAC,CAAC;QACtD;QACA;QACA,IAAIsB,SAAS,IAAIR,WAAW,EAAE;UAC1BQ,SAAS,CAACV,MAAM,GAAGE,WAAW;QAClC;QACA,IAAIS,QAAQ,IAAIR,SAAS,EAAE;UACvBQ,QAAQ,CAACX,MAAM,GAAGG,SAAS;QAC/B;QACA;QACA;QACA,IAAIS,WAAW,GAAGF,SAAS,IAAIA,SAAS,CAACV,MAAM;QAC/C,IAAIY,WAAW,IAAI,CAACV,WAAW,EAAE;UAC7BvB,QAAQ,GAAGL,SAAS,CAACK,QAAQ,EAAE;YAC3BsB,QAAQ,EAAE;cACNC,WAAW,EAAEU;YACjB;UACJ,CAAC,CAAC;QACN;QACA,IAAIC,UAAU,GAAGF,QAAQ,IAAIA,QAAQ,CAACX,MAAM;QAC5C,IAAIa,UAAU,IAAI,CAACV,SAAS,EAAE;UAC1BxB,QAAQ,GAAGL,SAAS,CAACK,QAAQ,EAAE;YAC3BsB,QAAQ,EAAE;cACNE,SAAS,EAAEU;YACf;UACJ,CAAC,CAAC;QACN;MACJ;MACA,IAAIC,MAAM,GAAGpC,QAAQ,CAACiB,KAAK;MAC3B,IAAIoB,MAAM,GAAG,EAAE;MACf,IAAIjC,IAAI,IAAIA,IAAI,CAACkC,KAAK,EAAE;QACpB;QACA;QACA;QACA,IAAIC,KAAK,GAAGH,MAAM,CAACI,SAAS,CAAC,UAAUnB,IAAI,EAAE;UAAE,OAAOA,IAAI,CAACC,MAAM,KAAKlB,IAAI,CAACkC,KAAK;QAAE,CAAC,CAAC;QACpF,IAAIC,KAAK,IAAI,CAAC,EAAE;UACZH,MAAM,GAAGA,MAAM,CAAC9B,KAAK,CAAC,CAAC,EAAEiC,KAAK,GAAG,CAAC,CAAC;UACnC;QACJ;MACJ,CAAC,MACI,IAAInC,IAAI,IAAIA,IAAI,CAACqC,MAAM,EAAE;QAC1B,IAAIF,KAAK,GAAGH,MAAM,CAACI,SAAS,CAAC,UAAUnB,IAAI,EAAE;UAAE,OAAOA,IAAI,CAACC,MAAM,KAAKlB,IAAI,CAACqC,MAAM;QAAE,CAAC,CAAC;QACrFJ,MAAM,GAAGE,KAAK,GAAG,CAAC,GAAGH,MAAM,GAAGA,MAAM,CAAC9B,KAAK,CAACiC,KAAK,CAAC;QACjDH,MAAM,GAAG,EAAE;MACf,CAAC,MACI,IAAInC,QAAQ,CAACgB,KAAK,EAAE;QACrB;QACA;QACA;QACAmB,MAAM,GAAG,EAAE;MACf;MACA,IAAInB,KAAK,GAAGtB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEyC,MAAM,EAAE,IAAI,CAAC,EAAEP,aAAa,EAAE,IAAI,CAAC,EAAEQ,MAAM,EAAE,IAAI,CAAC;MAC5G,IAAId,QAAQ,GAAG/B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAES,QAAQ,CAACsB,QAAQ,CAAC,EAAEvB,QAAQ,CAACuB,QAAQ,CAAC;MAC3E,IAAItB,QAAQ,CAACsB,QAAQ,EAAE;QACnB,IAAIhB,EAAE,GAAGN,QAAQ,CAACsB,QAAQ;UAAEmB,eAAe,GAAGnC,EAAE,CAACmC,eAAe;UAAEC,WAAW,GAAGpC,EAAE,CAACoC,WAAW;UAAEnB,WAAW,GAAGjB,EAAE,CAACiB,WAAW;UAAEC,SAAS,GAAGlB,EAAE,CAACkB,SAAS;UAAEmB,MAAM,GAAGlD,QAAQ,CAACa,EAAE,EAAE,CAAC,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QAC7O;QACA;QACA;QACA;QACAsC,MAAM,CAACC,MAAM,CAACvB,QAAQ,EAAEqB,MAAM,CAAC;QAC/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACR,MAAM,CAAC1B,MAAM,EAAE;UAChB,IAAI,KAAK,CAAC,KAAKgC,eAAe,EAC1BnB,QAAQ,CAACmB,eAAe,GAAGA,eAAe;UAC9C,IAAI,KAAK,CAAC,KAAKlB,WAAW,EACtBD,QAAQ,CAACC,WAAW,GAAGA,WAAW;QAC1C;QACA,IAAI,CAACa,MAAM,CAAC3B,MAAM,EAAE;UAChB,IAAI,KAAK,CAAC,KAAKiC,WAAW,EACtBpB,QAAQ,CAACoB,WAAW,GAAGA,WAAW;UACtC,IAAI,KAAK,CAAC,KAAKlB,SAAS,EACpBF,QAAQ,CAACE,SAAS,GAAGA,SAAS;QACtC;MACJ;MACA,OAAOjC,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkC,SAAS,CAAC1B,QAAQ,CAAC,CAAC,EAAE0B,SAAS,CAACzB,QAAQ,CAAC,CAAC,EAAE;QAAEgB,KAAK,EAAEA,KAAK;QAAEM,QAAQ,EAAEA;MAAS,CAAC,CAAC;IAC3H;EACJ,CAAC;AACL;AACA;AACA,IAAIG,SAAS,GAAG,SAAAA,CAAUqB,GAAG,EAAE;EAAE,OAAOtD,MAAM,CAACsD,GAAG,EAAEC,SAAS,CAAC;AAAE,CAAC;AACjE,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AACrC,SAASpB,aAAaA,CAAA,EAAG;EACrB,OAAO;IACHX,KAAK,EAAE,EAAE;IACTM,QAAQ,EAAE;MACNmB,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE,IAAI;MACjBnB,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACf;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}