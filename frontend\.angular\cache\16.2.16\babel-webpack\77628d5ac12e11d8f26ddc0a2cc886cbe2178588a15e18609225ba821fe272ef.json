{"ast": null, "code": "import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link addSeconds} function options.\n */\n\n/**\n * @name addSeconds\n * @category Second Helpers\n * @summary Add the specified number of seconds to the given date.\n *\n * @description\n * Add the specified number of seconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add 30 seconds to 10 July 2014 12:45:00:\n * const result = addSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:45:30\n */\nexport function addSeconds(date, amount, options) {\n  return addMilliseconds(date, amount * 1000, options);\n}\n\n// Fallback for modularized imports:\nexport default addSeconds;", "map": {"version": 3, "names": ["addMilliseconds", "addSeconds", "date", "amount", "options"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/date-fns/addSeconds.js"], "sourcesContent": ["import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link addSeconds} function options.\n */\n\n/**\n * @name addSeconds\n * @category Second Helpers\n * @summary Add the specified number of seconds to the given date.\n *\n * @description\n * Add the specified number of seconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add 30 seconds to 10 July 2014 12:45:00:\n * const result = addSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:45:30\n */\nexport function addSeconds(date, amount, options) {\n  return addMilliseconds(date, amount * 1000, options);\n}\n\n// Fallback for modularized imports:\nexport default addSeconds;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAChD,OAAOJ,eAAe,CAACE,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAEC,OAAO,CAAC;AACtD;;AAEA;AACA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}