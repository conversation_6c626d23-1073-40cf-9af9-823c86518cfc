{"ast": null, "code": "import { Observable } from \"zen-observable-ts\";\n// This simplified polyfill attempts to follow the ECMAScript Observable\n// proposal (https://github.com/zenparsing/es-observable)\nimport \"symbol-observable\";\n// The zen-observable package defines Observable.prototype[Symbol.observable]\n// when Symbol is supported, but RxJS interop depends on also setting this fake\n// '@@observable' string as a polyfill for Symbol.observable.\nvar prototype = Observable.prototype;\nvar fakeObsSymbol = \"@@observable\";\nif (!prototype[fakeObsSymbol]) {\n  // @ts-expect-error\n  prototype[fakeObsSymbol] = function () {\n    return this;\n  };\n}\nexport { Observable };", "map": {"version": 3, "names": ["Observable", "prototype", "fakeObsSymbol"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/observables/Observable.js"], "sourcesContent": ["import { Observable } from \"zen-observable-ts\";\n// This simplified polyfill attempts to follow the ECMAScript Observable\n// proposal (https://github.com/zenparsing/es-observable)\nimport \"symbol-observable\";\n// The zen-observable package defines Observable.prototype[Symbol.observable]\n// when Symbol is supported, but RxJS interop depends on also setting this fake\n// '@@observable' string as a polyfill for Symbol.observable.\nvar prototype = Observable.prototype;\nvar fakeObsSymbol = \"@@observable\";\nif (!prototype[fakeObsSymbol]) {\n    // @ts-expect-error\n    prototype[fakeObsSymbol] = function () {\n        return this;\n    };\n}\nexport { Observable };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C;AACA;AACA,OAAO,mBAAmB;AAC1B;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,UAAU,CAACC,SAAS;AACpC,IAAIC,aAAa,GAAG,cAAc;AAClC,IAAI,CAACD,SAAS,CAACC,aAAa,CAAC,EAAE;EAC3B;EACAD,SAAS,CAACC,aAAa,CAAC,GAAG,YAAY;IACnC,OAAO,IAAI;EACf,CAAC;AACL;AACA,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}