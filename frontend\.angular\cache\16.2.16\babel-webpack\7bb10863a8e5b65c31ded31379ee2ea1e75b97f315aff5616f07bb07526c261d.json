{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport class AuthadminService {\n  constructor(http, jwtHelper) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n  }\n  // login\n  login(body) {\n    return this.http.post(`${environment.urlBackend}auth/login`, body);\n  }\n  saveDataProfil(token) {\n    localStorage.setItem('token', token);\n  }\n  getUser() {\n    let token = localStorage.getItem('token');\n    let decodedToken = this.jwtHelper.decodeToken(token);\n    return decodedToken;\n  }\n  loggedIn() {\n    let token = localStorage.getItem('token');\n    if (!token) {\n      return false;\n    }\n    if (this.jwtHelper.decodeToken(token).role !== 'admin') {\n      return false;\n    }\n    if (this.jwtHelper.isTokenExpired(token)) {\n      return false;\n    }\n    return true;\n  }\n  clearAuthData() {\n    localStorage.removeItem('token');\n  }\n  static {\n    this.ɵfac = function AuthadminService_Factory(t) {\n      return new (t || AuthadminService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthadminService,\n      factory: AuthadminService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "AuthadminService", "constructor", "http", "jwtHelper", "login", "body", "post", "urlBackend", "saveDataProfil", "token", "localStorage", "setItem", "getUser", "getItem", "decodedToken", "decodeToken", "loggedIn", "role", "isTokenExpired", "clearAuthData", "removeItem", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\authadmin.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { User } from '../models/user.model';\r\nimport { Observable } from 'rxjs';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\nimport { environment } from 'src/environments/environment';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthadminService {\r\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService) {}\r\n  // login\r\n  login(body: Partial<User>): Observable<User> {\r\n    return this.http.post<User>(`${environment.urlBackend}auth/login`, body);\r\n  }\r\n  saveDataProfil(token: any) {\r\n    localStorage.setItem('token', token);\r\n  }\r\n  getUser() {\r\n    let token: any = localStorage.getItem('token');\r\n    let decodedToken = this.jwtHelper.decodeToken(token);\r\n    return decodedToken;\r\n  }\r\n\r\n  loggedIn() {\r\n    let token: any = localStorage.getItem('token');\r\n    if (!token) {\r\n      return false;\r\n    }\r\n    if (this.jwtHelper.decodeToken(token).role !== 'admin') {\r\n      return false;\r\n    }\r\n    if (this.jwtHelper.isTokenExpired(token)) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n  clearAuthData(): void {\r\n    localStorage.removeItem('token');\r\n  }\r\n}\r\n"], "mappings": "AAKA,SAASA,WAAW,QAAQ,8BAA8B;;;;AAI1D,OAAM,MAAOC,gBAAgB;EAC3BC,YAAoBC,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EAAqB;EAC5E;EACAC,KAAKA,CAACC,IAAmB;IACvB,OAAO,IAAI,CAACH,IAAI,CAACI,IAAI,CAAO,GAAGP,WAAW,CAACQ,UAAU,YAAY,EAAEF,IAAI,CAAC;EAC1E;EACAG,cAAcA,CAACC,KAAU;IACvBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,KAAK,CAAC;EACtC;EACAG,OAAOA,CAAA;IACL,IAAIH,KAAK,GAAQC,YAAY,CAACG,OAAO,CAAC,OAAO,CAAC;IAC9C,IAAIC,YAAY,GAAG,IAAI,CAACX,SAAS,CAACY,WAAW,CAACN,KAAK,CAAC;IACpD,OAAOK,YAAY;EACrB;EAEAE,QAAQA,CAAA;IACN,IAAIP,KAAK,GAAQC,YAAY,CAACG,OAAO,CAAC,OAAO,CAAC;IAC9C,IAAI,CAACJ,KAAK,EAAE;MACV,OAAO,KAAK;;IAEd,IAAI,IAAI,CAACN,SAAS,CAACY,WAAW,CAACN,KAAK,CAAC,CAACQ,IAAI,KAAK,OAAO,EAAE;MACtD,OAAO,KAAK;;IAEd,IAAI,IAAI,CAACd,SAAS,CAACe,cAAc,CAACT,KAAK,CAAC,EAAE;MACxC,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EACAU,aAAaA,CAAA;IACXT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;EAClC;;;uBA9BWpB,gBAAgB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAhB1B,gBAAgB;MAAA2B,OAAA,EAAhB3B,gBAAgB,CAAA4B,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}