{"ast": null, "code": "export var Cache;\n(function (Cache) {})(Cache || (Cache = {}));", "map": {"version": 3, "names": ["<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/core/types/Cache.js"], "sourcesContent": ["export var Cache;\n(function (Cache) {\n})(Cache || (Cache = {}));\n"], "mappings": "AAAA,OAAO,IAAIA,KAAK;AAChB,CAAC,UAAUA,KAAK,EAAE,CAClB,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}