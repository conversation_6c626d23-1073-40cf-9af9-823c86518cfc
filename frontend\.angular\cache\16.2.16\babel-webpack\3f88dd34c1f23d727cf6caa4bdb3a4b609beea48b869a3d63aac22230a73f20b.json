{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProjetService {\n  constructor(http) {\n    this.http = http;\n    // Correction de l'URL pour éviter la duplication de /api\n    this.apiUrl = `${environment.urlBackend}projets`;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getProjets() {\n    console.log('Appel API pour récupérer les projets:', this.apiUrl);\n    return this.http.get(this.apiUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(projets => console.log('Projets récupérés:', projets)), catchError(error => {\n      console.error('Erreur lors de la récupération des projets:', error);\n      return throwError(() => error);\n    }));\n  }\n  getProjetById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  addProjet(formData) {\n    // Pour les requêtes multipart/form-data, ne pas définir Content-Type\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\n    });\n    return this.http.post(`${this.apiUrl}/create`, formData, {\n      headers\n    }).pipe(tap(response => console.log('Projet ajouté:', response)), catchError(error => {\n      console.error('Erreur lors de l\\'ajout du projet:', error);\n      return throwError(() => error);\n    }));\n  }\n  updateProjet(id, projet) {\n    return this.http.put(`${this.apiUrl}/update/${id}`, projet, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  deleteProjet(id) {\n    // Assurez-vous que l'URL est correcte\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(response => console.log('Projet supprimé:', response)), catchError(error => {\n      console.error('Erreur lors de la suppression du projet:', error);\n      return throwError(() => error);\n    }));\n  }\n  uploadFile(file) {\n    const formData = new FormData();\n    formData.append('file', file);\n    // Utiliser les headers sans Content-Type pour permettre au navigateur de définir le boundary correct\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\n    });\n    return this.http.post(`${this.apiUrl}/uploads`, formData, {\n      headers\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  static {\n    this.ɵfac = function ProjetService_Factory(t) {\n      return new (t || ProjetService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProjetService,\n      factory: ProjetService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "catchError", "tap", "environment", "ProjetService", "constructor", "http", "apiUrl", "urlBackend", "getHeaders", "token", "localStorage", "getItem", "getProjets", "console", "log", "get", "headers", "pipe", "projets", "error", "getProjetById", "id", "addProjet", "formData", "post", "response", "updateProjet", "projet", "put", "deleteProjet", "delete", "uploadFile", "file", "FormData", "append", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\projets.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { Projet } from '../models/projet.model';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProjetService {\r\n  // Correction de l'URL pour éviter la duplication de /api\r\n  private apiUrl = `${environment.urlBackend}projets`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  getProjets(): Observable<Projet[]> {\r\n    console.log('Appel API pour récupérer les projets:', this.apiUrl);\r\n    return this.http.get<Projet[]>(this.apiUrl, { headers: this.getHeaders() })\r\n      .pipe(\r\n        tap(projets => console.log('Projets récupérés:', projets)),\r\n        catchError(error => {\r\n          console.error('Erreur lors de la récupération des projets:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  getProjetById(id: string): Observable<Projet> {\r\n    return this.http.get<Projet>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() })\r\n      .pipe(\r\n        catchError(error => throwError(() => error))\r\n      );\r\n  }\r\n\r\n  addProjet(formData: FormData): Observable<any> {\r\n    // Pour les requêtes multipart/form-data, ne pas définir Content-Type\r\n    const headers = new HttpHeaders({\r\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n    });\r\n    \r\n    return this.http.post(`${this.apiUrl}/create`, formData, { headers })\r\n      .pipe(\r\n        tap(response => console.log('Projet ajouté:', response)),\r\n        catchError(error => {\r\n          console.error('Erreur lors de l\\'ajout du projet:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  updateProjet(id: string, projet: Projet): Observable<Projet> {\r\n    return this.http.put<Projet>(`${this.apiUrl}/update/${id}`, projet, { headers: this.getHeaders() })\r\n      .pipe(\r\n        catchError(error => throwError(() => error))\r\n      );\r\n  }\r\n\r\n  deleteProjet(id: string): Observable<any> {\r\n    // Assurez-vous que l'URL est correcte\r\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { headers: this.getHeaders() })\r\n      .pipe(\r\n        tap(response => console.log('Projet supprimé:', response)),\r\n        catchError(error => {\r\n          console.error('Erreur lors de la suppression du projet:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  uploadFile(file: File): Observable<any> {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    \r\n    // Utiliser les headers sans Content-Type pour permettre au navigateur de définir le boundary correct\r\n    const headers = new HttpHeaders({\r\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n    });\r\n    \r\n    return this.http.post(`${this.apiUrl}/uploads`, formData, { headers })\r\n      .pipe(\r\n        catchError(error => throwError(() => error))\r\n      );\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAEhD,SAASC,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,aAAa;EAIxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHxB;IACQ,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,SAAS;EAEX;EAEhCC,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIb,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUW,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEAG,UAAUA,CAAA;IACRC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACR,MAAM,CAAC;IACjE,OAAO,IAAI,CAACD,IAAI,CAACU,GAAG,CAAW,IAAI,CAACT,MAAM,EAAE;MAAEU,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACxES,IAAI,CACHhB,GAAG,CAACiB,OAAO,IAAIL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,OAAO,CAAC,CAAC,EAC1DlB,UAAU,CAACmB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,OAAOpB,UAAU,CAAC,MAAMoB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAC,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAAChB,IAAI,CAACU,GAAG,CAAS,GAAG,IAAI,CAACT,MAAM,IAAIe,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACjFS,IAAI,CACHjB,UAAU,CAACmB,KAAK,IAAIpB,UAAU,CAAC,MAAMoB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAG,SAASA,CAACC,QAAkB;IAC1B;IACA,MAAMP,OAAO,GAAG,IAAIlB,WAAW,CAAC;MAC9B,eAAe,EAAE,UAAUY,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;KACzD,CAAC;IAEF,OAAO,IAAI,CAACN,IAAI,CAACmB,IAAI,CAAC,GAAG,IAAI,CAAClB,MAAM,SAAS,EAAEiB,QAAQ,EAAE;MAAEP;IAAO,CAAE,CAAC,CAClEC,IAAI,CACHhB,GAAG,CAACwB,QAAQ,IAAIZ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,QAAQ,CAAC,CAAC,EACxDzB,UAAU,CAACmB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAOpB,UAAU,CAAC,MAAMoB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAO,YAAYA,CAACL,EAAU,EAAEM,MAAc;IACrC,OAAO,IAAI,CAACtB,IAAI,CAACuB,GAAG,CAAS,GAAG,IAAI,CAACtB,MAAM,WAAWe,EAAE,EAAE,EAAEM,MAAM,EAAE;MAAEX,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CAChGS,IAAI,CACHjB,UAAU,CAACmB,KAAK,IAAIpB,UAAU,CAAC,MAAMoB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAU,YAAYA,CAACR,EAAU;IACrB;IACA,OAAO,IAAI,CAAChB,IAAI,CAACyB,MAAM,CAAC,GAAG,IAAI,CAACxB,MAAM,WAAWe,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACR,UAAU;IAAE,CAAE,CAAC,CACnFS,IAAI,CACHhB,GAAG,CAACwB,QAAQ,IAAIZ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEW,QAAQ,CAAC,CAAC,EAC1DzB,UAAU,CAACmB,KAAK,IAAG;MACjBN,OAAO,CAACM,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,OAAOpB,UAAU,CAAC,MAAMoB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAY,UAAUA,CAACC,IAAU;IACnB,MAAMT,QAAQ,GAAG,IAAIU,QAAQ,EAAE;IAC/BV,QAAQ,CAACW,MAAM,CAAC,MAAM,EAAEF,IAAI,CAAC;IAE7B;IACA,MAAMhB,OAAO,GAAG,IAAIlB,WAAW,CAAC;MAC9B,eAAe,EAAE,UAAUY,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;KACzD,CAAC;IAEF,OAAO,IAAI,CAACN,IAAI,CAACmB,IAAI,CAAC,GAAG,IAAI,CAAClB,MAAM,UAAU,EAAEiB,QAAQ,EAAE;MAAEP;IAAO,CAAE,CAAC,CACnEC,IAAI,CACHjB,UAAU,CAACmB,KAAK,IAAIpB,UAAU,CAAC,MAAMoB,KAAK,CAAC,CAAC,CAC7C;EACL;;;uBAjFWhB,aAAa,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbnC,aAAa;MAAAoC,OAAA,EAAbpC,aAAa,CAAAqC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}