{"ast": null, "code": "import { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\nexport function graphQLResultHasError(result) {\n  var errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\nexport function getGraphQLErrorsFromResult(result) {\n  var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n  if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n    result.incremental.forEach(function (incrementalResult) {\n      if (incrementalResult.errors) {\n        graphQLErrors.push.apply(graphQLErrors, incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}", "map": {"version": 3, "names": ["isNonEmptyArray", "isExecutionPatchIncrementalResult", "graphQLResultHasError", "result", "errors", "getGraphQLErrorsFromResult", "graphQLErrors", "slice", "incremental", "for<PERSON>ach", "incrementalResult", "push", "apply"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/errorHandling.js"], "sourcesContent": ["import { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\nexport function graphQLResultHasError(result) {\n    var errors = getGraphQLErrorsFromResult(result);\n    return isNonEmptyArray(errors);\n}\nexport function getGraphQLErrorsFromResult(result) {\n    var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (incrementalResult) {\n            if (incrementalResult.errors) {\n                graphQLErrors.push.apply(graphQLErrors, incrementalResult.errors);\n            }\n        });\n    }\n    return graphQLErrors;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,aAAa;AAC7C,SAASC,iCAAiC,QAAQ,wBAAwB;AAC1E,OAAO,SAASC,qBAAqBA,CAACC,MAAM,EAAE;EAC1C,IAAIC,MAAM,GAAGC,0BAA0B,CAACF,MAAM,CAAC;EAC/C,OAAOH,eAAe,CAACI,MAAM,CAAC;AAClC;AACA,OAAO,SAASC,0BAA0BA,CAACF,MAAM,EAAE;EAC/C,IAAIG,aAAa,GAAGN,eAAe,CAACG,MAAM,CAACC,MAAM,CAAC,GAAGD,MAAM,CAACC,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAChF,IAAIN,iCAAiC,CAACE,MAAM,CAAC,IACzCH,eAAe,CAACG,MAAM,CAACK,WAAW,CAAC,EAAE;IACrCL,MAAM,CAACK,WAAW,CAACC,OAAO,CAAC,UAAUC,iBAAiB,EAAE;MACpD,IAAIA,iBAAiB,CAACN,MAAM,EAAE;QAC1BE,aAAa,CAACK,IAAI,CAACC,KAAK,CAACN,aAAa,EAAEI,iBAAiB,CAACN,MAAM,CAAC;MACrE;IACJ,CAAC,CAAC;EACN;EACA,OAAOE,aAAa;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}