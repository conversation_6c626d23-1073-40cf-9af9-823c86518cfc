{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nexport class Source {\n  constructor(body, name = 'GraphQL request', locationOffset = {\n    line: 1,\n    column: 1\n  }) {\n    typeof body === 'string' || devAssert(false, `Body must be a string. Received: ${inspect(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 || devAssert(false, 'line in locationOffset is 1-indexed and must be positive.');\n    this.locationOffset.column > 0 || devAssert(false, 'column in locationOffset is 1-indexed and must be positive.');\n  }\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nexport function isSource(source) {\n  return instanceOf(source, Source);\n}", "map": {"version": 3, "names": ["devAssert", "inspect", "instanceOf", "Source", "constructor", "body", "name", "locationOffset", "line", "column", "Symbol", "toStringTag", "isSource", "source"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/language/source.mjs"], "sourcesContent": ["import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nexport class Source {\n  constructor(\n    body,\n    name = 'GraphQL request',\n    locationOffset = {\n      line: 1,\n      column: 1,\n    },\n  ) {\n    typeof body === 'string' ||\n      devAssert(false, `Body must be a string. Received: ${inspect(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 ||\n      devAssert(\n        false,\n        'line in locationOffset is 1-indexed and must be positive.',\n      );\n    this.locationOffset.column > 0 ||\n      devAssert(\n        false,\n        'column in locationOffset is 1-indexed and must be positive.',\n      );\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nexport function isSource(source) {\n  return instanceOf(source, Source);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,QAAQ,2BAA2B;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,CAAC;EAClBC,WAAWA,CACTC,IAAI,EACJC,IAAI,GAAG,iBAAiB,EACxBC,cAAc,GAAG;IACfC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE;EACV,CAAC,EACD;IACA,OAAOJ,IAAI,KAAK,QAAQ,IACtBL,SAAS,CAAC,KAAK,EAAG,oCAAmCC,OAAO,CAACI,IAAI,CAAE,GAAE,CAAC;IACxE,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACA,cAAc,CAACC,IAAI,GAAG,CAAC,IAC1BR,SAAS,CACP,KAAK,EACL,2DACF,CAAC;IACH,IAAI,CAACO,cAAc,CAACE,MAAM,GAAG,CAAC,IAC5BT,SAAS,CACP,KAAK,EACL,6DACF,CAAC;EACL;EAEA,KAAKU,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,QAAQ;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,QAAQA,CAACC,MAAM,EAAE;EAC/B,OAAOX,UAAU,CAACW,MAAM,EAAEV,MAAM,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}