{"ast": null, "code": "export var selectURI = function (operation, fallbackURI) {\n  var context = operation.getContext();\n  var contextURI = context.uri;\n  if (contextURI) {\n    return contextURI;\n  } else if (typeof fallbackURI === \"function\") {\n    return fallbackURI(operation);\n  } else {\n    return fallbackURI || \"/graphql\";\n  }\n};", "map": {"version": 3, "names": ["selectURI", "operation", "fallbackURI", "context", "getContext", "contextURI", "uri"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/selectURI.js"], "sourcesContent": ["export var selectURI = function (operation, fallbackURI) {\n    var context = operation.getContext();\n    var contextURI = context.uri;\n    if (contextURI) {\n        return contextURI;\n    }\n    else if (typeof fallbackURI === \"function\") {\n        return fallbackURI(operation);\n    }\n    else {\n        return fallbackURI || \"/graphql\";\n    }\n};\n"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG,SAAAA,CAAUC,SAAS,EAAEC,WAAW,EAAE;EACrD,IAAIC,OAAO,GAAGF,SAAS,CAACG,UAAU,CAAC,CAAC;EACpC,IAAIC,UAAU,GAAGF,OAAO,CAACG,GAAG;EAC5B,IAAID,UAAU,EAAE;IACZ,OAAOA,UAAU;EACrB,CAAC,MACI,IAAI,OAAOH,WAAW,KAAK,UAAU,EAAE;IACxC,OAAOA,WAAW,CAACD,SAAS,CAAC;EACjC,CAAC,MACI;IACD,OAAOC,WAAW,IAAI,UAAU;EACpC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}