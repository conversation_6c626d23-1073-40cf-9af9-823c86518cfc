{"ast": null, "code": "import { isNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { getEnterLeaveForKind } from '../language/visitor.mjs';\nimport { getNamedType, getNullableType, isCompositeType, isEnumType, isInputObjectType, isInputType, isInterfaceType, isListType, isObjectType, isOutputType } from '../type/definition.mjs';\nimport { SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef } from '../type/introspection.mjs';\nimport { typeFromAST } from './typeFromAST.mjs';\n/**\n * TypeInfo is a utility class which, given a GraphQL schema, can keep track\n * of the current field and type definitions at any point in a GraphQL document\n * AST during a recursive descent by calling `enter(node)` and `leave(node)`.\n */\n\nexport class TypeInfo {\n  constructor(schema,\n  /**\n   * Initial type may be provided in rare cases to facilitate traversals\n   *  beginning somewhere other than documents.\n   */\n  initialType, /** @deprecated will be removed in 17.0.0 */\n  getFieldDefFn) {\n    this._schema = schema;\n    this._typeStack = [];\n    this._parentTypeStack = [];\n    this._inputTypeStack = [];\n    this._fieldDefStack = [];\n    this._defaultValueStack = [];\n    this._directive = null;\n    this._argument = null;\n    this._enumValue = null;\n    this._getFieldDef = getFieldDefFn !== null && getFieldDefFn !== void 0 ? getFieldDefFn : getFieldDef;\n    if (initialType) {\n      if (isInputType(initialType)) {\n        this._inputTypeStack.push(initialType);\n      }\n      if (isCompositeType(initialType)) {\n        this._parentTypeStack.push(initialType);\n      }\n      if (isOutputType(initialType)) {\n        this._typeStack.push(initialType);\n      }\n    }\n  }\n  get [Symbol.toStringTag]() {\n    return 'TypeInfo';\n  }\n  getType() {\n    if (this._typeStack.length > 0) {\n      return this._typeStack[this._typeStack.length - 1];\n    }\n  }\n  getParentType() {\n    if (this._parentTypeStack.length > 0) {\n      return this._parentTypeStack[this._parentTypeStack.length - 1];\n    }\n  }\n  getInputType() {\n    if (this._inputTypeStack.length > 0) {\n      return this._inputTypeStack[this._inputTypeStack.length - 1];\n    }\n  }\n  getParentInputType() {\n    if (this._inputTypeStack.length > 1) {\n      return this._inputTypeStack[this._inputTypeStack.length - 2];\n    }\n  }\n  getFieldDef() {\n    if (this._fieldDefStack.length > 0) {\n      return this._fieldDefStack[this._fieldDefStack.length - 1];\n    }\n  }\n  getDefaultValue() {\n    if (this._defaultValueStack.length > 0) {\n      return this._defaultValueStack[this._defaultValueStack.length - 1];\n    }\n  }\n  getDirective() {\n    return this._directive;\n  }\n  getArgument() {\n    return this._argument;\n  }\n  getEnumValue() {\n    return this._enumValue;\n  }\n  enter(node) {\n    const schema = this._schema; // Note: many of the types below are explicitly typed as \"unknown\" to drop\n    // any assumptions of a valid schema to ensure runtime types are properly\n    // checked before continuing since TypeInfo is used as part of validation\n    // which occurs before guarantees of schema and document validity.\n\n    switch (node.kind) {\n      case Kind.SELECTION_SET:\n        {\n          const namedType = getNamedType(this.getType());\n          this._parentTypeStack.push(isCompositeType(namedType) ? namedType : undefined);\n          break;\n        }\n      case Kind.FIELD:\n        {\n          const parentType = this.getParentType();\n          let fieldDef;\n          let fieldType;\n          if (parentType) {\n            fieldDef = this._getFieldDef(schema, parentType, node);\n            if (fieldDef) {\n              fieldType = fieldDef.type;\n            }\n          }\n          this._fieldDefStack.push(fieldDef);\n          this._typeStack.push(isOutputType(fieldType) ? fieldType : undefined);\n          break;\n        }\n      case Kind.DIRECTIVE:\n        this._directive = schema.getDirective(node.name.value);\n        break;\n      case Kind.OPERATION_DEFINITION:\n        {\n          const rootType = schema.getRootType(node.operation);\n          this._typeStack.push(isObjectType(rootType) ? rootType : undefined);\n          break;\n        }\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION:\n        {\n          const typeConditionAST = node.typeCondition;\n          const outputType = typeConditionAST ? typeFromAST(schema, typeConditionAST) : getNamedType(this.getType());\n          this._typeStack.push(isOutputType(outputType) ? outputType : undefined);\n          break;\n        }\n      case Kind.VARIABLE_DEFINITION:\n        {\n          const inputType = typeFromAST(schema, node.type);\n          this._inputTypeStack.push(isInputType(inputType) ? inputType : undefined);\n          break;\n        }\n      case Kind.ARGUMENT:\n        {\n          var _this$getDirective;\n          let argDef;\n          let argType;\n          const fieldOrDirective = (_this$getDirective = this.getDirective()) !== null && _this$getDirective !== void 0 ? _this$getDirective : this.getFieldDef();\n          if (fieldOrDirective) {\n            argDef = fieldOrDirective.args.find(arg => arg.name === node.name.value);\n            if (argDef) {\n              argType = argDef.type;\n            }\n          }\n          this._argument = argDef;\n          this._defaultValueStack.push(argDef ? argDef.defaultValue : undefined);\n          this._inputTypeStack.push(isInputType(argType) ? argType : undefined);\n          break;\n        }\n      case Kind.LIST:\n        {\n          const listType = getNullableType(this.getInputType());\n          const itemType = isListType(listType) ? listType.ofType : listType; // List positions never have a default value.\n\n          this._defaultValueStack.push(undefined);\n          this._inputTypeStack.push(isInputType(itemType) ? itemType : undefined);\n          break;\n        }\n      case Kind.OBJECT_FIELD:\n        {\n          const objectType = getNamedType(this.getInputType());\n          let inputFieldType;\n          let inputField;\n          if (isInputObjectType(objectType)) {\n            inputField = objectType.getFields()[node.name.value];\n            if (inputField) {\n              inputFieldType = inputField.type;\n            }\n          }\n          this._defaultValueStack.push(inputField ? inputField.defaultValue : undefined);\n          this._inputTypeStack.push(isInputType(inputFieldType) ? inputFieldType : undefined);\n          break;\n        }\n      case Kind.ENUM:\n        {\n          const enumType = getNamedType(this.getInputType());\n          let enumValue;\n          if (isEnumType(enumType)) {\n            enumValue = enumType.getValue(node.value);\n          }\n          this._enumValue = enumValue;\n          break;\n        }\n      default: // Ignore other nodes\n    }\n  }\n\n  leave(node) {\n    switch (node.kind) {\n      case Kind.SELECTION_SET:\n        this._parentTypeStack.pop();\n        break;\n      case Kind.FIELD:\n        this._fieldDefStack.pop();\n        this._typeStack.pop();\n        break;\n      case Kind.DIRECTIVE:\n        this._directive = null;\n        break;\n      case Kind.OPERATION_DEFINITION:\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION:\n        this._typeStack.pop();\n        break;\n      case Kind.VARIABLE_DEFINITION:\n        this._inputTypeStack.pop();\n        break;\n      case Kind.ARGUMENT:\n        this._argument = null;\n        this._defaultValueStack.pop();\n        this._inputTypeStack.pop();\n        break;\n      case Kind.LIST:\n      case Kind.OBJECT_FIELD:\n        this._defaultValueStack.pop();\n        this._inputTypeStack.pop();\n        break;\n      case Kind.ENUM:\n        this._enumValue = null;\n        break;\n      default: // Ignore other nodes\n    }\n  }\n}\n\n/**\n * Not exactly the same as the executor's definition of getFieldDef, in this\n * statically evaluated environment we do not always have an Object type,\n * and need to handle Interface and Union types.\n */\nfunction getFieldDef(schema, parentType, fieldNode) {\n  const name = fieldNode.name.value;\n  if (name === SchemaMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return SchemaMetaFieldDef;\n  }\n  if (name === TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return TypeMetaFieldDef;\n  }\n  if (name === TypeNameMetaFieldDef.name && isCompositeType(parentType)) {\n    return TypeNameMetaFieldDef;\n  }\n  if (isObjectType(parentType) || isInterfaceType(parentType)) {\n    return parentType.getFields()[name];\n  }\n}\n/**\n * Creates a new visitor instance which maintains a provided TypeInfo instance\n * along with visiting visitor.\n */\n\nexport function visitWithTypeInfo(typeInfo, visitor) {\n  return {\n    enter(...args) {\n      const node = args[0];\n      typeInfo.enter(node);\n      const fn = getEnterLeaveForKind(visitor, node.kind).enter;\n      if (fn) {\n        const result = fn.apply(visitor, args);\n        if (result !== undefined) {\n          typeInfo.leave(node);\n          if (isNode(result)) {\n            typeInfo.enter(result);\n          }\n        }\n        return result;\n      }\n    },\n    leave(...args) {\n      const node = args[0];\n      const fn = getEnterLeaveForKind(visitor, node.kind).leave;\n      let result;\n      if (fn) {\n        result = fn.apply(visitor, args);\n      }\n      typeInfo.leave(node);\n      return result;\n    }\n  };\n}", "map": {"version": 3, "names": ["isNode", "Kind", "getEnterLeaveForKind", "getNamedType", "getNullableType", "isCompositeType", "isEnumType", "isInputObjectType", "isInputType", "isInterfaceType", "isListType", "isObjectType", "isOutputType", "SchemaMetaFieldDef", "TypeMetaFieldDef", "TypeNameMetaFieldDef", "typeFromAST", "TypeInfo", "constructor", "schema", "initialType", "getFieldDefFn", "_schema", "_typeStack", "_parentTypeStack", "_inputTypeStack", "_fieldDefStack", "_defaultValueStack", "_directive", "_argument", "_enumValue", "_getFieldDef", "getFieldDef", "push", "Symbol", "toStringTag", "getType", "length", "getParentType", "getInputType", "getParentInputType", "getDefaultValue", "getDirective", "getArgument", "getEnumValue", "enter", "node", "kind", "SELECTION_SET", "namedType", "undefined", "FIELD", "parentType", "fieldDef", "fieldType", "type", "DIRECTIVE", "name", "value", "OPERATION_DEFINITION", "rootType", "getRootType", "operation", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "typeConditionAST", "typeCondition", "outputType", "VARIABLE_DEFINITION", "inputType", "ARGUMENT", "_this$getDirective", "argDef", "argType", "fieldOrDirective", "args", "find", "arg", "defaultValue", "LIST", "listType", "itemType", "ofType", "OBJECT_FIELD", "objectType", "inputFieldType", "inputField", "getFields", "ENUM", "enumType", "enumValue", "getValue", "leave", "pop", "fieldNode", "getQueryType", "visitWithTypeInfo", "typeInfo", "visitor", "fn", "result", "apply"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/utilities/TypeInfo.mjs"], "sourcesContent": ["import { isNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { getEnterLeaveForKind } from '../language/visitor.mjs';\nimport {\n  getNamedType,\n  getNullableType,\n  isCompositeType,\n  isEnumType,\n  isInputObjectType,\n  isInputType,\n  isInterfaceType,\n  isListType,\n  isObjectType,\n  isOutputType,\n} from '../type/definition.mjs';\nimport {\n  SchemaMetaFieldDef,\n  TypeMetaFieldDef,\n  TypeNameMetaFieldDef,\n} from '../type/introspection.mjs';\nimport { typeFromAST } from './typeFromAST.mjs';\n/**\n * TypeInfo is a utility class which, given a GraphQL schema, can keep track\n * of the current field and type definitions at any point in a GraphQL document\n * AST during a recursive descent by calling `enter(node)` and `leave(node)`.\n */\n\nexport class TypeInfo {\n  constructor(\n    schema,\n    /**\n     * Initial type may be provided in rare cases to facilitate traversals\n     *  beginning somewhere other than documents.\n     */\n    initialType,\n    /** @deprecated will be removed in 17.0.0 */\n    getFieldDefFn,\n  ) {\n    this._schema = schema;\n    this._typeStack = [];\n    this._parentTypeStack = [];\n    this._inputTypeStack = [];\n    this._fieldDefStack = [];\n    this._defaultValueStack = [];\n    this._directive = null;\n    this._argument = null;\n    this._enumValue = null;\n    this._getFieldDef =\n      getFieldDefFn !== null && getFieldDefFn !== void 0\n        ? getFieldDefFn\n        : getFieldDef;\n\n    if (initialType) {\n      if (isInputType(initialType)) {\n        this._inputTypeStack.push(initialType);\n      }\n\n      if (isCompositeType(initialType)) {\n        this._parentTypeStack.push(initialType);\n      }\n\n      if (isOutputType(initialType)) {\n        this._typeStack.push(initialType);\n      }\n    }\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'TypeInfo';\n  }\n\n  getType() {\n    if (this._typeStack.length > 0) {\n      return this._typeStack[this._typeStack.length - 1];\n    }\n  }\n\n  getParentType() {\n    if (this._parentTypeStack.length > 0) {\n      return this._parentTypeStack[this._parentTypeStack.length - 1];\n    }\n  }\n\n  getInputType() {\n    if (this._inputTypeStack.length > 0) {\n      return this._inputTypeStack[this._inputTypeStack.length - 1];\n    }\n  }\n\n  getParentInputType() {\n    if (this._inputTypeStack.length > 1) {\n      return this._inputTypeStack[this._inputTypeStack.length - 2];\n    }\n  }\n\n  getFieldDef() {\n    if (this._fieldDefStack.length > 0) {\n      return this._fieldDefStack[this._fieldDefStack.length - 1];\n    }\n  }\n\n  getDefaultValue() {\n    if (this._defaultValueStack.length > 0) {\n      return this._defaultValueStack[this._defaultValueStack.length - 1];\n    }\n  }\n\n  getDirective() {\n    return this._directive;\n  }\n\n  getArgument() {\n    return this._argument;\n  }\n\n  getEnumValue() {\n    return this._enumValue;\n  }\n\n  enter(node) {\n    const schema = this._schema; // Note: many of the types below are explicitly typed as \"unknown\" to drop\n    // any assumptions of a valid schema to ensure runtime types are properly\n    // checked before continuing since TypeInfo is used as part of validation\n    // which occurs before guarantees of schema and document validity.\n\n    switch (node.kind) {\n      case Kind.SELECTION_SET: {\n        const namedType = getNamedType(this.getType());\n\n        this._parentTypeStack.push(\n          isCompositeType(namedType) ? namedType : undefined,\n        );\n\n        break;\n      }\n\n      case Kind.FIELD: {\n        const parentType = this.getParentType();\n        let fieldDef;\n        let fieldType;\n\n        if (parentType) {\n          fieldDef = this._getFieldDef(schema, parentType, node);\n\n          if (fieldDef) {\n            fieldType = fieldDef.type;\n          }\n        }\n\n        this._fieldDefStack.push(fieldDef);\n\n        this._typeStack.push(isOutputType(fieldType) ? fieldType : undefined);\n\n        break;\n      }\n\n      case Kind.DIRECTIVE:\n        this._directive = schema.getDirective(node.name.value);\n        break;\n\n      case Kind.OPERATION_DEFINITION: {\n        const rootType = schema.getRootType(node.operation);\n\n        this._typeStack.push(isObjectType(rootType) ? rootType : undefined);\n\n        break;\n      }\n\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION: {\n        const typeConditionAST = node.typeCondition;\n        const outputType = typeConditionAST\n          ? typeFromAST(schema, typeConditionAST)\n          : getNamedType(this.getType());\n\n        this._typeStack.push(isOutputType(outputType) ? outputType : undefined);\n\n        break;\n      }\n\n      case Kind.VARIABLE_DEFINITION: {\n        const inputType = typeFromAST(schema, node.type);\n\n        this._inputTypeStack.push(\n          isInputType(inputType) ? inputType : undefined,\n        );\n\n        break;\n      }\n\n      case Kind.ARGUMENT: {\n        var _this$getDirective;\n\n        let argDef;\n        let argType;\n        const fieldOrDirective =\n          (_this$getDirective = this.getDirective()) !== null &&\n          _this$getDirective !== void 0\n            ? _this$getDirective\n            : this.getFieldDef();\n\n        if (fieldOrDirective) {\n          argDef = fieldOrDirective.args.find(\n            (arg) => arg.name === node.name.value,\n          );\n\n          if (argDef) {\n            argType = argDef.type;\n          }\n        }\n\n        this._argument = argDef;\n\n        this._defaultValueStack.push(argDef ? argDef.defaultValue : undefined);\n\n        this._inputTypeStack.push(isInputType(argType) ? argType : undefined);\n\n        break;\n      }\n\n      case Kind.LIST: {\n        const listType = getNullableType(this.getInputType());\n        const itemType = isListType(listType) ? listType.ofType : listType; // List positions never have a default value.\n\n        this._defaultValueStack.push(undefined);\n\n        this._inputTypeStack.push(isInputType(itemType) ? itemType : undefined);\n\n        break;\n      }\n\n      case Kind.OBJECT_FIELD: {\n        const objectType = getNamedType(this.getInputType());\n        let inputFieldType;\n        let inputField;\n\n        if (isInputObjectType(objectType)) {\n          inputField = objectType.getFields()[node.name.value];\n\n          if (inputField) {\n            inputFieldType = inputField.type;\n          }\n        }\n\n        this._defaultValueStack.push(\n          inputField ? inputField.defaultValue : undefined,\n        );\n\n        this._inputTypeStack.push(\n          isInputType(inputFieldType) ? inputFieldType : undefined,\n        );\n\n        break;\n      }\n\n      case Kind.ENUM: {\n        const enumType = getNamedType(this.getInputType());\n        let enumValue;\n\n        if (isEnumType(enumType)) {\n          enumValue = enumType.getValue(node.value);\n        }\n\n        this._enumValue = enumValue;\n        break;\n      }\n\n      default: // Ignore other nodes\n    }\n  }\n\n  leave(node) {\n    switch (node.kind) {\n      case Kind.SELECTION_SET:\n        this._parentTypeStack.pop();\n\n        break;\n\n      case Kind.FIELD:\n        this._fieldDefStack.pop();\n\n        this._typeStack.pop();\n\n        break;\n\n      case Kind.DIRECTIVE:\n        this._directive = null;\n        break;\n\n      case Kind.OPERATION_DEFINITION:\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION:\n        this._typeStack.pop();\n\n        break;\n\n      case Kind.VARIABLE_DEFINITION:\n        this._inputTypeStack.pop();\n\n        break;\n\n      case Kind.ARGUMENT:\n        this._argument = null;\n\n        this._defaultValueStack.pop();\n\n        this._inputTypeStack.pop();\n\n        break;\n\n      case Kind.LIST:\n      case Kind.OBJECT_FIELD:\n        this._defaultValueStack.pop();\n\n        this._inputTypeStack.pop();\n\n        break;\n\n      case Kind.ENUM:\n        this._enumValue = null;\n        break;\n\n      default: // Ignore other nodes\n    }\n  }\n}\n\n/**\n * Not exactly the same as the executor's definition of getFieldDef, in this\n * statically evaluated environment we do not always have an Object type,\n * and need to handle Interface and Union types.\n */\nfunction getFieldDef(schema, parentType, fieldNode) {\n  const name = fieldNode.name.value;\n\n  if (\n    name === SchemaMetaFieldDef.name &&\n    schema.getQueryType() === parentType\n  ) {\n    return SchemaMetaFieldDef;\n  }\n\n  if (name === TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return TypeMetaFieldDef;\n  }\n\n  if (name === TypeNameMetaFieldDef.name && isCompositeType(parentType)) {\n    return TypeNameMetaFieldDef;\n  }\n\n  if (isObjectType(parentType) || isInterfaceType(parentType)) {\n    return parentType.getFields()[name];\n  }\n}\n/**\n * Creates a new visitor instance which maintains a provided TypeInfo instance\n * along with visiting visitor.\n */\n\nexport function visitWithTypeInfo(typeInfo, visitor) {\n  return {\n    enter(...args) {\n      const node = args[0];\n      typeInfo.enter(node);\n      const fn = getEnterLeaveForKind(visitor, node.kind).enter;\n\n      if (fn) {\n        const result = fn.apply(visitor, args);\n\n        if (result !== undefined) {\n          typeInfo.leave(node);\n\n          if (isNode(result)) {\n            typeInfo.enter(result);\n          }\n        }\n\n        return result;\n      }\n    },\n\n    leave(...args) {\n      const node = args[0];\n      const fn = getEnterLeaveForKind(visitor, node.kind).leave;\n      let result;\n\n      if (fn) {\n        result = fn.apply(visitor, args);\n      }\n\n      typeInfo.leave(node);\n      return result;\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SACEC,YAAY,EACZC,eAAe,EACfC,eAAe,EACfC,UAAU,EACVC,iBAAiB,EACjBC,WAAW,EACXC,eAAe,EACfC,UAAU,EACVC,YAAY,EACZC,YAAY,QACP,wBAAwB;AAC/B,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,oBAAoB,QACf,2BAA2B;AAClC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,QAAQ,CAAC;EACpBC,WAAWA,CACTC,MAAM;EACN;AACJ;AACA;AACA;EACIC,WAAW,EACX;EACAC,aAAa,EACb;IACA,IAAI,CAACC,OAAO,GAAGH,MAAM;IACrB,IAAI,CAACI,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,YAAY,GACfV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAC9CA,aAAa,GACbW,WAAW;IAEjB,IAAIZ,WAAW,EAAE;MACf,IAAIZ,WAAW,CAACY,WAAW,CAAC,EAAE;QAC5B,IAAI,CAACK,eAAe,CAACQ,IAAI,CAACb,WAAW,CAAC;MACxC;MAEA,IAAIf,eAAe,CAACe,WAAW,CAAC,EAAE;QAChC,IAAI,CAACI,gBAAgB,CAACS,IAAI,CAACb,WAAW,CAAC;MACzC;MAEA,IAAIR,YAAY,CAACQ,WAAW,CAAC,EAAE;QAC7B,IAAI,CAACG,UAAU,CAACU,IAAI,CAACb,WAAW,CAAC;MACnC;IACF;EACF;EAEA,KAAKc,MAAM,CAACC,WAAW,IAAI;IACzB,OAAO,UAAU;EACnB;EAEAC,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACb,UAAU,CAACc,MAAM,GAAG,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACd,UAAU,CAAC,IAAI,CAACA,UAAU,CAACc,MAAM,GAAG,CAAC,CAAC;IACpD;EACF;EAEAC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACd,gBAAgB,CAACa,MAAM,GAAG,CAAC,EAAE;MACpC,OAAO,IAAI,CAACb,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACa,MAAM,GAAG,CAAC,CAAC;IAChE;EACF;EAEAE,YAAYA,CAAA,EAAG;IACb,IAAI,IAAI,CAACd,eAAe,CAACY,MAAM,GAAG,CAAC,EAAE;MACnC,OAAO,IAAI,CAACZ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACY,MAAM,GAAG,CAAC,CAAC;IAC9D;EACF;EAEAG,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACf,eAAe,CAACY,MAAM,GAAG,CAAC,EAAE;MACnC,OAAO,IAAI,CAACZ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACY,MAAM,GAAG,CAAC,CAAC;IAC9D;EACF;EAEAL,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACN,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MAClC,OAAO,IAAI,CAACX,cAAc,CAAC,IAAI,CAACA,cAAc,CAACW,MAAM,GAAG,CAAC,CAAC;IAC5D;EACF;EAEAI,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACd,kBAAkB,CAACU,MAAM,GAAG,CAAC,EAAE;MACtC,OAAO,IAAI,CAACV,kBAAkB,CAAC,IAAI,CAACA,kBAAkB,CAACU,MAAM,GAAG,CAAC,CAAC;IACpE;EACF;EAEAK,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACd,UAAU;EACxB;EAEAe,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,SAAS;EACvB;EAEAe,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACd,UAAU;EACxB;EAEAe,KAAKA,CAACC,IAAI,EAAE;IACV,MAAM3B,MAAM,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IAC7B;IACA;IACA;;IAEA,QAAQwB,IAAI,CAACC,IAAI;MACf,KAAK9C,IAAI,CAAC+C,aAAa;QAAE;UACvB,MAAMC,SAAS,GAAG9C,YAAY,CAAC,IAAI,CAACiC,OAAO,CAAC,CAAC,CAAC;UAE9C,IAAI,CAACZ,gBAAgB,CAACS,IAAI,CACxB5B,eAAe,CAAC4C,SAAS,CAAC,GAAGA,SAAS,GAAGC,SAC3C,CAAC;UAED;QACF;MAEA,KAAKjD,IAAI,CAACkD,KAAK;QAAE;UACf,MAAMC,UAAU,GAAG,IAAI,CAACd,aAAa,CAAC,CAAC;UACvC,IAAIe,QAAQ;UACZ,IAAIC,SAAS;UAEb,IAAIF,UAAU,EAAE;YACdC,QAAQ,GAAG,IAAI,CAACtB,YAAY,CAACZ,MAAM,EAAEiC,UAAU,EAAEN,IAAI,CAAC;YAEtD,IAAIO,QAAQ,EAAE;cACZC,SAAS,GAAGD,QAAQ,CAACE,IAAI;YAC3B;UACF;UAEA,IAAI,CAAC7B,cAAc,CAACO,IAAI,CAACoB,QAAQ,CAAC;UAElC,IAAI,CAAC9B,UAAU,CAACU,IAAI,CAACrB,YAAY,CAAC0C,SAAS,CAAC,GAAGA,SAAS,GAAGJ,SAAS,CAAC;UAErE;QACF;MAEA,KAAKjD,IAAI,CAACuD,SAAS;QACjB,IAAI,CAAC5B,UAAU,GAAGT,MAAM,CAACuB,YAAY,CAACI,IAAI,CAACW,IAAI,CAACC,KAAK,CAAC;QACtD;MAEF,KAAKzD,IAAI,CAAC0D,oBAAoB;QAAE;UAC9B,MAAMC,QAAQ,GAAGzC,MAAM,CAAC0C,WAAW,CAACf,IAAI,CAACgB,SAAS,CAAC;UAEnD,IAAI,CAACvC,UAAU,CAACU,IAAI,CAACtB,YAAY,CAACiD,QAAQ,CAAC,GAAGA,QAAQ,GAAGV,SAAS,CAAC;UAEnE;QACF;MAEA,KAAKjD,IAAI,CAAC8D,eAAe;MACzB,KAAK9D,IAAI,CAAC+D,mBAAmB;QAAE;UAC7B,MAAMC,gBAAgB,GAAGnB,IAAI,CAACoB,aAAa;UAC3C,MAAMC,UAAU,GAAGF,gBAAgB,GAC/BjD,WAAW,CAACG,MAAM,EAAE8C,gBAAgB,CAAC,GACrC9D,YAAY,CAAC,IAAI,CAACiC,OAAO,CAAC,CAAC,CAAC;UAEhC,IAAI,CAACb,UAAU,CAACU,IAAI,CAACrB,YAAY,CAACuD,UAAU,CAAC,GAAGA,UAAU,GAAGjB,SAAS,CAAC;UAEvE;QACF;MAEA,KAAKjD,IAAI,CAACmE,mBAAmB;QAAE;UAC7B,MAAMC,SAAS,GAAGrD,WAAW,CAACG,MAAM,EAAE2B,IAAI,CAACS,IAAI,CAAC;UAEhD,IAAI,CAAC9B,eAAe,CAACQ,IAAI,CACvBzB,WAAW,CAAC6D,SAAS,CAAC,GAAGA,SAAS,GAAGnB,SACvC,CAAC;UAED;QACF;MAEA,KAAKjD,IAAI,CAACqE,QAAQ;QAAE;UAClB,IAAIC,kBAAkB;UAEtB,IAAIC,MAAM;UACV,IAAIC,OAAO;UACX,MAAMC,gBAAgB,GACpB,CAACH,kBAAkB,GAAG,IAAI,CAAC7B,YAAY,CAAC,CAAC,MAAM,IAAI,IACnD6B,kBAAkB,KAAK,KAAK,CAAC,GACzBA,kBAAkB,GAClB,IAAI,CAACvC,WAAW,CAAC,CAAC;UAExB,IAAI0C,gBAAgB,EAAE;YACpBF,MAAM,GAAGE,gBAAgB,CAACC,IAAI,CAACC,IAAI,CAChCC,GAAG,IAAKA,GAAG,CAACpB,IAAI,KAAKX,IAAI,CAACW,IAAI,CAACC,KAClC,CAAC;YAED,IAAIc,MAAM,EAAE;cACVC,OAAO,GAAGD,MAAM,CAACjB,IAAI;YACvB;UACF;UAEA,IAAI,CAAC1B,SAAS,GAAG2C,MAAM;UAEvB,IAAI,CAAC7C,kBAAkB,CAACM,IAAI,CAACuC,MAAM,GAAGA,MAAM,CAACM,YAAY,GAAG5B,SAAS,CAAC;UAEtE,IAAI,CAACzB,eAAe,CAACQ,IAAI,CAACzB,WAAW,CAACiE,OAAO,CAAC,GAAGA,OAAO,GAAGvB,SAAS,CAAC;UAErE;QACF;MAEA,KAAKjD,IAAI,CAAC8E,IAAI;QAAE;UACd,MAAMC,QAAQ,GAAG5E,eAAe,CAAC,IAAI,CAACmC,YAAY,CAAC,CAAC,CAAC;UACrD,MAAM0C,QAAQ,GAAGvE,UAAU,CAACsE,QAAQ,CAAC,GAAGA,QAAQ,CAACE,MAAM,GAAGF,QAAQ,CAAC,CAAC;;UAEpE,IAAI,CAACrD,kBAAkB,CAACM,IAAI,CAACiB,SAAS,CAAC;UAEvC,IAAI,CAACzB,eAAe,CAACQ,IAAI,CAACzB,WAAW,CAACyE,QAAQ,CAAC,GAAGA,QAAQ,GAAG/B,SAAS,CAAC;UAEvE;QACF;MAEA,KAAKjD,IAAI,CAACkF,YAAY;QAAE;UACtB,MAAMC,UAAU,GAAGjF,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAAC;UACpD,IAAI8C,cAAc;UAClB,IAAIC,UAAU;UAEd,IAAI/E,iBAAiB,CAAC6E,UAAU,CAAC,EAAE;YACjCE,UAAU,GAAGF,UAAU,CAACG,SAAS,CAAC,CAAC,CAACzC,IAAI,CAACW,IAAI,CAACC,KAAK,CAAC;YAEpD,IAAI4B,UAAU,EAAE;cACdD,cAAc,GAAGC,UAAU,CAAC/B,IAAI;YAClC;UACF;UAEA,IAAI,CAAC5B,kBAAkB,CAACM,IAAI,CAC1BqD,UAAU,GAAGA,UAAU,CAACR,YAAY,GAAG5B,SACzC,CAAC;UAED,IAAI,CAACzB,eAAe,CAACQ,IAAI,CACvBzB,WAAW,CAAC6E,cAAc,CAAC,GAAGA,cAAc,GAAGnC,SACjD,CAAC;UAED;QACF;MAEA,KAAKjD,IAAI,CAACuF,IAAI;QAAE;UACd,MAAMC,QAAQ,GAAGtF,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,CAAC,CAAC;UAClD,IAAImD,SAAS;UAEb,IAAIpF,UAAU,CAACmF,QAAQ,CAAC,EAAE;YACxBC,SAAS,GAAGD,QAAQ,CAACE,QAAQ,CAAC7C,IAAI,CAACY,KAAK,CAAC;UAC3C;UAEA,IAAI,CAAC5B,UAAU,GAAG4D,SAAS;UAC3B;QACF;MAEA,QAAQ,CAAC;IACX;EACF;;EAEAE,KAAKA,CAAC9C,IAAI,EAAE;IACV,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK9C,IAAI,CAAC+C,aAAa;QACrB,IAAI,CAACxB,gBAAgB,CAACqE,GAAG,CAAC,CAAC;QAE3B;MAEF,KAAK5F,IAAI,CAACkD,KAAK;QACb,IAAI,CAACzB,cAAc,CAACmE,GAAG,CAAC,CAAC;QAEzB,IAAI,CAACtE,UAAU,CAACsE,GAAG,CAAC,CAAC;QAErB;MAEF,KAAK5F,IAAI,CAACuD,SAAS;QACjB,IAAI,CAAC5B,UAAU,GAAG,IAAI;QACtB;MAEF,KAAK3B,IAAI,CAAC0D,oBAAoB;MAC9B,KAAK1D,IAAI,CAAC8D,eAAe;MACzB,KAAK9D,IAAI,CAAC+D,mBAAmB;QAC3B,IAAI,CAACzC,UAAU,CAACsE,GAAG,CAAC,CAAC;QAErB;MAEF,KAAK5F,IAAI,CAACmE,mBAAmB;QAC3B,IAAI,CAAC3C,eAAe,CAACoE,GAAG,CAAC,CAAC;QAE1B;MAEF,KAAK5F,IAAI,CAACqE,QAAQ;QAChB,IAAI,CAACzC,SAAS,GAAG,IAAI;QAErB,IAAI,CAACF,kBAAkB,CAACkE,GAAG,CAAC,CAAC;QAE7B,IAAI,CAACpE,eAAe,CAACoE,GAAG,CAAC,CAAC;QAE1B;MAEF,KAAK5F,IAAI,CAAC8E,IAAI;MACd,KAAK9E,IAAI,CAACkF,YAAY;QACpB,IAAI,CAACxD,kBAAkB,CAACkE,GAAG,CAAC,CAAC;QAE7B,IAAI,CAACpE,eAAe,CAACoE,GAAG,CAAC,CAAC;QAE1B;MAEF,KAAK5F,IAAI,CAACuF,IAAI;QACZ,IAAI,CAAC1D,UAAU,GAAG,IAAI;QACtB;MAEF,QAAQ,CAAC;IACX;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACb,MAAM,EAAEiC,UAAU,EAAE0C,SAAS,EAAE;EAClD,MAAMrC,IAAI,GAAGqC,SAAS,CAACrC,IAAI,CAACC,KAAK;EAEjC,IACED,IAAI,KAAK5C,kBAAkB,CAAC4C,IAAI,IAChCtC,MAAM,CAAC4E,YAAY,CAAC,CAAC,KAAK3C,UAAU,EACpC;IACA,OAAOvC,kBAAkB;EAC3B;EAEA,IAAI4C,IAAI,KAAK3C,gBAAgB,CAAC2C,IAAI,IAAItC,MAAM,CAAC4E,YAAY,CAAC,CAAC,KAAK3C,UAAU,EAAE;IAC1E,OAAOtC,gBAAgB;EACzB;EAEA,IAAI2C,IAAI,KAAK1C,oBAAoB,CAAC0C,IAAI,IAAIpD,eAAe,CAAC+C,UAAU,CAAC,EAAE;IACrE,OAAOrC,oBAAoB;EAC7B;EAEA,IAAIJ,YAAY,CAACyC,UAAU,CAAC,IAAI3C,eAAe,CAAC2C,UAAU,CAAC,EAAE;IAC3D,OAAOA,UAAU,CAACmC,SAAS,CAAC,CAAC,CAAC9B,IAAI,CAAC;EACrC;AACF;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASuC,iBAAiBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACnD,OAAO;IACLrD,KAAKA,CAAC,GAAG8B,IAAI,EAAE;MACb,MAAM7B,IAAI,GAAG6B,IAAI,CAAC,CAAC,CAAC;MACpBsB,QAAQ,CAACpD,KAAK,CAACC,IAAI,CAAC;MACpB,MAAMqD,EAAE,GAAGjG,oBAAoB,CAACgG,OAAO,EAAEpD,IAAI,CAACC,IAAI,CAAC,CAACF,KAAK;MAEzD,IAAIsD,EAAE,EAAE;QACN,MAAMC,MAAM,GAAGD,EAAE,CAACE,KAAK,CAACH,OAAO,EAAEvB,IAAI,CAAC;QAEtC,IAAIyB,MAAM,KAAKlD,SAAS,EAAE;UACxB+C,QAAQ,CAACL,KAAK,CAAC9C,IAAI,CAAC;UAEpB,IAAI9C,MAAM,CAACoG,MAAM,CAAC,EAAE;YAClBH,QAAQ,CAACpD,KAAK,CAACuD,MAAM,CAAC;UACxB;QACF;QAEA,OAAOA,MAAM;MACf;IACF,CAAC;IAEDR,KAAKA,CAAC,GAAGjB,IAAI,EAAE;MACb,MAAM7B,IAAI,GAAG6B,IAAI,CAAC,CAAC,CAAC;MACpB,MAAMwB,EAAE,GAAGjG,oBAAoB,CAACgG,OAAO,EAAEpD,IAAI,CAACC,IAAI,CAAC,CAAC6C,KAAK;MACzD,IAAIQ,MAAM;MAEV,IAAID,EAAE,EAAE;QACNC,MAAM,GAAGD,EAAE,CAACE,KAAK,CAACH,OAAO,EAAEvB,IAAI,CAAC;MAClC;MAEAsB,QAAQ,CAACL,KAAK,CAAC9C,IAAI,CAAC;MACpB,OAAOsD,MAAM;IACf;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}