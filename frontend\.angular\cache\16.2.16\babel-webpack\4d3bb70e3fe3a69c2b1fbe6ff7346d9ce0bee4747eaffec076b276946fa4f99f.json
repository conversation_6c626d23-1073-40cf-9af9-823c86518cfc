{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var checkFetcher = function (fetcher) {\n  if (!fetcher && typeof fetch === \"undefined\") {\n    throw newInvariantError(40);\n  }\n};", "map": {"version": 3, "names": ["newInvariantError", "checkFetcher", "fetcher", "fetch"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/checkFetcher.js"], "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var checkFetcher = function (fetcher) {\n    if (!fetcher && typeof fetch === \"undefined\") {\n        throw newInvariantError(40);\n    }\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,OAAO,IAAIC,YAAY,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACzC,IAAI,CAACA,OAAO,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;IAC1C,MAAMH,iBAAiB,CAAC,EAAE,CAAC;EAC/B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}