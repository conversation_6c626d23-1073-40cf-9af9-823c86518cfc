{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authadmin.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction AuthAdminLayoutComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 34);\n    i0.ɵɵelement(3, \"i\", 50)(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.messageFromRedirect, \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(62);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (_r2.errors == null ? null : _r2.errors[\"required\"]) ? \"Email requis\" : \"Format email invalide\", \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Mot de passe requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AuthAdminLayoutComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.messageAuthError);\n  }\n}\nexport class AuthAdminLayoutComponent {\n  constructor(authAdminService, authUserService, authService, router, route, themeService) {\n    this.authAdminService = authAdminService;\n    this.authUserService = authUserService;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.themeService = themeService;\n    this.messageAuthError = '';\n    this.messageFromRedirect = '';\n    this.destroy$ = new Subject();\n    this.checkExistingAuth();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  ngOnInit() {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';\n    this.subscribeToQueryParams();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  checkExistingAuth() {\n    if (this.authUserService.userLoggedIn()) {\n      this.router.navigate(['/'], {\n        queryParams: {\n          message: \"Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord.\"\n        }\n      });\n      return;\n    }\n    if (this.authAdminService.loggedIn()) {\n      this.router.navigateByUrl('/admin');\n    }\n  }\n  subscribeToQueryParams() {\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.messageFromRedirect = params['message'] || '';\n      this.clearMessageAfterDelay();\n    });\n  }\n  clearMessageAfterDelay() {\n    if (this.messageFromRedirect) {\n      setTimeout(() => this.messageFromRedirect = '', 5000);\n    }\n  }\n  loginAdmin(form) {\n    if (!form.valid) {\n      this.messageAuthError = 'Veuillez remplir correctement le formulaire.';\n      return;\n    }\n    const data = form.value;\n    this.authAdminService.login(data).subscribe({\n      next: response => {\n        this.handleLoginSuccess(response);\n      },\n      error: err => {\n        this.handleLoginError(err);\n      }\n    });\n  }\n  handleLoginSuccess(response) {\n    this.authAdminService.saveDataProfil(response.token);\n    this.router.navigate([this.returnUrl]);\n  }\n  handleLoginError(err) {\n    this.messageAuthError = err.error?.message || 'Une erreur est survenue lors de la connexion';\n    // Effacer le message d'erreur après 5 secondes\n    setTimeout(() => this.messageAuthError = '', 5000);\n  }\n  static {\n    this.ɵfac = function AuthAdminLayoutComponent_Factory(t) {\n      return new (t || AuthAdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthadminService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuthAdminLayoutComponent,\n      selectors: [[\"app-auth-admin-layout\"]],\n      decls: 81,\n      vars: 8,\n      consts: [[\"class\", \"fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"min-h-screen\", \"main-grid-container\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"overflow-hidden\"], [1, \"background-grid\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-full\", \"h-full\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"w-full\", \"max-w-4xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-xl\", \"dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"relative\", \"z-10\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"md:flex-row\"], [1, \"md:w-1/2\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#2a3052]\", \"dark:to-[#4f5fad]\", \"hidden\", \"md:flex\", \"items-center\", \"justify-center\", \"p-12\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-32\", \"h-32\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"translate-x-16\", \"-translate-y-16\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-40\", \"h-40\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"-translate-x-20\", \"translate-y-20\"], [1, \"absolute\", \"inset-0\", \"opacity-10\"], [1, \"grid\", \"grid-cols-12\", \"h-full\"], [1, \"border-r\", \"border-white/20\"], [1, \"grid\", \"grid-rows-12\", \"w-full\", \"absolute\", \"top-0\", \"left-0\", \"h-full\"], [1, \"border-b\", \"border-white/20\"], [1, \"text-center\", \"text-white\", \"relative\", \"z-10\"], [1, \"text-3xl\", \"font-bold\", \"mb-4\", \"text-white\"], [1, \"text-white/80\"], [1, \"mt-8\", \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-24\", \"w-24\", \"mx-auto\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-white/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"md:w-1/2\", \"p-8\", \"md:p-12\", \"relative\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-20\", \"h-20\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-24\", \"h-24\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"text-center\", \"mb-8\", \"relative\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"space-y-6\", \"relative\", \"z-10\", 3, \"ngSubmit\"], [\"f\", \"ngForm\"], [1, \"group\"], [\"for\", \"email\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"name\", \"email\", \"ngModel\", \"\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"email\", \"ngModel\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [\"id\", \"password\", \"type\", \"password\", \"name\", \"password\", \"ngModel\", \"\", \"required\", \"\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"password\", \"ngModel\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"block\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fixed\", \"top-4\", \"right-4\", \"left-4\", \"md:left-auto\", \"md:w-96\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"rounded-lg\", \"p-4\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)]\", \"z-50\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"p-3\", \"rounded-lg\", \"text-sm\", \"flex\", \"items-start\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mt-0.5\", \"mr-2\"]],\n      template: function AuthAdminLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r7 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, AuthAdminLayoutComponent_div_0_Template, 7, 1, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵelement(10, \"div\", 9)(11, \"div\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12);\n          i0.ɵɵelement(14, \"div\", 13)(15, \"div\", 13)(16, \"div\", 13)(17, \"div\", 13)(18, \"div\", 13)(19, \"div\", 13)(20, \"div\", 13)(21, \"div\", 13)(22, \"div\", 13)(23, \"div\", 13)(24, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 14);\n          i0.ɵɵelement(26, \"div\", 15)(27, \"div\", 15)(28, \"div\", 15)(29, \"div\", 15)(30, \"div\", 15)(31, \"div\", 15)(32, \"div\", 15)(33, \"div\", 15)(34, \"div\", 15)(35, \"div\", 15)(36, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 16)(38, \"h2\", 17);\n          i0.ɵɵtext(39, \" Espace Administrateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 18);\n          i0.ɵɵtext(41, \"Gestion compl\\u00E8te de votre plateforme\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(43, \"svg\", 20);\n          i0.ɵɵelement(44, \"path\", 21)(45, \"path\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(46, \"div\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 24);\n          i0.ɵɵelement(48, \"div\", 25)(49, \"div\", 26);\n          i0.ɵɵelementStart(50, \"div\", 27)(51, \"h1\", 28);\n          i0.ɵɵtext(52, \" Connexion Admin \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"p\", 29);\n          i0.ɵɵtext(54, \" Acc\\u00E9dez \\u00E0 votre tableau de bord \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"form\", 30, 31);\n          i0.ɵɵlistener(\"ngSubmit\", function AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r1 = i0.ɵɵreference(56);\n            return i0.ɵɵresetView(ctx.loginAdmin(_r1));\n          });\n          i0.ɵɵelementStart(57, \"div\", 32)(58, \"label\", 33);\n          i0.ɵɵtext(59, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 34);\n          i0.ɵɵelement(61, \"input\", 35, 36);\n          i0.ɵɵelementStart(63, \"div\", 37);\n          i0.ɵɵelement(64, \"div\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(65, AuthAdminLayoutComponent_div_65_Template, 3, 1, \"div\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 32)(67, \"label\", 40);\n          i0.ɵɵtext(68, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 34);\n          i0.ɵɵelement(70, \"input\", 41, 42);\n          i0.ɵɵelementStart(72, \"div\", 37);\n          i0.ɵɵelement(73, \"div\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(74, AuthAdminLayoutComponent_div_74_Template, 3, 0, \"div\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(75, AuthAdminLayoutComponent_div_75_Template, 4, 1, \"div\", 43);\n          i0.ɵɵelementStart(76, \"button\", 44);\n          i0.ɵɵelement(77, \"div\", 45)(78, \"div\", 46);\n          i0.ɵɵelementStart(79, \"span\", 47);\n          i0.ɵɵtext(80, \" Se connecter \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(62);\n          const _r4 = i0.ɵɵreference(71);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(2, 6, ctx.isDarkMode$));\n          i0.ɵɵadvance(64);\n          i0.ɵɵproperty(\"ngIf\", _r2.invalid && (_r2.dirty || _r2.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", _r4.invalid && (_r4.dirty || _r4.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageAuthError);\n        }\n      },\n      dependencies: [i6.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.EmailValidator, i7.NgModel, i7.NgForm, i6.AsyncPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.notification[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  right: 20px;\\n  padding: 15px 25px;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  max-width: 350px;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out forwards;\\n  font-family: \\\"Segoe UI\\\", Roboto, sans-serif;\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.notification-info[_ngcontent-%COMP%] {\\n  background-color: #e6f7ff;\\n  color: #0052cc;\\n  border-left: 4px solid #1890ff;\\n}\\n\\n.notification-error[_ngcontent-%COMP%] {\\n  background-color: #fff1f0;\\n  color: #cf1322;\\n  border-left: 4px solid #ff4d4f;\\n}\\n\\n.notification-success[_ngcontent-%COMP%] {\\n  background-color: #f6ffed;\\n  color: #389e0d;\\n  border-left: 4px solid #52c41a;\\n}\\n\\n\\n\\n.notification-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 18px;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeOut {\\n  to {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n}\\n\\n\\n.notification-auto-hide[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeOut 0.5s ease-in 4.5s forwards;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "messageFromRedirect", "_r2", "errors", "ɵɵtextInterpolate", "ctx_r6", "messageAuthError", "AuthAdminLayoutComponent", "constructor", "authAdminService", "authUserService", "authService", "router", "route", "themeService", "destroy$", "checkExistingAuth", "isDarkMode$", "darkMode$", "ngOnInit", "returnUrl", "snapshot", "queryParams", "subscribeToQueryParams", "ngOnDestroy", "next", "complete", "userLoggedIn", "navigate", "message", "loggedIn", "navigateByUrl", "pipe", "subscribe", "params", "clearMessageAfterDelay", "setTimeout", "loginAdmin", "form", "valid", "data", "value", "login", "response", "handleLoginSuccess", "error", "err", "handleLoginError", "saveDataProfil", "token", "ɵɵdirectiveInject", "i1", "AuthadminService", "i2", "AuthuserService", "i3", "AuthService", "i4", "Router", "ActivatedRoute", "i5", "ThemeService", "selectors", "decls", "vars", "consts", "template", "AuthAdminLayoutComponent_Template", "rf", "ctx", "ɵɵtemplate", "AuthAdminLayoutComponent_div_0_Template", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener", "ɵɵrestoreView", "_r7", "_r1", "ɵɵreference", "ɵɵresetView", "AuthAdminLayoutComponent_div_65_Template", "AuthAdminLayoutComponent_div_74_Template", "AuthAdminLayoutComponent_div_75_Template", "ɵɵproperty", "ɵɵclassProp", "ɵɵpipeBind1", "invalid", "dirty", "touched", "_r4"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\auth-admin-layout\\auth-admin-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\auth-admin-layout\\auth-admin-layout.component.html"], "sourcesContent": ["import { HttpErrorResponse } from '@angular/common/http';\r\nimport { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthadminService } from 'src/app/services/authadmin.service';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Observable, Subject, takeUntil } from 'rxjs';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { ThemeService } from '@app/services/theme.service';\r\n@Component({\r\n  selector: 'app-auth-admin-layout',\r\n  templateUrl: './auth-admin-layout.component.html',\r\n  styleUrls: ['./auth-admin-layout.component.css'],\r\n})\r\nexport class AuthAdminLayoutComponent implements OnInit, OnDestroy {\r\n  messageAuthError: string = '';\r\n  messageFromRedirect: string = '';\r\n  private destroy$ = new Subject<void>();\r\n  private returnUrl: string | undefined;\r\n  isDarkMode$: Observable<boolean>;\r\n\r\n  constructor(\r\n    private authAdminService: AuthadminService,\r\n    public authUserService: AuthuserService,\r\n    public authService: AuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private themeService: ThemeService\r\n  ) {\r\n    this.checkExistingAuth();\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';\r\n    this.subscribeToQueryParams();\r\n  }\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n  private checkExistingAuth(): void {\r\n    if (this.authUserService.userLoggedIn()) {\r\n      this.router.navigate(['/'], {\r\n        queryParams: {\r\n          message:\r\n            \"Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord.\",\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (this.authAdminService.loggedIn()) {\r\n      this.router.navigateByUrl('/admin');\r\n    }\r\n  }\r\n  private subscribeToQueryParams(): void {\r\n    this.route.queryParams\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe((params) => {\r\n        this.messageFromRedirect = params['message'] || '';\r\n        this.clearMessageAfterDelay();\r\n      });\r\n  }\r\n  private clearMessageAfterDelay(): void {\r\n    if (this.messageFromRedirect) {\r\n      setTimeout(() => (this.messageFromRedirect = ''), 5000);\r\n    }\r\n  }\r\n  loginAdmin(form: any): void {\r\n    if (!form.valid) {\r\n      this.messageAuthError = 'Veuillez remplir correctement le formulaire.';\r\n      return;\r\n    }\r\n    const data = form.value;\r\n    this.authAdminService.login(data).subscribe({\r\n      next: (response) => {\r\n        this.handleLoginSuccess(response);\r\n      },\r\n      error: (err: HttpErrorResponse) => {\r\n        this.handleLoginError(err);\r\n      },\r\n    });\r\n  }\r\n  private handleLoginSuccess(response: any): void {\r\n    this.authAdminService.saveDataProfil(response.token);\r\n    this.router.navigate([this.returnUrl]);\r\n  }\r\n  private handleLoginError(err: HttpErrorResponse): void {\r\n    this.messageAuthError =\r\n      err.error?.message || 'Une erreur est survenue lors de la connexion';\r\n\r\n    // Effacer le message d'erreur après 5 secondes\r\n    setTimeout(() => (this.messageAuthError = ''), 5000);\r\n  }\r\n}\r\n", "<!-- Notification message -->\r\n<div\r\n  *ngIf=\"messageFromRedirect\"\r\n  class=\"fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm\"\r\n>\r\n  <div class=\"flex items-center\">\r\n    <div class=\"relative\">\r\n      <i\r\n        class=\"fas fa-info-circle text-[#4f5fad] dark:text-[#6d78c9] text-lg mr-3\"\r\n      ></i>\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n      ></div>\r\n    </div>\r\n    <p class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">\r\n      {{ messageFromRedirect }}\r\n    </p>\r\n  </div>\r\n</div>\r\n\r\n<div\r\n  class=\"min-h-screen main-grid-container flex items-center justify-center p-4 relative overflow-hidden\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background Grid -->\r\n  <div class=\"background-grid\"></div>\r\n\r\n  <!-- Background decorative elements -->\r\n  <div\r\n    class=\"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\"\r\n  >\r\n    <!-- Decorative circles -->\r\n    <div\r\n      class=\"absolute top-[10%] left-[5%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n  </div>\r\n\r\n  <div\r\n    class=\"w-full max-w-4xl bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-xl dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm relative z-10 border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\r\n  >\r\n    <div class=\"flex flex-col md:flex-row\">\r\n      <!-- Image Section -->\r\n      <div\r\n        class=\"md:w-1/2 bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#2a3052] dark:to-[#4f5fad] hidden md:flex items-center justify-center p-12 relative overflow-hidden\"\r\n      >\r\n        <!-- Decorative elements -->\r\n        <div\r\n          class=\"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-3xl transform translate-x-16 -translate-y-16\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute bottom-0 left-0 w-40 h-40 bg-white/10 rounded-full blur-3xl transform -translate-x-20 translate-y-20\"\r\n        ></div>\r\n\r\n        <!-- Grid pattern - Harmonisé avec la grille commune -->\r\n        <div class=\"absolute inset-0 opacity-10\">\r\n          <div class=\"grid grid-cols-12 h-full\">\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n            <div class=\"border-r border-white/20\"></div>\r\n          </div>\r\n          <div class=\"grid grid-rows-12 w-full absolute top-0 left-0 h-full\">\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n            <div class=\"border-b border-white/20\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"text-center text-white relative z-10\">\r\n          <h2 class=\"text-3xl font-bold mb-4 text-white\">\r\n            Espace Administrateur\r\n          </h2>\r\n          <p class=\"text-white/80\">Gestion complète de votre plateforme</p>\r\n          <div class=\"mt-8 relative\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-24 w-24 mx-auto text-white\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"1.5\"\r\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\r\n              />\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"1.5\"\r\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n              />\r\n            </svg>\r\n            <!-- Glow effect -->\r\n            <div\r\n              class=\"absolute inset-0 bg-white/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"md:w-1/2 p-8 md:p-12 relative\">\r\n        <!-- Decorative elements -->\r\n        <div\r\n          class=\"absolute top-0 right-0 w-20 h-20 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute bottom-0 left-0 w-24 h-24 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl\"\r\n        ></div>\r\n\r\n        <div class=\"text-center mb-8 relative\">\r\n          <h1\r\n            class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n          >\r\n            Connexion Admin\r\n          </h1>\r\n          <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n            Accédez à votre tableau de bord\r\n          </p>\r\n        </div>\r\n\r\n        <form\r\n          #f=\"ngForm\"\r\n          (ngSubmit)=\"loginAdmin(f)\"\r\n          class=\"space-y-6 relative z-10\"\r\n        >\r\n          <!-- Email Input -->\r\n          <div class=\"group\">\r\n            <label\r\n              for=\"email\"\r\n              class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors\"\r\n              >Email</label\r\n            >\r\n            <div class=\"relative\">\r\n              <input\r\n                id=\"email\"\r\n                type=\"email\"\r\n                name=\"email\"\r\n                #email=\"ngModel\"\r\n                ngModel\r\n                required\r\n                email\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"<EMAIL>\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"email.invalid && (email.dirty || email.touched)\"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              {{ email.errors?.['required'] ? 'Email requis' : 'Format email invalide' }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Password Input -->\r\n          <div class=\"group\">\r\n            <label\r\n              for=\"password\"\r\n              class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors\"\r\n              >Mot de passe</label\r\n            >\r\n            <div class=\"relative\">\r\n              <input\r\n                id=\"password\"\r\n                type=\"password\"\r\n                name=\"password\"\r\n                #password=\"ngModel\"\r\n                ngModel\r\n                required\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"••••••••\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"password.invalid && (password.dirty || password.touched)\"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              Mot de passe requis\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"messageAuthError\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start\"\r\n          >\r\n            <i class=\"fas fa-exclamation-triangle mt-0.5 mr-2\"></i>\r\n            <span>{{ messageAuthError }}</span>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button type=\"submit\" class=\"w-full relative overflow-hidden group\">\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n            ></div>\r\n            <span\r\n              class=\"relative block text-white font-bold py-3 px-4 rounded-lg transition-all\"\r\n            >\r\n              Se connecter\r\n            </span>\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAKA,SAAqBA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICJrDC,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,mBAAA,MACF;;;;;IA6JQR,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAG,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,8DACF;;;;;IA6BAV,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAuD;IACvDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAsB;;;ADnNxC,OAAM,MAAOC,wBAAwB;EAOnCC,YACUC,gBAAkC,EACnCC,eAAgC,EAChCC,WAAwB,EACvBC,MAAc,EACdC,KAAqB,EACrBC,YAA0B;IAL1B,KAAAL,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IAZtB,KAAAR,gBAAgB,GAAW,EAAE;IAC7B,KAAAL,mBAAmB,GAAW,EAAE;IACxB,KAAAc,QAAQ,GAAG,IAAIxB,OAAO,EAAQ;IAYpC,IAAI,CAACyB,iBAAiB,EAAE;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,YAAY,CAACI,SAAS;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,SAAS;IAC1E,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACAC,WAAWA,CAAA;IACT,IAAI,CAACT,QAAQ,CAACU,IAAI,EAAE;IACpB,IAAI,CAACV,QAAQ,CAACW,QAAQ,EAAE;EAC1B;EACQV,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACN,eAAe,CAACiB,YAAY,EAAE,EAAE;MACvC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;QAC1BN,WAAW,EAAE;UACXO,OAAO,EACL;;OAEL,CAAC;MACF;;IAGF,IAAI,IAAI,CAACpB,gBAAgB,CAACqB,QAAQ,EAAE,EAAE;MACpC,IAAI,CAAClB,MAAM,CAACmB,aAAa,CAAC,QAAQ,CAAC;;EAEvC;EACQR,sBAAsBA,CAAA;IAC5B,IAAI,CAACV,KAAK,CAACS,WAAW,CACnBU,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAC9BkB,SAAS,CAAEC,MAAM,IAAI;MACpB,IAAI,CAACjC,mBAAmB,GAAGiC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;MAClD,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC;EACN;EACQA,sBAAsBA,CAAA;IAC5B,IAAI,IAAI,CAAClC,mBAAmB,EAAE;MAC5BmC,UAAU,CAAC,MAAO,IAAI,CAACnC,mBAAmB,GAAG,EAAG,EAAE,IAAI,CAAC;;EAE3D;EACAoC,UAAUA,CAACC,IAAS;IAClB,IAAI,CAACA,IAAI,CAACC,KAAK,EAAE;MACf,IAAI,CAACjC,gBAAgB,GAAG,8CAA8C;MACtE;;IAEF,MAAMkC,IAAI,GAAGF,IAAI,CAACG,KAAK;IACvB,IAAI,CAAChC,gBAAgB,CAACiC,KAAK,CAACF,IAAI,CAAC,CAACP,SAAS,CAAC;MAC1CR,IAAI,EAAGkB,QAAQ,IAAI;QACjB,IAAI,CAACC,kBAAkB,CAACD,QAAQ,CAAC;MACnC,CAAC;MACDE,KAAK,EAAGC,GAAsB,IAAI;QAChC,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC;MAC5B;KACD,CAAC;EACJ;EACQF,kBAAkBA,CAACD,QAAa;IACtC,IAAI,CAAClC,gBAAgB,CAACuC,cAAc,CAACL,QAAQ,CAACM,KAAK,CAAC;IACpD,IAAI,CAACrC,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC;EACxC;EACQ2B,gBAAgBA,CAACD,GAAsB;IAC7C,IAAI,CAACxC,gBAAgB,GACnBwC,GAAG,CAACD,KAAK,EAAEhB,OAAO,IAAI,8CAA8C;IAEtE;IACAO,UAAU,CAAC,MAAO,IAAI,CAAC9B,gBAAgB,GAAG,EAAG,EAAE,IAAI,CAAC;EACtD;;;uBAhFWC,wBAAwB,EAAAd,EAAA,CAAAyD,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA3D,EAAA,CAAAyD,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7D,EAAA,CAAAyD,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAAyD,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAjE,EAAA,CAAAyD,iBAAA,CAAAO,EAAA,CAAAE,cAAA,GAAAlE,EAAA,CAAAyD,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAxBtD,wBAAwB;MAAAuD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCZrC3E,EAAA,CAAA6E,UAAA,IAAAC,uCAAA,iBAkBM;UAEN9E,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAE,SAAA,aAAmC;UAGnCF,EAAA,CAAAC,cAAA,aAEC;UAECD,EAAA,CAAAE,SAAA,aAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAEC;UAOKD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAE,SAAA,eAA4C;UAW9CF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmE;UACjED,EAAA,CAAAE,SAAA,eAA4C;UAW9CF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAkD;UAE9CD,EAAA,CAAAI,MAAA,+BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAI,MAAA,iDAAoC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA+E,cAAA,EAMC;UAND/E,EAAA,CAAAC,cAAA,eAMC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UAOJF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAgF,eAAA,EAEC;UAFDhF,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA2C;UAEzCD,EAAA,CAAAE,SAAA,eAEO;UAKPF,EAAA,CAAAC,cAAA,eAAuC;UAInCD,EAAA,CAAAI,MAAA,yBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAmD;UACjDD,EAAA,CAAAI,MAAA,mDACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,oBAIC;UAFCD,EAAA,CAAAiF,UAAA,sBAAAC,4DAAA;YAAAlF,EAAA,CAAAmF,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAArF,EAAA,CAAAsF,WAAA;YAAA,OAAYtF,EAAA,CAAAuF,WAAA,CAAAX,GAAA,CAAAhC,UAAA,CAAAyC,GAAA,CAAa;UAAA,EAAC;UAI1BrF,EAAA,CAAAC,cAAA,eAAmB;UAIdD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EACP;UACDH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,qBAUE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAA6E,UAAA,KAAAW,wCAAA,kBAMM;UACRxF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmB;UAIdD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EACd;UACDH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,qBASE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAA6E,UAAA,KAAAY,wCAAA,kBAMM;UACRzF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA6E,UAAA,KAAAa,wCAAA,kBAMM;UAGN1F,EAAA,CAAAC,cAAA,kBAAoE;UAClED,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;UA7OhBH,EAAA,CAAA2F,UAAA,SAAAf,GAAA,CAAApE,mBAAA,CAAyB;UAqB1BR,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAA4F,WAAA,SAAA5F,EAAA,CAAA6F,WAAA,OAAAjB,GAAA,CAAApD,WAAA,EAAkC;UAwJrBxB,EAAA,CAAAK,SAAA,IAAqD;UAArDL,EAAA,CAAA2F,UAAA,SAAAlF,GAAA,CAAAqF,OAAA,KAAArF,GAAA,CAAAsF,KAAA,IAAAtF,GAAA,CAAAuF,OAAA,EAAqD;UAmCrDhG,EAAA,CAAAK,SAAA,GAA8D;UAA9DL,EAAA,CAAA2F,UAAA,SAAAM,GAAA,CAAAH,OAAA,KAAAG,GAAA,CAAAF,KAAA,IAAAE,GAAA,CAAAD,OAAA,EAA8D;UAUhEhG,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAA2F,UAAA,SAAAf,GAAA,CAAA/D,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}