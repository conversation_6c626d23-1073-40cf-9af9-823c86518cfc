{"ast": null, "code": "export function isNonNullObject(obj) {\n  return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n  return obj !== null && typeof obj === \"object\" && (Object.getPrototypeOf(obj) === Object.prototype || Object.getPrototypeOf(obj) === null);\n}", "map": {"version": 3, "names": ["isNonNullObject", "obj", "isPlainObject", "Object", "getPrototypeOf", "prototype"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/objects.js"], "sourcesContent": ["export function isNonNullObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n    return (obj !== null &&\n        typeof obj === \"object\" &&\n        (Object.getPrototypeOf(obj) === Object.prototype ||\n            Object.getPrototypeOf(obj) === null));\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAeA,CAACC,GAAG,EAAE;EACjC,OAAOA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ;AAClD;AACA,OAAO,SAASC,aAAaA,CAACD,GAAG,EAAE;EAC/B,OAAQA,GAAG,KAAK,IAAI,IAChB,OAAOA,GAAG,KAAK,QAAQ,KACtBE,MAAM,CAACC,cAAc,CAACH,GAAG,CAAC,KAAKE,MAAM,CAACE,SAAS,IAC5CF,MAAM,CAACC,cAAc,CAACH,GAAG,CAAC,KAAK,IAAI,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}