{"ast": null, "code": "import { ApolloLink } from \"./ApolloLink.js\";\nexport var from = ApolloLink.from;", "map": {"version": 3, "names": ["ApolloLink", "from"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/from.js"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\nexport var from = ApolloLink.from;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,IAAI,GAAGD,UAAU,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}