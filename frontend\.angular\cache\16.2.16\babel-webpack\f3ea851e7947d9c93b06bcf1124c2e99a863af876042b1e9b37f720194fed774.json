{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logger.service\";\nimport * as i2 from \"@angular/common\";\nfunction VoiceRecorderComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 15);\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", 5 + ctx_r2.Math.abs(ctx_r2.Math.sin(i_r3 / 3) * 15), \"px\")(\"animation-delay\", i_r3 * 60, \"ms\");\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];\n};\nfunction VoiceRecorderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵelement(3, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 7);\n    i0.ɵɵtemplate(5, VoiceRecorderComponent_div_1_div_5_Template, 1, 4, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.stopRecording());\n    });\n    i0.ɵɵelement(10, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_div_1_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancelRecording());\n    });\n    i0.ɵɵelement(12, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formattedTime);\n  }\n}\nfunction VoiceRecorderComponent_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class VoiceRecorderComponent {\n  constructor(logger) {\n    this.logger = logger;\n    this.recordingComplete = new EventEmitter();\n    this.recordingCancelled = new EventEmitter();\n    this.maxDuration = 60; // Durée maximale en secondes\n    this.isRecording = false;\n    this.recordingTime = 0;\n    this.mediaRecorder = null;\n    this.audioChunks = [];\n    this.audioStream = null;\n    // Exposer Math pour l'utiliser dans le template\n    this.Math = Math;\n  }\n  ngOnInit() {}\n  ngOnDestroy() {\n    this.stopRecording();\n    this.stopMediaTracks();\n  }\n  /**\n   * Démarre l'enregistrement vocal\n   */\n  startRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.audioChunks = [];\n        _this.recordingTime = 0;\n        // Demander l'accès au microphone\n        _this.audioStream = yield navigator.mediaDevices.getUserMedia({\n          audio: true\n        });\n        // Créer un MediaRecorder\n        _this.mediaRecorder = new MediaRecorder(_this.audioStream);\n        // Configurer les gestionnaires d'événements\n        _this.mediaRecorder.ondataavailable = event => {\n          if (event.data.size > 0) {\n            _this.audioChunks.push(event.data);\n          }\n        };\n        _this.mediaRecorder.onstop = () => {\n          // Créer un blob à partir des chunks audio\n          const audioBlob = new Blob(_this.audioChunks, {\n            type: 'audio/webm'\n          });\n          _this.recordingComplete.emit(audioBlob);\n          _this.stopMediaTracks();\n        };\n        // Démarrer l'enregistrement\n        _this.mediaRecorder.start();\n        _this.isRecording = true;\n        // Démarrer le timer\n        _this.startTimer();\n        // Arrêter automatiquement après la durée maximale\n        setTimeout(() => {\n          if (_this.isRecording) {\n            _this.stopRecording();\n          }\n        }, _this.maxDuration * 1000);\n      } catch (error) {\n        _this.logger.error('Error starting voice recording:', error);\n        _this.isRecording = false;\n      }\n    })();\n  }\n  /**\n   * Arrête l'enregistrement vocal\n   */\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n      this.stopTimer();\n    }\n  }\n  /**\n   * Annule l'enregistrement vocal\n   */\n  cancelRecording() {\n    this.stopRecording();\n    this.stopMediaTracks();\n    this.recordingCancelled.emit();\n  }\n  /**\n   * Démarre le timer pour afficher la durée d'enregistrement\n   */\n  startTimer() {\n    this.stopTimer();\n    this.timerInterval = setInterval(() => {\n      this.recordingTime++;\n      // Arrêter si on atteint la durée maximale\n      if (this.recordingTime >= this.maxDuration) {\n        this.stopRecording();\n      }\n    }, 1000);\n  }\n  /**\n   * Arrête le timer\n   */\n  stopTimer() {\n    if (this.timerInterval) {\n      clearInterval(this.timerInterval);\n      this.timerInterval = null;\n    }\n  }\n  /**\n   * Arrête les pistes média pour libérer le microphone\n   */\n  stopMediaTracks() {\n    if (this.audioStream) {\n      this.audioStream.getTracks().forEach(track => track.stop());\n      this.audioStream = null;\n    }\n  }\n  /**\n   * Formate le temps d'enregistrement en MM:SS\n   */\n  get formattedTime() {\n    const minutes = Math.floor(this.recordingTime / 60);\n    const seconds = this.recordingTime % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }\n  static {\n    this.ɵfac = function VoiceRecorderComponent_Factory(t) {\n      return new (t || VoiceRecorderComponent)(i0.ɵɵdirectiveInject(i1.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VoiceRecorderComponent,\n      selectors: [[\"app-voice-recorder\"]],\n      inputs: {\n        maxDuration: \"maxDuration\"\n      },\n      outputs: {\n        recordingComplete: \"recordingComplete\",\n        recordingCancelled: \"recordingCancelled\"\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"whatsapp-voice-recorder\"], [\"class\", \"whatsapp-voice-container\", 4, \"ngIf\"], [\"class\", \"whatsapp-voice-start-button\", 3, \"click\", 4, \"ngIf\"], [1, \"whatsapp-voice-container\"], [1, \"whatsapp-voice-info\"], [1, \"whatsapp-recording-indicator\"], [1, \"whatsapp-recording-dot\"], [1, \"whatsapp-waveform\"], [\"class\", \"whatsapp-waveform-bar\", 3, \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-recording-time\"], [1, \"whatsapp-voice-controls\"], [1, \"whatsapp-voice-stop-button\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"whatsapp-voice-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"whatsapp-waveform-bar\"], [1, \"whatsapp-voice-start-button\", 3, \"click\"], [1, \"fas\", \"fa-microphone\"]],\n      template: function VoiceRecorderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, VoiceRecorderComponent_div_1_Template, 13, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, VoiceRecorderComponent_button_2_Template, 2, 0, \"button\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecording);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isRecording);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf],\n      styles: [\".voice-recorder[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 10px;\\n  width: 100%;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  border-radius: var(--border-radius-full);\\n  padding: 8px 15px;\\n  transition: all var(--transition-medium);\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  width: 100%;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-status[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.3;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-status.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 53, 71, 0.1);\\n  border: 1px solid rgba(255, 53, 71, 0.3);\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.1);\\n  animation: _ngcontent-%COMP%_glow-pulse 2s infinite;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-status.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(90deg, transparent, #ff3547, transparent);\\n}\\n\\n@keyframes _ngcontent-%COMP%_glow-pulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 53, 71, 0.1);\\n  }\\n  50% {\\n    box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n  }\\n  100% {\\n    box-shadow: 0 0 5px rgba(255, 53, 71, 0.1);\\n  }\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 15px;\\n  background-color: rgba(255, 53, 71, 0.1);\\n  padding: 5px 10px;\\n  border-radius: var(--border-radius-full);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  border-radius: 50%;\\n  margin-right: 8px;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 18px;\\n  height: 18px;\\n  background: radial-gradient(\\n    circle,\\n    rgba(255, 53, 71, 0.5) 0%,\\n    transparent 70%\\n  );\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse-glow 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-glow {\\n  0% {\\n    opacity: 0.3;\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    opacity: 0.6;\\n    transform: translate(-50%, -50%) scale(1.5);\\n  }\\n  100% {\\n    opacity: 0.3;\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-time[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #ff3547;\\n  letter-spacing: 0.5px;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: auto;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%] {\\n  width: 45px;\\n  height: 45px;\\n  border-radius: 50%;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  color: var(--text-light);\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all var(--transition-medium);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.2) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.5);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record.recording[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record.recording[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 20px rgba(255, 53, 71, 0.5);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  background-color: rgba(255, 53, 71, 0.1);\\n  color: #ff3547;\\n  border: 1px solid rgba(255, 53, 71, 0.3);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  margin-left: 12px;\\n  transition: all var(--transition-medium);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.1) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background-color: #ff3547;\\n  color: white;\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n  border-color: transparent;\\n}\\n\\n.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0.7;\\n    transform: scale(1.2);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"\\n\\n  .whatsapp-voice-recorder[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    width: 100%;\\n    position: relative;\\n  }\\n\\n  .whatsapp-voice-container[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    width: 100%;\\n    background-color: #f0f2f5;\\n    border-radius: 24px;\\n    padding: 8px 12px;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-voice-container[_ngcontent-%COMP%] {\\n    background-color: #2a2a2a;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\\n  }\\n\\n  .whatsapp-voice-info[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    flex: 1;\\n  }\\n\\n  .whatsapp-recording-indicator[_ngcontent-%COMP%] {\\n    position: relative;\\n    margin-right: 12px;\\n  }\\n\\n  .whatsapp-recording-dot[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    background-color: #ff3b30;\\n    border-radius: 50%;\\n    animation: _ngcontent-%COMP%_whatsapp-pulse 1.5s infinite;\\n  }\\n\\n  .whatsapp-waveform[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    height: 32px;\\n    margin-right: 12px;\\n    gap: 2px;\\n  }\\n\\n  .whatsapp-waveform-bar[_ngcontent-%COMP%] {\\n    width: 3px;\\n    background-color: #25d366;\\n    border-radius: 1.5px;\\n    animation: _ngcontent-%COMP%_whatsapp-wave 1.5s ease-in-out infinite;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-waveform-bar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-waveform-bar[_ngcontent-%COMP%] {\\n    background-color: #00c853;\\n  }\\n\\n  .whatsapp-recording-time[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    color: #333;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-recording-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-recording-time[_ngcontent-%COMP%] {\\n    color: #e0e0e0;\\n  }\\n\\n  .whatsapp-voice-controls[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n  }\\n\\n  .whatsapp-voice-stop-button[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    border-radius: 50%;\\n    background-color: #25d366;\\n    color: white;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .whatsapp-voice-stop-button[_ngcontent-%COMP%]:hover {\\n    background-color: #128c7e;\\n  }\\n\\n  .whatsapp-voice-cancel-button[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    border-radius: 50%;\\n    background-color: #f0f2f5;\\n    color: #888;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%] {\\n    background-color: #3a3a3a;\\n    color: #ccc;\\n  }\\n\\n  .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover {\\n    background-color: #e0e0e0;\\n    color: #666;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover {\\n    background-color: #444;\\n    color: #fff;\\n  }\\n\\n  .whatsapp-voice-start-button[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    border-radius: 50%;\\n    background-color: #25d366;\\n    color: white;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .whatsapp-voice-start-button[_ngcontent-%COMP%]:hover {\\n    background-color: #128c7e;\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_whatsapp-pulse {\\n    0% {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 0.5;\\n      transform: scale(1.2);\\n    }\\n    100% {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_whatsapp-wave {\\n    0%,\\n    100% {\\n      transform: scaleY(0.3);\\n    }\\n    50% {\\n      transform: scaleY(1);\\n    }\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelement", "ɵɵstyleProp", "ctx_r2", "Math", "abs", "sin", "i_r3", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "VoiceRecorderComponent_div_1_div_5_Template", "ɵɵtext", "ɵɵlistener", "VoiceRecorderComponent_div_1_Template_button_click_9_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "stopRecording", "VoiceRecorderComponent_div_1_Template_button_click_11_listener", "ctx_r6", "cancelRecording", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate", "ctx_r0", "formattedTime", "VoiceRecorderComponent_button_2_Template_button_click_0_listener", "_r8", "ctx_r7", "startRecording", "VoiceRecorderComponent", "constructor", "logger", "recordingComplete", "recordingCancelled", "maxDuration", "isRecording", "recordingTime", "mediaRecorder", "audioChunks", "audioStream", "ngOnInit", "ngOnDestroy", "stopMediaTracks", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "MediaRecorder", "ondataavailable", "event", "data", "size", "push", "onstop", "audioBlob", "Blob", "type", "emit", "start", "startTimer", "setTimeout", "error", "stop", "stopTimer", "timerInterval", "setInterval", "clearInterval", "getTracks", "for<PERSON>ach", "track", "minutes", "floor", "seconds", "toString", "padStart", "ɵɵdirectiveInject", "i1", "LoggerService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "VoiceRecorderComponent_Template", "rf", "ctx", "VoiceRecorderComponent_div_1_Template", "VoiceRecorderComponent_button_2_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\voice-recorder\\voice-recorder.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\voice-recorder\\voice-recorder.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  OnDestroy,\r\n  Output,\r\n  EventEmitter,\r\n  Input,\r\n} from '@angular/core';\r\nimport { LoggerService } from '../../services/logger.service';\r\n\r\n@Component({\r\n  selector: 'app-voice-recorder',\r\n  templateUrl: './voice-recorder.component.html',\r\n  styleUrls: ['./voice-recorder.component.css'],\r\n})\r\nexport class VoiceRecorderComponent implements OnInit, OnDestroy {\r\n  @Output() recordingComplete = new EventEmitter<Blob>();\r\n  @Output() recordingCancelled = new EventEmitter<void>();\r\n  @Input() maxDuration = 60; // Durée maximale en secondes\r\n\r\n  isRecording = false;\r\n  recordingTime = 0;\r\n  timerInterval: any;\r\n  mediaRecorder: MediaRecorder | null = null;\r\n  audioChunks: Blob[] = [];\r\n  audioStream: MediaStream | null = null;\r\n\r\n  // Exposer Math pour l'utiliser dans le template\r\n  Math = Math;\r\n\r\n  constructor(private logger: LoggerService) {}\r\n\r\n  ngOnInit(): void {}\r\n\r\n  ngOnDestroy(): void {\r\n    this.stopRecording();\r\n    this.stopMediaTracks();\r\n  }\r\n\r\n  /**\r\n   * Démarre l'enregistrement vocal\r\n   */\r\n  async startRecording(): Promise<void> {\r\n    try {\r\n      this.audioChunks = [];\r\n      this.recordingTime = 0;\r\n\r\n      // Demander l'accès au microphone\r\n      this.audioStream = await navigator.mediaDevices.getUserMedia({\r\n        audio: true,\r\n      });\r\n\r\n      // Créer un MediaRecorder\r\n      this.mediaRecorder = new MediaRecorder(this.audioStream);\r\n\r\n      // Configurer les gestionnaires d'événements\r\n      this.mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          this.audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      this.mediaRecorder.onstop = () => {\r\n        // Créer un blob à partir des chunks audio\r\n        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });\r\n        this.recordingComplete.emit(audioBlob);\r\n        this.stopMediaTracks();\r\n      };\r\n\r\n      // Démarrer l'enregistrement\r\n      this.mediaRecorder.start();\r\n      this.isRecording = true;\r\n\r\n      // Démarrer le timer\r\n      this.startTimer();\r\n\r\n      // Arrêter automatiquement après la durée maximale\r\n      setTimeout(() => {\r\n        if (this.isRecording) {\r\n          this.stopRecording();\r\n        }\r\n      }, this.maxDuration * 1000);\r\n    } catch (error) {\r\n      this.logger.error('Error starting voice recording:', error);\r\n      this.isRecording = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Arrête l'enregistrement vocal\r\n   */\r\n  stopRecording(): void {\r\n    if (this.mediaRecorder && this.isRecording) {\r\n      this.mediaRecorder.stop();\r\n      this.isRecording = false;\r\n      this.stopTimer();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Annule l'enregistrement vocal\r\n   */\r\n  cancelRecording(): void {\r\n    this.stopRecording();\r\n    this.stopMediaTracks();\r\n    this.recordingCancelled.emit();\r\n  }\r\n\r\n  /**\r\n   * Démarre le timer pour afficher la durée d'enregistrement\r\n   */\r\n  private startTimer(): void {\r\n    this.stopTimer();\r\n    this.timerInterval = setInterval(() => {\r\n      this.recordingTime++;\r\n\r\n      // Arrêter si on atteint la durée maximale\r\n      if (this.recordingTime >= this.maxDuration) {\r\n        this.stopRecording();\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  /**\r\n   * Arrête le timer\r\n   */\r\n  private stopTimer(): void {\r\n    if (this.timerInterval) {\r\n      clearInterval(this.timerInterval);\r\n      this.timerInterval = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Arrête les pistes média pour libérer le microphone\r\n   */\r\n  private stopMediaTracks(): void {\r\n    if (this.audioStream) {\r\n      this.audioStream.getTracks().forEach((track) => track.stop());\r\n      this.audioStream = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Formate le temps d'enregistrement en MM:SS\r\n   */\r\n  get formattedTime(): string {\r\n    const minutes = Math.floor(this.recordingTime / 60);\r\n    const seconds = this.recordingTime % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds\r\n      .toString()\r\n      .padStart(2, '0')}`;\r\n  }\r\n}\r\n", "<div class=\"whatsapp-voice-recorder\">\r\n  <!-- État d'enregistrement -->\r\n  <div class=\"whatsapp-voice-container\" *ngIf=\"isRecording\">\r\n    <!-- Indicateur d'enregistrement avec temps -->\r\n    <div class=\"whatsapp-voice-info\">\r\n      <!-- Indicateur d'enregistrement -->\r\n      <div class=\"whatsapp-recording-indicator\">\r\n        <div class=\"whatsapp-recording-dot\"></div>\r\n      </div>\r\n\r\n      <!-- Visualisation de l'onde sonore style WhatsApp -->\r\n      <div class=\"whatsapp-waveform\">\r\n        <div\r\n          *ngFor=\"let i of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\"\r\n          class=\"whatsapp-waveform-bar\"\r\n          [style.height.px]=\"5 + Math.abs(Math.sin(i / 3) * 15)\"\r\n          [style.animation-delay.ms]=\"i * 60\"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- Temps d'enregistrement -->\r\n      <span class=\"whatsapp-recording-time\">{{ formattedTime }}</span>\r\n    </div>\r\n\r\n    <!-- Contrôles d'enregistrement -->\r\n    <div class=\"whatsapp-voice-controls\">\r\n      <!-- Bouton d'arrêt -->\r\n      <button (click)=\"stopRecording()\" class=\"whatsapp-voice-stop-button\">\r\n        <i class=\"fas fa-paper-plane\"></i>\r\n      </button>\r\n\r\n      <!-- Bouton d'annulation -->\r\n      <button (click)=\"cancelRecording()\" class=\"whatsapp-voice-cancel-button\">\r\n        <i class=\"fas fa-trash\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Bouton d'enregistrement (quand pas en cours d'enregistrement) -->\r\n  <button\r\n    *ngIf=\"!isRecording\"\r\n    (click)=\"startRecording()\"\r\n    class=\"whatsapp-voice-start-button\"\r\n  >\r\n    <i class=\"fas fa-microphone\"></i>\r\n  </button>\r\n</div>\r\n\r\n<style>\r\n  /* Styles WhatsApp pour l'enregistrement vocal */\r\n  .whatsapp-voice-recorder {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n    position: relative;\r\n  }\r\n\r\n  .whatsapp-voice-container {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    background-color: #f0f2f5;\r\n    border-radius: 24px;\r\n    padding: 8px 12px;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  :host-context(.dark) .whatsapp-voice-container {\r\n    background-color: #2a2a2a;\r\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\r\n  }\r\n\r\n  .whatsapp-voice-info {\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1;\r\n  }\r\n\r\n  .whatsapp-recording-indicator {\r\n    position: relative;\r\n    margin-right: 12px;\r\n  }\r\n\r\n  .whatsapp-recording-dot {\r\n    width: 10px;\r\n    height: 10px;\r\n    background-color: #ff3b30;\r\n    border-radius: 50%;\r\n    animation: whatsapp-pulse 1.5s infinite;\r\n  }\r\n\r\n  .whatsapp-waveform {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 32px;\r\n    margin-right: 12px;\r\n    gap: 2px;\r\n  }\r\n\r\n  .whatsapp-waveform-bar {\r\n    width: 3px;\r\n    background-color: #25d366;\r\n    border-radius: 1.5px;\r\n    animation: whatsapp-wave 1.5s ease-in-out infinite;\r\n  }\r\n\r\n  :host-context(.dark) .whatsapp-waveform-bar {\r\n    background-color: #00c853;\r\n  }\r\n\r\n  .whatsapp-recording-time {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #333;\r\n  }\r\n\r\n  :host-context(.dark) .whatsapp-recording-time {\r\n    color: #e0e0e0;\r\n  }\r\n\r\n  .whatsapp-voice-controls {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n  }\r\n\r\n  .whatsapp-voice-stop-button {\r\n    width: 36px;\r\n    height: 36px;\r\n    border-radius: 50%;\r\n    background-color: #25d366;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: none;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .whatsapp-voice-stop-button:hover {\r\n    background-color: #128c7e;\r\n  }\r\n\r\n  .whatsapp-voice-cancel-button {\r\n    width: 36px;\r\n    height: 36px;\r\n    border-radius: 50%;\r\n    background-color: #f0f2f5;\r\n    color: #888;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: none;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  :host-context(.dark) .whatsapp-voice-cancel-button {\r\n    background-color: #3a3a3a;\r\n    color: #ccc;\r\n  }\r\n\r\n  .whatsapp-voice-cancel-button:hover {\r\n    background-color: #e0e0e0;\r\n    color: #666;\r\n  }\r\n\r\n  :host-context(.dark) .whatsapp-voice-cancel-button:hover {\r\n    background-color: #444;\r\n    color: #fff;\r\n  }\r\n\r\n  .whatsapp-voice-start-button {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    background-color: #25d366;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: none;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .whatsapp-voice-start-button:hover {\r\n    background-color: #128c7e;\r\n  }\r\n\r\n  @keyframes whatsapp-pulse {\r\n    0% {\r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n    50% {\r\n      opacity: 0.5;\r\n      transform: scale(1.2);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n  }\r\n\r\n  @keyframes whatsapp-wave {\r\n    0%,\r\n    100% {\r\n      transform: scaleY(0.3);\r\n    }\r\n    50% {\r\n      transform: scaleY(1);\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";AAAA,SAKEA,YAAY,QAEP,eAAe;;;;;;ICKdC,EAAA,CAAAC,SAAA,cAKO;;;;;IAFLD,EAAA,CAAAE,WAAA,eAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA,CAAAF,MAAA,CAAAC,IAAA,CAAAE,GAAA,CAAAC,IAAA,kBAAsD,oBAAAA,IAAA;;;;;;;;;IAb9DP,EAAA,CAAAQ,cAAA,aAA0D;IAKpDR,EAAA,CAAAC,SAAA,aAA0C;IAC5CD,EAAA,CAAAS,YAAA,EAAM;IAGNT,EAAA,CAAAQ,cAAA,aAA+B;IAC7BR,EAAA,CAAAU,UAAA,IAAAC,2CAAA,iBAKO;IACTX,EAAA,CAAAS,YAAA,EAAM;IAGNT,EAAA,CAAAQ,cAAA,cAAsC;IAAAR,EAAA,CAAAY,MAAA,GAAmB;IAAAZ,EAAA,CAAAS,YAAA,EAAO;IAIlET,EAAA,CAAAQ,cAAA,cAAqC;IAE3BR,EAAA,CAAAa,UAAA,mBAAAC,8DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC/BpB,EAAA,CAAAC,SAAA,aAAkC;IACpCD,EAAA,CAAAS,YAAA,EAAS;IAGTT,EAAA,CAAAQ,cAAA,kBAAyE;IAAjER,EAAA,CAAAa,UAAA,mBAAAQ,+DAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAtB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAG,MAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACjCvB,EAAA,CAAAC,SAAA,aAA4B;IAC9BD,EAAA,CAAAS,YAAA,EAAS;;;;IArBST,EAAA,CAAAwB,SAAA,GAAsD;IAAtDxB,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAsD;IAQlC3B,EAAA,CAAAwB,SAAA,GAAmB;IAAnBxB,EAAA,CAAA4B,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;;;;;;IAkB7D9B,EAAA,CAAAQ,cAAA,iBAIC;IAFCR,EAAA,CAAAa,UAAA,mBAAAkB,iEAAA;MAAA/B,EAAA,CAAAe,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAjC,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAc,MAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAG1BlC,EAAA,CAAAC,SAAA,YAAiC;IACnCD,EAAA,CAAAS,YAAA,EAAS;;;AD9BX,OAAM,MAAO0B,sBAAsB;EAejCC,YAAoBC,MAAqB;IAArB,KAAAA,MAAM,GAANA,MAAM;IAdhB,KAAAC,iBAAiB,GAAG,IAAIvC,YAAY,EAAQ;IAC5C,KAAAwC,kBAAkB,GAAG,IAAIxC,YAAY,EAAQ;IAC9C,KAAAyC,WAAW,GAAG,EAAE,CAAC,CAAC;IAE3B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,aAAa,GAAG,CAAC;IAEjB,KAAAC,aAAa,GAAyB,IAAI;IAC1C,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAzC,IAAI,GAAGA,IAAI;EAEiC;EAE5C0C,QAAQA,CAAA,GAAU;EAElBC,WAAWA,CAAA;IACT,IAAI,CAAC3B,aAAa,EAAE;IACpB,IAAI,CAAC4B,eAAe,EAAE;EACxB;EAEA;;;EAGMd,cAAcA,CAAA;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAI;QACFD,KAAI,CAACL,WAAW,GAAG,EAAE;QACrBK,KAAI,CAACP,aAAa,GAAG,CAAC;QAEtB;QACAO,KAAI,CAACJ,WAAW,SAASM,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAC3DC,KAAK,EAAE;SACR,CAAC;QAEF;QACAL,KAAI,CAACN,aAAa,GAAG,IAAIY,aAAa,CAACN,KAAI,CAACJ,WAAW,CAAC;QAExD;QACAI,KAAI,CAACN,aAAa,CAACa,eAAe,GAAIC,KAAK,IAAI;UAC7C,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;YACvBV,KAAI,CAACL,WAAW,CAACgB,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;;QAErC,CAAC;QAEDT,KAAI,CAACN,aAAa,CAACkB,MAAM,GAAG,MAAK;UAC/B;UACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACd,KAAI,CAACL,WAAW,EAAE;YAAEoB,IAAI,EAAE;UAAY,CAAE,CAAC;UACpEf,KAAI,CAACX,iBAAiB,CAAC2B,IAAI,CAACH,SAAS,CAAC;UACtCb,KAAI,CAACD,eAAe,EAAE;QACxB,CAAC;QAED;QACAC,KAAI,CAACN,aAAa,CAACuB,KAAK,EAAE;QAC1BjB,KAAI,CAACR,WAAW,GAAG,IAAI;QAEvB;QACAQ,KAAI,CAACkB,UAAU,EAAE;QAEjB;QACAC,UAAU,CAAC,MAAK;UACd,IAAInB,KAAI,CAACR,WAAW,EAAE;YACpBQ,KAAI,CAAC7B,aAAa,EAAE;;QAExB,CAAC,EAAE6B,KAAI,CAACT,WAAW,GAAG,IAAI,CAAC;OAC5B,CAAC,OAAO6B,KAAK,EAAE;QACdpB,KAAI,CAACZ,MAAM,CAACgC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAC3DpB,KAAI,CAACR,WAAW,GAAG,KAAK;;IACzB;EACH;EAEA;;;EAGArB,aAAaA,CAAA;IACX,IAAI,IAAI,CAACuB,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAAC2B,IAAI,EAAE;MACzB,IAAI,CAAC7B,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC8B,SAAS,EAAE;;EAEpB;EAEA;;;EAGAhD,eAAeA,CAAA;IACb,IAAI,CAACH,aAAa,EAAE;IACpB,IAAI,CAAC4B,eAAe,EAAE;IACtB,IAAI,CAACT,kBAAkB,CAAC0B,IAAI,EAAE;EAChC;EAEA;;;EAGQE,UAAUA,CAAA;IAChB,IAAI,CAACI,SAAS,EAAE;IAChB,IAAI,CAACC,aAAa,GAAGC,WAAW,CAAC,MAAK;MACpC,IAAI,CAAC/B,aAAa,EAAE;MAEpB;MACA,IAAI,IAAI,CAACA,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;QAC1C,IAAI,CAACpB,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQmD,SAASA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBE,aAAa,CAAC,IAAI,CAACF,aAAa,CAAC;MACjC,IAAI,CAACA,aAAa,GAAG,IAAI;;EAE7B;EAEA;;;EAGQxB,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACH,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC8B,SAAS,EAAE,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACP,IAAI,EAAE,CAAC;MAC7D,IAAI,CAACzB,WAAW,GAAG,IAAI;;EAE3B;EAEA;;;EAGA,IAAIf,aAAaA,CAAA;IACf,MAAMgD,OAAO,GAAG1E,IAAI,CAAC2E,KAAK,CAAC,IAAI,CAACrC,aAAa,GAAG,EAAE,CAAC;IACnD,MAAMsC,OAAO,GAAG,IAAI,CAACtC,aAAa,GAAG,EAAE;IACvC,OAAO,GAAGoC,OAAO,CAACG,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CACrDC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;;;uBAzIW/C,sBAAsB,EAAAnC,EAAA,CAAAmF,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtBlD,sBAAsB;MAAAmD,SAAA;MAAAC,MAAA;QAAA/C,WAAA;MAAA;MAAAgD,OAAA;QAAAlD,iBAAA;QAAAC,kBAAA;MAAA;MAAAkD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfnC9F,EAAA,CAAAQ,cAAA,aAAqC;UAEnCR,EAAA,CAAAU,UAAA,IAAAsF,qCAAA,kBAkCM;UAGNhG,EAAA,CAAAU,UAAA,IAAAuF,wCAAA,oBAMS;UACXjG,EAAA,CAAAS,YAAA,EAAM;;;UA5CmCT,EAAA,CAAAwB,SAAA,GAAiB;UAAjBxB,EAAA,CAAAyB,UAAA,SAAAsE,GAAA,CAAAtD,WAAA,CAAiB;UAsCrDzC,EAAA,CAAAwB,SAAA,GAAkB;UAAlBxB,EAAA,CAAAyB,UAAA,UAAAsE,GAAA,CAAAtD,WAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}