{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  constructor(_platformId) {\n    this._platformId = _platformId;\n    // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n    /** Whether the Angular application is being rendered in the browser. */\n    this.isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n    /** Whether the current browser is Microsoft Edge. */\n    this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n    /** Whether the current rendering engine is Microsoft Trident. */\n    this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n    // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n    /** Whether the current rendering engine is Blink. */\n    this.BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n    // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n    /** Whether the current rendering engine is WebKit. */\n    this.WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n    /** Whether the current platform is Apple iOS. */\n    this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n    // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n    /** Whether the current browser is Firefox. */\n    this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n    this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n    // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n    /** Whether the current browser is Safari. */\n    this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  }\n  static {\n    this.ɵfac = function Platform_Factory(t) {\n      return new (t || Platform)(i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Platform,\n      factory: Platform.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\nclass PlatformModule {\n  static {\n    this.ɵfac = function PlatformModule_Factory(t) {\n      return new (t || PlatformModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PlatformModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return 0 /* RtlScrollAxisType.NORMAL */;\n  }\n\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = 0 /* RtlScrollAxisType.NORMAL */;\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? 1 /* RtlScrollAxisType.NEGATED */ : 2 /* RtlScrollAxisType.INVERTED */;\n    }\n\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\n\n/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n  return (\n    // @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ ||\n    // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine ||\n    // @ts-ignore\n    typeof jest !== 'undefined' && !!jest ||\n    // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };", "map": {"version": 3, "names": ["i0", "PLATFORM_ID", "Injectable", "Inject", "NgModule", "isPlatformBrowser", "hasV8BreakIterator", "Intl", "v8BreakIterator", "Platform", "constructor", "_platformId", "<PERSON><PERSON><PERSON><PERSON>", "document", "EDGE", "test", "navigator", "userAgent", "TRIDENT", "BLINK", "window", "chrome", "CSS", "WEBKIT", "IOS", "FIREFOX", "ANDROID", "SAFARI", "ɵfac", "Platform_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "Object", "decorators", "PlatformModule", "PlatformModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "supportedInputTypes", "candidateInputTypes", "getSupportedInputTypes", "Set", "featureTestInput", "createElement", "filter", "value", "setAttribute", "supportsPassiveEvents", "supportsPassiveEventListeners", "addEventListener", "defineProperty", "get", "normalizePassiveListenerOptions", "options", "capture", "rtlScrollAxisType", "scrollBehaviorSupported", "supportsScrollBehavior", "Element", "documentElement", "style", "scrollToFunction", "prototype", "scrollTo", "toString", "getRtlScrollAxisType", "scrollContainer", "containerStyle", "dir", "width", "overflow", "visibility", "pointerEvents", "position", "content", "contentStyle", "height", "append<PERSON><PERSON><PERSON>", "body", "scrollLeft", "remove", "shadowDomIsSupported", "_supportsShadowDom", "head", "createShadowRoot", "attachShadow", "_getShadowRoot", "element", "rootNode", "getRootNode", "ShadowRoot", "_getFocusedElementPierceShadowDom", "activeElement", "shadowRoot", "newActiveElement", "_getEventTarget", "event", "<PERSON><PERSON><PERSON>", "target", "_isTestEnvironment", "__karma__", "jasmine", "jest", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@angular/cdk/fesm2022/platform.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n}\ncatch {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    constructor(_platformId) {\n        this._platformId = _platformId;\n        // We want to use the Angular platform check because if the Document is shimmed\n        // without the navigator, the following checks will fail. This is preferred because\n        // sometimes the Document may be shimmed without the user's knowledge or intention\n        /** Whether the Angular application is being rendered in the browser. */\n        this.isBrowser = this._platformId\n            ? isPlatformBrowser(this._platformId)\n            : typeof document === 'object' && !!document;\n        /** Whether the current browser is Microsoft Edge. */\n        this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n        /** Whether the current rendering engine is Microsoft Trident. */\n        this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n        // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n        /** Whether the current rendering engine is Blink. */\n        this.BLINK = this.isBrowser &&\n            !!(window.chrome || hasV8BreakIterator) &&\n            typeof CSS !== 'undefined' &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n        // ensure that Webkit runs standalone and is not used as another engine's base.\n        /** Whether the current rendering engine is WebKit. */\n        this.WEBKIT = this.isBrowser &&\n            /AppleWebKit/i.test(navigator.userAgent) &&\n            !this.BLINK &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        /** Whether the current platform is Apple iOS. */\n        this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n        // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n        // them self as Gecko-like browsers and modify the userAgent's according to that.\n        // Since we only cover one explicit Firefox case, we can simply check for Firefox\n        // instead of having an unstable check for Gecko.\n        /** Whether the current browser is Firefox. */\n        this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n        /** Whether the current platform is Android. */\n        // Trident on mobile adds the android platform to the userAgent to trick detections.\n        this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n        // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n        // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n        // Safari browser should also use Webkit as its layout engine.\n        /** Whether the current browser is Safari. */\n        this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\nclass PlatformModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: PlatformModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n    // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n    // first changing it to something else:\n    // The specified value \"\" does not conform to the required format.\n    // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n    'color',\n    'button',\n    'checkbox',\n    'date',\n    'datetime-local',\n    'email',\n    'file',\n    'hidden',\n    'image',\n    'month',\n    'number',\n    'password',\n    'radio',\n    'range',\n    'reset',\n    'search',\n    'submit',\n    'tel',\n    'text',\n    'time',\n    'url',\n    'week',\n];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n    // Result is cached.\n    if (supportedInputTypes) {\n        return supportedInputTypes;\n    }\n    // We can't check if an input type is not supported until we're on the browser, so say that\n    // everything is supported when not on the browser. We don't use `Platform` here since it's\n    // just a helper function and can't inject it.\n    if (typeof document !== 'object' || !document) {\n        supportedInputTypes = new Set(candidateInputTypes);\n        return supportedInputTypes;\n    }\n    let featureTestInput = document.createElement('input');\n    supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n        featureTestInput.setAttribute('type', value);\n        return featureTestInput.type === value;\n    }));\n    return supportedInputTypes;\n}\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n    if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n        try {\n            window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n                get: () => (supportsPassiveEvents = true),\n            }));\n        }\n        finally {\n            supportsPassiveEvents = supportsPassiveEvents || false;\n        }\n    }\n    return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n    return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return 0 /* RtlScrollAxisType.NORMAL */;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = 0 /* RtlScrollAxisType.NORMAL */;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? 1 /* RtlScrollAxisType.NEGATED */ : 2 /* RtlScrollAxisType.INVERTED */;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n    if (shadowDomIsSupported == null) {\n        const head = typeof document !== 'undefined' ? document.head : null;\n        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n    }\n    return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n    if (_supportsShadowDom()) {\n        const rootNode = element.getRootNode ? element.getRootNode() : null;\n        // Note that this should be caught by `_supportsShadowDom`, but some\n        // teams have been able to hit this code path on unsupported browsers.\n        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n            return rootNode;\n        }\n    }\n    return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n    let activeElement = typeof document !== 'undefined' && document\n        ? document.activeElement\n        : null;\n    while (activeElement && activeElement.shadowRoot) {\n        const newActiveElement = activeElement.shadowRoot.activeElement;\n        if (newActiveElement === activeElement) {\n            break;\n        }\n        else {\n            activeElement = newActiveElement;\n        }\n    }\n    return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n    // If an event is bound outside the Shadow DOM, the `event.target` will\n    // point to the shadow root so we have to use `composedPath` instead.\n    return (event.composedPath ? event.composedPath()[0] : event.target);\n}\n\n/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n    // We can't use `declare const` because it causes conflicts inside Google with the real typings\n    // for these symbols and we can't read them off the global object, because they don't appear to\n    // be attached there for some runners like Jest.\n    // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n    return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n        // @ts-ignore\n        (typeof jasmine !== 'undefined' && !!jasmine) ||\n        // @ts-ignore\n        (typeof jest !== 'undefined' && !!jest) ||\n        // @ts-ignore\n        (typeof Mocha !== 'undefined' && !!Mocha));\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACzE,SAASC,iBAAiB,QAAQ,iBAAiB;;AAEnD;AACA;AACA,IAAIC,kBAAkB;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI;EACAA,kBAAkB,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,eAAe;AAC5E,CAAC,CACD,MAAM;EACFF,kBAAkB,GAAG,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMG,QAAQ,CAAC;EACXC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACD,WAAW,GAC3BN,iBAAiB,CAAC,IAAI,CAACM,WAAW,CAAC,GACnC,OAAOE,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ;IAChD;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACF,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IACjE;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACN,SAAS,IAAI,iBAAiB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IAC5E;IACA;IACA,IAAI,CAACE,KAAK,GAAG,IAAI,CAACP,SAAS,IACvB,CAAC,EAAEQ,MAAM,CAACC,MAAM,IAAIf,kBAAkB,CAAC,IACvC,OAAOgB,GAAG,KAAK,WAAW,IAC1B,CAAC,IAAI,CAACR,IAAI,IACV,CAAC,IAAI,CAACI,OAAO;IACjB;IACA;IACA;IACA,IAAI,CAACK,MAAM,GAAG,IAAI,CAACX,SAAS,IACxB,cAAc,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IACxC,CAAC,IAAI,CAACE,KAAK,IACX,CAAC,IAAI,CAACL,IAAI,IACV,CAAC,IAAI,CAACI,OAAO;IACjB;IACA,IAAI,CAACM,GAAG,GAAG,IAAI,CAACZ,SAAS,IAAI,kBAAkB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,EAAE,UAAU,IAAIG,MAAM,CAAC;IACpG;IACA;IACA;IACA;IACA;IACA,IAAI,CAACK,OAAO,GAAG,IAAI,CAACb,SAAS,IAAI,sBAAsB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;IACjF;IACA;IACA,IAAI,CAACS,OAAO,GAAG,IAAI,CAACd,SAAS,IAAI,UAAU,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAACC,OAAO;IACtF;IACA;IACA;IACA;IACA,IAAI,CAACS,MAAM,GAAG,IAAI,CAACf,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,IAAI,CAACM,MAAM;EACtF;EACA;IAAS,IAAI,CAACK,IAAI,YAAAC,iBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFrB,QAAQ,EAAlBT,EAAE,CAAA+B,QAAA,CAAkC9B,WAAW;IAAA,CAA6C;EAAE;EAC9L;IAAS,IAAI,CAAC+B,KAAK,kBAD6EhC,EAAE,CAAAiC,kBAAA;MAAAC,KAAA,EACYzB,QAAQ;MAAA0B,OAAA,EAAR1B,QAAQ,CAAAmB,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrC,EAAE,CAAAsC,iBAAA,CAGX7B,QAAQ,EAAc,CAAC;IACtG8B,IAAI,EAAErC,UAAU;IAChBsC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEE,MAAM;MAAEC,UAAU,EAAE,CAAC;QAC3DH,IAAI,EAAEpC,MAAM;QACZqC,IAAI,EAAE,CAACvC,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM0C,cAAc,CAAC;EACjB;IAAS,IAAI,CAACf,IAAI,YAAAgB,uBAAAd,CAAA;MAAA,YAAAA,CAAA,IAAwFa,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAb8E7C,EAAE,CAAA8C,gBAAA;MAAAP,IAAA,EAaSI;IAAc,EAAG;EAAE;EAC9H;IAAS,IAAI,CAACI,IAAI,kBAd8E/C,EAAE,CAAAgD,gBAAA,IAc0B;EAAE;AAClI;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAhBoGrC,EAAE,CAAAsC,iBAAA,CAgBXK,cAAc,EAAc,CAAC;IAC5GJ,IAAI,EAAEnC,QAAQ;IACdoC,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AAAA;;AAEV;AACA,IAAIS,mBAAmB;AACvB;AACA,MAAMC,mBAAmB,GAAG;AACxB;AACA;AACA;AACA;AACA,OAAO,EACP,QAAQ,EACR,UAAU,EACV,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,CACT;AACD;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B;EACA,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB;EAC9B;EACA;EACA;EACA;EACA,IAAI,OAAOpC,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3CoC,mBAAmB,GAAG,IAAIG,GAAG,CAACF,mBAAmB,CAAC;IAClD,OAAOD,mBAAmB;EAC9B;EACA,IAAII,gBAAgB,GAAGxC,QAAQ,CAACyC,aAAa,CAAC,OAAO,CAAC;EACtDL,mBAAmB,GAAG,IAAIG,GAAG,CAACF,mBAAmB,CAACK,MAAM,CAACC,KAAK,IAAI;IAC9DH,gBAAgB,CAACI,YAAY,CAAC,MAAM,EAAED,KAAK,CAAC;IAC5C,OAAOH,gBAAgB,CAACd,IAAI,KAAKiB,KAAK;EAC1C,CAAC,CAAC,CAAC;EACH,OAAOP,mBAAmB;AAC9B;;AAEA;AACA,IAAIS,qBAAqB;AACzB;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,IAAID,qBAAqB,IAAI,IAAI,IAAI,OAAOtC,MAAM,KAAK,WAAW,EAAE;IAChE,IAAI;MACAA,MAAM,CAACwC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAEnB,MAAM,CAACoB,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;QACvEC,GAAG,EAAEA,CAAA,KAAOJ,qBAAqB,GAAG;MACxC,CAAC,CAAC,CAAC;IACP,CAAC,SACO;MACJA,qBAAqB,GAAGA,qBAAqB,IAAI,KAAK;IAC1D;EACJ;EACA,OAAOA,qBAAqB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,+BAA+BA,CAACC,OAAO,EAAE;EAC9C,OAAOL,6BAA6B,CAAC,CAAC,GAAGK,OAAO,GAAG,CAAC,CAACA,OAAO,CAACC,OAAO;AACxE;;AAEA;AACA,IAAIC,iBAAiB;AACrB;AACA,IAAIC,uBAAuB;AAC3B;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B,IAAID,uBAAuB,IAAI,IAAI,EAAE;IACjC;IACA;IACA,IAAI,OAAOtD,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,IAAI,OAAOwD,OAAO,KAAK,UAAU,IAAI,CAACA,OAAO,EAAE;MACxFF,uBAAuB,GAAG,KAAK;MAC/B,OAAOA,uBAAuB;IAClC;IACA;IACA,IAAI,gBAAgB,IAAItD,QAAQ,CAACyD,eAAe,CAACC,KAAK,EAAE;MACpDJ,uBAAuB,GAAG,IAAI;IAClC,CAAC,MACI;MACD;MACA;MACA,MAAMK,gBAAgB,GAAGH,OAAO,CAACI,SAAS,CAACC,QAAQ;MACnD,IAAIF,gBAAgB,EAAE;QAClB;QACA;QACA;QACA;QACAL,uBAAuB,GAAG,CAAC,2BAA2B,CAACpD,IAAI,CAACyD,gBAAgB,CAACG,QAAQ,CAAC,CAAC,CAAC;MAC5F,CAAC,MACI;QACDR,uBAAuB,GAAG,KAAK;MACnC;IACJ;EACJ;EACA,OAAOA,uBAAuB;AAClC;AACA;AACA;AACA;AACA;AACA,SAASS,oBAAoBA,CAAA,EAAG;EAC5B;EACA,IAAI,OAAO/D,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3C,OAAO,CAAC,CAAC;EACb;;EACA,IAAIqD,iBAAiB,IAAI,IAAI,EAAE;IAC3B;IACA,MAAMW,eAAe,GAAGhE,QAAQ,CAACyC,aAAa,CAAC,KAAK,CAAC;IACrD,MAAMwB,cAAc,GAAGD,eAAe,CAACN,KAAK;IAC5CM,eAAe,CAACE,GAAG,GAAG,KAAK;IAC3BD,cAAc,CAACE,KAAK,GAAG,KAAK;IAC5BF,cAAc,CAACG,QAAQ,GAAG,MAAM;IAChCH,cAAc,CAACI,UAAU,GAAG,QAAQ;IACpCJ,cAAc,CAACK,aAAa,GAAG,MAAM;IACrCL,cAAc,CAACM,QAAQ,GAAG,UAAU;IACpC,MAAMC,OAAO,GAAGxE,QAAQ,CAACyC,aAAa,CAAC,KAAK,CAAC;IAC7C,MAAMgC,YAAY,GAAGD,OAAO,CAACd,KAAK;IAClCe,YAAY,CAACN,KAAK,GAAG,KAAK;IAC1BM,YAAY,CAACC,MAAM,GAAG,KAAK;IAC3BV,eAAe,CAACW,WAAW,CAACH,OAAO,CAAC;IACpCxE,QAAQ,CAAC4E,IAAI,CAACD,WAAW,CAACX,eAAe,CAAC;IAC1CX,iBAAiB,GAAG,CAAC,CAAC;IACtB;IACA;IACA;IACA,IAAIW,eAAe,CAACa,UAAU,KAAK,CAAC,EAAE;MAClC;MACA;MACA;MACA;MACAb,eAAe,CAACa,UAAU,GAAG,CAAC;MAC9BxB,iBAAiB,GACbW,eAAe,CAACa,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,kCAAkC,CAAC,CAAC;IACjF;;IACAb,eAAe,CAACc,MAAM,CAAC,CAAC;EAC5B;EACA,OAAOzB,iBAAiB;AAC5B;AAEA,IAAI0B,oBAAoB;AACxB;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAID,oBAAoB,IAAI,IAAI,EAAE;IAC9B,MAAME,IAAI,GAAG,OAAOjF,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACiF,IAAI,GAAG,IAAI;IACnEF,oBAAoB,GAAG,CAAC,EAAEE,IAAI,KAAKA,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACE,YAAY,CAAC,CAAC;EACnF;EACA,OAAOJ,oBAAoB;AAC/B;AACA;AACA,SAASK,cAAcA,CAACC,OAAO,EAAE;EAC7B,IAAIL,kBAAkB,CAAC,CAAC,EAAE;IACtB,MAAMM,QAAQ,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACE,WAAW,CAAC,CAAC,GAAG,IAAI;IACnE;IACA;IACA,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAAIF,QAAQ,YAAYE,UAAU,EAAE;MACnF,OAAOF,QAAQ;IACnB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,SAASG,iCAAiCA,CAAA,EAAG;EACzC,IAAIC,aAAa,GAAG,OAAO1F,QAAQ,KAAK,WAAW,IAAIA,QAAQ,GACzDA,QAAQ,CAAC0F,aAAa,GACtB,IAAI;EACV,OAAOA,aAAa,IAAIA,aAAa,CAACC,UAAU,EAAE;IAC9C,MAAMC,gBAAgB,GAAGF,aAAa,CAACC,UAAU,CAACD,aAAa;IAC/D,IAAIE,gBAAgB,KAAKF,aAAa,EAAE;MACpC;IACJ,CAAC,MACI;MACDA,aAAa,GAAGE,gBAAgB;IACpC;EACJ;EACA,OAAOF,aAAa;AACxB;AACA;AACA,SAASG,eAAeA,CAACC,KAAK,EAAE;EAC5B;EACA;EACA,OAAQA,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACE,MAAM;AACvE;;AAEA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B;EACA;EACA;EACA;EACA;IACA;IACC,OAAOC,SAAS,KAAK,WAAW,IAAI,CAAC,CAACA,SAAS;IAC5C;IACC,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAQ;IAC7C;IACC,OAAOC,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAK;IACvC;IACC,OAAOC,KAAK,KAAK,WAAW,IAAI,CAAC,CAACA;EAAM;AACjD;;AAEA;AACA;AACA;;AAEA,SAASzG,QAAQ,EAAEkC,cAAc,EAAE+D,eAAe,EAAEJ,iCAAiC,EAAEL,cAAc,EAAEa,kBAAkB,EAAEjB,kBAAkB,EAAEjB,oBAAoB,EAAEzB,sBAAsB,EAAEY,+BAA+B,EAAEJ,6BAA6B,EAAES,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}