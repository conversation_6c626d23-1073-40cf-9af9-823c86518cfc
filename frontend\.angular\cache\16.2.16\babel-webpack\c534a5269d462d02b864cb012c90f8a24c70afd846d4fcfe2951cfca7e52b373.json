{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AdminLayoutComponent } from './admin-layout/admin-layout.component';\nimport { FrontLayoutComponent } from './front-layout/front-layout.component';\nimport { AuthAdminLayoutComponent } from './auth-admin-layout/auth-admin-layout.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class LayoutsModule {\n  static {\n    this.ɵfac = function LayoutsModule_Factory(t) {\n      return new (t || LayoutsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LayoutsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LayoutsModule, {\n    declarations: [AdminLayoutComponent, FrontLayoutComponent, AuthAdminLayoutComponent],\n    imports: [CommonModule, RouterModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AdminLayoutComponent", "FrontLayoutComponent", "AuthAdminLayoutComponent", "RouterModule", "FormsModule", "LayoutsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\layouts\\layouts.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AdminLayoutComponent } from './admin-layout/admin-layout.component';\r\nimport { FrontLayoutComponent } from './front-layout/front-layout.component';\r\nimport { AuthAdminLayoutComponent } from './auth-admin-layout/auth-admin-layout.component';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AdminLayoutComponent,\r\n    FrontLayoutComponent,\r\n    AuthAdminLayoutComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    FormsModule\r\n  ]\r\n})\r\nexport class LayoutsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;AAgB5C,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBALtBN,YAAY,EACZI,YAAY,EACZC,WAAW;IAAA;EAAA;;;2EAGFC,aAAa;IAAAC,YAAA,GAVtBN,oBAAoB,EACpBC,oBAAoB,EACpBC,wBAAwB;IAAAK,OAAA,GAGxBR,YAAY,EACZI,YAAY,EACZC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}