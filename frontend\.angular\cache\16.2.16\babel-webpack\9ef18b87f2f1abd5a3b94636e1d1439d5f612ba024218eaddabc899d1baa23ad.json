{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\n\n/**\n * Lone anonymous operation\n *\n * A GraphQL document is only valid if when it contains an anonymous operation\n * (the query short-hand) that it contains only that one operation definition.\n *\n * See https://spec.graphql.org/draft/#sec-Lone-Anonymous-Operation\n */\nexport function LoneAnonymousOperationRule(context) {\n  let operationCount = 0;\n  return {\n    Document(node) {\n      operationCount = node.definitions.filter(definition => definition.kind === Kind.OPERATION_DEFINITION).length;\n    },\n    OperationDefinition(node) {\n      if (!node.name && operationCount > 1) {\n        context.reportError(new GraphQLError('This anonymous operation must be the only defined operation.', {\n          nodes: node\n        }));\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "Kind", "LoneAnonymousOperationRule", "context", "operationCount", "Document", "node", "definitions", "filter", "definition", "kind", "OPERATION_DEFINITION", "length", "OperationDefinition", "name", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/LoneAnonymousOperationRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\n\n/**\n * Lone anonymous operation\n *\n * A GraphQL document is only valid if when it contains an anonymous operation\n * (the query short-hand) that it contains only that one operation definition.\n *\n * See https://spec.graphql.org/draft/#sec-Lone-Anonymous-Operation\n */\nexport function LoneAnonymousOperationRule(context) {\n  let operationCount = 0;\n  return {\n    Document(node) {\n      operationCount = node.definitions.filter(\n        (definition) => definition.kind === Kind.OPERATION_DEFINITION,\n      ).length;\n    },\n\n    OperationDefinition(node) {\n      if (!node.name && operationCount > 1) {\n        context.reportError(\n          new GraphQLError(\n            'This anonymous operation must be the only defined operation.',\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,IAAI,QAAQ,0BAA0B;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EAClD,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO;IACLC,QAAQA,CAACC,IAAI,EAAE;MACbF,cAAc,GAAGE,IAAI,CAACC,WAAW,CAACC,MAAM,CACrCC,UAAU,IAAKA,UAAU,CAACC,IAAI,KAAKT,IAAI,CAACU,oBAC3C,CAAC,CAACC,MAAM;IACV,CAAC;IAEDC,mBAAmBA,CAACP,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,CAACQ,IAAI,IAAIV,cAAc,GAAG,CAAC,EAAE;QACpCD,OAAO,CAACY,WAAW,CACjB,IAAIf,YAAY,CACd,8DAA8D,EAC9D;UACEgB,KAAK,EAAEV;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}