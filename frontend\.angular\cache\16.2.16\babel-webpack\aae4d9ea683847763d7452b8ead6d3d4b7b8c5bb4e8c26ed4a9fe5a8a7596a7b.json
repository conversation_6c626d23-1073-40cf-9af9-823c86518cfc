{"ast": null, "code": "import { AutoCleanedStrongCache, cacheSizes } from \"../../utilities/caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n/**\n * Like JSON.stringify, but with object keys always sorted in the same order.\n *\n * To achieve performant sorting, this function uses a Map from JSON-serialized\n * arrays of keys (in any order) to sorted arrays of the same keys, with a\n * single sorted array reference shared by all permutations of the keys.\n *\n * As a drawback, this function will add a little bit more memory for every\n * object encountered that has different (more, less, a different order of) keys\n * than in the past.\n *\n * In a typical application, this extra memory usage should not play a\n * significant role, as `canonicalStringify` will be called for only a limited\n * number of object shapes, and the cache will not grow beyond a certain point.\n * But in some edge cases, this could be a problem, so we provide\n * canonicalStringify.reset() as a way of clearing the cache.\n * */\nexport var canonicalStringify = Object.assign(function canonicalStringify(value) {\n  return JSON.stringify(value, stableObjectReplacer);\n}, {\n  reset: function () {\n    // Clearing the sortingMap will reclaim all cached memory, without\n    // affecting the logical results of canonicalStringify, but potentially\n    // sacrificing performance until the cache is refilled.\n    sortingMap = new AutoCleanedStrongCache(cacheSizes.canonicalStringify || 1000 /* defaultCacheSizes.canonicalStringify */);\n  }\n});\n\nif (globalThis.__DEV__ !== false) {\n  registerGlobalCache(\"canonicalStringify\", function () {\n    return sortingMap.size;\n  });\n}\n// Values are JSON-serialized arrays of object keys (in any order), and values\n// are sorted arrays of the same keys.\nvar sortingMap;\ncanonicalStringify.reset();\n// The JSON.stringify function takes an optional second argument called a\n// replacer function. This function is called for each key-value pair in the\n// object being stringified, and its return value is used instead of the\n// original value. If the replacer function returns a new value, that value is\n// stringified as JSON instead of the original value of the property.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#the_replacer_parameter\nfunction stableObjectReplacer(key, value) {\n  if (value && typeof value === \"object\") {\n    var proto = Object.getPrototypeOf(value);\n    // We don't want to mess with objects that are not \"plain\" objects, which\n    // means their prototype is either Object.prototype or null. This check also\n    // prevents needlessly rearranging the indices of arrays.\n    if (proto === Object.prototype || proto === null) {\n      var keys = Object.keys(value);\n      // If keys is already sorted, let JSON.stringify serialize the original\n      // value instead of creating a new object with keys in the same order.\n      if (keys.every(everyKeyInOrder)) return value;\n      var unsortedKey = JSON.stringify(keys);\n      var sortedKeys = sortingMap.get(unsortedKey);\n      if (!sortedKeys) {\n        keys.sort();\n        var sortedKey = JSON.stringify(keys);\n        // Checking for sortedKey in the sortingMap allows us to share the same\n        // sorted array reference for all permutations of the same set of keys.\n        sortedKeys = sortingMap.get(sortedKey) || keys;\n        sortingMap.set(unsortedKey, sortedKeys);\n        sortingMap.set(sortedKey, sortedKeys);\n      }\n      var sortedObject_1 = Object.create(proto);\n      // Reassigning the keys in sorted order will cause JSON.stringify to\n      // serialize them in sorted order.\n      sortedKeys.forEach(function (key) {\n        sortedObject_1[key] = value[key];\n      });\n      return sortedObject_1;\n    }\n  }\n  return value;\n}\n// Since everything that happens in stableObjectReplacer benefits from being as\n// efficient as possible, we use a static function as the callback for\n// keys.every in order to test if the provided keys are already sorted without\n// allocating extra memory for a callback.\nfunction everyKeyInOrder(key, i, keys) {\n  return i === 0 || keys[i - 1] <= key;\n}", "map": {"version": 3, "names": ["AutoCleanedStrongCache", "cacheSizes", "registerGlobalCache", "canonicalStringify", "Object", "assign", "value", "JSON", "stringify", "stableObjectReplacer", "reset", "sortingMap", "globalThis", "__DEV__", "size", "key", "proto", "getPrototypeOf", "prototype", "keys", "every", "everyKeyInOrder", "unsorted<PERSON><PERSON>", "sortedKeys", "get", "sort", "<PERSON><PERSON><PERSON>", "set", "sortedObject_1", "create", "for<PERSON>ach", "i"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/canonicalStringify.js"], "sourcesContent": ["import { AutoCleanedStrongCache, cacheSizes, } from \"../../utilities/caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n/**\n * Like JSON.stringify, but with object keys always sorted in the same order.\n *\n * To achieve performant sorting, this function uses a Map from JSON-serialized\n * arrays of keys (in any order) to sorted arrays of the same keys, with a\n * single sorted array reference shared by all permutations of the keys.\n *\n * As a drawback, this function will add a little bit more memory for every\n * object encountered that has different (more, less, a different order of) keys\n * than in the past.\n *\n * In a typical application, this extra memory usage should not play a\n * significant role, as `canonicalStringify` will be called for only a limited\n * number of object shapes, and the cache will not grow beyond a certain point.\n * But in some edge cases, this could be a problem, so we provide\n * canonicalStringify.reset() as a way of clearing the cache.\n * */\nexport var canonicalStringify = Object.assign(function canonicalStringify(value) {\n    return JSON.stringify(value, stableObjectReplacer);\n}, {\n    reset: function () {\n        // Clearing the sortingMap will reclaim all cached memory, without\n        // affecting the logical results of canonicalStringify, but potentially\n        // sacrificing performance until the cache is refilled.\n        sortingMap = new AutoCleanedStrongCache(cacheSizes.canonicalStringify || 1000 /* defaultCacheSizes.canonicalStringify */);\n    },\n});\nif (globalThis.__DEV__ !== false) {\n    registerGlobalCache(\"canonicalStringify\", function () { return sortingMap.size; });\n}\n// Values are JSON-serialized arrays of object keys (in any order), and values\n// are sorted arrays of the same keys.\nvar sortingMap;\ncanonicalStringify.reset();\n// The JSON.stringify function takes an optional second argument called a\n// replacer function. This function is called for each key-value pair in the\n// object being stringified, and its return value is used instead of the\n// original value. If the replacer function returns a new value, that value is\n// stringified as JSON instead of the original value of the property.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#the_replacer_parameter\nfunction stableObjectReplacer(key, value) {\n    if (value && typeof value === \"object\") {\n        var proto = Object.getPrototypeOf(value);\n        // We don't want to mess with objects that are not \"plain\" objects, which\n        // means their prototype is either Object.prototype or null. This check also\n        // prevents needlessly rearranging the indices of arrays.\n        if (proto === Object.prototype || proto === null) {\n            var keys = Object.keys(value);\n            // If keys is already sorted, let JSON.stringify serialize the original\n            // value instead of creating a new object with keys in the same order.\n            if (keys.every(everyKeyInOrder))\n                return value;\n            var unsortedKey = JSON.stringify(keys);\n            var sortedKeys = sortingMap.get(unsortedKey);\n            if (!sortedKeys) {\n                keys.sort();\n                var sortedKey = JSON.stringify(keys);\n                // Checking for sortedKey in the sortingMap allows us to share the same\n                // sorted array reference for all permutations of the same set of keys.\n                sortedKeys = sortingMap.get(sortedKey) || keys;\n                sortingMap.set(unsortedKey, sortedKeys);\n                sortingMap.set(sortedKey, sortedKeys);\n            }\n            var sortedObject_1 = Object.create(proto);\n            // Reassigning the keys in sorted order will cause JSON.stringify to\n            // serialize them in sorted order.\n            sortedKeys.forEach(function (key) {\n                sortedObject_1[key] = value[key];\n            });\n            return sortedObject_1;\n        }\n    }\n    return value;\n}\n// Since everything that happens in stableObjectReplacer benefits from being as\n// efficient as possible, we use a static function as the callback for\n// keys.every in order to test if the provided keys are already sorted without\n// allocating extra memory for a callback.\nfunction everyKeyInOrder(key, i, keys) {\n    return i === 0 || keys[i - 1] <= key;\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,UAAU,QAAS,kCAAkC;AACtF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAC,SAASF,kBAAkBA,CAACG,KAAK,EAAE;EAC7E,OAAOC,IAAI,CAACC,SAAS,CAACF,KAAK,EAAEG,oBAAoB,CAAC;AACtD,CAAC,EAAE;EACCC,KAAK,EAAE,SAAAA,CAAA,EAAY;IACf;IACA;IACA;IACAC,UAAU,GAAG,IAAIX,sBAAsB,CAACC,UAAU,CAACE,kBAAkB,IAAI,IAAI,CAAC,0CAA0C,CAAC;EAC7H;AACJ,CAAC,CAAC;;AACF,IAAIS,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;EAC9BX,mBAAmB,CAAC,oBAAoB,EAAE,YAAY;IAAE,OAAOS,UAAU,CAACG,IAAI;EAAE,CAAC,CAAC;AACtF;AACA;AACA;AACA,IAAIH,UAAU;AACdR,kBAAkB,CAACO,KAAK,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,oBAAoBA,CAACM,GAAG,EAAET,KAAK,EAAE;EACtC,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpC,IAAIU,KAAK,GAAGZ,MAAM,CAACa,cAAc,CAACX,KAAK,CAAC;IACxC;IACA;IACA;IACA,IAAIU,KAAK,KAAKZ,MAAM,CAACc,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAIG,IAAI,GAAGf,MAAM,CAACe,IAAI,CAACb,KAAK,CAAC;MAC7B;MACA;MACA,IAAIa,IAAI,CAACC,KAAK,CAACC,eAAe,CAAC,EAC3B,OAAOf,KAAK;MAChB,IAAIgB,WAAW,GAAGf,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC;MACtC,IAAII,UAAU,GAAGZ,UAAU,CAACa,GAAG,CAACF,WAAW,CAAC;MAC5C,IAAI,CAACC,UAAU,EAAE;QACbJ,IAAI,CAACM,IAAI,CAAC,CAAC;QACX,IAAIC,SAAS,GAAGnB,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC;QACpC;QACA;QACAI,UAAU,GAAGZ,UAAU,CAACa,GAAG,CAACE,SAAS,CAAC,IAAIP,IAAI;QAC9CR,UAAU,CAACgB,GAAG,CAACL,WAAW,EAAEC,UAAU,CAAC;QACvCZ,UAAU,CAACgB,GAAG,CAACD,SAAS,EAAEH,UAAU,CAAC;MACzC;MACA,IAAIK,cAAc,GAAGxB,MAAM,CAACyB,MAAM,CAACb,KAAK,CAAC;MACzC;MACA;MACAO,UAAU,CAACO,OAAO,CAAC,UAAUf,GAAG,EAAE;QAC9Ba,cAAc,CAACb,GAAG,CAAC,GAAGT,KAAK,CAACS,GAAG,CAAC;MACpC,CAAC,CAAC;MACF,OAAOa,cAAc;IACzB;EACJ;EACA,OAAOtB,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASe,eAAeA,CAACN,GAAG,EAAEgB,CAAC,EAAEZ,IAAI,EAAE;EACnC,OAAOY,CAAC,KAAK,CAAC,IAAIZ,IAAI,CAACY,CAAC,GAAG,CAAC,CAAC,IAAIhB,GAAG;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}