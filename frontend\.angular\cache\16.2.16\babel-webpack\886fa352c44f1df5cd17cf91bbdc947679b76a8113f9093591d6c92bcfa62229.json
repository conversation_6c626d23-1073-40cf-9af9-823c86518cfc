{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument names\n *\n * A GraphQL field or directive is only valid if all supplied arguments are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n */\nexport function UniqueArgumentNamesRule(context) {\n  return {\n    Field: checkArgUniqueness,\n    Directive: checkArgUniqueness\n  };\n  function checkArgUniqueness(parentNode) {\n    var _parentNode$arguments;\n\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const argumentNodes = (_parentNode$arguments = parentNode.arguments) !== null && _parentNode$arguments !== void 0 ? _parentNode$arguments : [];\n    const seenArgs = groupBy(argumentNodes, arg => arg.name.value);\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(new GraphQLError(`There can be only one argument named \"${argName}\".`, {\n          nodes: argNodes.map(node => node.name)\n        }));\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["groupBy", "GraphQLError", "UniqueArgumentNamesRule", "context", "Field", "checkArgUniqueness", "Directive", "parentNode", "_parentNode$arguments", "argumentNodes", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "arg", "name", "value", "argName", "argNodes", "length", "reportError", "nodes", "map", "node"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/UniqueArgumentNamesRule.mjs"], "sourcesContent": ["import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument names\n *\n * A GraphQL field or directive is only valid if all supplied arguments are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n */\nexport function UniqueArgumentNamesRule(context) {\n  return {\n    Field: checkArgUniqueness,\n    Directive: checkArgUniqueness,\n  };\n\n  function checkArgUniqueness(parentNode) {\n    var _parentNode$arguments;\n\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const argumentNodes =\n      (_parentNode$arguments = parentNode.arguments) !== null &&\n      _parentNode$arguments !== void 0\n        ? _parentNode$arguments\n        : [];\n    const seenArgs = groupBy(argumentNodes, (arg) => arg.name.value);\n\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(\n          new GraphQLError(\n            `There can be only one argument named \"${argName}\".`,\n            {\n              nodes: argNodes.map((node) => node.name),\n            },\n          ),\n        );\n      }\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,OAAO,EAAE;EAC/C,OAAO;IACLC,KAAK,EAAEC,kBAAkB;IACzBC,SAAS,EAAED;EACb,CAAC;EAED,SAASA,kBAAkBA,CAACE,UAAU,EAAE;IACtC,IAAIC,qBAAqB;;IAEzB;;IAEA;IACA,MAAMC,aAAa,GACjB,CAACD,qBAAqB,GAAGD,UAAU,CAACG,SAAS,MAAM,IAAI,IACvDF,qBAAqB,KAAK,KAAK,CAAC,GAC5BA,qBAAqB,GACrB,EAAE;IACR,MAAMG,QAAQ,GAAGX,OAAO,CAACS,aAAa,EAAGG,GAAG,IAAKA,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC;IAEhE,KAAK,MAAM,CAACC,OAAO,EAAEC,QAAQ,CAAC,IAAIL,QAAQ,EAAE;MAC1C,IAAIK,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACvBd,OAAO,CAACe,WAAW,CACjB,IAAIjB,YAAY,CACb,yCAAwCc,OAAQ,IAAG,EACpD;UACEI,KAAK,EAAEH,QAAQ,CAACI,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACR,IAAI;QACzC,CACF,CACF,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}