{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused variables\n *\n * A GraphQL operation is only valid if all variables defined by an operation\n * are used, either directly or within a spread fragment.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variables-Used\n */\nexport function NoUnusedVariablesRule(context) {\n  let variableDefs = [];\n  return {\n    OperationDefinition: {\n      enter() {\n        variableDefs = [];\n      },\n      leave(operation) {\n        const variableNameUsed = Object.create(null);\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node\n        } of usages) {\n          variableNameUsed[node.name.value] = true;\n        }\n        for (const variableDef of variableDefs) {\n          const variableName = variableDef.variable.name.value;\n          if (variableNameUsed[variableName] !== true) {\n            context.reportError(new GraphQLError(operation.name ? `Variable \"$${variableName}\" is never used in operation \"${operation.name.value}\".` : `Variable \"$${variableName}\" is never used.`, {\n              nodes: variableDef\n            }));\n          }\n        }\n      }\n    },\n    VariableDefinition(def) {\n      variableDefs.push(def);\n    }\n  };\n}", "map": {"version": 3, "names": ["GraphQLError", "NoUnusedVariablesRule", "context", "variableDefs", "OperationDefinition", "enter", "leave", "operation", "variableNameUsed", "Object", "create", "usages", "getRecursiveVariableUsages", "node", "name", "value", "variableDef", "variableName", "variable", "reportError", "nodes", "VariableDefinition", "def", "push"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/NoUnusedVariablesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused variables\n *\n * A GraphQL operation is only valid if all variables defined by an operation\n * are used, either directly or within a spread fragment.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variables-Used\n */\nexport function NoUnusedVariablesRule(context) {\n  let variableDefs = [];\n  return {\n    OperationDefinition: {\n      enter() {\n        variableDefs = [];\n      },\n\n      leave(operation) {\n        const variableNameUsed = Object.create(null);\n        const usages = context.getRecursiveVariableUsages(operation);\n\n        for (const { node } of usages) {\n          variableNameUsed[node.name.value] = true;\n        }\n\n        for (const variableDef of variableDefs) {\n          const variableName = variableDef.variable.name.value;\n\n          if (variableNameUsed[variableName] !== true) {\n            context.reportError(\n              new GraphQLError(\n                operation.name\n                  ? `Variable \"$${variableName}\" is never used in operation \"${operation.name.value}\".`\n                  : `Variable \"$${variableName}\" is never used.`,\n                {\n                  nodes: variableDef,\n                },\n              ),\n            );\n          }\n        }\n      },\n    },\n\n    VariableDefinition(def) {\n      variableDefs.push(def);\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,IAAIC,YAAY,GAAG,EAAE;EACrB,OAAO;IACLC,mBAAmB,EAAE;MACnBC,KAAKA,CAAA,EAAG;QACNF,YAAY,GAAG,EAAE;MACnB,CAAC;MAEDG,KAAKA,CAACC,SAAS,EAAE;QACf,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QAC5C,MAAMC,MAAM,GAAGT,OAAO,CAACU,0BAA0B,CAACL,SAAS,CAAC;QAE5D,KAAK,MAAM;UAAEM;QAAK,CAAC,IAAIF,MAAM,EAAE;UAC7BH,gBAAgB,CAACK,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;QAC1C;QAEA,KAAK,MAAMC,WAAW,IAAIb,YAAY,EAAE;UACtC,MAAMc,YAAY,GAAGD,WAAW,CAACE,QAAQ,CAACJ,IAAI,CAACC,KAAK;UAEpD,IAAIP,gBAAgB,CAACS,YAAY,CAAC,KAAK,IAAI,EAAE;YAC3Cf,OAAO,CAACiB,WAAW,CACjB,IAAInB,YAAY,CACdO,SAAS,CAACO,IAAI,GACT,cAAaG,YAAa,iCAAgCV,SAAS,CAACO,IAAI,CAACC,KAAM,IAAG,GAClF,cAAaE,YAAa,kBAAiB,EAChD;cACEG,KAAK,EAAEJ;YACT,CACF,CACF,CAAC;UACH;QACF;MACF;IACF,CAAC;IAEDK,kBAAkBA,CAACC,GAAG,EAAE;MACtBnB,YAAY,CAACoB,IAAI,CAACD,GAAG,CAAC;IACxB;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}