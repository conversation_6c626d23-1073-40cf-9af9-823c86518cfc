{"ast": null, "code": "/**\n *\n * @param {Error} error\n */\nfunction parseErrorToStacks(error) {\n  if (!error || !(error instanceof Error)) {\n    throw new Error(\"parseErrorToStacks expects Error object\");\n  }\n  if (typeof error.stack === \"string\") {\n    return error.stack.split(\"\\n\").filter(function (stack) {\n      return stack !== \"Error: \".concat(error.message);\n    });\n  }\n}\n\n/**\n * @callback ErrorCallback\n * @param {ErrorEvent} error\n * @returns {void}\n */\n\n/**\n * @param {ErrorCallback} callback\n */\nfunction listenToRuntimeError(callback) {\n  window.addEventListener(\"error\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"error\", callback);\n  };\n}\n\n/**\n * @callback UnhandledRejectionCallback\n * @param {PromiseRejectionEvent} rejectionEvent\n * @returns {void}\n */\n\n/**\n * @param {UnhandledRejectionCallback} callback\n */\nfunction listenToUnhandledRejection(callback) {\n  window.addEventListener(\"unhandledrejection\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"unhandledrejection\", callback);\n  };\n}\nexport { listenToRuntimeError, listenToUnhandledRejection, parseErrorToStacks };", "map": {"version": 3, "names": ["parseErrorToStacks", "error", "Error", "stack", "split", "filter", "concat", "message", "listenToRuntimeError", "callback", "window", "addEventListener", "cleanup", "removeEventListener", "listenToUnhandledRejection"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/webpack-dev-server/client/overlay/runtime-error.js"], "sourcesContent": ["/**\n *\n * @param {Error} error\n */\nfunction parseErrorToStacks(error) {\n  if (!error || !(error instanceof Error)) {\n    throw new Error(\"parseErrorToStacks expects Error object\");\n  }\n  if (typeof error.stack === \"string\") {\n    return error.stack.split(\"\\n\").filter(function (stack) {\n      return stack !== \"Error: \".concat(error.message);\n    });\n  }\n}\n\n/**\n * @callback ErrorCallback\n * @param {ErrorEvent} error\n * @returns {void}\n */\n\n/**\n * @param {ErrorCallback} callback\n */\nfunction listenToRuntimeError(callback) {\n  window.addEventListener(\"error\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"error\", callback);\n  };\n}\n\n/**\n * @callback UnhandledRejectionCallback\n * @param {PromiseRejectionEvent} rejectionEvent\n * @returns {void}\n */\n\n/**\n * @param {UnhandledRejectionCallback} callback\n */\nfunction listenToUnhandledRejection(callback) {\n  window.addEventListener(\"unhandledrejection\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"unhandledrejection\", callback);\n  };\n}\nexport { listenToRuntimeError, listenToUnhandledRejection, parseErrorToStacks };"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAI,CAACA,KAAK,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE;IACvC,MAAM,IAAIA,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,IAAI,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,EAAE;IACnC,OAAOF,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAUF,KAAK,EAAE;MACrD,OAAOA,KAAK,KAAK,SAAS,CAACG,MAAM,CAACL,KAAK,CAACM,OAAO,CAAC;IAClD,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;EACtCC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,QAAQ,CAAC;EAC1C,OAAO,SAASG,OAAOA,CAAA,EAAG;IACxBF,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEJ,QAAQ,CAAC;EAC/C,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASK,0BAA0BA,CAACL,QAAQ,EAAE;EAC5CC,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAEF,QAAQ,CAAC;EACvD,OAAO,SAASG,OAAOA,CAAA,EAAG;IACxBF,MAAM,CAACG,mBAAmB,CAAC,oBAAoB,EAAEJ,QAAQ,CAAC;EAC5D,CAAC;AACH;AACA,SAASD,oBAAoB,EAAEM,0BAA0B,EAAEd,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}