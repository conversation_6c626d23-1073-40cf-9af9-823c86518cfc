{"ast": null, "code": "/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact() {\n  var objects = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    objects[_i] = arguments[_i];\n  }\n  var result = Object.create(null);\n  objects.forEach(function (obj) {\n    if (!obj) return;\n    Object.keys(obj).forEach(function (key) {\n      var value = obj[key];\n      if (value !== void 0) {\n        result[key] = value;\n      }\n    });\n  });\n  return result;\n}", "map": {"version": 3, "names": ["compact", "objects", "_i", "arguments", "length", "result", "Object", "create", "for<PERSON>ach", "obj", "keys", "key", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/compact.js"], "sourcesContent": ["/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact() {\n    var objects = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        objects[_i] = arguments[_i];\n    }\n    var result = Object.create(null);\n    objects.forEach(function (obj) {\n        if (!obj)\n            return;\n        Object.keys(obj).forEach(function (key) {\n            var value = obj[key];\n            if (value !== void 0) {\n                result[key] = value;\n            }\n        });\n    });\n    return result;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAAA,EAAG;EACtB,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC/B;EACA,IAAIG,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAChCN,OAAO,CAACO,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3B,IAAI,CAACA,GAAG,EACJ;IACJH,MAAM,CAACI,IAAI,CAACD,GAAG,CAAC,CAACD,OAAO,CAAC,UAAUG,GAAG,EAAE;MACpC,IAAIC,KAAK,GAAGH,GAAG,CAACE,GAAG,CAAC;MACpB,IAAIC,KAAK,KAAK,KAAK,CAAC,EAAE;QAClBP,MAAM,CAACM,GAAG,CAAC,GAAGC,KAAK;MACvB;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOP,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}