{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IncomingCallModule } from '../incoming-call/incoming-call.module';\nimport { ActiveCallModule } from '../active-call/active-call.module';\nimport * as i0 from \"@angular/core\";\nexport class CallModule {\n  static {\n    this.ɵfac = function CallModule_Factory(t) {\n      return new (t || CallModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CallModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, IncomingCallModule, ActiveCallModule, IncomingCallModule, ActiveCallModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CallModule, {\n    imports: [CommonModule, IncomingCallModule, ActiveCallModule],\n    exports: [IncomingCallModule, ActiveCallModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IncomingCallModule", "ActiveCallModule", "CallModule", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\call\\call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IncomingCallModule } from '../incoming-call/incoming-call.module';\r\nimport { ActiveCallModule } from '../active-call/active-call.module';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    IncomingCallModule,\r\n    ActiveCallModule\r\n  ],\r\n  exports: [\r\n    IncomingCallModule,\r\n    ActiveCallModule\r\n  ]\r\n})\r\nexport class CallModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,gBAAgB,QAAQ,mCAAmC;;AAapE,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBATnBH,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAGhBD,kBAAkB,EAClBC,gBAAgB;IAAA;EAAA;;;2EAGPC,UAAU;IAAAC,OAAA,GATnBJ,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB;IAAAG,OAAA,GAGhBJ,kBAAkB,EAClBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}