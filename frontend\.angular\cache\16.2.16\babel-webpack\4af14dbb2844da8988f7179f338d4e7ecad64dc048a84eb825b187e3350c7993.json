{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { visit } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes, getFragmentDefinitions } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\n// As long as createFragmentRegistry is not imported or used, the\n// FragmentRegistry example implementation provided below should not be bundled\n// (by tree-shaking bundlers like Rollup), because the implementation of\n// InMemoryCache refers only to the TypeScript interface FragmentRegistryAPI,\n// never the concrete implementation FragmentRegistry (which is deliberately not\n// exported from this module).\nexport function createFragmentRegistry() {\n  var fragments = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    fragments[_i] = arguments[_i];\n  }\n  return new (FragmentRegistry.bind.apply(FragmentRegistry, __spreadArray([void 0], fragments, false)))();\n}\nvar FragmentRegistry = /** @class */function () {\n  // Call `createFragmentRegistry` instead of invoking the\n  // FragmentRegistry constructor directly. This reserves the constructor for\n  // future configuration of the FragmentRegistry.\n  function FragmentRegistry() {\n    var fragments = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      fragments[_i] = arguments[_i];\n    }\n    this.registry = Object.create(null);\n    this.resetCaches();\n    if (fragments.length) {\n      this.register.apply(this, fragments);\n    }\n  }\n  FragmentRegistry.prototype.register = function () {\n    var _this = this;\n    var fragments = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      fragments[_i] = arguments[_i];\n    }\n    var definitions = new Map();\n    fragments.forEach(function (doc) {\n      getFragmentDefinitions(doc).forEach(function (node) {\n        definitions.set(node.name.value, node);\n      });\n    });\n    definitions.forEach(function (node, name) {\n      if (node !== _this.registry[name]) {\n        _this.registry[name] = node;\n        _this.invalidate(name);\n      }\n    });\n    return this;\n  };\n  // Overridden in the resetCaches method below.\n  FragmentRegistry.prototype.invalidate = function (name) {};\n  FragmentRegistry.prototype.resetCaches = function () {\n    var proto = FragmentRegistry.prototype;\n    this.invalidate = (this.lookup = wrap(proto.lookup.bind(this), {\n      makeCacheKey: function (arg) {\n        return arg;\n      },\n      max: cacheSizes[\"fragmentRegistry.lookup\"] || 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */\n    })).dirty; // This dirty function is bound to the wrapped lookup method.\n    this.transform = wrap(proto.transform.bind(this), {\n      cache: WeakCache,\n      max: cacheSizes[\"fragmentRegistry.transform\"] || 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */\n    });\n\n    this.findFragmentSpreads = wrap(proto.findFragmentSpreads.bind(this), {\n      cache: WeakCache,\n      max: cacheSizes[\"fragmentRegistry.findFragmentSpreads\"] || 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */\n    });\n  };\n  /*\n   * Note:\n   * This method is only memoized so it can serve as a dependency to `tranform`,\n   * so calling `invalidate` will invalidate cache entries for `transform`.\n   */\n  FragmentRegistry.prototype.lookup = function (fragmentName) {\n    return this.registry[fragmentName] || null;\n  };\n  FragmentRegistry.prototype.transform = function (document) {\n    var _this = this;\n    var defined = new Map();\n    getFragmentDefinitions(document).forEach(function (def) {\n      defined.set(def.name.value, def);\n    });\n    var unbound = new Set();\n    var enqueue = function (spreadName) {\n      if (!defined.has(spreadName)) {\n        unbound.add(spreadName);\n      }\n    };\n    var enqueueChildSpreads = function (node) {\n      return Object.keys(_this.findFragmentSpreads(node)).forEach(enqueue);\n    };\n    enqueueChildSpreads(document);\n    var missing = [];\n    var map = Object.create(null);\n    // This Set forEach loop can be extended during iteration by adding\n    // additional strings to the unbound set.\n    unbound.forEach(function (fragmentName) {\n      var knownFragmentDef = defined.get(fragmentName);\n      if (knownFragmentDef) {\n        enqueueChildSpreads(map[fragmentName] = knownFragmentDef);\n      } else {\n        missing.push(fragmentName);\n        var def = _this.lookup(fragmentName);\n        if (def) {\n          enqueueChildSpreads(map[fragmentName] = def);\n        }\n      }\n    });\n    if (missing.length) {\n      var defsToAppend_1 = [];\n      missing.forEach(function (name) {\n        var def = map[name];\n        if (def) {\n          defsToAppend_1.push(def);\n        }\n      });\n      if (defsToAppend_1.length) {\n        document = __assign(__assign({}, document), {\n          definitions: document.definitions.concat(defsToAppend_1)\n        });\n      }\n    }\n    return document;\n  };\n  FragmentRegistry.prototype.findFragmentSpreads = function (root) {\n    var spreads = Object.create(null);\n    visit(root, {\n      FragmentSpread: function (node) {\n        spreads[node.name.value] = node;\n      }\n    });\n    return spreads;\n  };\n  return FragmentRegistry;\n}();", "map": {"version": 3, "names": ["__assign", "__spread<PERSON><PERSON>y", "visit", "wrap", "cacheSizes", "getFragmentDefinitions", "<PERSON>ak<PERSON><PERSON>", "createFragmentRegistry", "fragments", "_i", "arguments", "length", "FragmentRegistry", "bind", "apply", "registry", "Object", "create", "resetCaches", "register", "prototype", "_this", "definitions", "Map", "for<PERSON>ach", "doc", "node", "set", "name", "value", "invalidate", "proto", "lookup", "make<PERSON><PERSON><PERSON><PERSON>", "arg", "max", "dirty", "transform", "cache", "findFragmentSpreads", "fragmentName", "document", "defined", "def", "unbound", "Set", "enqueue", "spreadName", "has", "add", "enqueueChildSpreads", "keys", "missing", "map", "knownFragmentDef", "get", "push", "defsToAppend_1", "concat", "root", "spreads", "FragmentSpread"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/fragmentRegistry.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { visit } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes, getFragmentDefinitions, } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\n// As long as createFragmentRegistry is not imported or used, the\n// FragmentRegistry example implementation provided below should not be bundled\n// (by tree-shaking bundlers like Rollup), because the implementation of\n// InMemoryCache refers only to the TypeScript interface FragmentRegistryAPI,\n// never the concrete implementation FragmentRegistry (which is deliberately not\n// exported from this module).\nexport function createFragmentRegistry() {\n    var fragments = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        fragments[_i] = arguments[_i];\n    }\n    return new (FragmentRegistry.bind.apply(FragmentRegistry, __spreadArray([void 0], fragments, false)))();\n}\nvar FragmentRegistry = /** @class */ (function () {\n    // Call `createFragmentRegistry` instead of invoking the\n    // FragmentRegistry constructor directly. This reserves the constructor for\n    // future configuration of the FragmentRegistry.\n    function FragmentRegistry() {\n        var fragments = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            fragments[_i] = arguments[_i];\n        }\n        this.registry = Object.create(null);\n        this.resetCaches();\n        if (fragments.length) {\n            this.register.apply(this, fragments);\n        }\n    }\n    FragmentRegistry.prototype.register = function () {\n        var _this = this;\n        var fragments = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            fragments[_i] = arguments[_i];\n        }\n        var definitions = new Map();\n        fragments.forEach(function (doc) {\n            getFragmentDefinitions(doc).forEach(function (node) {\n                definitions.set(node.name.value, node);\n            });\n        });\n        definitions.forEach(function (node, name) {\n            if (node !== _this.registry[name]) {\n                _this.registry[name] = node;\n                _this.invalidate(name);\n            }\n        });\n        return this;\n    };\n    // Overridden in the resetCaches method below.\n    FragmentRegistry.prototype.invalidate = function (name) { };\n    FragmentRegistry.prototype.resetCaches = function () {\n        var proto = FragmentRegistry.prototype;\n        this.invalidate = (this.lookup = wrap(proto.lookup.bind(this), {\n            makeCacheKey: function (arg) { return arg; },\n            max: cacheSizes[\"fragmentRegistry.lookup\"] ||\n                1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        })).dirty; // This dirty function is bound to the wrapped lookup method.\n        this.transform = wrap(proto.transform.bind(this), {\n            cache: WeakCache,\n            max: cacheSizes[\"fragmentRegistry.transform\"] ||\n                2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        });\n        this.findFragmentSpreads = wrap(proto.findFragmentSpreads.bind(this), {\n            cache: WeakCache,\n            max: cacheSizes[\"fragmentRegistry.findFragmentSpreads\"] ||\n                4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        });\n    };\n    /*\n     * Note:\n     * This method is only memoized so it can serve as a dependency to `tranform`,\n     * so calling `invalidate` will invalidate cache entries for `transform`.\n     */\n    FragmentRegistry.prototype.lookup = function (fragmentName) {\n        return this.registry[fragmentName] || null;\n    };\n    FragmentRegistry.prototype.transform = function (document) {\n        var _this = this;\n        var defined = new Map();\n        getFragmentDefinitions(document).forEach(function (def) {\n            defined.set(def.name.value, def);\n        });\n        var unbound = new Set();\n        var enqueue = function (spreadName) {\n            if (!defined.has(spreadName)) {\n                unbound.add(spreadName);\n            }\n        };\n        var enqueueChildSpreads = function (node) {\n            return Object.keys(_this.findFragmentSpreads(node)).forEach(enqueue);\n        };\n        enqueueChildSpreads(document);\n        var missing = [];\n        var map = Object.create(null);\n        // This Set forEach loop can be extended during iteration by adding\n        // additional strings to the unbound set.\n        unbound.forEach(function (fragmentName) {\n            var knownFragmentDef = defined.get(fragmentName);\n            if (knownFragmentDef) {\n                enqueueChildSpreads((map[fragmentName] = knownFragmentDef));\n            }\n            else {\n                missing.push(fragmentName);\n                var def = _this.lookup(fragmentName);\n                if (def) {\n                    enqueueChildSpreads((map[fragmentName] = def));\n                }\n            }\n        });\n        if (missing.length) {\n            var defsToAppend_1 = [];\n            missing.forEach(function (name) {\n                var def = map[name];\n                if (def) {\n                    defsToAppend_1.push(def);\n                }\n            });\n            if (defsToAppend_1.length) {\n                document = __assign(__assign({}, document), { definitions: document.definitions.concat(defsToAppend_1) });\n            }\n        }\n        return document;\n    };\n    FragmentRegistry.prototype.findFragmentSpreads = function (root) {\n        var spreads = Object.create(null);\n        visit(root, {\n            FragmentSpread: function (node) {\n                spreads[node.name.value] = node;\n            },\n        });\n        return spreads;\n    };\n    return FragmentRegistry;\n}());\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AAC/C,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,UAAU,EAAEC,sBAAsB,QAAS,0BAA0B;AAC9E,SAASC,SAAS,QAAQ,aAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACrC,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,SAAS,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EACjC;EACA,OAAO,KAAKG,gBAAgB,CAACC,IAAI,CAACC,KAAK,CAACF,gBAAgB,EAAEX,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,EAAEO,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;AAC3G;AACA,IAAII,gBAAgB,GAAG,aAAe,YAAY;EAC9C;EACA;EACA;EACA,SAASA,gBAAgBA,CAAA,EAAG;IACxB,IAAIJ,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,SAAS,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IACjC;IACA,IAAI,CAACM,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIV,SAAS,CAACG,MAAM,EAAE;MAClB,IAAI,CAACQ,QAAQ,CAACL,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;IACxC;EACJ;EACAI,gBAAgB,CAACQ,SAAS,CAACD,QAAQ,GAAG,YAAY;IAC9C,IAAIE,KAAK,GAAG,IAAI;IAChB,IAAIb,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,SAAS,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IACjC;IACA,IAAIa,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3Bf,SAAS,CAACgB,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC7BpB,sBAAsB,CAACoB,GAAG,CAAC,CAACD,OAAO,CAAC,UAAUE,IAAI,EAAE;QAChDJ,WAAW,CAACK,GAAG,CAACD,IAAI,CAACE,IAAI,CAACC,KAAK,EAAEH,IAAI,CAAC;MAC1C,CAAC,CAAC;IACN,CAAC,CAAC;IACFJ,WAAW,CAACE,OAAO,CAAC,UAAUE,IAAI,EAAEE,IAAI,EAAE;MACtC,IAAIF,IAAI,KAAKL,KAAK,CAACN,QAAQ,CAACa,IAAI,CAAC,EAAE;QAC/BP,KAAK,CAACN,QAAQ,CAACa,IAAI,CAAC,GAAGF,IAAI;QAC3BL,KAAK,CAACS,UAAU,CAACF,IAAI,CAAC;MAC1B;IACJ,CAAC,CAAC;IACF,OAAO,IAAI;EACf,CAAC;EACD;EACAhB,gBAAgB,CAACQ,SAAS,CAACU,UAAU,GAAG,UAAUF,IAAI,EAAE,CAAE,CAAC;EAC3DhB,gBAAgB,CAACQ,SAAS,CAACF,WAAW,GAAG,YAAY;IACjD,IAAIa,KAAK,GAAGnB,gBAAgB,CAACQ,SAAS;IACtC,IAAI,CAACU,UAAU,GAAG,CAAC,IAAI,CAACE,MAAM,GAAG7B,IAAI,CAAC4B,KAAK,CAACC,MAAM,CAACnB,IAAI,CAAC,IAAI,CAAC,EAAE;MAC3DoB,YAAY,EAAE,SAAAA,CAAUC,GAAG,EAAE;QAAE,OAAOA,GAAG;MAAE,CAAC;MAC5CC,GAAG,EAAE/B,UAAU,CAAC,yBAAyB,CAAC,IACtC,IAAI,CAAC;IACb,CAAC,CAAC,EAAEgC,KAAK,CAAC,CAAC;IACX,IAAI,CAACC,SAAS,GAAGlC,IAAI,CAAC4B,KAAK,CAACM,SAAS,CAACxB,IAAI,CAAC,IAAI,CAAC,EAAE;MAC9CyB,KAAK,EAAEhC,SAAS;MAChB6B,GAAG,EAAE/B,UAAU,CAAC,4BAA4B,CAAC,IACzC,IAAI,CAAC;IACb,CAAC,CAAC;;IACF,IAAI,CAACmC,mBAAmB,GAAGpC,IAAI,CAAC4B,KAAK,CAACQ,mBAAmB,CAAC1B,IAAI,CAAC,IAAI,CAAC,EAAE;MAClEyB,KAAK,EAAEhC,SAAS;MAChB6B,GAAG,EAAE/B,UAAU,CAAC,sCAAsC,CAAC,IACnD,IAAI,CAAC;IACb,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIQ,gBAAgB,CAACQ,SAAS,CAACY,MAAM,GAAG,UAAUQ,YAAY,EAAE;IACxD,OAAO,IAAI,CAACzB,QAAQ,CAACyB,YAAY,CAAC,IAAI,IAAI;EAC9C,CAAC;EACD5B,gBAAgB,CAACQ,SAAS,CAACiB,SAAS,GAAG,UAAUI,QAAQ,EAAE;IACvD,IAAIpB,KAAK,GAAG,IAAI;IAChB,IAAIqB,OAAO,GAAG,IAAInB,GAAG,CAAC,CAAC;IACvBlB,sBAAsB,CAACoC,QAAQ,CAAC,CAACjB,OAAO,CAAC,UAAUmB,GAAG,EAAE;MACpDD,OAAO,CAACf,GAAG,CAACgB,GAAG,CAACf,IAAI,CAACC,KAAK,EAAEc,GAAG,CAAC;IACpC,CAAC,CAAC;IACF,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAIC,OAAO,GAAG,SAAAA,CAAUC,UAAU,EAAE;MAChC,IAAI,CAACL,OAAO,CAACM,GAAG,CAACD,UAAU,CAAC,EAAE;QAC1BH,OAAO,CAACK,GAAG,CAACF,UAAU,CAAC;MAC3B;IACJ,CAAC;IACD,IAAIG,mBAAmB,GAAG,SAAAA,CAAUxB,IAAI,EAAE;MACtC,OAAOV,MAAM,CAACmC,IAAI,CAAC9B,KAAK,CAACkB,mBAAmB,CAACb,IAAI,CAAC,CAAC,CAACF,OAAO,CAACsB,OAAO,CAAC;IACxE,CAAC;IACDI,mBAAmB,CAACT,QAAQ,CAAC;IAC7B,IAAIW,OAAO,GAAG,EAAE;IAChB,IAAIC,GAAG,GAAGrC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7B;IACA;IACA2B,OAAO,CAACpB,OAAO,CAAC,UAAUgB,YAAY,EAAE;MACpC,IAAIc,gBAAgB,GAAGZ,OAAO,CAACa,GAAG,CAACf,YAAY,CAAC;MAChD,IAAIc,gBAAgB,EAAE;QAClBJ,mBAAmB,CAAEG,GAAG,CAACb,YAAY,CAAC,GAAGc,gBAAiB,CAAC;MAC/D,CAAC,MACI;QACDF,OAAO,CAACI,IAAI,CAAChB,YAAY,CAAC;QAC1B,IAAIG,GAAG,GAAGtB,KAAK,CAACW,MAAM,CAACQ,YAAY,CAAC;QACpC,IAAIG,GAAG,EAAE;UACLO,mBAAmB,CAAEG,GAAG,CAACb,YAAY,CAAC,GAAGG,GAAI,CAAC;QAClD;MACJ;IACJ,CAAC,CAAC;IACF,IAAIS,OAAO,CAACzC,MAAM,EAAE;MAChB,IAAI8C,cAAc,GAAG,EAAE;MACvBL,OAAO,CAAC5B,OAAO,CAAC,UAAUI,IAAI,EAAE;QAC5B,IAAIe,GAAG,GAAGU,GAAG,CAACzB,IAAI,CAAC;QACnB,IAAIe,GAAG,EAAE;UACLc,cAAc,CAACD,IAAI,CAACb,GAAG,CAAC;QAC5B;MACJ,CAAC,CAAC;MACF,IAAIc,cAAc,CAAC9C,MAAM,EAAE;QACvB8B,QAAQ,GAAGzC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyC,QAAQ,CAAC,EAAE;UAAEnB,WAAW,EAAEmB,QAAQ,CAACnB,WAAW,CAACoC,MAAM,CAACD,cAAc;QAAE,CAAC,CAAC;MAC7G;IACJ;IACA,OAAOhB,QAAQ;EACnB,CAAC;EACD7B,gBAAgB,CAACQ,SAAS,CAACmB,mBAAmB,GAAG,UAAUoB,IAAI,EAAE;IAC7D,IAAIC,OAAO,GAAG5C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjCf,KAAK,CAACyD,IAAI,EAAE;MACRE,cAAc,EAAE,SAAAA,CAAUnC,IAAI,EAAE;QAC5BkC,OAAO,CAAClC,IAAI,CAACE,IAAI,CAACC,KAAK,CAAC,GAAGH,IAAI;MACnC;IACJ,CAAC,CAAC;IACF,OAAOkC,OAAO;EAClB,CAAC;EACD,OAAOhD,gBAAgB;AAC3B,CAAC,CAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}