{"ast": null, "code": "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n  if (space === void 0) {\n    space = 0;\n  }\n  var undefId = makeUniqueId(\"stringifyForDisplay\");\n  return JSON.stringify(value, function (key, value) {\n    return value === void 0 ? undefId : value;\n  }, space).split(JSON.stringify(undefId)).join(\"<undefined>\");\n}", "map": {"version": 3, "names": ["makeUniqueId", "stringifyForDisplay", "value", "space", "undefId", "JSON", "stringify", "key", "split", "join"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/stringifyForDisplay.js"], "sourcesContent": ["import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"stringifyForDisplay\");\n    return JSON.stringify(value, function (key, value) {\n        return value === void 0 ? undefId : value;\n    }, space)\n        .split(JSON.stringify(undefId))\n        .join(\"<undefined>\");\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC;EAAE;EACnC,IAAIC,OAAO,GAAGJ,YAAY,CAAC,qBAAqB,CAAC;EACjD,OAAOK,IAAI,CAACC,SAAS,CAACJ,KAAK,EAAE,UAAUK,GAAG,EAAEL,KAAK,EAAE;IAC/C,OAAOA,KAAK,KAAK,KAAK,CAAC,GAAGE,OAAO,GAAGF,KAAK;EAC7C,CAAC,EAAEC,KAAK,CAAC,CACJK,KAAK,CAACH,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC,CAAC,CAC9BK,IAAI,CAAC,aAAa,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}