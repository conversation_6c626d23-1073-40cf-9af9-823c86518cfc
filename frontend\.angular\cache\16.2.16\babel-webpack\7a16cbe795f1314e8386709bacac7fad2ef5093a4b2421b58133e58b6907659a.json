{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nfunction EquipeListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 26);\n    i0.ɵɵtext(5, \" Chargement des \\u00E9quipes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"h3\", 33);\n    i0.ɵɵtext(7, \" Erreur de chargement des \\u00E9quipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_32_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadEquipes());\n    });\n    i0.ɵɵelement(11, \"i\", 36);\n    i0.ɵɵtext(12, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 40);\n    i0.ɵɵtext(4, \" Aucune \\u00E9quipe trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 41);\n    i0.ɵɵtext(6, \" Commencez par cr\\u00E9er une nouvelle \\u00E9quipe pour organiser vos projets et membres \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_33_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToAddEquipe());\n    });\n    i0.ɵɵelement(8, \"i\", 43);\n    i0.ɵɵtext(9, \" Cr\\u00E9er une \\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeListComponent_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 9)(3, \"div\", 47);\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"div\", 49)(6, \"div\", 32)(7, \"h3\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 51)(10, \"span\", 52);\n    i0.ɵɵelement(11, \"i\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 54);\n    i0.ɵɵelement(14, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"p\", 56);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"slice\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"div\", 58)(20, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_20_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));\n    });\n    i0.ɵɵelement(21, \"i\", 60);\n    i0.ɵɵtext(22, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 61)(24, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(25, \"i\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_26_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r13.navigateToTasks(equipe_r9._id));\n    });\n    i0.ɵɵelement(27, \"i\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeListComponent_div_34_div_1_Template_button_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const equipe_r9 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(equipe_r9._id && ctx_r14.deleteEquipe(equipe_r9._id));\n    });\n    i0.ɵɵelement(29, \"i\", 67);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const equipe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, \" membre(s) \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", equipe_r9.description && equipe_r9.description.length > 80 ? i0.ɵɵpipeBind3(17, 3, equipe_r9.description, 0, 80) + \"...\" : equipe_r9.description || \"Aucune description disponible\", \" \");\n  }\n}\nfunction EquipeListComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, EquipeListComponent_div_34_div_1_Template, 30, 7, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.equipes);\n  }\n}\nexport class EquipeListComponent {\n  constructor(equipeService, router, notificationService) {\n    this.equipeService = equipeService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.equipes = [];\n    this.loading = false;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadEquipes();\n  }\n  loadEquipes() {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipes().pipe(finalize(() => this.loading = false)).subscribe({\n      next: data => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n  navigateToAddEquipe() {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n  navigateToEditEquipe(id) {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n  navigateToEquipeDetail(id) {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n  deleteEquipe(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n      this.equipeService.deleteEquipe(id).pipe(finalize(() => this.loading = false)).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n  navigateToTasks(id) {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/tasks', id]);\n  }\n  static {\n    this.ɵfac = function EquipeListComponent_Factory(t) {\n      return new (t || EquipeListComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeListComponent,\n      selectors: [[\"app-equipe-list\"]],\n      decls: 35,\n      vars: 4,\n      consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-2\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-300\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"mt-3\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"text-center\", \"py-16\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"opacity-70\"], [1, \"fas\", \"fa-users\", \"text-5xl\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"xl:grid-cols-3\", \"gap-6\"], [\"class\", \"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"hover:shadow-xl\", \"dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-2\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-6\"], [1, \"flex\", \"items-start\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"group-hover:scale-[1.02]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"text-xs\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#00f7ff]/10\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\"], [1, \"fas\", \"fa-users\", \"mr-1\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [1, \"fas\", \"fa-users\", \"text-white\", \"text-lg\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"line-clamp-2\", \"mb-4\"], [1, \"px-6\", \"pb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\", \"border-t\", \"border-[#4f5fad]/10\", \"dark:border-[#00f7ff]/10\"], [1, \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"hover:text-[#7826b5]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"transition-colors\", \"flex\", \"items-center\", \"group/details\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\", \"group-hover/details:scale-110\", \"transition-transform\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Modifier l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#00f7ff]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#00f7ff]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"title\", \"G\\u00E9rer les t\\u00E2ches\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#00ff9d]\", \"hover:bg-[#00ff9d]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-tasks\"], [\"title\", \"Supprimer l'\\u00E9quipe\", 1, \"p-2\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:text-[#ff6b69]\", \"dark:hover:text-[#ff3b30]\", \"hover:bg-[#ff6b69]/10\", \"dark:hover:bg-[#ff3b30]/10\", \"rounded-lg\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-trash\"]],\n      template: function EquipeListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n          i0.ɵɵtext(25, \" \\u00C9quipes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\", 15);\n          i0.ɵɵtext(27, \" G\\u00E9rez vos \\u00E9quipes et leurs membres avec style futuriste \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function EquipeListComponent_Template_button_click_28_listener() {\n            return ctx.navigateToAddEquipe();\n          });\n          i0.ɵɵelement(29, \"i\", 17);\n          i0.ɵɵtext(30, \" Nouvelle \\u00C9quipe \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(31, EquipeListComponent_div_31_Template, 6, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, EquipeListComponent_div_32_Template, 13, 1, \"div\", 19);\n          i0.ɵɵtemplate(33, EquipeListComponent_div_33_Template, 10, 0, \"div\", 20);\n          i0.ɵɵtemplate(34, EquipeListComponent_div_34_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.equipes.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.equipes.length > 0);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n      styles: [\".hover-shadow[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n  }\\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .card-header.bg-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkVBQUU7SUFDRSwyQkFBMkI7SUFDM0Isa0RBQWtEO0VBQ3BEOztFQUVBO0lBQ0UseUJBQXlCO0VBQzNCOztFQUVBO0lBQ0UsK0RBQStEO0VBQ2pFIiwiZmlsZSI6ImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5ob3Zlci1zaGFkb3c6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAudHJhbnNpdGlvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLmNhcmQtaGVhZGVyLmJnLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjNjYxMGYyKSAhaW1wb3J0YW50O1xyXG4gIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9lcXVpcGUtbGlzdC9lcXVpcGUtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJFQUFFO0lBQ0UsMkJBQTJCO0lBQzNCLGtEQUFrRDtFQUNwRDs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLCtEQUErRDtFQUNqRTtBQUNGLDR1QkFBNHVCIiwic291cmNlc0NvbnRlbnQiOlsiICAuaG92ZXItc2hhZG93OmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMTBweCAyMHB4IHJnYmEoMCwwLDAsMC4xKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLnRyYW5zaXRpb24ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWhlYWRlci5iZy1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwN2JmZiwgIzY2MTBmMikgIWltcG9ydGFudDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeListComponent_div_32_Template_button_click_10_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "loadEquipes", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "EquipeListComponent_div_33_Template_button_click_7_listener", "_r7", "ctx_r6", "navigateToAddEquipe", "EquipeListComponent_div_34_div_1_Template_button_click_20_listener", "restoredCtx", "_r11", "equipe_r9", "$implicit", "ctx_r10", "_id", "navigateToEquipeDetail", "EquipeListComponent_div_34_div_1_Template_button_click_24_listener", "ctx_r12", "navigateToEditEquipe", "EquipeListComponent_div_34_div_1_Template_button_click_26_listener", "ctx_r13", "navigateToTasks", "EquipeListComponent_div_34_div_1_Template_button_click_28_listener", "ctx_r14", "deleteEquipe", "name", "members", "length", "description", "ɵɵpipeBind3", "ɵɵtemplate", "EquipeListComponent_div_34_div_1_Template", "ɵɵproperty", "ctx_r3", "equipes", "EquipeListComponent", "constructor", "equipeService", "router", "notificationService", "loading", "ngOnInit", "getEquipes", "pipe", "subscribe", "next", "data", "console", "log", "sort", "a", "b", "localeCompare", "showError", "navigate", "id", "equipe", "find", "e", "equipeName", "confirm", "showSuccess", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "Router", "i3", "NotificationService", "selectors", "decls", "vars", "consts", "template", "EquipeListComponent_Template", "rf", "ctx", "EquipeListComponent_Template_button_click_28_listener", "EquipeListComponent_div_31_Template", "EquipeListComponent_div_32_Template", "EquipeListComponent_div_33_Template", "EquipeListComponent_div_34_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\equipe-list\\equipe-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { NotificationService } from 'src/app/services/notification.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { finalize } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-equipe-list',\r\n  templateUrl: './equipe-list.component.html',\r\n  styleUrls: ['./equipe-list.component.css']\r\n})\r\nexport class EquipeListComponent implements OnInit {\r\n  equipes: Equipe[] = [];\r\n  loading = false;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private router: Router,\r\n    private notificationService: NotificationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadEquipes();\r\n  }\r\n\r\n  loadEquipes(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.equipeService.getEquipes().pipe(\r\n      finalize(() => this.loading = false)\r\n    ).subscribe({\r\n      next: (data) => {\r\n        console.log('Équipes chargées:', data);\r\n        this.equipes = data;\r\n\r\n        // Trier les équipes par nom\r\n        this.equipes.sort((a, b) => {\r\n          if (a.name && b.name) {\r\n            return a.name.localeCompare(b.name);\r\n          }\r\n          return 0;\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des équipes:', error);\r\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\r\n        this.notificationService.showError('Erreur lors du chargement des équipes');\r\n      }\r\n    });\r\n  }\r\n\r\n  navigateToAddEquipe(): void {\r\n    this.router.navigate(['/equipes/ajouter']);\r\n  }\r\n\r\n  navigateToEditEquipe(id: string): void {\r\n    this.router.navigate(['/equipes/modifier', id]);\r\n  }\r\n\r\n  navigateToEquipeDetail(id: string): void {\r\n    this.router.navigate(['/equipes/detail', id]);\r\n  }\r\n\r\n  deleteEquipe(id: string): void {\r\n    if (!id) {\r\n      console.error('ID est indéfini');\r\n      this.notificationService.showError('ID d\\'équipe invalide');\r\n      return;\r\n    }\r\n\r\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\r\n    const equipe = this.equipes.find(e => e._id === id);\r\n    const equipeName = equipe?.name || 'cette équipe';\r\n\r\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\r\n      this.loading = true;\r\n\r\n      this.equipeService.deleteEquipe(id).pipe(\r\n        finalize(() => this.loading = false)\r\n      ).subscribe({\r\n        next: () => {\r\n          console.log('Équipe supprimée avec succès');\r\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\r\n          this.loadEquipes();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\r\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\r\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  navigateToTasks(id: string): void {\r\n    if (!id) {\r\n      console.error('ID est indéfini');\r\n      this.notificationService.showError('ID d\\'équipe invalide');\r\n      return;\r\n    }\r\n\r\n    const equipe = this.equipes.find(e => e._id === id);\r\n    const equipeName = equipe?.name || 'cette équipe';\r\n\r\n    // Naviguer vers la page des tâches de l'équipe\r\n    this.router.navigate(['/tasks', id]);    }\r\n}\r\n\r\n", "<div\r\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\r\n    <!-- Header futuriste -->\r\n    <div class=\"mb-8 relative\">\r\n      <!-- Decorative top border -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\r\n      >\r\n        <div\r\n          class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\r\n        >\r\n          <div class=\"mb-4 lg:mb-0\">\r\n            <h1\r\n              class=\"text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide\"\r\n            >\r\n              Équipes\r\n            </h1>\r\n            <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\r\n              Gérez vos équipes et leurs membres avec style futuriste\r\n            </p>\r\n          </div>\r\n\r\n          <button\r\n            (click)=\"navigateToAddEquipe()\"\r\n            class=\"relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\r\n          >\r\n            <i\r\n              class=\"fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300\"\r\n            ></i>\r\n            Nouvelle Équipe\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div\r\n      *ngIf=\"loading\"\r\n      class=\"flex flex-col items-center justify-center py-16\"\r\n    >\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-12 h-12 border-3 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"\r\n        ></div>\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n      <p\r\n        class=\"mt-4 text-[#4f5fad] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\r\n      >\r\n        Chargement des équipes...\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Error Alert -->\r\n    <div *ngIf=\"error\" class=\"mb-6\">\r\n      <div\r\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\r\n      >\r\n        <div class=\"flex items-start\">\r\n          <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\r\n            <i class=\"fas fa-exclamation-triangle\"></i>\r\n          </div>\r\n          <div class=\"flex-1\">\r\n            <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\r\n              Erreur de chargement des équipes\r\n            </h3>\r\n            <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\r\n              {{ error }}\r\n            </p>\r\n            <button\r\n              (click)=\"loadEquipes()\"\r\n              class=\"mt-3 bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30 transition-colors\"\r\n            >\r\n              <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No Teams -->\r\n    <div\r\n      *ngIf=\"!loading && !error && equipes.length === 0\"\r\n      class=\"text-center py-16\"\r\n    >\r\n      <div\r\n        class=\"w-20 h-20 mx-auto mb-6 text-[#4f5fad] dark:text-[#00f7ff] opacity-70\"\r\n      >\r\n        <i class=\"fas fa-users text-5xl\"></i>\r\n      </div>\r\n      <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\r\n        Aucune équipe trouvée\r\n      </h3>\r\n      <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm mb-6\">\r\n        Commencez par créer une nouvelle équipe pour organiser vos projets et\r\n        membres\r\n      </p>\r\n      <button\r\n        (click)=\"navigateToAddEquipe()\"\r\n        class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\r\n      >\r\n        <i class=\"fas fa-plus-circle mr-2\"></i>\r\n        Créer une équipe\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Teams Grid -->\r\n    <div\r\n      *ngIf=\"!loading && equipes.length > 0\"\r\n      class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\"\r\n    >\r\n      <div\r\n        *ngFor=\"let equipe of equipes\"\r\n        class=\"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\"\r\n      >\r\n        <!-- Header avec gradient -->\r\n        <div class=\"relative\">\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\r\n          ></div>\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <div class=\"p-6\">\r\n            <div class=\"flex items-start justify-between mb-4\">\r\n              <div class=\"flex-1\">\r\n                <h3\r\n                  class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 group-hover:scale-[1.02] transition-transform duration-300 origin-left\"\r\n                >\r\n                  {{ equipe.name }}\r\n                </h3>\r\n                <div class=\"flex items-center text-xs\">\r\n                  <span\r\n                    class=\"bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full font-medium\"\r\n                  >\r\n                    <i class=\"fas fa-users mr-1\"></i>\r\n                    {{ equipe.members?.length || 0 }} membre(s)\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Avatar de l'équipe -->\r\n              <div\r\n                class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg\"\r\n              >\r\n                <i class=\"fas fa-users text-white text-lg\"></i>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Description -->\r\n            <p\r\n              class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0] line-clamp-2 mb-4\"\r\n            >\r\n              {{\r\n                equipe.description && equipe.description.length > 80\r\n                  ? (equipe.description | slice : 0 : 80) + \"...\"\r\n                  : equipe.description || \"Aucune description disponible\"\r\n              }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"px-6 pb-6\">\r\n          <div\r\n            class=\"flex items-center justify-between pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10\"\r\n          >\r\n            <!-- Bouton Détails -->\r\n            <button\r\n              (click)=\"equipe._id && navigateToEquipeDetail(equipe._id)\"\r\n              class=\"text-[#4f5fad] dark:text-[#00f7ff] hover:text-[#7826b5] dark:hover:text-[#4f5fad] text-sm font-medium transition-colors flex items-center group/details\"\r\n            >\r\n              <i\r\n                class=\"fas fa-eye mr-1 group-hover/details:scale-110 transition-transform\"\r\n              ></i>\r\n              Détails\r\n            </button>\r\n\r\n            <!-- Actions rapides -->\r\n            <div class=\"flex items-center space-x-2\">\r\n              <button\r\n                (click)=\"equipe._id && navigateToEditEquipe(equipe._id)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\"\r\n                title=\"Modifier l'équipe\"\r\n              >\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"equipe._id && navigateToTasks(equipe._id)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all\"\r\n                title=\"Gérer les tâches\"\r\n              >\r\n                <i class=\"fas fa-tasks\"></i>\r\n              </button>\r\n\r\n              <button\r\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\r\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\r\n                title=\"Supprimer l'équipe\"\r\n              >\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAKA,SAASA,QAAQ,QAAQ,gBAAgB;;;;;;;;ICmErCC,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IAITF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAEC;IACCD,EAAA,CAAAI,MAAA,uCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAINH,EAAA,CAAAC,cAAA,cAAgC;IAMxBD,EAAA,CAAAE,SAAA,YAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,8CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBZ,EAAA,CAAAE,SAAA,aAAsC;IAACF,EAAA,CAAAI,MAAA,wBACzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAPPH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;IAaRhB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,SAAA,YAAqC;IACvCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAI,MAAA,gGAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAY,4DAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAQ,MAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAG/BpB,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAI,MAAA,mCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQTH,EAAA,CAAAC,cAAA,cAGC;IAGGD,EAAA,CAAAE,SAAA,aAEO;IAKPF,EAAA,CAAAC,cAAA,cAAiB;IAMTD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuC;IAInCD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAI,MAAA,IAKF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAKRH,EAAA,CAAAC,cAAA,eAAuB;IAMjBD,EAAA,CAAAK,UAAA,mBAAAgB,mEAAA;MAAA,MAAAC,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcD,OAAA,CAAAE,sBAAA,CAAAJ,SAAA,CAAAG,GAAA,CAAkC;IAAA,EAAC;IAG1D3B,EAAA,CAAAE,SAAA,aAEK;IACLF,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAK,UAAA,mBAAAwB,mEAAA;MAAA,MAAAP,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAK,OAAA,GAAA9B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcG,OAAA,CAAAC,oBAAA,CAAAP,SAAA,CAAAG,GAAA,CAAgC;IAAA,EAAC;IAIxD3B,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAA2B,mEAAA;MAAA,MAAAV,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAAjC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcM,OAAA,CAAAC,eAAA,CAAAV,SAAA,CAAAG,GAAA,CAA2B;IAAA,EAAC;IAInD3B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAK,UAAA,mBAAA8B,mEAAA;MAAA,MAAAb,WAAA,GAAAtB,EAAA,CAAAO,aAAA,CAAAgB,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAW,OAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,SAAA,CAAAG,GAAA,IAAcS,OAAA,CAAAC,YAAA,CAAAb,SAAA,CAAAG,GAAA,CAAwB;IAAA,EAAC;IAIhD3B,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;IAzELH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAU,SAAA,CAAAc,IAAA,MACF;IAMItC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,OAAAU,SAAA,CAAAe,OAAA,kBAAAf,SAAA,CAAAe,OAAA,CAAAC,MAAA,sBACF;IAgBJxC,EAAA,CAAAa,SAAA,GAKF;IALEb,EAAA,CAAAc,kBAAA,MAAAU,SAAA,CAAAiB,WAAA,IAAAjB,SAAA,CAAAiB,WAAA,CAAAD,MAAA,QAAAxC,EAAA,CAAA0C,WAAA,QAAAlB,SAAA,CAAAiB,WAAA,mBAAAjB,SAAA,CAAAiB,WAAA,yCAKF;;;;;IApDRzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA2C,UAAA,IAAAC,yCAAA,mBAgGM;IACR5C,EAAA,CAAAG,YAAA,EAAM;;;;IAhGiBH,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAA6C,UAAA,YAAAC,MAAA,CAAAC,OAAA,CAAU;;;AD1IrC,OAAM,MAAOC,mBAAmB;EAK9BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,mBAAwC;IAFxC,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAP7B,KAAAL,OAAO,GAAa,EAAE;IACtB,KAAAM,OAAO,GAAG,KAAK;IACf,KAAArC,KAAK,GAAkB,IAAI;EAMxB;EAEHsC,QAAQA,CAAA;IACN,IAAI,CAAC1C,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACyC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACrC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACkC,aAAa,CAACK,UAAU,EAAE,CAACC,IAAI,CAClCzD,QAAQ,CAAC,MAAM,IAAI,CAACsD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;MACVC,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;QACtC,IAAI,CAACZ,OAAO,GAAGY,IAAI;QAEnB;QACA,IAAI,CAACZ,OAAO,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UACzB,IAAID,CAAC,CAACzB,IAAI,IAAI0B,CAAC,CAAC1B,IAAI,EAAE;YACpB,OAAOyB,CAAC,CAACzB,IAAI,CAAC2B,aAAa,CAACD,CAAC,CAAC1B,IAAI,CAAC;;UAErC,OAAO,CAAC;QACV,CAAC,CAAC;MACJ,CAAC;MACDtB,KAAK,EAAGA,KAAK,IAAI;QACf4C,OAAO,CAAC5C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uCAAuC,CAAC;MAC7E;KACD,CAAC;EACJ;EAEA9C,mBAAmBA,CAAA;IACjB,IAAI,CAAC+B,MAAM,CAACgB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEApC,oBAAoBA,CAACqC,EAAU;IAC7B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,mBAAmB,EAAEC,EAAE,CAAC,CAAC;EACjD;EAEAxC,sBAAsBA,CAACwC,EAAU;IAC/B,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,EAAEC,EAAE,CAAC,CAAC;EAC/C;EAEA/B,YAAYA,CAAC+B,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC5C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF;IACA,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,GAAG,KAAKyC,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD,IAAImC,OAAO,CAAC,gDAAgDD,UAAU,KAAK,CAAC,EAAE;MAC5E,IAAI,CAACnB,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACH,aAAa,CAACb,YAAY,CAAC+B,EAAE,CAAC,CAACZ,IAAI,CACtCzD,QAAQ,CAAC,MAAM,IAAI,CAACsD,OAAO,GAAG,KAAK,CAAC,CACrC,CAACI,SAAS,CAAC;QACVC,IAAI,EAAEA,CAAA,KAAK;UACTE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACT,mBAAmB,CAACsB,WAAW,CAAC,aAAaF,UAAU,+BAA+B,CAAC;UAC5F,IAAI,CAAC5D,WAAW,EAAE;QACpB,CAAC;QACDI,KAAK,EAAGA,KAAK,IAAI;UACf4C,OAAO,CAAC5C,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GAAG,kEAAkE;UAC/E,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,8CAA8CM,UAAU,GAAG,CAAC;QACjG;OACD,CAAC;;EAEN;EAEAtC,eAAeA,CAACkC,EAAU;IACxB,IAAI,CAACA,EAAE,EAAE;MACPR,OAAO,CAAC5C,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACoC,mBAAmB,CAACc,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF,MAAMG,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,GAAG,KAAKyC,EAAE,CAAC;IACnD,MAAMI,UAAU,GAAGH,MAAM,EAAE/B,IAAI,IAAI,cAAc;IAEjD;IACA,IAAI,CAACa,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAAK;;;uBAhGhCpB,mBAAmB,EAAAhD,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7E,EAAA,CAAA2E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/E,EAAA,CAAA2E,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBjC,mBAAmB;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCxF,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAiD;UAI7CD,EAAA,CAAAE,SAAA,cAEO;UAKPF,EAAA,CAAAC,cAAA,eAEC;UAQOD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAsD;UACpDD,EAAA,CAAAI,MAAA,2EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAK,UAAA,mBAAAqF,sDAAA;YAAA,OAASD,GAAA,CAAArE,mBAAA,EAAqB;UAAA,EAAC;UAG/BpB,EAAA,CAAAE,SAAA,aAEK;UACLF,EAAA,CAAAI,MAAA,8BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAA2C,UAAA,KAAAgD,mCAAA,kBAiBM;UAGN3F,EAAA,CAAA2C,UAAA,KAAAiD,mCAAA,mBAwBM;UAGN5F,EAAA,CAAA2C,UAAA,KAAAkD,mCAAA,mBAuBM;UAGN7F,EAAA,CAAA2C,UAAA,KAAAmD,mCAAA,kBAqGM;UACR9F,EAAA,CAAAG,YAAA,EAAM;;;UA9KDH,EAAA,CAAAa,SAAA,IAAa;UAAbb,EAAA,CAAA6C,UAAA,SAAA4C,GAAA,CAAApC,OAAA,CAAa;UAmBVrD,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAA6C,UAAA,SAAA4C,GAAA,CAAAzE,KAAA,CAAW;UA4BdhB,EAAA,CAAAa,SAAA,GAAgD;UAAhDb,EAAA,CAAA6C,UAAA,UAAA4C,GAAA,CAAApC,OAAA,KAAAoC,GAAA,CAAAzE,KAAA,IAAAyE,GAAA,CAAA1C,OAAA,CAAAP,MAAA,OAAgD;UA0BhDxC,EAAA,CAAAa,SAAA,GAAoC;UAApCb,EAAA,CAAA6C,UAAA,UAAA4C,GAAA,CAAApC,OAAA,IAAAoC,GAAA,CAAA1C,OAAA,CAAAP,MAAA,KAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}