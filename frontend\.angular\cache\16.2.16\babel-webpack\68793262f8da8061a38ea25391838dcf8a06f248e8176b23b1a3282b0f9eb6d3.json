{"ast": null, "code": "import { __extends } from \"tslib\";\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf,\n  setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n  } : _a;\nvar InvariantError = /** @class */function (_super) {\n  __extends(InvariantError, _super);\n  function InvariantError(message) {\n    if (message === void 0) {\n      message = genericMessage;\n    }\n    var _this = _super.call(this, typeof message === \"number\" ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\" : message) || this;\n    _this.framesToPop = 1;\n    _this.name = genericMessage;\n    setPrototypeOf(_this, InvariantError.prototype);\n    return _this;\n  }\n  return InvariantError;\n}(Error);\nexport { InvariantError };\nexport function invariant(condition, message) {\n  if (!condition) {\n    throw new InvariantError(message);\n  }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n  return function () {\n    if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n      // Default to console.log if this host environment happens not to provide\n      // all the console.* methods we need.\n      var method = console[name] || console.log;\n      return method.apply(console, arguments);\n    }\n  };\n}\n(function (invariant) {\n  invariant.debug = wrapConsoleMethod(\"debug\");\n  invariant.log = wrapConsoleMethod(\"log\");\n  invariant.warn = wrapConsoleMethod(\"warn\");\n  invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nexport function setVerbosity(level) {\n  var old = verbosityLevels[verbosityLevel];\n  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n  return old;\n}\nexport default invariant;", "map": {"version": 3, "names": ["__extends", "genericMessage", "_a", "Object", "setPrototypeOf", "obj", "proto", "__proto__", "InvariantError", "_super", "message", "_this", "call", "framesToPop", "name", "prototype", "Error", "invariant", "condition", "verbosityLevels", "verbosityLevel", "indexOf", "wrapConsoleMethod", "method", "console", "log", "apply", "arguments", "debug", "warn", "error", "setVerbosity", "level", "old", "Math", "max"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/ts-invariant/lib/invariant.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf, setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n} : _a;\nvar InvariantError = /** @class */ (function (_super) {\n    __extends(InvariantError, _super);\n    function InvariantError(message) {\n        if (message === void 0) { message = genericMessage; }\n        var _this = _super.call(this, typeof message === \"number\"\n            ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\"\n            : message) || this;\n        _this.framesToPop = 1;\n        _this.name = genericMessage;\n        setPrototypeOf(_this, InvariantError.prototype);\n        return _this;\n    }\n    return InvariantError;\n}(Error));\nexport { InvariantError };\nexport function invariant(condition, message) {\n    if (!condition) {\n        throw new InvariantError(message);\n    }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n    return function () {\n        if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n            // Default to console.log if this host environment happens not to provide\n            // all the console.* methods we need.\n            var method = console[name] || console.log;\n            return method.apply(console, arguments);\n        }\n    };\n}\n(function (invariant) {\n    invariant.debug = wrapConsoleMethod(\"debug\");\n    invariant.log = wrapConsoleMethod(\"log\");\n    invariant.warn = wrapConsoleMethod(\"warn\");\n    invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nexport function setVerbosity(level) {\n    var old = verbosityLevels[verbosityLevel];\n    verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n    return old;\n}\nexport default invariant;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,IAAIC,cAAc,GAAG,qBAAqB;AAC1C,IAAIC,EAAE,GAAGC,MAAM,CAACC,cAAc;EAAEA,cAAc,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,UAAUG,GAAG,EAAEC,KAAK,EAAE;IACnFD,GAAG,CAACE,SAAS,GAAGD,KAAK;IACrB,OAAOD,GAAG;EACd,CAAC,GAAGH,EAAE;AACN,IAAIM,cAAc,GAAG,aAAe,UAAUC,MAAM,EAAE;EAClDT,SAAS,CAACQ,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,OAAO,EAAE;IAC7B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGT,cAAc;IAAE;IACpD,IAAIU,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAE,OAAOF,OAAO,KAAK,QAAQ,GACnDT,cAAc,GAAG,IAAI,GAAGS,OAAO,GAAG,4DAA4D,GAC9FA,OAAO,CAAC,IAAI,IAAI;IACtBC,KAAK,CAACE,WAAW,GAAG,CAAC;IACrBF,KAAK,CAACG,IAAI,GAAGb,cAAc;IAC3BG,cAAc,CAACO,KAAK,EAAEH,cAAc,CAACO,SAAS,CAAC;IAC/C,OAAOJ,KAAK;EAChB;EACA,OAAOH,cAAc;AACzB,CAAC,CAACQ,KAAK,CAAE;AACT,SAASR,cAAc;AACvB,OAAO,SAASS,SAASA,CAACC,SAAS,EAAER,OAAO,EAAE;EAC1C,IAAI,CAACQ,SAAS,EAAE;IACZ,MAAM,IAAIV,cAAc,CAACE,OAAO,CAAC;EACrC;AACJ;AACA,IAAIS,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AACjE,IAAIC,cAAc,GAAGD,eAAe,CAACE,OAAO,CAAC,KAAK,CAAC;AACnD,SAASC,iBAAiBA,CAACR,IAAI,EAAE;EAC7B,OAAO,YAAY;IACf,IAAIK,eAAe,CAACE,OAAO,CAACP,IAAI,CAAC,IAAIM,cAAc,EAAE;MACjD;MACA;MACA,IAAIG,MAAM,GAAGC,OAAO,CAACV,IAAI,CAAC,IAAIU,OAAO,CAACC,GAAG;MACzC,OAAOF,MAAM,CAACG,KAAK,CAACF,OAAO,EAAEG,SAAS,CAAC;IAC3C;EACJ,CAAC;AACL;AACA,CAAC,UAAUV,SAAS,EAAE;EAClBA,SAAS,CAACW,KAAK,GAAGN,iBAAiB,CAAC,OAAO,CAAC;EAC5CL,SAAS,CAACQ,GAAG,GAAGH,iBAAiB,CAAC,KAAK,CAAC;EACxCL,SAAS,CAACY,IAAI,GAAGP,iBAAiB,CAAC,MAAM,CAAC;EAC1CL,SAAS,CAACa,KAAK,GAAGR,iBAAiB,CAAC,OAAO,CAAC;AAChD,CAAC,EAAEL,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO,SAASc,YAAYA,CAACC,KAAK,EAAE;EAChC,IAAIC,GAAG,GAAGd,eAAe,CAACC,cAAc,CAAC;EACzCA,cAAc,GAAGc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhB,eAAe,CAACE,OAAO,CAACW,KAAK,CAAC,CAAC;EAC5D,OAAOC,GAAG;AACd;AACA,eAAehB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}