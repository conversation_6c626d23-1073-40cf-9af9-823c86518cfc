{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"@angular/common\";\nfunction ReunionFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r8.titre);\n  }\n}\nfunction ReunionFormComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_ng_container_52_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r11.username);\n  }\n}\nfunction ReunionFormComponent_ng_container_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_52_option_1_Template, 2, 2, \"option\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r9 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r9);\n  }\n}\nexport class ReunionFormComponent {\n  constructor(fb, reunionService, planningService, userService, route, router, authService) {\n    this.fb = fb;\n    this.reunionService = reunionService;\n    this.planningService = planningService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.authService = authService;\n    this.plannings = [];\n    this.loading = true;\n    this.isSubmitting = false;\n    this.error = null;\n    this.isEditMode = false;\n    this.currentReunionId = null;\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n    this.users$ = this.userService.getAllUsers();\n  }\n  ngOnInit() {\n    this.loadPlannings();\n    this.checkEditMode();\n  }\n  checkEditMode() {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n  loadPlannings() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: response => {\n        this.plannings = response.plannings || [];\n      },\n      error: err => {\n        this.error = err;\n      }\n    });\n  }\n  loadReunion(id) {\n    this.reunionService.getReunionById(id).subscribe({\n      next: reunion => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n  formatDateForInput(date) {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  onSubmit() {\n    if (this.reunionForm.invalid) return;\n    this.isSubmitting = true;\n    const formValue = this.reunionForm.value;\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n    const reunionData = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n    console.log(reunionData);\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: res => {\n        this.router.navigate(['/reunions', res?.id]);\n        this.isSubmitting = false;\n      },\n      error: err => {\n        this.error = err;\n        this.isSubmitting = false;\n      }\n    });\n  }\n  goReunion() {\n    this.router.navigate(['/reunions']);\n  }\n  static {\n    this.ɵfac = function ReunionFormComponent_Factory(t) {\n      return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionFormComponent,\n      selectors: [[\"app-reunion-form\"]],\n      decls: 59,\n      vars: 14,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [3, \"value\"]],\n      template: function ReunionFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 2);\n          i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(4, ReunionFormComponent_div_4_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\")(7, \"label\", 5);\n          i0.ɵɵtext(8, \"Titre *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"input\", 6);\n          i0.ɵɵtemplate(10, ReunionFormComponent_div_10_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\")(12, \"label\", 8);\n          i0.ɵɵtext(13, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"textarea\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\")(17, \"label\", 11);\n          i0.ɵɵtext(18, \"Date *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 12);\n          i0.ɵɵtemplate(20, ReunionFormComponent_div_20_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\")(22, \"label\", 13);\n          i0.ɵɵtext(23, \"Heure de d\\u00E9but *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 14);\n          i0.ɵɵtemplate(25, ReunionFormComponent_div_25_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\")(27, \"label\", 15);\n          i0.ɵɵtext(28, \"Heure de fin *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 16);\n          i0.ɵɵtemplate(30, ReunionFormComponent_div_30_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 17)(32, \"div\")(33, \"label\", 18);\n          i0.ɵɵtext(34, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\")(37, \"label\", 20);\n          i0.ɵɵtext(38, \"Lien Visio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\")(41, \"label\", 22);\n          i0.ɵɵtext(42, \"Planning *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"select\", 23)(44, \"option\", 24);\n          i0.ɵɵtext(45, \"S\\u00E9lectionnez un planning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, ReunionFormComponent_option_46_Template, 2, 2, \"option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ReunionFormComponent_div_47_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\")(49, \"label\", 26);\n          i0.ɵɵtext(50, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"select\", 27);\n          i0.ɵɵtemplate(52, ReunionFormComponent_ng_container_52_Template, 2, 1, \"ng-container\", 28);\n          i0.ɵɵpipe(53, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 29)(55, \"button\", 30);\n          i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_55_listener() {\n            return ctx.goReunion();\n          });\n          i0.ɵɵtext(56, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"button\", 31);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_8_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(53, 12, ctx.users$));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWZvcm0uY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1mb3JtL3JldW5pb24tZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "message", "ɵɵproperty", "planning_r8", "id", "ɵɵtextInterpolate", "titre", "user_r11", "_id", "username", "ɵɵelementContainerStart", "ɵɵtemplate", "ReunionFormComponent_ng_container_52_option_1_Template", "ɵɵelementContainerEnd", "users_r9", "ReunionFormComponent", "constructor", "fb", "reunionService", "planningService", "userService", "route", "router", "authService", "plannings", "loading", "isSubmitting", "isEditMode", "currentReunionId", "reunionForm", "group", "required", "description", "date", "heureDebut", "heure<PERSON>in", "lieu", "lienVisio", "planning", "participants", "users$", "getAllUsers", "ngOnInit", "loadPlannings", "checkEditMode", "reunionId", "snapshot", "paramMap", "get", "loadReunion", "userId", "getCurrentUserId", "getPlanningsByUser", "subscribe", "next", "response", "err", "getReunionById", "reunion", "patchValue", "dateDebut", "formatDateForInput", "dateFin", "planningId", "Date", "toISOString", "slice", "onSubmit", "invalid", "formValue", "value", "reunionData", "console", "log", "createReunion", "res", "navigate", "goReunion", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ReunionService", "i3", "PlanningService", "i4", "DataService", "i5", "ActivatedRoute", "Router", "i6", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ReunionFormComponent_Template", "rf", "ctx", "ɵɵlistener", "ReunionFormComponent_Template_form_ngSubmit_3_listener", "ReunionFormComponent_div_4_Template", "ɵɵelement", "ReunionFormComponent_div_10_Template", "ReunionFormComponent_div_20_Template", "ReunionFormComponent_div_25_Template", "ReunionFormComponent_div_30_Template", "ReunionFormComponent_option_46_Template", "ReunionFormComponent_div_47_Template", "ReunionFormComponent_ng_container_52_Template", "ReunionFormComponent_Template_button_click_55_listener", "tmp_3_0", "touched", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_8_0", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-form\\reunion-form.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-form\\reunion-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ReunionService } from 'src/app/services/reunion.service';\r\nimport { PlanningService } from 'src/app/services/planning.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Reunion } from 'src/app/models/reunion.model';\r\nimport { Planning } from 'src/app/models/planning.model';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport {Observable} from \"rxjs\";\r\nimport {User} from \"@app/models/user.model\";\r\nimport {DataService} from \"@app/services/data.service\";\r\n\r\n@Component({\r\n  selector: 'app-reunion-form',\r\n  templateUrl: './reunion-form.component.html',\r\n  styleUrls: ['./reunion-form.component.css']\r\n})\r\nexport class ReunionFormComponent implements OnInit {\r\n  reunionForm: FormGroup;\r\n  plannings: Planning[] = [];\r\n  users$: Observable<User[]>;\r\n  loading = true;\r\n  isSubmitting = false;\r\n  error: any = null;\r\n  isEditMode = false;\r\n  currentReunionId: string | null = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private reunionService: ReunionService,\r\n    private planningService: PlanningService,\r\n    private userService: DataService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.reunionForm = this.fb.group({\r\n      titre: ['', Validators.required],\r\n      description: [''],\r\n      date: ['', Validators.required],\r\n      heureDebut: ['', Validators.required],\r\n      heureFin: ['', Validators.required],\r\n      lieu: [''],\r\n      lienVisio: [''],\r\n      planning: ['', Validators.required],\r\n      participants: [[]]\r\n    });\r\n\r\n\r\n    this.users$ = this.userService.getAllUsers();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadPlannings();\r\n    this.checkEditMode();\r\n  }\r\n\r\n  checkEditMode(): void {\r\n    const reunionId = this.route.snapshot.paramMap.get('id');\r\n    if (reunionId) {\r\n      this.isEditMode = true;\r\n      this.currentReunionId = reunionId;\r\n      this.loadReunion(reunionId);\r\n    }\r\n  }\r\n\r\n  loadPlannings(): void {\r\n    const userId = this.authService.getCurrentUserId();\r\n    if (!userId) return;\r\n\r\n    this.planningService.getPlanningsByUser(userId).subscribe({\r\n      next: (response:any) => {\r\n        this.plannings = response.plannings || [];\r\n      },\r\n      error: (err) => {\r\n        this.error = err;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadReunion(id: string): void {\r\n    this.reunionService.getReunionById(id).subscribe({\r\n      next: (reunion) => {\r\n        this.reunionForm.patchValue({\r\n          titre: reunion.titre,\r\n          description: reunion.description,\r\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\r\n          dateFin: this.formatDateForInput(reunion.dateFin),\r\n          lieu: reunion.lieu,\r\n          lienVisio: reunion.lienVisio,\r\n          planningId: reunion.planningId,\r\n          participants: reunion.participants\r\n        });\r\n        this.loading = false;\r\n      },\r\n      error: (err) => {\r\n        this.error = err;\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  formatDateForInput(date: Date | string): string {\r\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.reunionForm.invalid) return;\r\n\r\n    this.isSubmitting = true;\r\n    const formValue = this.reunionForm.value;\r\n\r\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\r\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\r\n    const heureFin = formValue.heureFin;\r\n\r\n    const reunionData: any = {\r\n      titre: formValue.titre,\r\n      description: formValue.description,\r\n      date: date,\r\n      heureDebut: heureDebut,\r\n      heureFin: heureFin,\r\n      lieu: formValue.lieu,\r\n      lienVisio: formValue.lienVisio,\r\n      planning: formValue.planning,\r\n      participants: formValue.participants || []\r\n    };\r\n\r\n    console.log(reunionData);\r\n\r\n    this.reunionService.createReunion(reunionData).subscribe({\r\n      next: (res) => {\r\n        this.router.navigate(['/reunions', res?.id]);\r\n        this.isSubmitting = false;\r\n      },\r\n      error: (err) => {\r\n        this.error = err;\r\n        this.isSubmitting = false;\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  goReunion(): void {\r\n    this.router.navigate(['/reunions']);\r\n  }\r\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\r\n  <h1 class=\"text-2xl font-bold text-gray-800 mb-6\">\r\n    {{ isEditMode ? 'Modifier la Réunion' : 'Nouvelle Réunion' }}\r\n  </h1>\r\n\r\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-lg shadow-md p-6\">\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error.message || 'Une erreur est survenue' }}\r\n    </div>\r\n\r\n    <div class=\"grid grid-cols-1 gap-6\">\r\n      <!-- Titre -->\r\n      <div>\r\n        <label for=\"titre\" class=\"block text-sm font-medium text-gray-700\">Titre *</label>\r\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\r\n               class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\r\n             class=\"text-red-500 text-sm mt-1\">\r\n          Le titre est obligatoire\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Description -->\r\n      <div>\r\n        <label for=\"description\" class=\"block text-sm font-medium text-gray-700\">Description</label>\r\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\r\n                  class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\"></textarea>\r\n      </div>\r\n\r\n      <!-- Date and Time -->\r\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n        <div>\r\n          <label for=\"date\" class=\"block text-sm font-medium text-gray-700\">Date *</label>\r\n          <input id=\"date\" type=\"date\" formControlName=\"date\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            La date est obligatoire\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"heureDebut\" class=\"block text-sm font-medium text-gray-700\">Heure de début *</label>\r\n          <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            L'heure de début est obligatoire\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"heureFin\" class=\"block text-sm font-medium text-gray-700\">Heure de fin *</label>\r\n          <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            L'heure de fin est obligatoire\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Lieu / Lien visio -->\r\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        <div>\r\n          <label for=\"lieu\" class=\"block text-sm font-medium text-gray-700\">Lieu</label>\r\n          <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"lienVisio\" class=\"block text-sm font-medium text-gray-700\">Lien Visio</label>\r\n          <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Planning -->\r\n      <div>\r\n        <label for=\"planning\" class=\"block text-sm font-medium text-gray-700\">Planning *</label>\r\n        <select id=\"planning\" formControlName=\"planning\"\r\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <option value=\"\">Sélectionnez un planning</option>\r\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning.id\">{{ planning.titre }}</option>\r\n        </select>\r\n        <div *ngIf=\"reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched\"\r\n             class=\"text-red-500 text-sm mt-1\">\r\n          Le planning est obligatoire\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Participants -->\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\r\n        <select formControlName=\"participants\" multiple\r\n                class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\">\r\n          <ng-container *ngIf=\"users$ | async as users\">\r\n            <option *ngFor=\"let user of users\" [value]=\"user._id\">{{ user.username }}</option>\r\n          </ng-container>\r\n        </select>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mt-6 flex justify-end space-x-3\">\r\n      <button type=\"button\" (click)=\"goReunion()\"\r\n              class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\">\r\n        Annuler\r\n      </button>\r\n      <button type=\"submit\" [disabled]=\"reunionForm.invalid || isSubmitting\"\r\n              class=\"px-4 py-2 rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50\">\r\n        {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICK/DC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,mCACF;;;;;IAQIR,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBJH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBNH,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnDH,EAAA,CAAAS,UAAA,UAAAC,WAAA,CAAAC,EAAA,CAAqB;IAACX,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,iBAAA,CAAAF,WAAA,CAAAG,KAAA,CAAoB;;;;;IAEvFb,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IASFH,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAS,UAAA,UAAAK,QAAA,CAAAC,GAAA,CAAkB;IAACf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAY,iBAAA,CAAAE,QAAA,CAAAE,QAAA,CAAmB;;;;;IAD3EhB,EAAA,CAAAiB,uBAAA,GAA8C;IAC5CjB,EAAA,CAAAkB,UAAA,IAAAC,sDAAA,qBAAkF;IACpFnB,EAAA,CAAAoB,qBAAA,EAAe;;;;IADYpB,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAS,UAAA,YAAAY,QAAA,CAAQ;;;ADhF7C,OAAM,MAAOC,oBAAoB;EAU/BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,WAA4B;IAN5B,KAAAN,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAC,SAAS,GAAe,EAAE;IAE1B,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAA1B,KAAK,GAAQ,IAAI;IACjB,KAAA2B,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAkB,IAAI;IAWpC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,EAAE,CAACa,KAAK,CAAC;MAC/BxB,KAAK,EAAE,CAAC,EAAE,EAAEd,UAAU,CAACuC,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,EAAEzC,UAAU,CAACuC,QAAQ,CAAC;MAC/BG,UAAU,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAACuC,QAAQ,CAAC;MACrCI,QAAQ,EAAE,CAAC,EAAE,EAAE3C,UAAU,CAACuC,QAAQ,CAAC;MACnCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAACuC,QAAQ,CAAC;MACnCQ,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IAGF,IAAI,CAACC,MAAM,GAAG,IAAI,CAACpB,WAAW,CAACqB,WAAW,EAAE;EAC9C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,MAAMC,SAAS,GAAG,IAAI,CAACxB,KAAK,CAACyB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACxD,IAAIH,SAAS,EAAE;MACb,IAAI,CAAClB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAGiB,SAAS;MACjC,IAAI,CAACI,WAAW,CAACJ,SAAS,CAAC;;EAE/B;EAEAF,aAAaA,CAAA;IACX,MAAMO,MAAM,GAAG,IAAI,CAAC3B,WAAW,CAAC4B,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb,IAAI,CAAC/B,eAAe,CAACiC,kBAAkB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAY,IAAI;QACrB,IAAI,CAAC/B,SAAS,GAAG+B,QAAQ,CAAC/B,SAAS,IAAI,EAAE;MAC3C,CAAC;MACDxB,KAAK,EAAGwD,GAAG,IAAI;QACb,IAAI,CAACxD,KAAK,GAAGwD,GAAG;MAClB;KACD,CAAC;EACJ;EAEAP,WAAWA,CAAC7C,EAAU;IACpB,IAAI,CAACc,cAAc,CAACuC,cAAc,CAACrD,EAAE,CAAC,CAACiD,SAAS,CAAC;MAC/CC,IAAI,EAAGI,OAAO,IAAI;QAChB,IAAI,CAAC7B,WAAW,CAAC8B,UAAU,CAAC;UAC1BrD,KAAK,EAAEoD,OAAO,CAACpD,KAAK;UACpB0B,WAAW,EAAE0B,OAAO,CAAC1B,WAAW;UAChC4B,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAACH,OAAO,CAACE,SAAS,CAAC;UACrDE,OAAO,EAAE,IAAI,CAACD,kBAAkB,CAACH,OAAO,CAACI,OAAO,CAAC;UACjD1B,IAAI,EAAEsB,OAAO,CAACtB,IAAI;UAClBC,SAAS,EAAEqB,OAAO,CAACrB,SAAS;UAC5B0B,UAAU,EAAEL,OAAO,CAACK,UAAU;UAC9BxB,YAAY,EAAEmB,OAAO,CAACnB;SACvB,CAAC;QACF,IAAI,CAACd,OAAO,GAAG,KAAK;MACtB,CAAC;MACDzB,KAAK,EAAGwD,GAAG,IAAI;QACb,IAAI,CAACxD,KAAK,GAAGwD,GAAG;QAChB,IAAI,CAAC/B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAoC,kBAAkBA,CAAC5B,IAAmB;IACpC,OAAO,IAAI+B,IAAI,CAAC/B,IAAI,CAAC,CAACgC,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACpD;;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,WAAW,CAACuC,OAAO,EAAE;IAE9B,IAAI,CAAC1C,YAAY,GAAG,IAAI;IACxB,MAAM2C,SAAS,GAAG,IAAI,CAACxC,WAAW,CAACyC,KAAK;IAExC,MAAMrC,IAAI,GAAGoC,SAAS,CAACpC,IAAI,CAAC,CAAC;IAC7B,MAAMC,UAAU,GAAGmC,SAAS,CAACnC,UAAU,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAGkC,SAAS,CAAClC,QAAQ;IAEnC,MAAMoC,WAAW,GAAQ;MACvBjE,KAAK,EAAE+D,SAAS,CAAC/D,KAAK;MACtB0B,WAAW,EAAEqC,SAAS,CAACrC,WAAW;MAClCC,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA,QAAQ;MAClBC,IAAI,EAAEiC,SAAS,CAACjC,IAAI;MACpBC,SAAS,EAAEgC,SAAS,CAAChC,SAAS;MAC9BC,QAAQ,EAAE+B,SAAS,CAAC/B,QAAQ;MAC5BC,YAAY,EAAE8B,SAAS,CAAC9B,YAAY,IAAI;KACzC;IAEDiC,OAAO,CAACC,GAAG,CAACF,WAAW,CAAC;IAExB,IAAI,CAACrD,cAAc,CAACwD,aAAa,CAACH,WAAW,CAAC,CAAClB,SAAS,CAAC;MACvDC,IAAI,EAAGqB,GAAG,IAAI;QACZ,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,WAAW,EAAED,GAAG,EAAEvE,EAAE,CAAC,CAAC;QAC5C,IAAI,CAACsB,YAAY,GAAG,KAAK;MAC3B,CAAC;MACD1B,KAAK,EAAGwD,GAAG,IAAI;QACb,IAAI,CAACxD,KAAK,GAAGwD,GAAG;QAChB,IAAI,CAAC9B,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAGAmD,SAASA,CAAA;IACP,IAAI,CAACvD,MAAM,CAACsD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;;;uBAhIW7D,oBAAoB,EAAAtB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA3F,EAAA,CAAAqF,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAqF,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA/F,EAAA,CAAAqF,iBAAA,CAAAS,EAAA,CAAAE,MAAA,GAAAhG,EAAA,CAAAqF,iBAAA,CAAAY,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApB5E,oBAAoB;MAAA6E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCzG,EAAA,CAAAC,cAAA,aAAmD;UAE/CD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,cAAkG;UAAlED,EAAA,CAAA2G,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAhC,QAAA,EAAU;UAAA,EAAC;UACrD1E,EAAA,CAAAkB,UAAA,IAAA2F,mCAAA,iBAEM;UAEN7G,EAAA,CAAAC,cAAA,aAAoC;UAGmCD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClFH,EAAA,CAAA8G,SAAA,eACoH;UACpH9G,EAAA,CAAAkB,UAAA,KAAA6F,oCAAA,iBAGM;UACR/G,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UACsED,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA8G,SAAA,mBACkI;UACpI9G,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmD;UAEmBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChFH,EAAA,CAAA8G,SAAA,iBACoH;UACpH9G,EAAA,CAAAkB,UAAA,KAAA8F,oCAAA,iBAGM;UACRhH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACqED,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChGH,EAAA,CAAA8G,SAAA,iBACoH;UACpH9G,EAAA,CAAAkB,UAAA,KAAA+F,oCAAA,iBAGM;UACRjH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA8G,SAAA,iBACoH;UACpH9G,EAAA,CAAAkB,UAAA,KAAAgG,oCAAA,iBAGM;UACRlH,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAmD;UAEmBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAA8G,SAAA,iBACoH;UACtH9G,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACoED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzFH,EAAA,CAAA8G,SAAA,iBACoH;UACtH9G,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxFH,EAAA,CAAAC,cAAA,kBACqH;UAClGD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAkB,UAAA,KAAAiG,uCAAA,qBAA8F;UAChGnH,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAkB,UAAA,KAAAkG,oCAAA,iBAGM;UACRpH,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UACoDD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBACiI;UAC/HD,EAAA,CAAAkB,UAAA,KAAAmG,6CAAA,2BAEe;;UACjBrH,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6C;UACrBD,EAAA,CAAA2G,UAAA,mBAAAW,uDAAA;YAAA,OAASZ,GAAA,CAAAtB,SAAA,EAAW;UAAA,EAAC;UAEzCpF,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAC0H;UACxHD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;UA7GXH,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAqG,GAAA,CAAAxE,UAAA,6DACF;UAEMlC,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAS,UAAA,cAAAiG,GAAA,CAAAtE,WAAA,CAAyB;UACvBpC,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAS,UAAA,SAAAiG,GAAA,CAAAnG,KAAA,CAAW;UAUPP,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAS,UAAA,WAAA8G,OAAA,GAAAb,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,4BAAAgE,OAAA,CAAA5C,OAAA,OAAA4C,OAAA,GAAAb,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,4BAAAgE,OAAA,CAAAC,OAAA,EAA4E;UAmB1ExH,EAAA,CAAAI,SAAA,IAA0E;UAA1EJ,EAAA,CAAAS,UAAA,WAAAgH,OAAA,GAAAf,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,2BAAAkE,OAAA,CAAA9C,OAAA,OAAA8C,OAAA,GAAAf,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,2BAAAkE,OAAA,CAAAD,OAAA,EAA0E;UAU1ExH,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAS,UAAA,WAAAiH,OAAA,GAAAhB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,iCAAAmE,OAAA,CAAA/C,OAAA,OAAA+C,OAAA,GAAAhB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,iCAAAmE,OAAA,CAAAF,OAAA,EAAsF;UAUtFxH,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAS,UAAA,WAAAkH,OAAA,GAAAjB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,+BAAAoE,OAAA,CAAAhD,OAAA,OAAAgD,OAAA,GAAAjB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,+BAAAoE,OAAA,CAAAH,OAAA,EAAkF;UA4B3DxH,EAAA,CAAAI,SAAA,IAAY;UAAZJ,EAAA,CAAAS,UAAA,YAAAiG,GAAA,CAAA3E,SAAA,CAAY;UAErC/B,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAS,UAAA,WAAAmH,OAAA,GAAAlB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,+BAAAqE,OAAA,CAAAjD,OAAA,OAAAiD,OAAA,GAAAlB,GAAA,CAAAtE,WAAA,CAAAmB,GAAA,+BAAAqE,OAAA,CAAAJ,OAAA,EAAkF;UAWvExH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAS,UAAA,SAAAT,EAAA,CAAA6H,WAAA,SAAAnB,GAAA,CAAA3D,MAAA,EAAqB;UAYlB/C,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAS,UAAA,aAAAiG,GAAA,CAAAtE,WAAA,CAAAuC,OAAA,IAAA+B,GAAA,CAAAzE,YAAA,CAAgD;UAEpEjC,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAqG,GAAA,CAAAzE,YAAA,4CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}