{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { isNonNullObject } from \"./objects.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function mergeDeep() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  return mergeDeepArray(sources);\n}\n// In almost any situation where you could succeed in getting the\n// TypeScript compiler to infer a tuple type for the sources array, you\n// could just use mergeDeep instead of mergeDeepArray, so instead of\n// trying to convert T[] to an intersection type we just infer the array\n// element type, which works perfectly when the sources array has a\n// consistent element type.\nexport function mergeDeepArray(sources) {\n  var target = sources[0] || {};\n  var count = sources.length;\n  if (count > 1) {\n    var merger = new DeepMerger();\n    for (var i = 1; i < count; ++i) {\n      target = merger.merge(target, sources[i]);\n    }\n  }\n  return target;\n}\nvar defaultReconciler = function (target, source, property) {\n  return this.merge(target[property], source[property]);\n};\nvar DeepMerger = /** @class */function () {\n  function DeepMerger(reconciler) {\n    if (reconciler === void 0) {\n      reconciler = defaultReconciler;\n    }\n    this.reconciler = reconciler;\n    this.isObject = isNonNullObject;\n    this.pastCopies = new Set();\n  }\n  DeepMerger.prototype.merge = function (target, source) {\n    var _this = this;\n    var context = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      context[_i - 2] = arguments[_i];\n    }\n    if (isNonNullObject(source) && isNonNullObject(target)) {\n      Object.keys(source).forEach(function (sourceKey) {\n        if (hasOwnProperty.call(target, sourceKey)) {\n          var targetValue = target[sourceKey];\n          if (source[sourceKey] !== targetValue) {\n            var result = _this.reconciler.apply(_this, __spreadArray([target, source, sourceKey], context, false));\n            // A well-implemented reconciler may return targetValue to indicate\n            // the merge changed nothing about the structure of the target.\n            if (result !== targetValue) {\n              target = _this.shallowCopyForMerge(target);\n              target[sourceKey] = result;\n            }\n          }\n        } else {\n          // If there is no collision, the target can safely share memory with\n          // the source, and the recursion can terminate here.\n          target = _this.shallowCopyForMerge(target);\n          target[sourceKey] = source[sourceKey];\n        }\n      });\n      return target;\n    }\n    // If source (or target) is not an object, let source replace target.\n    return source;\n  };\n  DeepMerger.prototype.shallowCopyForMerge = function (value) {\n    if (isNonNullObject(value)) {\n      if (!this.pastCopies.has(value)) {\n        if (Array.isArray(value)) {\n          value = value.slice(0);\n        } else {\n          value = __assign({\n            __proto__: Object.getPrototypeOf(value)\n          }, value);\n        }\n        this.pastCopies.add(value);\n      }\n    }\n    return value;\n  };\n  return DeepMerger;\n}();\nexport { DeepMerger };", "map": {"version": 3, "names": ["__assign", "__spread<PERSON><PERSON>y", "isNonNullObject", "hasOwnProperty", "Object", "prototype", "mergeDeep", "sources", "_i", "arguments", "length", "mergeDeepArray", "target", "count", "merger", "DeepMerger", "i", "merge", "defaultReconciler", "source", "property", "reconciler", "isObject", "pastCopies", "Set", "_this", "context", "keys", "for<PERSON>ach", "sourceKey", "call", "targetValue", "result", "apply", "shallowCopyForMerge", "value", "has", "Array", "isArray", "slice", "__proto__", "getPrototypeOf", "add"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/mergeDeep.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { isNonNullObject } from \"./objects.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function mergeDeep() {\n    var sources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        sources[_i] = arguments[_i];\n    }\n    return mergeDeepArray(sources);\n}\n// In almost any situation where you could succeed in getting the\n// TypeScript compiler to infer a tuple type for the sources array, you\n// could just use mergeDeep instead of mergeDeepArray, so instead of\n// trying to convert T[] to an intersection type we just infer the array\n// element type, which works perfectly when the sources array has a\n// consistent element type.\nexport function mergeDeepArray(sources) {\n    var target = sources[0] || {};\n    var count = sources.length;\n    if (count > 1) {\n        var merger = new DeepMerger();\n        for (var i = 1; i < count; ++i) {\n            target = merger.merge(target, sources[i]);\n        }\n    }\n    return target;\n}\nvar defaultReconciler = function (target, source, property) {\n    return this.merge(target[property], source[property]);\n};\nvar DeepMerger = /** @class */ (function () {\n    function DeepMerger(reconciler) {\n        if (reconciler === void 0) { reconciler = defaultReconciler; }\n        this.reconciler = reconciler;\n        this.isObject = isNonNullObject;\n        this.pastCopies = new Set();\n    }\n    DeepMerger.prototype.merge = function (target, source) {\n        var _this = this;\n        var context = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            context[_i - 2] = arguments[_i];\n        }\n        if (isNonNullObject(source) && isNonNullObject(target)) {\n            Object.keys(source).forEach(function (sourceKey) {\n                if (hasOwnProperty.call(target, sourceKey)) {\n                    var targetValue = target[sourceKey];\n                    if (source[sourceKey] !== targetValue) {\n                        var result = _this.reconciler.apply(_this, __spreadArray([target,\n                            source,\n                            sourceKey], context, false));\n                        // A well-implemented reconciler may return targetValue to indicate\n                        // the merge changed nothing about the structure of the target.\n                        if (result !== targetValue) {\n                            target = _this.shallowCopyForMerge(target);\n                            target[sourceKey] = result;\n                        }\n                    }\n                }\n                else {\n                    // If there is no collision, the target can safely share memory with\n                    // the source, and the recursion can terminate here.\n                    target = _this.shallowCopyForMerge(target);\n                    target[sourceKey] = source[sourceKey];\n                }\n            });\n            return target;\n        }\n        // If source (or target) is not an object, let source replace target.\n        return source;\n    };\n    DeepMerger.prototype.shallowCopyForMerge = function (value) {\n        if (isNonNullObject(value)) {\n            if (!this.pastCopies.has(value)) {\n                if (Array.isArray(value)) {\n                    value = value.slice(0);\n                }\n                else {\n                    value = __assign({ __proto__: Object.getPrototypeOf(value) }, value);\n                }\n                this.pastCopies.add(value);\n            }\n        }\n        return value;\n    };\n    return DeepMerger;\n}());\nexport { DeepMerger };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AAC/C,SAASC,eAAe,QAAQ,cAAc;AAC9C,IAAIC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACpD,OAAO,SAASG,SAASA,CAAA,EAAG;EACxB,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,OAAO,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC/B;EACA,OAAOG,cAAc,CAACJ,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACJ,OAAO,EAAE;EACpC,IAAIK,MAAM,GAAGL,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EAC7B,IAAIM,KAAK,GAAGN,OAAO,CAACG,MAAM;EAC1B,IAAIG,KAAK,GAAG,CAAC,EAAE;IACX,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,EAAE,EAAEG,CAAC,EAAE;MAC5BJ,MAAM,GAAGE,MAAM,CAACG,KAAK,CAACL,MAAM,EAAEL,OAAO,CAACS,CAAC,CAAC,CAAC;IAC7C;EACJ;EACA,OAAOJ,MAAM;AACjB;AACA,IAAIM,iBAAiB,GAAG,SAAAA,CAAUN,MAAM,EAAEO,MAAM,EAAEC,QAAQ,EAAE;EACxD,OAAO,IAAI,CAACH,KAAK,CAACL,MAAM,CAACQ,QAAQ,CAAC,EAAED,MAAM,CAACC,QAAQ,CAAC,CAAC;AACzD,CAAC;AACD,IAAIL,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACM,UAAU,EAAE;IAC5B,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAGH,iBAAiB;IAAE;IAC7D,IAAI,CAACG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGpB,eAAe;IAC/B,IAAI,CAACqB,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B;EACAT,UAAU,CAACV,SAAS,CAACY,KAAK,GAAG,UAAUL,MAAM,EAAEO,MAAM,EAAE;IACnD,IAAIM,KAAK,GAAG,IAAI;IAChB,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIlB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CkB,OAAO,CAAClB,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IACnC;IACA,IAAIN,eAAe,CAACiB,MAAM,CAAC,IAAIjB,eAAe,CAACU,MAAM,CAAC,EAAE;MACpDR,MAAM,CAACuB,IAAI,CAACR,MAAM,CAAC,CAACS,OAAO,CAAC,UAAUC,SAAS,EAAE;QAC7C,IAAI1B,cAAc,CAAC2B,IAAI,CAAClB,MAAM,EAAEiB,SAAS,CAAC,EAAE;UACxC,IAAIE,WAAW,GAAGnB,MAAM,CAACiB,SAAS,CAAC;UACnC,IAAIV,MAAM,CAACU,SAAS,CAAC,KAAKE,WAAW,EAAE;YACnC,IAAIC,MAAM,GAAGP,KAAK,CAACJ,UAAU,CAACY,KAAK,CAACR,KAAK,EAAExB,aAAa,CAAC,CAACW,MAAM,EAC5DO,MAAM,EACNU,SAAS,CAAC,EAAEH,OAAO,EAAE,KAAK,CAAC,CAAC;YAChC;YACA;YACA,IAAIM,MAAM,KAAKD,WAAW,EAAE;cACxBnB,MAAM,GAAGa,KAAK,CAACS,mBAAmB,CAACtB,MAAM,CAAC;cAC1CA,MAAM,CAACiB,SAAS,CAAC,GAAGG,MAAM;YAC9B;UACJ;QACJ,CAAC,MACI;UACD;UACA;UACApB,MAAM,GAAGa,KAAK,CAACS,mBAAmB,CAACtB,MAAM,CAAC;UAC1CA,MAAM,CAACiB,SAAS,CAAC,GAAGV,MAAM,CAACU,SAAS,CAAC;QACzC;MACJ,CAAC,CAAC;MACF,OAAOjB,MAAM;IACjB;IACA;IACA,OAAOO,MAAM;EACjB,CAAC;EACDJ,UAAU,CAACV,SAAS,CAAC6B,mBAAmB,GAAG,UAAUC,KAAK,EAAE;IACxD,IAAIjC,eAAe,CAACiC,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC,IAAI,CAACZ,UAAU,CAACa,GAAG,CAACD,KAAK,CAAC,EAAE;QAC7B,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;UACtBA,KAAK,GAAGA,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC;QAC1B,CAAC,MACI;UACDJ,KAAK,GAAGnC,QAAQ,CAAC;YAAEwC,SAAS,EAAEpC,MAAM,CAACqC,cAAc,CAACN,KAAK;UAAE,CAAC,EAAEA,KAAK,CAAC;QACxE;QACA,IAAI,CAACZ,UAAU,CAACmB,GAAG,CAACP,KAAK,CAAC;MAC9B;IACJ;IACA,OAAOA,KAAK;EAChB,CAAC;EACD,OAAOpB,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}