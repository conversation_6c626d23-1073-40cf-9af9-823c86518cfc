{"ast": null, "code": "import { dep, Slot } from \"optimism\";\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport var cacheSlot = new Slot();\nvar cacheInfoMap = new WeakMap();\nfunction getCacheInfo(cache) {\n  var info = cacheInfoMap.get(cache);\n  if (!info) {\n    cacheInfoMap.set(cache, info = {\n      vars: new Set(),\n      dep: dep()\n    });\n  }\n  return info;\n}\nexport function forgetCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.forgetCache(cache);\n  });\n}\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.attachCache(cache);\n  });\n}\nexport function makeVar(value) {\n  var caches = new Set();\n  var listeners = new Set();\n  var rv = function (newValue) {\n    if (arguments.length > 0) {\n      if (value !== newValue) {\n        value = newValue;\n        caches.forEach(function (cache) {\n          // Invalidate any fields with custom read functions that\n          // consumed this variable, so query results involving those\n          // fields will be recomputed the next time we read them.\n          getCacheInfo(cache).dep.dirty(rv);\n          // Broadcast changes to any caches that have previously read\n          // from this variable.\n          broadcast(cache);\n        });\n        // Finally, notify any listeners added via rv.onNextChange.\n        var oldListeners = Array.from(listeners);\n        listeners.clear();\n        oldListeners.forEach(function (listener) {\n          return listener(value);\n        });\n      }\n    } else {\n      // When reading from the variable, obtain the current cache from\n      // context via cacheSlot. This isn't entirely foolproof, but it's\n      // the same system that powers varDep.\n      var cache = cacheSlot.getValue();\n      if (cache) {\n        attach(cache);\n        getCacheInfo(cache).dep(rv);\n      }\n    }\n    return value;\n  };\n  rv.onNextChange = function (listener) {\n    listeners.add(listener);\n    return function () {\n      listeners.delete(listener);\n    };\n  };\n  var attach = rv.attachCache = function (cache) {\n    caches.add(cache);\n    getCacheInfo(cache).vars.add(rv);\n    return rv;\n  };\n  rv.forgetCache = function (cache) {\n    return caches.delete(cache);\n  };\n  return rv;\n}\nfunction broadcast(cache) {\n  if (cache.broadcastWatches) {\n    cache.broadcastWatches();\n  }\n}", "map": {"version": 3, "names": ["dep", "Slot", "cacheSlot", "cacheInfoMap", "WeakMap", "getCacheInfo", "cache", "info", "get", "set", "vars", "Set", "forgetCache", "for<PERSON>ach", "rv", "recallCache", "attachCache", "makeVar", "value", "caches", "listeners", "newValue", "arguments", "length", "dirty", "broadcast", "oldListeners", "Array", "from", "clear", "listener", "getValue", "attach", "onNextChange", "add", "delete", "broadcastWatches"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/reactiveVars.js"], "sourcesContent": ["import { dep, Slot } from \"optimism\";\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport var cacheSlot = new Slot();\nvar cacheInfoMap = new WeakMap();\nfunction getCacheInfo(cache) {\n    var info = cacheInfoMap.get(cache);\n    if (!info) {\n        cacheInfoMap.set(cache, (info = {\n            vars: new Set(),\n            dep: dep(),\n        }));\n    }\n    return info;\n}\nexport function forgetCache(cache) {\n    getCacheInfo(cache).vars.forEach(function (rv) { return rv.forgetCache(cache); });\n}\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache) {\n    getCacheInfo(cache).vars.forEach(function (rv) { return rv.attachCache(cache); });\n}\nexport function makeVar(value) {\n    var caches = new Set();\n    var listeners = new Set();\n    var rv = function (newValue) {\n        if (arguments.length > 0) {\n            if (value !== newValue) {\n                value = newValue;\n                caches.forEach(function (cache) {\n                    // Invalidate any fields with custom read functions that\n                    // consumed this variable, so query results involving those\n                    // fields will be recomputed the next time we read them.\n                    getCacheInfo(cache).dep.dirty(rv);\n                    // Broadcast changes to any caches that have previously read\n                    // from this variable.\n                    broadcast(cache);\n                });\n                // Finally, notify any listeners added via rv.onNextChange.\n                var oldListeners = Array.from(listeners);\n                listeners.clear();\n                oldListeners.forEach(function (listener) { return listener(value); });\n            }\n        }\n        else {\n            // When reading from the variable, obtain the current cache from\n            // context via cacheSlot. This isn't entirely foolproof, but it's\n            // the same system that powers varDep.\n            var cache = cacheSlot.getValue();\n            if (cache) {\n                attach(cache);\n                getCacheInfo(cache).dep(rv);\n            }\n        }\n        return value;\n    };\n    rv.onNextChange = function (listener) {\n        listeners.add(listener);\n        return function () {\n            listeners.delete(listener);\n        };\n    };\n    var attach = (rv.attachCache = function (cache) {\n        caches.add(cache);\n        getCacheInfo(cache).vars.add(rv);\n        return rv;\n    });\n    rv.forgetCache = function (cache) { return caches.delete(cache); };\n    return rv;\n}\nfunction broadcast(cache) {\n    if (cache.broadcastWatches) {\n        cache.broadcastWatches();\n    }\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACpC;AACA;AACA,OAAO,IAAIC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;AACjC,IAAIE,YAAY,GAAG,IAAIC,OAAO,CAAC,CAAC;AAChC,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,IAAIC,IAAI,GAAGJ,YAAY,CAACK,GAAG,CAACF,KAAK,CAAC;EAClC,IAAI,CAACC,IAAI,EAAE;IACPJ,YAAY,CAACM,GAAG,CAACH,KAAK,EAAGC,IAAI,GAAG;MAC5BG,IAAI,EAAE,IAAIC,GAAG,CAAC,CAAC;MACfX,GAAG,EAAEA,GAAG,CAAC;IACb,CAAE,CAAC;EACP;EACA,OAAOO,IAAI;AACf;AACA,OAAO,SAASK,WAAWA,CAACN,KAAK,EAAE;EAC/BD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACG,OAAO,CAAC,UAAUC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACF,WAAW,CAACN,KAAK,CAAC;EAAE,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,WAAWA,CAACT,KAAK,EAAE;EAC/BD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACG,OAAO,CAAC,UAAUC,EAAE,EAAE;IAAE,OAAOA,EAAE,CAACE,WAAW,CAACV,KAAK,CAAC;EAAE,CAAC,CAAC;AACrF;AACA,OAAO,SAASW,OAAOA,CAACC,KAAK,EAAE;EAC3B,IAAIC,MAAM,GAAG,IAAIR,GAAG,CAAC,CAAC;EACtB,IAAIS,SAAS,GAAG,IAAIT,GAAG,CAAC,CAAC;EACzB,IAAIG,EAAE,GAAG,SAAAA,CAAUO,QAAQ,EAAE;IACzB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACtB,IAAIL,KAAK,KAAKG,QAAQ,EAAE;QACpBH,KAAK,GAAGG,QAAQ;QAChBF,MAAM,CAACN,OAAO,CAAC,UAAUP,KAAK,EAAE;UAC5B;UACA;UACA;UACAD,YAAY,CAACC,KAAK,CAAC,CAACN,GAAG,CAACwB,KAAK,CAACV,EAAE,CAAC;UACjC;UACA;UACAW,SAAS,CAACnB,KAAK,CAAC;QACpB,CAAC,CAAC;QACF;QACA,IAAIoB,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACR,SAAS,CAAC;QACxCA,SAAS,CAACS,KAAK,CAAC,CAAC;QACjBH,YAAY,CAACb,OAAO,CAAC,UAAUiB,QAAQ,EAAE;UAAE,OAAOA,QAAQ,CAACZ,KAAK,CAAC;QAAE,CAAC,CAAC;MACzE;IACJ,CAAC,MACI;MACD;MACA;MACA;MACA,IAAIZ,KAAK,GAAGJ,SAAS,CAAC6B,QAAQ,CAAC,CAAC;MAChC,IAAIzB,KAAK,EAAE;QACP0B,MAAM,CAAC1B,KAAK,CAAC;QACbD,YAAY,CAACC,KAAK,CAAC,CAACN,GAAG,CAACc,EAAE,CAAC;MAC/B;IACJ;IACA,OAAOI,KAAK;EAChB,CAAC;EACDJ,EAAE,CAACmB,YAAY,GAAG,UAAUH,QAAQ,EAAE;IAClCV,SAAS,CAACc,GAAG,CAACJ,QAAQ,CAAC;IACvB,OAAO,YAAY;MACfV,SAAS,CAACe,MAAM,CAACL,QAAQ,CAAC;IAC9B,CAAC;EACL,CAAC;EACD,IAAIE,MAAM,GAAIlB,EAAE,CAACE,WAAW,GAAG,UAAUV,KAAK,EAAE;IAC5Ca,MAAM,CAACe,GAAG,CAAC5B,KAAK,CAAC;IACjBD,YAAY,CAACC,KAAK,CAAC,CAACI,IAAI,CAACwB,GAAG,CAACpB,EAAE,CAAC;IAChC,OAAOA,EAAE;EACb,CAAE;EACFA,EAAE,CAACF,WAAW,GAAG,UAAUN,KAAK,EAAE;IAAE,OAAOa,MAAM,CAACgB,MAAM,CAAC7B,KAAK,CAAC;EAAE,CAAC;EAClE,OAAOQ,EAAE;AACb;AACA,SAASW,SAASA,CAACnB,KAAK,EAAE;EACtB,IAAIA,KAAK,CAAC8B,gBAAgB,EAAE;IACxB9B,KAAK,CAAC8B,gBAAgB,CAAC,CAAC;EAC5B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}