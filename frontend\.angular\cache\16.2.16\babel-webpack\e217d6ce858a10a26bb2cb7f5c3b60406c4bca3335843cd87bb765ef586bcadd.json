{"ast": null, "code": "import { __assign } from \"tslib\";\nexport function createOperation(starting, operation) {\n  var context = __assign({}, starting);\n  var setContext = function (next) {\n    if (typeof next === \"function\") {\n      context = __assign(__assign({}, context), next(context));\n    } else {\n      context = __assign(__assign({}, context), next);\n    }\n  };\n  var getContext = function () {\n    return __assign({}, context);\n  };\n  Object.defineProperty(operation, \"setContext\", {\n    enumerable: false,\n    value: setContext\n  });\n  Object.defineProperty(operation, \"getContext\", {\n    enumerable: false,\n    value: getContext\n  });\n  return operation;\n}", "map": {"version": 3, "names": ["__assign", "createOperation", "starting", "operation", "context", "setContext", "next", "getContext", "Object", "defineProperty", "enumerable", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/utils/createOperation.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nexport function createOperation(starting, operation) {\n    var context = __assign({}, starting);\n    var setContext = function (next) {\n        if (typeof next === \"function\") {\n            context = __assign(__assign({}, context), next(context));\n        }\n        else {\n            context = __assign(__assign({}, context), next);\n        }\n    };\n    var getContext = function () { return (__assign({}, context)); };\n    Object.defineProperty(operation, \"setContext\", {\n        enumerable: false,\n        value: setContext,\n    });\n    Object.defineProperty(operation, \"getContext\", {\n        enumerable: false,\n        value: getContext,\n    });\n    return operation;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,SAASC,eAAeA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACjD,IAAIC,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAEE,QAAQ,CAAC;EACpC,IAAIG,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAC7B,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC5BF,OAAO,GAAGJ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEI,OAAO,CAAC,EAAEE,IAAI,CAACF,OAAO,CAAC,CAAC;IAC5D,CAAC,MACI;MACDA,OAAO,GAAGJ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEI,OAAO,CAAC,EAAEE,IAAI,CAAC;IACnD;EACJ,CAAC;EACD,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAQP,QAAQ,CAAC,CAAC,CAAC,EAAEI,OAAO,CAAC;EAAG,CAAC;EAChEI,MAAM,CAACC,cAAc,CAACN,SAAS,EAAE,YAAY,EAAE;IAC3CO,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAEN;EACX,CAAC,CAAC;EACFG,MAAM,CAACC,cAAc,CAACN,SAAS,EAAE,YAAY,EAAE;IAC3CO,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAEJ;EACX,CAAC,CAAC;EACF,OAAOJ,SAAS;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}