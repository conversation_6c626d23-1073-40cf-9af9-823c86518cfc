{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { LoginComponent } from './login.component';\nimport * as i0 from \"@angular/core\";\nexport class LoginModule {\n  static {\n    this.ɵfac = function LoginModule_Factory(t) {\n      return new (t || LoginModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LoginModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, LoginRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LoginModule, {\n    declarations: [LoginComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, LoginRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "LoginRoutingModule", "LoginComponent", "LoginModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\login\\login.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { LoginRoutingModule } from './login-routing.module';\r\nimport { LoginComponent } from './login.component';\r\n\r\n@NgModule({\r\n  declarations: [LoginComponent],\r\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, LoginRoutingModule],\r\n})\r\nexport class LoginModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;;AAMlD,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAFZL,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,kBAAkB;IAAA;EAAA;;;2EAEjEE,WAAW;IAAAC,YAAA,GAHPF,cAAc;IAAAG,OAAA,GACnBP,YAAY,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}