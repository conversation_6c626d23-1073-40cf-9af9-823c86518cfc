{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProjectListComponent\n}, {\n  path: 'detail/:id',\n  component: ProjectDetailComponent\n}, {\n  path: 'submit/:id',\n  component: ProjectSubmissionComponent\n}];\nexport class ProjectsRoutingModule {\n  static {\n    this.ɵfac = function ProjectsRoutingModule_Factory(t) {\n      return new (t || ProjectsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProjectsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProjectsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ProjectListComponent", "ProjectDetailComponent", "ProjectSubmissionComponent", "routes", "path", "component", "ProjectsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\projects\\projects-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ProjectListComponent } from './project-list/project-list.component';\r\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\r\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ProjectListComponent },\r\n  { path: 'detail/:id', component: ProjectDetailComponent },\r\n  { path: 'submit/:id', component: ProjectSubmissionComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ProjectsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,0BAA0B,QAAQ,mDAAmD;;;AAE9F,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEL;AAAoB,CAAE,EAC7C;EAAEI,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEJ;AAAsB,CAAE,EACzD;EAAEG,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEH;AAA0B,CAAE,CAC9D;AAMD,OAAM,MAAOI,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBP,YAAY,CAACQ,QAAQ,CAACJ,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEXO,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFtBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}