{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SignupRoutingModule } from './signup-routing.module';\nimport { SignupComponent } from './signup.component';\nimport * as i0 from \"@angular/core\";\nexport class SignupModule {\n  static {\n    this.ɵfac = function SignupModule_Factory(t) {\n      return new (t || SignupModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SignupModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, SignupRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SignupModule, {\n    declarations: [SignupComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, SignupRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "SignupRoutingModule", "SignupComponent", "SignupModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\signup\\signup.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { SignupRoutingModule } from './signup-routing.module';\r\nimport { SignupComponent } from './signup.component';\r\n\r\n@NgModule({\r\n  declarations: [SignupComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    SignupRoutingModule,\r\n  ],\r\n})\r\nexport class SignupModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;;AAWpD,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBANrBL,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,mBAAmB;IAAA;EAAA;;;2EAGVE,YAAY;IAAAC,YAAA,GARRF,eAAe;IAAAG,OAAA,GAE5BP,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}