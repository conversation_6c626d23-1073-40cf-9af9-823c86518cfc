{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IncomingCallComponent } from './incoming-call.component';\nimport * as i0 from \"@angular/core\";\nexport class IncomingCallModule {\n  static {\n    this.ɵfac = function IncomingCallModule_Factory(t) {\n      return new (t || IncomingCallModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IncomingCallModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IncomingCallModule, {\n    declarations: [IncomingCallComponent],\n    imports: [CommonModule],\n    exports: [IncomingCallComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IncomingCallComponent", "IncomingCallModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\incoming-call\\incoming-call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IncomingCallComponent } from './incoming-call.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    IncomingCallComponent\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    IncomingCallComponent\r\n  ]\r\n})\r\nexport class IncomingCallModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,2BAA2B;;AAajE,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAN3BF,YAAY;IAAA;EAAA;;;2EAMHE,kBAAkB;IAAAC,YAAA,GAT3BF,qBAAqB;IAAAG,OAAA,GAGrBJ,YAAY;IAAAK,OAAA,GAGZJ,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}