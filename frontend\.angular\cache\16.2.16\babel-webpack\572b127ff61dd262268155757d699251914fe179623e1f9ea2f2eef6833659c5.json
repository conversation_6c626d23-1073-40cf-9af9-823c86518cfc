{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nexport const noguarduserGuard = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n  if (authService.userLoggedIn() == false) {\n    return true;\n  } else {\n    router.navigate(['/users']);\n    return false;\n  }\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthuserService", "noguarduser<PERSON><PERSON>", "route", "state", "authService", "router", "userLoggedIn", "navigate"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\guards\\noguarduser.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\n\r\nexport const noguarduserGuard: CanActivateFn = (route, state) => {\r\n   const authService=inject(AuthuserService)\r\n     const router= inject(Router)\r\n     if(authService.userLoggedIn()==false){\r\n     return true;\r\n   }else \r\n   {\r\n     router.navigate(['/users']);\r\n     return false;\r\n   }\r\n  \r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,eAAe,QAAQ,mCAAmC;AAEnE,OAAO,MAAMC,gBAAgB,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EAC7D,MAAMC,WAAW,GAACN,MAAM,CAACE,eAAe,CAAC;EACvC,MAAMK,MAAM,GAAEP,MAAM,CAACC,MAAM,CAAC;EAC5B,IAAGK,WAAW,CAACE,YAAY,EAAE,IAAE,KAAK,EAAC;IACrC,OAAO,IAAI;GACZ,MACD;IACED,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3B,OAAO,KAAK;;AAGjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}