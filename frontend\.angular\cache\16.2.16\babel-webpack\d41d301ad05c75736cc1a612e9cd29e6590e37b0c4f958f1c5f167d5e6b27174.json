{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../ai-chat/ai-chat.component\";\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1,\n    \"bg-secondary\": a2\n  };\n};\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"bi-mortarboard-fill\": a0,\n    \"bi-briefcase-fill\": a1,\n    \"bi-person-fill\": a2\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-success bg-opacity-10 text-success\": a0,\n    \"bg-primary bg-opacity-10 text-primary\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"bi-person-fill-gear\": a0,\n    \"bi-person\": a1\n  };\n};\nfunction EquipeDetailComponent_div_0_div_122_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 88)(3, \"div\", 89);\n    i0.ɵɵelement(4, \"i\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 91);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 92)(9, \"span\", 93);\n    i0.ɵɵelement(10, \"i\", 90);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"small\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(14, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_122_div_4_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const membre_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.removeMembreFromEquipe(membre_r8._id));\n    });\n    i0.ɵɵelement(15, \"i\", 96);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const membre_r8 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c1, ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\", ctx_r5.getUserProfession(membre_r8.user) === \"professeur\", !ctx_r5.getUserProfession(membre_r8.user)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getUserName(membre_r8.user), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c2, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c3, membre_r8.role === \"admin\", membre_r8.role === \"membre\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", membre_r8.role === \"admin\" ? \"Administrateur\" : \"Membre\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getUserProfession(membre_r8.user) === \"etudiant\" ? \"\\u00C9tudiant\" : ctx_r5.getUserProfession(membre_r8.user) === \"professeur\" ? \"Professeur\" : \"Utilisateur\");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 98);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_10_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r15._id || user_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r15.firstName || \"\", \" \", user_r15.lastName || user_r15.name || user_r15.id, \" \", user_r15.email ? \"- \" + user_r15.email : \"\", \" \", user_r15.profession ? \"(\" + (user_r15.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"label\", 101);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 102, 103)(6, \"option\", 104);\n    i0.ɵɵtext(7, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_div_122_div_10_option_8_Template, 2, 5, \"option\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 100)(10, \"label\", 106);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 107)(13, \"div\", 108);\n    i0.ɵɵelement(14, \"input\", 109, 110);\n    i0.ɵɵelementStart(16, \"label\", 111);\n    i0.ɵɵelement(17, \"i\", 112);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 108);\n    i0.ɵɵelement(20, \"input\", 113, 114);\n    i0.ɵɵelementStart(22, \"label\", 115);\n    i0.ɵɵelement(23, \"i\", 116);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 117)(26, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_div_122_div_10_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const _r11 = i0.ɵɵreference(5);\n      const _r13 = i0.ɵɵreference(15);\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      ctx_r16.addMembre(_r11.value, _r13.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r11.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 119);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(5);\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r11.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 43)(2, \"div\", 78)(3, \"div\", 79);\n    i0.ɵɵtemplate(4, EquipeDetailComponent_div_0_div_122_div_4_Template, 16, 21, \"div\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 81)(6, \"h5\", 82);\n    i0.ɵɵelement(7, \"i\", 83);\n    i0.ɵɵtext(8, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, EquipeDetailComponent_div_0_div_122_div_9_Template, 4, 0, \"div\", 84);\n    i0.ɵɵtemplate(10, EquipeDetailComponent_div_0_div_122_div_10_Template, 29, 2, \"div\", 85);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.teamMembers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"i\", 98);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \" Aucun utilisateur disponible. Veuillez d'abord cr\\u00E9er des utilisateurs. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r24._id || user_r24.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate4(\" \", user_r24.firstName || \"\", \" \", user_r24.lastName || user_r24.name || user_r24.id, \" \", user_r24.email ? \"- \" + user_r24.email : \"\", \" \", user_r24.profession ? \"(\" + (user_r24.profession === \"etudiant\" ? \"\\u00C9tudiant\" : \"Professeur\") + \")\" : \"\", \" \");\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"label\", 124);\n    i0.ɵɵtext(3, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 125, 126)(6, \"option\", 104);\n    i0.ɵɵtext(7, \" S\\u00E9lectionnez un utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template, 2, 5, \"option\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 100)(10, \"label\", 127);\n    i0.ɵɵtext(11, \"R\\u00F4le dans l'\\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 107)(13, \"div\", 108);\n    i0.ɵɵelement(14, \"input\", 128, 129);\n    i0.ɵɵelementStart(16, \"label\", 130);\n    i0.ɵɵelement(17, \"i\", 112);\n    i0.ɵɵtext(18, \" Membre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 108);\n    i0.ɵɵelement(20, \"input\", 131, 132);\n    i0.ɵɵelementStart(22, \"label\", 133);\n    i0.ɵɵelement(23, \"i\", 116);\n    i0.ɵɵtext(24, \" Admin \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 117)(26, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_ng_template_123_div_14_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const _r20 = i0.ɵɵreference(5);\n      const _r22 = i0.ɵɵreference(15);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      ctx_r25.addMembre(_r20.value, _r22.checked ? \"membre\" : \"admin\");\n      return i0.ɵɵresetView(_r20.value = \"\");\n    });\n    i0.ɵɵelement(27, \"i\", 119);\n    i0.ɵɵtext(28, \" Ajouter \\u00E0 l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r20 = i0.ɵɵreference(5);\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.availableUsers);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"disabled\", !_r20.value);\n  }\n}\nfunction EquipeDetailComponent_div_0_ng_template_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 78)(2, \"div\", 121)(3, \"div\", 122);\n    i0.ɵɵelement(4, \"i\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 94);\n    i0.ɵɵtext(6, \"Aucun membre dans cette \\u00E9quipe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 94);\n    i0.ɵɵtext(8, \" Ajoutez des membres \\u00E0 l'\\u00E9quipe en utilisant le formulaire ci-contre. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 81)(10, \"h5\", 82);\n    i0.ɵɵelement(11, \"i\", 83);\n    i0.ɵɵtext(12, \" Ajouter un membre \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, EquipeDetailComponent_div_0_ng_template_123_div_13_Template, 4, 0, \"div\", 84);\n    i0.ɵɵtemplate(14, EquipeDetailComponent_div_0_ng_template_123_div_14_Template, 29, 2, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.availableUsers.length > 0);\n  }\n}\nfunction EquipeDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"div\", 4)(3, \"div\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelement(6, \"div\", 8)(7, \"div\", 8)(8, \"div\", 8)(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10);\n    i0.ɵɵelement(19, \"div\", 11)(20, \"div\", 12);\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15)(24, \"h1\", 16);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\", 17);\n    i0.ɵɵtext(27, \" Gestion et collaboration d'\\u00E9quipe moderne \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19);\n    i0.ɵɵelement(30, \"i\", 20);\n    i0.ɵɵelementStart(31, \"span\", 21);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"small\", 22);\n    i0.ɵɵtext(34, \"Membres\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 19);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementStart(37, \"span\", 21);\n    i0.ɵɵtext(38, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"small\", 22);\n    i0.ɵɵtext(40, \"T\\u00E2ches\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 19);\n    i0.ɵɵelement(42, \"i\", 24);\n    i0.ɵɵelementStart(43, \"span\", 25);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"small\", 22);\n    i0.ɵɵtext(46, \"Cr\\u00E9\\u00E9e le\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 26)(48, \"h4\", 27);\n    i0.ɵɵelement(49, \"i\", 28);\n    i0.ɵɵtext(50, \"Actions rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 29)(52, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.navigateToTasks());\n    });\n    i0.ɵɵelement(53, \"i\", 31);\n    i0.ɵɵtext(54, \" G\\u00E9rer les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.navigateToEditEquipe());\n    });\n    i0.ɵɵelement(56, \"i\", 33);\n    i0.ɵɵtext(57, \" Modifier l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 34)(59, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.navigateToEquipeList());\n    });\n    i0.ɵɵelement(60, \"i\", 36);\n    i0.ɵɵtext(61, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_0_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.deleteEquipe());\n    });\n    i0.ɵɵelement(63, \"i\", 38);\n    i0.ɵɵtext(64, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(65, \"div\", 39)(66, \"div\", 40)(67, \"div\", 41)(68, \"div\", 42)(69, \"div\", 43)(70, \"div\", 44)(71, \"div\", 45);\n    i0.ɵɵelement(72, \"i\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"h3\", 47);\n    i0.ɵɵtext(74, \"\\u00C0 propos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"p\", 48);\n    i0.ɵɵtext(76, \" D\\u00E9tails et informations sur l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 49)(78, \"div\", 50)(79, \"h4\", 51);\n    i0.ɵɵtext(80, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 52);\n    i0.ɵɵelement(82, \"i\", 53);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"div\", 54)(85, \"p\", 55);\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 56)(88, \"span\", 57);\n    i0.ɵɵelement(89, \"i\", 58);\n    i0.ɵɵtext(90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"span\", 59);\n    i0.ɵɵelement(92, \"i\", 60);\n    i0.ɵɵtext(93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\", 61);\n    i0.ɵɵelement(95, \"i\", 62);\n    i0.ɵɵtext(96, \" Gestion de projet \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(97, \"div\", 39)(98, \"div\", 40)(99, \"div\", 41)(100, \"div\", 63)(101, \"div\", 64)(102, \"h3\", 65)(103, \"div\", 66);\n    i0.ɵɵelement(104, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(105, \" Assistant IA Gemini \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"span\", 68);\n    i0.ɵɵelement(107, \"i\", 69);\n    i0.ɵɵtext(108, \" G\\u00E9n\\u00E9ration de t\\u00E2ches intelligente \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 42);\n    i0.ɵɵelement(110, \"app-ai-chat\", 70);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(111, \"div\", 39)(112, \"div\", 40)(113, \"div\", 41)(114, \"div\", 71)(115, \"h3\", 65)(116, \"div\", 72);\n    i0.ɵɵelement(117, \"i\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(118, \" Membres de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(119, \"span\", 74);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"div\", 42);\n    i0.ɵɵtemplate(122, EquipeDetailComponent_div_0_div_122_Template, 11, 3, \"div\", 75);\n    i0.ɵɵtemplate(123, EquipeDetailComponent_div_0_ng_template_123_Template, 15, 2, \"ng-template\", null, 76, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const _r3 = i0.ɵɵreference(124);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.name, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.formatDate(ctx_r0.equipe.createdAt));\n    i0.ɵɵadvance(39);\n    i0.ɵɵtextInterpolate1(\" Admin: \", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.equipe.admin) || ctx_r0.equipe.admin : \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.equipe.description || \"Aucune description disponible pour cette \\u00E9quipe.\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, \" membres \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Cr\\u00E9\\u00E9e le \", ctx_r0.formatDate(ctx_r0.equipe.createdAt), \" \");\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"team\", ctx_r0.equipe);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teamMembers.length || 0, \" membres \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teamMembers && ctx_r0.teamMembers.length > 0)(\"ngIfElse\", _r3);\n  }\n}\nfunction EquipeDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 135)(2, \"div\", 136)(3, \"div\", 137)(4, \"div\", 138);\n    i0.ɵɵelement(5, \"i\", 139);\n    i0.ɵɵelementStart(6, \"div\", 140);\n    i0.ɵɵtext(7, \" \\u00C9quipe non trouv\\u00E9e ou en cours de chargement... \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.navigateToEquipeList());\n    });\n    i0.ɵɵelement(9, \"i\", 142);\n    i0.ɵɵtext(10, \" Retour \\u00E0 la liste des \\u00E9quipes \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class EquipeDetailComponent {\n  constructor(equipeService, userService,\n  // TODO: Will be used when implementing real user API calls\n  route, router) {\n    this.equipeService = equipeService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.equipe = null;\n    this.loading = false;\n    this.error = null;\n    this.equipeId = null;\n    this.newMembre = {\n      id: '',\n      role: 'membre'\n    };\n    this.availableUsers = [];\n    this.memberNames = {}; // Map pour stocker les noms des membres\n    this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails\n  }\n\n  ngOnInit() {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n  // Méthode pour charger tous les utilisateurs\n  loadUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers = [{\n      _id: 'user1',\n      username: 'john_doe',\n      email: '<EMAIL>',\n      role: 'admin',\n      isActive: true\n    }, {\n      _id: 'user2',\n      username: 'jane_smith',\n      email: '<EMAIL>',\n      role: 'student',\n      isActive: true\n    }, {\n      _id: 'user3',\n      username: 'bob_wilson',\n      email: '<EMAIL>',\n      role: 'teacher',\n      isActive: true\n    }];\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map(m => m.user);\n        this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));\n      } else {\n        this.availableUsers = mockUsers;\n      }\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames() {\n    if (!this.equipe || !this.equipe.members) return;\n    this.equipe.members.forEach(membreId => {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId) {\n    return this.memberNames[membreId] || membreId;\n  }\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId) {\n    if (!userId) {\n      return 'Non défini';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId) {\n    if (!userId) {\n      return '';\n    }\n    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n  loadEquipe(id) {\n    this.loading = true;\n    this.error = null;\n    this.equipeService.getEquipe(id).subscribe({\n      next: data => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n        // Mettre à jour les noms des membres\n        if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {\n          this.updateMemberNames();\n        }\n        this.loading = false;\n      },\n      error: error => {\n        console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n        this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      }\n    });\n  }\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId) {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: members => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des détails des membres:', error);\n      }\n    });\n  }\n  navigateToEditEquipe() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n  navigateToEquipeList() {\n    this.router.navigate(['/equipes/liste']);\n  }\n  navigateToTasks() {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n  // Méthode pour formater les dates\n  formatDate(date) {\n    if (!date) {\n      return 'N/A';\n    }\n    try {\n      let dateObj;\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId, role) {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some(m => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n    // Créer l'objet membre avec le rôle spécifié\n    const membre = {\n      id: userId,\n      role: role || 'membre'\n    };\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: response => {\n        console.log(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`, response);\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId);\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId);\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'ajout de l'utilisateur comme membre:\", error);\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      }\n    });\n  }\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers() {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe() {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n  removeMembreFromEquipe(membreId) {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n    console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);\n    if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({\n        next: response => {\n          console.log(`Utilisateur \"${userName}\" retiré avec succès de l'équipe:`, response);\n          this.loading = false;\n          // Afficher un message de succès\n          alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n          // Recharger les membres de l'équipe\n          this.loadTeamMembers(this.equipeId);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(this.equipeId);\n          // Mettre à jour la liste des utilisateurs disponibles\n          this.updateAvailableUsers();\n        },\n        error: error => {\n          console.error(`Erreur lors du retrait de l'utilisateur \"${userName}\":`, error);\n          this.loading = false;\n          this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  deleteEquipe() {\n    console.log('Méthode deleteEquipe appelée');\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`)) {\n      console.log('Confirmation acceptée, suppression en cours...');\n      this.loading = true;\n      this.error = null;\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: error => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        }\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n  static {\n    this.ɵfac = function EquipeDetailComponent_Factory(t) {\n      return new (t || EquipeDetailComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipeDetailComponent,\n      selectors: [[\"app-equipe-detail\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\", 4, \"ngIf\"], [\"class\", \"container-fluid py-5 bg-light\", 4, \"ngIf\"], [1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-7xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-0\"], [1, \"lg:col-span-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"p-6\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\", \"tracking-wide\"], [1, \"text-white/80\", \"text-sm\", \"mb-6\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"bg-white/20\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-4\", \"text-white\", \"text-center\"], [1, \"fas\", \"fa-users\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-xl\", \"font-bold\", \"block\"], [1, \"text-white/80\"], [1, \"fas\", \"fa-tasks\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"fas\", \"fa-calendar-check\", \"text-2xl\", \"mb-2\", \"block\"], [1, \"text-sm\", \"font-bold\", \"block\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"p-6\", \"flex\", \"flex-col\", \"justify-center\"], [1, \"text-lg\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-bolt\", \"mr-2\"], [1, \"space-y-3\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"dark:from-[#00f7ff]\", \"dark:to-[#4f5fad]\", \"text-white\", \"px-4\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", 3, \"click\"], [1, \"fas\", \"fa-tasks\", \"mr-2\"], [1, \"w-full\", \"bg-[#4f5fad]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#4f5fad]\", \"dark:text-[#00f7ff]\", \"px-4\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"grid\", \"grid-cols-2\", \"gap-2\"], [1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-4\", \"py-2\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-1\"], [1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-4\", \"py-2\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-1\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"overflow-hidden\", \"hover-card\"], [1, \"card-body\", \"p-0\"], [1, \"row\", \"g-0\"], [1, \"col-md-3\", \"bg-primary\", \"text-white\", \"p-4\", \"d-flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"text-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"mb-3\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-1\"], [1, \"mb-2\"], [1, \"mb-0\", \"text-white-50\"], [1, \"col-md-9\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-primary\", \"mb-0\"], [1, \"badge\", \"bg-light\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-person-fill-gear\", \"me-1\"], [1, \"description-box\", \"p-3\", \"bg-light\", \"rounded-4\", \"mb-4\"], [1, \"lead\", \"mb-0\"], [1, \"d-flex\", \"flex-wrap\", \"gap-2\", \"mt-4\"], [1, \"badge\", \"bg-primary\", \"bg-opacity-10\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-people-fill\", \"me-1\"], [1, \"badge\", \"bg-success\", \"bg-opacity-10\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-calendar-check\", \"me-1\"], [1, \"badge\", \"bg-info\", \"bg-opacity-10\", \"text-info\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-kanban\", \"me-1\"], [1, \"card-header\", \"border-0\", \"py-4\", 2, \"background\", \"linear-gradient(45deg, #8e2de2, #4a00e0)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\", \"text-white\", \"d-flex\", \"align-items-center\"], [1, \"icon-circle\", \"bg-white\", \"text-primary\", \"me-3\"], [1, \"bi\", \"bi-robot\"], [1, \"badge\", \"bg-white\", \"text-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-magic\", \"me-1\"], [3, \"team\"], [1, \"card-header\", \"border-0\", \"py-4\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background\", \"linear-gradient(45deg, #11998e, #38ef7d)\"], [1, \"icon-circle\", \"bg-white\", \"text-success\", \"me-3\"], [1, \"bi\", \"bi-people-fill\"], [1, \"badge\", \"bg-white\", \"text-success\", \"rounded-pill\", \"px-3\", \"py-2\"], [\"class\", \"p-0\", 4, \"ngIf\", \"ngIfElse\"], [\"noMembers\", \"\"], [1, \"p-0\"], [1, \"col-md-8\"], [1, \"member-grid\", \"p-4\"], [\"class\", \"member-card mb-3 p-3 rounded-4 shadow-sm transition\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-4\", \"bg-light\", \"p-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-4\", \"text-success\"], [1, \"bi\", \"bi-person-plus-fill\", \"me-2\"], [\"class\", \"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"add-member-form\", 4, \"ngIf\"], [1, \"member-card\", \"mb-3\", \"p-3\", \"rounded-4\", \"shadow-sm\", \"transition\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\"], [1, \"d-flex\", \"align-items-center\"], [1, \"member-avatar\", \"rounded-circle\", \"text-white\", \"me-3\", 3, \"ngClass\"], [1, \"bi\", 3, \"ngClass\"], [1, \"mb-0\", \"fw-bold\"], [1, \"d-flex\", \"align-items-center\", \"mt-1\"], [1, \"badge\", \"rounded-pill\", \"me-2\", 3, \"ngClass\"], [1, \"text-muted\"], [\"title\", \"Retirer de l'\\u00E9quipe\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", \"rounded-circle\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"alert\", \"alert-info\", \"border-0\", \"rounded-4\", \"shadow-sm\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle-fill\", \"fs-4\", \"me-3\", \"text-primary\"], [1, \"add-member-form\"], [1, \"mb-3\"], [\"for\", \"userSelect\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect\", \"\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"roleSelect\", 1, \"form-label\", \"fw-medium\"], [1, \"d-flex\", \"gap-2\"], [1, \"form-check\", \"flex-grow-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleMembre\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre\", \"\"], [\"for\", \"roleMembre\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person\", \"d-block\", \"fs-4\", \"mb-1\"], [\"type\", \"radio\", \"name\", \"roleRadio\", \"id\", \"roleAdmin\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin\", \"\"], [\"for\", \"roleAdmin\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"bi\", \"bi-person-fill-gear\", \"d-block\", \"fs-4\", \"mb-1\"], [1, \"d-grid\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"rounded-4\", \"py-2\", \"shadow-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [3, \"value\"], [1, \"text-center\", \"py-5\"], [1, \"empty-state-icon\", \"mb-4\"], [1, \"bi\", \"bi-people\", \"fs-1\", \"text-muted\"], [\"for\", \"userSelect2\", 1, \"form-label\", \"fw-medium\"], [\"id\", \"userSelect2\", 1, \"form-select\", \"border-0\", \"shadow-sm\", \"rounded-4\", \"py-2\"], [\"userSelect2\", \"\"], [\"for\", \"roleSelect2\", 1, \"form-label\", \"fw-medium\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleMembre2\", \"value\", \"membre\", \"checked\", \"\", 1, \"form-check-input\"], [\"roleMembre2\", \"\"], [\"for\", \"roleMembre2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [\"type\", \"radio\", \"name\", \"roleRadio2\", \"id\", \"roleAdmin2\", \"value\", \"admin\", 1, \"form-check-input\"], [\"roleAdmin2\", \"\"], [\"for\", \"roleAdmin2\", 1, \"form-check-label\", \"w-100\", \"p-2\", \"border\", \"rounded-4\", \"text-center\"], [1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"text-center\"], [1, \"alert\", \"alert-warning\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\", \"p-4\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-1\", \"me-4\", \"text-warning\"], [1, \"fs-5\"], [1, \"btn\", \"btn-outline-primary\", \"rounded-pill\", \"mt-4\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"]],\n      template: function EquipeDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, EquipeDetailComponent_div_0_Template, 125, 11, \"div\", 0);\n          i0.ɵɵtemplate(1, EquipeDetailComponent_div_1_Template, 11, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.equipe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.equipe);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i6.AiChatComponent],\n      styles: [\"\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\nsummary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCIiwiZmlsZSI6ImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBlcXVpcGUtZGV0YWlsICovXHJcbi5jdXJzb3ItcG9pbnRlciB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5zdW1tYXJ5OmhvdmVyIHtcclxuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtZGV0YWlsL2VxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCO0FBQ0Esd2dCQUF3Z0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZXF1aXBlLWRldGFpbCAqL1xyXG4uY3Vyc29yLXBvaW50ZXIge1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuc3VtbWFyeTpob3ZlciB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\", \"\\n\\n  .bg-gradient-primary[_ngcontent-%COMP%] {\\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\\n  }\\n\\n  .bg-gradient-light[_ngcontent-%COMP%] {\\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\\n  }\\n\\n  \\n\\n  .transition[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  \\n\\n  .hover-card[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .hover-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-5px);\\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-card[_ngcontent-%COMP%] {\\n    background-color: white;\\n    transition: all 0.3s ease;\\n    border-left: 4px solid transparent;\\n  }\\n\\n  .member-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-3px);\\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\\n  }\\n\\n  \\n\\n  .member-avatar[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .icon-circle[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 1.2rem;\\n  }\\n\\n  \\n\\n  .description-box[_ngcontent-%COMP%] {\\n    border-left: 4px solid #007bff;\\n  }\\n\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n    transition: all 0.3s ease;\\n  }\\n\\n  .btn[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-2px);\\n  }\\n\\n  \\n\\n  .badge[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n  }\\n\\n  \\n\\n  .form-select[_ngcontent-%COMP%], .form-control[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\\n  }\\n\\n  \\n\\n  .empty-state-icon[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n    margin: 0 auto;\\n    background-color: #f8f9fa;\\n    border-radius: 50%;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    font-size: 2rem;\\n    color: #adb5bd;\\n  }\\n\\n  \\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .form-check-input[_ngcontent-%COMP%]:checked    + .form-check-label[_ngcontent-%COMP%] {\\n    background-color: rgba(13, 110, 253, 0.1);\\n    border-color: #007bff;\\n  }\\n\\n  \\n\\n  .rounded-4[_ngcontent-%COMP%] {\\n    border-radius: 0.75rem !important;\\n  }\\n\\n  \\n\\n  .member-grid[_ngcontent-%COMP%] {\\n    max-height: 500px;\\n    overflow-y: auto;\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "EquipeDetailComponent_div_0_div_122_div_4_Template_button_click_14_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "membre_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "removeMembreFromEquipe", "_id", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "ctx_r5", "getUserProfession", "user", "_c1", "ɵɵtextInterpolate1", "getUserName", "ɵɵpureFunction2", "_c2", "role", "_c3", "ɵɵtextInterpolate", "user_r15", "id", "ɵɵtextInterpolate4", "firstName", "lastName", "name", "email", "profession", "ɵɵtemplate", "EquipeDetailComponent_div_0_div_122_div_10_option_8_Template", "EquipeDetailComponent_div_0_div_122_div_10_Template_button_click_26_listener", "_r17", "_r11", "ɵɵreference", "_r13", "ctx_r16", "addMembre", "value", "checked", "ctx_r7", "availableUsers", "EquipeDetailComponent_div_0_div_122_div_4_Template", "EquipeDetailComponent_div_0_div_122_div_9_Template", "EquipeDetailComponent_div_0_div_122_div_10_Template", "ctx_r2", "teamMembers", "length", "user_r24", "EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template", "EquipeDetailComponent_div_0_ng_template_123_div_14_Template_button_click_26_listener", "_r26", "_r20", "_r22", "ctx_r25", "ctx_r19", "EquipeDetailComponent_div_0_ng_template_123_div_13_Template", "EquipeDetailComponent_div_0_ng_template_123_div_14_Template", "ctx_r4", "EquipeDetailComponent_div_0_Template_button_click_52_listener", "_r28", "ctx_r27", "navigateToTasks", "EquipeDetailComponent_div_0_Template_button_click_55_listener", "ctx_r29", "navigateToEditEquipe", "EquipeDetailComponent_div_0_Template_button_click_59_listener", "ctx_r30", "navigateToEquipeList", "EquipeDetailComponent_div_0_Template_button_click_62_listener", "ctx_r31", "deleteEquipe", "EquipeDetailComponent_div_0_div_122_Template", "EquipeDetailComponent_div_0_ng_template_123_Template", "ɵɵtemplateRefExtractor", "ctx_r0", "equipe", "members", "formatDate", "createdAt", "admin", "description", "_r3", "EquipeDetailComponent_div_1_Template_button_click_8_listener", "_r33", "ctx_r32", "EquipeDetailComponent", "constructor", "equipeService", "userService", "route", "router", "loading", "error", "equipeId", "newMembre", "memberNames", "ngOnInit", "snapshot", "paramMap", "get", "loadUsers", "loadEquipe", "mockUsers", "username", "isActive", "setTimeout", "allUsers", "console", "log", "memberUserIds", "map", "m", "filter", "includes", "updateMemberNames", "for<PERSON>ach", "membreId", "find", "u", "getMembreName", "userId", "getEquipe", "subscribe", "next", "data", "loadTeamMembers", "teamId", "getTeamMembers", "navigate", "date", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "toLocaleDateString", "day", "month", "year", "isAlreadyMember", "some", "alert", "membre", "userName", "<PERSON><PERSON><PERSON>", "addMembreToEquipe", "response", "updateAvailableUsers", "confirm", "message", "ɵɵdirectiveInject", "i1", "EquipeService", "i2", "AuthService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "EquipeDetailComponent_Template", "rf", "ctx", "EquipeDetailComponent_div_0_Template", "EquipeDetailComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-detail\\equipe-detail.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipe-detail\\equipe-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { EquipeService } from 'src/app/services/equipe.service';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Membre } from 'src/app/models/membre.model';\r\nimport { User } from 'src/app/models/user.model';\r\n@Component({\r\n  selector: 'app-equipe-detail',\r\n  templateUrl: './equipe-detail.component.html',\r\n  styleUrls: ['./equipe-detail.component.css'],\r\n})\r\nexport class EquipeDetailComponent implements OnInit {\r\n  equipe: Equipe | null = null;\r\n  loading = false;\r\n  error: string | null = null;\r\n  equipeId: string | null = null;\r\n  newMembre: any = { id: '', role: 'membre' };\r\n  availableUsers: User[] = [];\r\n  memberNames: { [key: string]: string } = {}; // Map pour stocker les noms des membres\r\n  teamMembers: any[] = []; // Liste des membres de l'équipe avec leurs détails\r\n\r\n  constructor(\r\n    private equipeService: EquipeService,\r\n    private userService: AuthService, // TODO: Will be used when implementing real user API calls\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.equipeId = this.route.snapshot.paramMap.get('id');\r\n\r\n    // Charger tous les utilisateurs disponibles\r\n    this.loadUsers();\r\n\r\n    if (this.equipeId) {\r\n      this.loadEquipe(this.equipeId);\r\n    } else {\r\n      this.error = \"ID d'équipe non spécifié\";\r\n    }\r\n  }\r\n\r\n  // Méthode pour charger tous les utilisateurs\r\n  loadUsers(): void {\r\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\r\n    // Pour l'instant, utiliser des données mockées\r\n    const mockUsers: User[] = [\r\n      {\r\n        _id: 'user1',\r\n        username: 'john_doe',\r\n        email: '<EMAIL>',\r\n        role: 'admin',\r\n        isActive: true,\r\n      },\r\n      {\r\n        _id: 'user2',\r\n        username: 'jane_smith',\r\n        email: '<EMAIL>',\r\n        role: 'student',\r\n        isActive: true,\r\n      },\r\n      {\r\n        _id: 'user3',\r\n        username: 'bob_wilson',\r\n        email: '<EMAIL>',\r\n        role: 'teacher',\r\n        isActive: true,\r\n      },\r\n    ];\r\n\r\n    // Simuler un délai d'API\r\n    setTimeout(() => {\r\n      // Stocker tous les utilisateurs pour la recherche de noms\r\n      const allUsers = [...mockUsers];\r\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\r\n\r\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\r\n      if (this.teamMembers && this.teamMembers.length > 0) {\r\n        const memberUserIds = this.teamMembers.map((m) => m.user);\r\n        this.availableUsers = mockUsers.filter(\r\n          (user) => !memberUserIds.includes(user._id || user.id || '')\r\n        );\r\n      } else {\r\n        this.availableUsers = mockUsers;\r\n      }\r\n\r\n      console.log('Utilisateurs disponibles:', this.availableUsers);\r\n\r\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\r\n      if (this.equipe && this.equipe.members) {\r\n        this.updateMemberNames();\r\n      }\r\n    }, 500);\r\n  }\r\n\r\n  // Méthode pour mettre à jour les noms des membres\r\n  updateMemberNames(): void {\r\n    if (!this.equipe || !this.equipe.members) return;\r\n\r\n    this.equipe.members.forEach((membreId) => {\r\n      const user = this.availableUsers.find(\r\n        (u) => u._id === membreId || u.id === membreId\r\n      );\r\n      if (user && user.name) {\r\n        this.memberNames[membreId] = user.name;\r\n      } else {\r\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\r\n        // TODO: Implémenter getUser dans AuthuserService\r\n        // Pour l'instant, utiliser l'ID comme nom par défaut\r\n        this.memberNames[membreId] = membreId;\r\n      }\r\n    });\r\n  }\r\n\r\n  // Méthode pour obtenir le nom d'un membre\r\n  getMembreName(membreId: string): string {\r\n    return this.memberNames[membreId] || membreId;\r\n  }\r\n\r\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\r\n  getUserName(userId: string | undefined): string {\r\n    if (!userId) {\r\n      return 'Non défini';\r\n    }\r\n\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === userId || u.id === userId\r\n    );\r\n    if (user) {\r\n      if (user.firstName && user.lastName) {\r\n        return `${user.firstName} ${user.lastName}`;\r\n      } else if (user.name) {\r\n        return user.name;\r\n      }\r\n    }\r\n    return userId;\r\n  }\r\n\r\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\r\n  getUserProfession(userId: string | undefined): string {\r\n    if (!userId) {\r\n      return '';\r\n    }\r\n\r\n    const user = this.availableUsers.find(\r\n      (u) => u._id === userId || u.id === userId\r\n    );\r\n    if (user) {\r\n      return user.profession || user.role || '';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  loadEquipe(id: string): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.equipeService.getEquipe(id).subscribe({\r\n      next: (data) => {\r\n        console.log(\"Détails de l'équipe chargés:\", data);\r\n        this.equipe = data;\r\n\r\n        // Charger les détails des membres de l'équipe\r\n        this.loadTeamMembers(id);\r\n\r\n        // Mettre à jour les noms des membres\r\n        if (\r\n          this.equipe &&\r\n          this.equipe.members &&\r\n          this.equipe.members.length > 0\r\n        ) {\r\n          this.updateMemberNames();\r\n        }\r\n\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error(\r\n          \"Erreur lors du chargement des détails de l'équipe:\",\r\n          error\r\n        );\r\n        this.error =\r\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode pour charger les détails des membres de l'équipe\r\n  loadTeamMembers(teamId: string): void {\r\n    this.equipeService.getTeamMembers(teamId).subscribe({\r\n      next: (members) => {\r\n        console.log('Détails des membres chargés:', members);\r\n        this.teamMembers = members;\r\n      },\r\n      error: (error) => {\r\n        console.error(\r\n          'Erreur lors du chargement des détails des membres:',\r\n          error\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  navigateToEditEquipe(): void {\r\n    if (this.equipeId) {\r\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\r\n    }\r\n  }\r\n\r\n  navigateToEquipeList(): void {\r\n    this.router.navigate(['/equipes/liste']);\r\n  }\r\n\r\n  navigateToTasks(): void {\r\n    if (this.equipeId) {\r\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\r\n    }\r\n  }\r\n\r\n  // Méthode pour formater les dates\r\n  formatDate(date: Date | string | undefined): string {\r\n    if (!date) {\r\n      return 'N/A';\r\n    }\r\n\r\n    try {\r\n      let dateObj: Date;\r\n\r\n      if (typeof date === 'string') {\r\n        dateObj = new Date(date);\r\n      } else {\r\n        dateObj = date;\r\n      }\r\n\r\n      if (isNaN(dateObj.getTime())) {\r\n        return 'Date invalide';\r\n      }\r\n\r\n      // Format: JJ/MM/AAAA\r\n      return dateObj.toLocaleDateString('fr-FR', {\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n        year: 'numeric',\r\n      });\r\n    } catch (error) {\r\n      console.error('Erreur lors du formatage de la date:', error);\r\n      return 'Erreur de date';\r\n    }\r\n  }\r\n\r\n  // Méthode pour ajouter un membre à l'équipe\r\n  addMembre(userId: string, role: string): void {\r\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\r\n\r\n    if (!this.equipeId || !userId) {\r\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\r\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\r\n      return;\r\n    }\r\n\r\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\r\n    const isAlreadyMember = this.teamMembers.some((m) => m.user === userId);\r\n    if (isAlreadyMember) {\r\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\r\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\r\n      return;\r\n    }\r\n\r\n    // Créer l'objet membre avec le rôle spécifié\r\n    const membre: Membre = {\r\n      id: userId,\r\n      role: role || 'membre',\r\n    };\r\n\r\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\r\n    const userName = this.getUserName(userId);\r\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\r\n\r\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\r\n      next: (response) => {\r\n        console.log(\r\n          `Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`,\r\n          response\r\n        );\r\n\r\n        // Afficher un message de succès\r\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\r\n\r\n        // Recharger les membres de l'équipe\r\n        this.loadTeamMembers(this.equipeId!);\r\n\r\n        // Recharger l'équipe pour mettre à jour la liste des membres\r\n        this.loadEquipe(this.equipeId!);\r\n\r\n        // Mettre à jour la liste des utilisateurs disponibles\r\n        this.updateAvailableUsers();\r\n      },\r\n      error: (error) => {\r\n        console.error(\r\n          \"Erreur lors de l'ajout de l'utilisateur comme membre:\",\r\n          error\r\n        );\r\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\r\n        alert(this.error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\r\n  updateAvailableUsers(): void {\r\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\r\n    // Pour l'instant, utiliser les données mockées de loadUsers()\r\n    this.loadUsers();\r\n  }\r\n\r\n  // Ancienne méthode maintenue pour compatibilité\r\n  addMembreToEquipe(): void {\r\n    if (!this.equipeId || !this.newMembre.id) {\r\n      console.error(\"ID d'équipe ou ID de membre manquant\");\r\n      return;\r\n    }\r\n\r\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\r\n  }\r\n\r\n  removeMembreFromEquipe(membreId: string): void {\r\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\r\n\r\n    if (!this.equipeId) {\r\n      console.error(\"ID d'équipe manquant\");\r\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\r\n      return;\r\n    }\r\n\r\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\r\n    const userId = membreId;\r\n\r\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\r\n    const userName = this.getUserName(userId);\r\n\r\n    console.log(\r\n      `Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`\r\n    );\r\n\r\n    if (\r\n      confirm(\r\n        `Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`\r\n      )\r\n    ) {\r\n      console.log('Confirmation acceptée, suppression en cours...');\r\n\r\n      this.loading = true;\r\n      this.error = null;\r\n\r\n      this.equipeService\r\n        .removeMembreFromEquipe(this.equipeId, userId)\r\n        .subscribe({\r\n          next: (response) => {\r\n            console.log(\r\n              `Utilisateur \"${userName}\" retiré avec succès de l'équipe:`,\r\n              response\r\n            );\r\n            this.loading = false;\r\n\r\n            // Afficher un message de succès\r\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\r\n\r\n            // Recharger les membres de l'équipe\r\n            this.loadTeamMembers(this.equipeId!);\r\n\r\n            // Recharger l'équipe pour mettre à jour la liste des membres\r\n            this.loadEquipe(this.equipeId!);\r\n\r\n            // Mettre à jour la liste des utilisateurs disponibles\r\n            this.updateAvailableUsers();\r\n          },\r\n          error: (error) => {\r\n            console.error(\r\n              `Erreur lors du retrait de l'utilisateur \"${userName}\":`,\r\n              error\r\n            );\r\n            this.loading = false;\r\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${\r\n              error.message || 'Erreur inconnue'\r\n            }`;\r\n          },\r\n        });\r\n    } else {\r\n      console.log(\"Suppression annulée par l'utilisateur\");\r\n    }\r\n  }\r\n\r\n  deleteEquipe(): void {\r\n    console.log('Méthode deleteEquipe appelée');\r\n\r\n    if (!this.equipeId) {\r\n      console.error(\"ID d'équipe manquant\");\r\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\r\n      return;\r\n    }\r\n\r\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\r\n\r\n    if (\r\n      confirm(\r\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`\r\n      )\r\n    ) {\r\n      console.log('Confirmation acceptée, suppression en cours...');\r\n\r\n      this.loading = true;\r\n      this.error = null;\r\n\r\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\r\n        next: () => {\r\n          console.log('Équipe supprimée avec succès');\r\n          this.loading = false;\r\n          alert('Équipe supprimée avec succès');\r\n          this.router.navigate(['/equipes/liste']);\r\n        },\r\n        error: (error) => {\r\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\r\n          this.loading = false;\r\n          this.error = `Impossible de supprimer l'équipe: ${\r\n            error.message || 'Erreur inconnue'\r\n          }`;\r\n          alert(`Erreur lors de la suppression: ${this.error}`);\r\n        },\r\n      });\r\n    } else {\r\n      console.log(\"Suppression annulée par l'utilisateur\");\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\r\n  *ngIf=\"equipe\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\r\n    <!-- Header futuriste -->\r\n    <div class=\"mb-8 relative\">\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <div\r\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\r\n      >\r\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-0\">\r\n          <!-- Bannière avec titre -->\r\n          <div\r\n            class=\"lg:col-span-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] p-6\"\r\n          >\r\n            <h1 class=\"text-3xl font-bold text-white mb-2 tracking-wide\">\r\n              {{ equipe.name }}\r\n            </h1>\r\n            <p class=\"text-white/80 text-sm mb-6\">\r\n              Gestion et collaboration d'équipe moderne\r\n            </p>\r\n\r\n            <!-- Statistiques rapides -->\r\n            <div class=\"grid grid-cols-3 gap-4\">\r\n              <div\r\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\r\n              >\r\n                <i class=\"fas fa-users text-2xl mb-2 block\"></i>\r\n                <span class=\"text-xl font-bold block\">{{\r\n                  equipe.members?.length || 0\r\n                }}</span>\r\n                <small class=\"text-white/80\">Membres</small>\r\n              </div>\r\n              <div\r\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\r\n              >\r\n                <i class=\"fas fa-tasks text-2xl mb-2 block\"></i>\r\n                <span class=\"text-xl font-bold block\">0</span>\r\n                <small class=\"text-white/80\">Tâches</small>\r\n              </div>\r\n              <div\r\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\r\n              >\r\n                <i class=\"fas fa-calendar-check text-2xl mb-2 block\"></i>\r\n                <span class=\"text-sm font-bold block\">{{\r\n                  formatDate(equipe.createdAt)\r\n                }}</span>\r\n                <small class=\"text-white/80\">Créée le</small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Actions rapides -->\r\n          <div\r\n            class=\"bg-white dark:bg-[#1a1a1a] p-6 flex flex-col justify-center\"\r\n          >\r\n            <h4\r\n              class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-bolt mr-2\"></i>Actions rapides\r\n            </h4>\r\n            <div class=\"space-y-3\">\r\n              <button\r\n                (click)=\"navigateToTasks()\"\r\n                class=\"w-full bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\r\n              >\r\n                <i class=\"fas fa-tasks mr-2\"></i> Gérer les tâches\r\n              </button>\r\n              <button\r\n                (click)=\"navigateToEditEquipe()\"\r\n                class=\"w-full bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 text-[#4f5fad] dark:text-[#00f7ff] px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\r\n              >\r\n                <i class=\"fas fa-edit mr-2\"></i> Modifier l'équipe\r\n              </button>\r\n              <div class=\"grid grid-cols-2 gap-2\">\r\n                <button\r\n                  (click)=\"navigateToEquipeList()\"\r\n                  class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\r\n                >\r\n                  <i class=\"fas fa-arrow-left mr-1\"></i> Retour\r\n                </button>\r\n                <button\r\n                  (click)=\"deleteEquipe()\"\r\n                  class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\r\n                >\r\n                  <i class=\"fas fa-trash mr-1\"></i> Supprimer\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Carte d'informations de l'équipe -->\r\n    <div class=\"row mb-5\">\r\n      <div class=\"col-12\">\r\n        <div\r\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\r\n        >\r\n          <div class=\"card-body p-0\">\r\n            <div class=\"row g-0\">\r\n              <!-- Icône et titre -->\r\n              <div\r\n                class=\"col-md-3 bg-primary text-white p-4 d-flex flex-column justify-content-center align-items-center text-center\"\r\n              >\r\n                <div class=\"icon-circle bg-white text-primary mb-3\">\r\n                  <i class=\"bi bi-info-circle-fill fs-1\"></i>\r\n                </div>\r\n                <h3 class=\"mb-2\">À propos</h3>\r\n                <p class=\"mb-0 text-white-50\">\r\n                  Détails et informations sur l'équipe\r\n                </p>\r\n              </div>\r\n\r\n              <!-- Contenu -->\r\n              <div class=\"col-md-9 p-4\">\r\n                <div\r\n                  class=\"d-flex justify-content-between align-items-center mb-4\"\r\n                >\r\n                  <h4 class=\"text-primary mb-0\">Description</h4>\r\n                  <span\r\n                    class=\"badge bg-light text-primary rounded-pill px-3 py-2\"\r\n                  >\r\n                    <i class=\"bi bi-person-fill-gear me-1\"></i>\r\n                    Admin:\r\n                    {{\r\n                      equipe.admin\r\n                        ? getUserName(equipe.admin) || equipe.admin\r\n                        : \"Non défini\"\r\n                    }}\r\n                  </span>\r\n                </div>\r\n\r\n                <div class=\"description-box p-3 bg-light rounded-4 mb-4\">\r\n                  <p class=\"lead mb-0\">\r\n                    {{\r\n                      equipe.description ||\r\n                        \"Aucune description disponible pour cette équipe.\"\r\n                    }}\r\n                  </p>\r\n                </div>\r\n\r\n                <!-- Tags et informations supplémentaires -->\r\n                <div class=\"d-flex flex-wrap gap-2 mt-4\">\r\n                  <span\r\n                    class=\"badge bg-primary bg-opacity-10 text-primary rounded-pill px-3 py-2\"\r\n                  >\r\n                    <i class=\"bi bi-people-fill me-1\"></i>\r\n                    {{ equipe.members?.length || 0 }} membres\r\n                  </span>\r\n                  <span\r\n                    class=\"badge bg-success bg-opacity-10 text-success rounded-pill px-3 py-2\"\r\n                  >\r\n                    <i class=\"bi bi-calendar-check me-1\"></i>\r\n                    Créée le {{ formatDate(equipe.createdAt) }}\r\n                  </span>\r\n                  <span\r\n                    class=\"badge bg-info bg-opacity-10 text-info rounded-pill px-3 py-2\"\r\n                  >\r\n                    <i class=\"bi bi-kanban me-1\"></i>\r\n                    Gestion de projet\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Assistant IA pour la gestion de projet (pleine largeur) -->\r\n    <div class=\"row mb-5\">\r\n      <div class=\"col-12\">\r\n        <div\r\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\r\n        >\r\n          <div\r\n            class=\"card-header border-0 py-4\"\r\n            style=\"background: linear-gradient(45deg, #8e2de2, #4a00e0)\"\r\n          >\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <h3 class=\"mb-0 text-white d-flex align-items-center\">\r\n                <div class=\"icon-circle bg-white text-primary me-3\">\r\n                  <i class=\"bi bi-robot\"></i>\r\n                </div>\r\n                Assistant IA Gemini\r\n              </h3>\r\n              <span class=\"badge bg-white text-primary rounded-pill px-3 py-2\">\r\n                <i class=\"bi bi-magic me-1\"></i>\r\n                Génération de tâches intelligente\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-body p-0\">\r\n            <app-ai-chat [team]=\"equipe\"></app-ai-chat>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Section des membres de l'équipe -->\r\n    <div class=\"row mb-5\">\r\n      <div class=\"col-12\">\r\n        <div\r\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\r\n        >\r\n          <div\r\n            class=\"card-header border-0 py-4 d-flex justify-content-between align-items-center\"\r\n            style=\"background: linear-gradient(45deg, #11998e, #38ef7d)\"\r\n          >\r\n            <h3 class=\"mb-0 text-white d-flex align-items-center\">\r\n              <div class=\"icon-circle bg-white text-success me-3\">\r\n                <i class=\"bi bi-people-fill\"></i>\r\n              </div>\r\n              Membres de l'équipe\r\n            </h3>\r\n            <span class=\"badge bg-white text-success rounded-pill px-3 py-2\">\r\n              {{ teamMembers.length || 0 }} membres\r\n            </span>\r\n          </div>\r\n\r\n          <div class=\"card-body p-0\">\r\n            <!-- Liste des membres -->\r\n            <div\r\n              *ngIf=\"teamMembers && teamMembers.length > 0; else noMembers\"\r\n              class=\"p-0\"\r\n            >\r\n              <div class=\"row g-0\">\r\n                <div class=\"col-md-8\">\r\n                  <div class=\"member-grid p-4\">\r\n                    <div\r\n                      *ngFor=\"let membre of teamMembers\"\r\n                      class=\"member-card mb-3 p-3 rounded-4 shadow-sm transition\"\r\n                    >\r\n                      <div\r\n                        class=\"d-flex justify-content-between align-items-start\"\r\n                      >\r\n                        <!-- Informations du membre -->\r\n                        <div class=\"d-flex align-items-center\">\r\n                          <div\r\n                            class=\"member-avatar rounded-circle text-white me-3\"\r\n                            [ngClass]=\"{\r\n                              'bg-primary':\r\n                                getUserProfession(membre.user) === 'etudiant',\r\n                              'bg-success':\r\n                                getUserProfession(membre.user) === 'professeur',\r\n                              'bg-secondary': !getUserProfession(membre.user)\r\n                            }\"\r\n                          >\r\n                            <i\r\n                              class=\"bi\"\r\n                              [ngClass]=\"{\r\n                                'bi-mortarboard-fill':\r\n                                  getUserProfession(membre.user) === 'etudiant',\r\n                                'bi-briefcase-fill':\r\n                                  getUserProfession(membre.user) ===\r\n                                  'professeur',\r\n                                'bi-person-fill': !getUserProfession(\r\n                                  membre.user\r\n                                )\r\n                              }\"\r\n                            ></i>\r\n                          </div>\r\n                          <div>\r\n                            <h6 class=\"mb-0 fw-bold\">\r\n                              {{ getUserName(membre.user) }}\r\n                            </h6>\r\n                            <div class=\"d-flex align-items-center mt-1\">\r\n                              <span\r\n                                class=\"badge rounded-pill me-2\"\r\n                                [ngClass]=\"{\r\n                                  'bg-success bg-opacity-10 text-success':\r\n                                    membre.role === 'admin',\r\n                                  'bg-primary bg-opacity-10 text-primary':\r\n                                    membre.role === 'membre'\r\n                                }\"\r\n                              >\r\n                                <i\r\n                                  class=\"bi\"\r\n                                  [ngClass]=\"{\r\n                                    'bi-person-fill-gear':\r\n                                      membre.role === 'admin',\r\n                                    'bi-person': membre.role === 'membre'\r\n                                  }\"\r\n                                ></i>\r\n                                {{\r\n                                  membre.role === \"admin\"\r\n                                    ? \"Administrateur\"\r\n                                    : \"Membre\"\r\n                                }}\r\n                              </span>\r\n                              <small class=\"text-muted\">{{\r\n                                getUserProfession(membre.user) === \"etudiant\"\r\n                                  ? \"Étudiant\"\r\n                                  : getUserProfession(membre.user) ===\r\n                                    \"professeur\"\r\n                                  ? \"Professeur\"\r\n                                  : \"Utilisateur\"\r\n                              }}</small>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Actions -->\r\n                        <button\r\n                          class=\"btn btn-sm btn-outline-danger rounded-circle\"\r\n                          title=\"Retirer de l'équipe\"\r\n                          (click)=\"removeMembreFromEquipe(membre._id)\"\r\n                        >\r\n                          <i class=\"bi bi-trash\"></i>\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Formulaire d'ajout de membre -->\r\n                <div class=\"col-md-4 bg-light p-4\">\r\n                  <h5 class=\"d-flex align-items-center mb-4 text-success\">\r\n                    <i class=\"bi bi-person-plus-fill me-2\"></i>\r\n                    Ajouter un membre\r\n                  </h5>\r\n\r\n                  <!-- Afficher un message si aucun utilisateur n'est disponible -->\r\n                  <div\r\n                    *ngIf=\"availableUsers.length === 0\"\r\n                    class=\"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\"\r\n                  >\r\n                    <i\r\n                      class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"\r\n                    ></i>\r\n                    <div>\r\n                      Aucun utilisateur disponible. Veuillez d'abord créer des\r\n                      utilisateurs.\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->\r\n                  <div\r\n                    *ngIf=\"availableUsers.length > 0\"\r\n                    class=\"add-member-form\"\r\n                  >\r\n                    <div class=\"mb-3\">\r\n                      <label for=\"userSelect\" class=\"form-label fw-medium\"\r\n                        >Utilisateur</label\r\n                      >\r\n                      <select\r\n                        #userSelect\r\n                        id=\"userSelect\"\r\n                        class=\"form-select border-0 shadow-sm rounded-4 py-2\"\r\n                      >\r\n                        <option value=\"\" selected disabled>\r\n                          Sélectionnez un utilisateur\r\n                        </option>\r\n                        <option\r\n                          *ngFor=\"let user of availableUsers\"\r\n                          [value]=\"user._id || user.id\"\r\n                        >\r\n                          {{ user.firstName || \"\" }}\r\n                          {{ user.lastName || user.name || user.id }}\r\n                          {{ user.email ? \"- \" + user.email : \"\" }}\r\n                          {{\r\n                            user.profession\r\n                              ? \"(\" +\r\n                                (user.profession === \"etudiant\"\r\n                                  ? \"Étudiant\"\r\n                                  : \"Professeur\") +\r\n                                \")\"\r\n                              : \"\"\r\n                          }}\r\n                        </option>\r\n                      </select>\r\n                    </div>\r\n\r\n                    <div class=\"mb-3\">\r\n                      <label for=\"roleSelect\" class=\"form-label fw-medium\"\r\n                        >Rôle dans l'équipe</label\r\n                      >\r\n                      <div class=\"d-flex gap-2\">\r\n                        <div class=\"form-check flex-grow-1\">\r\n                          <input\r\n                            class=\"form-check-input\"\r\n                            type=\"radio\"\r\n                            name=\"roleRadio\"\r\n                            id=\"roleMembre\"\r\n                            value=\"membre\"\r\n                            checked\r\n                            #roleMembre\r\n                          />\r\n                          <label\r\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\r\n                            for=\"roleMembre\"\r\n                          >\r\n                            <i class=\"bi bi-person d-block fs-4 mb-1\"></i>\r\n                            Membre\r\n                          </label>\r\n                        </div>\r\n                        <div class=\"form-check flex-grow-1\">\r\n                          <input\r\n                            class=\"form-check-input\"\r\n                            type=\"radio\"\r\n                            name=\"roleRadio\"\r\n                            id=\"roleAdmin\"\r\n                            value=\"admin\"\r\n                            #roleAdmin\r\n                          />\r\n                          <label\r\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\r\n                            for=\"roleAdmin\"\r\n                          >\r\n                            <i\r\n                              class=\"bi bi-person-fill-gear d-block fs-4 mb-1\"\r\n                            ></i>\r\n                            Admin\r\n                          </label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"d-grid\">\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-success rounded-4 py-2 shadow-sm\"\r\n                        [disabled]=\"!userSelect.value\"\r\n                        (click)=\"\r\n                          addMembre(\r\n                            userSelect.value,\r\n                            roleMembre.checked ? 'membre' : 'admin'\r\n                          );\r\n                          userSelect.value = ''\r\n                        \"\r\n                      >\r\n                        <i class=\"bi bi-plus-circle me-2\"></i> Ajouter à\r\n                        l'équipe\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <ng-template #noMembers>\r\n              <div class=\"row g-0\">\r\n                <div class=\"col-md-8\">\r\n                  <div class=\"text-center py-5\">\r\n                    <div class=\"empty-state-icon mb-4\">\r\n                      <i class=\"bi bi-people fs-1 text-muted\"></i>\r\n                    </div>\r\n                    <h5 class=\"text-muted\">Aucun membre dans cette équipe</h5>\r\n                    <p class=\"text-muted\">\r\n                      Ajoutez des membres à l'équipe en utilisant le formulaire\r\n                      ci-contre.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Formulaire d'ajout de membre (même code que ci-dessus) -->\r\n                <div class=\"col-md-4 bg-light p-4\">\r\n                  <h5 class=\"d-flex align-items-center mb-4 text-success\">\r\n                    <i class=\"bi bi-person-plus-fill me-2\"></i>\r\n                    Ajouter un membre\r\n                  </h5>\r\n\r\n                  <!-- Afficher un message si aucun utilisateur n'est disponible -->\r\n                  <div\r\n                    *ngIf=\"availableUsers.length === 0\"\r\n                    class=\"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\"\r\n                  >\r\n                    <i\r\n                      class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"\r\n                    ></i>\r\n                    <div>\r\n                      Aucun utilisateur disponible. Veuillez d'abord créer des\r\n                      utilisateurs.\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->\r\n                  <div\r\n                    *ngIf=\"availableUsers.length > 0\"\r\n                    class=\"add-member-form\"\r\n                  >\r\n                    <div class=\"mb-3\">\r\n                      <label for=\"userSelect2\" class=\"form-label fw-medium\"\r\n                        >Utilisateur</label\r\n                      >\r\n                      <select\r\n                        #userSelect2\r\n                        id=\"userSelect2\"\r\n                        class=\"form-select border-0 shadow-sm rounded-4 py-2\"\r\n                      >\r\n                        <option value=\"\" selected disabled>\r\n                          Sélectionnez un utilisateur\r\n                        </option>\r\n                        <option\r\n                          *ngFor=\"let user of availableUsers\"\r\n                          [value]=\"user._id || user.id\"\r\n                        >\r\n                          {{ user.firstName || \"\" }}\r\n                          {{ user.lastName || user.name || user.id }}\r\n                          {{ user.email ? \"- \" + user.email : \"\" }}\r\n                          {{\r\n                            user.profession\r\n                              ? \"(\" +\r\n                                (user.profession === \"etudiant\"\r\n                                  ? \"Étudiant\"\r\n                                  : \"Professeur\") +\r\n                                \")\"\r\n                              : \"\"\r\n                          }}\r\n                        </option>\r\n                      </select>\r\n                    </div>\r\n\r\n                    <div class=\"mb-3\">\r\n                      <label for=\"roleSelect2\" class=\"form-label fw-medium\"\r\n                        >Rôle dans l'équipe</label\r\n                      >\r\n                      <div class=\"d-flex gap-2\">\r\n                        <div class=\"form-check flex-grow-1\">\r\n                          <input\r\n                            class=\"form-check-input\"\r\n                            type=\"radio\"\r\n                            name=\"roleRadio2\"\r\n                            id=\"roleMembre2\"\r\n                            value=\"membre\"\r\n                            checked\r\n                            #roleMembre2\r\n                          />\r\n                          <label\r\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\r\n                            for=\"roleMembre2\"\r\n                          >\r\n                            <i class=\"bi bi-person d-block fs-4 mb-1\"></i>\r\n                            Membre\r\n                          </label>\r\n                        </div>\r\n                        <div class=\"form-check flex-grow-1\">\r\n                          <input\r\n                            class=\"form-check-input\"\r\n                            type=\"radio\"\r\n                            name=\"roleRadio2\"\r\n                            id=\"roleAdmin2\"\r\n                            value=\"admin\"\r\n                            #roleAdmin2\r\n                          />\r\n                          <label\r\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\r\n                            for=\"roleAdmin2\"\r\n                          >\r\n                            <i\r\n                              class=\"bi bi-person-fill-gear d-block fs-4 mb-1\"\r\n                            ></i>\r\n                            Admin\r\n                          </label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"d-grid\">\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"btn btn-success rounded-4 py-2 shadow-sm\"\r\n                        [disabled]=\"!userSelect2.value\"\r\n                        (click)=\"\r\n                          addMembre(\r\n                            userSelect2.value,\r\n                            roleMembre2.checked ? 'membre' : 'admin'\r\n                          );\r\n                          userSelect2.value = ''\r\n                        \"\r\n                      >\r\n                        <i class=\"bi bi-plus-circle me-2\"></i> Ajouter à\r\n                        l'équipe\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </ng-template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Message de chargement ou d'erreur -->\r\n<div class=\"container-fluid py-5 bg-light\" *ngIf=\"!equipe\">\r\n  <div class=\"container\">\r\n    <div class=\"row justify-content-center\">\r\n      <div class=\"col-md-8 text-center\">\r\n        <div\r\n          class=\"alert alert-warning shadow-sm border-0 rounded-3 d-flex align-items-center p-4\"\r\n        >\r\n          <i class=\"bi bi-exclamation-triangle-fill fs-1 me-4 text-warning\"></i>\r\n          <div class=\"fs-5\">\r\n            Équipe non trouvée ou en cours de chargement...\r\n          </div>\r\n        </div>\r\n        <button\r\n          class=\"btn btn-outline-primary rounded-pill mt-4\"\r\n          (click)=\"navigateToEquipeList()\"\r\n        >\r\n          <i class=\"bi bi-arrow-left me-2\"></i> Retour à la liste des équipes\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Styles spécifiques pour cette page -->\r\n<style>\r\n  /* Fond dégradé pour l'en-tête du formulaire */\r\n  .bg-gradient-primary {\r\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\r\n  }\r\n\r\n  .bg-gradient-light {\r\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\r\n  }\r\n\r\n  /* Animation au survol des éléments */\r\n  .transition {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  /* Effet de survol pour les cartes */\r\n  .hover-card {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .hover-card:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\r\n  }\r\n\r\n  /* Styles pour les cartes de membres */\r\n  .member-card {\r\n    background-color: white;\r\n    transition: all 0.3s ease;\r\n    border-left: 4px solid transparent;\r\n  }\r\n\r\n  .member-card:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\r\n  }\r\n\r\n  /* Avatar des membres */\r\n  .member-avatar {\r\n    width: 45px;\r\n    height: 45px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  /* Icône circulaire */\r\n  .icon-circle {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 1.2rem;\r\n  }\r\n\r\n  /* Styles pour la boîte de description */\r\n  .description-box {\r\n    border-left: 4px solid #007bff;\r\n  }\r\n\r\n  /* Animation pour les boutons */\r\n  .btn {\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .btn:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  /* Styles pour les badges */\r\n  .badge {\r\n    font-weight: 500;\r\n    letter-spacing: 0.5px;\r\n  }\r\n\r\n  /* Styles pour les formulaires */\r\n  .form-select,\r\n  .form-control {\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .form-select:focus,\r\n  .form-control:focus {\r\n    border-color: #007bff;\r\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\r\n  }\r\n\r\n  /* Styles pour les états vides */\r\n  .empty-state-icon {\r\n    width: 80px;\r\n    height: 80px;\r\n    margin: 0 auto;\r\n    background-color: #f8f9fa;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 2rem;\r\n    color: #adb5bd;\r\n  }\r\n\r\n  /* Styles pour les sélecteurs de rôle */\r\n  .form-check-label {\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n  }\r\n\r\n  .form-check-input:checked + .form-check-label {\r\n    background-color: rgba(13, 110, 253, 0.1);\r\n    border-color: #007bff;\r\n  }\r\n\r\n  /* Styles pour les arrondis */\r\n  .rounded-4 {\r\n    border-radius: 0.75rem !important;\r\n  }\r\n\r\n  /* Styles pour la grille de membres */\r\n  .member-grid {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICyQoBA,EAAA,CAAAC,cAAA,cAGC;IAgBOD,EAAA,CAAAE,SAAA,YAYK;IACPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA4C;IAUxCD,EAAA,CAAAE,SAAA,aAOK;IACLF,EAAA,CAAAI,MAAA,IAKF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAOxB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAMhBH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAK,UAAA,mBAAAC,4EAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,sBAAA,CAAAL,SAAA,CAAAM,GAAA,CAAkC;IAAA,EAAC;IAE5ChB,EAAA,CAAAE,SAAA,aAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;;IArELH,EAAA,CAAAiB,SAAA,GAME;IANFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,kBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,GAME;IAIAvB,EAAA,CAAAiB,SAAA,GASE;IATFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,KAAAK,GAAA,EAAAH,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,kBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qBAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,GASE;IAKFvB,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAAJ,MAAA,CAAAK,WAAA,CAAAhB,SAAA,CAAAa,IAAA,OACF;IAIIvB,EAAA,CAAAiB,SAAA,GAKE;IALFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA2B,eAAA,KAAAC,GAAA,EAAAlB,SAAA,CAAAmB,IAAA,cAAAnB,SAAA,CAAAmB,IAAA,eAKE;IAIA7B,EAAA,CAAAiB,SAAA,GAIE;IAJFjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA2B,eAAA,KAAAG,GAAA,EAAApB,SAAA,CAAAmB,IAAA,cAAAnB,SAAA,CAAAmB,IAAA,eAIE;IAEJ7B,EAAA,CAAAiB,SAAA,GAKF;IALEjB,EAAA,CAAAyB,kBAAA,MAAAf,SAAA,CAAAmB,IAAA,gDAKF;IAC0B7B,EAAA,CAAAiB,SAAA,GAOxB;IAPwBjB,EAAA,CAAA+B,iBAAA,CAAAV,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,qCAAAF,MAAA,CAAAC,iBAAA,CAAAZ,SAAA,CAAAa,IAAA,kDAOxB;;;;;IA0BdvB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAEK;IACLF,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAI,MAAA,oFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBFH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAI,MAAA,GAYF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAdPH,EAAA,CAAAkB,UAAA,UAAAc,QAAA,CAAAhB,GAAA,IAAAgB,QAAA,CAAAC,EAAA,CAA6B;IAE7BjC,EAAA,CAAAiB,SAAA,GAYF;IAZEjB,EAAA,CAAAkC,kBAAA,MAAAF,QAAA,CAAAG,SAAA,aAAAH,QAAA,CAAAI,QAAA,IAAAJ,QAAA,CAAAK,IAAA,IAAAL,QAAA,CAAAC,EAAA,OAAAD,QAAA,CAAAM,KAAA,UAAAN,QAAA,CAAAM,KAAA,YAAAN,QAAA,CAAAO,UAAA,UAAAP,QAAA,CAAAO,UAAA,kEAYF;;;;;;IAhCNvC,EAAA,CAAAC,cAAA,cAGC;IAGMD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EACb;IACDH,EAAA,CAAAC,cAAA,uBAIC;IAEGD,EAAA,CAAAI,MAAA,yCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAwC,UAAA,IAAAC,4DAAA,sBAgBS;IACXzC,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAkB;IAEbD,EAAA,CAAAI,MAAA,oCAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;IACDH,EAAA,CAAAC,cAAA,gBAA0B;IAEtBD,EAAA,CAAAE,SAAA,uBAQE;IACFF,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,SAAA,cAA8C;IAC9CF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAAE,SAAA,uBAOE;IACFF,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,SAAA,cAEK;IACLF,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAKdH,EAAA,CAAAC,cAAA,gBAAoB;IAKhBD,EAAA,CAAAK,UAAA,mBAAAqC,6EAAA;MAAA1C,EAAA,CAAAQ,aAAA,CAAAmC,IAAA;MAAA,MAAAC,IAAA,GAAA5C,EAAA,CAAA6C,WAAA;MAAA,MAAAC,IAAA,GAAA9C,EAAA,CAAA6C,WAAA;MAAA,MAAAE,OAAA,GAAA/C,EAAA,CAAAa,aAAA;MAETkC,OAAA,CAAAC,SAAA,CAAAJ,IAAA,CAAAK,KAAA,EAAAH,IAAA,CAAAI,OAAA,GAEV,QAAQ,GAAG,OAAO,CACF;MAAA,OACrBlD,EAAA,CAAAc,WAAA,CAAA8B,IAAA,CAAAK,KAAA,GAAmB,EAAE;IAAA,EAAI;IAEDjD,EAAA,CAAAE,SAAA,cAAsC;IAACF,EAAA,CAAAI,MAAA,sCAEzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IA/EYH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAiC,MAAA,CAAAC,cAAA,CAAiB;IAoEpCpD,EAAA,CAAAiB,SAAA,IAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,cAAA0B,IAAA,CAAAK,KAAA,CAA8B;;;;;IAxM1CjD,EAAA,CAAAC,cAAA,cAGC;IAIOD,EAAA,CAAAwC,UAAA,IAAAa,kDAAA,oBAkFM;IACRrD,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,SAAA,YAA2C;IAC3CF,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAwC,UAAA,IAAAc,kDAAA,kBAWM;IAGNtD,EAAA,CAAAwC,UAAA,KAAAe,mDAAA,mBAkGM;IACRvD,EAAA,CAAAG,YAAA,EAAM;;;;IA9MmBH,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,YAAAsC,MAAA,CAAAC,WAAA,CAAc;IA8FlCzD,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,SAAAsC,MAAA,CAAAJ,cAAA,CAAAM,MAAA,OAAiC;IAcjC1D,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAkB,UAAA,SAAAsC,MAAA,CAAAJ,cAAA,CAAAM,MAAA,KAA+B;;;;;IA6HlC1D,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAEK;IACLF,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAI,MAAA,oFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBFH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAI,MAAA,GAYF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAdPH,EAAA,CAAAkB,UAAA,UAAAyC,QAAA,CAAA3C,GAAA,IAAA2C,QAAA,CAAA1B,EAAA,CAA6B;IAE7BjC,EAAA,CAAAiB,SAAA,GAYF;IAZEjB,EAAA,CAAAkC,kBAAA,MAAAyB,QAAA,CAAAxB,SAAA,aAAAwB,QAAA,CAAAvB,QAAA,IAAAuB,QAAA,CAAAtB,IAAA,IAAAsB,QAAA,CAAA1B,EAAA,OAAA0B,QAAA,CAAArB,KAAA,UAAAqB,QAAA,CAAArB,KAAA,YAAAqB,QAAA,CAAApB,UAAA,UAAAoB,QAAA,CAAApB,UAAA,kEAYF;;;;;;IAhCNvC,EAAA,CAAAC,cAAA,cAGC;IAGMD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EACb;IACDH,EAAA,CAAAC,cAAA,uBAIC;IAEGD,EAAA,CAAAI,MAAA,yCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAwC,UAAA,IAAAoB,oEAAA,sBAgBS;IACX5D,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAkB;IAEbD,EAAA,CAAAI,MAAA,oCAAkB;IAAAJ,EAAA,CAAAG,YAAA,EACpB;IACDH,EAAA,CAAAC,cAAA,gBAA0B;IAEtBD,EAAA,CAAAE,SAAA,uBAQE;IACFF,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,SAAA,cAA8C;IAC9CF,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAAE,SAAA,uBAOE;IACFF,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAE,SAAA,cAEK;IACLF,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAKdH,EAAA,CAAAC,cAAA,gBAAoB;IAKhBD,EAAA,CAAAK,UAAA,mBAAAwD,qFAAA;MAAA7D,EAAA,CAAAQ,aAAA,CAAAsD,IAAA;MAAA,MAAAC,IAAA,GAAA/D,EAAA,CAAA6C,WAAA;MAAA,MAAAmB,IAAA,GAAAhE,EAAA,CAAA6C,WAAA;MAAA,MAAAoB,OAAA,GAAAjE,EAAA,CAAAa,aAAA;MAEToD,OAAA,CAAAjB,SAAA,CAAAe,IAAA,CAAAd,KAAA,EAAAe,IAAA,CAAAd,OAAA,GAEV,QAAQ,GAAG,OAAO,CACF;MAAA,OAA4BlD,EAAA,CAAAc,WAAA,CAAAiD,IAAA,CAAAd,KAAA,GAC9B,EAAE;IAAA,EAAI;IAEDjD,EAAA,CAAAE,SAAA,cAAsC;IAACF,EAAA,CAAAI,MAAA,sCAEzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IA/EYH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,UAAA,YAAAgD,OAAA,CAAAd,cAAA,CAAiB;IAoEpCpD,EAAA,CAAAiB,SAAA,IAA+B;IAA/BjB,EAAA,CAAAkB,UAAA,cAAA6C,IAAA,CAAAd,KAAA,CAA+B;;;;;IAzHzCjD,EAAA,CAAAC,cAAA,cAAqB;IAIbD,EAAA,CAAAE,SAAA,aAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAI,MAAA,0CAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,YAAsB;IACpBD,EAAA,CAAAI,MAAA,uFAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAKRH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAE,SAAA,aAA2C;IAC3CF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAwC,UAAA,KAAA2B,2DAAA,kBAWM;IAGNnE,EAAA,CAAAwC,UAAA,KAAA4B,2DAAA,mBAkGM;IACRpE,EAAA,CAAAG,YAAA,EAAM;;;;IAhHDH,EAAA,CAAAiB,SAAA,IAAiC;IAAjCjB,EAAA,CAAAkB,UAAA,SAAAmD,MAAA,CAAAjB,cAAA,CAAAM,MAAA,OAAiC;IAcjC1D,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAkB,UAAA,SAAAmD,MAAA,CAAAjB,cAAA,CAAAM,MAAA,KAA+B;;;;;;IAlgBpD1D,EAAA,CAAAC,cAAA,aAGC;IAGGD,EAAA,CAAAE,SAAA,aAEO;IAMPF,EAAA,CAAAC,cAAA,aAA4D;IAExDD,EAAA,CAAAE,SAAA,aAAmE;IAWrEF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,cAAiD;IAG7CD,EAAA,CAAAE,SAAA,eAEO;IAKPF,EAAA,CAAAC,cAAA,eAEC;IAOOD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAAI,MAAA,wDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,eAAoC;IAIhCD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAI,MAAA,IAEpC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAE9CH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAgD;IAChDF,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,mBAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAE7CH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAC,cAAA,gBAAsC;IAAAD,EAAA,CAAAI,MAAA,IAEpC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,0BAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAMnDH,EAAA,CAAAC,cAAA,eAEC;IAIGD,EAAA,CAAAE,SAAA,aAAgC;IAAAF,EAAA,CAAAI,MAAA,wBAClC;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAK,UAAA,mBAAAiE,8DAAA;MAAAtE,EAAA,CAAAQ,aAAA,CAAA+D,IAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAA0D,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3BzE,EAAA,CAAAE,SAAA,aAAiC;IAACF,EAAA,CAAAI,MAAA,oCACpC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAqE,8DAAA;MAAA1E,EAAA,CAAAQ,aAAA,CAAA+D,IAAA;MAAA,MAAAI,OAAA,GAAA3E,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAA6D,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC5E,EAAA,CAAAE,SAAA,aAAgC;IAACF,EAAA,CAAAI,MAAA,gCACnC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAAoC;IAEhCD,EAAA,CAAAK,UAAA,mBAAAwE,8DAAA;MAAA7E,EAAA,CAAAQ,aAAA,CAAA+D,IAAA;MAAA,MAAAO,OAAA,GAAA9E,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAgE,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC/E,EAAA,CAAAE,SAAA,aAAsC;IAACF,EAAA,CAAAI,MAAA,gBACzC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA2E,8DAAA;MAAAhF,EAAA,CAAAQ,aAAA,CAAA+D,IAAA;MAAA,MAAAU,OAAA,GAAAjF,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAmE,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAGxBlF,EAAA,CAAAE,SAAA,aAAiC;IAACF,EAAA,CAAAI,MAAA,mBACpC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IASrBH,EAAA,CAAAC,cAAA,eAAsB;IAYRD,EAAA,CAAAE,SAAA,aAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,aAA8B;IAC5BD,EAAA,CAAAI,MAAA,wDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAA0B;IAIQD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAA2C;IAC3CF,EAAA,CAAAI,MAAA,IAMF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAAyD;IAErDD,EAAA,CAAAI,MAAA,IAIF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,eAAyC;IAIrCD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAAyC;IACzCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,aAAiC;IACjCF,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAUrBH,EAAA,CAAAC,cAAA,eAAsB;IAYRD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,8BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAAgC;IAChCF,EAAA,CAAAI,MAAA,2DACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAE,SAAA,wBAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,gBAAsB;IAWVD,EAAA,CAAAE,SAAA,cAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,mCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAI,MAAA,KACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,gBAA2B;IAEzBD,EAAA,CAAAwC,UAAA,MAAA2C,4CAAA,mBAwNM;IAENnF,EAAA,CAAAwC,UAAA,MAAA4C,oDAAA,kCAAApF,EAAA,CAAAqF,sBAAA,CA0Ic;IAChBrF,EAAA,CAAAG,YAAA,EAAM;;;;;IArjBFH,EAAA,CAAAiB,SAAA,IACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAA6D,MAAA,CAAAC,MAAA,CAAAlD,IAAA,MACF;IAW0CrC,EAAA,CAAAiB,SAAA,GAEpC;IAFoCjB,EAAA,CAAA+B,iBAAA,EAAAuD,MAAA,CAAAC,MAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,MAAA,CAAAC,OAAA,CAAA9B,MAAA,OAEpC;IAcoC1D,EAAA,CAAAiB,SAAA,IAEpC;IAFoCjB,EAAA,CAAA+B,iBAAA,CAAAuD,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAC,MAAA,CAAAG,SAAA,EAEpC;IA+EE1F,EAAA,CAAAiB,SAAA,IAMF;IANEjB,EAAA,CAAAyB,kBAAA,aAAA6D,MAAA,CAAAC,MAAA,CAAAI,KAAA,GAAAL,MAAA,CAAA5D,WAAA,CAAA4D,MAAA,CAAAC,MAAA,CAAAI,KAAA,KAAAL,MAAA,CAAAC,MAAA,CAAAI,KAAA,0BAMF;IAKE3F,EAAA,CAAAiB,SAAA,GAIF;IAJEjB,EAAA,CAAAyB,kBAAA,MAAA6D,MAAA,CAAAC,MAAA,CAAAK,WAAA,iEAIF;IASE5F,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,OAAA6D,MAAA,CAAAC,MAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,MAAA,CAAAC,OAAA,CAAA9B,MAAA,oBACF;IAKE1D,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAyB,kBAAA,yBAAA6D,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAC,MAAA,CAAAG,SAAA,OACF;IAuCO1F,EAAA,CAAAiB,SAAA,IAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAoE,MAAA,CAAAC,MAAA,CAAe;IAuB1BvF,EAAA,CAAAiB,SAAA,IACF;IADEjB,EAAA,CAAAyB,kBAAA,MAAA6D,MAAA,CAAA7B,WAAA,CAAAC,MAAA,mBACF;IAMG1D,EAAA,CAAAiB,SAAA,GAA6C;IAA7CjB,EAAA,CAAAkB,UAAA,SAAAoE,MAAA,CAAA7B,WAAA,IAAA6B,MAAA,CAAA7B,WAAA,CAAAC,MAAA,KAA6C,aAAAmC,GAAA;;;;;;IA4W5D7F,EAAA,CAAAC,cAAA,eAA2D;IAOjDD,EAAA,CAAAE,SAAA,aAAsE;IACtEF,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAI,MAAA,kEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAK,UAAA,mBAAAyF,6DAAA;MAAA9F,EAAA,CAAAQ,aAAA,CAAAuF,IAAA;MAAA,MAAAC,OAAA,GAAAhG,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAkF,OAAA,CAAAjB,oBAAA,EAAsB;IAAA,EAAC;IAEhC/E,EAAA,CAAAE,SAAA,aAAqC;IAACF,EAAA,CAAAI,MAAA,iDACxC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;ADpnBjB,OAAM,MAAO8F,qBAAqB;EAUhCC,YACUC,aAA4B,EAC5BC,WAAwB;EAAE;EAC1BC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAf,MAAM,GAAkB,IAAI;IAC5B,KAAAgB,OAAO,GAAG,KAAK;IACf,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAQ;MAAEzE,EAAE,EAAE,EAAE;MAAEJ,IAAI,EAAE;IAAQ,CAAE;IAC3C,KAAAuB,cAAc,GAAW,EAAE;IAC3B,KAAAuD,WAAW,GAA8B,EAAE,CAAC,CAAC;IAC7C,KAAAlD,WAAW,GAAU,EAAE,CAAC,CAAC;EAOtB;;EAEHmD,QAAQA,CAAA;IACN,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAEtD;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjB,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAQ,CAAC;KAC/B,MAAM;MACL,IAAI,CAACD,KAAK,GAAG,0BAA0B;;EAE3C;EAEA;EACAQ,SAASA,CAAA;IACP;IACA;IACA,MAAME,SAAS,GAAW,CACxB;MACElG,GAAG,EAAE,OAAO;MACZmG,QAAQ,EAAE,UAAU;MACpB7E,KAAK,EAAE,kBAAkB;MACzBT,IAAI,EAAE,OAAO;MACbuF,QAAQ,EAAE;KACX,EACD;MACEpG,GAAG,EAAE,OAAO;MACZmG,QAAQ,EAAE,YAAY;MACtB7E,KAAK,EAAE,kBAAkB;MACzBT,IAAI,EAAE,SAAS;MACfuF,QAAQ,EAAE;KACX,EACD;MACEpG,GAAG,EAAE,OAAO;MACZmG,QAAQ,EAAE,YAAY;MACtB7E,KAAK,EAAE,iBAAiB;MACxBT,IAAI,EAAE,SAAS;MACfuF,QAAQ,EAAE;KACX,CACF;IAED;IACAC,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,QAAQ,GAAG,CAAC,GAAGJ,SAAS,CAAC;MAC/BK,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;MAE9D;MACA,IAAI,IAAI,CAAC7D,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;QACnD,MAAM+D,aAAa,GAAG,IAAI,CAAChE,WAAW,CAACiE,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACpG,IAAI,CAAC;QACzD,IAAI,CAAC6B,cAAc,GAAG8D,SAAS,CAACU,MAAM,CACnCrG,IAAI,IAAK,CAACkG,aAAa,CAACI,QAAQ,CAACtG,IAAI,CAACP,GAAG,IAAIO,IAAI,CAACU,EAAE,IAAI,EAAE,CAAC,CAC7D;OACF,MAAM;QACL,IAAI,CAACmB,cAAc,GAAG8D,SAAS;;MAGjCK,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACpE,cAAc,CAAC;MAE7D;MACA,IAAI,IAAI,CAACmC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;QACtC,IAAI,CAACsC,iBAAiB,EAAE;;IAE5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvC,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;IAE1C,IAAI,CAACD,MAAM,CAACC,OAAO,CAACuC,OAAO,CAAEC,QAAQ,IAAI;MACvC,MAAMzG,IAAI,GAAG,IAAI,CAAC6B,cAAc,CAAC6E,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClH,GAAG,KAAKgH,QAAQ,IAAIE,CAAC,CAACjG,EAAE,KAAK+F,QAAQ,CAC/C;MACD,IAAIzG,IAAI,IAAIA,IAAI,CAACc,IAAI,EAAE;QACrB,IAAI,CAACsE,WAAW,CAACqB,QAAQ,CAAC,GAAGzG,IAAI,CAACc,IAAI;OACvC,MAAM;QACL;QACA;QACA;QACA,IAAI,CAACsE,WAAW,CAACqB,QAAQ,CAAC,GAAGA,QAAQ;;IAEzC,CAAC,CAAC;EACJ;EAEA;EACAG,aAAaA,CAACH,QAAgB;IAC5B,OAAO,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAAC,IAAIA,QAAQ;EAC/C;EAEA;EACAtG,WAAWA,CAAC0G,MAA0B;IACpC,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,YAAY;;IAGrB,MAAM7G,IAAI,GAAG,IAAI,CAAC6B,cAAc,CAAC6E,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClH,GAAG,KAAKoH,MAAM,IAAIF,CAAC,CAACjG,EAAE,KAAKmG,MAAM,CAC3C;IACD,IAAI7G,IAAI,EAAE;MACR,IAAIA,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,EAAE;QACnC,OAAO,GAAGb,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,EAAE;OAC5C,MAAM,IAAIb,IAAI,CAACc,IAAI,EAAE;QACpB,OAAOd,IAAI,CAACc,IAAI;;;IAGpB,OAAO+F,MAAM;EACf;EAEA;EACA9G,iBAAiBA,CAAC8G,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;;IAGX,MAAM7G,IAAI,GAAG,IAAI,CAAC6B,cAAc,CAAC6E,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAAClH,GAAG,KAAKoH,MAAM,IAAIF,CAAC,CAACjG,EAAE,KAAKmG,MAAM,CAC3C;IACD,IAAI7G,IAAI,EAAE;MACR,OAAOA,IAAI,CAACgB,UAAU,IAAIhB,IAAI,CAACM,IAAI,IAAI,EAAE;;IAE3C,OAAO,EAAE;EACX;EAEAoF,UAAUA,CAAChF,EAAU;IACnB,IAAI,CAACsE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACL,aAAa,CAACkC,SAAS,CAACpG,EAAE,CAAC,CAACqG,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACbjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,IAAI,CAAC;QACjD,IAAI,CAACjD,MAAM,GAAGiD,IAAI;QAElB;QACA,IAAI,CAACC,eAAe,CAACxG,EAAE,CAAC;QAExB;QACA,IACE,IAAI,CAACsD,MAAM,IACX,IAAI,CAACA,MAAM,CAACC,OAAO,IACnB,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC9B,MAAM,GAAG,CAAC,EAC9B;UACA,IAAI,CAACoE,iBAAiB,EAAE;;QAG1B,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAkC,eAAeA,CAACC,MAAc;IAC5B,IAAI,CAACvC,aAAa,CAACwC,cAAc,CAACD,MAAM,CAAC,CAACJ,SAAS,CAAC;MAClDC,IAAI,EAAG/C,OAAO,IAAI;QAChB+B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEhC,OAAO,CAAC;QACpD,IAAI,CAAC/B,WAAW,GAAG+B,OAAO;MAC5B,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEA5B,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC6B,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACsC,QAAQ,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAACnC,QAAQ,CAAC,CAAC;;EAE9D;EAEA1B,oBAAoBA,CAAA;IAClB,IAAI,CAACuB,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAnE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACgC,QAAQ,EAAE;MACjB,IAAI,CAACH,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACnC,QAAQ,CAAC,CAAC;;EAE3D;EAEA;EACAhB,UAAUA,CAACoD,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,IAAI;MACF,IAAIC,OAAa;MAEjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;OACzB,MAAM;QACLC,OAAO,GAAGD,IAAI;;MAGhB,IAAIG,KAAK,CAACF,OAAO,CAACG,OAAO,EAAE,CAAC,EAAE;QAC5B,OAAO,eAAe;;MAGxB;MACA,OAAOH,OAAO,CAACI,kBAAkB,CAAC,OAAO,EAAE;QACzCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,OAAO7C,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,gBAAgB;;EAE3B;EAEA;EACAxD,SAASA,CAACoF,MAAc,EAAEvG,IAAY;IACpC0F,OAAO,CAACC,GAAG,CAAC,0BAA0BY,MAAM,iBAAiBvG,IAAI,EAAE,CAAC;IAEpE,IAAI,CAAC,IAAI,CAAC4E,QAAQ,IAAI,CAAC2B,MAAM,EAAE;MAC7Bb,OAAO,CAACf,KAAK,CAAC,0CAA0C,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,0CAA0C;MACvD;;IAGF;IACA,MAAM8C,eAAe,GAAG,IAAI,CAAC7F,WAAW,CAAC8F,IAAI,CAAE5B,CAAC,IAAKA,CAAC,CAACpG,IAAI,KAAK6G,MAAM,CAAC;IACvE,IAAIkB,eAAe,EAAE;MACnB,IAAI,CAAC9C,KAAK,GAAG,6CAA6C;MAC1DgD,KAAK,CAAC,6CAA6C,CAAC;MACpD;;IAGF;IACA,MAAMC,MAAM,GAAW;MACrBxH,EAAE,EAAEmG,MAAM;MACVvG,IAAI,EAAEA,IAAI,IAAI;KACf;IAED;IACA,MAAM6H,QAAQ,GAAG,IAAI,CAAChI,WAAW,CAAC0G,MAAM,CAAC;IACzC,MAAMuB,QAAQ,GAAG9H,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QAAQ;IAE/D,IAAI,CAACsE,aAAa,CAACyD,iBAAiB,CAAC,IAAI,CAACnD,QAAQ,EAAEgD,MAAM,CAAC,CAACnB,SAAS,CAAC;MACpEC,IAAI,EAAGsB,QAAQ,IAAI;QACjBtC,OAAO,CAACC,GAAG,CACT,gBAAgBkC,QAAQ,kBAAkBC,QAAQ,eAAe,EACjEE,QAAQ,CACT;QAED;QACAL,KAAK,CAAC,gBAAgBE,QAAQ,kBAAkBC,QAAQ,cAAc,CAAC;QAEvE;QACA,IAAI,CAAClB,eAAe,CAAC,IAAI,CAAChC,QAAS,CAAC;QAEpC;QACA,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAS,CAAC;QAE/B;QACA,IAAI,CAACqD,oBAAoB,EAAE;MAC7B,CAAC;MACDtD,KAAK,EAAGA,KAAK,IAAI;QACfe,OAAO,CAACf,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAG,uCAAuCkD,QAAQ,WAAWC,QAAQ,iCAAiC;QAChHH,KAAK,CAAC,IAAI,CAAChD,KAAK,CAAC;MACnB;KACD,CAAC;EACJ;EAEA;EACAsD,oBAAoBA,CAAA;IAClB;IACA;IACA,IAAI,CAAC9C,SAAS,EAAE;EAClB;EAEA;EACA4C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACnD,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACzE,EAAE,EAAE;MACxCsF,OAAO,CAACf,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAACxD,SAAS,CAAC,IAAI,CAAC0D,SAAS,CAACzE,EAAE,EAAE,IAAI,CAACyE,SAAS,CAAC7E,IAAI,IAAI,QAAQ,CAAC;EACpE;EAEAd,sBAAsBA,CAACiH,QAAgB;IACrCT,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEQ,QAAQ,CAAC;IAExE,IAAI,CAAC,IAAI,CAACvB,QAAQ,EAAE;MAClBc,OAAO,CAACf,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE;;IAGF;IACA,MAAM4B,MAAM,GAAGJ,QAAQ;IAEvB;IACA,MAAM0B,QAAQ,GAAG,IAAI,CAAChI,WAAW,CAAC0G,MAAM,CAAC;IAEzCb,OAAO,CAACC,GAAG,CACT,yCAAyCY,MAAM,KAAKsB,QAAQ,iBAAiB,IAAI,CAACjD,QAAQ,EAAE,CAC7F;IAED,IACEsD,OAAO,CACL,mDAAmDL,QAAQ,gBAAgB,CAC5E,EACD;MACAnC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACjB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CACfpF,sBAAsB,CAAC,IAAI,CAAC0F,QAAQ,EAAE2B,MAAM,CAAC,CAC7CE,SAAS,CAAC;QACTC,IAAI,EAAGsB,QAAQ,IAAI;UACjBtC,OAAO,CAACC,GAAG,CACT,gBAAgBkC,QAAQ,mCAAmC,EAC3DG,QAAQ,CACT;UACD,IAAI,CAACtD,OAAO,GAAG,KAAK;UAEpB;UACAiD,KAAK,CAAC,gBAAgBE,QAAQ,kCAAkC,CAAC;UAEjE;UACA,IAAI,CAACjB,eAAe,CAAC,IAAI,CAAChC,QAAS,CAAC;UAEpC;UACA,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,QAAS,CAAC;UAE/B;UACA,IAAI,CAACqD,oBAAoB,EAAE;QAC7B,CAAC;QACDtD,KAAK,EAAGA,KAAK,IAAI;UACfe,OAAO,CAACf,KAAK,CACX,4CAA4CkD,QAAQ,IAAI,EACxDlD,KAAK,CACN;UACD,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,wCAAwCkD,QAAQ,kBAC3DlD,KAAK,CAACwD,OAAO,IAAI,iBACnB,EAAE;QACJ;OACD,CAAC;KACL,MAAM;MACLzC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEAtC,YAAYA,CAAA;IACVqC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACf,QAAQ,EAAE;MAClBc,OAAO,CAACf,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE;;IAGFe,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACf,QAAQ,CAAC;IAEzD,IACEsD,OAAO,CACL,gDAAgD,IAAI,CAACxE,MAAM,EAAElD,IAAI,mCAAmC,CACrG,EACD;MACAkF,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACjB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI,CAACL,aAAa,CAACjB,YAAY,CAAC,IAAI,CAACuB,QAAQ,CAAC,CAAC6B,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACThB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACjB,OAAO,GAAG,KAAK;UACpBiD,KAAK,CAAC,8BAA8B,CAAC;UACrC,IAAI,CAAClD,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACDpC,KAAK,EAAGA,KAAK,IAAI;UACfe,OAAO,CAACf,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAG,qCACXA,KAAK,CAACwD,OAAO,IAAI,iBACnB,EAAE;UACFR,KAAK,CAAC,kCAAkC,IAAI,CAAChD,KAAK,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACLe,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;;;uBAraWvB,qBAAqB,EAAAjG,EAAA,CAAAiK,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnK,EAAA,CAAAiK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArK,EAAA,CAAAiK,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvK,EAAA,CAAAiK,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAArBvE,qBAAqB;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlC/K,EAAA,CAAAwC,UAAA,IAAAyI,oCAAA,oBA4mBM;UAGNjL,EAAA,CAAAwC,UAAA,IAAA0I,oCAAA,kBAqBM;;;UAloBHlL,EAAA,CAAAkB,UAAA,SAAA8J,GAAA,CAAAzF,MAAA,CAAY;UA6mB6BvF,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAkB,UAAA,UAAA8J,GAAA,CAAAzF,MAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}