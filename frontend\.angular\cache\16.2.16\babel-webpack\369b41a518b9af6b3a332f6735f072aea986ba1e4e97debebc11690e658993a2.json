{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport function validateOperation(operation) {\n  var OPERATION_FIELDS = [\"query\", \"operationName\", \"variables\", \"extensions\", \"context\"];\n  for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {\n    var key = _a[_i];\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(46, key);\n    }\n  }\n  return operation;\n}", "map": {"version": 3, "names": ["newInvariantError", "validateOperation", "operation", "OPERATION_FIELDS", "_i", "_a", "Object", "keys", "length", "key", "indexOf"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/utils/validateOperation.js"], "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport function validateOperation(operation) {\n    var OPERATION_FIELDS = [\n        \"query\",\n        \"operationName\",\n        \"variables\",\n        \"extensions\",\n        \"context\",\n    ];\n    for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {\n        var key = _a[_i];\n        if (OPERATION_FIELDS.indexOf(key) < 0) {\n            throw newInvariantError(46, key);\n        }\n    }\n    return operation;\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kCAAkC;AACpE,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACzC,IAAIC,gBAAgB,GAAG,CACnB,OAAO,EACP,eAAe,EACf,WAAW,EACX,YAAY,EACZ,SAAS,CACZ;EACD,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,EAAEE,EAAE,GAAGC,EAAE,CAACG,MAAM,EAAEJ,EAAE,EAAE,EAAE;IAChE,IAAIK,GAAG,GAAGJ,EAAE,CAACD,EAAE,CAAC;IAChB,IAAID,gBAAgB,CAACO,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,EAAE;MACnC,MAAMT,iBAAiB,CAAC,EAAE,EAAES,GAAG,CAAC;IACpC;EACJ;EACA,OAAOP,SAAS;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}