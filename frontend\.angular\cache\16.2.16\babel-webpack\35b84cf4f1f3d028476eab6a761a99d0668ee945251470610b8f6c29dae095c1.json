{"ast": null, "code": "import { WeakCache, StrongCache } from \"@wry/caches\";\nvar scheduledCleanup = new WeakSet();\nfunction schedule(cache) {\n  if (cache.size <= (cache.max || -1)) {\n    return;\n  }\n  if (!scheduledCleanup.has(cache)) {\n    scheduledCleanup.add(cache);\n    setTimeout(function () {\n      cache.clean();\n      scheduledCleanup.delete(cache);\n    }, 100);\n  }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport var AutoCleanedWeakCache = function (max, dispose) {\n  /*\n  Some builds of `WeakCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  var cache = new WeakCache(max, dispose);\n  cache.set = function (key, value) {\n    var ret = WeakCache.prototype.set.call(this, key, value);\n    schedule(this);\n    return ret;\n  };\n  return cache;\n};\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport var AutoCleanedStrongCache = function (max, dispose) {\n  /*\n  Some builds of `StrongCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  var cache = new StrongCache(max, dispose);\n  cache.set = function (key, value) {\n    var ret = StrongCache.prototype.set.call(this, key, value);\n    schedule(this);\n    return ret;\n  };\n  return cache;\n};", "map": {"version": 3, "names": ["<PERSON>ak<PERSON><PERSON>", "StrongCache", "scheduledCleanup", "WeakSet", "schedule", "cache", "size", "max", "has", "add", "setTimeout", "clean", "delete", "AutoCleanedWeakCache", "dispose", "set", "key", "value", "ret", "prototype", "call", "AutoCleanedStrongCache"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/caching/caches.js"], "sourcesContent": ["import { WeakCache, StrongCache } from \"@wry/caches\";\nvar scheduledCleanup = new WeakSet();\nfunction schedule(cache) {\n    if (cache.size <= (cache.max || -1)) {\n        return;\n    }\n    if (!scheduledCleanup.has(cache)) {\n        scheduledCleanup.add(cache);\n        setTimeout(function () {\n            cache.clean();\n            scheduledCleanup.delete(cache);\n        }, 100);\n    }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport var AutoCleanedWeakCache = function (max, dispose) {\n    /*\n    Some builds of `WeakCache` are function prototypes, some are classes.\n    This library still builds with an ES5 target, so we can't extend the\n    real classes.\n    Instead, we have to use this workaround until we switch to a newer build\n    target.\n    */\n    var cache = new WeakCache(max, dispose);\n    cache.set = function (key, value) {\n        var ret = WeakCache.prototype.set.call(this, key, value);\n        schedule(this);\n        return ret;\n    };\n    return cache;\n};\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport var AutoCleanedStrongCache = function (max, dispose) {\n    /*\n    Some builds of `StrongCache` are function prototypes, some are classes.\n    This library still builds with an ES5 target, so we can't extend the\n    real classes.\n    Instead, we have to use this workaround until we switch to a newer build\n    target.\n    */\n    var cache = new StrongCache(max, dispose);\n    cache.set = function (key, value) {\n        var ret = StrongCache.prototype.set.call(this, key, value);\n        schedule(this);\n        return ret;\n    };\n    return cache;\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,aAAa;AACpD,IAAIC,gBAAgB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACpC,SAASC,QAAQA,CAACC,KAAK,EAAE;EACrB,IAAIA,KAAK,CAACC,IAAI,KAAKD,KAAK,CAACE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;IACjC;EACJ;EACA,IAAI,CAACL,gBAAgB,CAACM,GAAG,CAACH,KAAK,CAAC,EAAE;IAC9BH,gBAAgB,CAACO,GAAG,CAACJ,KAAK,CAAC;IAC3BK,UAAU,CAAC,YAAY;MACnBL,KAAK,CAACM,KAAK,CAAC,CAAC;MACbT,gBAAgB,CAACU,MAAM,CAACP,KAAK,CAAC;IAClC,CAAC,EAAE,GAAG,CAAC;EACX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIQ,oBAAoB,GAAG,SAAAA,CAAUN,GAAG,EAAEO,OAAO,EAAE;EACtD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIT,KAAK,GAAG,IAAIL,SAAS,CAACO,GAAG,EAAEO,OAAO,CAAC;EACvCT,KAAK,CAACU,GAAG,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC9B,IAAIC,GAAG,GAAGlB,SAAS,CAACmB,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,IAAI,EAAEJ,GAAG,EAAEC,KAAK,CAAC;IACxDb,QAAQ,CAAC,IAAI,CAAC;IACd,OAAOc,GAAG;EACd,CAAC;EACD,OAAOb,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgB,sBAAsB,GAAG,SAAAA,CAAUd,GAAG,EAAEO,OAAO,EAAE;EACxD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIT,KAAK,GAAG,IAAIJ,WAAW,CAACM,GAAG,EAAEO,OAAO,CAAC;EACzCT,KAAK,CAACU,GAAG,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC9B,IAAIC,GAAG,GAAGjB,WAAW,CAACkB,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,IAAI,EAAEJ,GAAG,EAAEC,KAAK,CAAC;IAC1Db,QAAQ,CAAC,IAAI,CAAC;IACd,OAAOc,GAAG;EACd,CAAC;EACD,OAAOb,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}