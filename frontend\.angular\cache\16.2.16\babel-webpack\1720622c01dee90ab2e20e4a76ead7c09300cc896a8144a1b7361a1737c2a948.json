{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport class PlanningService {\n  constructor(http, jwtHelper) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n  }\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getAllPlannings() {\n    return this.http.get(`${environment.urlBackend}plannings/getall`);\n  }\n  getPlanningById(id) {\n    return this.http.get(`${environment.urlBackend}plannings/getone/${id}`);\n  }\n  createPlanning(planning) {\n    return this.http.post(`${environment.urlBackend}plannings/add`, planning, {\n      headers: this.getUserHeaders()\n    });\n  }\n  updatePlanning(id, planning) {\n    return this.http.put(`${environment.urlBackend}plannings/update/${id}`, planning, {\n      headers: this.getUserHeaders()\n    });\n  }\n  deletePlanning(id) {\n    return this.http.delete(`${environment.urlBackend}plannings/delete/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningsByUser(userId) {\n    return this.http.get(`${environment.urlBackend}plannings/user/${userId}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningsWithDetails() {\n    return this.http.get(`${environment.urlBackend}plannings/with-details`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningWithReunions(id) {\n    return this.http.get(`${environment.urlBackend}plannings/with-reunions/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  static {\n    this.ɵfac = function PlanningService_Factory(t) {\n      return new (t || PlanningService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PlanningService,\n      factory: PlanningService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "PlanningService", "constructor", "http", "jwtHelper", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getAllPlannings", "get", "urlBackend", "getPlanningById", "id", "createPlanning", "planning", "post", "headers", "updatePlanning", "put", "deletePlanning", "delete", "getPlanningsByUser", "userId", "getPlanningsWithDetails", "getPlanningWithReunions", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\planning.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {HttpHeaders,HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Planning } from '../models/planning.model';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PlanningService {\r\n\r\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService)\r\n  {}\r\n   private getUserHeaders(): HttpHeaders {\r\n     const token = localStorage.getItem('token');\r\n     if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n       throw new Error('Token invalide ou expiré');\r\n     }\r\n     return new HttpHeaders({\r\n       Authorization: `Bearer ${token || ''}`,\r\n       'Content-Type': 'application/json',\r\n     });\r\n   }\r\n  \r\n  getAllPlannings(): Observable<Planning[]> {\r\n    return this.http.get<Planning[]>(`${environment.urlBackend}plannings/getall`);\r\n    \r\n  }\r\n\r\n  getPlanningById(id: string): Observable<Planning> {\r\n    return this.http.get<Planning>(`${environment.urlBackend}plannings/getone/${id}`);\r\n  }\r\n\r\n  createPlanning(planning: Planning): Observable<Planning> {\r\n    return this.http.post<Planning>(`${environment.urlBackend}plannings/add`,planning,{headers: this.getUserHeaders()});\r\n  }\r\n\r\n  updatePlanning(id: string, planning: Planning): Observable<Planning> {\r\n    return this.http.put<Planning>(`${environment.urlBackend}plannings/update/${id}`,planning,{headers: this.getUserHeaders()});\r\n  }\r\n\r\n  deletePlanning(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${environment.urlBackend}plannings/delete/${id}`,{headers: this.getUserHeaders()});\r\n  }\r\n\r\n\r\ngetPlanningsByUser(userId: string): Observable<Planning[]> {\r\n  return this.http.get<Planning[]>(\r\n    `${environment.urlBackend}plannings/user/${userId}`,\r\n    {\r\n      headers: this.getUserHeaders()\r\n    }\r\n  );\r\n}\r\ngetPlanningsWithDetails(): Observable<Planning[]> {\r\n  return this.http.get<Planning[]>(\r\n    `${environment.urlBackend}plannings/with-details`,\r\n    { headers: this.getUserHeaders() }\r\n  );\r\n}\r\n\r\ngetPlanningWithReunions(id: string): Observable<Planning> {\r\n  return this.http.get<Planning>(\r\n    `${environment.urlBackend}plannings/with-reunions/${id}`,\r\n    { headers: this.getUserHeaders() }\r\n  );\r\n}\r\n}"], "mappings": "AACA,SAAQA,WAAW,QAAmB,sBAAsB;AAE5D,SAASC,WAAW,QAAQ,8BAA8B;;;;AAO1D,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EACtD;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIX,WAAW,CAAC;MACrBY,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEDM,eAAeA,CAAA;IACb,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAAa,GAAGb,WAAW,CAACc,UAAU,kBAAkB,CAAC;EAE/E;EAEAC,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACb,IAAI,CAACU,GAAG,CAAW,GAAGb,WAAW,CAACc,UAAU,oBAAoBE,EAAE,EAAE,CAAC;EACnF;EAEAC,cAAcA,CAACC,QAAkB;IAC/B,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAW,GAAGnB,WAAW,CAACc,UAAU,eAAe,EAACI,QAAQ,EAAC;MAACE,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EACrH;EAEAgB,cAAcA,CAACL,EAAU,EAAEE,QAAkB;IAC3C,OAAO,IAAI,CAACf,IAAI,CAACmB,GAAG,CAAW,GAAGtB,WAAW,CAACc,UAAU,oBAAoBE,EAAE,EAAE,EAACE,QAAQ,EAAC;MAACE,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EAC7H;EAEAkB,cAAcA,CAACP,EAAU;IACvB,OAAO,IAAI,CAACb,IAAI,CAACqB,MAAM,CAAO,GAAGxB,WAAW,CAACc,UAAU,oBAAoBE,EAAE,EAAE,EAAC;MAACI,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAC,CAAC;EACnH;EAGFoB,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAACvB,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,kBAAkBY,MAAM,EAAE,EACnD;MACEN,OAAO,EAAE,IAAI,CAACf,cAAc;KAC7B,CACF;EACH;EACAsB,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACxB,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,wBAAwB,EACjD;MAAEM,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAE,CACnC;EACH;EAEAuB,uBAAuBA,CAACZ,EAAU;IAChC,OAAO,IAAI,CAACb,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,2BAA2BE,EAAE,EAAE,EACxD;MAAEI,OAAO,EAAE,IAAI,CAACf,cAAc;IAAE,CAAE,CACnC;EACH;;;uBAzDaJ,eAAe,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAfjC,eAAe;MAAAkC,OAAA,EAAflC,eAAe,CAAAmC,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}