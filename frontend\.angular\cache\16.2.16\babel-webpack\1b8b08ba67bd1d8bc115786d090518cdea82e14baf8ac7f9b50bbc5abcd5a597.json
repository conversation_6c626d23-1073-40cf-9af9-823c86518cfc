{"ast": null, "code": "import { <PERSON><PERSON>L<PERSON>_OPTIONS, ApolloModule } from 'apollo-angular';\nimport { InMemoryCache, split, ApolloClient } from '@apollo/client/core';\nimport { HttpLink } from 'apollo-angular/http';\nimport { GraphQLWsLink } from '@apollo/client/link/subscriptions';\nimport { getMainDefinition } from '@apollo/client/utilities';\nimport { environment } from '../environments/environment';\nimport { ApolloLink, from } from '@apollo/client/core';\nimport { HttpHeaders } from '@angular/common/http';\nimport { AuthuserService } from './services/authuser.service';\nimport { createClient } from 'graphql-ws';\nimport * as ApolloUploadClient from 'apollo-upload-client';\nimport { onError } from '@apollo/client/link/error';\nimport { BehaviorSubject } from 'rxjs';\nimport { typeDefs } from './graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/authuser.service\";\nimport * as i2 from \"apollo-angular/http\";\nconst createUploadLink = ApolloUploadClient.createUploadLink;\n// Service pour gérer le client Apollo\nexport class GraphQLClientService {\n  constructor() {\n    this.client = null;\n    this.clientSubject = new BehaviorSubject(null);\n    this.client$ = this.clientSubject.asObservable();\n  }\n  setClient(client) {\n    this.client = client;\n    this.clientSubject.next(client);\n  }\n  getClient() {\n    return this.client;\n  }\n  static {\n    this.ɵfac = function GraphQLClientService_Factory(t) {\n      return new (t || GraphQLClientService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GraphQLClientService,\n      factory: GraphQLClientService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n// Fonction pour créer le client Apollo\nexport function createApollo(httpLink, authService) {\n  // Récupérer le token à chaque requête plutôt qu'à l'initialisation\n  const getToken = () => authService.getToken();\n  const httpUri = `${environment.urlBackend.replace('/api/', '/graphql')}`;\n  const wsUri = httpUri.replace('http', 'ws');\n  // Lien d'erreur pour gérer les erreurs GraphQL\n  const errorLink = onError(({\n    graphQLErrors,\n    networkError,\n    operation,\n    forward\n  }) => {\n    if (graphQLErrors) {\n      for (const err of graphQLErrors) {\n        // Conserver uniquement les erreurs importantes\n        if (err.message.includes('Enum \"MessageType\" cannot represent value')) {\n          // Ignorer cette erreur spécifique\n        } else {\n          console.error(`[GraphQL error]: Message: ${err.message}, Location: ${err.locations}, Path: ${err.path}`, err);\n        }\n        // Gérer les erreurs d'authentification\n        if (err.extensions?.['code'] === 'UNAUTHENTICATED') {\n          // Afficher uniquement en développement\n          if (environment.production === false) {\n            console.warn('Authentication token is invalid or expired');\n          }\n          // Rediriger vers la page de connexion ou rafraîchir le token\n        }\n      }\n    }\n\n    if (networkError) {\n      // Filtrer certaines erreurs réseau\n      const errorMessage = networkError.toString();\n      if (!errorMessage.includes('cors') && !errorMessage.includes('Failed to fetch')) {\n        console.error(`[Network error]: ${networkError}`);\n      }\n    }\n    return forward(operation);\n  });\n  // Lien pour transformer les variables et s'assurer que les énumérations sont correctement envoyées\n  const transformVariablesLink = new ApolloLink((operation, forward) => {\n    const operationName = operation.operationName;\n    const variables = operation.variables;\n    // Traitement spécial pour les mutations SendMessage\n    if (operationName === 'SendMessage' && variables['type']) {\n      // S'assurer que le type est bien une valeur d'énumération en majuscules\n      if (typeof variables['type'] === 'string' && variables['type'].toLowerCase() === 'text') {\n        variables['type'] = 'TEXT';\n      }\n      // Logs activés\n      console.log(`[GraphQL] SendMessage operation variables after transform:`, variables);\n    }\n    return forward(operation);\n  });\n  // Lien d'authentification pour ajouter le token à chaque requête\n  const authLink = new ApolloLink((operation, forward) => {\n    const token = getToken();\n    // Log des variables de l'opération pour le débogage\n    const operationName = operation.operationName;\n    const variables = operation.variables;\n    if (operationName === 'SendMessage') {\n      console.log(`[GraphQL] SendMessage operation variables:`, variables);\n    }\n    operation.setContext(({\n      headers = {}\n    }) => ({\n      headers: {\n        ...headers,\n        authorization: token ? `Bearer ${token}` : ''\n      }\n    }));\n    return forward(operation);\n  });\n  // Créer le lien pour l'upload de fichiers\n  const uploadLink = createUploadLink({\n    uri: httpUri,\n    headers: {\n      'Apollo-Require-Preflight': 'true'\n    }\n    // Les en-têtes d'authentification seront ajoutés par authLink\n  });\n  // Créer le lien HTTP standard\n  const http = httpLink.create({\n    uri: httpUri,\n    headers: new HttpHeaders({\n      'Content-Type': 'application/json'\n    })\n    // Les en-têtes d'authentification seront ajoutés par authLink\n  });\n  // Combiner les liens pour gérer les uploads\n  const httpLinkSplit = split(({\n    query,\n    variables\n  }) => {\n    const definition = getMainDefinition(query);\n    return definition.kind === 'OperationDefinition' && definition.operation === 'mutation' && variables?.['file'] !== undefined;\n  }, uploadLink, http);\n  // Combiner tous les liens HTTP\n  let link = from([transformVariablesLink, authLink, errorLink, httpLinkSplit]);\n  // Ajouter le lien WebSocket pour les souscriptions\n  if (typeof window !== 'undefined') {\n    try {\n      const wsClient = createClient({\n        url: wsUri,\n        connectionParams: () => {\n          const token = getToken();\n          const userId = authService.getCurrentUserId();\n          if (!token) {\n            // console.warn('No token available for WebSocket connection');\n            return {};\n          }\n          // Log désactivé\n          // console.debug(\n          //   'Setting up WebSocket connection with token and userId:',\n          //   userId\n          // );\n          return {\n            authorization: `Bearer ${token}`,\n            userId: userId\n          };\n        },\n        shouldRetry: err => {\n          // Conserver uniquement les erreurs importantes\n          if (err && err.toString().includes('critical')) {\n            console.error('WebSocket critical error:', err);\n          }\n          return true;\n        },\n        retryAttempts: 10,\n        keepAlive: 30000,\n        on: {\n          connected: () => {\n            /* WebSocket connected successfully */\n          },\n          error: err => console.error('WebSocket connection error:', err),\n          closed: () => {\n            /* WebSocket connection closed */\n          },\n          connecting: () => {\n            /* WebSocket connecting... */\n          },\n          ping: () => {\n            /* Ping/Pong events */\n          }\n        }\n      });\n      const wsLink = new GraphQLWsLink(wsClient);\n      link = split(({\n        query\n      }) => {\n        const definition = getMainDefinition(query);\n        return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';\n      }, wsLink, link);\n    } catch (error) {\n      // Afficher uniquement en développement\n      if (environment.production === false) {\n        console.error('WebSocket initialization failed:', error);\n      }\n    }\n  }\n  return {\n    link,\n    cache: new InMemoryCache({\n      addTypename: false,\n      typePolicies: {\n        // Configuration pour gérer les uploads de fichiers\n        Upload: {\n          merge: true\n        }\n      }\n    }),\n    typeDefs,\n    defaultOptions: {\n      watchQuery: {\n        fetchPolicy: 'cache-and-network',\n        errorPolicy: 'ignore'\n      },\n      query: {\n        fetchPolicy: 'network-only',\n        errorPolicy: 'all'\n      },\n      mutate: {\n        errorPolicy: 'all'\n      }\n    }\n  };\n}\nexport class GraphQLModule {\n  constructor(graphQLClientService, authService, httpLink) {\n    this.graphQLClientService = graphQLClientService;\n    this.authService = authService;\n    this.httpLink = httpLink;\n    // Écouter les changements d'authentification pour recréer le client Apollo si nécessaire\n    this.authService.authChange$.subscribe(() => {\n      // Log désactivé\n      // console.log(\n      //   `Auth change detected: ${type}, token: ${token ? 'present' : 'absent'}`\n      // );\n      // Recréer le client Apollo avec les nouvelles informations d'authentification\n      const newClient = new ApolloClient(createApollo(this.httpLink, this.authService));\n      this.graphQLClientService.setClient(newClient);\n      // console.log(`Apollo client recreated after ${type}`);\n    });\n  }\n\n  static {\n    this.ɵfac = function GraphQLModule_Factory(t) {\n      return new (t || GraphQLModule)(i0.ɵɵinject(GraphQLClientService), i0.ɵɵinject(i1.AuthuserService), i0.ɵɵinject(i2.HttpLink));\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GraphQLModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [GraphQLClientService, {\n        provide: APOLLO_OPTIONS,\n        useFactory: createApollo,\n        deps: [HttpLink, AuthuserService]\n      }],\n      imports: [ApolloModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GraphQLModule, {\n    exports: [ApolloModule]\n  });\n})();", "map": {"version": 3, "names": ["APOLLO_OPTIONS", "ApolloModule", "InMemoryCache", "split", "ApolloClient", "HttpLink", "GraphQLWsLink", "getMainDefinition", "environment", "ApolloLink", "from", "HttpHeaders", "AuthuserService", "createClient", "ApolloUploadClient", "onError", "BehaviorSubject", "typeDefs", "createUploadLink", "GraphQLClientService", "constructor", "client", "clientSubject", "client$", "asObservable", "setClient", "next", "getClient", "factory", "ɵfac", "providedIn", "createApollo", "httpLink", "authService", "getToken", "httpUri", "urlBackend", "replace", "wsUri", "errorLink", "graphQLErrors", "networkError", "operation", "forward", "err", "message", "includes", "console", "error", "locations", "path", "extensions", "production", "warn", "errorMessage", "toString", "transformVariablesLink", "operationName", "variables", "toLowerCase", "log", "authLink", "token", "setContext", "headers", "authorization", "uploadLink", "uri", "http", "create", "httpLinkSplit", "query", "definition", "kind", "undefined", "link", "window", "wsClient", "url", "connectionParams", "userId", "getCurrentUserId", "shouldRetry", "retryAttempts", "keepAlive", "on", "connected", "closed", "connecting", "ping", "wsLink", "cache", "addTypename", "typePolicies", "Upload", "merge", "defaultOptions", "watch<PERSON><PERSON>y", "fetchPolicy", "errorPolicy", "mutate", "GraphQLModule", "graphQLClientService", "authChange$", "subscribe", "newClient", "i0", "ɵɵinject", "i1", "i2", "provide", "useFactory", "deps", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\graphql.module.ts"], "sourcesContent": ["import { NgModule, Injectable } from '@angular/core';\r\nimport { APOLLO_OPTIONS, ApolloModule } from 'apollo-angular';\r\nimport {\r\n  ApolloClientOptions,\r\n  InMemoryCache,\r\n  split,\r\n  ApolloClient,\r\n} from '@apollo/client/core';\r\nimport { HttpLink } from 'apollo-angular/http';\r\nimport { GraphQLWsLink } from '@apollo/client/link/subscriptions';\r\nimport { getMainDefinition } from '@apollo/client/utilities';\r\nimport { environment } from '../environments/environment';\r\nimport { ApolloLink, from } from '@apollo/client/core';\r\nimport { HttpHeaders } from '@angular/common/http';\r\nimport { AuthuserService } from './services/authuser.service';\r\nimport { createClient } from 'graphql-ws';\r\nimport * as ApolloUploadClient from 'apollo-upload-client';\r\nimport { onError } from '@apollo/client/link/error';\r\nimport { Observable, BehaviorSubject } from 'rxjs';\r\nimport { typeDefs } from './graphql/message.graphql';\r\nconst createUploadLink = ApolloUploadClient.createUploadLink;\r\n// Service pour gérer le client Apollo\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GraphQLClientService {\r\n  private client: ApolloClient<any> | null = null;\r\n  private clientSubject = new BehaviorSubject<ApolloClient<any> | null>(null);\r\n  public client$ = this.clientSubject.asObservable();\r\n  constructor() {}\r\n  setClient(client: ApolloClient<any>) {\r\n    this.client = client;\r\n    this.clientSubject.next(client);\r\n  }\r\n  getClient(): ApolloClient<any> | null {\r\n    return this.client;\r\n  }\r\n}\r\n// Fonction pour créer le client Apollo\r\nexport function createApollo(\r\n  httpLink: HttpLink,\r\n  authService: AuthuserService\r\n): ApolloClientOptions<any> {\r\n  // Récupérer le token à chaque requête plutôt qu'à l'initialisation\r\n  const getToken = () => authService.getToken();\r\n  const httpUri = `${environment.urlBackend.replace('/api/', '/graphql')}`;\r\n  const wsUri = httpUri.replace('http', 'ws');\r\n\r\n  // Lien d'erreur pour gérer les erreurs GraphQL\r\n  const errorLink = onError(\r\n    ({ graphQLErrors, networkError, operation, forward }) => {\r\n      if (graphQLErrors) {\r\n        for (const err of graphQLErrors) {\r\n          // Conserver uniquement les erreurs importantes\r\n          if (\r\n            err.message.includes('Enum \"MessageType\" cannot represent value')\r\n          ) {\r\n            // Ignorer cette erreur spécifique\r\n          } else {\r\n            console.error(\r\n              `[GraphQL error]: Message: ${err.message}, Location: ${err.locations}, Path: ${err.path}`,\r\n              err\r\n            );\r\n          }\r\n\r\n          // Gérer les erreurs d'authentification\r\n          if (err.extensions?.['code'] === 'UNAUTHENTICATED') {\r\n            // Afficher uniquement en développement\r\n            if (environment.production === false) {\r\n              console.warn('Authentication token is invalid or expired');\r\n            }\r\n            // Rediriger vers la page de connexion ou rafraîchir le token\r\n          }\r\n        }\r\n      }\r\n\r\n      if (networkError) {\r\n        // Filtrer certaines erreurs réseau\r\n        const errorMessage = networkError.toString();\r\n        if (\r\n          !errorMessage.includes('cors') &&\r\n          !errorMessage.includes('Failed to fetch')\r\n        ) {\r\n          console.error(`[Network error]: ${networkError}`);\r\n        }\r\n      }\r\n\r\n      return forward(operation);\r\n    }\r\n  );\r\n  // Lien pour transformer les variables et s'assurer que les énumérations sont correctement envoyées\r\n  const transformVariablesLink = new ApolloLink((operation, forward) => {\r\n    const operationName = operation.operationName;\r\n    const variables = operation.variables as Record<string, any>;\r\n\r\n    // Traitement spécial pour les mutations SendMessage\r\n    if (operationName === 'SendMessage' && variables['type']) {\r\n      // S'assurer que le type est bien une valeur d'énumération en majuscules\r\n      if (\r\n        typeof variables['type'] === 'string' &&\r\n        variables['type'].toLowerCase() === 'text'\r\n      ) {\r\n        variables['type'] = 'TEXT';\r\n      }\r\n      // Logs activés\r\n      console.log(\r\n        `[GraphQL] SendMessage operation variables after transform:`,\r\n        variables\r\n      );\r\n    }\r\n\r\n    return forward(operation);\r\n  });\r\n\r\n  // Lien d'authentification pour ajouter le token à chaque requête\r\n  const authLink = new ApolloLink((operation, forward) => {\r\n    const token = getToken();\r\n\r\n    // Log des variables de l'opération pour le débogage\r\n    const operationName = operation.operationName;\r\n    const variables = operation.variables;\r\n    if (operationName === 'SendMessage') {\r\n      console.log(`[GraphQL] SendMessage operation variables:`, variables);\r\n    }\r\n\r\n    operation.setContext(({ headers = {} }) => ({\r\n      headers: {\r\n        ...headers,\r\n        authorization: token ? `Bearer ${token}` : '',\r\n      },\r\n    }));\r\n\r\n    return forward(operation);\r\n  });\r\n  // Créer le lien pour l'upload de fichiers\r\n  const uploadLink = createUploadLink({\r\n    uri: httpUri,\r\n    headers: {\r\n      'Apollo-Require-Preflight': 'true',\r\n    },\r\n    // Les en-têtes d'authentification seront ajoutés par authLink\r\n  });\r\n  // Créer le lien HTTP standard\r\n  const http = httpLink.create({\r\n    uri: httpUri,\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    }),\r\n    // Les en-têtes d'authentification seront ajoutés par authLink\r\n  });\r\n\r\n  // Combiner les liens pour gérer les uploads\r\n  const httpLinkSplit = split(\r\n    ({ query, variables }) => {\r\n      const definition = getMainDefinition(query);\r\n      return (\r\n        definition.kind === 'OperationDefinition' &&\r\n        definition.operation === 'mutation' &&\r\n        variables?.['file'] !== undefined\r\n      );\r\n    },\r\n    uploadLink,\r\n    http\r\n  );\r\n\r\n  // Combiner tous les liens HTTP\r\n  let link: ApolloLink = from([\r\n    transformVariablesLink,\r\n    authLink,\r\n    errorLink,\r\n    httpLinkSplit,\r\n  ]);\r\n\r\n  // Ajouter le lien WebSocket pour les souscriptions\r\n  if (typeof window !== 'undefined') {\r\n    try {\r\n      const wsClient = createClient({\r\n        url: wsUri,\r\n        connectionParams: () => {\r\n          const token = getToken();\r\n          const userId = authService.getCurrentUserId();\r\n\r\n          if (!token) {\r\n            // console.warn('No token available for WebSocket connection');\r\n            return {};\r\n          }\r\n\r\n          // Log désactivé\r\n          // console.debug(\r\n          //   'Setting up WebSocket connection with token and userId:',\r\n          //   userId\r\n          // );\r\n          return {\r\n            authorization: `Bearer ${token}`,\r\n            userId: userId,\r\n          };\r\n        },\r\n        shouldRetry: (err) => {\r\n          // Conserver uniquement les erreurs importantes\r\n          if (err && err.toString().includes('critical')) {\r\n            console.error('WebSocket critical error:', err);\r\n          }\r\n          return true;\r\n        },\r\n        retryAttempts: 10,\r\n        keepAlive: 30000,\r\n        on: {\r\n          connected: () => {\r\n            /* WebSocket connected successfully */\r\n          },\r\n          error: (err) => console.error('WebSocket connection error:', err),\r\n          closed: () => {\r\n            /* WebSocket connection closed */\r\n          },\r\n          connecting: () => {\r\n            /* WebSocket connecting... */\r\n          },\r\n          ping: () => {\r\n            /* Ping/Pong events */\r\n          },\r\n        },\r\n      });\r\n\r\n      const wsLink = new GraphQLWsLink(wsClient);\r\n\r\n      link = split(\r\n        ({ query }) => {\r\n          const definition = getMainDefinition(query);\r\n          return (\r\n            definition.kind === 'OperationDefinition' &&\r\n            definition.operation === 'subscription'\r\n          );\r\n        },\r\n        wsLink,\r\n        link\r\n      );\r\n    } catch (error) {\r\n      // Afficher uniquement en développement\r\n      if (environment.production === false) {\r\n        console.error('WebSocket initialization failed:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return {\r\n    link,\r\n    cache: new InMemoryCache({\r\n      addTypename: false,\r\n      typePolicies: {\r\n        // Configuration pour gérer les uploads de fichiers\r\n        Upload: {\r\n          merge: true,\r\n        },\r\n      },\r\n    }),\r\n    typeDefs, // Ajouter les définitions de types GraphQL\r\n    defaultOptions: {\r\n      watchQuery: {\r\n        fetchPolicy: 'cache-and-network',\r\n        errorPolicy: 'ignore',\r\n      },\r\n      query: {\r\n        fetchPolicy: 'network-only',\r\n        errorPolicy: 'all',\r\n      },\r\n      mutate: {\r\n        errorPolicy: 'all',\r\n      },\r\n    },\r\n  };\r\n}\r\n@NgModule({\r\n  exports: [ApolloModule],\r\n  providers: [\r\n    GraphQLClientService,\r\n    {\r\n      provide: APOLLO_OPTIONS,\r\n      useFactory: createApollo,\r\n      deps: [HttpLink, AuthuserService],\r\n    },\r\n  ],\r\n})\r\nexport class GraphQLModule {\r\n  constructor(\r\n    private graphQLClientService: GraphQLClientService,\r\n    private authService: AuthuserService,\r\n    private httpLink: HttpLink\r\n  ) {\r\n    // Écouter les changements d'authentification pour recréer le client Apollo si nécessaire\r\n    this.authService.authChange$.subscribe(() => {\r\n      // Log désactivé\r\n      // console.log(\r\n      //   `Auth change detected: ${type}, token: ${token ? 'present' : 'absent'}`\r\n      // );\r\n\r\n      // Recréer le client Apollo avec les nouvelles informations d'authentification\r\n      const newClient = new ApolloClient(\r\n        createApollo(this.httpLink, this.authService)\r\n      );\r\n      this.graphQLClientService.setClient(newClient);\r\n\r\n      // console.log(`Apollo client recreated after ${type}`);\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC7D,SAEEC,aAAa,EACbC,KAAK,EACLC,YAAY,QACP,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,UAAU,EAAEC,IAAI,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,YAAY,QAAQ,YAAY;AACzC,OAAO,KAAKC,kBAAkB,MAAM,sBAAsB;AAC1D,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAAqBC,eAAe,QAAQ,MAAM;AAClD,SAASC,QAAQ,QAAQ,2BAA2B;;;;AACpD,MAAMC,gBAAgB,GAAGJ,kBAAkB,CAACI,gBAAgB;AAC5D;AAIA,OAAM,MAAOC,oBAAoB;EAI/BC,YAAA;IAHQ,KAAAC,MAAM,GAA6B,IAAI;IACvC,KAAAC,aAAa,GAAG,IAAIN,eAAe,CAA2B,IAAI,CAAC;IACpE,KAAAO,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;EACnC;EACfC,SAASA,CAACJ,MAAyB;IACjC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,aAAa,CAACI,IAAI,CAACL,MAAM,CAAC;EACjC;EACAM,SAASA,CAAA;IACP,OAAO,IAAI,CAACN,MAAM;EACpB;;;uBAXWF,oBAAoB;IAAA;EAAA;;;aAApBA,oBAAoB;MAAAS,OAAA,EAApBT,oBAAoB,CAAAU,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA;;AAepB;AACA,OAAM,SAAUC,YAAYA,CAC1BC,QAAkB,EAClBC,WAA4B;EAE5B;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAMD,WAAW,CAACC,QAAQ,EAAE;EAC7C,MAAMC,OAAO,GAAG,GAAG3B,WAAW,CAAC4B,UAAU,CAACC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;EACxE,MAAMC,KAAK,GAAGH,OAAO,CAACE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;EAE3C;EACA,MAAME,SAAS,GAAGxB,OAAO,CACvB,CAAC;IAAEyB,aAAa;IAAEC,YAAY;IAAEC,SAAS;IAAEC;EAAO,CAAE,KAAI;IACtD,IAAIH,aAAa,EAAE;MACjB,KAAK,MAAMI,GAAG,IAAIJ,aAAa,EAAE;QAC/B;QACA,IACEI,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,2CAA2C,CAAC,EACjE;UACA;QAAA,CACD,MAAM;UACLC,OAAO,CAACC,KAAK,CACX,6BAA6BJ,GAAG,CAACC,OAAO,eAAeD,GAAG,CAACK,SAAS,WAAWL,GAAG,CAACM,IAAI,EAAE,EACzFN,GAAG,CACJ;;QAGH;QACA,IAAIA,GAAG,CAACO,UAAU,GAAG,MAAM,CAAC,KAAK,iBAAiB,EAAE;UAClD;UACA,IAAI3C,WAAW,CAAC4C,UAAU,KAAK,KAAK,EAAE;YACpCL,OAAO,CAACM,IAAI,CAAC,4CAA4C,CAAC;;UAE5D;;;;;IAKN,IAAIZ,YAAY,EAAE;MAChB;MACA,MAAMa,YAAY,GAAGb,YAAY,CAACc,QAAQ,EAAE;MAC5C,IACE,CAACD,YAAY,CAACR,QAAQ,CAAC,MAAM,CAAC,IAC9B,CAACQ,YAAY,CAACR,QAAQ,CAAC,iBAAiB,CAAC,EACzC;QACAC,OAAO,CAACC,KAAK,CAAC,oBAAoBP,YAAY,EAAE,CAAC;;;IAIrD,OAAOE,OAAO,CAACD,SAAS,CAAC;EAC3B,CAAC,CACF;EACD;EACA,MAAMc,sBAAsB,GAAG,IAAI/C,UAAU,CAAC,CAACiC,SAAS,EAAEC,OAAO,KAAI;IACnE,MAAMc,aAAa,GAAGf,SAAS,CAACe,aAAa;IAC7C,MAAMC,SAAS,GAAGhB,SAAS,CAACgB,SAAgC;IAE5D;IACA,IAAID,aAAa,KAAK,aAAa,IAAIC,SAAS,CAAC,MAAM,CAAC,EAAE;MACxD;MACA,IACE,OAAOA,SAAS,CAAC,MAAM,CAAC,KAAK,QAAQ,IACrCA,SAAS,CAAC,MAAM,CAAC,CAACC,WAAW,EAAE,KAAK,MAAM,EAC1C;QACAD,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;;MAE5B;MACAX,OAAO,CAACa,GAAG,CACT,4DAA4D,EAC5DF,SAAS,CACV;;IAGH,OAAOf,OAAO,CAACD,SAAS,CAAC;EAC3B,CAAC,CAAC;EAEF;EACA,MAAMmB,QAAQ,GAAG,IAAIpD,UAAU,CAAC,CAACiC,SAAS,EAAEC,OAAO,KAAI;IACrD,MAAMmB,KAAK,GAAG5B,QAAQ,EAAE;IAExB;IACA,MAAMuB,aAAa,GAAGf,SAAS,CAACe,aAAa;IAC7C,MAAMC,SAAS,GAAGhB,SAAS,CAACgB,SAAS;IACrC,IAAID,aAAa,KAAK,aAAa,EAAE;MACnCV,OAAO,CAACa,GAAG,CAAC,4CAA4C,EAAEF,SAAS,CAAC;;IAGtEhB,SAAS,CAACqB,UAAU,CAAC,CAAC;MAAEC,OAAO,GAAG;IAAE,CAAE,MAAM;MAC1CA,OAAO,EAAE;QACP,GAAGA,OAAO;QACVC,aAAa,EAAEH,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;;KAE9C,CAAC,CAAC;IAEH,OAAOnB,OAAO,CAACD,SAAS,CAAC;EAC3B,CAAC,CAAC;EACF;EACA,MAAMwB,UAAU,GAAGhD,gBAAgB,CAAC;IAClCiD,GAAG,EAAEhC,OAAO;IACZ6B,OAAO,EAAE;MACP,0BAA0B,EAAE;;IAE9B;GACD,CAAC;EACF;EACA,MAAMI,IAAI,GAAGpC,QAAQ,CAACqC,MAAM,CAAC;IAC3BF,GAAG,EAAEhC,OAAO;IACZ6B,OAAO,EAAE,IAAIrD,WAAW,CAAC;MACvB,cAAc,EAAE;KACjB;IACD;GACD,CAAC;EAEF;EACA,MAAM2D,aAAa,GAAGnE,KAAK,CACzB,CAAC;IAAEoE,KAAK;IAAEb;EAAS,CAAE,KAAI;IACvB,MAAMc,UAAU,GAAGjE,iBAAiB,CAACgE,KAAK,CAAC;IAC3C,OACEC,UAAU,CAACC,IAAI,KAAK,qBAAqB,IACzCD,UAAU,CAAC9B,SAAS,KAAK,UAAU,IACnCgB,SAAS,GAAG,MAAM,CAAC,KAAKgB,SAAS;EAErC,CAAC,EACDR,UAAU,EACVE,IAAI,CACL;EAED;EACA,IAAIO,IAAI,GAAejE,IAAI,CAAC,CAC1B8C,sBAAsB,EACtBK,QAAQ,EACRtB,SAAS,EACT+B,aAAa,CACd,CAAC;EAEF;EACA,IAAI,OAAOM,MAAM,KAAK,WAAW,EAAE;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAGhE,YAAY,CAAC;QAC5BiE,GAAG,EAAExC,KAAK;QACVyC,gBAAgB,EAAEA,CAAA,KAAK;UACrB,MAAMjB,KAAK,GAAG5B,QAAQ,EAAE;UACxB,MAAM8C,MAAM,GAAG/C,WAAW,CAACgD,gBAAgB,EAAE;UAE7C,IAAI,CAACnB,KAAK,EAAE;YACV;YACA,OAAO,EAAE;;UAGX;UACA;UACA;UACA;UACA;UACA,OAAO;YACLG,aAAa,EAAE,UAAUH,KAAK,EAAE;YAChCkB,MAAM,EAAEA;WACT;QACH,CAAC;QACDE,WAAW,EAAGtC,GAAG,IAAI;UACnB;UACA,IAAIA,GAAG,IAAIA,GAAG,CAACW,QAAQ,EAAE,CAACT,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC9CC,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEJ,GAAG,CAAC;;UAEjD,OAAO,IAAI;QACb,CAAC;QACDuC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE;UACFC,SAAS,EAAEA,CAAA,KAAK;YACd;UAAA,CACD;UACDtC,KAAK,EAAGJ,GAAG,IAAKG,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEJ,GAAG,CAAC;UACjE2C,MAAM,EAAEA,CAAA,KAAK;YACX;UAAA,CACD;UACDC,UAAU,EAAEA,CAAA,KAAK;YACf;UAAA,CACD;UACDC,IAAI,EAAEA,CAAA,KAAK;YACT;UAAA;;OAGL,CAAC;MAEF,MAAMC,MAAM,GAAG,IAAIpF,aAAa,CAACuE,QAAQ,CAAC;MAE1CF,IAAI,GAAGxE,KAAK,CACV,CAAC;QAAEoE;MAAK,CAAE,KAAI;QACZ,MAAMC,UAAU,GAAGjE,iBAAiB,CAACgE,KAAK,CAAC;QAC3C,OACEC,UAAU,CAACC,IAAI,KAAK,qBAAqB,IACzCD,UAAU,CAAC9B,SAAS,KAAK,cAAc;MAE3C,CAAC,EACDgD,MAAM,EACNf,IAAI,CACL;KACF,CAAC,OAAO3B,KAAK,EAAE;MACd;MACA,IAAIxC,WAAW,CAAC4C,UAAU,KAAK,KAAK,EAAE;QACpCL,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;;;EAK9D,OAAO;IACL2B,IAAI;IACJgB,KAAK,EAAE,IAAIzF,aAAa,CAAC;MACvB0F,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;QACZ;QACAC,MAAM,EAAE;UACNC,KAAK,EAAE;;;KAGZ,CAAC;IACF9E,QAAQ;IACR+E,cAAc,EAAE;MACdC,UAAU,EAAE;QACVC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE;OACd;MACD5B,KAAK,EAAE;QACL2B,WAAW,EAAE,cAAc;QAC3BC,WAAW,EAAE;OACd;MACDC,MAAM,EAAE;QACND,WAAW,EAAE;;;GAGlB;AACH;AAYA,OAAM,MAAOE,aAAa;EACxBjF,YACUkF,oBAA0C,EAC1CrE,WAA4B,EAC5BD,QAAkB;IAFlB,KAAAsE,oBAAoB,GAApBA,oBAAoB;IACpB,KAAArE,WAAW,GAAXA,WAAW;IACX,KAAAD,QAAQ,GAARA,QAAQ;IAEhB;IACA,IAAI,CAACC,WAAW,CAACsE,WAAW,CAACC,SAAS,CAAC,MAAK;MAC1C;MACA;MACA;MACA;MAEA;MACA,MAAMC,SAAS,GAAG,IAAIrG,YAAY,CAChC2B,YAAY,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,WAAW,CAAC,CAC9C;MACD,IAAI,CAACqE,oBAAoB,CAAC7E,SAAS,CAACgF,SAAS,CAAC;MAE9C;IACF,CAAC,CAAC;EACJ;;;;uBArBWJ,aAAa,EAAAK,EAAA,CAAAC,QAAA,CAAAxF,oBAAA,GAAAuF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAhG,eAAA,GAAA8F,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAxG,QAAA;IAAA;EAAA;;;YAAbgG;IAAa;EAAA;;;iBATb,CACTlF,oBAAoB,EACpB;QACE2F,OAAO,EAAE9G,cAAc;QACvB+G,UAAU,EAAEhF,YAAY;QACxBiF,IAAI,EAAE,CAAC3G,QAAQ,EAAEO,eAAe;OACjC,CACF;MAAAqG,OAAA,GARShH,YAAY;IAAA;EAAA;;;2EAUXoG,aAAa;IAAAa,OAAA,GAVdjH,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}