{"ast": null, "code": "import { CalendarView } from 'angular-calendar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/authuser.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"angular-calendar\";\nfunction PlanningDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.editPlanning());\n    });\n    i0.ɵɵtext(2, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_div_7_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.deletePlanning());\n    });\n    i0.ɵɵtext(4, \" Supprimer \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlanningDetailComponent_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 19);\n    i0.ɵɵelement(2, \"path\", 32)(3, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.planning.lieu);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r10.username);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_36_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 40)(1, \"div\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(6, 3, event_r12.start, \"shortTime\"), \" - \", i0.ɵɵpipeBind2(7, 6, event_r12.end, \"shortTime\"), \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"hr\", 36);\n    i0.ɵɵelementStart(2, \"h3\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\", 38);\n    i0.ɵɵtemplate(6, PlanningDetailComponent_div_7_div_36_li_6_Template, 8, 9, \"li\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails pour le \", i0.ɵɵpipeBind2(4, 2, ctx_r6.selectedDate, \"fullDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.selectedDayEvents);\n  }\n}\nfunction PlanningDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\")(3, \"h1\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, PlanningDetailComponent_div_7_div_7_Template, 5, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"div\")(10, \"h2\", 16);\n    i0.ɵɵtext(11, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 17)(13, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 19);\n    i0.ɵɵelement(15, \"path\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\", 21);\n    i0.ɵɵtext(17, \" Du \");\n    i0.ɵɵelementStart(18, \"strong\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" au \");\n    i0.ɵɵelementStart(22, \"strong\");\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(25, PlanningDetailComponent_div_7_div_25_Template, 6, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\")(27, \"h2\", 16);\n    i0.ɵɵtext(28, \"Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 23);\n    i0.ɵɵtemplate(30, PlanningDetailComponent_div_7_div_30_Template, 3, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 25)(32, \"h2\", 16);\n    i0.ɵɵtext(33, \"R\\u00E9unions associ\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 26)(35, \"mwl-calendar-month-view\", 27);\n    i0.ɵɵlistener(\"dayClicked\", function PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_35_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.handleDayClick($event.day));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, PlanningDetailComponent_div_7_div_36_Template, 7, 5, \"div\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.planning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.planning.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCreator);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 10, ctx_r2.planning.dateDebut, \"mediumDate\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 13, ctx_r2.planning.dateFin, \"mediumDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.planning.lieu);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.planning.participants);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"viewDate\", ctx_r2.viewDate)(\"events\", ctx_r2.events);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDayEvents.length > 0);\n  }\n}\nexport class PlanningDetailComponent {\n  constructor(route, router, planningService, authService, cdr) {\n    this.route = route;\n    this.router = router;\n    this.planningService = planningService;\n    this.authService = authService;\n    this.cdr = cdr;\n    this.planning = null;\n    this.loading = true;\n    this.error = null;\n    this.isCreator = false;\n    this.selectedDayEvents = [];\n    this.selectedDate = null;\n    // Calendar setup\n    this.view = CalendarView.Month;\n    this.viewDate = new Date();\n    this.events = [];\n  }\n  ngOnInit() {\n    this.loadPlanningDetails();\n  }\n  loadPlanningDetails() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de planning non fourni';\n      this.loading = false;\n      return;\n    }\n    this.planningService.getPlanningById(id).subscribe({\n      next: planning => {\n        this.planning = planning.planning;\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n        this.loading = false;\n        this.events = this.planning.reunions.map(reunion => {\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n          return {\n            start: new Date(startStr),\n            end: new Date(endStr),\n            title: reunion.titre,\n            allDay: false\n          };\n        });\n        this.cdr.detectChanges();\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n  handleDayClick(day) {\n    this.selectedDate = day.date;\n    this.selectedDayEvents = day.events; // These come from your `events` array\n  }\n\n  editPlanning() {\n    if (this.planning) {\n      this.router.navigate(['/plannings/edit', this.planning._id]);\n    }\n  }\n  deletePlanning() {\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n      this.planningService.deletePlanning(this.planning._id).subscribe({\n        next: () => this.router.navigate(['/plannings']),\n        error: err => this.error = err.error?.message || 'Erreur lors de la suppression'\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningDetailComponent_Factory(t) {\n      return new (t || PlanningDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningDetailComponent,\n      selectors: [[\"app-planning-detail\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-gray-600\", \"mt-1\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"text-lg\", \"font-semibold\", \"mb-3\", \"text-gray-800\"], [1, \"space-y-3\"], [1, \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"flex items-center bg-gray-100 rounded-full px-3 py-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-6\"], [1, \"calendar-container\"], [3, \"viewDate\", \"events\", \"dayClicked\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", 3, \"click\"], [1, \"px-3\", \"py-1\", \"bg-red-500\", \"text-white\", \"rounded\", \"hover:bg-red-600\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-full\", \"px-3\", \"py-1\"], [1, \"mt-4\"], [1, \"my-2\", \"border-gray-300\"], [1, \"text-md\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"p-2 border rounded bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-2\", \"border\", \"rounded\", \"bg-gray-50\"]],\n      template: function PlanningDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function PlanningDetailComponent_Template_button_click_1_listener() {\n            return ctx.router.navigate([\"/plannings\"]);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(2, \"svg\", 2);\n          i0.ɵɵelement(3, \"path\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Retour aux plannings \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, PlanningDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n          i0.ɵɵtemplate(6, PlanningDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, PlanningDetailComponent_div_7_Template, 37, 16, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.planning);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.CalendarMonthViewComponent, i4.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcGxhbm5pbmdzL3BsYW5uaW5nLWRldGFpbC9wbGFubmluZy1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CalendarView", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵlistener", "PlanningDetailComponent_div_7_div_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "editPlanning", "PlanningDetailComponent_div_7_div_7_Template_button_click_3_listener", "ctx_r9", "deletePlanning", "ɵɵnamespaceSVG", "ɵɵtextInterpolate", "ctx_r4", "planning", "lieu", "participant_r10", "username", "event_r12", "title", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "start", "end", "ɵɵtemplate", "PlanningDetailComponent_div_7_div_36_li_6_Template", "ctx_r6", "selectedDate", "ɵɵproperty", "selectedDayEvents", "PlanningDetailComponent_div_7_div_7_Template", "PlanningDetailComponent_div_7_div_25_Template", "PlanningDetailComponent_div_7_div_30_Template", "PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_35_listener", "$event", "_r14", "ctx_r13", "handleDayClick", "day", "PlanningDetailComponent_div_7_div_36_Template", "ctx_r2", "titre", "description", "isCreator", "dateDebut", "dateFin", "participants", "viewDate", "events", "length", "PlanningDetailComponent", "constructor", "route", "router", "planningService", "authService", "cdr", "loading", "view", "Month", "Date", "ngOnInit", "loadPlanningDetails", "id", "snapshot", "paramMap", "get", "getPlanningById", "subscribe", "next", "<PERSON>ur", "_id", "getCurrentUserId", "reunions", "map", "reunion", "startStr", "date", "substring", "heureDebut", "endStr", "heure<PERSON>in", "allDay", "detectChanges", "err", "message", "console", "navigate", "confirm", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "PlanningService", "i3", "AuthuserService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "PlanningDetailComponent_Template", "rf", "ctx", "PlanningDetailComponent_Template_button_click_1_listener", "PlanningDetailComponent_div_5_Template", "PlanningDetailComponent_div_6_Template", "PlanningDetailComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\planning-detail\\planning-detail.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\planning-detail\\planning-detail.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, OnInit} from '@angular/core';\r\n\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthuserService } from '@app/services/authuser.service';\r\nimport { PlanningService } from '@app/services/planning.service';\r\nimport {\r\n  CalendarEvent, CalendarMonthViewDay,\r\n  CalendarView,\r\n} from 'angular-calendar';\r\n\r\n\r\n@Component({\r\n  selector: 'app-planning-detail',\r\n  templateUrl: './planning-detail.component.html',\r\n  styleUrls: ['./planning-detail.component.css']\r\n})\r\nexport class PlanningDetailComponent implements OnInit {\r\n\r\n  planning: any | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n  isCreator = false;\r\n  selectedDayEvents: CalendarEvent[] = [];\r\n  selectedDate: Date | null = null;\r\n\r\n  // Calendar setup\r\n  view: CalendarView = CalendarView.Month;\r\n  viewDate: Date = new Date();\r\n  events: CalendarEvent[] = [];\r\n\r\n  constructor(\r\n    public route: ActivatedRoute,\r\n    public router: Router,\r\n    private planningService: PlanningService,\r\n    public authService: AuthuserService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadPlanningDetails();\r\n  }\r\n\r\n  loadPlanningDetails(): void {\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (!id) {\r\n      this.error = 'ID de planning non fourni';\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.planningService.getPlanningById(id).subscribe({\r\n      next: (planning: any) => {\r\n        this.planning = planning.planning;\r\n        this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\r\n        this.loading = false;\r\n\r\n        this.events = this.planning.reunions.map((reunion: any) => {\r\n          const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\r\n          const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\r\n\r\n          return {\r\n            start: new Date(startStr),\r\n            end: new Date(endStr),\r\n            title: reunion.titre,\r\n            allDay: false\r\n          };\r\n        });\r\n\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (err: any) => {\r\n        this.error = err.error?.message || 'Erreur lors du chargement';\r\n        this.loading = false;\r\n        console.error('Erreur:', err);\r\n      }\r\n    });\r\n  }\r\n\r\n  handleDayClick(day: CalendarMonthViewDay): void {\r\n    this.selectedDate = day.date;\r\n    this.selectedDayEvents = day.events; // These come from your `events` array\r\n  }\r\n\r\n\r\n  editPlanning(): void {\r\n    if (this.planning) {\r\n      this.router.navigate(['/plannings/edit', this.planning._id]);\r\n    }\r\n  }\r\n\r\n  deletePlanning(): void {\r\n    if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\r\n      this.planningService.deletePlanning(this.planning._id).subscribe({\r\n        next: () => this.router.navigate(['/plannings']),\r\n        error: (err) => this.error = err.error?.message || 'Erreur lors de la suppression'\r\n      });\r\n    }\r\n  }\r\n}", "<div class=\"container mx-auto px-4 py-6\">\r\n  <!-- Bouton retour -->\r\n  <button (click)=\"router.navigate(['/plannings'])\"\r\n          class=\"mb-4 flex items-center text-purple-600 hover:text-purple-800\">\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\r\n    </svg>\r\n    Retour aux plannings\r\n  </button>\r\n\r\n  <!-- Chargement -->\r\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\r\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\r\n  </div>\r\n\r\n  <!-- Erreur -->\r\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n    {{ error }}\r\n  </div>\r\n\r\n  <!-- Détails du planning -->\r\n  <div *ngIf=\"!loading && planning\" class=\"bg-white rounded-lg shadow-md p-6\">\r\n    <div class=\"flex justify-between items-start mb-4\">\r\n      <div>\r\n        <h1 class=\"text-2xl font-bold text-gray-800\">{{ planning.titre }}</h1>\r\n        <p class=\"text-gray-600 mt-1\">{{ planning.description || 'Aucune description' }}</p>\r\n      </div>\r\n\r\n      <div *ngIf=\"isCreator\" class=\"flex space-x-2\">\r\n        <button (click)=\"editPlanning()\" class=\"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\">\r\n          Modifier\r\n        </button>\r\n        <button (click)=\"deletePlanning()\" class=\"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600\">\r\n          Supprimer\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n      <!-- Section Informations -->\r\n      <div>\r\n        <h2 class=\"text-lg font-semibold mb-3 text-gray-800\">Informations</h2>\r\n        <div class=\"space-y-3\">\r\n          <div class=\"flex items-center\">\r\n            <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n            </svg>\r\n            <span class=\"text-gray-700\">\r\n              Du <strong>{{ planning.dateDebut | date:'mediumDate' }}</strong>\r\n              au <strong>{{ planning.dateFin | date:'mediumDate' }}</strong>\r\n            </span>\r\n          </div>\r\n\r\n          <div *ngIf=\"planning.lieu\" class=\"flex items-center\">\r\n            <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n            </svg>\r\n            <span class=\"text-gray-700\">{{ planning.lieu }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Section Participants -->\r\n      <div>\r\n        <h2 class=\"text-lg font-semibold mb-3 text-gray-800\">Participants</h2>\r\n        <div class=\"flex flex-wrap gap-2\">\r\n          <div *ngFor=\"let participant of planning.participants\" class=\"flex items-center bg-gray-100 rounded-full px-3 py-1\">\r\n            <span class=\"text-gray-700\">{{ participant.username }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mb-6\">\r\n      <h2 class=\"text-lg font-semibold mb-3 text-gray-800\">Réunions associées</h2>\r\n\r\n      <div class=\"calendar-container\">\r\n        <mwl-calendar-month-view\r\n          [viewDate]=\"viewDate\"\r\n          [events]=\"events\"\r\n          (dayClicked)=\"handleDayClick($event.day)\">\r\n        </mwl-calendar-month-view>\r\n      </div>\r\n      <div class=\"mt-4\" *ngIf=\"selectedDayEvents.length > 0\">\r\n        <hr class=\"my-2 border-gray-300\" />\r\n        <h3 class=\"text-md font-medium text-gray-700 mb-2\">\r\n          Détails pour le {{ selectedDate | date: 'fullDate' }}\r\n        </h3>\r\n        <ul class=\"space-y-2\">\r\n          <li *ngFor=\"let event of selectedDayEvents\" class=\"p-2 border rounded bg-gray-50\">\r\n            <div><strong>{{ event.title }}</strong></div>\r\n            <div>\r\n              {{ event.start | date: 'shortTime' }} - {{ event.end | date: 'shortTime' }}\r\n            </div>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAKA,SAEEA,YAAY,QACP,kBAAkB;;;;;;;;;;ICGvBC,EAAA,CAAAC,eAAA,EAA8C;IAA9CD,EAAA,CAAAE,cAAA,aAA8C;IAC5CF,EAAA,CAAAG,SAAA,aAA4F;IAC9FH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAGNJ,EAAA,CAAAC,eAAA,EAAgG;IAAhGD,EAAA,CAAAE,cAAA,aAAgG;IAC9FF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;IAUIT,EAAA,CAAAE,cAAA,cAA8C;IACpCF,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAC9BjB,EAAA,CAAAK,MAAA,iBACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,iBAAoG;IAA5FF,EAAA,CAAAU,UAAA,mBAAAQ,qEAAA;MAAAlB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAnB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAG,MAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAChCpB,EAAA,CAAAK,MAAA,kBACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;;;;;IAmBPJ,EAAA,CAAAE,cAAA,cAAqD;IACnDF,EAAA,CAAAqB,cAAA,EAA8F;IAA9FrB,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAG,SAAA,eAA+J;IAEjKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAK,MAAA,GAAmB;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAmB;;;;;IASjDzB,EAAA,CAAAE,cAAA,cAAoH;IACtFF,EAAA,CAAAK,MAAA,GAA0B;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAAjCJ,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAsB,iBAAA,CAAAI,eAAA,CAAAC,QAAA,CAA0B;;;;;IAsBxD3B,EAAA,CAAAE,cAAA,aAAkF;IACnEF,EAAA,CAAAK,MAAA,GAAiB;IAAAL,EAAA,CAAAI,YAAA,EAAS;IACvCJ,EAAA,CAAAE,cAAA,UAAK;IACHF,EAAA,CAAAK,MAAA,GACF;;;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IAHOJ,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAsB,iBAAA,CAAAM,SAAA,CAAAC,KAAA,CAAiB;IAE5B7B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA8B,kBAAA,MAAA9B,EAAA,CAAA+B,WAAA,OAAAH,SAAA,CAAAI,KAAA,uBAAAhC,EAAA,CAAA+B,WAAA,OAAAH,SAAA,CAAAK,GAAA,oBACF;;;;;IAVNjC,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAG,SAAA,aAAmC;IACnCH,EAAA,CAAAE,cAAA,aAAmD;IACjDF,EAAA,CAAAK,MAAA,GACF;;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,aAAsB;IACpBF,EAAA,CAAAkC,UAAA,IAAAC,kDAAA,iBAKK;IACPnC,EAAA,CAAAI,YAAA,EAAK;;;;IATHJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAP,EAAA,CAAA+B,WAAA,OAAAK,MAAA,CAAAC,YAAA,mBACF;IAEwBrC,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAsC,UAAA,YAAAF,MAAA,CAAAG,iBAAA,CAAoB;;;;;;;IArElDvC,EAAA,CAAAC,eAAA,EAA4E;IAA5ED,EAAA,CAAAE,cAAA,cAA4E;IAGzBF,EAAA,CAAAK,MAAA,GAAoB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,YAA8B;IAAAF,EAAA,CAAAK,MAAA,GAAkD;IAAAL,EAAA,CAAAI,YAAA,EAAI;IAGtFJ,EAAA,CAAAkC,UAAA,IAAAM,4CAAA,kBAOM;IACRxC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,cAAmD;IAGMF,EAAA,CAAAK,MAAA,oBAAY;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,eAAuB;IAEnBF,EAAA,CAAAqB,cAAA,EAA8F;IAA9FrB,EAAA,CAAAE,cAAA,eAA8F;IAC5FF,EAAA,CAAAG,SAAA,gBAAmK;IACrKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,gBAA4B;IAC1BF,EAAA,CAAAK,MAAA,YAAG;IAAAL,EAAA,CAAAE,cAAA,cAAQ;IAAAF,EAAA,CAAAK,MAAA,IAA4C;;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAChEJ,EAAA,CAAAK,MAAA,YAAG;IAAAL,EAAA,CAAAE,cAAA,cAAQ;IAAAF,EAAA,CAAAK,MAAA,IAA0C;;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAIlEJ,EAAA,CAAAkC,UAAA,KAAAO,6CAAA,kBAMM;IACRzC,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,WAAK;IACkDF,EAAA,CAAAK,MAAA,oBAAY;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAE,cAAA,eAAkC;IAChCF,EAAA,CAAAkC,UAAA,KAAAQ,6CAAA,kBAEM;IACR1C,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,oCAAkB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IAE5EJ,EAAA,CAAAE,cAAA,eAAgC;IAI5BF,EAAA,CAAAU,UAAA,wBAAAiC,sFAAAC,MAAA;MAAA5C,EAAA,CAAAY,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAAe,aAAA;MAAA,OAAcf,EAAA,CAAAgB,WAAA,CAAA8B,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAAI,GAAA,CAA0B;IAAA,EAAC;IAC3ChD,EAAA,CAAAI,YAAA,EAA0B;IAE5BJ,EAAA,CAAAkC,UAAA,KAAAe,6CAAA,kBAaM;IAERjD,EAAA,CAAAI,YAAA,EAAM;;;;IA3E2CJ,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAsB,iBAAA,CAAA4B,MAAA,CAAA1B,QAAA,CAAA2B,KAAA,CAAoB;IACnCnD,EAAA,CAAAM,SAAA,GAAkD;IAAlDN,EAAA,CAAAsB,iBAAA,CAAA4B,MAAA,CAAA1B,QAAA,CAAA4B,WAAA,yBAAkD;IAG5EpD,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAsC,UAAA,SAAAY,MAAA,CAAAG,SAAA,CAAe;IAoBFrD,EAAA,CAAAM,SAAA,IAA4C;IAA5CN,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAA+B,WAAA,SAAAmB,MAAA,CAAA1B,QAAA,CAAA8B,SAAA,gBAA4C;IAC5CtD,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAA+B,WAAA,SAAAmB,MAAA,CAAA1B,QAAA,CAAA+B,OAAA,gBAA0C;IAInDvD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAsC,UAAA,SAAAY,MAAA,CAAA1B,QAAA,CAAAC,IAAA,CAAmB;IAcIzB,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAsC,UAAA,YAAAY,MAAA,CAAA1B,QAAA,CAAAgC,YAAA,CAAwB;IAYrDxD,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAsC,UAAA,aAAAY,MAAA,CAAAO,QAAA,CAAqB,WAAAP,MAAA,CAAAQ,MAAA;IAKN1D,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAsC,UAAA,SAAAY,MAAA,CAAAX,iBAAA,CAAAoB,MAAA,KAAkC;;;ADpE3D,OAAM,MAAOC,uBAAuB;EAclCC,YACSC,KAAqB,EACrBC,MAAc,EACbC,eAAgC,EACjCC,WAA4B,EAC3BC,GAAsB;IAJvB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,GAAG,GAAHA,GAAG;IAjBb,KAAA1C,QAAQ,GAAe,IAAI;IAC3B,KAAA2C,OAAO,GAAG,IAAI;IACd,KAAA1D,KAAK,GAAkB,IAAI;IAC3B,KAAA4C,SAAS,GAAG,KAAK;IACjB,KAAAd,iBAAiB,GAAoB,EAAE;IACvC,KAAAF,YAAY,GAAgB,IAAI;IAEhC;IACA,KAAA+B,IAAI,GAAiBrE,YAAY,CAACsE,KAAK;IACvC,KAAAZ,QAAQ,GAAS,IAAIa,IAAI,EAAE;IAC3B,KAAAZ,MAAM,GAAoB,EAAE;EAQzB;EAEHa,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,EAAE,GAAG,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAAChE,KAAK,GAAG,2BAA2B;MACxC,IAAI,CAAC0D,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACH,eAAe,CAACa,eAAe,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MACjDC,IAAI,EAAGvD,QAAa,IAAI;QACtB,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;QACjC,IAAI,CAAC6B,SAAS,GAAG7B,QAAQ,CAACA,QAAQ,CAACwD,QAAQ,CAACC,GAAG,KAAK,IAAI,CAAChB,WAAW,CAACiB,gBAAgB,EAAE;QACvF,IAAI,CAACf,OAAO,GAAG,KAAK;QAEpB,IAAI,CAACT,MAAM,GAAG,IAAI,CAAClC,QAAQ,CAAC2D,QAAQ,CAACC,GAAG,CAAEC,OAAY,IAAI;UACxD,MAAMC,QAAQ,GAAG,GAAGD,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACI,UAAU,KAAK;UAC5E,MAAMC,MAAM,GAAG,GAAGL,OAAO,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIH,OAAO,CAACM,QAAQ,KAAK;UAExE,OAAO;YACL3D,KAAK,EAAE,IAAIsC,IAAI,CAACgB,QAAQ,CAAC;YACzBrD,GAAG,EAAE,IAAIqC,IAAI,CAACoB,MAAM,CAAC;YACrB7D,KAAK,EAAEwD,OAAO,CAAClC,KAAK;YACpByC,MAAM,EAAE;WACT;QACH,CAAC,CAAC;QAEF,IAAI,CAAC1B,GAAG,CAAC2B,aAAa,EAAE;MAC1B,CAAC;MACDpF,KAAK,EAAGqF,GAAQ,IAAI;QAClB,IAAI,CAACrF,KAAK,GAAGqF,GAAG,CAACrF,KAAK,EAAEsF,OAAO,IAAI,2BAA2B;QAC9D,IAAI,CAAC5B,OAAO,GAAG,KAAK;QACpB6B,OAAO,CAACvF,KAAK,CAAC,SAAS,EAAEqF,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEA/C,cAAcA,CAACC,GAAyB;IACtC,IAAI,CAACX,YAAY,GAAGW,GAAG,CAACuC,IAAI;IAC5B,IAAI,CAAChD,iBAAiB,GAAGS,GAAG,CAACU,MAAM,CAAC,CAAC;EACvC;;EAGAzC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACO,QAAQ,EAAE;MACjB,IAAI,CAACuC,MAAM,CAACkC,QAAQ,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAACzE,QAAQ,CAACyD,GAAG,CAAC,CAAC;;EAEhE;EAEA7D,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACI,QAAQ,IAAI0E,OAAO,CAAC,wCAAwC,CAAC,EAAE;MACtE,IAAI,CAAClC,eAAe,CAAC5C,cAAc,CAAC,IAAI,CAACI,QAAQ,CAACyD,GAAG,CAAC,CAACH,SAAS,CAAC;QAC/DC,IAAI,EAAEA,CAAA,KAAM,IAAI,CAAChB,MAAM,CAACkC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QAChDxF,KAAK,EAAGqF,GAAG,IAAK,IAAI,CAACrF,KAAK,GAAGqF,GAAG,CAACrF,KAAK,EAAEsF,OAAO,IAAI;OACpD,CAAC;;EAEN;;;uBAjFWnC,uBAAuB,EAAA5D,EAAA,CAAAmG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArG,EAAA,CAAAmG,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAtG,EAAA,CAAAmG,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAxG,EAAA,CAAAmG,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAA1G,EAAA,CAAAmG,iBAAA,CAAAnG,EAAA,CAAA2G,iBAAA;IAAA;EAAA;;;YAAvB/C,uBAAuB;MAAAgD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBpClH,EAAA,CAAAE,cAAA,aAAyC;UAE/BF,EAAA,CAAAU,UAAA,mBAAA0G,yDAAA;YAAA,OAASD,GAAA,CAAApD,MAAA,CAAAkC,QAAA,EAAiB,YAAY,EAAE;UAAA,EAAC;UAE/CjG,EAAA,CAAAqB,cAAA,EAAqG;UAArGrB,EAAA,CAAAE,cAAA,aAAqG;UACnGF,EAAA,CAAAG,SAAA,cAA0L;UAC5LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,MAAA,6BACF;UAAAL,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAkC,UAAA,IAAAmF,sCAAA,iBAEM;UAGNrH,EAAA,CAAAkC,UAAA,IAAAoF,sCAAA,iBAEM;UAGNtH,EAAA,CAAAkC,UAAA,IAAAqF,sCAAA,mBA+EM;UACRvH,EAAA,CAAAI,YAAA,EAAM;;;UA1FEJ,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAsC,UAAA,SAAA6E,GAAA,CAAAhD,OAAA,CAAa;UAKbnE,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAsC,UAAA,SAAA6E,GAAA,CAAA1G,KAAA,CAAW;UAKXT,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAsC,UAAA,UAAA6E,GAAA,CAAAhD,OAAA,IAAAgD,GAAA,CAAA3F,QAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}