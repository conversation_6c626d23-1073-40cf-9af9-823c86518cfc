{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"div\", 18)(3, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22);\n    i0.ɵɵelement(3, \"i\", 23)(4, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 25);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProfileComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 21)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"i\", 29)(4, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 30);\n    i0.ɵɵtext(7, \" Succ\\u00E8s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 26);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_26_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.toggleEditMode());\n    });\n    i0.ɵɵtext(2, \" Complete Profile \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"div\", 32);\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"h3\", 34);\n    i0.ɵɵtext(5, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36);\n    i0.ɵɵelement(9, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 26);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ProfileComponent_div_26_div_12_Template, 3, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.progressPercentage, \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.progressPercentage, \"%\")(\"background-color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getMotivationalMessage(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction ProfileComponent_div_27_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onUpload());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_13_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_13_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r8.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.uploadLoading ? \"Uploading...\" : \"Upload\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 115);\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction ProfileComponent_div_27_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.removeProfileImage());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_14_i_1_Template, 1, 0, \"i\", 114);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_14_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.removeLoading ? \"Removing...\" : \"Remove\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.user.department, \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 117);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.user.position, \" \");\n  }\n}\nfunction ProfileComponent_div_27_p_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.user.bio.length > 150 ? i0.ɵɵpipeBind3(2, 1, ctx_r12.user.bio, 0, 150) + \"...\" : ctx_r12.user.bio, \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 121);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r35.progressPercentage, \"% \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_36_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const tab_r34 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.activeTab = tab_r34.id);\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ProfileComponent_div_27_button_36_span_3_Template, 2, 1, \"span\", 120);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r34 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gradient-to-r\", ctx_r13.activeTab === tab_r34.id)(\"from-[#4f5fad]\", ctx_r13.activeTab === tab_r34.id)(\"to-[#7826b5]\", ctx_r13.activeTab === tab_r34.id)(\"text-white\", ctx_r13.activeTab === tab_r34.id)(\"text-[#6d6870]\", ctx_r13.activeTab !== tab_r34.id)(\"dark:text-[#a0a0a0]\", ctx_r13.activeTab !== tab_r34.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(tab_r34.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r34.label, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r34.id === \"completion\" && ctx_r13.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 122)(2, \"h3\", 123);\n    i0.ɵɵelement(3, \"i\", 124);\n    i0.ɵɵtext(4, \" Personal Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.setEditSection(\"personal\"));\n    });\n    i0.ɵɵelement(6, \"i\", 126);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 127)(9, \"div\", 128)(10, \"label\", 129);\n    i0.ɵɵtext(11, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 130);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 128)(15, \"label\", 129);\n    i0.ɵɵtext(16, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 130);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 128)(20, \"label\", 129);\n    i0.ɵɵtext(21, \"Full Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 130);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 128)(25, \"label\", 129);\n    i0.ɵɵtext(26, \"Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 130);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 128)(31, \"label\", 129);\n    i0.ɵɵtext(32, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 130);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 128)(36, \"label\", 129);\n    i0.ɵɵtext(37, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\", 130);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 128)(41, \"label\", 131);\n    i0.ɵɵtext(42, \"Bio\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\", 132);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r14.user.firstName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.user.lastName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.user.fullName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.user.dateOfBirth ? i0.ɵɵpipeBind2(29, 7, ctx_r14.user.dateOfBirth, \"mediumDate\") : \"Not provided\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r14.user.phoneNumber || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.user.address || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.user.bio || \"Tell us about yourself, your interests, and goals...\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 139);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r44 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r44, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_div_39_div_34_span_1_Template, 2, 1, \"span\", 138);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r40.user.skills);\n  }\n}\nfunction ProfileComponent_div_27_div_39_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 140);\n    i0.ɵɵtext(1, \"No skills added yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 122)(2, \"h3\", 123);\n    i0.ɵɵelement(3, \"i\", 133);\n    i0.ɵɵtext(4, \" Professional Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_39_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.setEditSection(\"professional\"));\n    });\n    i0.ɵɵelement(6, \"i\", 126);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 127)(9, \"div\", 128)(10, \"label\", 129);\n    i0.ɵɵtext(11, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 130);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 128)(15, \"label\", 129);\n    i0.ɵɵtext(16, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 130);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 128)(20, \"label\", 129);\n    i0.ɵɵtext(21, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 130);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 128)(26, \"label\", 129);\n    i0.ɵɵtext(27, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 130);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 128)(32, \"label\", 134);\n    i0.ɵɵtext(33, \"Skills & Expertise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, ProfileComponent_div_27_div_39_div_34_Template, 2, 1, \"div\", 135);\n    i0.ɵɵtemplate(35, ProfileComponent_div_27_div_39_ng_template_35_Template, 2, 0, \"ng-template\", null, 136, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r41 = i0.ɵɵreference(36);\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r15.user.department || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.position || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 6, ctx_r15.user.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 8, ctx_r15.user.createdAt, \"mediumDate\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.user.skills && ctx_r15.user.skills.length > 0)(\"ngIfElse\", _r41);\n  }\n}\nfunction ProfileComponent_div_27_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Pr\\u00E9nom \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.user.firstName, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Nom de famille \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.user.lastName, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Date de naissance \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 1, ctx_r18.user.dateOfBirth, \"mediumDate\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" T\\u00E9l\\u00E9phone \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.user.phoneNumber, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" D\\u00E9partement \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.user.department, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Position \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.user.position, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Bio \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.user.bio, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Adresse \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.user.address, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_88_span_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_88_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_div_88_span_6_span_2_Template, 2, 0, \"span\", 142);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r48 = ctx.$implicit;\n    const last_r49 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r48, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r49);\n  }\n}\nfunction ProfileComponent_div_27_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Comp\\u00E9tences \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 82);\n    i0.ɵɵtemplate(6, ProfileComponent_div_27_div_88_span_6_Template, 3, 2, \"span\", 141);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.user.skills);\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r55.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 178);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r56.getFieldError(\"bio\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_127_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_127_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 179);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 180);\n    i0.ɵɵelement(2, \"circle\", 181)(3, \"path\", 182);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Saving... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_127_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 183)(1, \"p\", 184);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r59.message);\n  }\n}\nfunction ProfileComponent_div_27_div_127_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 185)(1, \"p\", 186);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r60.error);\n  }\n}\nfunction ProfileComponent_div_27_div_127_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 143)(1, \"div\", 144)(2, \"div\", 145)(3, \"div\", 146)(4, \"h2\", 147);\n    i0.ɵɵtext(5, \"Edit Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_127_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.cancelEdit());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 149);\n    i0.ɵɵelement(8, \"path\", 150);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"form\", 151);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_27_div_127_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.onEditSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 127)(11, \"div\")(12, \"label\", 152);\n    i0.ɵɵtext(13, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 153);\n    i0.ɵɵtemplate(15, ProfileComponent_div_27_div_127_p_15_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"label\", 152);\n    i0.ɵɵtext(18, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 155);\n    i0.ɵɵtemplate(20, ProfileComponent_div_27_div_127_p_20_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"label\", 152);\n    i0.ɵɵtext(23, \" Full Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 156);\n    i0.ɵɵtemplate(25, ProfileComponent_div_27_div_127_p_25_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"label\", 152);\n    i0.ɵɵtext(28, \" Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 157);\n    i0.ɵɵtemplate(30, ProfileComponent_div_27_div_127_p_30_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"label\", 152);\n    i0.ɵɵtext(33, \" Date of Birth \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 158);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\")(36, \"label\", 152);\n    i0.ɵɵtext(37, \" Phone Number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 159);\n    i0.ɵɵtemplate(39, ProfileComponent_div_27_div_127_p_39_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"label\", 152);\n    i0.ɵɵtext(42, \" Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 160);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"label\", 152);\n    i0.ɵɵtext(46, \" Position \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 161);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 162)(49, \"label\", 152);\n    i0.ɵɵtext(50, \" Bio \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 163);\n    i0.ɵɵtemplate(52, ProfileComponent_div_27_div_127_p_52_Template, 2, 1, \"p\", 154);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 162)(54, \"label\", 152);\n    i0.ɵɵtext(55, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 162)(58, \"label\", 152);\n    i0.ɵɵtext(59, \" Skills \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"input\", 165);\n    i0.ɵɵelementStart(61, \"p\", 166);\n    i0.ɵɵtext(62, \" Separate skills with commas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 162)(64, \"label\", 152);\n    i0.ɵɵtext(65, \" Profile Picture \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 167)(67, \"div\", 168);\n    i0.ɵɵelement(68, \"img\", 169);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 170)(70, \"input\", 171);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_27_div_127_Template_input_change_70_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"p\", 166);\n    i0.ɵɵtext(72, \" Upload a new profile picture (optional) \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"div\", 172)(74, \"button\", 173);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_127_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.cancelEdit());\n    });\n    i0.ɵɵtext(75, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 174);\n    i0.ɵɵtemplate(77, ProfileComponent_div_27_div_127_span_77_Template, 2, 0, \"span\", 142);\n    i0.ɵɵtemplate(78, ProfileComponent_div_27_div_127_span_78_Template, 5, 0, \"span\", 175);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, ProfileComponent_div_27_div_127_div_79_Template, 3, 1, \"div\", 176);\n    i0.ɵɵtemplate(80, ProfileComponent_div_27_div_127_div_80_Template, 3, 1, \"div\", 177);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r25.editForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"fullName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"email\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"phoneNumber\"));\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r25.isFieldInvalid(\"bio\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.getFieldError(\"bio\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"src\", ctx_r25.previewUrl || ctx_r25.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r25.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.error);\n  }\n}\nfunction ProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"div\", 32);\n    i0.ɵɵelementStart(3, \"div\", 43)(4, \"div\", 44)(5, \"div\", 45);\n    i0.ɵɵelement(6, \"img\", 46);\n    i0.ɵɵelementStart(7, \"div\", 47)(8, \"label\", 48);\n    i0.ɵɵelement(9, \"i\", 49);\n    i0.ɵɵtext(10, \" Change Photo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 50);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_27_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 51);\n    i0.ɵɵtemplate(13, ProfileComponent_div_27_button_13_Template, 4, 4, \"button\", 52);\n    i0.ɵɵtemplate(14, ProfileComponent_div_27_button_14_Template, 4, 4, \"button\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 54)(16, \"h2\", 55);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 56);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 57)(21, \"span\", 58);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ProfileComponent_div_27_span_24_Template, 2, 1, \"span\", 59);\n    i0.ɵɵtemplate(25, ProfileComponent_div_27_span_25_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ProfileComponent_div_27_p_26_Template, 3, 5, \"p\", 61);\n    i0.ɵɵelementStart(27, \"div\", 62)(28, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.toggleEditMode());\n    });\n    i0.ɵɵelement(29, \"i\", 64);\n    i0.ɵɵtext(30, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"a\", 65);\n    i0.ɵɵelement(32, \"i\", 66);\n    i0.ɵɵtext(33, \" Dashboard \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 67)(35, \"div\", 68);\n    i0.ɵɵtemplate(36, ProfileComponent_div_27_button_36_Template, 4, 16, \"button\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 70);\n    i0.ɵɵtemplate(38, ProfileComponent_div_27_div_38_Template, 45, 10, \"div\", 15);\n    i0.ɵɵtemplate(39, ProfileComponent_div_27_div_39_Template, 37, 11, \"div\", 15);\n    i0.ɵɵelementStart(40, \"div\", 42);\n    i0.ɵɵelement(41, \"div\", 32)(42, \"div\", 71);\n    i0.ɵɵelementStart(43, \"h3\", 72)(44, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 74);\n    i0.ɵɵelement(46, \"path\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(47, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Informations du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 77)(50, \"div\", 78)(51, \"div\", 79);\n    i0.ɵɵelement(52, \"div\", 80);\n    i0.ɵɵelementStart(53, \"div\", 81);\n    i0.ɵɵtext(54, \" Nom complet \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 82);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 78)(58, \"div\", 79);\n    i0.ɵɵelement(59, \"div\", 80);\n    i0.ɵɵelementStart(60, \"div\", 81);\n    i0.ɵɵtext(61, \" Adresse email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 82);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 78)(65, \"div\", 79);\n    i0.ɵɵelement(66, \"div\", 80);\n    i0.ɵɵelementStart(67, \"div\", 81);\n    i0.ɵɵtext(68, \" Type de compte \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 82);\n    i0.ɵɵtext(70);\n    i0.ɵɵpipe(71, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 78)(73, \"div\", 79);\n    i0.ɵɵelement(74, \"div\", 80);\n    i0.ɵɵelementStart(75, \"div\", 81);\n    i0.ɵɵtext(76, \" Membre depuis \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 82);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(80, ProfileComponent_div_27_div_80_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(81, ProfileComponent_div_27_div_81_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(82, ProfileComponent_div_27_div_82_Template, 8, 4, \"div\", 83);\n    i0.ɵɵtemplate(83, ProfileComponent_div_27_div_83_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(84, ProfileComponent_div_27_div_84_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(85, ProfileComponent_div_27_div_85_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(86, ProfileComponent_div_27_div_86_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(87, ProfileComponent_div_27_div_87_Template, 7, 1, \"div\", 83);\n    i0.ɵɵtemplate(88, ProfileComponent_div_27_div_88_Template, 7, 1, \"div\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 42);\n    i0.ɵɵelement(90, \"div\", 32)(91, \"div\", 71);\n    i0.ɵɵelementStart(92, \"h3\", 72)(93, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(94, \"svg\", 74);\n    i0.ɵɵelement(95, \"path\", 84)(96, \"path\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(97, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \" Actions du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"div\", 86)(100, \"a\", 87);\n    i0.ɵɵelement(101, \"div\", 88)(102, \"div\", 89);\n    i0.ɵɵelementStart(103, \"span\", 90)(104, \"div\", 91);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(105, \"svg\", 92);\n    i0.ɵɵelement(106, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(107, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(108, \" Changer le mot de passe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(109, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_109_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.logout());\n    });\n    i0.ɵɵelement(110, \"div\", 96)(111, \"div\", 97);\n    i0.ɵɵelementStart(112, \"span\", 98)(113, \"div\", 91);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(114, \"svg\", 92);\n    i0.ɵɵelement(115, \"path\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(116, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(117, \" D\\u00E9connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(118, \"a\", 101);\n    i0.ɵɵelement(119, \"div\", 102)(120, \"div\", 103);\n    i0.ɵɵelementStart(121, \"span\", 104)(122, \"div\", 91);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(123, \"svg\", 92);\n    i0.ɵɵelement(124, \"path\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(125, \"div\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(126, \" Tableau de bord \");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(127, ProfileComponent_div_27_div_127_Template, 81, 25, \"div\", 107);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r4.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.profileImage && ctx_r4.user.profileImage !== \"assets/images/default-profile.png\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName || ctx_r4.user.firstName + \" \" + ctx_r4.user.lastName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.user.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 28, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.department);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.bio);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.profileTabs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"personal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"professional\");\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.email, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(71, 30, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(79, 32, ctx_r4.user.createdAt, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.firstName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.lastName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.dateOfBirth);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.phoneNumber);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.department);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.bio);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.address);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.skills && ctx_r4.user.skills.length > 0);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isEditMode);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, authuserService, dataService, router, fb) {\n    this.authService = authService;\n    this.authuserService = authuserService;\n    this.dataService = dataService;\n    this.router = router;\n    this.fb = fb;\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.message = '';\n    this.error = '';\n    this.uploadLoading = false;\n    this.removeLoading = false;\n    // Edit profile functionality\n    this.isEditMode = false;\n    this.editLoading = false;\n    this.progressPercentage = 0;\n    this.editForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      dateOfBirth: [''],\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: [''],\n      position: [''],\n      bio: ['', [Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    // Load user profile using DataService\n    this.dataService.getProfile().subscribe({\n      next: res => {\n        this.user = res;\n        // Ensure image properties are consistent\n        if (!this.user.profileImage && this.user.image) {\n          this.user.profileImage = this.user.image;\n        } else if (!this.user.image && this.user.profileImage) {\n          this.user.image = this.user.profileImage;\n        }\n        // If no image is available, use default\n        if (!this.user.profileImage || this.user.profileImage === 'null' || this.user.profileImage.trim() === '') {\n          this.user.profileImage = 'assets/images/default-profile.png';\n          this.user.image = 'assets/images/default-profile.png';\n        }\n        // Ensure profileImageURL is also set for backward compatibility\n        if (!this.user.profileImageURL) {\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\n        }\n        // Calculate profile completion percentage\n        this.calculateProfileCompletion();\n        // Populate edit form with current user data\n        this.populateEditForm();\n      },\n      error: () => {\n        this.error = 'Failed to load profile.';\n      }\n    });\n  }\n  calculateProfileCompletion() {\n    if (!this.user) return;\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  populateEditForm() {\n    if (!this.user) return;\n    this.editForm.patchValue({\n      firstName: this.user.firstName || '',\n      lastName: this.user.lastName || '',\n      fullName: this.user.fullName || '',\n      email: this.user.email || '',\n      dateOfBirth: this.user.dateOfBirth || '',\n      phoneNumber: this.user.phoneNumber || '',\n      department: this.user.department || '',\n      position: this.user.position || '',\n      bio: this.user.bio || '',\n      address: this.user.address || '',\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : this.user.skills || ''\n    });\n  }\n  /**\n   * Returns the appropriate profile image URL based on available properties\n   * Uses the same logic as in front-layout component for consistency\n   */\n  getProfileImageUrl() {\n    if (!this.user) return 'assets/images/default-profile.png';\n    // Check profileImage first\n    if (this.user.profileImage && this.user.profileImage !== 'null' && this.user.profileImage.trim() !== '') {\n      return this.user.profileImage;\n    }\n    // Then check image\n    if (this.user.image && this.user.image !== 'null' && this.user.image.trim() !== '') {\n      return this.user.image;\n    }\n    // Then check profileImageURL (for backward compatibility)\n    if (this.user.profileImageURL && this.user.profileImageURL !== 'null' && this.user.profileImageURL.trim() !== '') {\n      return this.user.profileImageURL;\n    }\n    // Default fallback\n    return 'assets/images/default-profile.png';\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      const file = input.files[0];\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n        this.resetFileInput();\n        return;\n      }\n      if (file.size > 2 * 1024 * 1024) {\n        this.error = \"L'image ne doit pas dépasser 2MB\";\n        this.resetFileInput();\n        return;\n      }\n      this.selectedImage = file;\n      this.error = '';\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewUrl = e.target?.result || null;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onUpload() {\n    if (!this.selectedImage) return;\n    this.uploadLoading = true; // Activer l'état de chargement\n    this.message = '';\n    this.error = '';\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\n    this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n      this.uploadLoading = false;\n      console.log('Upload finished, uploadLoading:', this.uploadLoading);\n    })).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile updated successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = response.imageUrl;\n        this.user.profileImage = response.imageUrl;\n        this.user.image = response.imageUrl;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Upload failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  removeProfileImage() {\n    if (!confirm('Are you sure you want to remove your profile picture?')) return;\n    this.removeLoading = true;\n    this.message = '';\n    this.error = '';\n    this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile picture removed successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = null;\n        this.user.profileImage = null;\n        this.user.image = null;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Removal failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  resetFileInput() {\n    this.selectedImage = null;\n    this.previewUrl = null;\n    const fileInput = document.getElementById('profile-upload');\n    if (fileInput) fileInput.value = '';\n  }\n  navigateTo(path) {\n    this.router.navigate([path]);\n  }\n  // Edit profile methods\n  toggleEditMode() {\n    this.isEditMode = !this.isEditMode;\n    if (this.isEditMode) {\n      this.populateEditForm();\n    }\n    this.message = '';\n    this.error = '';\n  }\n  onEditSubmit() {\n    if (this.editForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.editLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.editForm.value).forEach(key => {\n      const value = this.editForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedImage) {\n      formData.append('image', this.selectedImage);\n    }\n    this.dataService.completeProfile(formData).subscribe({\n      next: response => {\n        this.editLoading = false;\n        this.message = 'Profile updated successfully!';\n        // Update current user data\n        this.user = {\n          ...this.user,\n          ...response.user\n        };\n        this.authuserService.setCurrentUser(this.user);\n        // Recalculate progress\n        this.calculateProfileCompletion();\n        // Exit edit mode\n        this.isEditMode = false;\n        // Clear selected image\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.editLoading = false;\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\n        // Auto-hide error after 5 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 5000);\n      }\n    });\n  }\n  cancelEdit() {\n    this.isEditMode = false;\n    this.populateEditForm(); // Reset form to original values\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.resetFileInput();\n    this.message = '';\n    this.error = '';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.editForm.controls).forEach(key => {\n      this.editForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.editForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['email']) return `Invalid email format`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.editForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Let's complete your profile to unlock all features! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making great progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Excellent! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost perfect! Just a few more details! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete! ✨\";\n    }\n  }\n  logout() {\n    this.authuserService.logout().subscribe({\n      next: () => {\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {});\n        }, 100);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 28,\n      vars: 5,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-1\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-2xl\", \"font-bold\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-3\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"text-sm\", \"font-medium\", 3, \"click\"], [1, \"space-y-6\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"items-center\", \"md:items-start\", \"gap-6\"], [1, \"flex-shrink-0\", \"text-center\"], [1, \"relative\", \"group/avatar\"], [\"alt\", \"Profile Image\", 1, \"w-32\", \"h-32\", \"rounded-full\", \"object-cover\", \"border-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"shadow-lg\", \"group-hover/avatar:scale-105\", \"transition-transform\", \"duration-300\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"rounded-full\", \"opacity-0\", \"group-hover/avatar:opacity-100\", \"transition-opacity\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [\"for\", \"profile-upload\", 1, \"cursor-pointer\", \"text-white\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-camera\", \"text-xl\", \"mb-1\", \"block\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [1, \"flex\", \"gap-2\", \"mt-4\"], [\"class\", \"px-3 py-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-xs rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"flex-1\", \"text-center\", \"md:text-left\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\", \"mb-4\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [\"class\", \"px-3 py-1 bg-[#3d4a85]/10 dark:bg-[#4f5fad]/10 text-[#3d4a85] dark:text-[#4f5fad] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-[#7826b5]/10 dark:bg-[#6d78c9]/10 text-[#7826b5] dark:text-[#6d78c9] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-4\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-sm\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-lg\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"dark:hover:text-white\", \"transition-all\", \"flex\", \"items-center\", 3, \"routerLink\"], [1, \"fas\", \"fa-home\", \"mr-2\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"flex\", \"flex-wrap\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"class\", \"flex-1 px-4 py-3 text-sm font-medium transition-all hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center justify-center gap-2\", 3, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"text-base\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-4\", \"flex\", \"items-center\"], [1, \"relative\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"space-y-4\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"p-4\", \"rounded-lg\", \"backdrop-blur-sm\", \"group/item\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"mb-1\"], [1, \"w-1\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"mr-2\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"font-medium\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-3\", \"group-hover/item:translate-x-1\", \"transition-transform\"], [\"class\", \"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/change-password\", 1, \"relative\", \"overflow-hidden\", \"group/btn\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"relative\", \"mr-1.5\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"relative\", \"z-10\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff8785]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\", 4, \"ngIf\"], [1, \"px-3\", \"py-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-upload mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-upload\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:bg-red-600\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-trash mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-[#3d4a85]/10\", \"dark:bg-[#4f5fad]/10\", \"text-[#3d4a85]\", \"dark:text-[#4f5fad]\", \"text-sm\", \"rounded-full\"], [1, \"px-3\", \"py-1\", \"bg-[#7826b5]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#7826b5]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"leading-relaxed\", \"mb-4\"], [1, \"flex-1\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"transition-all\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"flex\", \"items-center\", \"justify-center\", \"gap-2\", 3, \"click\"], [\"class\", \"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"bg-orange-500\", \"text-white\", \"text-xs\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"text-xl\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"p-4\", \"rounded-lg\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"leading-relaxed\"], [1, \"fas\", \"fa-briefcase\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-3\"], [\"class\", \"flex flex-wrap gap-2\", 4, \"ngIf\", \"ngIfElse\"], [\"noSkills\", \"\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"italic\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-2xl\", \"max-w-4xl\", \"w-full\", \"max-h-[90vh]\", \"overflow-y-auto\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white\", \"hover:text-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"department\", \"placeholder\", \"e.g., Computer Science, Marketing\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Student, Professor, Developer\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"mt-6\"], [\"formControlName\", \"bio\", \"rows\", \"4\", \"placeholder\", \"Tell us about yourself, your interests, and goals...\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"resize-none\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"skills\", \"placeholder\", \"e.g., JavaScript, Python, Project Management (comma separated)\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex-shrink-0\"], [\"alt\", \"Profile preview\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", 3, \"src\"], [1, \"flex-1\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [1, \"flex\", \"justify-end\", \"space-x-4\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]\", \"hover:text-white\", \"dark:hover:bg-[#a0a0a0]\", \"dark:hover:text-black\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"h1\", 9);\n          i0.ɵɵtext(20, \" Mon Profil \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 10);\n          i0.ɵɵtext(22, \" G\\u00E9rez vos informations personnelles et vos pr\\u00E9f\\u00E9rences \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ProfileComponent_div_23_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(24, ProfileComponent_div_24_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(25, ProfileComponent_div_25_Template, 10, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, ProfileComponent_div_26_Template, 13, 9, \"div\", 14);\n          i0.ɵɵtemplate(27, ProfileComponent_div_27_Template, 128, 35, \"div\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", !ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i6.SlicePipe, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\"\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 4px solid rgba(0, 0, 0, 0.1);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  border-left-color: #09f;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.form-loading[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2ZpbGUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw4QkFBOEI7QUFDOUI7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULG9DQUFvQztFQUNwQyxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtBQUNmOztBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHVCQUF1QjtFQUN2QixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7QUFDQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtBQUN6QiIsImZpbGUiOiJwcm9maWxlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvZmlsZS9wcm9maWxlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsOEJBQThCO0FBQzlCO0VBQ0UsZUFBZTtFQUNmLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxvQ0FBb0M7RUFDcEMsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix1QkFBdUI7RUFDdkIsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGO0FBQ0E7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUEsb3FEQUFvcUQiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "ctx_r2", "message", "ɵɵlistener", "ProfileComponent_div_26_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "toggleEditMode", "ɵɵtemplate", "ProfileComponent_div_26_div_12_Template", "ɵɵstyleProp", "ctx_r3", "getProgressColor", "progressPercentage", "getMotivationalMessage", "ɵɵproperty", "ProfileComponent_div_27_button_13_Template_button_click_0_listener", "_r29", "ctx_r28", "onUpload", "ProfileComponent_div_27_button_13_i_1_Template", "ProfileComponent_div_27_button_13_i_2_Template", "ctx_r8", "uploadLoading", "ProfileComponent_div_27_button_14_Template_button_click_0_listener", "_r33", "ctx_r32", "removeProfileImage", "ProfileComponent_div_27_button_14_i_1_Template", "ProfileComponent_div_27_button_14_i_2_Template", "ctx_r9", "removeLoading", "ctx_r10", "user", "department", "ctx_r11", "position", "ctx_r12", "bio", "length", "ɵɵpipeBind3", "ctx_r35", "ProfileComponent_div_27_button_36_Template_button_click_0_listener", "restoredCtx", "_r37", "tab_r34", "$implicit", "ctx_r36", "activeTab", "id", "ProfileComponent_div_27_button_36_span_3_Template", "ɵɵclassProp", "ctx_r13", "ɵɵclassMap", "icon", "label", "ProfileComponent_div_27_div_38_Template_button_click_5_listener", "_r39", "ctx_r38", "setEditSection", "ctx_r14", "firstName", "lastName", "fullName", "dateOfBirth", "ɵɵpipeBind2", "phoneNumber", "address", "skill_r44", "ProfileComponent_div_27_div_39_div_34_span_1_Template", "ctx_r40", "skills", "ProfileComponent_div_27_div_39_Template_button_click_5_listener", "_r46", "ctx_r45", "ProfileComponent_div_27_div_39_div_34_Template", "ProfileComponent_div_27_div_39_ng_template_35_Template", "ɵɵtemplateRefExtractor", "ctx_r15", "ɵɵpipeBind1", "role", "createdAt", "_r41", "ctx_r16", "ctx_r17", "ctx_r18", "ctx_r19", "ctx_r20", "ctx_r21", "ctx_r22", "ctx_r23", "ProfileComponent_div_27_div_88_span_6_span_2_Template", "skill_r48", "last_r49", "ProfileComponent_div_27_div_88_span_6_Template", "ctx_r24", "ctx_r51", "getFieldError", "ctx_r52", "ctx_r53", "ctx_r54", "ctx_r55", "ctx_r56", "ɵɵnamespaceSVG", "ctx_r59", "ctx_r60", "ProfileComponent_div_27_div_127_Template_button_click_6_listener", "_r62", "ctx_r61", "cancelEdit", "ɵɵnamespaceHTML", "ProfileComponent_div_27_div_127_Template_form_ngSubmit_9_listener", "ctx_r63", "onEditSubmit", "ProfileComponent_div_27_div_127_p_15_Template", "ProfileComponent_div_27_div_127_p_20_Template", "ProfileComponent_div_27_div_127_p_25_Template", "ProfileComponent_div_27_div_127_p_30_Template", "ProfileComponent_div_27_div_127_p_39_Template", "ProfileComponent_div_27_div_127_p_52_Template", "ProfileComponent_div_27_div_127_Template_input_change_70_listener", "$event", "ctx_r64", "onFileSelected", "ProfileComponent_div_27_div_127_Template_button_click_74_listener", "ctx_r65", "ProfileComponent_div_27_div_127_span_77_Template", "ProfileComponent_div_27_div_127_span_78_Template", "ProfileComponent_div_27_div_127_div_79_Template", "ProfileComponent_div_27_div_127_div_80_Template", "ctx_r25", "editForm", "isFieldInvalid", "previewUrl", "getProfileImageUrl", "ɵɵsanitizeUrl", "editLoading", "ProfileComponent_div_27_Template_input_change_11_listener", "_r67", "ctx_r66", "ProfileComponent_div_27_button_13_Template", "ProfileComponent_div_27_button_14_Template", "ProfileComponent_div_27_span_24_Template", "ProfileComponent_div_27_span_25_Template", "ProfileComponent_div_27_p_26_Template", "ProfileComponent_div_27_Template_button_click_28_listener", "ctx_r68", "ProfileComponent_div_27_button_36_Template", "ProfileComponent_div_27_div_38_Template", "ProfileComponent_div_27_div_39_Template", "ProfileComponent_div_27_div_80_Template", "ProfileComponent_div_27_div_81_Template", "ProfileComponent_div_27_div_82_Template", "ProfileComponent_div_27_div_83_Template", "ProfileComponent_div_27_div_84_Template", "ProfileComponent_div_27_div_85_Template", "ProfileComponent_div_27_div_86_Template", "ProfileComponent_div_27_div_87_Template", "ProfileComponent_div_27_div_88_Template", "ProfileComponent_div_27_Template_button_click_109_listener", "ctx_r69", "logout", "ProfileComponent_div_27_div_127_Template", "ctx_r4", "selectedImage", "profileImage", "email", "profileTabs", "isEditMode", "ProfileComponent", "constructor", "authService", "authuserService", "dataService", "router", "fb", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "ngOnInit", "getProfile", "subscribe", "next", "res", "image", "trim", "profileImageURL", "calculateProfileCompletion", "populateEditForm", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "value", "toString", "hasProfileImage", "requiredPercentage", "optionalPercentage", "imagePercentage", "Math", "round", "patchValue", "Array", "isArray", "join", "event", "input", "target", "files", "file", "validTypes", "includes", "type", "resetFileInput", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "console", "log", "uploadProfileImage", "pipe", "response", "imageUrl", "updateCurrentUser", "setCurrentUser", "token", "localStorage", "setItem", "setTimeout", "err", "confirm", "fileInput", "document", "getElementById", "navigateTo", "path", "navigate", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "completeProfile", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "clearAuthData", "queryParams", "replaceUrl", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "i5", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_23_Template", "ProfileComponent_div_24_Template", "ProfileComponent_div_25_Template", "ProfileComponent_div_26_Template", "ProfileComponent_div_27_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Router } from '@angular/router';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.css'],\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  user: any;\r\n  selectedImage: File | null = null;\r\n  previewUrl: string | null = null;\r\n  message = '';\r\n  error = '';\r\n  uploadLoading = false;\r\n  removeLoading = false;\r\n\r\n  // Edit profile functionality\r\n  isEditMode = false;\r\n  editForm: FormGroup;\r\n  editLoading = false;\r\n  progressPercentage = 0;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private authuserService: AuthuserService,\r\n    private dataService: DataService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.editForm = this.fb.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      dateOfBirth: [''],\r\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\r\n      department: [''],\r\n      position: [''],\r\n      bio: ['', [Validators.minLength(10)]],\r\n      address: [''],\r\n      skills: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Load user profile using DataService\r\n    this.dataService.getProfile().subscribe({\r\n      next: (res) => {\r\n        this.user = res;\r\n\r\n        // Ensure image properties are consistent\r\n        if (!this.user.profileImage && this.user.image) {\r\n          this.user.profileImage = this.user.image;\r\n        } else if (!this.user.image && this.user.profileImage) {\r\n          this.user.image = this.user.profileImage;\r\n        }\r\n\r\n        // If no image is available, use default\r\n        if (\r\n          !this.user.profileImage ||\r\n          this.user.profileImage === 'null' ||\r\n          this.user.profileImage.trim() === ''\r\n        ) {\r\n          this.user.profileImage = 'assets/images/default-profile.png';\r\n          this.user.image = 'assets/images/default-profile.png';\r\n        }\r\n\r\n        // Ensure profileImageURL is also set for backward compatibility\r\n        if (!this.user.profileImageURL) {\r\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\r\n        }\r\n\r\n        // Calculate profile completion percentage\r\n        this.calculateProfileCompletion();\r\n\r\n        // Populate edit form with current user data\r\n        this.populateEditForm();\r\n      },\r\n      error: () => {\r\n        this.error = 'Failed to load profile.';\r\n      },\r\n    });\r\n  }\r\n\r\n  calculateProfileCompletion(): void {\r\n    if (!this.user) return;\r\n\r\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\r\n    const optionalFields = ['position', 'address', 'skills'];\r\n\r\n    let completedRequired = 0;\r\n    let completedOptional = 0;\r\n\r\n    // Check required fields\r\n    requiredFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\r\n        completedRequired++;\r\n      }\r\n    });\r\n\r\n    // Check optional fields\r\n    optionalFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '') {\r\n        completedOptional++;\r\n      }\r\n    });\r\n\r\n    // Check profile image\r\n    let hasProfileImage = 0;\r\n    if (this.user.profileImage &&\r\n        this.user.profileImage !== 'uploads/default.png' &&\r\n        this.user.profileImage !== 'assets/images/default-profile.png' &&\r\n        this.user.profileImage.trim() !== '') {\r\n      hasProfileImage = 1;\r\n    }\r\n\r\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\r\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\r\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\r\n    const imagePercentage = hasProfileImage * 10;\r\n\r\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\r\n  }\r\n\r\n  populateEditForm(): void {\r\n    if (!this.user) return;\r\n\r\n    this.editForm.patchValue({\r\n      firstName: this.user.firstName || '',\r\n      lastName: this.user.lastName || '',\r\n      fullName: this.user.fullName || '',\r\n      email: this.user.email || '',\r\n      dateOfBirth: this.user.dateOfBirth || '',\r\n      phoneNumber: this.user.phoneNumber || '',\r\n      department: this.user.department || '',\r\n      position: this.user.position || '',\r\n      bio: this.user.bio || '',\r\n      address: this.user.address || '',\r\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : (this.user.skills || '')\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns the appropriate profile image URL based on available properties\r\n   * Uses the same logic as in front-layout component for consistency\r\n   */\r\n  getProfileImageUrl(): string {\r\n    if (!this.user) return 'assets/images/default-profile.png';\r\n\r\n    // Check profileImage first\r\n    if (\r\n      this.user.profileImage &&\r\n      this.user.profileImage !== 'null' &&\r\n      this.user.profileImage.trim() !== ''\r\n    ) {\r\n      return this.user.profileImage;\r\n    }\r\n\r\n    // Then check image\r\n    if (\r\n      this.user.image &&\r\n      this.user.image !== 'null' &&\r\n      this.user.image.trim() !== ''\r\n    ) {\r\n      return this.user.image;\r\n    }\r\n\r\n    // Then check profileImageURL (for backward compatibility)\r\n    if (\r\n      this.user.profileImageURL &&\r\n      this.user.profileImageURL !== 'null' &&\r\n      this.user.profileImageURL.trim() !== ''\r\n    ) {\r\n      return this.user.profileImageURL;\r\n    }\r\n\r\n    // Default fallback\r\n    return 'assets/images/default-profile.png';\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      const file = input.files[0];\r\n\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.error = \"L'image ne doit pas dépasser 2MB\";\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      this.selectedImage = file;\r\n      this.error = '';\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        this.previewUrl = (e.target?.result as string) || null;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedImage) return;\r\n\r\n    this.uploadLoading = true; // Activer l'état de chargement\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\r\n\r\n    this.dataService\r\n      .uploadProfileImage(this.selectedImage)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.uploadLoading = false;\r\n          console.log('Upload finished, uploadLoading:', this.uploadLoading);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message = response.message || 'Profile updated successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = response.imageUrl;\r\n          this.user.profileImage = response.imageUrl;\r\n          this.user.image = response.imageUrl;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          this.selectedImage = null;\r\n          this.previewUrl = null;\r\n          this.resetFileInput();\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Upload failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  removeProfileImage(): void {\r\n    if (!confirm('Are you sure you want to remove your profile picture?'))\r\n      return;\r\n\r\n    this.removeLoading = true;\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    this.dataService\r\n      .removeProfileImage()\r\n      .pipe(finalize(() => (this.removeLoading = false)))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message =\r\n            response.message || 'Profile picture removed successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = null;\r\n          this.user.profileImage = null;\r\n          this.user.image = null;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Removal failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  private resetFileInput(): void {\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    const fileInput = document.getElementById(\r\n      'profile-upload'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n  }\r\n\r\n  navigateTo(path: string): void {\r\n    this.router.navigate([path]);\r\n  }\r\n\r\n  // Edit profile methods\r\n  toggleEditMode(): void {\r\n    this.isEditMode = !this.isEditMode;\r\n    if (this.isEditMode) {\r\n      this.populateEditForm();\r\n    }\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  onEditSubmit(): void {\r\n    if (this.editForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    this.editLoading = true;\r\n    this.error = '';\r\n    this.message = '';\r\n\r\n    const formData = new FormData();\r\n\r\n    // Add form fields\r\n    Object.keys(this.editForm.value).forEach(key => {\r\n      const value = this.editForm.value[key];\r\n      if (key === 'skills' && value) {\r\n        // Convert skills string to array\r\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\r\n        formData.append(key, JSON.stringify(skillsArray));\r\n      } else if (value) {\r\n        formData.append(key, value);\r\n      }\r\n    });\r\n\r\n    // Add profile image if selected\r\n    if (this.selectedImage) {\r\n      formData.append('image', this.selectedImage);\r\n    }\r\n\r\n    this.dataService.completeProfile(formData).subscribe({\r\n      next: (response: any) => {\r\n        this.editLoading = false;\r\n        this.message = 'Profile updated successfully!';\r\n\r\n        // Update current user data\r\n        this.user = { ...this.user, ...response.user };\r\n        this.authuserService.setCurrentUser(this.user);\r\n\r\n        // Recalculate progress\r\n        this.calculateProfileCompletion();\r\n\r\n        // Exit edit mode\r\n        this.isEditMode = false;\r\n\r\n        // Clear selected image\r\n        this.selectedImage = null;\r\n        this.previewUrl = null;\r\n        this.resetFileInput();\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.editLoading = false;\r\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\r\n\r\n        // Auto-hide error after 5 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 5000);\r\n      }\r\n    });\r\n  }\r\n\r\n  cancelEdit(): void {\r\n    this.isEditMode = false;\r\n    this.populateEditForm(); // Reset form to original values\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    this.resetFileInput();\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.editForm.controls).forEach(key => {\r\n      this.editForm.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  // Helper methods for template\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.editForm.get(fieldName);\r\n    if (field?.errors && field.touched) {\r\n      if (field.errors['required']) return `${fieldName} is required`;\r\n      if (field.errors['minlength']) return `${fieldName} is too short`;\r\n      if (field.errors['email']) return `Invalid email format`;\r\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.editForm.get(fieldName);\r\n    return !!(field?.invalid && field.touched);\r\n  }\r\n\r\n  getProgressColor(): string {\r\n    if (this.progressPercentage < 25) return '#ef4444'; // red\r\n    if (this.progressPercentage < 50) return '#f97316'; // orange\r\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\r\n    if (this.progressPercentage < 100) return '#22c55e'; // green\r\n    return '#10b981'; // emerald\r\n  }\r\n\r\n  getMotivationalMessage(): string {\r\n    if (this.progressPercentage < 25) {\r\n      return \"Let's complete your profile to unlock all features! 🚀\";\r\n    } else if (this.progressPercentage < 50) {\r\n      return \"You're making great progress! Keep going! 💪\";\r\n    } else if (this.progressPercentage < 75) {\r\n      return \"Excellent! You're more than halfway there! 🌟\";\r\n    } else if (this.progressPercentage < 100) {\r\n      return \"Almost perfect! Just a few more details! 🎯\";\r\n    } else {\r\n      return \"Perfect! Your profile is complete! ✨\";\r\n    }\r\n  }\r\n\r\n  logout(): void {\r\n    this.authuserService.logout().subscribe({\r\n      next: () => {\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Logout error:', err);\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {});\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-6 relative z-10\">\r\n    <!-- Page Title -->\r\n    <div class=\"mb-8\">\r\n      <h1\r\n        class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n      >\r\n        Mon Profil\r\n      </h1>\r\n      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n        Gérez vos informations personnelles et vos préférences\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"!user\" class=\"flex justify-center items-center py-20\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\r\n            Erreur\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div\r\n      *ngIf=\"message\"\r\n      class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-1\">\r\n            Succès\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            {{ message }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Completion Progress -->\r\n    <div *ngIf=\"user\" class=\"mb-8\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden\">\r\n        <!-- Decorative gradient top border -->\r\n        <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"></div>\r\n\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h3 class=\"text-lg font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n            Profile Completion\r\n          </h3>\r\n          <span class=\"text-2xl font-bold\" [style.color]=\"getProgressColor()\">\r\n            {{ progressPercentage }}%\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Progress Bar -->\r\n        <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-4 overflow-hidden mb-3\">\r\n          <div\r\n            class=\"h-full rounded-full transition-all duration-500 ease-out\"\r\n            [style.width.%]=\"progressPercentage\"\r\n            [style.background-color]=\"getProgressColor()\">\r\n          </div>\r\n        </div>\r\n\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n          {{ getMotivationalMessage() }}\r\n        </p>\r\n\r\n        <!-- Complete Profile Button (if not 100%) -->\r\n        <div *ngIf=\"progressPercentage < 100\" class=\"mt-4\">\r\n          <button\r\n            (click)=\"toggleEditMode()\"\r\n            class=\"px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all text-sm font-medium\">\r\n            Complete Profile\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Profile -->\r\n    <div *ngIf=\"user\" class=\"space-y-6\">\r\n      <!-- Profile Header Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n        ></div>\r\n\r\n        <!-- Profile Header Content -->\r\n        <div class=\"flex flex-col md:flex-row items-center md:items-start gap-6\">\r\n          <!-- Profile Image Section -->\r\n          <div class=\"flex-shrink-0 text-center\">\r\n            <div class=\"relative group/avatar\">\r\n              <img\r\n                [src]=\"getProfileImageUrl()\"\r\n                alt=\"Profile Image\"\r\n                class=\"w-32 h-32 rounded-full object-cover border-4 border-[#4f5fad] dark:border-[#6d78c9] shadow-lg group-hover/avatar:scale-105 transition-transform duration-300\"\r\n              />\r\n              <!-- Profile Image Upload Overlay -->\r\n              <div class=\"absolute inset-0 bg-black bg-opacity-50 rounded-full opacity-0 group-hover/avatar:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\r\n                <label for=\"profile-upload\" class=\"cursor-pointer text-white text-sm font-medium\">\r\n                  <i class=\"fas fa-camera text-xl mb-1 block\"></i>\r\n                  Change Photo\r\n                </label>\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"profile-upload\"\r\n                  accept=\"image/*\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  class=\"hidden\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Upload/Remove Buttons -->\r\n            <div class=\"flex gap-2 mt-4\">\r\n              <button\r\n                *ngIf=\"selectedImage\"\r\n                (click)=\"onUpload()\"\r\n                [disabled]=\"uploadLoading\"\r\n                class=\"px-3 py-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-xs rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50\">\r\n                <i *ngIf=\"!uploadLoading\" class=\"fas fa-upload mr-1\"></i>\r\n                <i *ngIf=\"uploadLoading\" class=\"fas fa-spinner fa-spin mr-1\"></i>\r\n                {{ uploadLoading ? 'Uploading...' : 'Upload' }}\r\n              </button>\r\n\r\n              <button\r\n                *ngIf=\"user.profileImage && user.profileImage !== 'assets/images/default-profile.png'\"\r\n                (click)=\"removeProfileImage()\"\r\n                [disabled]=\"removeLoading\"\r\n                class=\"px-3 py-1 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-all disabled:opacity-50\">\r\n                <i *ngIf=\"!removeLoading\" class=\"fas fa-trash mr-1\"></i>\r\n                <i *ngIf=\"removeLoading\" class=\"fas fa-spinner fa-spin mr-1\"></i>\r\n                {{ removeLoading ? 'Removing...' : 'Remove' }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Profile Info Section -->\r\n          <div class=\"flex-1 text-center md:text-left\">\r\n            <h2 class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\">\r\n              {{ user.fullName || (user.firstName + ' ' + user.lastName) }}\r\n            </h2>\r\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mb-2\">{{ user.email }}</p>\r\n            <div class=\"flex flex-wrap gap-2 justify-center md:justify-start mb-4\">\r\n              <span class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full\">\r\n                {{ user.role | titlecase }}\r\n              </span>\r\n              <span *ngIf=\"user.department\" class=\"px-3 py-1 bg-[#3d4a85]/10 dark:bg-[#4f5fad]/10 text-[#3d4a85] dark:text-[#4f5fad] text-sm rounded-full\">\r\n                {{ user.department }}\r\n              </span>\r\n              <span *ngIf=\"user.position\" class=\"px-3 py-1 bg-[#7826b5]/10 dark:bg-[#6d78c9]/10 text-[#7826b5] dark:text-[#6d78c9] text-sm rounded-full\">\r\n                {{ user.position }}\r\n              </span>\r\n            </div>\r\n\r\n            <!-- Bio Preview -->\r\n            <p *ngIf=\"user.bio\" class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-4\">\r\n              {{ user.bio.length > 150 ? (user.bio | slice:0:150) + '...' : user.bio }}\r\n            </p>\r\n\r\n            <!-- Quick Actions -->\r\n            <div class=\"flex flex-wrap gap-2 justify-center md:justify-start\">\r\n              <button\r\n                (click)=\"toggleEditMode()\"\r\n                class=\"px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-sm rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all flex items-center\">\r\n                <i class=\"fas fa-edit mr-2\"></i>\r\n                Edit Profile\r\n              </button>\r\n              <a\r\n                [routerLink]=\"user.role === 'admin' ? '/admin/dashboard' : '/home'\"\r\n                class=\"px-4 py-2 border border-[#4f5fad] dark:border-[#6d78c9] text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all flex items-center\">\r\n                <i class=\"fas fa-home mr-2\"></i>\r\n                Dashboard\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Navigation Tabs -->\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\r\n        <div class=\"flex flex-wrap border-b border-[#edf1f4] dark:border-[#2a2a2a]\">\r\n          <button\r\n            *ngFor=\"let tab of profileTabs\"\r\n            (click)=\"activeTab = tab.id\"\r\n            [class.bg-gradient-to-r]=\"activeTab === tab.id\"\r\n            [class.from-[#4f5fad]]=\"activeTab === tab.id\"\r\n            [class.to-[#7826b5]]=\"activeTab === tab.id\"\r\n            [class.text-white]=\"activeTab === tab.id\"\r\n            [class.text-[#6d6870]]=\"activeTab !== tab.id\"\r\n            [class.dark:text-[#a0a0a0]]=\"activeTab !== tab.id\"\r\n            class=\"flex-1 px-4 py-3 text-sm font-medium transition-all hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center justify-center gap-2\">\r\n            <i [class]=\"tab.icon\"></i>\r\n            {{ tab.label }}\r\n            <span *ngIf=\"tab.id === 'completion' && progressPercentage < 100\"\r\n                  class=\"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\">\r\n              {{ progressPercentage }}%\r\n            </span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Tab Content -->\r\n        <div class=\"p-6\">\r\n          <!-- Personal Information Tab -->\r\n          <div *ngIf=\"activeTab === 'personal'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-user mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Personal Information\r\n              </h3>\r\n              <button\r\n                (click)=\"setEditSection('personal')\"\r\n                class=\"px-3 py-1 text-sm bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] rounded-lg hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 transition-all\">\r\n                <i class=\"fas fa-edit mr-1\"></i>\r\n                Edit\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- First Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">First Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.firstName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Last Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Last Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.lastName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Full Name -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Full Name</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.fullName || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Date of Birth -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Date of Birth</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">\r\n                  {{ user.dateOfBirth ? (user.dateOfBirth | date:'mediumDate') : 'Not provided' }}\r\n                </p>\r\n              </div>\r\n\r\n              <!-- Phone Number -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Phone Number</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.phoneNumber || 'Not provided' }}</p>\r\n              </div>\r\n\r\n              <!-- Address -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Address</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.address || 'Not provided' }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Bio Section -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n              <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-2\">Bio</label>\r\n              <p class=\"text-[#4f5fad] dark:text-[#6d78c9] leading-relaxed\">\r\n                {{ user.bio || 'Tell us about yourself, your interests, and goals...' }}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Professional Information Tab -->\r\n          <div *ngIf=\"activeTab === 'professional'\" class=\"space-y-6\">\r\n            <div class=\"flex items-center justify-between mb-6\">\r\n              <h3 class=\"text-xl font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center\">\r\n                <i class=\"fas fa-briefcase mr-3 text-[#4f5fad] dark:text-[#6d78c9]\"></i>\r\n                Professional Information\r\n              </h3>\r\n              <button\r\n                (click)=\"setEditSection('professional')\"\r\n                class=\"px-3 py-1 text-sm bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] rounded-lg hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 transition-all\">\r\n                <i class=\"fas fa-edit mr-1\"></i>\r\n                Edit\r\n              </button>\r\n            </div>\r\n\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- Department -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Department</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.department || 'Not specified' }}</p>\r\n              </div>\r\n\r\n              <!-- Position -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Position</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.position || 'Not specified' }}</p>\r\n              </div>\r\n\r\n              <!-- Role -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Role</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.role | titlecase }}</p>\r\n              </div>\r\n\r\n              <!-- Member Since -->\r\n              <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n                <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1\">Member Since</label>\r\n                <p class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ user.createdAt | date:'mediumDate' }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Skills Section -->\r\n            <div class=\"bg-[#f8fafc] dark:bg-[#2a2a2a] p-4 rounded-lg\">\r\n              <label class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-3\">Skills & Expertise</label>\r\n              <div *ngIf=\"user.skills && user.skills.length > 0; else noSkills\" class=\"flex flex-wrap gap-2\">\r\n                <span *ngFor=\"let skill of user.skills\"\r\n                      class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20\">\r\n                  {{ skill }}\r\n                </span>\r\n              </div>\r\n              <ng-template #noSkills>\r\n                <p class=\"text-[#6d6870] dark:text-[#a0a0a0] italic\">No skills added yet</p>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n        <!-- Account Details -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Informations du compte\r\n          </h3>\r\n\r\n          <div class=\"space-y-4\">\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Nom complet\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.fullName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Adresse email\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.email }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Type de compte\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.role | titlecase }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Membre depuis\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.createdAt | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Additional Profile Fields -->\r\n            <div *ngIf=\"user.firstName\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Prénom\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.firstName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.lastName\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Nom de famille\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.lastName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.dateOfBirth\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Date de naissance\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.dateOfBirth | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.phoneNumber\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Téléphone\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.phoneNumber }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.department\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Département\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.department }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.position\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Position\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.position }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.bio\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Bio\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.bio }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.address\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Adresse\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.address }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.skills && user.skills.length > 0\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Compétences\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                <span *ngFor=\"let skill of user.skills; let last = last\">\r\n                  {{ skill }}<span *ngIf=\"!last\">, </span>\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Account Actions -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\r\n                />\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Actions du compte\r\n          </h3>\r\n\r\n          <div class=\"flex flex-wrap gap-3\">\r\n            <!-- Change Password Button -->\r\n            <a\r\n              routerLink=\"/change-password\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#4f5fad] dark:text-[#6d78c9] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#4f5fad] dark:border-[#6d78c9]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Changer le mot de passe\r\n              </span>\r\n            </a>\r\n\r\n            <!-- Logout Button -->\r\n            <button\r\n              (click)=\"logout()\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#ff6b69] dark:text-[#ff8785] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#ff6b69] dark:border-[#ff8785]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Déconnexion\r\n              </span>\r\n            </button>\r\n\r\n            <!-- Dashboard Button -->\r\n            <a\r\n              [routerLink]=\"\r\n                user.role === 'admin' ? '/admin/dashboard' : '/home'\r\n              \"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#6d6870] dark:text-[#a0a0a0] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#6d6870] dark:border-[#a0a0a0]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Tableau de bord\r\n              </span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Edit Profile Modal/Form -->\r\n    <div *ngIf=\"isEditMode\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n        <!-- Header -->\r\n        <div class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] p-6 rounded-t-2xl\">\r\n          <div class=\"flex justify-between items-center\">\r\n            <h2 class=\"text-2xl font-bold text-white\">Edit Profile</h2>\r\n            <button\r\n              (click)=\"cancelEdit()\"\r\n              class=\"text-white hover:text-gray-200 transition-colors\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form Content -->\r\n        <form [formGroup]=\"editForm\" (ngSubmit)=\"onEditSubmit()\" class=\"p-6\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <!-- First Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                First Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"firstName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('firstName')\"\r\n                placeholder=\"Enter your first name\">\r\n              <p *ngIf=\"getFieldError('firstName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('firstName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Last Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Last Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"lastName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('lastName')\"\r\n                placeholder=\"Enter your last name\">\r\n              <p *ngIf=\"getFieldError('lastName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('lastName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Full Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Full Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"fullName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('fullName')\"\r\n                placeholder=\"Enter your full name\">\r\n              <p *ngIf=\"getFieldError('fullName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('fullName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Email -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Email *\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('email')\"\r\n                placeholder=\"Enter your email\">\r\n              <p *ngIf=\"getFieldError('email')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('email') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Date of Birth -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Date of Birth\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                formControlName=\"dateOfBirth\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n            </div>\r\n\r\n            <!-- Phone Number -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Phone Number\r\n              </label>\r\n              <input\r\n                type=\"tel\"\r\n                formControlName=\"phoneNumber\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('phoneNumber')\"\r\n                placeholder=\"Enter your phone number\">\r\n              <p *ngIf=\"getFieldError('phoneNumber')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('phoneNumber') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Department -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Department\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"department\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Computer Science, Marketing\">\r\n            </div>\r\n\r\n            <!-- Position -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Position\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"position\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Student, Professor, Developer\">\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Bio -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Bio\r\n            </label>\r\n            <textarea\r\n              formControlName=\"bio\"\r\n              rows=\"4\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all resize-none\"\r\n              [class.border-red-500]=\"isFieldInvalid('bio')\"\r\n              placeholder=\"Tell us about yourself, your interests, and goals...\"></textarea>\r\n            <p *ngIf=\"getFieldError('bio')\" class=\"text-red-500 text-sm mt-1\">\r\n              {{ getFieldError('bio') }}\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Address -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Address\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"address\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"Enter your address\">\r\n          </div>\r\n\r\n          <!-- Skills -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Skills\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"skills\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"e.g., JavaScript, Python, Project Management (comma separated)\">\r\n            <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n              Separate skills with commas\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Profile Picture Upload -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Profile Picture\r\n            </label>\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"flex-shrink-0\">\r\n                <img\r\n                  [src]=\"previewUrl || getProfileImageUrl()\"\r\n                  alt=\"Profile preview\"\r\n                  class=\"w-20 h-20 rounded-full object-cover border-2 border-[#4f5fad] dark:border-[#6d78c9]\">\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <input\r\n                  type=\"file\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  accept=\"image/*\"\r\n                  class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n                <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n                  Upload a new profile picture (optional)\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"flex justify-end space-x-4 mt-8 pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\r\n            <button\r\n              type=\"button\"\r\n              (click)=\"cancelEdit()\"\r\n              class=\"px-6 py-3 text-[#6d6870] dark:text-[#a0a0a0] border border-[#6d6870] dark:border-[#a0a0a0] rounded-lg hover:bg-[#6d6870] hover:text-white dark:hover:bg-[#a0a0a0] dark:hover:text-black transition-all\">\r\n              Cancel\r\n            </button>\r\n\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"editLoading\"\r\n              class=\"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\">\r\n              <span *ngIf=\"!editLoading\">Save Changes</span>\r\n              <span *ngIf=\"editLoading\" class=\"flex items-center\">\r\n                <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n                Saving...\r\n              </span>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Messages -->\r\n          <div *ngIf=\"message\" class=\"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n            <p class=\"text-green-800 dark:text-green-200\">{{ message }}</p>\r\n          </div>\r\n\r\n          <div *ngIf=\"error\" class=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\r\n            <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;IC0CrCC,EAAA,CAAAC,cAAA,cAAkE;IAE9DD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAMvER,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;;IAkCFX,EAAA,CAAAC,cAAA,cAAmD;IAE/CD,EAAA,CAAAY,UAAA,mBAAAC,gEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAjCfH,EAAA,CAAAC,cAAA,aAA+B;IAG3BD,EAAA,CAAAE,SAAA,cAAwI;IAExIF,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,SAAA,cAIM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAoB,UAAA,KAAAC,uCAAA,kBAMM;IACRrB,EAAA,CAAAG,YAAA,EAAM;;;;IA1B+BH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAC,gBAAA,GAAkC;IACjExB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAE,kBAAA,OACF;IAOEzB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAE,kBAAA,MAAoC,qBAAAF,MAAA,CAAAC,gBAAA;IAMtCxB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAG,sBAAA,QACF;IAGM1B,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2B,UAAA,SAAAJ,MAAA,CAAAE,kBAAA,OAA8B;;;;;IAsD5BzB,EAAA,CAAAE,SAAA,aAAyD;;;;;IACzDF,EAAA,CAAAE,SAAA,aAAiE;;;;;;IANnEF,EAAA,CAAAC,cAAA,kBAI0K;IAFxKD,EAAA,CAAAY,UAAA,mBAAAgB,mEAAA;MAAA5B,EAAA,CAAAc,aAAA,CAAAe,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAY,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAGpB/B,EAAA,CAAAoB,UAAA,IAAAY,8CAAA,iBAAyD;IACzDhC,EAAA,CAAAoB,UAAA,IAAAa,8CAAA,iBAAiE;IACjEjC,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAA2B,UAAA,aAAAO,MAAA,CAAAC,aAAA,CAA0B;IAEtBnC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAO,MAAA,CAAAC,aAAA,CAAoB;IACpBnC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAO,MAAA,CAAAC,aAAA,CAAmB;IACvBnC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyB,MAAA,CAAAC,aAAA,kCACF;;;;;IAOEnC,EAAA,CAAAE,SAAA,aAAwD;;;;;IACxDF,EAAA,CAAAE,SAAA,aAAiE;;;;;;IANnEF,EAAA,CAAAC,cAAA,kBAIiH;IAF/GD,EAAA,CAAAY,UAAA,mBAAAwB,mEAAA;MAAApC,EAAA,CAAAc,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BvC,EAAA,CAAAoB,UAAA,IAAAoB,8CAAA,iBAAwD;IACxDxC,EAAA,CAAAoB,UAAA,IAAAqB,8CAAA,iBAAiE;IACjEzC,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAA2B,UAAA,aAAAe,MAAA,CAAAC,aAAA,CAA0B;IAEtB3C,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAe,MAAA,CAAAC,aAAA,CAAoB;IACpB3C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAe,MAAA,CAAAC,aAAA,CAAmB;IACvB3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAiC,MAAA,CAAAC,aAAA,iCACF;;;;;IAcA3C,EAAA,CAAAC,cAAA,gBAA6I;IAC3ID,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAmC,OAAA,CAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IACA9C,EAAA,CAAAC,cAAA,gBAA2I;IACzID,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsC,OAAA,CAAAF,IAAA,CAAAG,QAAA,MACF;;;;;IAIFhD,EAAA,CAAAC,cAAA,aAA4F;IAC1FD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAwC,OAAA,CAAAJ,IAAA,CAAAK,GAAA,CAAAC,MAAA,SAAAnD,EAAA,CAAAoD,WAAA,OAAAH,OAAA,CAAAJ,IAAA,CAAAK,GAAA,oBAAAD,OAAA,CAAAJ,IAAA,CAAAK,GAAA,MACF;;;;;IAoCAlD,EAAA,CAAAC,cAAA,gBACsE;IACpED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,OAAA,CAAA5B,kBAAA,OACF;;;;;;IAfFzB,EAAA,CAAAC,cAAA,kBASsJ;IAPpJD,EAAA,CAAAY,UAAA,mBAAA0C,mEAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAc,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAAyC,OAAA,CAAAC,SAAA,GAAAH,OAAA,CAAAI,EAAA;IAAA,EAA4B;IAQ5B7D,EAAA,CAAAE,SAAA,QAA0B;IAC1BF,EAAA,CAAAI,MAAA,GACA;IAAAJ,EAAA,CAAAoB,UAAA,IAAA0C,iDAAA,oBAGO;IACT9D,EAAA,CAAAG,YAAA,EAAS;;;;;IAbPH,EAAA,CAAA+D,WAAA,qBAAAC,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,CAA+C,mBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,kBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,gBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,oBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA,yBAAAG,OAAA,CAAAJ,SAAA,KAAAH,OAAA,CAAAI,EAAA;IAO5C7D,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAiE,UAAA,CAAAR,OAAA,CAAAS,IAAA,CAAkB;IACrBlE,EAAA,CAAAK,SAAA,GACA;IADAL,EAAA,CAAAS,kBAAA,MAAAgD,OAAA,CAAAU,KAAA,MACA;IAAOnE,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAA2B,UAAA,SAAA8B,OAAA,CAAAI,EAAA,qBAAAG,OAAA,CAAAvC,kBAAA,OAAyD;;;;;;IAUlEzB,EAAA,CAAAC,cAAA,cAAwD;IAGlDD,EAAA,CAAAE,SAAA,aAAmE;IACnEF,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAE+K;IAD7KD,EAAA,CAAAY,UAAA,mBAAAwD,gEAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IAEpCvE,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAmD;IAGkCD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACnGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAsC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAItGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAClGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIrGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAClGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIrGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtGH,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAChGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAoC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAKtGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,WAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC5FH,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAzCwDH,EAAA,CAAAK,SAAA,IAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA4B,SAAA,mBAAsC;IAMtCzE,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA6B,QAAA,mBAAqC;IAMrC1E,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAA8B,QAAA,mBAAqC;IAO7F3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA+D,OAAA,CAAA3B,IAAA,CAAA+B,WAAA,GAAA5E,EAAA,CAAA6E,WAAA,QAAAL,OAAA,CAAA3B,IAAA,CAAA+B,WAAA,sCACF;IAM0D5E,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAAiC,WAAA,mBAAwC;IAMxC9E,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,iBAAA,CAAAkE,OAAA,CAAA3B,IAAA,CAAAkC,OAAA,mBAAoC;IAQ9F/E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA+D,OAAA,CAAA3B,IAAA,CAAAK,GAAA,gEACF;;;;;IAiDElD,EAAA,CAAAC,cAAA,gBACyK;IACvKD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuE,SAAA,MACF;;;;;IAJFhF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAoB,UAAA,IAAA6D,qDAAA,oBAGO;IACTjF,EAAA,CAAAG,YAAA,EAAM;;;;IAJoBH,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,YAAAuD,OAAA,CAAArC,IAAA,CAAAsC,MAAA,CAAc;;;;;IAMtCnF,EAAA,CAAAC,cAAA,aAAqD;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAlDlFH,EAAA,CAAAC,cAAA,cAA4D;IAGtDD,EAAA,CAAAE,SAAA,aAAwE;IACxEF,EAAA,CAAAI,MAAA,iCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,kBAE+K;IAD7KD,EAAA,CAAAY,UAAA,mBAAAwE,gEAAA;MAAApF,EAAA,CAAAc,aAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAAtF,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAoE,OAAA,CAAAf,cAAA,CAAe,cAAc,CAAC;IAAA,EAAC;IAExCvE,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGXH,EAAA,CAAAC,cAAA,eAAmD;IAGkCD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACnGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACjGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAsC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAItGH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAA2B;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI3FH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAI,MAAA,IAAwC;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAK1GH,EAAA,CAAAC,cAAA,gBAA2D;IACwBD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC3GH,EAAA,CAAAoB,UAAA,KAAAmE,8CAAA,mBAKM;IACNvF,EAAA,CAAAoB,UAAA,KAAAoE,sDAAA,kCAAAxF,EAAA,CAAAyF,sBAAA,CAEc;IAChBzF,EAAA,CAAAG,YAAA,EAAM;;;;;IAlCwDH,EAAA,CAAAK,SAAA,IAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAoF,OAAA,CAAA7C,IAAA,CAAAC,UAAA,oBAAwC;IAMxC9C,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAoF,OAAA,CAAA7C,IAAA,CAAAG,QAAA,oBAAsC;IAMtChD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAA2F,WAAA,QAAAD,OAAA,CAAA7C,IAAA,CAAA+C,IAAA,EAA2B;IAM3B5F,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAA6E,WAAA,QAAAa,OAAA,CAAA7C,IAAA,CAAAgD,SAAA,gBAAwC;IAO9F7F,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA2B,UAAA,SAAA+D,OAAA,CAAA7C,IAAA,CAAAsC,MAAA,IAAAO,OAAA,CAAA7C,IAAA,CAAAsC,MAAA,CAAAhC,MAAA,KAA6C,aAAA2C,IAAA;;;;;IAqIrD9F,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsF,OAAA,CAAAlD,IAAA,CAAA4B,SAAA,MACF;;;;;IAGFzE,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuF,OAAA,CAAAnD,IAAA,CAAA6B,QAAA,MACF;;;;;IAGF1E,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA6E,WAAA,OAAAoB,OAAA,CAAApD,IAAA,CAAA+B,WAAA,qBACF;;;;;IAGF5E,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,OAAA,CAAArD,IAAA,CAAAiC,WAAA,MACF;;;;;IAGF9E,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA0F,OAAA,CAAAtD,IAAA,CAAAC,UAAA,MACF;;;;;IAGF9C,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA2F,OAAA,CAAAvD,IAAA,CAAAG,QAAA,MACF;;;;;IAGFhD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4F,OAAA,CAAAxD,IAAA,CAAAK,GAAA,MACF;;;;;IAGFlD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA6F,OAAA,CAAAzD,IAAA,CAAAkC,OAAA,MACF;;;;;IAoBe/E,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAI,MAAA,SAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAD1CH,EAAA,CAAAC,cAAA,WAAyD;IACvDD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAoB,UAAA,IAAAmF,qDAAA,oBAA6B;IAC1CvG,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAS,kBAAA,MAAA+F,SAAA,KAAW;IAAOxG,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,UAAA,UAAA8E,QAAA,CAAW;;;;;IAjBnCzG,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAoB,UAAA,IAAAsF,8CAAA,oBAEO;IACT1G,EAAA,CAAAG,YAAA,EAAM;;;;IAHoBH,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA2B,UAAA,YAAAgF,OAAA,CAAA9D,IAAA,CAAAsC,MAAA,CAAgB;;;;;IA8M1CnF,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAmG,OAAA,CAAAC,aAAA,mBACF;;;;;IAcA7G,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAqG,OAAA,CAAAD,aAAA,kBACF;;;;;IAcA7G,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsG,OAAA,CAAAF,aAAA,kBACF;;;;;IAcA7G,EAAA,CAAAC,cAAA,aAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuG,OAAA,CAAAH,aAAA,eACF;;;;;IAyBA7G,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAwG,OAAA,CAAAJ,aAAA,qBACF;;;;;IAuCF7G,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyG,OAAA,CAAAL,aAAA,aACF;;;;;IAoEE7G,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,gBAAoD;IAClDD,EAAA,CAAAmH,cAAA,EAA2H;IAA3HnH,EAAA,CAAAC,cAAA,eAA2H;IACzHD,EAAA,CAAAE,SAAA,kBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAKXH,EAAA,CAAAC,cAAA,eAAgI;IAChFD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAjBH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,iBAAA,CAAA8G,OAAA,CAAAzG,OAAA,CAAa;;;;;IAG7DX,EAAA,CAAAC,cAAA,eAAsH;IAC1ED,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAA+G,OAAA,CAAA7G,KAAA,CAAW;;;;;;IAzO7DR,EAAA,CAAAC,cAAA,eAA+G;IAK7DD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,kBAE2D;IADzDD,EAAA,CAAAY,UAAA,mBAAA0G,iEAAA;MAAAtH,EAAA,CAAAc,aAAA,CAAAyG,IAAA;MAAA,MAAAC,OAAA,GAAAxH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAsG,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtBzH,EAAA,CAAAmH,cAAA,EAA8G;IAA9GnH,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAiG;IACnGF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAA0H,eAAA,EAAqE;IAArE1H,EAAA,CAAAC,cAAA,gBAAqE;IAAxCD,EAAA,CAAAY,UAAA,sBAAA+G,kEAAA;MAAA3H,EAAA,CAAAc,aAAA,CAAAyG,IAAA;MAAA,MAAAK,OAAA,GAAA5H,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAA0G,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACtD7H,EAAA,CAAAC,cAAA,gBAAmD;IAI7CD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKsC;IACtCF,EAAA,CAAAoB,UAAA,KAAA0G,6CAAA,iBAEI;IACN9H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAA2G,6CAAA,iBAEI;IACN/H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAA4G,6CAAA,iBAEI;IACNhI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKiC;IACjCF,EAAA,CAAAoB,UAAA,KAAA6G,6CAAA,iBAEI;IACNjI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAGyS;IAC3SF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKwC;IACxCF,EAAA,CAAAoB,UAAA,KAAA8G,6CAAA,iBAEI;IACNlI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIoD;IACtDF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,qBAKgF;IAChFF,EAAA,CAAAoB,UAAA,KAAA+G,6CAAA,iBAEI;IACNnI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAImC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAI+E;IAC/EF,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAE,SAAA,gBAG8F;IAChGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAoB;IAGhBD,EAAA,CAAAY,UAAA,oBAAAwH,kEAAAC,MAAA;MAAArI,EAAA,CAAAc,aAAA,CAAAyG,IAAA;MAAA,MAAAe,OAAA,GAAAtI,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAoH,OAAA,CAAAC,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAFnCrI,EAAA,CAAAG,YAAA,EAIyS;IACzSH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAMVH,EAAA,CAAAC,cAAA,gBAAkG;IAG9FD,EAAA,CAAAY,UAAA,mBAAA4H,kEAAA;MAAAxI,EAAA,CAAAc,aAAA,CAAAyG,IAAA;MAAA,MAAAkB,OAAA,GAAAzI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAuH,OAAA,CAAAhB,UAAA,EAAY;IAAA,EAAC;IAEtBzH,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,mBAG8L;IAC5LD,EAAA,CAAAoB,UAAA,KAAAsH,gDAAA,oBAA8C;IAC9C1I,EAAA,CAAAoB,UAAA,KAAAuH,gDAAA,oBAMO;IACT3I,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAoB,UAAA,KAAAwH,+CAAA,mBAEM;IAEN5I,EAAA,CAAAoB,UAAA,KAAAyH,+CAAA,mBAEM;IACR7I,EAAA,CAAAG,YAAA,EAAO;;;;IA1NDH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,cAAAmH,OAAA,CAAAC,QAAA,CAAsB;IAWpB/I,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,cAAoD;IAElDhJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,cAAgC;IAclC7G,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,aAAmD;IAEjDhJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,aAA+B;IAcjC7G,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,aAAmD;IAEjDhJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,aAA+B;IAcjC7G,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,UAAgD;IAE9ChJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,UAA4B;IAyB9B7G,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,gBAAsD;IAEpDhJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,gBAAkC;IAuCtC7G,EAAA,CAAAK,SAAA,IAA8C;IAA9CL,EAAA,CAAA+D,WAAA,mBAAA+E,OAAA,CAAAE,cAAA,QAA8C;IAE5ChJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAjC,aAAA,QAA0B;IAwCxB7G,EAAA,CAAAK,SAAA,IAA0C;IAA1CL,EAAA,CAAA2B,UAAA,QAAAmH,OAAA,CAAAG,UAAA,IAAAH,OAAA,CAAAI,kBAAA,IAAAlJ,EAAA,CAAAmJ,aAAA,CAA0C;IA4B9CnJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA2B,UAAA,aAAAmH,OAAA,CAAAM,WAAA,CAAwB;IAEjBpJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA2B,UAAA,UAAAmH,OAAA,CAAAM,WAAA,CAAkB;IAClBpJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAM,WAAA,CAAiB;IAWtBpJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAnI,OAAA,CAAa;IAIbX,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,UAAA,SAAAmH,OAAA,CAAAtI,KAAA,CAAW;;;;;;IAv7BvBR,EAAA,CAAAC,cAAA,cAAoC;IAMhCD,EAAA,CAAAE,SAAA,cAEO;IAGPF,EAAA,CAAAC,cAAA,cAAyE;IAInED,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAC,cAAA,cAA4K;IAExKD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAME;IAFAD,EAAA,CAAAY,UAAA,oBAAAyI,0DAAAhB,MAAA;MAAArI,EAAA,CAAAc,aAAA,CAAAwI,IAAA;MAAA,MAAAC,OAAA,GAAAvJ,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAqI,OAAA,CAAAhB,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAJnCrI,EAAA,CAAAG,YAAA,EAME;IAKNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAoB,UAAA,KAAAoI,0CAAA,qBAQS;IAETxJ,EAAA,CAAAoB,UAAA,KAAAqI,0CAAA,qBAQS;IACXzJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,eAAuE;IAEnED,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAoB,UAAA,KAAAsI,wCAAA,mBAEO;IACP1J,EAAA,CAAAoB,UAAA,KAAAuI,wCAAA,mBAEO;IACT3J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAoB,UAAA,KAAAwI,qCAAA,gBAEI;IAGJ5J,EAAA,CAAAC,cAAA,eAAkE;IAE9DD,EAAA,CAAAY,UAAA,mBAAAiJ,0DAAA;MAAA7J,EAAA,CAAAc,aAAA,CAAAwI,IAAA;MAAA,MAAAQ,OAAA,GAAA9J,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA4I,OAAA,CAAA3I,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,aAE2O;IACzOD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAOZH,EAAA,CAAAC,cAAA,eAAuK;IAEnKD,EAAA,CAAAoB,UAAA,KAAA2I,0CAAA,sBAgBS;IACX/J,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiB;IAEfD,EAAA,CAAAoB,UAAA,KAAA4I,uCAAA,oBA6DM;IAGNhK,EAAA,CAAAoB,UAAA,KAAA6I,uCAAA,oBAqDM;IAERjK,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAmH,cAAA,EAMC;IANDnH,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA0H,eAAA,EAEC;IAFD1H,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAuB;IAKjBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAoB,UAAA,KAAA8I,uCAAA,kBAkBM;IAENlK,EAAA,CAAAoB,UAAA,KAAA+I,uCAAA,kBAkBM;IAENnK,EAAA,CAAAoB,UAAA,KAAAgJ,uCAAA,kBAkBM;IAENpK,EAAA,CAAAoB,UAAA,KAAAiJ,uCAAA,kBAkBM;IAENrK,EAAA,CAAAoB,UAAA,KAAAkJ,uCAAA,kBAkBM;IAENtK,EAAA,CAAAoB,UAAA,KAAAmJ,uCAAA,kBAkBM;IAENvK,EAAA,CAAAoB,UAAA,KAAAoJ,uCAAA,kBAkBM;IAENxK,EAAA,CAAAoB,UAAA,KAAAqJ,uCAAA,kBAkBM;IAENzK,EAAA,CAAAoB,UAAA,KAAAsJ,uCAAA,kBAoBM;IACR1K,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAmH,cAAA,EAMC;IANDnH,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IAOJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA0H,eAAA,EAEC;IAFD1H,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAkC;IAM9BD,EAAA,CAAAE,SAAA,gBAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IAEGD,EAAA,CAAAmH,cAAA,EAMC;IANDnH,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA0H,eAAA,EAEC;IAFD1H,EAAA,CAAAE,SAAA,gBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,mBAGC;IAFCD,EAAA,CAAAY,UAAA,mBAAA+J,2DAAA;MAAA3K,EAAA,CAAAc,aAAA,CAAAwI,IAAA;MAAA,MAAAsB,OAAA,GAAA5K,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA0J,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlB7K,EAAA,CAAAE,SAAA,gBAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IAEGD,EAAA,CAAAmH,cAAA,EAMC;IANDnH,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA0H,eAAA,EAEC;IAFD1H,EAAA,CAAAE,SAAA,iBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,iBAEO;IAIPF,EAAA,CAAAC,cAAA,kBAEC;IAEGD,EAAA,CAAAmH,cAAA,EAMC;IANDnH,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,kBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA0H,eAAA,EAEC;IAFD1H,EAAA,CAAAE,SAAA,iBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAQjBH,EAAA,CAAAoB,UAAA,MAAA0J,wCAAA,qBA6OM;IACR9K,EAAA,CAAAG,YAAA,EAAM;;;;IA76BQH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,UAAA,QAAAoJ,MAAA,CAAA7B,kBAAA,IAAAlJ,EAAA,CAAAmJ,aAAA,CAA4B;IAuB3BnJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAC,aAAA,CAAmB;IAUnBhL,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAoI,YAAA,IAAAF,MAAA,CAAAlI,IAAA,CAAAoI,YAAA,yCAAoF;IAcvFjL,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsK,MAAA,CAAAlI,IAAA,CAAA8B,QAAA,IAAAoG,MAAA,CAAAlI,IAAA,CAAA4B,SAAA,SAAAsG,MAAA,CAAAlI,IAAA,CAAA6B,QAAA,MACF;IACmD1E,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAyK,MAAA,CAAAlI,IAAA,CAAAqI,KAAA,CAAgB;IAG/DlL,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA2F,WAAA,SAAAoF,MAAA,CAAAlI,IAAA,CAAA+C,IAAA,OACF;IACO5F,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAC,UAAA,CAAqB;IAGrB9C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAG,QAAA,CAAmB;IAMxBhD,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAK,GAAA,CAAc;IAadlD,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAA2B,UAAA,eAAAoJ,MAAA,CAAAlI,IAAA,CAAA+C,IAAA,4CAAmE;IAcvD5F,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,YAAAoJ,MAAA,CAAAI,WAAA,CAAc;IAqB1BnL,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAnH,SAAA,gBAA8B;IAgE9B5D,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAnH,SAAA,oBAAkC;IA+GlC5D,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsK,MAAA,CAAAlI,IAAA,CAAA8B,QAAA,MACF;IAmBE3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAsK,MAAA,CAAAlI,IAAA,CAAAqI,KAAA,MACF;IAmBElL,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA2F,WAAA,SAAAoF,MAAA,CAAAlI,IAAA,CAAA+C,IAAA,OACF;IAmBE5F,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA6E,WAAA,SAAAkG,MAAA,CAAAlI,IAAA,CAAAgD,SAAA,qBACF;IAII7F,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAA4B,SAAA,CAAoB;IAoBpBzE,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAA6B,QAAA,CAAmB;IAoBnB1E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAA+B,WAAA,CAAsB;IAoBtB5E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAiC,WAAA,CAAsB;IAoBtB9E,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAC,UAAA,CAAqB;IAoBrB9C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAG,QAAA,CAAmB;IAoBnBhD,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAK,GAAA,CAAc;IAoBdlD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAkC,OAAA,CAAkB;IAoBlB/E,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAlI,IAAA,CAAAsC,MAAA,IAAA4F,MAAA,CAAAlI,IAAA,CAAAsC,MAAA,CAAAhC,MAAA,KAA2C;IAqJ/CnD,EAAA,CAAAK,SAAA,IAEC;IAFDL,EAAA,CAAA2B,UAAA,eAAAoJ,MAAA,CAAAlI,IAAA,CAAA+C,IAAA,4CAEC;IAyCL5F,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA2B,UAAA,SAAAoJ,MAAA,CAAAK,UAAA,CAAgB;;;ADh1B1B,OAAM,MAAOC,gBAAgB;EAe3BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAJf,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAlBZ,KAAAX,aAAa,GAAgB,IAAI;IACjC,KAAA/B,UAAU,GAAkB,IAAI;IAChC,KAAAtI,OAAO,GAAG,EAAE;IACZ,KAAAH,KAAK,GAAG,EAAE;IACV,KAAA2B,aAAa,GAAG,KAAK;IACrB,KAAAQ,aAAa,GAAG,KAAK;IAErB;IACA,KAAAyI,UAAU,GAAG,KAAK;IAElB,KAAAhC,WAAW,GAAG,KAAK;IACnB,KAAA3H,kBAAkB,GAAG,CAAC;IASpB,IAAI,CAACsH,QAAQ,GAAG,IAAI,CAAC4C,EAAE,CAACC,KAAK,CAAC;MAC5BnH,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC3E,UAAU,CAAC+L,QAAQ,EAAE/L,UAAU,CAACgM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DpH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5E,UAAU,CAAC+L,QAAQ,EAAE/L,UAAU,CAACgM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DnH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC+L,QAAQ,EAAE/L,UAAU,CAACgM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DZ,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpL,UAAU,CAAC+L,QAAQ,EAAE/L,UAAU,CAACoL,KAAK,CAAC,CAAC;MACpDtG,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAChF,UAAU,CAACiM,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC1DjJ,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdE,GAAG,EAAE,CAAC,EAAE,EAAE,CAACpD,UAAU,CAACgM,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACrC/G,OAAO,EAAE,CAAC,EAAE,CAAC;MACbI,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEA6G,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,WAAW,CAACQ,UAAU,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACvJ,IAAI,GAAGuJ,GAAG;QAEf;QACA,IAAI,CAAC,IAAI,CAACvJ,IAAI,CAACoI,YAAY,IAAI,IAAI,CAACpI,IAAI,CAACwJ,KAAK,EAAE;UAC9C,IAAI,CAACxJ,IAAI,CAACoI,YAAY,GAAG,IAAI,CAACpI,IAAI,CAACwJ,KAAK;SACzC,MAAM,IAAI,CAAC,IAAI,CAACxJ,IAAI,CAACwJ,KAAK,IAAI,IAAI,CAACxJ,IAAI,CAACoI,YAAY,EAAE;UACrD,IAAI,CAACpI,IAAI,CAACwJ,KAAK,GAAG,IAAI,CAACxJ,IAAI,CAACoI,YAAY;;QAG1C;QACA,IACE,CAAC,IAAI,CAACpI,IAAI,CAACoI,YAAY,IACvB,IAAI,CAACpI,IAAI,CAACoI,YAAY,KAAK,MAAM,IACjC,IAAI,CAACpI,IAAI,CAACoI,YAAY,CAACqB,IAAI,EAAE,KAAK,EAAE,EACpC;UACA,IAAI,CAACzJ,IAAI,CAACoI,YAAY,GAAG,mCAAmC;UAC5D,IAAI,CAACpI,IAAI,CAACwJ,KAAK,GAAG,mCAAmC;;QAGvD;QACA,IAAI,CAAC,IAAI,CAACxJ,IAAI,CAAC0J,eAAe,EAAE;UAC9B,IAAI,CAAC1J,IAAI,CAAC0J,eAAe,GAAG,IAAI,CAAC1J,IAAI,CAACoI,YAAY,IAAI,IAAI,CAACpI,IAAI,CAACwJ,KAAK;;QAGvE;QACA,IAAI,CAACG,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDjM,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,yBAAyB;MACxC;KACD,CAAC;EACJ;EAEAgM,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC3J,IAAI,EAAE;IAEhB,MAAM6J,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACxH,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACnK,IAAI,CAACkK,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,IAAIU,KAAK,KAAK,qBAAqB,EAAE;QAC9EJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACnK,IAAI,CAACkK,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACX,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3CO,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAACrK,IAAI,CAACoI,YAAY,IACtB,IAAI,CAACpI,IAAI,CAACoI,YAAY,KAAK,qBAAqB,IAChD,IAAI,CAACpI,IAAI,CAACoI,YAAY,KAAK,mCAAmC,IAC9D,IAAI,CAACpI,IAAI,CAACoI,YAAY,CAACqB,IAAI,EAAE,KAAK,EAAE,EAAE;MACxCY,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAMC,kBAAkB,GAAIP,iBAAiB,GAAGF,cAAc,CAACvJ,MAAM,GAAI,EAAE;IAC3E,MAAMiK,kBAAkB,GAAIP,iBAAiB,GAAGF,cAAc,CAACxJ,MAAM,GAAI,EAAE;IAC3E,MAAMkK,eAAe,GAAGH,eAAe,GAAG,EAAE;IAE5C,IAAI,CAACzL,kBAAkB,GAAG6L,IAAI,CAACC,KAAK,CAACJ,kBAAkB,GAAGC,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEAZ,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC5J,IAAI,EAAE;IAEhB,IAAI,CAACkG,QAAQ,CAACyE,UAAU,CAAC;MACvB/I,SAAS,EAAE,IAAI,CAAC5B,IAAI,CAAC4B,SAAS,IAAI,EAAE;MACpCC,QAAQ,EAAE,IAAI,CAAC7B,IAAI,CAAC6B,QAAQ,IAAI,EAAE;MAClCC,QAAQ,EAAE,IAAI,CAAC9B,IAAI,CAAC8B,QAAQ,IAAI,EAAE;MAClCuG,KAAK,EAAE,IAAI,CAACrI,IAAI,CAACqI,KAAK,IAAI,EAAE;MAC5BtG,WAAW,EAAE,IAAI,CAAC/B,IAAI,CAAC+B,WAAW,IAAI,EAAE;MACxCE,WAAW,EAAE,IAAI,CAACjC,IAAI,CAACiC,WAAW,IAAI,EAAE;MACxChC,UAAU,EAAE,IAAI,CAACD,IAAI,CAACC,UAAU,IAAI,EAAE;MACtCE,QAAQ,EAAE,IAAI,CAACH,IAAI,CAACG,QAAQ,IAAI,EAAE;MAClCE,GAAG,EAAE,IAAI,CAACL,IAAI,CAACK,GAAG,IAAI,EAAE;MACxB6B,OAAO,EAAE,IAAI,CAAClC,IAAI,CAACkC,OAAO,IAAI,EAAE;MAChCI,MAAM,EAAEsI,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC7K,IAAI,CAACsC,MAAM,CAAC,GAAG,IAAI,CAACtC,IAAI,CAACsC,MAAM,CAACwI,IAAI,CAAC,IAAI,CAAC,GAAI,IAAI,CAAC9K,IAAI,CAACsC,MAAM,IAAI;KAC9F,CAAC;EACJ;EAEA;;;;EAIA+D,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrG,IAAI,EAAE,OAAO,mCAAmC;IAE1D;IACA,IACE,IAAI,CAACA,IAAI,CAACoI,YAAY,IACtB,IAAI,CAACpI,IAAI,CAACoI,YAAY,KAAK,MAAM,IACjC,IAAI,CAACpI,IAAI,CAACoI,YAAY,CAACqB,IAAI,EAAE,KAAK,EAAE,EACpC;MACA,OAAO,IAAI,CAACzJ,IAAI,CAACoI,YAAY;;IAG/B;IACA,IACE,IAAI,CAACpI,IAAI,CAACwJ,KAAK,IACf,IAAI,CAACxJ,IAAI,CAACwJ,KAAK,KAAK,MAAM,IAC1B,IAAI,CAACxJ,IAAI,CAACwJ,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAC7B;MACA,OAAO,IAAI,CAACzJ,IAAI,CAACwJ,KAAK;;IAGxB;IACA,IACE,IAAI,CAACxJ,IAAI,CAAC0J,eAAe,IACzB,IAAI,CAAC1J,IAAI,CAAC0J,eAAe,KAAK,MAAM,IACpC,IAAI,CAAC1J,IAAI,CAAC0J,eAAe,CAACD,IAAI,EAAE,KAAK,EAAE,EACvC;MACA,OAAO,IAAI,CAACzJ,IAAI,CAAC0J,eAAe;;IAGlC;IACA,OAAO,mCAAmC;EAC5C;EAEAhE,cAAcA,CAACqF,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE5K,MAAM,EAAE;MACvB,MAAM6K,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACnC,IAAI,CAAC3N,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAAC4N,cAAc,EAAE;QACrB;;MAGF,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAAC7N,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAAC4N,cAAc,EAAE;QACrB;;MAGF,IAAI,CAACpD,aAAa,GAAGgD,IAAI;MACzB,IAAI,CAACxN,KAAK,GAAG,EAAE;MAEf,MAAM8N,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACxF,UAAU,GAAIwF,CAAC,CAACX,MAAM,EAAEY,MAAiB,IAAI,IAAI;MACxD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;EAE9B;EAEAjM,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACiJ,aAAa,EAAE;IAEzB,IAAI,CAAC7I,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACxB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEfoO,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC1M,aAAa,CAAC;IAEjE,IAAI,CAACsJ,WAAW,CACbqD,kBAAkB,CAAC,IAAI,CAAC9D,aAAa,CAAC,CACtC+D,IAAI,CACHhP,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACoC,aAAa,GAAG,KAAK;MAC1ByM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC1M,aAAa,CAAC;IACpE,CAAC,CAAC,CACH,CACA+J,SAAS,CAAC;MACTC,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAI,CAACrO,OAAO,GAAGqO,QAAQ,CAACrO,OAAO,IAAI,8BAA8B;QAEjE;QACA,IAAI,CAACkC,IAAI,CAAC0J,eAAe,GAAGyC,QAAQ,CAACC,QAAQ;QAC7C,IAAI,CAACpM,IAAI,CAACoI,YAAY,GAAG+D,QAAQ,CAACC,QAAQ;QAC1C,IAAI,CAACpM,IAAI,CAACwJ,KAAK,GAAG2C,QAAQ,CAACC,QAAQ;QAEnC;QACA,IAAI,CAACxD,WAAW,CAACyD,iBAAiB,CAAC;UACjCjE,YAAY,EAAE+D,QAAQ,CAACC,QAAQ;UAC/B5C,KAAK,EAAE2C,QAAQ,CAACC;SACjB,CAAC;QAEF;QACA,IAAI,CAACzD,eAAe,CAAC2D,cAAc,CAAC;UAClC,GAAG,IAAI,CAACtM,IAAI;UACZoI,YAAY,EAAE+D,QAAQ,CAACC,QAAQ;UAC/B5C,KAAK,EAAE2C,QAAQ,CAACC;SACjB,CAAC;QAEF,IAAI,CAACjE,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC/B,UAAU,GAAG,IAAI;QACtB,IAAI,CAACmF,cAAc,EAAE;QAErB,IAAIY,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5O,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGgP,GAAmC,IAAI;QAC7C,IAAI,CAAChP,KAAK,GAAGgP,GAAG,CAAChP,KAAK,EAAEG,OAAO,IAAI,eAAe;QAClD;QACA4O,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/O,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEA+B,kBAAkBA,CAAA;IAChB,IAAI,CAACkN,OAAO,CAAC,uDAAuD,CAAC,EACnE;IAEF,IAAI,CAAC9M,aAAa,GAAG,IAAI;IACzB,IAAI,CAAChC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf,IAAI,CAACiL,WAAW,CACblJ,kBAAkB,EAAE,CACpBwM,IAAI,CAAChP,QAAQ,CAAC,MAAO,IAAI,CAAC4C,aAAa,GAAG,KAAM,CAAC,CAAC,CAClDuJ,SAAS,CAAC;MACTC,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAI,CAACrO,OAAO,GACVqO,QAAQ,CAACrO,OAAO,IAAI,sCAAsC;QAE5D;QACA,IAAI,CAACkC,IAAI,CAAC0J,eAAe,GAAG,IAAI;QAChC,IAAI,CAAC1J,IAAI,CAACoI,YAAY,GAAG,IAAI;QAC7B,IAAI,CAACpI,IAAI,CAACwJ,KAAK,GAAG,IAAI;QAEtB;QACA,IAAI,CAACZ,WAAW,CAACyD,iBAAiB,CAAC;UACjCjE,YAAY,EAAE,mCAAmC;UACjDoB,KAAK,EAAE;SACR,CAAC;QAEF;QACA,IAAI,CAACb,eAAe,CAAC2D,cAAc,CAAC;UAClC,GAAG,IAAI,CAACtM,IAAI;UACZoI,YAAY,EAAE,mCAAmC;UACjDoB,KAAK,EAAE;SACR,CAAC;QAEF,IAAI2C,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5O,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGgP,GAAmC,IAAI;QAC7C,IAAI,CAAChP,KAAK,GAAGgP,GAAG,CAAChP,KAAK,EAAEG,OAAO,IAAI,gBAAgB;QACnD;QACA4O,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/O,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEQ4N,cAAcA,CAAA;IACpB,IAAI,CAACpD,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC/B,UAAU,GAAG,IAAI;IACtB,MAAMyG,SAAS,GAAGC,QAAQ,CAACC,cAAc,CACvC,gBAAgB,CACG;IACrB,IAAIF,SAAS,EAAEA,SAAS,CAAC1C,KAAK,GAAG,EAAE;EACrC;EAEA6C,UAAUA,CAACC,IAAY;IACrB,IAAI,CAACpE,MAAM,CAACqE,QAAQ,CAAC,CAACD,IAAI,CAAC,CAAC;EAC9B;EAEA;EACA3O,cAAcA,CAAA;IACZ,IAAI,CAACiK,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnB,IAAI,CAACqB,gBAAgB,EAAE;;IAEzB,IAAI,CAAC9L,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEAqH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACkB,QAAQ,CAACiH,OAAO,EAAE;MACzB,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC7G,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC5I,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,OAAO,GAAG,EAAE;IAEjB,MAAMuP,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtH,QAAQ,CAACiE,KAAK,CAAC,CAACF,OAAO,CAACwD,GAAG,IAAG;MAC7C,MAAMtD,KAAK,GAAG,IAAI,CAACjE,QAAQ,CAACiE,KAAK,CAACsD,GAAG,CAAC;MACtC,IAAIA,GAAG,KAAK,QAAQ,IAAItD,KAAK,EAAE;QAC7B;QACA,MAAMuD,WAAW,GAAGvD,KAAK,CAACwD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAACpE,IAAI,EAAE,CAAC,CAACqE,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAIvD,KAAK,EAAE;QAChBkD,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEtD,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAAChC,aAAa,EAAE;MACtBkF,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC5F,aAAa,CAAC;;IAG9C,IAAI,CAACS,WAAW,CAACsF,eAAe,CAACb,QAAQ,CAAC,CAAChE,SAAS,CAAC;MACnDC,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAI,CAAC5F,WAAW,GAAG,KAAK;QACxB,IAAI,CAACzI,OAAO,GAAG,+BAA+B;QAE9C;QACA,IAAI,CAACkC,IAAI,GAAG;UAAE,GAAG,IAAI,CAACA,IAAI;UAAE,GAAGmM,QAAQ,CAACnM;QAAI,CAAE;QAC9C,IAAI,CAAC2I,eAAe,CAAC2D,cAAc,CAAC,IAAI,CAACtM,IAAI,CAAC;QAE9C;QACA,IAAI,CAAC2J,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACpB,UAAU,GAAG,KAAK;QAEvB;QACA,IAAI,CAACJ,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC/B,UAAU,GAAG,IAAI;QACtB,IAAI,CAACmF,cAAc,EAAE;QAErB;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5O,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGgP,GAAG,IAAI;QACb,IAAI,CAACpG,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC5I,KAAK,GAAGgP,GAAG,CAAChP,KAAK,EAAEG,OAAO,IAAI,gDAAgD;QAEnF;QACA4O,UAAU,CAAC,MAAK;UACd,IAAI,CAAC/O,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEAiH,UAAUA,CAAA;IACR,IAAI,CAAC2D,UAAU,GAAG,KAAK;IACvB,IAAI,CAACqB,gBAAgB,EAAE,CAAC,CAAC;IACzB,IAAI,CAACzB,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC/B,UAAU,GAAG,IAAI;IACtB,IAAI,CAACmF,cAAc,EAAE;IACrB,IAAI,CAACzN,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEQyP,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtH,QAAQ,CAACiI,QAAQ,CAAC,CAAClE,OAAO,CAACwD,GAAG,IAAG;MAChD,IAAI,CAACvH,QAAQ,CAACkI,GAAG,CAACX,GAAG,CAAC,EAAEY,aAAa,EAAE;IACzC,CAAC,CAAC;EACJ;EAEA;EACArK,aAAaA,CAACsK,SAAiB;IAC7B,MAAMpE,KAAK,GAAG,IAAI,CAAChE,QAAQ,CAACkI,GAAG,CAACE,SAAS,CAAC;IAC1C,IAAIpE,KAAK,EAAEqE,MAAM,IAAIrE,KAAK,CAACsE,OAAO,EAAE;MAClC,IAAItE,KAAK,CAACqE,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAIpE,KAAK,CAACqE,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAIpE,KAAK,CAACqE,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,sBAAsB;MACxD,IAAIrE,KAAK,CAACqE,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEAnI,cAAcA,CAACmI,SAAiB;IAC9B,MAAMpE,KAAK,GAAG,IAAI,CAAChE,QAAQ,CAACkI,GAAG,CAACE,SAAS,CAAC;IAC1C,OAAO,CAAC,EAAEpE,KAAK,EAAEiD,OAAO,IAAIjD,KAAK,CAACsE,OAAO,CAAC;EAC5C;EAEA7P,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACC,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACD,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,wDAAwD;KAChE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,8CAA8C;KACtD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,6CAA6C;KACrD,MAAM;MACL,OAAO,sCAAsC;;EAEjD;EAEAoJ,MAAMA,CAAA;IACJ,IAAI,CAACW,eAAe,CAACX,MAAM,EAAE,CAACqB,SAAS,CAAC;MACtCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACX,eAAe,CAAC8F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7D,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC/BwB,WAAW,EAAE;cAAE5Q,OAAO,EAAE;YAAqB,CAAE;YAC/C6Q,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDhR,KAAK,EAAGgP,GAAQ,IAAI;QAClBZ,OAAO,CAACpO,KAAK,CAAC,eAAe,EAAEgP,GAAG,CAAC;QACnC,IAAI,CAAChE,eAAe,CAAC8F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7D,MAAM,CAACqE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACtC,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;;;uBAleW1E,gBAAgB,EAAArL,EAAA,CAAAyR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3R,EAAA,CAAAyR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7R,EAAA,CAAAyR,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/R,EAAA,CAAAyR,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAjS,EAAA,CAAAyR,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB9G,gBAAgB;MAAA+G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7B1S,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAuD;UAMjDD,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,+EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAoB,UAAA,KAAAwR,gCAAA,kBAUM;UAGN5S,EAAA,CAAAoB,UAAA,KAAAyR,gCAAA,mBAmBM;UAGN7S,EAAA,CAAAoB,UAAA,KAAA0R,gCAAA,mBAqBM;UAGN9S,EAAA,CAAAoB,UAAA,KAAA2R,gCAAA,mBAoCM;UAGN/S,EAAA,CAAAoB,UAAA,KAAA4R,gCAAA,qBA67BI;UACRhT,EAAA,CAAAG,YAAA,EAAM;;;UAhiCIH,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAA2B,UAAA,UAAAgR,GAAA,CAAA9P,IAAA,CAAW;UAcd7C,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA2B,UAAA,SAAAgR,GAAA,CAAAnS,KAAA,CAAW;UAsBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2B,UAAA,SAAAgR,GAAA,CAAAhS,OAAA,CAAa;UAuBVX,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAAgR,GAAA,CAAA9P,IAAA,CAAU;UAuCV7C,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAAgR,GAAA,CAAA9P,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}