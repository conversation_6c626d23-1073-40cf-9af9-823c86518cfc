{"ast": null, "code": "import { Observable } from \"../../utilities/index.js\";\nexport function fromPromise(promise) {\n  return new Observable(function (observer) {\n    promise.then(function (value) {\n      observer.next(value);\n      observer.complete();\n    }).catch(observer.error.bind(observer));\n  });\n}", "map": {"version": 3, "names": ["Observable", "fromPromise", "promise", "observer", "then", "value", "next", "complete", "catch", "error", "bind"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/utils/fromPromise.js"], "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\nexport function fromPromise(promise) {\n    return new Observable(function (observer) {\n        promise\n            .then(function (value) {\n            observer.next(value);\n            observer.complete();\n        })\n            .catch(observer.error.bind(observer));\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,0BAA0B;AACrD,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAE;EACjC,OAAO,IAAIF,UAAU,CAAC,UAAUG,QAAQ,EAAE;IACtCD,OAAO,CACFE,IAAI,CAAC,UAAUC,KAAK,EAAE;MACvBF,QAAQ,CAACG,IAAI,CAACD,KAAK,CAAC;MACpBF,QAAQ,CAACI,QAAQ,CAAC,CAAC;IACvB,CAAC,CAAC,CACGC,KAAK,CAACL,QAAQ,CAACM,KAAK,CAACC,IAAI,CAACP,QAAQ,CAAC,CAAC;EAC7C,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}