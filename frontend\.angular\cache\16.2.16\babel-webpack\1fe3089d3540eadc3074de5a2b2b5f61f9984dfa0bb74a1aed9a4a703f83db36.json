{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nexport const guarduserGuard = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n  if (authService.userLoggedIn() == true) {\n    return true;\n  } else {\n    router.navigate(['/loginuser'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    localStorage.removeItem('token');\n    return false;\n  }\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthuserService", "<PERSON><PERSON><PERSON><PERSON>", "route", "state", "authService", "router", "userLoggedIn", "navigate", "queryParams", "returnUrl", "url", "localStorage", "removeItem"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\guards\\guarduser.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\n\r\nexport const guarduserGuard: CanActivateFn = (route, state) => {\r\n  const authService=inject(AuthuserService)\r\n   const router= inject(Router)\r\n   if(authService.userLoggedIn()==true){\r\n   return true;\r\n }else \r\n {\r\n   router.navigate(['/loginuser'],{queryParams:{returnUrl:state.url}});\r\n   localStorage.removeItem('token')\r\n   return false;\r\n }\r\n\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,eAAe,QAAQ,mCAAmC;AAEnE,OAAO,MAAMC,cAAc,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EAC5D,MAAMC,WAAW,GAACN,MAAM,CAACE,eAAe,CAAC;EACxC,MAAMK,MAAM,GAAEP,MAAM,CAACC,MAAM,CAAC;EAC5B,IAAGK,WAAW,CAACE,YAAY,EAAE,IAAE,IAAI,EAAC;IACpC,OAAO,IAAI;GACZ,MACD;IACED,MAAM,CAACE,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAC;MAACC,WAAW,EAAC;QAACC,SAAS,EAACN,KAAK,CAACO;MAAG;IAAC,CAAC,CAAC;IACnEC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO,KAAK;;AAGf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}