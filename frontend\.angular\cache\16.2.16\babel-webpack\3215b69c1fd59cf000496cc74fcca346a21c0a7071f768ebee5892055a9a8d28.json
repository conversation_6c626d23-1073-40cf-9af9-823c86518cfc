{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class GeminiChatService {\n  constructor(http) {\n    this.http = http;\n    this.GEMINI_API_KEY = 'AIzaSyBL2R5ESS0q2DtZMbW6f-aMnk_y3bd4re8';\n    this.GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';\n    this.messagesSubject = new BehaviorSubject([]);\n    this.messages$ = this.messagesSubject.asObservable();\n    this.isOpenSubject = new BehaviorSubject(false);\n    this.isOpen$ = this.isOpenSubject.asObservable();\n    this.initializeChat();\n  }\n  initializeChat() {\n    const welcomeMessage = {\n      id: this.generateId(),\n      content: \"👋 Bonjour ! Je suis votre assistant IA. Je peux vous aider avec vos projets, répondre à vos questions sur la plateforme, ou vous donner des conseils académiques. Comment puis-je vous assister aujourd'hui ?\",\n      isUser: false,\n      timestamp: new Date()\n    };\n    this.messagesSubject.next([welcomeMessage]);\n  }\n  toggleChat() {\n    this.isOpenSubject.next(!this.isOpenSubject.value);\n  }\n  closeChat() {\n    this.isOpenSubject.next(false);\n  }\n  openChat() {\n    this.isOpenSubject.next(true);\n  }\n  sendMessage(content) {\n    const userMessage = {\n      id: this.generateId(),\n      content: content.trim(),\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Ajouter le message utilisateur\n    const currentMessages = this.messagesSubject.value;\n    this.messagesSubject.next([...currentMessages, userMessage]);\n    // Ajouter un indicateur de frappe\n    const typingMessage = {\n      id: 'typing',\n      content: '',\n      isUser: false,\n      timestamp: new Date(),\n      isTyping: true\n    };\n    this.messagesSubject.next([...this.messagesSubject.value, typingMessage]);\n    // Préparer le contexte pour Gemini\n    const contextualPrompt = this.buildContextualPrompt(content);\n    return this.callGeminiAPI(contextualPrompt).pipe(map(response => {\n      // Supprimer l'indicateur de frappe\n      const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n      const aiMessage = {\n        id: this.generateId(),\n        content: response,\n        isUser: false,\n        timestamp: new Date()\n      };\n      // Ajouter la réponse IA\n      this.messagesSubject.next([...messagesWithoutTyping, aiMessage]);\n      return aiMessage;\n    }), catchError(error => {\n      // Supprimer l'indicateur de frappe en cas d'erreur\n      const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\n      const errorMessage = {\n        id: this.generateId(),\n        content: \"Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.\",\n        isUser: false,\n        timestamp: new Date()\n      };\n      this.messagesSubject.next([...messagesWithoutTyping, errorMessage]);\n      return of(errorMessage);\n    }));\n  }\n  buildContextualPrompt(userMessage) {\n    return `Tu es un assistant IA pour une plateforme de gestion de projets étudiants. Tu aides les professeurs, étudiants et administrateurs.\n\nContexte de la plateforme :\n- Les professeurs peuvent créer des projets, voir les rendus des étudiants, et gérer les évaluations\n- Les étudiants peuvent voir leurs projets assignés, soumettre leurs travaux, et suivre leurs notes\n- Les administrateurs gèrent les utilisateurs et supervisent la plateforme\n\nTon rôle :\n- Répondre aux questions sur l'utilisation de la plateforme\n- Donner des conseils académiques et techniques\n- Aider à résoudre les problèmes courants\n- Être bienveillant, professionnel et pédagogique\n\nRéponds en français, de manière claire et concise. Utilise des emojis appropriés pour rendre tes réponses plus engageantes.\n\nQuestion de l'utilisateur : ${userMessage}`;\n  }\n  callGeminiAPI(prompt) {\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    const body = {\n      contents: [{\n        parts: [{\n          text: prompt\n        }]\n      }],\n      generationConfig: {\n        temperature: 0.7,\n        topK: 40,\n        topP: 0.95,\n        maxOutputTokens: 1024\n      }\n    };\n    return this.http.post(`${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`, body, {\n      headers\n    }).pipe(map(response => {\n      if (response.candidates && response.candidates.length > 0) {\n        return response.candidates[0].content.parts[0].text;\n      }\n      throw new Error('Réponse invalide de l\\'API');\n    }));\n  }\n  clearChat() {\n    this.initializeChat();\n  }\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n  getCurrentMessages() {\n    return this.messagesSubject.value;\n  }\n  static {\n    this.ɵfac = function GeminiChatService_Factory(t) {\n      return new (t || GeminiChatService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GeminiChatService,\n      factory: GeminiChatService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "map", "catchError", "of", "GeminiChatService", "constructor", "http", "GEMINI_API_KEY", "GEMINI_API_URL", "messagesSubject", "messages$", "asObservable", "isOpenSubject", "isOpen$", "initializeChat", "welcomeMessage", "id", "generateId", "content", "isUser", "timestamp", "Date", "next", "toggleChat", "value", "closeChat", "openChat", "sendMessage", "userMessage", "trim", "currentMessages", "typingMessage", "isTyping", "contextualPrompt", "buildContextualPrompt", "callGeminiAPI", "pipe", "response", "messagesWithoutTyping", "filter", "msg", "aiMessage", "error", "errorMessage", "prompt", "headers", "body", "contents", "parts", "text", "generationConfig", "temperature", "topK", "topP", "maxOutputTokens", "post", "candidates", "length", "Error", "clearChat", "now", "toString", "Math", "random", "substr", "getCurrentMessages", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\gemini-chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\n\r\nexport interface ChatMessage {\r\n  id: string;\r\n  content: string;\r\n  isUser: boolean;\r\n  timestamp: Date;\r\n  isTyping?: boolean;\r\n}\r\n\r\nexport interface GeminiResponse {\r\n  candidates: Array<{\r\n    content: {\r\n      parts: Array<{\r\n        text: string;\r\n      }>;\r\n    };\r\n  }>;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class GeminiChatService {\r\n  private readonly GEMINI_API_KEY = 'AIzaSyBL2R5ESS0q2DtZMbW6f-aMnk_y3bd4re8';\r\n  private readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';\r\n\r\n  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);\r\n  public messages$ = this.messagesSubject.asObservable();\r\n\r\n  private isOpenSubject = new BehaviorSubject<boolean>(false);\r\n  public isOpen$ = this.isOpenSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {\r\n    this.initializeChat();\r\n  }\r\n\r\n  private initializeChat(): void {\r\n    const welcomeMessage: ChatMessage = {\r\n      id: this.generateId(),\r\n      content: \"👋 Bonjour ! Je suis votre assistant IA. Je peux vous aider avec vos projets, répondre à vos questions sur la plateforme, ou vous donner des conseils académiques. Comment puis-je vous assister aujourd'hui ?\",\r\n      isUser: false,\r\n      timestamp: new Date()\r\n    };\r\n    this.messagesSubject.next([welcomeMessage]);\r\n  }\r\n\r\n  toggleChat(): void {\r\n    this.isOpenSubject.next(!this.isOpenSubject.value);\r\n  }\r\n\r\n  closeChat(): void {\r\n    this.isOpenSubject.next(false);\r\n  }\r\n\r\n  openChat(): void {\r\n    this.isOpenSubject.next(true);\r\n  }\r\n\r\n  sendMessage(content: string): Observable<ChatMessage> {\r\n    const userMessage: ChatMessage = {\r\n      id: this.generateId(),\r\n      content: content.trim(),\r\n      isUser: true,\r\n      timestamp: new Date()\r\n    };\r\n\r\n    // Ajouter le message utilisateur\r\n    const currentMessages = this.messagesSubject.value;\r\n    this.messagesSubject.next([...currentMessages, userMessage]);\r\n\r\n    // Ajouter un indicateur de frappe\r\n    const typingMessage: ChatMessage = {\r\n      id: 'typing',\r\n      content: '',\r\n      isUser: false,\r\n      timestamp: new Date(),\r\n      isTyping: true\r\n    };\r\n    this.messagesSubject.next([...this.messagesSubject.value, typingMessage]);\r\n\r\n    // Préparer le contexte pour Gemini\r\n    const contextualPrompt = this.buildContextualPrompt(content);\r\n\r\n    return this.callGeminiAPI(contextualPrompt).pipe(\r\n      map(response => {\r\n        // Supprimer l'indicateur de frappe\r\n        const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\r\n        \r\n        const aiMessage: ChatMessage = {\r\n          id: this.generateId(),\r\n          content: response,\r\n          isUser: false,\r\n          timestamp: new Date()\r\n        };\r\n\r\n        // Ajouter la réponse IA\r\n        this.messagesSubject.next([...messagesWithoutTyping, aiMessage]);\r\n        return aiMessage;\r\n      }),\r\n      catchError(error => {\r\n        // Supprimer l'indicateur de frappe en cas d'erreur\r\n        const messagesWithoutTyping = this.messagesSubject.value.filter(msg => msg.id !== 'typing');\r\n        \r\n        const errorMessage: ChatMessage = {\r\n          id: this.generateId(),\r\n          content: \"Désolé, je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.\",\r\n          isUser: false,\r\n          timestamp: new Date()\r\n        };\r\n\r\n        this.messagesSubject.next([...messagesWithoutTyping, errorMessage]);\r\n        return of(errorMessage);\r\n      })\r\n    );\r\n  }\r\n\r\n  private buildContextualPrompt(userMessage: string): string {\r\n    return `Tu es un assistant IA pour une plateforme de gestion de projets étudiants. Tu aides les professeurs, étudiants et administrateurs.\r\n\r\nContexte de la plateforme :\r\n- Les professeurs peuvent créer des projets, voir les rendus des étudiants, et gérer les évaluations\r\n- Les étudiants peuvent voir leurs projets assignés, soumettre leurs travaux, et suivre leurs notes\r\n- Les administrateurs gèrent les utilisateurs et supervisent la plateforme\r\n\r\nTon rôle :\r\n- Répondre aux questions sur l'utilisation de la plateforme\r\n- Donner des conseils académiques et techniques\r\n- Aider à résoudre les problèmes courants\r\n- Être bienveillant, professionnel et pédagogique\r\n\r\nRéponds en français, de manière claire et concise. Utilise des emojis appropriés pour rendre tes réponses plus engageantes.\r\n\r\nQuestion de l'utilisateur : ${userMessage}`;\r\n  }\r\n\r\n  private callGeminiAPI(prompt: string): Observable<string> {\r\n    const headers = new HttpHeaders({\r\n      'Content-Type': 'application/json'\r\n    });\r\n\r\n    const body = {\r\n      contents: [\r\n        {\r\n          parts: [\r\n            {\r\n              text: prompt\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      generationConfig: {\r\n        temperature: 0.7,\r\n        topK: 40,\r\n        topP: 0.95,\r\n        maxOutputTokens: 1024,\r\n      }\r\n    };\r\n\r\n    return this.http.post<GeminiResponse>(\r\n      `${this.GEMINI_API_URL}?key=${this.GEMINI_API_KEY}`,\r\n      body,\r\n      { headers }\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.candidates && response.candidates.length > 0) {\r\n          return response.candidates[0].content.parts[0].text;\r\n        }\r\n        throw new Error('Réponse invalide de l\\'API');\r\n      })\r\n    );\r\n  }\r\n\r\n  clearChat(): void {\r\n    this.initializeChat();\r\n  }\r\n\r\n  private generateId(): string {\r\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\r\n  }\r\n\r\n  getCurrentMessages(): ChatMessage[] {\r\n    return this.messagesSubject.value;\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,eAAe,QAAQ,MAAM;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,EAAE,QAAQ,MAAM;;;AAuBzB,OAAM,MAAOC,iBAAiB;EAU5BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IATP,KAAAC,cAAc,GAAG,yCAAyC;IAC1D,KAAAC,cAAc,GAAG,0FAA0F;IAEpH,KAAAC,eAAe,GAAG,IAAIT,eAAe,CAAgB,EAAE,CAAC;IACzD,KAAAU,SAAS,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;IAE9C,KAAAC,aAAa,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IACpD,KAAAa,OAAO,GAAG,IAAI,CAACD,aAAa,CAACD,YAAY,EAAE;IAGhD,IAAI,CAACG,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,MAAMC,cAAc,GAAgB;MAClCC,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,OAAO,EAAE,gNAAgN;MACzNC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAACP,cAAc,CAAC,CAAC;EAC7C;EAEAQ,UAAUA,CAAA;IACR,IAAI,CAACX,aAAa,CAACU,IAAI,CAAC,CAAC,IAAI,CAACV,aAAa,CAACY,KAAK,CAAC;EACpD;EAEAC,SAASA,CAAA;IACP,IAAI,CAACb,aAAa,CAACU,IAAI,CAAC,KAAK,CAAC;EAChC;EAEAI,QAAQA,CAAA;IACN,IAAI,CAACd,aAAa,CAACU,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEAK,WAAWA,CAACT,OAAe;IACzB,MAAMU,WAAW,GAAgB;MAC/BZ,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;MACrBC,OAAO,EAAEA,OAAO,CAACW,IAAI,EAAE;MACvBV,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,MAAMS,eAAe,GAAG,IAAI,CAACrB,eAAe,CAACe,KAAK;IAClD,IAAI,CAACf,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGQ,eAAe,EAAEF,WAAW,CAAC,CAAC;IAE5D;IACA,MAAMG,aAAa,GAAgB;MACjCf,EAAE,EAAE,QAAQ;MACZE,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBW,QAAQ,EAAE;KACX;IACD,IAAI,CAACvB,eAAe,CAACa,IAAI,CAAC,CAAC,GAAG,IAAI,CAACb,eAAe,CAACe,KAAK,EAAEO,aAAa,CAAC,CAAC;IAEzE;IACA,MAAME,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,CAAChB,OAAO,CAAC;IAE5D,OAAO,IAAI,CAACiB,aAAa,CAACF,gBAAgB,CAAC,CAACG,IAAI,CAC9CnC,GAAG,CAACoC,QAAQ,IAAG;MACb;MACA,MAAMC,qBAAqB,GAAG,IAAI,CAAC7B,eAAe,CAACe,KAAK,CAACe,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,EAAE,KAAK,QAAQ,CAAC;MAE3F,MAAMyB,SAAS,GAAgB;QAC7BzB,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBC,OAAO,EAAEmB,QAAQ;QACjBlB,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED;MACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGgB,qBAAqB,EAAEG,SAAS,CAAC,CAAC;MAChE,OAAOA,SAAS;IAClB,CAAC,CAAC,EACFvC,UAAU,CAACwC,KAAK,IAAG;MACjB;MACA,MAAMJ,qBAAqB,GAAG,IAAI,CAAC7B,eAAe,CAACe,KAAK,CAACe,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACxB,EAAE,KAAK,QAAQ,CAAC;MAE3F,MAAM2B,YAAY,GAAgB;QAChC3B,EAAE,EAAE,IAAI,CAACC,UAAU,EAAE;QACrBC,OAAO,EAAE,6FAA6F;QACtGC,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,IAAIC,IAAI;OACpB;MAED,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGgB,qBAAqB,EAAEK,YAAY,CAAC,CAAC;MACnE,OAAOxC,EAAE,CAACwC,YAAY,CAAC;IACzB,CAAC,CAAC,CACH;EACH;EAEQT,qBAAqBA,CAACN,WAAmB;IAC/C,OAAO;;;;;;;;;;;;;;;8BAemBA,WAAW,EAAE;EACzC;EAEQO,aAAaA,CAACS,MAAc;IAClC,MAAMC,OAAO,GAAG,IAAI9C,WAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,MAAM+C,IAAI,GAAG;MACXC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,CACL;UACEC,IAAI,EAAEL;SACP;OAEJ,CACF;MACDM,gBAAgB,EAAE;QAChBC,WAAW,EAAE,GAAG;QAChBC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,IAAI;QACVC,eAAe,EAAE;;KAEpB;IAED,OAAO,IAAI,CAAChD,IAAI,CAACiD,IAAI,CACnB,GAAG,IAAI,CAAC/C,cAAc,QAAQ,IAAI,CAACD,cAAc,EAAE,EACnDuC,IAAI,EACJ;MAAED;IAAO,CAAE,CACZ,CAACT,IAAI,CACJnC,GAAG,CAACoC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACmB,UAAU,IAAInB,QAAQ,CAACmB,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QACzD,OAAOpB,QAAQ,CAACmB,UAAU,CAAC,CAAC,CAAC,CAACtC,OAAO,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI;;MAErD,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC,CAAC,CACH;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAAC7C,cAAc,EAAE;EACvB;EAEQG,UAAUA,CAAA;IAChB,OAAOI,IAAI,CAACuC,GAAG,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC;EACvE;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACxD,eAAe,CAACe,KAAK;EACnC;;;uBAhKWpB,iBAAiB,EAAA8D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAjBjE,iBAAiB;MAAAkE,OAAA,EAAjBlE,iBAAiB,CAAAmE,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}