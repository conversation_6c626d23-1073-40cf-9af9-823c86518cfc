{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ConnectionStatusComponent } from './connection-status.component';\nimport * as i0 from \"@angular/core\";\nexport class ConnectionStatusModule {\n  static {\n    this.ɵfac = function ConnectionStatusModule_Factory(t) {\n      return new (t || ConnectionStatusModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ConnectionStatusModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ConnectionStatusModule, {\n    declarations: [ConnectionStatusComponent],\n    imports: [CommonModule],\n    exports: [ConnectionStatusComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ConnectionStatusComponent", "ConnectionStatusModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\connection-status\\connection-status.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ConnectionStatusComponent } from './connection-status.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ConnectionStatusComponent\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    ConnectionStatusComponent\r\n  ]\r\n})\r\nexport class ConnectionStatusModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,yBAAyB,QAAQ,+BAA+B;;AAazE,OAAM,MAAOC,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAN/BF,YAAY;IAAA;EAAA;;;2EAMHE,sBAAsB;IAAAC,YAAA,GAT/BF,yBAAyB;IAAAG,OAAA,GAGzBJ,YAAY;IAAAK,OAAA,GAGZJ,yBAAyB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}