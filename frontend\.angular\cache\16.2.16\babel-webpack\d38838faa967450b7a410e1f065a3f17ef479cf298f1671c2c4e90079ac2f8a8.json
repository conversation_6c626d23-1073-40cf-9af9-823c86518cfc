{"ast": null, "code": "// A version of Array.isArray that works better with readonly arrays.\nexport var isArray = Array.isArray;\nexport function isNonEmptyArray(value) {\n  return Array.isArray(value) && value.length > 0;\n}", "map": {"version": 3, "names": ["isArray", "Array", "isNonEmptyArray", "value", "length"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/arrays.js"], "sourcesContent": ["// A version of Array.isArray that works better with readonly arrays.\nexport var isArray = Array.isArray;\nexport function isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\n"], "mappings": "AAAA;AACA,OAAO,IAAIA,OAAO,GAAGC,KAAK,CAACD,OAAO;AAClC,OAAO,SAASE,eAAeA,CAACC,KAAK,EAAE;EACnC,OAAOF,KAAK,CAACD,OAAO,CAACG,KAAK,CAAC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}