{"ast": null, "code": "import { maybe } from \"../globals/index.js\";\nvar isReactNative = maybe(function () {\n  return navigator.product;\n}) == \"ReactNative\";\nexport var canUseWeakMap = typeof WeakMap === \"function\" && !(isReactNative && !global.HermesInternal);\nexport var canUseWeakSet = typeof WeakSet === \"function\";\nexport var canUseSymbol = typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\nexport var canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\nexport var canUseDOM = typeof maybe(function () {\n  return window.document.createElement;\n}) === \"function\";\nvar usingJSDOM =\n// Following advice found in this comment from @domenic (maintainer of jsdom):\n// https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n//\n// Since we control the version of Jest and jsdom used when running Apollo\n// Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n// at the end of the user agent string, I believe this case is all we need to\n// check. Testing for \"Node.js\" was recommended for backwards compatibility\n// with older version of jsdom, but we don't have that problem.\nmaybe(function () {\n  return navigator.userAgent.indexOf(\"jsdom\") >= 0;\n}) || false;\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport var canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;", "map": {"version": 3, "names": ["maybe", "isReactNative", "navigator", "product", "canUseWeakMap", "WeakMap", "global", "HermesInternal", "canUseWeakSet", "WeakSet", "canUseSymbol", "Symbol", "for", "canUseAsyncIteratorSymbol", "asyncIterator", "canUseDOM", "window", "document", "createElement", "usingJSDOM", "userAgent", "indexOf", "canUseLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/canUse.js"], "sourcesContent": ["import { maybe } from \"../globals/index.js\";\nvar isReactNative = maybe(function () { return navigator.product; }) == \"ReactNative\";\nexport var canUseWeakMap = typeof WeakMap === \"function\" &&\n    !(isReactNative && !global.HermesInternal);\nexport var canUseWeakSet = typeof WeakSet === \"function\";\nexport var canUseSymbol = typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\nexport var canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\nexport var canUseDOM = typeof maybe(function () { return window.document.createElement; }) === \"function\";\nvar usingJSDOM = \n// Following advice found in this comment from @domenic (maintainer of jsdom):\n// https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n//\n// Since we control the version of Jest and jsdom used when running Apollo\n// Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n// at the end of the user agent string, I believe this case is all we need to\n// check. Testing for \"Node.js\" was recommended for backwards compatibility\n// with older version of jsdom, but we don't have that problem.\nmaybe(function () { return navigator.userAgent.indexOf(\"jsdom\") >= 0; }) || false;\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport var canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,IAAIC,aAAa,GAAGD,KAAK,CAAC,YAAY;EAAE,OAAOE,SAAS,CAACC,OAAO;AAAE,CAAC,CAAC,IAAI,aAAa;AACrF,OAAO,IAAIC,aAAa,GAAG,OAAOC,OAAO,KAAK,UAAU,IACpD,EAAEJ,aAAa,IAAI,CAACK,MAAM,CAACC,cAAc,CAAC;AAC9C,OAAO,IAAIC,aAAa,GAAG,OAAOC,OAAO,KAAK,UAAU;AACxD,OAAO,IAAIC,YAAY,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,GAAG,KAAK,UAAU;AAC1F,OAAO,IAAIC,yBAAyB,GAAGH,YAAY,IAAIC,MAAM,CAACG,aAAa;AAC3E,OAAO,IAAIC,SAAS,GAAG,OAAOf,KAAK,CAAC,YAAY;EAAE,OAAOgB,MAAM,CAACC,QAAQ,CAACC,aAAa;AAAE,CAAC,CAAC,KAAK,UAAU;AACzG,IAAIC,UAAU;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnB,KAAK,CAAC,YAAY;EAAE,OAAOE,SAAS,CAACkB,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAAE,CAAC,CAAC,IAAI,KAAK;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAG,CAACP,SAAS,IAAId,aAAa,KAAK,CAACkB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}