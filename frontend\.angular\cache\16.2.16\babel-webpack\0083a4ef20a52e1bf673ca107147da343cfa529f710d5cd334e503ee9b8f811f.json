{"ast": null, "code": "/**\n * @deprecated\n * This is not used internally any more and will be removed in\n * the next major version of Apollo Client.\n */\nexport var createSignalIfSupported = function () {\n  if (typeof AbortController === \"undefined\") return {\n    controller: false,\n    signal: false\n  };\n  var controller = new AbortController();\n  var signal = controller.signal;\n  return {\n    controller: controller,\n    signal: signal\n  };\n};", "map": {"version": 3, "names": ["createSignalIfSupported", "AbortController", "controller", "signal"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/createSignalIfSupported.js"], "sourcesContent": ["/**\n * @deprecated\n * This is not used internally any more and will be removed in\n * the next major version of Apollo Client.\n */\nexport var createSignalIfSupported = function () {\n    if (typeof AbortController === \"undefined\")\n        return { controller: false, signal: false };\n    var controller = new AbortController();\n    var signal = controller.signal;\n    return { controller: controller, signal: signal };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,uBAAuB,GAAG,SAAAA,CAAA,EAAY;EAC7C,IAAI,OAAOC,eAAe,KAAK,WAAW,EACtC,OAAO;IAAEC,UAAU,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAM,CAAC;EAC/C,IAAID,UAAU,GAAG,IAAID,eAAe,CAAC,CAAC;EACtC,IAAIE,MAAM,GAAGD,UAAU,CAACC,MAAM;EAC9B,OAAO;IAAED,UAAU,EAAEA,UAAU;IAAEC,MAAM,EAAEA;EAAO,CAAC;AACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}