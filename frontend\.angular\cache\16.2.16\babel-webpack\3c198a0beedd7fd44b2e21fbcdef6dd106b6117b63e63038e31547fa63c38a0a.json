{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nexport const profileCompletionGuard = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n  // Check if user is logged in\n  if (!authService.userLoggedIn()) {\n    router.navigate(['/login']);\n    return false;\n  }\n  const currentUser = authService.getCurrentUser();\n  // If user is not found, redirect to login\n  if (!currentUser) {\n    router.navigate(['/login']);\n    return false;\n  }\n  // If this is the profile completion page itself, allow access\n  if (state.url === '/complete-profile') {\n    return true;\n  }\n  // Only redirect for first-time logins or truly incomplete profiles\n  const isFirstLogin = currentUser.isFirstLogin;\n  // Check if profile has essential information (more lenient check)\n  const hasEssentialInfo = currentUser.firstName && currentUser.lastName && currentUser.email;\n  // Only redirect if it's first login OR missing essential information\n  if (isFirstLogin || !hasEssentialInfo) {\n    router.navigate(['/complete-profile']);\n    return false;\n  }\n  // Allow access to other pages if profile is complete\n  return true;\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthuserService", "profileCompletionGuard", "route", "state", "authService", "router", "userLoggedIn", "navigate", "currentUser", "getCurrentUser", "url", "is<PERSON>irstL<PERSON>in", "hasEssentialInfo", "firstName", "lastName", "email"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\guards\\profile-completion.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\nimport { CanActivateFn, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\n\nexport const profileCompletionGuard: CanActivateFn = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n\n  // Check if user is logged in\n  if (!authService.userLoggedIn()) {\n    router.navigate(['/login']);\n    return false;\n  }\n\n  const currentUser = authService.getCurrentUser();\n\n  // If user is not found, redirect to login\n  if (!currentUser) {\n    router.navigate(['/login']);\n    return false;\n  }\n\n  // If this is the profile completion page itself, allow access\n  if (state.url === '/complete-profile') {\n    return true;\n  }\n\n  // Only redirect for first-time logins or truly incomplete profiles\n  const isFirstLogin = currentUser.isFirstLogin;\n\n  // Check if profile has essential information (more lenient check)\n  const hasEssentialInfo = currentUser.firstName &&\n                          currentUser.lastName &&\n                          currentUser.email;\n\n  // Only redirect if it's first login OR missing essential information\n  if (isFirstLogin || !hasEssentialInfo) {\n    router.navigate(['/complete-profile']);\n    return false;\n  }\n\n  // Allow access to other pages if profile is complete\n  return true;\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,eAAe,QAAQ,mCAAmC;AAEnE,OAAO,MAAMC,sBAAsB,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACpE,MAAMC,WAAW,GAAGN,MAAM,CAACE,eAAe,CAAC;EAC3C,MAAMK,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC;EAE7B;EACA,IAAI,CAACK,WAAW,CAACE,YAAY,EAAE,EAAE;IAC/BD,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3B,OAAO,KAAK;;EAGd,MAAMC,WAAW,GAAGJ,WAAW,CAACK,cAAc,EAAE;EAEhD;EACA,IAAI,CAACD,WAAW,EAAE;IAChBH,MAAM,CAACE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3B,OAAO,KAAK;;EAGd;EACA,IAAIJ,KAAK,CAACO,GAAG,KAAK,mBAAmB,EAAE;IACrC,OAAO,IAAI;;EAGb;EACA,MAAMC,YAAY,GAAGH,WAAW,CAACG,YAAY;EAE7C;EACA,MAAMC,gBAAgB,GAAGJ,WAAW,CAACK,SAAS,IACtBL,WAAW,CAACM,QAAQ,IACpBN,WAAW,CAACO,KAAK;EAEzC;EACA,IAAIJ,YAAY,IAAI,CAACC,gBAAgB,EAAE;IACrCP,MAAM,CAACE,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;IACtC,OAAO,KAAK;;EAGd;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}