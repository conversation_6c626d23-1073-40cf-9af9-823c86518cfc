{"ast": null, "code": "import { environment } from '@env/environment';\nimport * as i0 from \"@angular/core\";\nexport class LoggerService {\n  constructor() {\n    // Configuration des logs\n    this.enableLogs = false; // Désactiver les logs par défaut\n    this.enabledComponents = [\n      // Liste des composants pour lesquels les logs sont activés\n      // Exemple: 'MessageService', 'MessageChat'\n    ];\n  }\n  /**\n   * Active ou désactive les logs\n   */\n  setLogsEnabled(enabled) {\n    this.enableLogs = enabled;\n  }\n  /**\n   * Ajoute un composant à la liste des composants pour lesquels les logs sont activés\n   */\n  enableComponentLogs(component) {\n    if (!this.enabledComponents.includes(component)) {\n      this.enabledComponents.push(component);\n    }\n  }\n  /**\n   * Supprime un composant de la liste des composants pour lesquels les logs sont activés\n   */\n  disableComponentLogs(component) {\n    const index = this.enabledComponents.indexOf(component);\n    if (index !== -1) {\n      this.enabledComponents.splice(index, 1);\n    }\n  }\n  /**\n   * Supprime tous les composants de la liste des composants pour lesquels les logs sont activés\n   */\n  clearEnabledComponents() {\n    this.enabledComponents = [];\n  }\n  /**\n   * Log a message at the 'log' level\n   */\n  log(message, context) {\n    if (!environment.production) {\n      console.log(message, context);\n    }\n  }\n  /**\n   * Log a message at the 'debug' level\n   * Supports multiple formats:\n   * - debug(message)\n   * - debug(message, context)\n   * - debug(component, message)\n   * - debug(component, message, context)\n   */\n  /**\n   * Vérifie si les logs sont activés pour un composant donné\n   */\n  shouldLog(component) {\n    if (environment.production) return false;\n    if (!this.enableLogs) return false;\n    if (!component) return true;\n    if (this.enabledComponents.length === 0) return true;\n    return this.enabledComponents.includes(component);\n  }\n  debug(messageOrComponent, contextOrMessage, context) {\n    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {\n      // Format: debug(component, message, context)\n      if (!this.shouldLog(messageOrComponent)) return;\n      if (context !== undefined) {\n        console.debug(`[${messageOrComponent}] ${contextOrMessage}`, context);\n      } else {\n        console.debug(`[${messageOrComponent}] ${contextOrMessage}`);\n      }\n    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {\n      // Format: debug(message, context)\n      if (!this.shouldLog()) return;\n      console.debug(messageOrComponent, contextOrMessage);\n    } else {\n      // Format: debug(message)\n      if (!this.shouldLog()) return;\n      console.debug(messageOrComponent);\n    }\n  }\n  /**\n   * Log a message at the 'error' level\n   * Supports multiple formats:\n   * - error(message)\n   * - error(error)\n   * - error(message, error)\n   * - error(message, context)\n   * - error(component, message)\n   * - error(component, error)\n   * - error(component, message, error)\n   * - error(component, message, context)\n   */\n  error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError) {\n    // Les erreurs sont toujours affichées, même en production\n    if (typeof messageOrComponentOrError === 'string' && typeof errorOrMessageOrContext === 'string') {\n      // Format: error(component, message, context/error)\n      // Pour les erreurs, on vérifie quand même si le composant est activé\n      if (!this.shouldLog(messageOrComponentOrError) && !environment.production) return;\n      if (contextOrError !== undefined) {\n        console.error(`[${messageOrComponentOrError}] ${errorOrMessageOrContext}`, contextOrError);\n      } else {\n        console.error(`[${messageOrComponentOrError}] ${errorOrMessageOrContext}`);\n      }\n    } else if (typeof messageOrComponentOrError === 'string' && errorOrMessageOrContext instanceof Error) {\n      // Format: error(component/message, error)\n      console.error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError);\n    } else if (typeof messageOrComponentOrError === 'string') {\n      // Format: error(message, context)\n      console.error(messageOrComponentOrError, errorOrMessageOrContext);\n    } else if (messageOrComponentOrError instanceof Error) {\n      // Format: error(error, context)\n      console.error(messageOrComponentOrError, errorOrMessageOrContext);\n    } else {\n      // Fallback\n      console.error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError);\n    }\n  }\n  /**\n   * Log a message at the 'warn' level\n   * Supports multiple formats:\n   * - warn(message)\n   * - warn(message, context)\n   * - warn(component, message)\n   * - warn(component, message, context)\n   */\n  warn(messageOrComponent, contextOrMessage, context) {\n    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {\n      // Format: warn(component, message, context)\n      if (!this.shouldLog(messageOrComponent)) return;\n      if (context !== undefined) {\n        console.warn(`[${messageOrComponent}] ${contextOrMessage}`, context);\n      } else {\n        console.warn(`[${messageOrComponent}] ${contextOrMessage}`);\n      }\n    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {\n      // Format: warn(message, context)\n      if (!this.shouldLog()) return;\n      console.warn(messageOrComponent, contextOrMessage);\n    } else {\n      // Format: warn(message)\n      if (!this.shouldLog()) return;\n      console.warn(messageOrComponent);\n    }\n  }\n  /**\n   * Log a message at the 'info' level\n   * Supports multiple formats:\n   * - info(message)\n   * - info(message, context)\n   * - info(component, message)\n   * - info(component, message, context)\n   */\n  info(messageOrComponent, contextOrMessage, context) {\n    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {\n      // Format: info(component, message, context)\n      if (!this.shouldLog(messageOrComponent)) return;\n      if (context !== undefined) {\n        console.info(`[${messageOrComponent}] ${contextOrMessage}`, context);\n      } else {\n        console.info(`[${messageOrComponent}] ${contextOrMessage}`);\n      }\n    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {\n      // Format: info(message, context)\n      if (!this.shouldLog()) return;\n      console.info(messageOrComponent, contextOrMessage);\n    } else {\n      // Format: info(message)\n      if (!this.shouldLog()) return;\n      console.info(messageOrComponent);\n    }\n  }\n  static {\n    this.ɵfac = function LoggerService_Factory(t) {\n      return new (t || LoggerService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoggerService,\n      factory: LoggerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "LoggerService", "constructor", "enableLogs", "enabledComponents", "setLogs<PERSON>nabled", "enabled", "enableComponentLogs", "component", "includes", "push", "disableComponentLogs", "index", "indexOf", "splice", "clearEnabledComponents", "log", "message", "context", "production", "console", "shouldLog", "length", "debug", "messageOrComponent", "contextOrMessage", "undefined", "error", "messageOrComponentOrError", "errorOrMessageOrContext", "contextOrError", "Error", "warn", "info", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\logger.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { environment } from '@env/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LoggerService {\r\n  // Configuration des logs\r\n  private enableLogs = false; // Désactiver les logs par défaut\r\n  private enabledComponents: string[] = [\r\n    // Liste des composants pour lesquels les logs sont activés\r\n    // Exemple: 'MessageService', 'MessageChat'\r\n  ];\r\n\r\n  constructor() {}\r\n\r\n  /**\r\n   * Active ou désactive les logs\r\n   */\r\n  setLogsEnabled(enabled: boolean): void {\r\n    this.enableLogs = enabled;\r\n  }\r\n\r\n  /**\r\n   * Ajoute un composant à la liste des composants pour lesquels les logs sont activés\r\n   */\r\n  enableComponentLogs(component: string): void {\r\n    if (!this.enabledComponents.includes(component)) {\r\n      this.enabledComponents.push(component);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Supprime un composant de la liste des composants pour lesquels les logs sont activés\r\n   */\r\n  disableComponentLogs(component: string): void {\r\n    const index = this.enabledComponents.indexOf(component);\r\n    if (index !== -1) {\r\n      this.enabledComponents.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Supprime tous les composants de la liste des composants pour lesquels les logs sont activés\r\n   */\r\n  clearEnabledComponents(): void {\r\n    this.enabledComponents = [];\r\n  }\r\n\r\n  /**\r\n   * Log a message at the 'log' level\r\n   */\r\n  log(message: any, context?: any) {\r\n    if (!environment.production) {\r\n      console.log(message, context);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log a message at the 'debug' level\r\n   * Supports multiple formats:\r\n   * - debug(message)\r\n   * - debug(message, context)\r\n   * - debug(component, message)\r\n   * - debug(component, message, context)\r\n   */\r\n  /**\r\n   * Vérifie si les logs sont activés pour un composant donné\r\n   */\r\n  private shouldLog(component?: string): boolean {\r\n    if (environment.production) return false;\r\n    if (!this.enableLogs) return false;\r\n    if (!component) return true;\r\n    if (this.enabledComponents.length === 0) return true;\r\n    return this.enabledComponents.includes(component);\r\n  }\r\n\r\n  debug(messageOrComponent: any, contextOrMessage?: any, context?: any) {\r\n    if (\r\n      typeof messageOrComponent === 'string' &&\r\n      typeof contextOrMessage === 'string'\r\n    ) {\r\n      // Format: debug(component, message, context)\r\n      if (!this.shouldLog(messageOrComponent)) return;\r\n\r\n      if (context !== undefined) {\r\n        console.debug(`[${messageOrComponent}] ${contextOrMessage}`, context);\r\n      } else {\r\n        console.debug(`[${messageOrComponent}] ${contextOrMessage}`);\r\n      }\r\n    } else if (\r\n      typeof messageOrComponent === 'string' &&\r\n      contextOrMessage !== undefined\r\n    ) {\r\n      // Format: debug(message, context)\r\n      if (!this.shouldLog()) return;\r\n      console.debug(messageOrComponent, contextOrMessage);\r\n    } else {\r\n      // Format: debug(message)\r\n      if (!this.shouldLog()) return;\r\n      console.debug(messageOrComponent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log a message at the 'error' level\r\n   * Supports multiple formats:\r\n   * - error(message)\r\n   * - error(error)\r\n   * - error(message, error)\r\n   * - error(message, context)\r\n   * - error(component, message)\r\n   * - error(component, error)\r\n   * - error(component, message, error)\r\n   * - error(component, message, context)\r\n   */\r\n  error(\r\n    messageOrComponentOrError: any,\r\n    errorOrMessageOrContext?: any,\r\n    contextOrError?: any\r\n  ) {\r\n    // Les erreurs sont toujours affichées, même en production\r\n    if (\r\n      typeof messageOrComponentOrError === 'string' &&\r\n      typeof errorOrMessageOrContext === 'string'\r\n    ) {\r\n      // Format: error(component, message, context/error)\r\n      // Pour les erreurs, on vérifie quand même si le composant est activé\r\n      if (!this.shouldLog(messageOrComponentOrError) && !environment.production)\r\n        return;\r\n\r\n      if (contextOrError !== undefined) {\r\n        console.error(\r\n          `[${messageOrComponentOrError}] ${errorOrMessageOrContext}`,\r\n          contextOrError\r\n        );\r\n      } else {\r\n        console.error(\r\n          `[${messageOrComponentOrError}] ${errorOrMessageOrContext}`\r\n        );\r\n      }\r\n    } else if (\r\n      typeof messageOrComponentOrError === 'string' &&\r\n      errorOrMessageOrContext instanceof Error\r\n    ) {\r\n      // Format: error(component/message, error)\r\n      console.error(\r\n        messageOrComponentOrError,\r\n        errorOrMessageOrContext,\r\n        contextOrError\r\n      );\r\n    } else if (typeof messageOrComponentOrError === 'string') {\r\n      // Format: error(message, context)\r\n      console.error(messageOrComponentOrError, errorOrMessageOrContext);\r\n    } else if (messageOrComponentOrError instanceof Error) {\r\n      // Format: error(error, context)\r\n      console.error(messageOrComponentOrError, errorOrMessageOrContext);\r\n    } else {\r\n      // Fallback\r\n      console.error(\r\n        messageOrComponentOrError,\r\n        errorOrMessageOrContext,\r\n        contextOrError\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log a message at the 'warn' level\r\n   * Supports multiple formats:\r\n   * - warn(message)\r\n   * - warn(message, context)\r\n   * - warn(component, message)\r\n   * - warn(component, message, context)\r\n   */\r\n  warn(messageOrComponent: any, contextOrMessage?: any, context?: any) {\r\n    if (\r\n      typeof messageOrComponent === 'string' &&\r\n      typeof contextOrMessage === 'string'\r\n    ) {\r\n      // Format: warn(component, message, context)\r\n      if (!this.shouldLog(messageOrComponent)) return;\r\n\r\n      if (context !== undefined) {\r\n        console.warn(`[${messageOrComponent}] ${contextOrMessage}`, context);\r\n      } else {\r\n        console.warn(`[${messageOrComponent}] ${contextOrMessage}`);\r\n      }\r\n    } else if (\r\n      typeof messageOrComponent === 'string' &&\r\n      contextOrMessage !== undefined\r\n    ) {\r\n      // Format: warn(message, context)\r\n      if (!this.shouldLog()) return;\r\n      console.warn(messageOrComponent, contextOrMessage);\r\n    } else {\r\n      // Format: warn(message)\r\n      if (!this.shouldLog()) return;\r\n      console.warn(messageOrComponent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log a message at the 'info' level\r\n   * Supports multiple formats:\r\n   * - info(message)\r\n   * - info(message, context)\r\n   * - info(component, message)\r\n   * - info(component, message, context)\r\n   */\r\n  info(messageOrComponent: any, contextOrMessage?: any, context?: any) {\r\n    if (\r\n      typeof messageOrComponent === 'string' &&\r\n      typeof contextOrMessage === 'string'\r\n    ) {\r\n      // Format: info(component, message, context)\r\n      if (!this.shouldLog(messageOrComponent)) return;\r\n\r\n      if (context !== undefined) {\r\n        console.info(`[${messageOrComponent}] ${contextOrMessage}`, context);\r\n      } else {\r\n        console.info(`[${messageOrComponent}] ${contextOrMessage}`);\r\n      }\r\n    } else if (\r\n      typeof messageOrComponent === 'string' &&\r\n      contextOrMessage !== undefined\r\n    ) {\r\n      // Format: info(message, context)\r\n      if (!this.shouldLog()) return;\r\n      console.info(messageOrComponent, contextOrMessage);\r\n    } else {\r\n      // Format: info(message)\r\n      if (!this.shouldLog()) return;\r\n      console.info(messageOrComponent);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,kBAAkB;;AAK9C,OAAM,MAAOC,aAAa;EAQxBC,YAAA;IAPA;IACQ,KAAAC,UAAU,GAAG,KAAK,CAAC,CAAC;IACpB,KAAAC,iBAAiB,GAAa;MACpC;MACA;IAAA,CACD;EAEc;EAEf;;;EAGAC,cAAcA,CAACC,OAAgB;IAC7B,IAAI,CAACH,UAAU,GAAGG,OAAO;EAC3B;EAEA;;;EAGAC,mBAAmBA,CAACC,SAAiB;IACnC,IAAI,CAAC,IAAI,CAACJ,iBAAiB,CAACK,QAAQ,CAACD,SAAS,CAAC,EAAE;MAC/C,IAAI,CAACJ,iBAAiB,CAACM,IAAI,CAACF,SAAS,CAAC;;EAE1C;EAEA;;;EAGAG,oBAAoBA,CAACH,SAAiB;IACpC,MAAMI,KAAK,GAAG,IAAI,CAACR,iBAAiB,CAACS,OAAO,CAACL,SAAS,CAAC;IACvD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACR,iBAAiB,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;;EAE3C;EAEA;;;EAGAG,sBAAsBA,CAAA;IACpB,IAAI,CAACX,iBAAiB,GAAG,EAAE;EAC7B;EAEA;;;EAGAY,GAAGA,CAACC,OAAY,EAAEC,OAAa;IAC7B,IAAI,CAAClB,WAAW,CAACmB,UAAU,EAAE;MAC3BC,OAAO,CAACJ,GAAG,CAACC,OAAO,EAAEC,OAAO,CAAC;;EAEjC;EAEA;;;;;;;;EAQA;;;EAGQG,SAASA,CAACb,SAAkB;IAClC,IAAIR,WAAW,CAACmB,UAAU,EAAE,OAAO,KAAK;IACxC,IAAI,CAAC,IAAI,CAAChB,UAAU,EAAE,OAAO,KAAK;IAClC,IAAI,CAACK,SAAS,EAAE,OAAO,IAAI;IAC3B,IAAI,IAAI,CAACJ,iBAAiB,CAACkB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IACpD,OAAO,IAAI,CAAClB,iBAAiB,CAACK,QAAQ,CAACD,SAAS,CAAC;EACnD;EAEAe,KAAKA,CAACC,kBAAuB,EAAEC,gBAAsB,EAAEP,OAAa;IAClE,IACE,OAAOM,kBAAkB,KAAK,QAAQ,IACtC,OAAOC,gBAAgB,KAAK,QAAQ,EACpC;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACG,kBAAkB,CAAC,EAAE;MAEzC,IAAIN,OAAO,KAAKQ,SAAS,EAAE;QACzBN,OAAO,CAACG,KAAK,CAAC,IAAIC,kBAAkB,KAAKC,gBAAgB,EAAE,EAAEP,OAAO,CAAC;OACtE,MAAM;QACLE,OAAO,CAACG,KAAK,CAAC,IAAIC,kBAAkB,KAAKC,gBAAgB,EAAE,CAAC;;KAE/D,MAAM,IACL,OAAOD,kBAAkB,KAAK,QAAQ,IACtCC,gBAAgB,KAAKC,SAAS,EAC9B;MACA;MACA,IAAI,CAAC,IAAI,CAACL,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACG,KAAK,CAACC,kBAAkB,EAAEC,gBAAgB,CAAC;KACpD,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACG,KAAK,CAACC,kBAAkB,CAAC;;EAErC;EAEA;;;;;;;;;;;;EAYAG,KAAKA,CACHC,yBAA8B,EAC9BC,uBAA6B,EAC7BC,cAAoB;IAEpB;IACA,IACE,OAAOF,yBAAyB,KAAK,QAAQ,IAC7C,OAAOC,uBAAuB,KAAK,QAAQ,EAC3C;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACR,SAAS,CAACO,yBAAyB,CAAC,IAAI,CAAC5B,WAAW,CAACmB,UAAU,EACvE;MAEF,IAAIW,cAAc,KAAKJ,SAAS,EAAE;QAChCN,OAAO,CAACO,KAAK,CACX,IAAIC,yBAAyB,KAAKC,uBAAuB,EAAE,EAC3DC,cAAc,CACf;OACF,MAAM;QACLV,OAAO,CAACO,KAAK,CACX,IAAIC,yBAAyB,KAAKC,uBAAuB,EAAE,CAC5D;;KAEJ,MAAM,IACL,OAAOD,yBAAyB,KAAK,QAAQ,IAC7CC,uBAAuB,YAAYE,KAAK,EACxC;MACA;MACAX,OAAO,CAACO,KAAK,CACXC,yBAAyB,EACzBC,uBAAuB,EACvBC,cAAc,CACf;KACF,MAAM,IAAI,OAAOF,yBAAyB,KAAK,QAAQ,EAAE;MACxD;MACAR,OAAO,CAACO,KAAK,CAACC,yBAAyB,EAAEC,uBAAuB,CAAC;KAClE,MAAM,IAAID,yBAAyB,YAAYG,KAAK,EAAE;MACrD;MACAX,OAAO,CAACO,KAAK,CAACC,yBAAyB,EAAEC,uBAAuB,CAAC;KAClE,MAAM;MACL;MACAT,OAAO,CAACO,KAAK,CACXC,yBAAyB,EACzBC,uBAAuB,EACvBC,cAAc,CACf;;EAEL;EAEA;;;;;;;;EAQAE,IAAIA,CAACR,kBAAuB,EAAEC,gBAAsB,EAAEP,OAAa;IACjE,IACE,OAAOM,kBAAkB,KAAK,QAAQ,IACtC,OAAOC,gBAAgB,KAAK,QAAQ,EACpC;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACG,kBAAkB,CAAC,EAAE;MAEzC,IAAIN,OAAO,KAAKQ,SAAS,EAAE;QACzBN,OAAO,CAACY,IAAI,CAAC,IAAIR,kBAAkB,KAAKC,gBAAgB,EAAE,EAAEP,OAAO,CAAC;OACrE,MAAM;QACLE,OAAO,CAACY,IAAI,CAAC,IAAIR,kBAAkB,KAAKC,gBAAgB,EAAE,CAAC;;KAE9D,MAAM,IACL,OAAOD,kBAAkB,KAAK,QAAQ,IACtCC,gBAAgB,KAAKC,SAAS,EAC9B;MACA;MACA,IAAI,CAAC,IAAI,CAACL,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACY,IAAI,CAACR,kBAAkB,EAAEC,gBAAgB,CAAC;KACnD,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACY,IAAI,CAACR,kBAAkB,CAAC;;EAEpC;EAEA;;;;;;;;EAQAS,IAAIA,CAACT,kBAAuB,EAAEC,gBAAsB,EAAEP,OAAa;IACjE,IACE,OAAOM,kBAAkB,KAAK,QAAQ,IACtC,OAAOC,gBAAgB,KAAK,QAAQ,EACpC;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACG,kBAAkB,CAAC,EAAE;MAEzC,IAAIN,OAAO,KAAKQ,SAAS,EAAE;QACzBN,OAAO,CAACa,IAAI,CAAC,IAAIT,kBAAkB,KAAKC,gBAAgB,EAAE,EAAEP,OAAO,CAAC;OACrE,MAAM;QACLE,OAAO,CAACa,IAAI,CAAC,IAAIT,kBAAkB,KAAKC,gBAAgB,EAAE,CAAC;;KAE9D,MAAM,IACL,OAAOD,kBAAkB,KAAK,QAAQ,IACtCC,gBAAgB,KAAKC,SAAS,EAC9B;MACA;MACA,IAAI,CAAC,IAAI,CAACL,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACa,IAAI,CAACT,kBAAkB,EAAEC,gBAAgB,CAAC;KACnD,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE,EAAE;MACvBD,OAAO,CAACa,IAAI,CAACT,kBAAkB,CAAC;;EAEpC;;;uBArOWvB,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAiC,OAAA,EAAbjC,aAAa,CAAAkC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}