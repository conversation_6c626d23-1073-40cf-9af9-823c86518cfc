{"ast": null, "code": "const {\n  toString,\n  hasOwnProperty\n} = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nexport function equal(a, b) {\n  try {\n    return check(a, b);\n  } finally {\n    previousComparisons.clear();\n  }\n}\n// Allow default imports as well.\nexport default equal;\nfunction check(a, b) {\n  // If the two values are strictly equal, our job is easy.\n  if (a === b) {\n    return true;\n  }\n  // Object.prototype.toString returns a representation of the runtime type of\n  // the given value that is considerably more precise than typeof.\n  const aTag = toString.call(a);\n  const bTag = toString.call(b);\n  // If the runtime types of a and b are different, they could maybe be equal\n  // under some interpretation of equality, but for simplicity and performance\n  // we just return false instead.\n  if (aTag !== bTag) {\n    return false;\n  }\n  switch (aTag) {\n    case '[object Array]':\n      // Arrays are a lot like other objects, but we can cheaply compare their\n      // lengths as a short-cut before comparing their elements.\n      if (a.length !== b.length) return false;\n    // Fall through to object case...\n    case '[object Object]':\n      {\n        if (previouslyCompared(a, b)) return true;\n        const aKeys = definedKeys(a);\n        const bKeys = definedKeys(b);\n        // If `a` and `b` have a different number of enumerable keys, they\n        // must be different.\n        const keyCount = aKeys.length;\n        if (keyCount !== bKeys.length) return false;\n        // Now make sure they have the same keys.\n        for (let k = 0; k < keyCount; ++k) {\n          if (!hasOwnProperty.call(b, aKeys[k])) {\n            return false;\n          }\n        }\n        // Finally, check deep equality of all child properties.\n        for (let k = 0; k < keyCount; ++k) {\n          const key = aKeys[k];\n          if (!check(a[key], b[key])) {\n            return false;\n          }\n        }\n        return true;\n      }\n    case '[object Error]':\n      return a.name === b.name && a.message === b.message;\n    case '[object Number]':\n      // Handle NaN, which is !== itself.\n      if (a !== a) return b !== b;\n    // Fall through to shared +a === +b case...\n    case '[object Boolean]':\n    case '[object Date]':\n      return +a === +b;\n    case '[object RegExp]':\n    case '[object String]':\n      return a == `${b}`;\n    case '[object Map]':\n    case '[object Set]':\n      {\n        if (a.size !== b.size) return false;\n        if (previouslyCompared(a, b)) return true;\n        const aIterator = a.entries();\n        const isMap = aTag === '[object Map]';\n        while (true) {\n          const info = aIterator.next();\n          if (info.done) break;\n          // If a instanceof Set, aValue === aKey.\n          const [aKey, aValue] = info.value;\n          // So this works the same way for both Set and Map.\n          if (!b.has(aKey)) {\n            return false;\n          }\n          // However, we care about deep equality of values only when dealing\n          // with Map structures.\n          if (isMap && !check(aValue, b.get(aKey))) {\n            return false;\n          }\n        }\n        return true;\n      }\n    case '[object Uint16Array]':\n    case '[object Uint8Array]': // Buffer, in Node.js.\n    case '[object Uint32Array]':\n    case '[object Int32Array]':\n    case '[object Int8Array]':\n    case '[object Int16Array]':\n    case '[object ArrayBuffer]':\n      // DataView doesn't need these conversions, but the equality check is\n      // otherwise the same.\n      a = new Uint8Array(a);\n      b = new Uint8Array(b);\n    // Fall through...\n    case '[object DataView]':\n      {\n        let len = a.byteLength;\n        if (len === b.byteLength) {\n          while (len-- && a[len] === b[len]) {\n            // Keep looping as long as the bytes are equal.\n          }\n        }\n        return len === -1;\n      }\n    case '[object AsyncFunction]':\n    case '[object GeneratorFunction]':\n    case '[object AsyncGeneratorFunction]':\n    case '[object Function]':\n      {\n        const aCode = fnToStr.call(a);\n        if (aCode !== fnToStr.call(b)) {\n          return false;\n        }\n        // We consider non-native functions equal if they have the same code\n        // (native functions require === because their code is censored).\n        // Note that this behavior is not entirely sound, since !== function\n        // objects with the same code can behave differently depending on\n        // their closure scope. However, any function can behave differently\n        // depending on the values of its input arguments (including this)\n        // and its calling context (including its closure scope), even\n        // though the function object is === to itself; and it is entirely\n        // possible for functions that are not === to behave exactly the\n        // same under all conceivable circumstances. Because none of these\n        // factors are statically decidable in JavaScript, JS function\n        // equality is not well-defined. This ambiguity allows us to\n        // consider the best possible heuristic among various imperfect\n        // options, and equating non-native functions that have the same\n        // code has enormous practical benefits, such as when comparing\n        // functions that are repeatedly passed as fresh function\n        // expressions within objects that are otherwise deeply equal. Since\n        // any function created from the same syntactic expression (in the\n        // same code location) will always stringify to the same code\n        // according to fnToStr.call, we can reasonably expect these\n        // repeatedly passed function expressions to have the same code, and\n        // thus behave \"the same\" (with all the caveats mentioned above),\n        // even though the runtime function objects are !== to one another.\n        return !endsWith(aCode, nativeCodeSuffix);\n      }\n  }\n  // Otherwise the values are not equal.\n  return false;\n}\nfunction definedKeys(obj) {\n  // Remember that the second argument to Array.prototype.filter will be\n  // used as `this` within the callback function.\n  return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n  return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n  const fromIndex = full.length - suffix.length;\n  return fromIndex >= 0 && full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n  // Though cyclic references can make an object graph appear infinite from the\n  // perspective of a depth-first traversal, the graph still contains a finite\n  // number of distinct object references. We use the previousComparisons cache\n  // to avoid comparing the same pair of object references more than once, which\n  // guarantees termination (even if we end up comparing every object in one\n  // graph to every object in the other graph, which is extremely unlikely),\n  // while still allowing weird isomorphic structures (like rings with different\n  // lengths) a chance to pass the equality test.\n  let bSet = previousComparisons.get(a);\n  if (bSet) {\n    // Return true here because we can be sure false will be returned somewhere\n    // else if the objects are not equivalent.\n    if (bSet.has(b)) return true;\n  } else {\n    previousComparisons.set(a, bSet = new Set());\n  }\n  bSet.add(b);\n  return false;\n}", "map": {"version": 3, "names": ["toString", "hasOwnProperty", "Object", "prototype", "fnToStr", "Function", "previousComparisons", "Map", "equal", "a", "b", "check", "clear", "aTag", "call", "bTag", "length", "previouslyCompared", "a<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "keyCount", "k", "key", "name", "message", "size", "aIterator", "entries", "isMap", "info", "next", "done", "a<PERSON><PERSON>", "aValue", "value", "has", "get", "Uint8Array", "len", "byteLength", "aCode", "endsWith", "nativeCodeSuffix", "obj", "keys", "filter", "isDefinedKey", "full", "suffix", "fromIndex", "indexOf", "bSet", "set", "Set", "add"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@wry/equality/lib/index.js"], "sourcesContent": ["const { toString, hasOwnProperty } = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nexport function equal(a, b) {\n    try {\n        return check(a, b);\n    }\n    finally {\n        previousComparisons.clear();\n    }\n}\n// Allow default imports as well.\nexport default equal;\nfunction check(a, b) {\n    // If the two values are strictly equal, our job is easy.\n    if (a === b) {\n        return true;\n    }\n    // Object.prototype.toString returns a representation of the runtime type of\n    // the given value that is considerably more precise than typeof.\n    const aTag = toString.call(a);\n    const bTag = toString.call(b);\n    // If the runtime types of a and b are different, they could maybe be equal\n    // under some interpretation of equality, but for simplicity and performance\n    // we just return false instead.\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch (aTag) {\n        case '[object Array]':\n            // Arrays are a lot like other objects, but we can cheaply compare their\n            // lengths as a short-cut before comparing their elements.\n            if (a.length !== b.length)\n                return false;\n        // Fall through to object case...\n        case '[object Object]': {\n            if (previouslyCompared(a, b))\n                return true;\n            const aKeys = definedKeys(a);\n            const bKeys = definedKeys(b);\n            // If `a` and `b` have a different number of enumerable keys, they\n            // must be different.\n            const keyCount = aKeys.length;\n            if (keyCount !== bKeys.length)\n                return false;\n            // Now make sure they have the same keys.\n            for (let k = 0; k < keyCount; ++k) {\n                if (!hasOwnProperty.call(b, aKeys[k])) {\n                    return false;\n                }\n            }\n            // Finally, check deep equality of all child properties.\n            for (let k = 0; k < keyCount; ++k) {\n                const key = aKeys[k];\n                if (!check(a[key], b[key])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Error]':\n            return a.name === b.name && a.message === b.message;\n        case '[object Number]':\n            // Handle NaN, which is !== itself.\n            if (a !== a)\n                return b !== b;\n        // Fall through to shared +a === +b case...\n        case '[object Boolean]':\n        case '[object Date]':\n            return +a === +b;\n        case '[object RegExp]':\n        case '[object String]':\n            return a == `${b}`;\n        case '[object Map]':\n        case '[object Set]': {\n            if (a.size !== b.size)\n                return false;\n            if (previouslyCompared(a, b))\n                return true;\n            const aIterator = a.entries();\n            const isMap = aTag === '[object Map]';\n            while (true) {\n                const info = aIterator.next();\n                if (info.done)\n                    break;\n                // If a instanceof Set, aValue === aKey.\n                const [aKey, aValue] = info.value;\n                // So this works the same way for both Set and Map.\n                if (!b.has(aKey)) {\n                    return false;\n                }\n                // However, we care about deep equality of values only when dealing\n                // with Map structures.\n                if (isMap && !check(aValue, b.get(aKey))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Uint16Array]':\n        case '[object Uint8Array]': // Buffer, in Node.js.\n        case '[object Uint32Array]':\n        case '[object Int32Array]':\n        case '[object Int8Array]':\n        case '[object Int16Array]':\n        case '[object ArrayBuffer]':\n            // DataView doesn't need these conversions, but the equality check is\n            // otherwise the same.\n            a = new Uint8Array(a);\n            b = new Uint8Array(b);\n        // Fall through...\n        case '[object DataView]': {\n            let len = a.byteLength;\n            if (len === b.byteLength) {\n                while (len-- && a[len] === b[len]) {\n                    // Keep looping as long as the bytes are equal.\n                }\n            }\n            return len === -1;\n        }\n        case '[object AsyncFunction]':\n        case '[object GeneratorFunction]':\n        case '[object AsyncGeneratorFunction]':\n        case '[object Function]': {\n            const aCode = fnToStr.call(a);\n            if (aCode !== fnToStr.call(b)) {\n                return false;\n            }\n            // We consider non-native functions equal if they have the same code\n            // (native functions require === because their code is censored).\n            // Note that this behavior is not entirely sound, since !== function\n            // objects with the same code can behave differently depending on\n            // their closure scope. However, any function can behave differently\n            // depending on the values of its input arguments (including this)\n            // and its calling context (including its closure scope), even\n            // though the function object is === to itself; and it is entirely\n            // possible for functions that are not === to behave exactly the\n            // same under all conceivable circumstances. Because none of these\n            // factors are statically decidable in JavaScript, JS function\n            // equality is not well-defined. This ambiguity allows us to\n            // consider the best possible heuristic among various imperfect\n            // options, and equating non-native functions that have the same\n            // code has enormous practical benefits, such as when comparing\n            // functions that are repeatedly passed as fresh function\n            // expressions within objects that are otherwise deeply equal. Since\n            // any function created from the same syntactic expression (in the\n            // same code location) will always stringify to the same code\n            // according to fnToStr.call, we can reasonably expect these\n            // repeatedly passed function expressions to have the same code, and\n            // thus behave \"the same\" (with all the caveats mentioned above),\n            // even though the runtime function objects are !== to one another.\n            return !endsWith(aCode, nativeCodeSuffix);\n        }\n    }\n    // Otherwise the values are not equal.\n    return false;\n}\nfunction definedKeys(obj) {\n    // Remember that the second argument to Array.prototype.filter will be\n    // used as `this` within the callback function.\n    return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n    return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n    const fromIndex = full.length - suffix.length;\n    return fromIndex >= 0 &&\n        full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n    // Though cyclic references can make an object graph appear infinite from the\n    // perspective of a depth-first traversal, the graph still contains a finite\n    // number of distinct object references. We use the previousComparisons cache\n    // to avoid comparing the same pair of object references more than once, which\n    // guarantees termination (even if we end up comparing every object in one\n    // graph to every object in the other graph, which is extremely unlikely),\n    // while still allowing weird isomorphic structures (like rings with different\n    // lengths) a chance to pass the equality test.\n    let bSet = previousComparisons.get(a);\n    if (bSet) {\n        // Return true here because we can be sure false will be returned somewhere\n        // else if the objects are not equivalent.\n        if (bSet.has(b))\n            return true;\n    }\n    else {\n        previousComparisons.set(a, bSet = new Set);\n    }\n    bSet.add(b);\n    return false;\n}\n"], "mappings": "AAAA,MAAM;EAAEA,QAAQ;EAAEC;AAAe,CAAC,GAAGC,MAAM,CAACC,SAAS;AACrD,MAAMC,OAAO,GAAGC,QAAQ,CAACF,SAAS,CAACH,QAAQ;AAC3C,MAAMM,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAI;IACA,OAAOC,KAAK,CAACF,CAAC,EAAEC,CAAC,CAAC;EACtB,CAAC,SACO;IACJJ,mBAAmB,CAACM,KAAK,CAAC,CAAC;EAC/B;AACJ;AACA;AACA,eAAeJ,KAAK;AACpB,SAASG,KAAKA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACjB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA;EACA;EACA,MAAMG,IAAI,GAAGb,QAAQ,CAACc,IAAI,CAACL,CAAC,CAAC;EAC7B,MAAMM,IAAI,GAAGf,QAAQ,CAACc,IAAI,CAACJ,CAAC,CAAC;EAC7B;EACA;EACA;EACA,IAAIG,IAAI,KAAKE,IAAI,EAAE;IACf,OAAO,KAAK;EAChB;EACA,QAAQF,IAAI;IACR,KAAK,gBAAgB;MACjB;MACA;MACA,IAAIJ,CAAC,CAACO,MAAM,KAAKN,CAAC,CAACM,MAAM,EACrB,OAAO,KAAK;IACpB;IACA,KAAK,iBAAiB;MAAE;QACpB,IAAIC,kBAAkB,CAACR,CAAC,EAAEC,CAAC,CAAC,EACxB,OAAO,IAAI;QACf,MAAMQ,KAAK,GAAGC,WAAW,CAACV,CAAC,CAAC;QAC5B,MAAMW,KAAK,GAAGD,WAAW,CAACT,CAAC,CAAC;QAC5B;QACA;QACA,MAAMW,QAAQ,GAAGH,KAAK,CAACF,MAAM;QAC7B,IAAIK,QAAQ,KAAKD,KAAK,CAACJ,MAAM,EACzB,OAAO,KAAK;QAChB;QACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAE,EAAEC,CAAC,EAAE;UAC/B,IAAI,CAACrB,cAAc,CAACa,IAAI,CAACJ,CAAC,EAAEQ,KAAK,CAACI,CAAC,CAAC,CAAC,EAAE;YACnC,OAAO,KAAK;UAChB;QACJ;QACA;QACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAE,EAAEC,CAAC,EAAE;UAC/B,MAAMC,GAAG,GAAGL,KAAK,CAACI,CAAC,CAAC;UACpB,IAAI,CAACX,KAAK,CAACF,CAAC,CAACc,GAAG,CAAC,EAAEb,CAAC,CAACa,GAAG,CAAC,CAAC,EAAE;YACxB,OAAO,KAAK;UAChB;QACJ;QACA,OAAO,IAAI;MACf;IACA,KAAK,gBAAgB;MACjB,OAAOd,CAAC,CAACe,IAAI,KAAKd,CAAC,CAACc,IAAI,IAAIf,CAAC,CAACgB,OAAO,KAAKf,CAAC,CAACe,OAAO;IACvD,KAAK,iBAAiB;MAClB;MACA,IAAIhB,CAAC,KAAKA,CAAC,EACP,OAAOC,CAAC,KAAKA,CAAC;IACtB;IACA,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAChB,OAAO,CAACD,CAAC,KAAK,CAACC,CAAC;IACpB,KAAK,iBAAiB;IACtB,KAAK,iBAAiB;MAClB,OAAOD,CAAC,IAAK,GAAEC,CAAE,EAAC;IACtB,KAAK,cAAc;IACnB,KAAK,cAAc;MAAE;QACjB,IAAID,CAAC,CAACiB,IAAI,KAAKhB,CAAC,CAACgB,IAAI,EACjB,OAAO,KAAK;QAChB,IAAIT,kBAAkB,CAACR,CAAC,EAAEC,CAAC,CAAC,EACxB,OAAO,IAAI;QACf,MAAMiB,SAAS,GAAGlB,CAAC,CAACmB,OAAO,CAAC,CAAC;QAC7B,MAAMC,KAAK,GAAGhB,IAAI,KAAK,cAAc;QACrC,OAAO,IAAI,EAAE;UACT,MAAMiB,IAAI,GAAGH,SAAS,CAACI,IAAI,CAAC,CAAC;UAC7B,IAAID,IAAI,CAACE,IAAI,EACT;UACJ;UACA,MAAM,CAACC,IAAI,EAAEC,MAAM,CAAC,GAAGJ,IAAI,CAACK,KAAK;UACjC;UACA,IAAI,CAACzB,CAAC,CAAC0B,GAAG,CAACH,IAAI,CAAC,EAAE;YACd,OAAO,KAAK;UAChB;UACA;UACA;UACA,IAAIJ,KAAK,IAAI,CAAClB,KAAK,CAACuB,MAAM,EAAExB,CAAC,CAAC2B,GAAG,CAACJ,IAAI,CAAC,CAAC,EAAE;YACtC,OAAO,KAAK;UAChB;QACJ;QACA,OAAO,IAAI;MACf;IACA,KAAK,sBAAsB;IAC3B,KAAK,qBAAqB,CAAC,CAAC;IAC5B,KAAK,sBAAsB;IAC3B,KAAK,qBAAqB;IAC1B,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,sBAAsB;MACvB;MACA;MACAxB,CAAC,GAAG,IAAI6B,UAAU,CAAC7B,CAAC,CAAC;MACrBC,CAAC,GAAG,IAAI4B,UAAU,CAAC5B,CAAC,CAAC;IACzB;IACA,KAAK,mBAAmB;MAAE;QACtB,IAAI6B,GAAG,GAAG9B,CAAC,CAAC+B,UAAU;QACtB,IAAID,GAAG,KAAK7B,CAAC,CAAC8B,UAAU,EAAE;UACtB,OAAOD,GAAG,EAAE,IAAI9B,CAAC,CAAC8B,GAAG,CAAC,KAAK7B,CAAC,CAAC6B,GAAG,CAAC,EAAE;YAC/B;UAAA;QAER;QACA,OAAOA,GAAG,KAAK,CAAC,CAAC;MACrB;IACA,KAAK,wBAAwB;IAC7B,KAAK,4BAA4B;IACjC,KAAK,iCAAiC;IACtC,KAAK,mBAAmB;MAAE;QACtB,MAAME,KAAK,GAAGrC,OAAO,CAACU,IAAI,CAACL,CAAC,CAAC;QAC7B,IAAIgC,KAAK,KAAKrC,OAAO,CAACU,IAAI,CAACJ,CAAC,CAAC,EAAE;UAC3B,OAAO,KAAK;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAO,CAACgC,QAAQ,CAACD,KAAK,EAAEE,gBAAgB,CAAC;MAC7C;EACJ;EACA;EACA,OAAO,KAAK;AAChB;AACA,SAASxB,WAAWA,CAACyB,GAAG,EAAE;EACtB;EACA;EACA,OAAO1C,MAAM,CAAC2C,IAAI,CAACD,GAAG,CAAC,CAACE,MAAM,CAACC,YAAY,EAAEH,GAAG,CAAC;AACrD;AACA,SAASG,YAAYA,CAACxB,GAAG,EAAE;EACvB,OAAO,IAAI,CAACA,GAAG,CAAC,KAAK,KAAK,CAAC;AAC/B;AACA,MAAMoB,gBAAgB,GAAG,mBAAmB;AAC5C,SAASD,QAAQA,CAACM,IAAI,EAAEC,MAAM,EAAE;EAC5B,MAAMC,SAAS,GAAGF,IAAI,CAAChC,MAAM,GAAGiC,MAAM,CAACjC,MAAM;EAC7C,OAAOkC,SAAS,IAAI,CAAC,IACjBF,IAAI,CAACG,OAAO,CAACF,MAAM,EAAEC,SAAS,CAAC,KAAKA,SAAS;AACrD;AACA,SAASjC,kBAAkBA,CAACR,CAAC,EAAEC,CAAC,EAAE;EAC9B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI0C,IAAI,GAAG9C,mBAAmB,CAAC+B,GAAG,CAAC5B,CAAC,CAAC;EACrC,IAAI2C,IAAI,EAAE;IACN;IACA;IACA,IAAIA,IAAI,CAAChB,GAAG,CAAC1B,CAAC,CAAC,EACX,OAAO,IAAI;EACnB,CAAC,MACI;IACDJ,mBAAmB,CAAC+C,GAAG,CAAC5C,CAAC,EAAE2C,IAAI,GAAG,IAAIE,GAAG,CAAD,CAAC,CAAC;EAC9C;EACAF,IAAI,CAACG,GAAG,CAAC7C,CAAC,CAAC;EACX,OAAO,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}