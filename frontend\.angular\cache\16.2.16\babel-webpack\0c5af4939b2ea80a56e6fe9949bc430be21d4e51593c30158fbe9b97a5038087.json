{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { visit, BREAK, isSelectionNode } from \"graphql\";\nimport { argumentsObjectFromField, buildQueryFromSelectionSet, createFragmentMap, getFragmentDefinitions, getMainDefinition, hasDirectives, isField, isInlineFragment, mergeDeep, mergeDeepArray, removeClientSetsFromDocument, resultKeyNameFromField, shouldInclude } from \"../utilities/index.js\";\nimport { cacheSlot } from \"../cache/index.js\";\nvar LocalState = /** @class */function () {\n  function LocalState(_a) {\n    var cache = _a.cache,\n      client = _a.client,\n      resolvers = _a.resolvers,\n      fragmentMatcher = _a.fragmentMatcher;\n    this.selectionsToResolveCache = new WeakMap();\n    this.cache = cache;\n    if (client) {\n      this.client = client;\n    }\n    if (resolvers) {\n      this.addResolvers(resolvers);\n    }\n    if (fragmentMatcher) {\n      this.setFragmentMatcher(fragmentMatcher);\n    }\n  }\n  LocalState.prototype.addResolvers = function (resolvers) {\n    var _this = this;\n    this.resolvers = this.resolvers || {};\n    if (Array.isArray(resolvers)) {\n      resolvers.forEach(function (resolverGroup) {\n        _this.resolvers = mergeDeep(_this.resolvers, resolverGroup);\n      });\n    } else {\n      this.resolvers = mergeDeep(this.resolvers, resolvers);\n    }\n  };\n  LocalState.prototype.setResolvers = function (resolvers) {\n    this.resolvers = {};\n    this.addResolvers(resolvers);\n  };\n  LocalState.prototype.getResolvers = function () {\n    return this.resolvers || {};\n  };\n  // Run local client resolvers against the incoming query and remote data.\n  // Locally resolved field values are merged with the incoming remote data,\n  // and returned. Note that locally resolved fields will overwrite\n  // remote data using the same field name.\n  LocalState.prototype.runResolvers = function (_a) {\n    return __awaiter(this, arguments, void 0, function (_b) {\n      var document = _b.document,\n        remoteResult = _b.remoteResult,\n        context = _b.context,\n        variables = _b.variables,\n        _c = _b.onlyRunForcedResolvers,\n        onlyRunForcedResolvers = _c === void 0 ? false : _c;\n      return __generator(this, function (_d) {\n        if (document) {\n          return [2 /*return*/, this.resolveDocument(document, remoteResult.data, context, variables, this.fragmentMatcher, onlyRunForcedResolvers).then(function (localResult) {\n            return __assign(__assign({}, remoteResult), {\n              data: localResult.result\n            });\n          })];\n        }\n        return [2 /*return*/, remoteResult];\n      });\n    });\n  };\n  LocalState.prototype.setFragmentMatcher = function (fragmentMatcher) {\n    this.fragmentMatcher = fragmentMatcher;\n  };\n  LocalState.prototype.getFragmentMatcher = function () {\n    return this.fragmentMatcher;\n  };\n  // Client queries contain everything in the incoming document (if a @client\n  // directive is found).\n  LocalState.prototype.clientQuery = function (document) {\n    if (hasDirectives([\"client\"], document)) {\n      if (this.resolvers) {\n        return document;\n      }\n    }\n    return null;\n  };\n  // Server queries are stripped of all @client based selection sets.\n  LocalState.prototype.serverQuery = function (document) {\n    return removeClientSetsFromDocument(document);\n  };\n  LocalState.prototype.prepareContext = function (context) {\n    var cache = this.cache;\n    return __assign(__assign({}, context), {\n      cache: cache,\n      // Getting an entry's cache key is useful for local state resolvers.\n      getCacheKey: function (obj) {\n        return cache.identify(obj);\n      }\n    });\n  };\n  // To support `@client @export(as: \"someVar\")` syntax, we'll first resolve\n  // @client @export fields locally, then pass the resolved values back to be\n  // used alongside the original operation variables.\n  LocalState.prototype.addExportedVariables = function (document_1) {\n    return __awaiter(this, arguments, void 0, function (document, variables, context) {\n      if (variables === void 0) {\n        variables = {};\n      }\n      if (context === void 0) {\n        context = {};\n      }\n      return __generator(this, function (_a) {\n        if (document) {\n          return [2 /*return*/, this.resolveDocument(document, this.buildRootValueFromCache(document, variables) || {}, this.prepareContext(context), variables).then(function (data) {\n            return __assign(__assign({}, variables), data.exportedVariables);\n          })];\n        }\n        return [2 /*return*/, __assign({}, variables)];\n      });\n    });\n  };\n  LocalState.prototype.shouldForceResolvers = function (document) {\n    var forceResolvers = false;\n    visit(document, {\n      Directive: {\n        enter: function (node) {\n          if (node.name.value === \"client\" && node.arguments) {\n            forceResolvers = node.arguments.some(function (arg) {\n              return arg.name.value === \"always\" && arg.value.kind === \"BooleanValue\" && arg.value.value === true;\n            });\n            if (forceResolvers) {\n              return BREAK;\n            }\n          }\n        }\n      }\n    });\n    return forceResolvers;\n  };\n  // Query the cache and return matching data.\n  LocalState.prototype.buildRootValueFromCache = function (document, variables) {\n    return this.cache.diff({\n      query: buildQueryFromSelectionSet(document),\n      variables: variables,\n      returnPartialData: true,\n      optimistic: false\n    }).result;\n  };\n  LocalState.prototype.resolveDocument = function (document_1, rootValue_1) {\n    return __awaiter(this, arguments, void 0, function (document, rootValue, context, variables, fragmentMatcher, onlyRunForcedResolvers) {\n      var mainDefinition, fragments, fragmentMap, selectionsToResolve, definitionOperation, defaultOperationType, _a, cache, client, execContext, isClientFieldDescendant;\n      if (context === void 0) {\n        context = {};\n      }\n      if (variables === void 0) {\n        variables = {};\n      }\n      if (fragmentMatcher === void 0) {\n        fragmentMatcher = function () {\n          return true;\n        };\n      }\n      if (onlyRunForcedResolvers === void 0) {\n        onlyRunForcedResolvers = false;\n      }\n      return __generator(this, function (_b) {\n        mainDefinition = getMainDefinition(document);\n        fragments = getFragmentDefinitions(document);\n        fragmentMap = createFragmentMap(fragments);\n        selectionsToResolve = this.collectSelectionsToResolve(mainDefinition, fragmentMap);\n        definitionOperation = mainDefinition.operation;\n        defaultOperationType = definitionOperation ? definitionOperation.charAt(0).toUpperCase() + definitionOperation.slice(1) : \"Query\";\n        _a = this, cache = _a.cache, client = _a.client;\n        execContext = {\n          fragmentMap: fragmentMap,\n          context: __assign(__assign({}, context), {\n            cache: cache,\n            client: client\n          }),\n          variables: variables,\n          fragmentMatcher: fragmentMatcher,\n          defaultOperationType: defaultOperationType,\n          exportedVariables: {},\n          selectionsToResolve: selectionsToResolve,\n          onlyRunForcedResolvers: onlyRunForcedResolvers\n        };\n        isClientFieldDescendant = false;\n        return [2 /*return*/, this.resolveSelectionSet(mainDefinition.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (result) {\n          return {\n            result: result,\n            exportedVariables: execContext.exportedVariables\n          };\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveSelectionSet = function (selectionSet, isClientFieldDescendant, rootValue, execContext) {\n    return __awaiter(this, void 0, void 0, function () {\n      var fragmentMap, context, variables, resultsToMerge, execute;\n      var _this = this;\n      return __generator(this, function (_a) {\n        fragmentMap = execContext.fragmentMap, context = execContext.context, variables = execContext.variables;\n        resultsToMerge = [rootValue];\n        execute = function (selection) {\n          return __awaiter(_this, void 0, void 0, function () {\n            var fragment, typeCondition;\n            return __generator(this, function (_a) {\n              if (!isClientFieldDescendant && !execContext.selectionsToResolve.has(selection)) {\n                // Skip selections without @client directives\n                // (still processing if one of the ancestors or one of the child fields has @client directive)\n                return [2 /*return*/];\n              }\n\n              if (!shouldInclude(selection, variables)) {\n                // Skip this entirely.\n                return [2 /*return*/];\n              }\n\n              if (isField(selection)) {\n                return [2 /*return*/, this.resolveField(selection, isClientFieldDescendant, rootValue, execContext).then(function (fieldResult) {\n                  var _a;\n                  if (typeof fieldResult !== \"undefined\") {\n                    resultsToMerge.push((_a = {}, _a[resultKeyNameFromField(selection)] = fieldResult, _a));\n                  }\n                })];\n              }\n              if (isInlineFragment(selection)) {\n                fragment = selection;\n              } else {\n                // This is a named fragment.\n                fragment = fragmentMap[selection.name.value];\n                invariant(fragment, 19, selection.name.value);\n              }\n              if (fragment && fragment.typeCondition) {\n                typeCondition = fragment.typeCondition.name.value;\n                if (execContext.fragmentMatcher(rootValue, typeCondition, context)) {\n                  return [2 /*return*/, this.resolveSelectionSet(fragment.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (fragmentResult) {\n                    resultsToMerge.push(fragmentResult);\n                  })];\n                }\n              }\n              return [2 /*return*/];\n            });\n          });\n        };\n\n        return [2 /*return*/, Promise.all(selectionSet.selections.map(execute)).then(function () {\n          return mergeDeepArray(resultsToMerge);\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveField = function (field, isClientFieldDescendant, rootValue, execContext) {\n    return __awaiter(this, void 0, void 0, function () {\n      var variables, fieldName, aliasedFieldName, aliasUsed, defaultResult, resultPromise, resolverType, resolverMap, resolve;\n      var _this = this;\n      return __generator(this, function (_a) {\n        if (!rootValue) {\n          return [2 /*return*/, null];\n        }\n        variables = execContext.variables;\n        fieldName = field.name.value;\n        aliasedFieldName = resultKeyNameFromField(field);\n        aliasUsed = fieldName !== aliasedFieldName;\n        defaultResult = rootValue[aliasedFieldName] || rootValue[fieldName];\n        resultPromise = Promise.resolve(defaultResult);\n        // Usually all local resolvers are run when passing through here, but\n        // if we've specifically identified that we only want to run forced\n        // resolvers (that is, resolvers for fields marked with\n        // `@client(always: true)`), then we'll skip running non-forced resolvers.\n        if (!execContext.onlyRunForcedResolvers || this.shouldForceResolvers(field)) {\n          resolverType = rootValue.__typename || execContext.defaultOperationType;\n          resolverMap = this.resolvers && this.resolvers[resolverType];\n          if (resolverMap) {\n            resolve = resolverMap[aliasUsed ? fieldName : aliasedFieldName];\n            if (resolve) {\n              resultPromise = Promise.resolve(\n              // In case the resolve function accesses reactive variables,\n              // set cacheSlot to the current cache instance.\n              cacheSlot.withValue(this.cache, resolve, [rootValue, argumentsObjectFromField(field, variables), execContext.context, {\n                field: field,\n                fragmentMap: execContext.fragmentMap\n              }]));\n            }\n          }\n        }\n        return [2 /*return*/, resultPromise.then(function (result) {\n          var _a, _b;\n          if (result === void 0) {\n            result = defaultResult;\n          }\n          // If an @export directive is associated with the current field, store\n          // the `as` export variable name and current result for later use.\n          if (field.directives) {\n            field.directives.forEach(function (directive) {\n              if (directive.name.value === \"export\" && directive.arguments) {\n                directive.arguments.forEach(function (arg) {\n                  if (arg.name.value === \"as\" && arg.value.kind === \"StringValue\") {\n                    execContext.exportedVariables[arg.value.value] = result;\n                  }\n                });\n              }\n            });\n          }\n          // Handle all scalar types here.\n          if (!field.selectionSet) {\n            return result;\n          }\n          // From here down, the field has a selection set, which means it's trying\n          // to query a GraphQLObjectType.\n          if (result == null) {\n            // Basically any field in a GraphQL response can be null, or missing\n            return result;\n          }\n          var isClientField = (_b = (_a = field.directives) === null || _a === void 0 ? void 0 : _a.some(function (d) {\n            return d.name.value === \"client\";\n          })) !== null && _b !== void 0 ? _b : false;\n          if (Array.isArray(result)) {\n            return _this.resolveSubSelectedArray(field, isClientFieldDescendant || isClientField, result, execContext);\n          }\n          // Returned value is an object, and the query has a sub-selection. Recurse.\n          if (field.selectionSet) {\n            return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant || isClientField, result, execContext);\n          }\n        })];\n      });\n    });\n  };\n  LocalState.prototype.resolveSubSelectedArray = function (field, isClientFieldDescendant, result, execContext) {\n    var _this = this;\n    return Promise.all(result.map(function (item) {\n      if (item === null) {\n        return null;\n      }\n      // This is a nested array, recurse.\n      if (Array.isArray(item)) {\n        return _this.resolveSubSelectedArray(field, isClientFieldDescendant, item, execContext);\n      }\n      // This is an object, run the selection set on it.\n      if (field.selectionSet) {\n        return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant, item, execContext);\n      }\n    }));\n  };\n  // Collect selection nodes on paths from document root down to all @client directives.\n  // This function takes into account transitive fragment spreads.\n  // Complexity equals to a single `visit` over the full document.\n  LocalState.prototype.collectSelectionsToResolve = function (mainDefinition, fragmentMap) {\n    var isSingleASTNode = function (node) {\n      return !Array.isArray(node);\n    };\n    var selectionsToResolveCache = this.selectionsToResolveCache;\n    function collectByDefinition(definitionNode) {\n      if (!selectionsToResolveCache.has(definitionNode)) {\n        var matches_1 = new Set();\n        selectionsToResolveCache.set(definitionNode, matches_1);\n        visit(definitionNode, {\n          Directive: function (node, _, __, ___, ancestors) {\n            if (node.name.value === \"client\") {\n              ancestors.forEach(function (node) {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches_1.add(node);\n                }\n              });\n            }\n          },\n          FragmentSpread: function (spread, _, __, ___, ancestors) {\n            var fragment = fragmentMap[spread.name.value];\n            invariant(fragment, 20, spread.name.value);\n            var fragmentSelections = collectByDefinition(fragment);\n            if (fragmentSelections.size > 0) {\n              // Fragment for this spread contains @client directive (either directly or transitively)\n              // Collect selection nodes on paths from the root down to fields with the @client directive\n              ancestors.forEach(function (node) {\n                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                  matches_1.add(node);\n                }\n              });\n              matches_1.add(spread);\n              fragmentSelections.forEach(function (selection) {\n                matches_1.add(selection);\n              });\n            }\n          }\n        });\n      }\n      return selectionsToResolveCache.get(definitionNode);\n    }\n    return collectByDefinition(mainDefinition);\n  };\n  return LocalState;\n}();\nexport { LocalState };", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "invariant", "visit", "BREAK", "isSelectionNode", "argumentsObjectFromField", "buildQueryFromSelectionSet", "createFragmentMap", "getFragmentDefinitions", "getMainDefinition", "hasDirectives", "isField", "isInlineFragment", "mergeDeep", "mergeDeepArray", "removeClientSetsFromDocument", "resultKeyNameFromField", "shouldInclude", "cacheSlot", "LocalState", "_a", "cache", "client", "resolvers", "fragmentMatcher", "selectionsToResolveCache", "WeakMap", "addResolvers", "setFragmentMatcher", "prototype", "_this", "Array", "isArray", "for<PERSON>ach", "resolverGroup", "setResolvers", "getResolvers", "runResolvers", "arguments", "_b", "document", "remoteResult", "context", "variables", "_c", "onlyRunForcedResolvers", "_d", "resolveDocument", "data", "then", "localResult", "result", "getFragmentMatcher", "clientQuery", "serverQuery", "prepareContext", "get<PERSON><PERSON><PERSON><PERSON>", "obj", "identify", "addExportedVariables", "document_1", "buildRootValueFromCache", "exportedVariables", "shouldForceResolvers", "forceResolvers", "Directive", "enter", "node", "name", "value", "some", "arg", "kind", "diff", "query", "returnPartialData", "optimistic", "rootValue_1", "rootValue", "mainDefinition", "fragments", "fragmentMap", "selectionsToResolve", "definitionOperation", "defaultOperationType", "execContext", "isClientFieldDescendant", "collectSelectionsToResolve", "operation", "char<PERSON>t", "toUpperCase", "slice", "resolveSelectionSet", "selectionSet", "resultsToMerge", "execute", "selection", "fragment", "typeCondition", "has", "resolveField", "fieldResult", "push", "fragmentResult", "Promise", "all", "selections", "map", "field", "fieldName", "alias<PERSON><PERSON><PERSON><PERSON><PERSON>", "aliasUsed", "defaultResult", "resultPromise", "resolverType", "resolverMap", "resolve", "__typename", "with<PERSON><PERSON><PERSON>", "directives", "directive", "isClientField", "d", "resolveSubSelectedArray", "item", "isSingleASTNode", "collectByDefinition", "definitionNode", "matches_1", "Set", "set", "_", "__", "___", "ancestors", "add", "FragmentSpread", "spread", "fragmentSelections", "size", "get"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/LocalState.js"], "sourcesContent": ["import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { visit, BREAK, isSelectionNode } from \"graphql\";\nimport { argumentsObjectFromField, buildQueryFromSelectionSet, createFragmentMap, getFragmentDefinitions, getMainDefinition, hasDirectives, isField, isInlineFragment, mergeDeep, mergeDeepArray, removeClientSetsFromDocument, resultKeyNameFromField, shouldInclude, } from \"../utilities/index.js\";\nimport { cacheSlot } from \"../cache/index.js\";\nvar LocalState = /** @class */ (function () {\n    function LocalState(_a) {\n        var cache = _a.cache, client = _a.client, resolvers = _a.resolvers, fragmentMatcher = _a.fragmentMatcher;\n        this.selectionsToResolveCache = new WeakMap();\n        this.cache = cache;\n        if (client) {\n            this.client = client;\n        }\n        if (resolvers) {\n            this.addResolvers(resolvers);\n        }\n        if (fragmentMatcher) {\n            this.setFragmentMatcher(fragmentMatcher);\n        }\n    }\n    LocalState.prototype.addResolvers = function (resolvers) {\n        var _this = this;\n        this.resolvers = this.resolvers || {};\n        if (Array.isArray(resolvers)) {\n            resolvers.forEach(function (resolverGroup) {\n                _this.resolvers = mergeDeep(_this.resolvers, resolverGroup);\n            });\n        }\n        else {\n            this.resolvers = mergeDeep(this.resolvers, resolvers);\n        }\n    };\n    LocalState.prototype.setResolvers = function (resolvers) {\n        this.resolvers = {};\n        this.addResolvers(resolvers);\n    };\n    LocalState.prototype.getResolvers = function () {\n        return this.resolvers || {};\n    };\n    // Run local client resolvers against the incoming query and remote data.\n    // Locally resolved field values are merged with the incoming remote data,\n    // and returned. Note that locally resolved fields will overwrite\n    // remote data using the same field name.\n    LocalState.prototype.runResolvers = function (_a) {\n        return __awaiter(this, arguments, void 0, function (_b) {\n            var document = _b.document, remoteResult = _b.remoteResult, context = _b.context, variables = _b.variables, _c = _b.onlyRunForcedResolvers, onlyRunForcedResolvers = _c === void 0 ? false : _c;\n            return __generator(this, function (_d) {\n                if (document) {\n                    return [2 /*return*/, this.resolveDocument(document, remoteResult.data, context, variables, this.fragmentMatcher, onlyRunForcedResolvers).then(function (localResult) { return (__assign(__assign({}, remoteResult), { data: localResult.result })); })];\n                }\n                return [2 /*return*/, remoteResult];\n            });\n        });\n    };\n    LocalState.prototype.setFragmentMatcher = function (fragmentMatcher) {\n        this.fragmentMatcher = fragmentMatcher;\n    };\n    LocalState.prototype.getFragmentMatcher = function () {\n        return this.fragmentMatcher;\n    };\n    // Client queries contain everything in the incoming document (if a @client\n    // directive is found).\n    LocalState.prototype.clientQuery = function (document) {\n        if (hasDirectives([\"client\"], document)) {\n            if (this.resolvers) {\n                return document;\n            }\n        }\n        return null;\n    };\n    // Server queries are stripped of all @client based selection sets.\n    LocalState.prototype.serverQuery = function (document) {\n        return removeClientSetsFromDocument(document);\n    };\n    LocalState.prototype.prepareContext = function (context) {\n        var cache = this.cache;\n        return __assign(__assign({}, context), { cache: cache, \n            // Getting an entry's cache key is useful for local state resolvers.\n            getCacheKey: function (obj) {\n                return cache.identify(obj);\n            } });\n    };\n    // To support `@client @export(as: \"someVar\")` syntax, we'll first resolve\n    // @client @export fields locally, then pass the resolved values back to be\n    // used alongside the original operation variables.\n    LocalState.prototype.addExportedVariables = function (document_1) {\n        return __awaiter(this, arguments, void 0, function (document, variables, context) {\n            if (variables === void 0) { variables = {}; }\n            if (context === void 0) { context = {}; }\n            return __generator(this, function (_a) {\n                if (document) {\n                    return [2 /*return*/, this.resolveDocument(document, this.buildRootValueFromCache(document, variables) || {}, this.prepareContext(context), variables).then(function (data) { return (__assign(__assign({}, variables), data.exportedVariables)); })];\n                }\n                return [2 /*return*/, __assign({}, variables)];\n            });\n        });\n    };\n    LocalState.prototype.shouldForceResolvers = function (document) {\n        var forceResolvers = false;\n        visit(document, {\n            Directive: {\n                enter: function (node) {\n                    if (node.name.value === \"client\" && node.arguments) {\n                        forceResolvers = node.arguments.some(function (arg) {\n                            return arg.name.value === \"always\" &&\n                                arg.value.kind === \"BooleanValue\" &&\n                                arg.value.value === true;\n                        });\n                        if (forceResolvers) {\n                            return BREAK;\n                        }\n                    }\n                },\n            },\n        });\n        return forceResolvers;\n    };\n    // Query the cache and return matching data.\n    LocalState.prototype.buildRootValueFromCache = function (document, variables) {\n        return this.cache.diff({\n            query: buildQueryFromSelectionSet(document),\n            variables: variables,\n            returnPartialData: true,\n            optimistic: false,\n        }).result;\n    };\n    LocalState.prototype.resolveDocument = function (document_1, rootValue_1) {\n        return __awaiter(this, arguments, void 0, function (document, rootValue, context, variables, fragmentMatcher, onlyRunForcedResolvers) {\n            var mainDefinition, fragments, fragmentMap, selectionsToResolve, definitionOperation, defaultOperationType, _a, cache, client, execContext, isClientFieldDescendant;\n            if (context === void 0) { context = {}; }\n            if (variables === void 0) { variables = {}; }\n            if (fragmentMatcher === void 0) { fragmentMatcher = function () { return true; }; }\n            if (onlyRunForcedResolvers === void 0) { onlyRunForcedResolvers = false; }\n            return __generator(this, function (_b) {\n                mainDefinition = getMainDefinition(document);\n                fragments = getFragmentDefinitions(document);\n                fragmentMap = createFragmentMap(fragments);\n                selectionsToResolve = this.collectSelectionsToResolve(mainDefinition, fragmentMap);\n                definitionOperation = mainDefinition.operation;\n                defaultOperationType = definitionOperation ?\n                    definitionOperation.charAt(0).toUpperCase() +\n                        definitionOperation.slice(1)\n                    : \"Query\";\n                _a = this, cache = _a.cache, client = _a.client;\n                execContext = {\n                    fragmentMap: fragmentMap,\n                    context: __assign(__assign({}, context), { cache: cache, client: client }),\n                    variables: variables,\n                    fragmentMatcher: fragmentMatcher,\n                    defaultOperationType: defaultOperationType,\n                    exportedVariables: {},\n                    selectionsToResolve: selectionsToResolve,\n                    onlyRunForcedResolvers: onlyRunForcedResolvers,\n                };\n                isClientFieldDescendant = false;\n                return [2 /*return*/, this.resolveSelectionSet(mainDefinition.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (result) { return ({\n                        result: result,\n                        exportedVariables: execContext.exportedVariables,\n                    }); })];\n            });\n        });\n    };\n    LocalState.prototype.resolveSelectionSet = function (selectionSet, isClientFieldDescendant, rootValue, execContext) {\n        return __awaiter(this, void 0, void 0, function () {\n            var fragmentMap, context, variables, resultsToMerge, execute;\n            var _this = this;\n            return __generator(this, function (_a) {\n                fragmentMap = execContext.fragmentMap, context = execContext.context, variables = execContext.variables;\n                resultsToMerge = [rootValue];\n                execute = function (selection) { return __awaiter(_this, void 0, void 0, function () {\n                    var fragment, typeCondition;\n                    return __generator(this, function (_a) {\n                        if (!isClientFieldDescendant &&\n                            !execContext.selectionsToResolve.has(selection)) {\n                            // Skip selections without @client directives\n                            // (still processing if one of the ancestors or one of the child fields has @client directive)\n                            return [2 /*return*/];\n                        }\n                        if (!shouldInclude(selection, variables)) {\n                            // Skip this entirely.\n                            return [2 /*return*/];\n                        }\n                        if (isField(selection)) {\n                            return [2 /*return*/, this.resolveField(selection, isClientFieldDescendant, rootValue, execContext).then(function (fieldResult) {\n                                    var _a;\n                                    if (typeof fieldResult !== \"undefined\") {\n                                        resultsToMerge.push((_a = {},\n                                            _a[resultKeyNameFromField(selection)] = fieldResult,\n                                            _a));\n                                    }\n                                })];\n                        }\n                        if (isInlineFragment(selection)) {\n                            fragment = selection;\n                        }\n                        else {\n                            // This is a named fragment.\n                            fragment = fragmentMap[selection.name.value];\n                            invariant(fragment, 19, selection.name.value);\n                        }\n                        if (fragment && fragment.typeCondition) {\n                            typeCondition = fragment.typeCondition.name.value;\n                            if (execContext.fragmentMatcher(rootValue, typeCondition, context)) {\n                                return [2 /*return*/, this.resolveSelectionSet(fragment.selectionSet, isClientFieldDescendant, rootValue, execContext).then(function (fragmentResult) {\n                                        resultsToMerge.push(fragmentResult);\n                                    })];\n                            }\n                        }\n                        return [2 /*return*/];\n                    });\n                }); };\n                return [2 /*return*/, Promise.all(selectionSet.selections.map(execute)).then(function () {\n                        return mergeDeepArray(resultsToMerge);\n                    })];\n            });\n        });\n    };\n    LocalState.prototype.resolveField = function (field, isClientFieldDescendant, rootValue, execContext) {\n        return __awaiter(this, void 0, void 0, function () {\n            var variables, fieldName, aliasedFieldName, aliasUsed, defaultResult, resultPromise, resolverType, resolverMap, resolve;\n            var _this = this;\n            return __generator(this, function (_a) {\n                if (!rootValue) {\n                    return [2 /*return*/, null];\n                }\n                variables = execContext.variables;\n                fieldName = field.name.value;\n                aliasedFieldName = resultKeyNameFromField(field);\n                aliasUsed = fieldName !== aliasedFieldName;\n                defaultResult = rootValue[aliasedFieldName] || rootValue[fieldName];\n                resultPromise = Promise.resolve(defaultResult);\n                // Usually all local resolvers are run when passing through here, but\n                // if we've specifically identified that we only want to run forced\n                // resolvers (that is, resolvers for fields marked with\n                // `@client(always: true)`), then we'll skip running non-forced resolvers.\n                if (!execContext.onlyRunForcedResolvers ||\n                    this.shouldForceResolvers(field)) {\n                    resolverType = rootValue.__typename || execContext.defaultOperationType;\n                    resolverMap = this.resolvers && this.resolvers[resolverType];\n                    if (resolverMap) {\n                        resolve = resolverMap[aliasUsed ? fieldName : aliasedFieldName];\n                        if (resolve) {\n                            resultPromise = Promise.resolve(\n                            // In case the resolve function accesses reactive variables,\n                            // set cacheSlot to the current cache instance.\n                            cacheSlot.withValue(this.cache, resolve, [\n                                rootValue,\n                                argumentsObjectFromField(field, variables),\n                                execContext.context,\n                                { field: field, fragmentMap: execContext.fragmentMap },\n                            ]));\n                        }\n                    }\n                }\n                return [2 /*return*/, resultPromise.then(function (result) {\n                        var _a, _b;\n                        if (result === void 0) { result = defaultResult; }\n                        // If an @export directive is associated with the current field, store\n                        // the `as` export variable name and current result for later use.\n                        if (field.directives) {\n                            field.directives.forEach(function (directive) {\n                                if (directive.name.value === \"export\" && directive.arguments) {\n                                    directive.arguments.forEach(function (arg) {\n                                        if (arg.name.value === \"as\" && arg.value.kind === \"StringValue\") {\n                                            execContext.exportedVariables[arg.value.value] = result;\n                                        }\n                                    });\n                                }\n                            });\n                        }\n                        // Handle all scalar types here.\n                        if (!field.selectionSet) {\n                            return result;\n                        }\n                        // From here down, the field has a selection set, which means it's trying\n                        // to query a GraphQLObjectType.\n                        if (result == null) {\n                            // Basically any field in a GraphQL response can be null, or missing\n                            return result;\n                        }\n                        var isClientField = (_b = (_a = field.directives) === null || _a === void 0 ? void 0 : _a.some(function (d) { return d.name.value === \"client\"; })) !== null && _b !== void 0 ? _b : false;\n                        if (Array.isArray(result)) {\n                            return _this.resolveSubSelectedArray(field, isClientFieldDescendant || isClientField, result, execContext);\n                        }\n                        // Returned value is an object, and the query has a sub-selection. Recurse.\n                        if (field.selectionSet) {\n                            return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant || isClientField, result, execContext);\n                        }\n                    })];\n            });\n        });\n    };\n    LocalState.prototype.resolveSubSelectedArray = function (field, isClientFieldDescendant, result, execContext) {\n        var _this = this;\n        return Promise.all(result.map(function (item) {\n            if (item === null) {\n                return null;\n            }\n            // This is a nested array, recurse.\n            if (Array.isArray(item)) {\n                return _this.resolveSubSelectedArray(field, isClientFieldDescendant, item, execContext);\n            }\n            // This is an object, run the selection set on it.\n            if (field.selectionSet) {\n                return _this.resolveSelectionSet(field.selectionSet, isClientFieldDescendant, item, execContext);\n            }\n        }));\n    };\n    // Collect selection nodes on paths from document root down to all @client directives.\n    // This function takes into account transitive fragment spreads.\n    // Complexity equals to a single `visit` over the full document.\n    LocalState.prototype.collectSelectionsToResolve = function (mainDefinition, fragmentMap) {\n        var isSingleASTNode = function (node) { return !Array.isArray(node); };\n        var selectionsToResolveCache = this.selectionsToResolveCache;\n        function collectByDefinition(definitionNode) {\n            if (!selectionsToResolveCache.has(definitionNode)) {\n                var matches_1 = new Set();\n                selectionsToResolveCache.set(definitionNode, matches_1);\n                visit(definitionNode, {\n                    Directive: function (node, _, __, ___, ancestors) {\n                        if (node.name.value === \"client\") {\n                            ancestors.forEach(function (node) {\n                                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                                    matches_1.add(node);\n                                }\n                            });\n                        }\n                    },\n                    FragmentSpread: function (spread, _, __, ___, ancestors) {\n                        var fragment = fragmentMap[spread.name.value];\n                        invariant(fragment, 20, spread.name.value);\n                        var fragmentSelections = collectByDefinition(fragment);\n                        if (fragmentSelections.size > 0) {\n                            // Fragment for this spread contains @client directive (either directly or transitively)\n                            // Collect selection nodes on paths from the root down to fields with the @client directive\n                            ancestors.forEach(function (node) {\n                                if (isSingleASTNode(node) && isSelectionNode(node)) {\n                                    matches_1.add(node);\n                                }\n                            });\n                            matches_1.add(spread);\n                            fragmentSelections.forEach(function (selection) {\n                                matches_1.add(selection);\n                            });\n                        }\n                    },\n                });\n            }\n            return selectionsToResolveCache.get(definitionNode);\n        }\n        return collectByDefinition(mainDefinition);\n    };\n    return LocalState;\n}());\nexport { LocalState };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,KAAK,EAAEC,KAAK,EAAEC,eAAe,QAAQ,SAAS;AACvD,SAASC,wBAAwB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,sBAAsB,EAAEC,aAAa,QAAS,uBAAuB;AACrS,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,EAAE,EAAE;IACpB,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;MAAEC,MAAM,GAAGF,EAAE,CAACE,MAAM;MAAEC,SAAS,GAAGH,EAAE,CAACG,SAAS;MAAEC,eAAe,GAAGJ,EAAE,CAACI,eAAe;IACxG,IAAI,CAACC,wBAAwB,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7C,IAAI,CAACL,KAAK,GAAGA,KAAK;IAClB,IAAIC,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;IACA,IAAIC,SAAS,EAAE;MACX,IAAI,CAACI,YAAY,CAACJ,SAAS,CAAC;IAChC;IACA,IAAIC,eAAe,EAAE;MACjB,IAAI,CAACI,kBAAkB,CAACJ,eAAe,CAAC;IAC5C;EACJ;EACAL,UAAU,CAACU,SAAS,CAACF,YAAY,GAAG,UAAUJ,SAAS,EAAE;IACrD,IAAIO,KAAK,GAAG,IAAI;IAChB,IAAI,CAACP,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,CAAC,CAAC;IACrC,IAAIQ,KAAK,CAACC,OAAO,CAACT,SAAS,CAAC,EAAE;MAC1BA,SAAS,CAACU,OAAO,CAAC,UAAUC,aAAa,EAAE;QACvCJ,KAAK,CAACP,SAAS,GAAGV,SAAS,CAACiB,KAAK,CAACP,SAAS,EAAEW,aAAa,CAAC;MAC/D,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACX,SAAS,GAAGV,SAAS,CAAC,IAAI,CAACU,SAAS,EAAEA,SAAS,CAAC;IACzD;EACJ,CAAC;EACDJ,UAAU,CAACU,SAAS,CAACM,YAAY,GAAG,UAAUZ,SAAS,EAAE;IACrD,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACI,YAAY,CAACJ,SAAS,CAAC;EAChC,CAAC;EACDJ,UAAU,CAACU,SAAS,CAACO,YAAY,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACb,SAAS,IAAI,CAAC,CAAC;EAC/B,CAAC;EACD;EACA;EACA;EACA;EACAJ,UAAU,CAACU,SAAS,CAACQ,YAAY,GAAG,UAAUjB,EAAE,EAAE;IAC9C,OAAOrB,SAAS,CAAC,IAAI,EAAEuC,SAAS,EAAE,KAAK,CAAC,EAAE,UAAUC,EAAE,EAAE;MACpD,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;QAAEC,YAAY,GAAGF,EAAE,CAACE,YAAY;QAAEC,OAAO,GAAGH,EAAE,CAACG,OAAO;QAAEC,SAAS,GAAGJ,EAAE,CAACI,SAAS;QAAEC,EAAE,GAAGL,EAAE,CAACM,sBAAsB;QAAEA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAC/L,OAAO5C,WAAW,CAAC,IAAI,EAAE,UAAU8C,EAAE,EAAE;QACnC,IAAIN,QAAQ,EAAE;UACV,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACO,eAAe,CAACP,QAAQ,EAAEC,YAAY,CAACO,IAAI,EAAEN,OAAO,EAAEC,SAAS,EAAE,IAAI,CAACnB,eAAe,EAAEqB,sBAAsB,CAAC,CAACI,IAAI,CAAC,UAAUC,WAAW,EAAE;YAAE,OAAQpD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,CAAC,EAAE;cAAEO,IAAI,EAAEE,WAAW,CAACC;YAAO,CAAC,CAAC;UAAG,CAAC,CAAC,CAAC;QAC5P;QACA,OAAO,CAAC,CAAC,CAAC,YAAYV,YAAY,CAAC;MACvC,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACDtB,UAAU,CAACU,SAAS,CAACD,kBAAkB,GAAG,UAAUJ,eAAe,EAAE;IACjE,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C,CAAC;EACDL,UAAU,CAACU,SAAS,CAACuB,kBAAkB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAC5B,eAAe;EAC/B,CAAC;EACD;EACA;EACAL,UAAU,CAACU,SAAS,CAACwB,WAAW,GAAG,UAAUb,QAAQ,EAAE;IACnD,IAAI9B,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE8B,QAAQ,CAAC,EAAE;MACrC,IAAI,IAAI,CAACjB,SAAS,EAAE;QAChB,OAAOiB,QAAQ;MACnB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;EACArB,UAAU,CAACU,SAAS,CAACyB,WAAW,GAAG,UAAUd,QAAQ,EAAE;IACnD,OAAOzB,4BAA4B,CAACyB,QAAQ,CAAC;EACjD,CAAC;EACDrB,UAAU,CAACU,SAAS,CAAC0B,cAAc,GAAG,UAAUb,OAAO,EAAE;IACrD,IAAIrB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,OAAOvB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4C,OAAO,CAAC,EAAE;MAAErB,KAAK,EAAEA,KAAK;MACjD;MACAmC,WAAW,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACxB,OAAOpC,KAAK,CAACqC,QAAQ,CAACD,GAAG,CAAC;MAC9B;IAAE,CAAC,CAAC;EACZ,CAAC;EACD;EACA;EACA;EACAtC,UAAU,CAACU,SAAS,CAAC8B,oBAAoB,GAAG,UAAUC,UAAU,EAAE;IAC9D,OAAO7D,SAAS,CAAC,IAAI,EAAEuC,SAAS,EAAE,KAAK,CAAC,EAAE,UAAUE,QAAQ,EAAEG,SAAS,EAAED,OAAO,EAAE;MAC9E,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;QAAEA,SAAS,GAAG,CAAC,CAAC;MAAE;MAC5C,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;QAAEA,OAAO,GAAG,CAAC,CAAC;MAAE;MACxC,OAAO1C,WAAW,CAAC,IAAI,EAAE,UAAUoB,EAAE,EAAE;QACnC,IAAIoB,QAAQ,EAAE;UACV,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACO,eAAe,CAACP,QAAQ,EAAE,IAAI,CAACqB,uBAAuB,CAACrB,QAAQ,EAAEG,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACY,cAAc,CAACb,OAAO,CAAC,EAAEC,SAAS,CAAC,CAACM,IAAI,CAAC,UAAUD,IAAI,EAAE;YAAE,OAAQlD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6C,SAAS,CAAC,EAAEK,IAAI,CAACc,iBAAiB,CAAC;UAAG,CAAC,CAAC,CAAC;QACzP;QACA,OAAO,CAAC,CAAC,CAAC,YAAYhE,QAAQ,CAAC,CAAC,CAAC,EAAE6C,SAAS,CAAC,CAAC;MAClD,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACDxB,UAAU,CAACU,SAAS,CAACkC,oBAAoB,GAAG,UAAUvB,QAAQ,EAAE;IAC5D,IAAIwB,cAAc,GAAG,KAAK;IAC1B9D,KAAK,CAACsC,QAAQ,EAAE;MACZyB,SAAS,EAAE;QACPC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;UACnB,IAAIA,IAAI,CAACC,IAAI,CAACC,KAAK,KAAK,QAAQ,IAAIF,IAAI,CAAC7B,SAAS,EAAE;YAChD0B,cAAc,GAAGG,IAAI,CAAC7B,SAAS,CAACgC,IAAI,CAAC,UAAUC,GAAG,EAAE;cAChD,OAAOA,GAAG,CAACH,IAAI,CAACC,KAAK,KAAK,QAAQ,IAC9BE,GAAG,CAACF,KAAK,CAACG,IAAI,KAAK,cAAc,IACjCD,GAAG,CAACF,KAAK,CAACA,KAAK,KAAK,IAAI;YAChC,CAAC,CAAC;YACF,IAAIL,cAAc,EAAE;cAChB,OAAO7D,KAAK;YAChB;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,OAAO6D,cAAc;EACzB,CAAC;EACD;EACA7C,UAAU,CAACU,SAAS,CAACgC,uBAAuB,GAAG,UAAUrB,QAAQ,EAAEG,SAAS,EAAE;IAC1E,OAAO,IAAI,CAACtB,KAAK,CAACoD,IAAI,CAAC;MACnBC,KAAK,EAAEpE,0BAA0B,CAACkC,QAAQ,CAAC;MAC3CG,SAAS,EAAEA,SAAS;MACpBgC,iBAAiB,EAAE,IAAI;MACvBC,UAAU,EAAE;IAChB,CAAC,CAAC,CAACzB,MAAM;EACb,CAAC;EACDhC,UAAU,CAACU,SAAS,CAACkB,eAAe,GAAG,UAAUa,UAAU,EAAEiB,WAAW,EAAE;IACtE,OAAO9E,SAAS,CAAC,IAAI,EAAEuC,SAAS,EAAE,KAAK,CAAC,EAAE,UAAUE,QAAQ,EAAEsC,SAAS,EAAEpC,OAAO,EAAEC,SAAS,EAAEnB,eAAe,EAAEqB,sBAAsB,EAAE;MAClI,IAAIkC,cAAc,EAAEC,SAAS,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEhE,EAAE,EAAEC,KAAK,EAAEC,MAAM,EAAE+D,WAAW,EAAEC,uBAAuB;MACnK,IAAI5C,OAAO,KAAK,KAAK,CAAC,EAAE;QAAEA,OAAO,GAAG,CAAC,CAAC;MAAE;MACxC,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;QAAEA,SAAS,GAAG,CAAC,CAAC;MAAE;MAC5C,IAAInB,eAAe,KAAK,KAAK,CAAC,EAAE;QAAEA,eAAe,GAAG,SAAAA,CAAA,EAAY;UAAE,OAAO,IAAI;QAAE,CAAC;MAAE;MAClF,IAAIqB,sBAAsB,KAAK,KAAK,CAAC,EAAE;QAAEA,sBAAsB,GAAG,KAAK;MAAE;MACzE,OAAO7C,WAAW,CAAC,IAAI,EAAE,UAAUuC,EAAE,EAAE;QACnCwC,cAAc,GAAGtE,iBAAiB,CAAC+B,QAAQ,CAAC;QAC5CwC,SAAS,GAAGxE,sBAAsB,CAACgC,QAAQ,CAAC;QAC5CyC,WAAW,GAAG1E,iBAAiB,CAACyE,SAAS,CAAC;QAC1CE,mBAAmB,GAAG,IAAI,CAACK,0BAA0B,CAACR,cAAc,EAAEE,WAAW,CAAC;QAClFE,mBAAmB,GAAGJ,cAAc,CAACS,SAAS;QAC9CJ,oBAAoB,GAAGD,mBAAmB,GACtCA,mBAAmB,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACvCP,mBAAmB,CAACQ,KAAK,CAAC,CAAC,CAAC,GAC9B,OAAO;QACbvE,EAAE,GAAG,IAAI,EAAEC,KAAK,GAAGD,EAAE,CAACC,KAAK,EAAEC,MAAM,GAAGF,EAAE,CAACE,MAAM;QAC/C+D,WAAW,GAAG;UACVJ,WAAW,EAAEA,WAAW;UACxBvC,OAAO,EAAE5C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4C,OAAO,CAAC,EAAE;YAAErB,KAAK,EAAEA,KAAK;YAAEC,MAAM,EAAEA;UAAO,CAAC,CAAC;UAC1EqB,SAAS,EAAEA,SAAS;UACpBnB,eAAe,EAAEA,eAAe;UAChC4D,oBAAoB,EAAEA,oBAAoB;UAC1CtB,iBAAiB,EAAE,CAAC,CAAC;UACrBoB,mBAAmB,EAAEA,mBAAmB;UACxCrC,sBAAsB,EAAEA;QAC5B,CAAC;QACDyC,uBAAuB,GAAG,KAAK;QAC/B,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACM,mBAAmB,CAACb,cAAc,CAACc,YAAY,EAAEP,uBAAuB,EAAER,SAAS,EAAEO,WAAW,CAAC,CAACpC,IAAI,CAAC,UAAUE,MAAM,EAAE;UAAE,OAAQ;YACtJA,MAAM,EAAEA,MAAM;YACdW,iBAAiB,EAAEuB,WAAW,CAACvB;UACnC,CAAC;QAAG,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD3C,UAAU,CAACU,SAAS,CAAC+D,mBAAmB,GAAG,UAAUC,YAAY,EAAEP,uBAAuB,EAAER,SAAS,EAAEO,WAAW,EAAE;IAChH,OAAOtF,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAIkF,WAAW,EAAEvC,OAAO,EAAEC,SAAS,EAAEmD,cAAc,EAAEC,OAAO;MAC5D,IAAIjE,KAAK,GAAG,IAAI;MAChB,OAAO9B,WAAW,CAAC,IAAI,EAAE,UAAUoB,EAAE,EAAE;QACnC6D,WAAW,GAAGI,WAAW,CAACJ,WAAW,EAAEvC,OAAO,GAAG2C,WAAW,CAAC3C,OAAO,EAAEC,SAAS,GAAG0C,WAAW,CAAC1C,SAAS;QACvGmD,cAAc,GAAG,CAAChB,SAAS,CAAC;QAC5BiB,OAAO,GAAG,SAAAA,CAAUC,SAAS,EAAE;UAAE,OAAOjG,SAAS,CAAC+B,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;YACjF,IAAImE,QAAQ,EAAEC,aAAa;YAC3B,OAAOlG,WAAW,CAAC,IAAI,EAAE,UAAUoB,EAAE,EAAE;cACnC,IAAI,CAACkE,uBAAuB,IACxB,CAACD,WAAW,CAACH,mBAAmB,CAACiB,GAAG,CAACH,SAAS,CAAC,EAAE;gBACjD;gBACA;gBACA,OAAO,CAAC,CAAC,CAAC,WAAW;cACzB;;cACA,IAAI,CAAC/E,aAAa,CAAC+E,SAAS,EAAErD,SAAS,CAAC,EAAE;gBACtC;gBACA,OAAO,CAAC,CAAC,CAAC,WAAW;cACzB;;cACA,IAAIhC,OAAO,CAACqF,SAAS,CAAC,EAAE;gBACpB,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACI,YAAY,CAACJ,SAAS,EAAEV,uBAAuB,EAAER,SAAS,EAAEO,WAAW,CAAC,CAACpC,IAAI,CAAC,UAAUoD,WAAW,EAAE;kBACxH,IAAIjF,EAAE;kBACN,IAAI,OAAOiF,WAAW,KAAK,WAAW,EAAE;oBACpCP,cAAc,CAACQ,IAAI,EAAElF,EAAE,GAAG,CAAC,CAAC,EACxBA,EAAE,CAACJ,sBAAsB,CAACgF,SAAS,CAAC,CAAC,GAAGK,WAAW,EACnDjF,EAAE,CAAC,CAAC;kBACZ;gBACJ,CAAC,CAAC,CAAC;cACX;cACA,IAAIR,gBAAgB,CAACoF,SAAS,CAAC,EAAE;gBAC7BC,QAAQ,GAAGD,SAAS;cACxB,CAAC,MACI;gBACD;gBACAC,QAAQ,GAAGhB,WAAW,CAACe,SAAS,CAAC5B,IAAI,CAACC,KAAK,CAAC;gBAC5CpE,SAAS,CAACgG,QAAQ,EAAE,EAAE,EAAED,SAAS,CAAC5B,IAAI,CAACC,KAAK,CAAC;cACjD;cACA,IAAI4B,QAAQ,IAAIA,QAAQ,CAACC,aAAa,EAAE;gBACpCA,aAAa,GAAGD,QAAQ,CAACC,aAAa,CAAC9B,IAAI,CAACC,KAAK;gBACjD,IAAIgB,WAAW,CAAC7D,eAAe,CAACsD,SAAS,EAAEoB,aAAa,EAAExD,OAAO,CAAC,EAAE;kBAChE,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACkD,mBAAmB,CAACK,QAAQ,CAACJ,YAAY,EAAEP,uBAAuB,EAAER,SAAS,EAAEO,WAAW,CAAC,CAACpC,IAAI,CAAC,UAAUsD,cAAc,EAAE;oBAC9IT,cAAc,CAACQ,IAAI,CAACC,cAAc,CAAC;kBACvC,CAAC,CAAC,CAAC;gBACX;cACJ;cACA,OAAO,CAAC,CAAC,CAAC,WAAW;YACzB,CAAC,CAAC;UACN,CAAC,CAAC;QAAE,CAAC;;QACL,OAAO,CAAC,CAAC,CAAC,YAAYC,OAAO,CAACC,GAAG,CAACZ,YAAY,CAACa,UAAU,CAACC,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC9C,IAAI,CAAC,YAAY;UACjF,OAAOnC,cAAc,CAACgF,cAAc,CAAC;QACzC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD3E,UAAU,CAACU,SAAS,CAACuE,YAAY,GAAG,UAAUQ,KAAK,EAAEtB,uBAAuB,EAAER,SAAS,EAAEO,WAAW,EAAE;IAClG,OAAOtF,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4C,SAAS,EAAEkE,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,EAAEC,OAAO;MACvH,IAAItF,KAAK,GAAG,IAAI;MAChB,OAAO9B,WAAW,CAAC,IAAI,EAAE,UAAUoB,EAAE,EAAE;QACnC,IAAI,CAAC0D,SAAS,EAAE;UACZ,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC;QAC/B;QACAnC,SAAS,GAAG0C,WAAW,CAAC1C,SAAS;QACjCkE,SAAS,GAAGD,KAAK,CAACxC,IAAI,CAACC,KAAK;QAC5ByC,gBAAgB,GAAG9F,sBAAsB,CAAC4F,KAAK,CAAC;QAChDG,SAAS,GAAGF,SAAS,KAAKC,gBAAgB;QAC1CE,aAAa,GAAGlC,SAAS,CAACgC,gBAAgB,CAAC,IAAIhC,SAAS,CAAC+B,SAAS,CAAC;QACnEI,aAAa,GAAGT,OAAO,CAACY,OAAO,CAACJ,aAAa,CAAC;QAC9C;QACA;QACA;QACA;QACA,IAAI,CAAC3B,WAAW,CAACxC,sBAAsB,IACnC,IAAI,CAACkB,oBAAoB,CAAC6C,KAAK,CAAC,EAAE;UAClCM,YAAY,GAAGpC,SAAS,CAACuC,UAAU,IAAIhC,WAAW,CAACD,oBAAoB;UACvE+B,WAAW,GAAG,IAAI,CAAC5F,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC2F,YAAY,CAAC;UAC5D,IAAIC,WAAW,EAAE;YACbC,OAAO,GAAGD,WAAW,CAACJ,SAAS,GAAGF,SAAS,GAAGC,gBAAgB,CAAC;YAC/D,IAAIM,OAAO,EAAE;cACTH,aAAa,GAAGT,OAAO,CAACY,OAAO;cAC/B;cACA;cACAlG,SAAS,CAACoG,SAAS,CAAC,IAAI,CAACjG,KAAK,EAAE+F,OAAO,EAAE,CACrCtC,SAAS,EACTzE,wBAAwB,CAACuG,KAAK,EAAEjE,SAAS,CAAC,EAC1C0C,WAAW,CAAC3C,OAAO,EACnB;gBAAEkE,KAAK,EAAEA,KAAK;gBAAE3B,WAAW,EAAEI,WAAW,CAACJ;cAAY,CAAC,CACzD,CAAC,CAAC;YACP;UACJ;QACJ;QACA,OAAO,CAAC,CAAC,CAAC,YAAYgC,aAAa,CAAChE,IAAI,CAAC,UAAUE,MAAM,EAAE;UACnD,IAAI/B,EAAE,EAAEmB,EAAE;UACV,IAAIY,MAAM,KAAK,KAAK,CAAC,EAAE;YAAEA,MAAM,GAAG6D,aAAa;UAAE;UACjD;UACA;UACA,IAAIJ,KAAK,CAACW,UAAU,EAAE;YAClBX,KAAK,CAACW,UAAU,CAACtF,OAAO,CAAC,UAAUuF,SAAS,EAAE;cAC1C,IAAIA,SAAS,CAACpD,IAAI,CAACC,KAAK,KAAK,QAAQ,IAAImD,SAAS,CAAClF,SAAS,EAAE;gBAC1DkF,SAAS,CAAClF,SAAS,CAACL,OAAO,CAAC,UAAUsC,GAAG,EAAE;kBACvC,IAAIA,GAAG,CAACH,IAAI,CAACC,KAAK,KAAK,IAAI,IAAIE,GAAG,CAACF,KAAK,CAACG,IAAI,KAAK,aAAa,EAAE;oBAC7Da,WAAW,CAACvB,iBAAiB,CAACS,GAAG,CAACF,KAAK,CAACA,KAAK,CAAC,GAAGlB,MAAM;kBAC3D;gBACJ,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN;UACA;UACA,IAAI,CAACyD,KAAK,CAACf,YAAY,EAAE;YACrB,OAAO1C,MAAM;UACjB;UACA;UACA;UACA,IAAIA,MAAM,IAAI,IAAI,EAAE;YAChB;YACA,OAAOA,MAAM;UACjB;UACA,IAAIsE,aAAa,GAAG,CAAClF,EAAE,GAAG,CAACnB,EAAE,GAAGwF,KAAK,CAACW,UAAU,MAAM,IAAI,IAAInG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkD,IAAI,CAAC,UAAUoD,CAAC,EAAE;YAAE,OAAOA,CAAC,CAACtD,IAAI,CAACC,KAAK,KAAK,QAAQ;UAAE,CAAC,CAAC,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;UAC1L,IAAIR,KAAK,CAACC,OAAO,CAACmB,MAAM,CAAC,EAAE;YACvB,OAAOrB,KAAK,CAAC6F,uBAAuB,CAACf,KAAK,EAAEtB,uBAAuB,IAAImC,aAAa,EAAEtE,MAAM,EAAEkC,WAAW,CAAC;UAC9G;UACA;UACA,IAAIuB,KAAK,CAACf,YAAY,EAAE;YACpB,OAAO/D,KAAK,CAAC8D,mBAAmB,CAACgB,KAAK,CAACf,YAAY,EAAEP,uBAAuB,IAAImC,aAAa,EAAEtE,MAAM,EAAEkC,WAAW,CAAC;UACvH;QACJ,CAAC,CAAC,CAAC;MACX,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACDlE,UAAU,CAACU,SAAS,CAAC8F,uBAAuB,GAAG,UAAUf,KAAK,EAAEtB,uBAAuB,EAAEnC,MAAM,EAAEkC,WAAW,EAAE;IAC1G,IAAIvD,KAAK,GAAG,IAAI;IAChB,OAAO0E,OAAO,CAACC,GAAG,CAACtD,MAAM,CAACwD,GAAG,CAAC,UAAUiB,IAAI,EAAE;MAC1C,IAAIA,IAAI,KAAK,IAAI,EAAE;QACf,OAAO,IAAI;MACf;MACA;MACA,IAAI7F,KAAK,CAACC,OAAO,CAAC4F,IAAI,CAAC,EAAE;QACrB,OAAO9F,KAAK,CAAC6F,uBAAuB,CAACf,KAAK,EAAEtB,uBAAuB,EAAEsC,IAAI,EAAEvC,WAAW,CAAC;MAC3F;MACA;MACA,IAAIuB,KAAK,CAACf,YAAY,EAAE;QACpB,OAAO/D,KAAK,CAAC8D,mBAAmB,CAACgB,KAAK,CAACf,YAAY,EAAEP,uBAAuB,EAAEsC,IAAI,EAAEvC,WAAW,CAAC;MACpG;IACJ,CAAC,CAAC,CAAC;EACP,CAAC;EACD;EACA;EACA;EACAlE,UAAU,CAACU,SAAS,CAAC0D,0BAA0B,GAAG,UAAUR,cAAc,EAAEE,WAAW,EAAE;IACrF,IAAI4C,eAAe,GAAG,SAAAA,CAAU1D,IAAI,EAAE;MAAE,OAAO,CAACpC,KAAK,CAACC,OAAO,CAACmC,IAAI,CAAC;IAAE,CAAC;IACtE,IAAI1C,wBAAwB,GAAG,IAAI,CAACA,wBAAwB;IAC5D,SAASqG,mBAAmBA,CAACC,cAAc,EAAE;MACzC,IAAI,CAACtG,wBAAwB,CAAC0E,GAAG,CAAC4B,cAAc,CAAC,EAAE;QAC/C,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;QACzBxG,wBAAwB,CAACyG,GAAG,CAACH,cAAc,EAAEC,SAAS,CAAC;QACvD9H,KAAK,CAAC6H,cAAc,EAAE;UAClB9D,SAAS,EAAE,SAAAA,CAAUE,IAAI,EAAEgE,CAAC,EAAEC,EAAE,EAAEC,GAAG,EAAEC,SAAS,EAAE;YAC9C,IAAInE,IAAI,CAACC,IAAI,CAACC,KAAK,KAAK,QAAQ,EAAE;cAC9BiE,SAAS,CAACrG,OAAO,CAAC,UAAUkC,IAAI,EAAE;gBAC9B,IAAI0D,eAAe,CAAC1D,IAAI,CAAC,IAAI/D,eAAe,CAAC+D,IAAI,CAAC,EAAE;kBAChD6D,SAAS,CAACO,GAAG,CAACpE,IAAI,CAAC;gBACvB;cACJ,CAAC,CAAC;YACN;UACJ,CAAC;UACDqE,cAAc,EAAE,SAAAA,CAAUC,MAAM,EAAEN,CAAC,EAAEC,EAAE,EAAEC,GAAG,EAAEC,SAAS,EAAE;YACrD,IAAIrC,QAAQ,GAAGhB,WAAW,CAACwD,MAAM,CAACrE,IAAI,CAACC,KAAK,CAAC;YAC7CpE,SAAS,CAACgG,QAAQ,EAAE,EAAE,EAAEwC,MAAM,CAACrE,IAAI,CAACC,KAAK,CAAC;YAC1C,IAAIqE,kBAAkB,GAAGZ,mBAAmB,CAAC7B,QAAQ,CAAC;YACtD,IAAIyC,kBAAkB,CAACC,IAAI,GAAG,CAAC,EAAE;cAC7B;cACA;cACAL,SAAS,CAACrG,OAAO,CAAC,UAAUkC,IAAI,EAAE;gBAC9B,IAAI0D,eAAe,CAAC1D,IAAI,CAAC,IAAI/D,eAAe,CAAC+D,IAAI,CAAC,EAAE;kBAChD6D,SAAS,CAACO,GAAG,CAACpE,IAAI,CAAC;gBACvB;cACJ,CAAC,CAAC;cACF6D,SAAS,CAACO,GAAG,CAACE,MAAM,CAAC;cACrBC,kBAAkB,CAACzG,OAAO,CAAC,UAAU+D,SAAS,EAAE;gBAC5CgC,SAAS,CAACO,GAAG,CAACvC,SAAS,CAAC;cAC5B,CAAC,CAAC;YACN;UACJ;QACJ,CAAC,CAAC;MACN;MACA,OAAOvE,wBAAwB,CAACmH,GAAG,CAACb,cAAc,CAAC;IACvD;IACA,OAAOD,mBAAmB,CAAC/C,cAAc,CAAC;EAC9C,CAAC;EACD,OAAO5D,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}