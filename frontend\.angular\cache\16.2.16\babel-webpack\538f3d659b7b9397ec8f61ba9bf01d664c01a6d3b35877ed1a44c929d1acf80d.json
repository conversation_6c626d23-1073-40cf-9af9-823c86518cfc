{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport { handleError, readMultipartBody, parseAndCheckHttpResponse } from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport { selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig } from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport { maybe, getMainDefinition, removeClientSetsFromDocument } from \"../../utilities/index.js\";\nvar backupFetch = maybe(function () {\n  return fetch;\n});\nexport var createHttpLink = function (linkOptions) {\n  if (linkOptions === void 0) {\n    linkOptions = {};\n  }\n  var _a = linkOptions.uri,\n    uri = _a === void 0 ? \"/graphql\" : _a,\n    // use default global fetch if nothing passed in\n    preferredFetch = linkOptions.fetch,\n    _b = linkOptions.print,\n    print = _b === void 0 ? defaultPrinter : _b,\n    includeExtensions = linkOptions.includeExtensions,\n    preserveHeaderCase = linkOptions.preserveHeaderCase,\n    useGETForQueries = linkOptions.useGETForQueries,\n    _c = linkOptions.includeUnusedVariables,\n    includeUnusedVariables = _c === void 0 ? false : _c,\n    requestOptions = __rest(linkOptions, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"useGETForQueries\", \"includeUnusedVariables\"]);\n  if (globalThis.__DEV__ !== false) {\n    // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n    // defined, so requests won't fail at runtime.\n    checkFetcher(preferredFetch || backupFetch);\n  }\n  var linkConfig = {\n    http: {\n      includeExtensions: includeExtensions,\n      preserveHeaderCase: preserveHeaderCase\n    },\n    options: requestOptions.fetchOptions,\n    credentials: requestOptions.credentials,\n    headers: requestOptions.headers\n  };\n  return new ApolloLink(function (operation) {\n    var chosenURI = selectURI(operation, uri);\n    var context = operation.getContext();\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`. If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    var clientAwarenessHeaders = {};\n    if (context.clientAwareness) {\n      var _a = context.clientAwareness,\n        name_1 = _a.name,\n        version = _a.version;\n      if (name_1) {\n        clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n      }\n      if (version) {\n        clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n      }\n    }\n    var contextHeaders = __assign(__assign({}, clientAwarenessHeaders), context.headers);\n    var contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: contextHeaders\n    };\n    if (hasDirectives([\"client\"], operation.query)) {\n      var transformedQuery = removeClientSetsFromDocument(operation.query);\n      if (!transformedQuery) {\n        return fromError(new Error(\"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"));\n      }\n      operation.query = transformedQuery;\n    }\n    //uses fallback, link, and then context to build options\n    var _b = selectHttpOptionsAndBodyInternal(operation, print, fallbackHttpConfig, linkConfig, contextConfig),\n      options = _b.options,\n      body = _b.body;\n    if (body.variables && !includeUnusedVariables) {\n      body.variables = filterOperationVariables(body.variables, operation.query);\n    }\n    var controller;\n    if (!options.signal && typeof AbortController !== \"undefined\") {\n      controller = new AbortController();\n      options.signal = controller.signal;\n    }\n    // If requested, set method to GET if there are no mutations.\n    var definitionIsMutation = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n    };\n    var definitionIsSubscription = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n    };\n    var isSubscription = definitionIsSubscription(getMainDefinition(operation.query));\n    // does not match custom directives beginning with @defer\n    var hasDefer = hasDirectives([\"defer\"], operation.query);\n    if (useGETForQueries && !operation.query.definitions.some(definitionIsMutation)) {\n      options.method = \"GET\";\n    }\n    if (hasDefer || isSubscription) {\n      options.headers = options.headers || {};\n      var acceptHeader = \"multipart/mixed;\";\n      // Omit defer-specific headers if the user attempts to defer a selection\n      // set on a subscription and log a warning.\n      if (isSubscription && hasDefer) {\n        globalThis.__DEV__ !== false && invariant.warn(41);\n      }\n      if (isSubscription) {\n        acceptHeader += \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n      } else if (hasDefer) {\n        acceptHeader += \"deferSpec=20220824,application/json\";\n      }\n      options.headers.accept = acceptHeader;\n    }\n    if (options.method === \"GET\") {\n      var _c = rewriteURIForGET(chosenURI, body),\n        newURI = _c.newURI,\n        parseError = _c.parseError;\n      if (parseError) {\n        return fromError(parseError);\n      }\n      chosenURI = newURI;\n    } else {\n      try {\n        options.body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        return fromError(parseError);\n      }\n    }\n    return new Observable(function (observer) {\n      // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n      // fall back to the *current* global window.fetch function (see issue\n      // #7832), or (if all else fails) the backupFetch function we saved when\n      // this module was first evaluated. This last option protects against the\n      // removal of window.fetch, which is unlikely but not impossible.\n      var currentFetch = preferredFetch || maybe(function () {\n        return fetch;\n      }) || backupFetch;\n      var observerNext = observer.next.bind(observer);\n      currentFetch(chosenURI, options).then(function (response) {\n        var _a;\n        operation.setContext({\n          response: response\n        });\n        var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n        if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n          return readMultipartBody(response, observerNext);\n        } else {\n          return parseAndCheckHttpResponse(operation)(response).then(observerNext);\n        }\n      }).then(function () {\n        controller = undefined;\n        observer.complete();\n      }).catch(function (err) {\n        controller = undefined;\n        handleError(err, observer);\n      });\n      return function () {\n        // XXX support canceling this request\n        // https://developers.google.com/web/updates/2017/09/abortable-fetch\n        if (controller) controller.abort();\n      };\n    });\n  });\n};", "map": {"version": 3, "names": ["__assign", "__rest", "invariant", "ApolloLink", "Observable", "hasDirectives", "serializeFetchParameter", "selectURI", "handleError", "readMultipartBody", "parseAndCheckHttpResponse", "checkFetcher", "selectHttpOptionsAndBodyInternal", "defaultPrinter", "fallbackHttpConfig", "rewriteURIForGET", "fromError", "filterOperationVariables", "maybe", "getMainDefinition", "removeClientSetsFromDocument", "<PERSON><PERSON><PERSON><PERSON>", "fetch", "createHttpLink", "linkOptions", "_a", "uri", "preferredFetch", "_b", "print", "includeExtensions", "preserveHeaderCase", "useGETForQueries", "_c", "includeUnusedVariables", "requestOptions", "globalThis", "__DEV__", "linkConfig", "http", "options", "fetchOptions", "credentials", "headers", "operation", "chosenURI", "context", "getContext", "clientAwarenessHeaders", "clientAwareness", "name_1", "name", "version", "contextHeaders", "contextConfig", "query", "<PERSON><PERSON><PERSON><PERSON>", "Error", "body", "variables", "controller", "signal", "AbortController", "definitionIsMutation", "d", "kind", "definitionIsSubscription", "isSubscription", "<PERSON><PERSON><PERSON><PERSON>", "definitions", "some", "method", "acceptHeader", "warn", "accept", "newURI", "parseError", "observer", "currentFetch", "observerNext", "next", "bind", "then", "response", "setContext", "ctype", "get", "test", "undefined", "complete", "catch", "err", "abort"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/createHttpLink.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport { handleError, readMultipartBody, parseAndCheckHttpResponse, } from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport { selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig, } from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport { maybe, getMainDefinition, removeClientSetsFromDocument, } from \"../../utilities/index.js\";\nvar backupFetch = maybe(function () { return fetch; });\nexport var createHttpLink = function (linkOptions) {\n    if (linkOptions === void 0) { linkOptions = {}; }\n    var _a = linkOptions.uri, uri = _a === void 0 ? \"/graphql\" : _a, \n    // use default global fetch if nothing passed in\n    preferredFetch = linkOptions.fetch, _b = linkOptions.print, print = _b === void 0 ? defaultPrinter : _b, includeExtensions = linkOptions.includeExtensions, preserveHeaderCase = linkOptions.preserveHeaderCase, useGETForQueries = linkOptions.useGETForQueries, _c = linkOptions.includeUnusedVariables, includeUnusedVariables = _c === void 0 ? false : _c, requestOptions = __rest(linkOptions, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"useGETForQueries\", \"includeUnusedVariables\"]);\n    if (globalThis.__DEV__ !== false) {\n        // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n        // defined, so requests won't fail at runtime.\n        checkFetcher(preferredFetch || backupFetch);\n    }\n    var linkConfig = {\n        http: { includeExtensions: includeExtensions, preserveHeaderCase: preserveHeaderCase },\n        options: requestOptions.fetchOptions,\n        credentials: requestOptions.credentials,\n        headers: requestOptions.headers,\n    };\n    return new ApolloLink(function (operation) {\n        var chosenURI = selectURI(operation, uri);\n        var context = operation.getContext();\n        // `apollographql-client-*` headers are automatically set if a\n        // `clientAwareness` object is found in the context. These headers are\n        // set first, followed by the rest of the headers pulled from\n        // `context.headers`. If desired, `apollographql-client-*` headers set by\n        // the `clientAwareness` object can be overridden by\n        // `apollographql-client-*` headers set in `context.headers`.\n        var clientAwarenessHeaders = {};\n        if (context.clientAwareness) {\n            var _a = context.clientAwareness, name_1 = _a.name, version = _a.version;\n            if (name_1) {\n                clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n            }\n            if (version) {\n                clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n            }\n        }\n        var contextHeaders = __assign(__assign({}, clientAwarenessHeaders), context.headers);\n        var contextConfig = {\n            http: context.http,\n            options: context.fetchOptions,\n            credentials: context.credentials,\n            headers: contextHeaders,\n        };\n        if (hasDirectives([\"client\"], operation.query)) {\n            var transformedQuery = removeClientSetsFromDocument(operation.query);\n            if (!transformedQuery) {\n                return fromError(new Error(\"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"));\n            }\n            operation.query = transformedQuery;\n        }\n        //uses fallback, link, and then context to build options\n        var _b = selectHttpOptionsAndBodyInternal(operation, print, fallbackHttpConfig, linkConfig, contextConfig), options = _b.options, body = _b.body;\n        if (body.variables && !includeUnusedVariables) {\n            body.variables = filterOperationVariables(body.variables, operation.query);\n        }\n        var controller;\n        if (!options.signal && typeof AbortController !== \"undefined\") {\n            controller = new AbortController();\n            options.signal = controller.signal;\n        }\n        // If requested, set method to GET if there are no mutations.\n        var definitionIsMutation = function (d) {\n            return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n        };\n        var definitionIsSubscription = function (d) {\n            return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n        };\n        var isSubscription = definitionIsSubscription(getMainDefinition(operation.query));\n        // does not match custom directives beginning with @defer\n        var hasDefer = hasDirectives([\"defer\"], operation.query);\n        if (useGETForQueries &&\n            !operation.query.definitions.some(definitionIsMutation)) {\n            options.method = \"GET\";\n        }\n        if (hasDefer || isSubscription) {\n            options.headers = options.headers || {};\n            var acceptHeader = \"multipart/mixed;\";\n            // Omit defer-specific headers if the user attempts to defer a selection\n            // set on a subscription and log a warning.\n            if (isSubscription && hasDefer) {\n                globalThis.__DEV__ !== false && invariant.warn(41);\n            }\n            if (isSubscription) {\n                acceptHeader +=\n                    \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n            }\n            else if (hasDefer) {\n                acceptHeader += \"deferSpec=20220824,application/json\";\n            }\n            options.headers.accept = acceptHeader;\n        }\n        if (options.method === \"GET\") {\n            var _c = rewriteURIForGET(chosenURI, body), newURI = _c.newURI, parseError = _c.parseError;\n            if (parseError) {\n                return fromError(parseError);\n            }\n            chosenURI = newURI;\n        }\n        else {\n            try {\n                options.body = serializeFetchParameter(body, \"Payload\");\n            }\n            catch (parseError) {\n                return fromError(parseError);\n            }\n        }\n        return new Observable(function (observer) {\n            // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n            // fall back to the *current* global window.fetch function (see issue\n            // #7832), or (if all else fails) the backupFetch function we saved when\n            // this module was first evaluated. This last option protects against the\n            // removal of window.fetch, which is unlikely but not impossible.\n            var currentFetch = preferredFetch || maybe(function () { return fetch; }) || backupFetch;\n            var observerNext = observer.next.bind(observer);\n            currentFetch(chosenURI, options)\n                .then(function (response) {\n                var _a;\n                operation.setContext({ response: response });\n                var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n                if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n                    return readMultipartBody(response, observerNext);\n                }\n                else {\n                    return parseAndCheckHttpResponse(operation)(response).then(observerNext);\n                }\n            })\n                .then(function () {\n                controller = undefined;\n                observer.complete();\n            })\n                .catch(function (err) {\n                controller = undefined;\n                handleError(err, observer);\n            });\n            return function () {\n                // XXX support canceling this request\n                // https://developers.google.com/web/updates/2017/09/abortable-fetch\n                if (controller)\n                    controller.abort();\n            };\n        });\n    });\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,UAAU,EAAEC,aAAa,QAAQ,0BAA0B;AACpE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,yBAAyB,QAAS,gCAAgC;AAC3G,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,gCAAgC,EAAEC,cAAc,EAAEC,kBAAkB,QAAS,+BAA+B;AACrH,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,SAAS,EAAEC,wBAAwB,QAAQ,mBAAmB;AACvE,SAASC,KAAK,EAAEC,iBAAiB,EAAEC,4BAA4B,QAAS,0BAA0B;AAClG,IAAIC,WAAW,GAAGH,KAAK,CAAC,YAAY;EAAE,OAAOI,KAAK;AAAE,CAAC,CAAC;AACtD,OAAO,IAAIC,cAAc,GAAG,SAAAA,CAAUC,WAAW,EAAE;EAC/C,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,CAAC,CAAC;EAAE;EAChD,IAAIC,EAAE,GAAGD,WAAW,CAACE,GAAG;IAAEA,GAAG,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,EAAE;IAC/D;IACAE,cAAc,GAAGH,WAAW,CAACF,KAAK;IAAEM,EAAE,GAAGJ,WAAW,CAACK,KAAK;IAAEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGf,cAAc,GAAGe,EAAE;IAAEE,iBAAiB,GAAGN,WAAW,CAACM,iBAAiB;IAAEC,kBAAkB,GAAGP,WAAW,CAACO,kBAAkB;IAAEC,gBAAgB,GAAGR,WAAW,CAACQ,gBAAgB;IAAEC,EAAE,GAAGT,WAAW,CAACU,sBAAsB;IAAEA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAAEE,cAAc,GAAGlC,MAAM,CAACuB,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,wBAAwB,CAAC,CAAC;EACxf,IAAIY,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;IAC9B;IACA;IACA1B,YAAY,CAACgB,cAAc,IAAIN,WAAW,CAAC;EAC/C;EACA,IAAIiB,UAAU,GAAG;IACbC,IAAI,EAAE;MAAET,iBAAiB,EAAEA,iBAAiB;MAAEC,kBAAkB,EAAEA;IAAmB,CAAC;IACtFS,OAAO,EAAEL,cAAc,CAACM,YAAY;IACpCC,WAAW,EAAEP,cAAc,CAACO,WAAW;IACvCC,OAAO,EAAER,cAAc,CAACQ;EAC5B,CAAC;EACD,OAAO,IAAIxC,UAAU,CAAC,UAAUyC,SAAS,EAAE;IACvC,IAAIC,SAAS,GAAGtC,SAAS,CAACqC,SAAS,EAAElB,GAAG,CAAC;IACzC,IAAIoB,OAAO,GAAGF,SAAS,CAACG,UAAU,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAIF,OAAO,CAACG,eAAe,EAAE;MACzB,IAAIxB,EAAE,GAAGqB,OAAO,CAACG,eAAe;QAAEC,MAAM,GAAGzB,EAAE,CAAC0B,IAAI;QAAEC,OAAO,GAAG3B,EAAE,CAAC2B,OAAO;MACxE,IAAIF,MAAM,EAAE;QACRF,sBAAsB,CAAC,2BAA2B,CAAC,GAAGE,MAAM;MAChE;MACA,IAAIE,OAAO,EAAE;QACTJ,sBAAsB,CAAC,8BAA8B,CAAC,GAAGI,OAAO;MACpE;IACJ;IACA,IAAIC,cAAc,GAAGrD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgD,sBAAsB,CAAC,EAAEF,OAAO,CAACH,OAAO,CAAC;IACpF,IAAIW,aAAa,GAAG;MAChBf,IAAI,EAAEO,OAAO,CAACP,IAAI;MAClBC,OAAO,EAAEM,OAAO,CAACL,YAAY;MAC7BC,WAAW,EAAEI,OAAO,CAACJ,WAAW;MAChCC,OAAO,EAAEU;IACb,CAAC;IACD,IAAIhD,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAEuC,SAAS,CAACW,KAAK,CAAC,EAAE;MAC5C,IAAIC,gBAAgB,GAAGpC,4BAA4B,CAACwB,SAAS,CAACW,KAAK,CAAC;MACpE,IAAI,CAACC,gBAAgB,EAAE;QACnB,OAAOxC,SAAS,CAAC,IAAIyC,KAAK,CAAC,uMAAuM,CAAC,CAAC;MACxO;MACAb,SAAS,CAACW,KAAK,GAAGC,gBAAgB;IACtC;IACA;IACA,IAAI5B,EAAE,GAAGhB,gCAAgC,CAACgC,SAAS,EAAEf,KAAK,EAAEf,kBAAkB,EAAEwB,UAAU,EAAEgB,aAAa,CAAC;MAAEd,OAAO,GAAGZ,EAAE,CAACY,OAAO;MAAEkB,IAAI,GAAG9B,EAAE,CAAC8B,IAAI;IAChJ,IAAIA,IAAI,CAACC,SAAS,IAAI,CAACzB,sBAAsB,EAAE;MAC3CwB,IAAI,CAACC,SAAS,GAAG1C,wBAAwB,CAACyC,IAAI,CAACC,SAAS,EAAEf,SAAS,CAACW,KAAK,CAAC;IAC9E;IACA,IAAIK,UAAU;IACd,IAAI,CAACpB,OAAO,CAACqB,MAAM,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;MAC3DF,UAAU,GAAG,IAAIE,eAAe,CAAC,CAAC;MAClCtB,OAAO,CAACqB,MAAM,GAAGD,UAAU,CAACC,MAAM;IACtC;IACA;IACA,IAAIE,oBAAoB,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACpC,OAAOA,CAAC,CAACC,IAAI,KAAK,qBAAqB,IAAID,CAAC,CAACpB,SAAS,KAAK,UAAU;IACzE,CAAC;IACD,IAAIsB,wBAAwB,GAAG,SAAAA,CAAUF,CAAC,EAAE;MACxC,OAAOA,CAAC,CAACC,IAAI,KAAK,qBAAqB,IAAID,CAAC,CAACpB,SAAS,KAAK,cAAc;IAC7E,CAAC;IACD,IAAIuB,cAAc,GAAGD,wBAAwB,CAAC/C,iBAAiB,CAACyB,SAAS,CAACW,KAAK,CAAC,CAAC;IACjF;IACA,IAAIa,QAAQ,GAAG/D,aAAa,CAAC,CAAC,OAAO,CAAC,EAAEuC,SAAS,CAACW,KAAK,CAAC;IACxD,IAAIvB,gBAAgB,IAChB,CAACY,SAAS,CAACW,KAAK,CAACc,WAAW,CAACC,IAAI,CAACP,oBAAoB,CAAC,EAAE;MACzDvB,OAAO,CAAC+B,MAAM,GAAG,KAAK;IAC1B;IACA,IAAIH,QAAQ,IAAID,cAAc,EAAE;MAC5B3B,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MACvC,IAAI6B,YAAY,GAAG,kBAAkB;MACrC;MACA;MACA,IAAIL,cAAc,IAAIC,QAAQ,EAAE;QAC5BhC,UAAU,CAACC,OAAO,KAAK,KAAK,IAAInC,SAAS,CAACuE,IAAI,CAAC,EAAE,CAAC;MACtD;MACA,IAAIN,cAAc,EAAE;QAChBK,YAAY,IACR,wDAAwD;MAChE,CAAC,MACI,IAAIJ,QAAQ,EAAE;QACfI,YAAY,IAAI,qCAAqC;MACzD;MACAhC,OAAO,CAACG,OAAO,CAAC+B,MAAM,GAAGF,YAAY;IACzC;IACA,IAAIhC,OAAO,CAAC+B,MAAM,KAAK,KAAK,EAAE;MAC1B,IAAItC,EAAE,GAAGlB,gBAAgB,CAAC8B,SAAS,EAAEa,IAAI,CAAC;QAAEiB,MAAM,GAAG1C,EAAE,CAAC0C,MAAM;QAAEC,UAAU,GAAG3C,EAAE,CAAC2C,UAAU;MAC1F,IAAIA,UAAU,EAAE;QACZ,OAAO5D,SAAS,CAAC4D,UAAU,CAAC;MAChC;MACA/B,SAAS,GAAG8B,MAAM;IACtB,CAAC,MACI;MACD,IAAI;QACAnC,OAAO,CAACkB,IAAI,GAAGpD,uBAAuB,CAACoD,IAAI,EAAE,SAAS,CAAC;MAC3D,CAAC,CACD,OAAOkB,UAAU,EAAE;QACf,OAAO5D,SAAS,CAAC4D,UAAU,CAAC;MAChC;IACJ;IACA,OAAO,IAAIxE,UAAU,CAAC,UAAUyE,QAAQ,EAAE;MACtC;MACA;MACA;MACA;MACA;MACA,IAAIC,YAAY,GAAGnD,cAAc,IAAIT,KAAK,CAAC,YAAY;QAAE,OAAOI,KAAK;MAAE,CAAC,CAAC,IAAID,WAAW;MACxF,IAAI0D,YAAY,GAAGF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC;MAC/CC,YAAY,CAACjC,SAAS,EAAEL,OAAO,CAAC,CAC3B0C,IAAI,CAAC,UAAUC,QAAQ,EAAE;QAC1B,IAAI1D,EAAE;QACNmB,SAAS,CAACwC,UAAU,CAAC;UAAED,QAAQ,EAAEA;QAAS,CAAC,CAAC;QAC5C,IAAIE,KAAK,GAAG,CAAC5D,EAAE,GAAG0D,QAAQ,CAACxC,OAAO,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6D,GAAG,CAAC,cAAc,CAAC;QAC/F,IAAID,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;UACpD,OAAO5E,iBAAiB,CAAC0E,QAAQ,EAAEJ,YAAY,CAAC;QACpD,CAAC,MACI;UACD,OAAOrE,yBAAyB,CAACkC,SAAS,CAAC,CAACuC,QAAQ,CAAC,CAACD,IAAI,CAACH,YAAY,CAAC;QAC5E;MACJ,CAAC,CAAC,CACGG,IAAI,CAAC,YAAY;QAClBtB,UAAU,GAAG4B,SAAS;QACtBX,QAAQ,CAACY,QAAQ,CAAC,CAAC;MACvB,CAAC,CAAC,CACGC,KAAK,CAAC,UAAUC,GAAG,EAAE;QACtB/B,UAAU,GAAG4B,SAAS;QACtBhF,WAAW,CAACmF,GAAG,EAAEd,QAAQ,CAAC;MAC9B,CAAC,CAAC;MACF,OAAO,YAAY;QACf;QACA;QACA,IAAIjB,UAAU,EACVA,UAAU,CAACgC,KAAK,CAAC,CAAC;MAC1B,CAAC;IACL,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}