{"ast": null, "code": "import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\nfunction identity(document) {\n  return document;\n}\nvar DocumentTransform = /** @class */function () {\n  function DocumentTransform(transform, options) {\n    if (options === void 0) {\n      options = Object.create(null);\n    }\n    this.resultCache = canUseWeakSet ? new WeakSet() : new Set();\n    this.transform = transform;\n    if (options.getCacheKey) {\n      // Override default `getCacheKey` function, which returns [document].\n      this.getCacheKey = options.getCacheKey;\n    }\n    this.cached = options.cache !== false;\n    this.resetCache();\n  }\n  // This default implementation of getCacheKey can be overridden by providing\n  // options.getCacheKey to the DocumentTransform constructor. In general, a\n  // getCacheKey function may either return an array of keys (often including\n  // the document) to be used as a cache key, or undefined to indicate the\n  // transform for this document should not be cached.\n  DocumentTransform.prototype.getCacheKey = function (document) {\n    return [document];\n  };\n  DocumentTransform.identity = function () {\n    // No need to cache this transform since it just returns the document\n    // unchanged. This should save a bit of memory that would otherwise be\n    // needed to populate the `documentCache` of this transform.\n    return new DocumentTransform(identity, {\n      cache: false\n    });\n  };\n  DocumentTransform.split = function (predicate, left, right) {\n    if (right === void 0) {\n      right = DocumentTransform.identity();\n    }\n    return Object.assign(new DocumentTransform(function (document) {\n      var documentTransform = predicate(document) ? left : right;\n      return documentTransform.transformDocument(document);\n    },\n    // Reasonably assume both `left` and `right` transforms handle their own caching\n    {\n      cache: false\n    }), {\n      left: left,\n      right: right\n    });\n  };\n  /**\n   * Resets the internal cache of this transform, if it has one.\n   */\n  DocumentTransform.prototype.resetCache = function () {\n    var _this = this;\n    if (this.cached) {\n      var stableCacheKeys_1 = new Trie(canUseWeakMap);\n      this.performWork = wrap(DocumentTransform.prototype.performWork.bind(this), {\n        makeCacheKey: function (document) {\n          var cacheKeys = _this.getCacheKey(document);\n          if (cacheKeys) {\n            invariant(Array.isArray(cacheKeys), 77);\n            return stableCacheKeys_1.lookupArray(cacheKeys);\n          }\n        },\n        max: cacheSizes[\"documentTransform.cache\"],\n        cache: WeakCache\n      });\n    }\n  };\n  DocumentTransform.prototype.performWork = function (document) {\n    checkDocument(document);\n    return this.transform(document);\n  };\n  DocumentTransform.prototype.transformDocument = function (document) {\n    // If a user passes an already transformed result back to this function,\n    // immediately return it.\n    if (this.resultCache.has(document)) {\n      return document;\n    }\n    var transformedDocument = this.performWork(document);\n    this.resultCache.add(transformedDocument);\n    return transformedDocument;\n  };\n  DocumentTransform.prototype.concat = function (otherTransform) {\n    var _this = this;\n    return Object.assign(new DocumentTransform(function (document) {\n      return otherTransform.transformDocument(_this.transformDocument(document));\n    },\n    // Reasonably assume both transforms handle their own caching\n    {\n      cache: false\n    }), {\n      left: this,\n      right: otherTransform\n    });\n  };\n  return DocumentTransform;\n}();\nexport { DocumentTransform };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "canUseWeakMap", "canUseWeakSet", "checkDocument", "invariant", "<PERSON>ak<PERSON><PERSON>", "wrap", "cacheSizes", "identity", "document", "DocumentTransform", "transform", "options", "Object", "create", "resultCache", "WeakSet", "Set", "get<PERSON><PERSON><PERSON><PERSON>", "cached", "cache", "resetCache", "prototype", "split", "predicate", "left", "right", "assign", "documentTransform", "transformDocument", "_this", "stableCacheKeys_1", "performWork", "bind", "make<PERSON><PERSON><PERSON><PERSON>", "cacheKeys", "Array", "isArray", "lookupArray", "max", "has", "transformedDocument", "add", "concat", "otherTransform"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/DocumentTransform.js"], "sourcesContent": ["import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\nfunction identity(document) {\n    return document;\n}\nvar DocumentTransform = /** @class */ (function () {\n    function DocumentTransform(transform, options) {\n        if (options === void 0) { options = Object.create(null); }\n        this.resultCache = canUseWeakSet ? new WeakSet() : new Set();\n        this.transform = transform;\n        if (options.getCacheKey) {\n            // Override default `getCacheKey` function, which returns [document].\n            this.getCacheKey = options.getCacheKey;\n        }\n        this.cached = options.cache !== false;\n        this.resetCache();\n    }\n    // This default implementation of getCacheKey can be overridden by providing\n    // options.getCacheKey to the DocumentTransform constructor. In general, a\n    // getCacheKey function may either return an array of keys (often including\n    // the document) to be used as a cache key, or undefined to indicate the\n    // transform for this document should not be cached.\n    DocumentTransform.prototype.getCacheKey = function (document) {\n        return [document];\n    };\n    DocumentTransform.identity = function () {\n        // No need to cache this transform since it just returns the document\n        // unchanged. This should save a bit of memory that would otherwise be\n        // needed to populate the `documentCache` of this transform.\n        return new DocumentTransform(identity, { cache: false });\n    };\n    DocumentTransform.split = function (predicate, left, right) {\n        if (right === void 0) { right = DocumentTransform.identity(); }\n        return Object.assign(new DocumentTransform(function (document) {\n            var documentTransform = predicate(document) ? left : right;\n            return documentTransform.transformDocument(document);\n        }, \n        // Reasonably assume both `left` and `right` transforms handle their own caching\n        { cache: false }), { left: left, right: right });\n    };\n    /**\n     * Resets the internal cache of this transform, if it has one.\n     */\n    DocumentTransform.prototype.resetCache = function () {\n        var _this = this;\n        if (this.cached) {\n            var stableCacheKeys_1 = new Trie(canUseWeakMap);\n            this.performWork = wrap(DocumentTransform.prototype.performWork.bind(this), {\n                makeCacheKey: function (document) {\n                    var cacheKeys = _this.getCacheKey(document);\n                    if (cacheKeys) {\n                        invariant(Array.isArray(cacheKeys), 77);\n                        return stableCacheKeys_1.lookupArray(cacheKeys);\n                    }\n                },\n                max: cacheSizes[\"documentTransform.cache\"],\n                cache: (WeakCache),\n            });\n        }\n    };\n    DocumentTransform.prototype.performWork = function (document) {\n        checkDocument(document);\n        return this.transform(document);\n    };\n    DocumentTransform.prototype.transformDocument = function (document) {\n        // If a user passes an already transformed result back to this function,\n        // immediately return it.\n        if (this.resultCache.has(document)) {\n            return document;\n        }\n        var transformedDocument = this.performWork(document);\n        this.resultCache.add(transformedDocument);\n        return transformedDocument;\n    };\n    DocumentTransform.prototype.concat = function (otherTransform) {\n        var _this = this;\n        return Object.assign(new DocumentTransform(function (document) {\n            return otherTransform.transformDocument(_this.transformDocument(document));\n        }, \n        // Reasonably assume both transforms handle their own caching\n        { cache: false }), {\n            left: this,\n            right: otherTransform,\n        });\n    };\n    return DocumentTransform;\n}());\nexport { DocumentTransform };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,EAAEC,aAAa,QAAQ,qBAAqB;AAClE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,QAAQA,CAACC,QAAQ,EAAE;EACxB,OAAOA,QAAQ;AACnB;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC3C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAAE;IACzD,IAAI,CAACC,WAAW,GAAGb,aAAa,GAAG,IAAIc,OAAO,CAAC,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5D,IAAI,CAACN,SAAS,GAAGA,SAAS;IAC1B,IAAIC,OAAO,CAACM,WAAW,EAAE;MACrB;MACA,IAAI,CAACA,WAAW,GAAGN,OAAO,CAACM,WAAW;IAC1C;IACA,IAAI,CAACC,MAAM,GAAGP,OAAO,CAACQ,KAAK,KAAK,KAAK;IACrC,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACA;EACA;EACA;EACA;EACA;EACAX,iBAAiB,CAACY,SAAS,CAACJ,WAAW,GAAG,UAAUT,QAAQ,EAAE;IAC1D,OAAO,CAACA,QAAQ,CAAC;EACrB,CAAC;EACDC,iBAAiB,CAACF,QAAQ,GAAG,YAAY;IACrC;IACA;IACA;IACA,OAAO,IAAIE,iBAAiB,CAACF,QAAQ,EAAE;MAAEY,KAAK,EAAE;IAAM,CAAC,CAAC;EAC5D,CAAC;EACDV,iBAAiB,CAACa,KAAK,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACxD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAGhB,iBAAiB,CAACF,QAAQ,CAAC,CAAC;IAAE;IAC9D,OAAOK,MAAM,CAACc,MAAM,CAAC,IAAIjB,iBAAiB,CAAC,UAAUD,QAAQ,EAAE;MAC3D,IAAImB,iBAAiB,GAAGJ,SAAS,CAACf,QAAQ,CAAC,GAAGgB,IAAI,GAAGC,KAAK;MAC1D,OAAOE,iBAAiB,CAACC,iBAAiB,CAACpB,QAAQ,CAAC;IACxD,CAAC;IACD;IACA;MAAEW,KAAK,EAAE;IAAM,CAAC,CAAC,EAAE;MAAEK,IAAI,EAAEA,IAAI;MAAEC,KAAK,EAAEA;IAAM,CAAC,CAAC;EACpD,CAAC;EACD;AACJ;AACA;EACIhB,iBAAiB,CAACY,SAAS,CAACD,UAAU,GAAG,YAAY;IACjD,IAAIS,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAACX,MAAM,EAAE;MACb,IAAIY,iBAAiB,GAAG,IAAI/B,IAAI,CAACC,aAAa,CAAC;MAC/C,IAAI,CAAC+B,WAAW,GAAG1B,IAAI,CAACI,iBAAiB,CAACY,SAAS,CAACU,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;QACxEC,YAAY,EAAE,SAAAA,CAAUzB,QAAQ,EAAE;UAC9B,IAAI0B,SAAS,GAAGL,KAAK,CAACZ,WAAW,CAACT,QAAQ,CAAC;UAC3C,IAAI0B,SAAS,EAAE;YACX/B,SAAS,CAACgC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE,EAAE,CAAC;YACvC,OAAOJ,iBAAiB,CAACO,WAAW,CAACH,SAAS,CAAC;UACnD;QACJ,CAAC;QACDI,GAAG,EAAEhC,UAAU,CAAC,yBAAyB,CAAC;QAC1Ca,KAAK,EAAGf;MACZ,CAAC,CAAC;IACN;EACJ,CAAC;EACDK,iBAAiB,CAACY,SAAS,CAACU,WAAW,GAAG,UAAUvB,QAAQ,EAAE;IAC1DN,aAAa,CAACM,QAAQ,CAAC;IACvB,OAAO,IAAI,CAACE,SAAS,CAACF,QAAQ,CAAC;EACnC,CAAC;EACDC,iBAAiB,CAACY,SAAS,CAACO,iBAAiB,GAAG,UAAUpB,QAAQ,EAAE;IAChE;IACA;IACA,IAAI,IAAI,CAACM,WAAW,CAACyB,GAAG,CAAC/B,QAAQ,CAAC,EAAE;MAChC,OAAOA,QAAQ;IACnB;IACA,IAAIgC,mBAAmB,GAAG,IAAI,CAACT,WAAW,CAACvB,QAAQ,CAAC;IACpD,IAAI,CAACM,WAAW,CAAC2B,GAAG,CAACD,mBAAmB,CAAC;IACzC,OAAOA,mBAAmB;EAC9B,CAAC;EACD/B,iBAAiB,CAACY,SAAS,CAACqB,MAAM,GAAG,UAAUC,cAAc,EAAE;IAC3D,IAAId,KAAK,GAAG,IAAI;IAChB,OAAOjB,MAAM,CAACc,MAAM,CAAC,IAAIjB,iBAAiB,CAAC,UAAUD,QAAQ,EAAE;MAC3D,OAAOmC,cAAc,CAACf,iBAAiB,CAACC,KAAK,CAACD,iBAAiB,CAACpB,QAAQ,CAAC,CAAC;IAC9E,CAAC;IACD;IACA;MAAEW,KAAK,EAAE;IAAM,CAAC,CAAC,EAAE;MACfK,IAAI,EAAE,IAAI;MACVC,KAAK,EAAEkB;IACX,CAAC,CAAC;EACN,CAAC;EACD,OAAOlC,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}