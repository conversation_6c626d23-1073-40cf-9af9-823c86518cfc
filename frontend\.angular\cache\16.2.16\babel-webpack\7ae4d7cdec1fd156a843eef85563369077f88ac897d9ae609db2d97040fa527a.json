{"ast": null, "code": "import { invariant, newInvariantError, InvariantError } from \"./invariantWrappers.js\";\nexport { maybe } from \"./maybe.js\";\nexport { default as global } from \"./global.js\";\nexport { invariant, newInvariantError, InvariantError };\n/**\n * @deprecated we do not use this internally anymore,\n * it is just exported for backwards compatibility\n */\n// this file is extempt from automatic `__DEV__` replacement\n// so we have to write it out here\n// @ts-ignore\nexport var DEV = globalThis.__DEV__ !== false;\nexport { DEV as __DEV__ };", "map": {"version": 3, "names": ["invariant", "newInvariantError", "InvariantError", "maybe", "default", "global", "DEV", "globalThis", "__DEV__"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/globals/index.js"], "sourcesContent": ["import { invariant, newInvariantError, InvariantError, } from \"./invariantWrappers.js\";\nexport { maybe } from \"./maybe.js\";\nexport { default as global } from \"./global.js\";\nexport { invariant, newInvariantError, InvariantError };\n/**\n * @deprecated we do not use this internally anymore,\n * it is just exported for backwards compatibility\n */\n// this file is extempt from automatic `__DEV__` replacement\n// so we have to write it out here\n// @ts-ignore\nexport var DEV = globalThis.__DEV__ !== false;\nexport { DEV as __DEV__ };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,iBAAiB,EAAEC,cAAc,QAAS,wBAAwB;AACtF,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,OAAO,IAAIC,MAAM,QAAQ,aAAa;AAC/C,SAASL,SAAS,EAAEC,iBAAiB,EAAEC,cAAc;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,GAAG,GAAGC,UAAU,CAACC,OAAO,KAAK,KAAK;AAC7C,SAASF,GAAG,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}