{"ast": null, "code": "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n  var count = prefixCounts.get(prefix) || 1;\n  prefixCounts.set(prefix, count + 1);\n  return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}", "map": {"version": 3, "names": ["prefixCounts", "Map", "makeUniqueId", "prefix", "count", "get", "set", "concat", "Math", "random", "toString", "slice"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/makeUniqueId.js"], "sourcesContent": ["var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n    var count = prefixCounts.get(prefix) || 1;\n    prefixCounts.set(prefix, count + 1);\n    return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC5B;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACjC,IAAIC,KAAK,GAAGJ,YAAY,CAACK,GAAG,CAACF,MAAM,CAAC,IAAI,CAAC;EACzCH,YAAY,CAACM,GAAG,CAACH,MAAM,EAAEC,KAAK,GAAG,CAAC,CAAC;EACnC,OAAO,EAAE,CAACG,MAAM,CAACJ,MAAM,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,KAAK,EAAE,GAAG,CAAC,CAACG,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}