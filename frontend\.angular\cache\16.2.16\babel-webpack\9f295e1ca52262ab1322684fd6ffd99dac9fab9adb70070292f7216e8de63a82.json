{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ResetPasswordRoutingModule } from './reset-password-routing.module';\nimport { ResetPasswordComponent } from './reset-password.component';\nimport * as i0 from \"@angular/core\";\nexport class ResetPasswordModule {\n  static {\n    this.ɵfac = function ResetPasswordModule_Factory(t) {\n      return new (t || ResetPasswordModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResetPasswordModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, ResetPasswordRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResetPasswordModule, {\n    declarations: [ResetPasswordComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, ResetPasswordRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "ResetPasswordRoutingModule", "ResetPasswordComponent", "ResetPasswordModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reset-password\\reset-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { ResetPasswordRoutingModule } from './reset-password-routing.module';\r\nimport { ResetPasswordComponent } from './reset-password.component';\r\n\r\n@NgModule({\r\n  declarations: [ResetPasswordComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ResetPasswordRoutingModule,\r\n  ],\r\n})\r\nexport class ResetPasswordModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,sBAAsB,QAAQ,4BAA4B;;AAWnE,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAN5BL,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,0BAA0B;IAAA;EAAA;;;2EAGjBE,mBAAmB;IAAAC,YAAA,GARfF,sBAAsB;IAAAG,OAAA,GAEnCP,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}