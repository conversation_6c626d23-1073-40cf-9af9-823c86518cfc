{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ForgotPasswordRoutingModule } from './forgot-password-routing.module';\nimport { ForgotPasswordComponent } from './forgot-password.component';\nimport * as i0 from \"@angular/core\";\nexport class ForgotPasswordModule {\n  static {\n    this.ɵfac = function ForgotPasswordModule_Factory(t) {\n      return new (t || ForgotPasswordModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ForgotPasswordModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, ForgotPasswordRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ForgotPasswordModule, {\n    declarations: [ForgotPasswordComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, ForgotPasswordRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "ForgotPasswordRoutingModule", "ForgotPasswordComponent", "ForgotPasswordModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\forgot-password\\forgot-password.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { ForgotPasswordRoutingModule } from './forgot-password-routing.module';\r\nimport { ForgotPasswordComponent } from './forgot-password.component';\r\n\r\n@NgModule({\r\n  declarations: [ForgotPasswordComponent],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ForgotPasswordRoutingModule,\r\n  ],\r\n})\r\nexport class ForgotPasswordModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,uBAAuB,QAAQ,6BAA6B;;AAWrE,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAN7BL,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,2BAA2B;IAAA;EAAA;;;2EAGlBE,oBAAoB;IAAAC,YAAA,GARhBF,uBAAuB;IAAAG,OAAA,GAEpCP,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}