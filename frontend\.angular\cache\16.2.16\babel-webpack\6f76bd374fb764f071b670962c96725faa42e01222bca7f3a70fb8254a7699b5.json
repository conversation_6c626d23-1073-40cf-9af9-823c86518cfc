{"ast": null, "code": "import { __assign, __extends, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { dep } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { isReference, makeReference, DeepMerger, maybeDeepFreeze, canUseWeakMap, isNonNullObject } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName } from \"./helpers.js\";\nvar DELETE = Object.create(null);\nvar delModifier = function () {\n  return DELETE;\n};\nvar INVALIDATE = Object.create(null);\nvar EntityStore = /** @class */function () {\n  function EntityStore(policies, group) {\n    var _this = this;\n    this.policies = policies;\n    this.group = group;\n    this.data = Object.create(null);\n    // Maps root entity IDs to the number of times they have been retained, minus\n    // the number of times they have been released. Retained entities keep other\n    // entities they reference (even indirectly) from being garbage collected.\n    this.rootIds = Object.create(null);\n    // Lazily tracks { __ref: <dataId> } strings contained by this.data[dataId].\n    this.refs = Object.create(null);\n    // Bound function that can be passed around to provide easy access to fields\n    // of Reference objects as well as ordinary objects.\n    this.getFieldValue = function (objectOrReference, storeFieldName) {\n      return maybeDeepFreeze(isReference(objectOrReference) ? _this.get(objectOrReference.__ref, storeFieldName) : objectOrReference && objectOrReference[storeFieldName]);\n    };\n    // Returns true for non-normalized StoreObjects and non-dangling\n    // References, indicating that readField(name, objOrRef) has a chance of\n    // working. Useful for filtering out dangling references from lists.\n    this.canRead = function (objOrRef) {\n      return isReference(objOrRef) ? _this.has(objOrRef.__ref) : typeof objOrRef === \"object\";\n    };\n    // Bound function that converts an id or an object with a __typename and\n    // primary key fields to a Reference object. If called with a Reference object,\n    // that same Reference object is returned. Pass true for mergeIntoStore to persist\n    // an object into the store.\n    this.toReference = function (objOrIdOrRef, mergeIntoStore) {\n      if (typeof objOrIdOrRef === \"string\") {\n        return makeReference(objOrIdOrRef);\n      }\n      if (isReference(objOrIdOrRef)) {\n        return objOrIdOrRef;\n      }\n      var id = _this.policies.identify(objOrIdOrRef)[0];\n      if (id) {\n        var ref = makeReference(id);\n        if (mergeIntoStore) {\n          _this.merge(id, objOrIdOrRef);\n        }\n        return ref;\n      }\n    };\n  }\n  // Although the EntityStore class is abstract, it contains concrete\n  // implementations of the various NormalizedCache interface methods that\n  // are inherited by the Root and Layer subclasses.\n  EntityStore.prototype.toObject = function () {\n    return __assign({}, this.data);\n  };\n  EntityStore.prototype.has = function (dataId) {\n    return this.lookup(dataId, true) !== void 0;\n  };\n  EntityStore.prototype.get = function (dataId, fieldName) {\n    this.group.depend(dataId, fieldName);\n    if (hasOwn.call(this.data, dataId)) {\n      var storeObject = this.data[dataId];\n      if (storeObject && hasOwn.call(storeObject, fieldName)) {\n        return storeObject[fieldName];\n      }\n    }\n    if (fieldName === \"__typename\" && hasOwn.call(this.policies.rootTypenamesById, dataId)) {\n      return this.policies.rootTypenamesById[dataId];\n    }\n    if (this instanceof Layer) {\n      return this.parent.get(dataId, fieldName);\n    }\n  };\n  EntityStore.prototype.lookup = function (dataId, dependOnExistence) {\n    // The has method (above) calls lookup with dependOnExistence = true, so\n    // that it can later be invalidated when we add or remove a StoreObject for\n    // this dataId. Any consumer who cares about the contents of the StoreObject\n    // should not rely on this dependency, since the contents could change\n    // without the object being added or removed.\n    if (dependOnExistence) this.group.depend(dataId, \"__exists\");\n    if (hasOwn.call(this.data, dataId)) {\n      return this.data[dataId];\n    }\n    if (this instanceof Layer) {\n      return this.parent.lookup(dataId, dependOnExistence);\n    }\n    if (this.policies.rootTypenamesById[dataId]) {\n      return Object.create(null);\n    }\n  };\n  EntityStore.prototype.merge = function (older, newer) {\n    var _this = this;\n    var dataId;\n    // Convert unexpected references to ID strings.\n    if (isReference(older)) older = older.__ref;\n    if (isReference(newer)) newer = newer.__ref;\n    var existing = typeof older === \"string\" ? this.lookup(dataId = older) : older;\n    var incoming = typeof newer === \"string\" ? this.lookup(dataId = newer) : newer;\n    // If newer was a string ID, but that ID was not defined in this store,\n    // then there are no fields to be merged, so we're done.\n    if (!incoming) return;\n    invariant(typeof dataId === \"string\", 2);\n    var merged = new DeepMerger(storeObjectReconciler).merge(existing, incoming);\n    // Even if merged === existing, existing may have come from a lower\n    // layer, so we always need to set this.data[dataId] on this level.\n    this.data[dataId] = merged;\n    if (merged !== existing) {\n      delete this.refs[dataId];\n      if (this.group.caching) {\n        var fieldsToDirty_1 = Object.create(null);\n        // If we added a new StoreObject where there was previously none, dirty\n        // anything that depended on the existence of this dataId, such as the\n        // EntityStore#has method.\n        if (!existing) fieldsToDirty_1.__exists = 1;\n        // Now invalidate dependents who called getFieldValue for any fields\n        // that are changing as a result of this merge.\n        Object.keys(incoming).forEach(function (storeFieldName) {\n          if (!existing || existing[storeFieldName] !== merged[storeFieldName]) {\n            // Always dirty the full storeFieldName, which may include\n            // serialized arguments following the fieldName prefix.\n            fieldsToDirty_1[storeFieldName] = 1;\n            // Also dirty fieldNameFromStoreName(storeFieldName) if it's\n            // different from storeFieldName and this field does not have\n            // keyArgs configured, because that means the cache can't make\n            // any assumptions about how field values with the same field\n            // name but different arguments might be interrelated, so it\n            // must err on the side of invalidating all field values that\n            // share the same short fieldName, regardless of arguments.\n            var fieldName = fieldNameFromStoreName(storeFieldName);\n            if (fieldName !== storeFieldName && !_this.policies.hasKeyArgs(merged.__typename, fieldName)) {\n              fieldsToDirty_1[fieldName] = 1;\n            }\n            // If merged[storeFieldName] has become undefined, and this is the\n            // Root layer, actually delete the property from the merged object,\n            // which is guaranteed to have been created fresh in this method.\n            if (merged[storeFieldName] === void 0 && !(_this instanceof Layer)) {\n              delete merged[storeFieldName];\n            }\n          }\n        });\n        if (fieldsToDirty_1.__typename && !(existing && existing.__typename) &&\n        // Since we return default root __typename strings\n        // automatically from store.get, we don't need to dirty the\n        // ROOT_QUERY.__typename field if merged.__typename is equal\n        // to the default string (usually \"Query\").\n        this.policies.rootTypenamesById[dataId] === merged.__typename) {\n          delete fieldsToDirty_1.__typename;\n        }\n        Object.keys(fieldsToDirty_1).forEach(function (fieldName) {\n          return _this.group.dirty(dataId, fieldName);\n        });\n      }\n    }\n  };\n  EntityStore.prototype.modify = function (dataId, fields) {\n    var _this = this;\n    var storeObject = this.lookup(dataId);\n    if (storeObject) {\n      var changedFields_1 = Object.create(null);\n      var needToMerge_1 = false;\n      var allDeleted_1 = true;\n      var sharedDetails_1 = {\n        DELETE: DELETE,\n        INVALIDATE: INVALIDATE,\n        isReference: isReference,\n        toReference: this.toReference,\n        canRead: this.canRead,\n        readField: function (fieldNameOrOptions, from) {\n          return _this.policies.readField(typeof fieldNameOrOptions === \"string\" ? {\n            fieldName: fieldNameOrOptions,\n            from: from || makeReference(dataId)\n          } : fieldNameOrOptions, {\n            store: _this\n          });\n        }\n      };\n      Object.keys(storeObject).forEach(function (storeFieldName) {\n        var fieldName = fieldNameFromStoreName(storeFieldName);\n        var fieldValue = storeObject[storeFieldName];\n        if (fieldValue === void 0) return;\n        var modify = typeof fields === \"function\" ? fields : fields[storeFieldName] || fields[fieldName];\n        if (modify) {\n          var newValue = modify === delModifier ? DELETE : modify(maybeDeepFreeze(fieldValue), __assign(__assign({}, sharedDetails_1), {\n            fieldName: fieldName,\n            storeFieldName: storeFieldName,\n            storage: _this.getStorage(dataId, storeFieldName)\n          }));\n          if (newValue === INVALIDATE) {\n            _this.group.dirty(dataId, storeFieldName);\n          } else {\n            if (newValue === DELETE) newValue = void 0;\n            if (newValue !== fieldValue) {\n              changedFields_1[storeFieldName] = newValue;\n              needToMerge_1 = true;\n              fieldValue = newValue;\n              if (globalThis.__DEV__ !== false) {\n                var checkReference = function (ref) {\n                  if (_this.lookup(ref.__ref) === undefined) {\n                    globalThis.__DEV__ !== false && invariant.warn(3, ref);\n                    return true;\n                  }\n                };\n                if (isReference(newValue)) {\n                  checkReference(newValue);\n                } else if (Array.isArray(newValue)) {\n                  // Warn about writing \"mixed\" arrays of Reference and non-Reference objects\n                  var seenReference = false;\n                  var someNonReference = void 0;\n                  for (var _i = 0, newValue_1 = newValue; _i < newValue_1.length; _i++) {\n                    var value = newValue_1[_i];\n                    if (isReference(value)) {\n                      seenReference = true;\n                      if (checkReference(value)) break;\n                    } else {\n                      // Do not warn on primitive values, since those could never be represented\n                      // by a reference. This is a valid (albeit uncommon) use case.\n                      if (typeof value === \"object\" && !!value) {\n                        var id = _this.policies.identify(value)[0];\n                        // check if object could even be referenced, otherwise we are not interested in it for this warning\n                        if (id) {\n                          someNonReference = value;\n                        }\n                      }\n                    }\n                    if (seenReference && someNonReference !== undefined) {\n                      globalThis.__DEV__ !== false && invariant.warn(4, someNonReference);\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n        if (fieldValue !== void 0) {\n          allDeleted_1 = false;\n        }\n      });\n      if (needToMerge_1) {\n        this.merge(dataId, changedFields_1);\n        if (allDeleted_1) {\n          if (this instanceof Layer) {\n            this.data[dataId] = void 0;\n          } else {\n            delete this.data[dataId];\n          }\n          this.group.dirty(dataId, \"__exists\");\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  // If called with only one argument, removes the entire entity\n  // identified by dataId. If called with a fieldName as well, removes all\n  // fields of that entity whose names match fieldName according to the\n  // fieldNameFromStoreName helper function. If called with a fieldName\n  // and variables, removes all fields of that entity whose names match fieldName\n  // and whose arguments when cached exactly match the variables passed.\n  EntityStore.prototype.delete = function (dataId, fieldName, args) {\n    var _a;\n    var storeObject = this.lookup(dataId);\n    if (storeObject) {\n      var typename = this.getFieldValue(storeObject, \"__typename\");\n      var storeFieldName = fieldName && args ? this.policies.getStoreFieldName({\n        typename: typename,\n        fieldName: fieldName,\n        args: args\n      }) : fieldName;\n      return this.modify(dataId, storeFieldName ? (_a = {}, _a[storeFieldName] = delModifier, _a) : delModifier);\n    }\n    return false;\n  };\n  EntityStore.prototype.evict = function (options, limit) {\n    var evicted = false;\n    if (options.id) {\n      if (hasOwn.call(this.data, options.id)) {\n        evicted = this.delete(options.id, options.fieldName, options.args);\n      }\n      if (this instanceof Layer && this !== limit) {\n        evicted = this.parent.evict(options, limit) || evicted;\n      }\n      // Always invalidate the field to trigger rereading of watched\n      // queries, even if no cache data was modified by the eviction,\n      // because queries may depend on computed fields with custom read\n      // functions, whose values are not stored in the EntityStore.\n      if (options.fieldName || evicted) {\n        this.group.dirty(options.id, options.fieldName || \"__exists\");\n      }\n    }\n    return evicted;\n  };\n  EntityStore.prototype.clear = function () {\n    this.replace(null);\n  };\n  EntityStore.prototype.extract = function () {\n    var _this = this;\n    var obj = this.toObject();\n    var extraRootIds = [];\n    this.getRootIdSet().forEach(function (id) {\n      if (!hasOwn.call(_this.policies.rootTypenamesById, id)) {\n        extraRootIds.push(id);\n      }\n    });\n    if (extraRootIds.length) {\n      obj.__META = {\n        extraRootIds: extraRootIds.sort()\n      };\n    }\n    return obj;\n  };\n  EntityStore.prototype.replace = function (newData) {\n    var _this = this;\n    Object.keys(this.data).forEach(function (dataId) {\n      if (!(newData && hasOwn.call(newData, dataId))) {\n        _this.delete(dataId);\n      }\n    });\n    if (newData) {\n      var __META = newData.__META,\n        rest_1 = __rest(newData, [\"__META\"]);\n      Object.keys(rest_1).forEach(function (dataId) {\n        _this.merge(dataId, rest_1[dataId]);\n      });\n      if (__META) {\n        __META.extraRootIds.forEach(this.retain, this);\n      }\n    }\n  };\n  EntityStore.prototype.retain = function (rootId) {\n    return this.rootIds[rootId] = (this.rootIds[rootId] || 0) + 1;\n  };\n  EntityStore.prototype.release = function (rootId) {\n    if (this.rootIds[rootId] > 0) {\n      var count = --this.rootIds[rootId];\n      if (!count) delete this.rootIds[rootId];\n      return count;\n    }\n    return 0;\n  };\n  // Return a Set<string> of all the ID strings that have been retained by\n  // this layer/root *and* any layers/roots beneath it.\n  EntityStore.prototype.getRootIdSet = function (ids) {\n    if (ids === void 0) {\n      ids = new Set();\n    }\n    Object.keys(this.rootIds).forEach(ids.add, ids);\n    if (this instanceof Layer) {\n      this.parent.getRootIdSet(ids);\n    } else {\n      // Official singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n      // always considered roots for garbage collection, regardless of\n      // their retainment counts in this.rootIds.\n      Object.keys(this.policies.rootTypenamesById).forEach(ids.add, ids);\n    }\n    return ids;\n  };\n  // The goal of garbage collection is to remove IDs from the Root layer of the\n  // store that are no longer reachable starting from any IDs that have been\n  // explicitly retained (see retain and release, above). Returns an array of\n  // dataId strings that were removed from the store.\n  EntityStore.prototype.gc = function () {\n    var _this = this;\n    var ids = this.getRootIdSet();\n    var snapshot = this.toObject();\n    ids.forEach(function (id) {\n      if (hasOwn.call(snapshot, id)) {\n        // Because we are iterating over an ECMAScript Set, the IDs we add here\n        // will be visited in later iterations of the forEach loop only if they\n        // were not previously contained by the Set.\n        Object.keys(_this.findChildRefIds(id)).forEach(ids.add, ids);\n        // By removing IDs from the snapshot object here, we protect them from\n        // getting removed from the root store layer below.\n        delete snapshot[id];\n      }\n    });\n    var idsToRemove = Object.keys(snapshot);\n    if (idsToRemove.length) {\n      var root_1 = this;\n      while (root_1 instanceof Layer) root_1 = root_1.parent;\n      idsToRemove.forEach(function (id) {\n        return root_1.delete(id);\n      });\n    }\n    return idsToRemove;\n  };\n  EntityStore.prototype.findChildRefIds = function (dataId) {\n    if (!hasOwn.call(this.refs, dataId)) {\n      var found_1 = this.refs[dataId] = Object.create(null);\n      var root = this.data[dataId];\n      if (!root) return found_1;\n      var workSet_1 = new Set([root]);\n      // Within the store, only arrays and objects can contain child entity\n      // references, so we can prune the traversal using this predicate:\n      workSet_1.forEach(function (obj) {\n        if (isReference(obj)) {\n          found_1[obj.__ref] = true;\n          // In rare cases, a { __ref } Reference object may have other fields.\n          // This often indicates a mismerging of References with StoreObjects,\n          // but garbage collection should not be fooled by a stray __ref\n          // property in a StoreObject (ignoring all the other fields just\n          // because the StoreObject looks like a Reference). To avoid this\n          // premature termination of findChildRefIds recursion, we fall through\n          // to the code below, which will handle any other properties of obj.\n        }\n\n        if (isNonNullObject(obj)) {\n          Object.keys(obj).forEach(function (key) {\n            var child = obj[key];\n            // No need to add primitive values to the workSet, since they cannot\n            // contain reference objects.\n            if (isNonNullObject(child)) {\n              workSet_1.add(child);\n            }\n          });\n        }\n      });\n    }\n    return this.refs[dataId];\n  };\n  EntityStore.prototype.makeCacheKey = function () {\n    return this.group.keyMaker.lookupArray(arguments);\n  };\n  return EntityStore;\n}();\nexport { EntityStore };\n// A single CacheGroup represents a set of one or more EntityStore objects,\n// typically the Root store in a CacheGroup by itself, and all active Layer\n// stores in a group together. A single EntityStore object belongs to only\n// one CacheGroup, store.group. The CacheGroup is responsible for tracking\n// dependencies, so store.group is helpful for generating unique keys for\n// cached results that need to be invalidated when/if those dependencies\n// change. If we used the EntityStore objects themselves as cache keys (that\n// is, store rather than store.group), the cache would become unnecessarily\n// fragmented by all the different Layer objects. Instead, the CacheGroup\n// approach allows all optimistic Layer objects in the same linked list to\n// belong to one CacheGroup, with the non-optimistic Root object belonging\n// to another CacheGroup, allowing resultCaching dependencies to be tracked\n// separately for optimistic and non-optimistic entity data.\nvar CacheGroup = /** @class */function () {\n  function CacheGroup(caching, parent) {\n    if (parent === void 0) {\n      parent = null;\n    }\n    this.caching = caching;\n    this.parent = parent;\n    this.d = null;\n    this.resetCaching();\n  }\n  CacheGroup.prototype.resetCaching = function () {\n    this.d = this.caching ? dep() : null;\n    this.keyMaker = new Trie(canUseWeakMap);\n  };\n  CacheGroup.prototype.depend = function (dataId, storeFieldName) {\n    if (this.d) {\n      this.d(makeDepKey(dataId, storeFieldName));\n      var fieldName = fieldNameFromStoreName(storeFieldName);\n      if (fieldName !== storeFieldName) {\n        // Fields with arguments that contribute extra identifying\n        // information to the fieldName (thus forming the storeFieldName)\n        // depend not only on the full storeFieldName but also on the\n        // short fieldName, so the field can be invalidated using either\n        // level of specificity.\n        this.d(makeDepKey(dataId, fieldName));\n      }\n      if (this.parent) {\n        this.parent.depend(dataId, storeFieldName);\n      }\n    }\n  };\n  CacheGroup.prototype.dirty = function (dataId, storeFieldName) {\n    if (this.d) {\n      this.d.dirty(makeDepKey(dataId, storeFieldName),\n      // When storeFieldName === \"__exists\", that means the entity identified\n      // by dataId has either disappeared from the cache or was newly added,\n      // so the result caching system would do well to \"forget everything it\n      // knows\" about that object. To achieve that kind of invalidation, we\n      // not only dirty the associated result cache entry, but also remove it\n      // completely from the dependency graph. For the optimism implementation\n      // details, see https://github.com/benjamn/optimism/pull/195.\n      storeFieldName === \"__exists\" ? \"forget\" : \"setDirty\");\n    }\n  };\n  return CacheGroup;\n}();\nfunction makeDepKey(dataId, storeFieldName) {\n  // Since field names cannot have '#' characters in them, this method\n  // of joining the field name and the ID should be unambiguous, and much\n  // cheaper than JSON.stringify([dataId, fieldName]).\n  return storeFieldName + \"#\" + dataId;\n}\nexport function maybeDependOnExistenceOfEntity(store, entityId) {\n  if (supportsResultCaching(store)) {\n    // We use this pseudo-field __exists elsewhere in the EntityStore code to\n    // represent changes in the existence of the entity object identified by\n    // entityId. This dependency gets reliably dirtied whenever an object with\n    // this ID is deleted (or newly created) within this group, so any result\n    // cache entries (for example, StoreReader#executeSelectionSet results) that\n    // depend on __exists for this entityId will get dirtied as well, leading to\n    // the eventual recomputation (instead of reuse) of those result objects the\n    // next time someone reads them from the cache.\n    store.group.depend(entityId, \"__exists\");\n  }\n}\n(function (EntityStore) {\n  // Refer to this class as EntityStore.Root outside this namespace.\n  var Root = /** @class */function (_super) {\n    __extends(Root, _super);\n    function Root(_a) {\n      var policies = _a.policies,\n        _b = _a.resultCaching,\n        resultCaching = _b === void 0 ? true : _b,\n        seed = _a.seed;\n      var _this = _super.call(this, policies, new CacheGroup(resultCaching)) || this;\n      _this.stump = new Stump(_this);\n      _this.storageTrie = new Trie(canUseWeakMap);\n      if (seed) _this.replace(seed);\n      return _this;\n    }\n    Root.prototype.addLayer = function (layerId, replay) {\n      // Adding an optimistic Layer on top of the Root actually adds the Layer\n      // on top of the Stump, so the Stump always comes between the Root and\n      // any Layer objects that we've added.\n      return this.stump.addLayer(layerId, replay);\n    };\n    Root.prototype.removeLayer = function () {\n      // Never remove the root layer.\n      return this;\n    };\n    Root.prototype.getStorage = function () {\n      return this.storageTrie.lookupArray(arguments);\n    };\n    return Root;\n  }(EntityStore);\n  EntityStore.Root = Root;\n})(EntityStore || (EntityStore = {}));\n// Not exported, since all Layer instances are created by the addLayer method\n// of the EntityStore.Root class.\nvar Layer = /** @class */function (_super) {\n  __extends(Layer, _super);\n  function Layer(id, parent, replay, group) {\n    var _this = _super.call(this, parent.policies, group) || this;\n    _this.id = id;\n    _this.parent = parent;\n    _this.replay = replay;\n    _this.group = group;\n    replay(_this);\n    return _this;\n  }\n  Layer.prototype.addLayer = function (layerId, replay) {\n    return new Layer(layerId, this, replay, this.group);\n  };\n  Layer.prototype.removeLayer = function (layerId) {\n    var _this = this;\n    // Remove all instances of the given id, not just the first one.\n    var parent = this.parent.removeLayer(layerId);\n    if (layerId === this.id) {\n      if (this.group.caching) {\n        // Dirty every ID we're removing. Technically we might be able to avoid\n        // dirtying fields that have values in higher layers, but we don't have\n        // easy access to higher layers here, and we're about to recreate those\n        // layers anyway (see parent.addLayer below).\n        Object.keys(this.data).forEach(function (dataId) {\n          var ownStoreObject = _this.data[dataId];\n          var parentStoreObject = parent[\"lookup\"](dataId);\n          if (!parentStoreObject) {\n            // The StoreObject identified by dataId was defined in this layer\n            // but will be undefined in the parent layer, so we can delete the\n            // whole entity using this.delete(dataId). Since we're about to\n            // throw this layer away, the only goal of this deletion is to dirty\n            // the removed fields.\n            _this.delete(dataId);\n          } else if (!ownStoreObject) {\n            // This layer had an entry for dataId but it was undefined, which\n            // means the entity was deleted in this layer, and it's about to\n            // become undeleted when we remove this layer, so we need to dirty\n            // all fields that are about to be reexposed.\n            _this.group.dirty(dataId, \"__exists\");\n            Object.keys(parentStoreObject).forEach(function (storeFieldName) {\n              _this.group.dirty(dataId, storeFieldName);\n            });\n          } else if (ownStoreObject !== parentStoreObject) {\n            // If ownStoreObject is not exactly the same as parentStoreObject,\n            // dirty any fields whose values will change as a result of this\n            // removal.\n            Object.keys(ownStoreObject).forEach(function (storeFieldName) {\n              if (!equal(ownStoreObject[storeFieldName], parentStoreObject[storeFieldName])) {\n                _this.group.dirty(dataId, storeFieldName);\n              }\n            });\n          }\n        });\n      }\n      return parent;\n    }\n    // No changes are necessary if the parent chain remains identical.\n    if (parent === this.parent) return this;\n    // Recreate this layer on top of the new parent.\n    return parent.addLayer(this.id, this.replay);\n  };\n  Layer.prototype.toObject = function () {\n    return __assign(__assign({}, this.parent.toObject()), this.data);\n  };\n  Layer.prototype.findChildRefIds = function (dataId) {\n    var fromParent = this.parent.findChildRefIds(dataId);\n    return hasOwn.call(this.data, dataId) ? __assign(__assign({}, fromParent), _super.prototype.findChildRefIds.call(this, dataId)) : fromParent;\n  };\n  Layer.prototype.getStorage = function () {\n    var p = this.parent;\n    while (p.parent) p = p.parent;\n    return p.getStorage.apply(p,\n    // @ts-expect-error\n    arguments);\n  };\n  return Layer;\n}(EntityStore);\n// Represents a Layer permanently installed just above the Root, which allows\n// reading optimistically (and registering optimistic dependencies) even when\n// no optimistic layers are currently active. The stump.group CacheGroup object\n// is shared by any/all Layer objects added on top of the Stump.\nvar Stump = /** @class */function (_super) {\n  __extends(Stump, _super);\n  function Stump(root) {\n    return _super.call(this, \"EntityStore.Stump\", root, function () {}, new CacheGroup(root.group.caching, root.group)) || this;\n  }\n  Stump.prototype.removeLayer = function () {\n    // Never remove the Stump layer.\n    return this;\n  };\n  Stump.prototype.merge = function (older, newer) {\n    // We never want to write any data into the Stump, so we forward any merge\n    // calls to the Root instead. Another option here would be to throw an\n    // exception, but the toReference(object, true) function can sometimes\n    // trigger Stump writes (which used to be Root writes, before the Stump\n    // concept was introduced).\n    return this.parent.merge(older, newer);\n  };\n  return Stump;\n}(Layer);\nfunction storeObjectReconciler(existingObject, incomingObject, property) {\n  var existingValue = existingObject[property];\n  var incomingValue = incomingObject[property];\n  // Wherever there is a key collision, prefer the incoming value, unless\n  // it is deeply equal to the existing value. It's worth checking deep\n  // equality here (even though blindly returning incoming would be\n  // logically correct) because preserving the referential identity of\n  // existing data can prevent needless rereading and rerendering.\n  return equal(existingValue, incomingValue) ? existingValue : incomingValue;\n}\nexport function supportsResultCaching(store) {\n  // When result caching is disabled, store.depend will be null.\n  return !!(store instanceof EntityStore && store.group.caching);\n}", "map": {"version": 3, "names": ["__assign", "__extends", "__rest", "invariant", "dep", "equal", "<PERSON><PERSON>", "isReference", "makeReference", "DeepMerger", "maybeDeepFreeze", "canUseWeakMap", "isNonNullObject", "hasOwn", "fieldNameFromStoreName", "DELETE", "Object", "create", "delModifier", "INVALIDATE", "EntityStore", "policies", "group", "_this", "data", "rootIds", "refs", "getFieldValue", "objectOrReference", "storeFieldName", "get", "__ref", "canRead", "objOrRef", "has", "toReference", "objOrIdOrRef", "mergeIntoStore", "id", "identify", "ref", "merge", "prototype", "toObject", "dataId", "lookup", "fieldName", "depend", "call", "storeObject", "rootTypenamesById", "Layer", "parent", "dependOnExistence", "older", "newer", "existing", "incoming", "merged", "storeObjectReconciler", "caching", "fieldsToDirty_1", "__exists", "keys", "for<PERSON>ach", "has<PERSON>eyArg<PERSON>", "__typename", "dirty", "modify", "fields", "changedFields_1", "needToMerge_1", "allDeleted_1", "sharedDetails_1", "readField", "fieldNameOrOptions", "from", "store", "fieldValue", "newValue", "storage", "getStorage", "globalThis", "__DEV__", "checkReference", "undefined", "warn", "Array", "isArray", "seenReference", "someNonReference", "_i", "newValue_1", "length", "value", "delete", "args", "_a", "typename", "getStoreFieldName", "evict", "options", "limit", "evicted", "clear", "replace", "extract", "obj", "extraRootIds", "getRootIdSet", "push", "__META", "sort", "newData", "rest_1", "retain", "rootId", "release", "count", "ids", "Set", "add", "gc", "snapshot", "findChildRefIds", "idsToRemove", "root_1", "found_1", "root", "workSet_1", "key", "child", "make<PERSON><PERSON><PERSON><PERSON>", "keyMaker", "lookupArray", "arguments", "CacheGroup", "d", "resetCaching", "makeDepKey", "maybeDependOnExistenceOfEntity", "entityId", "supportsResultCaching", "Root", "_super", "_b", "resultCaching", "seed", "stump", "Stump", "storageTrie", "add<PERSON><PERSON>er", "layerId", "replay", "<PERSON><PERSON><PERSON>er", "ownStoreObject", "parentStoreObject", "fromParent", "p", "apply", "existingObject", "incomingObject", "property", "existingValue", "incomingValue"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/entityStore.js"], "sourcesContent": ["import { __assign, __extends, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { dep } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { isReference, makeReference, DeepMerger, maybeDeepFreeze, canUseWeakMap, isNonNullObject, } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName } from \"./helpers.js\";\nvar DELETE = Object.create(null);\nvar delModifier = function () { return DELETE; };\nvar INVALIDATE = Object.create(null);\nvar EntityStore = /** @class */ (function () {\n    function EntityStore(policies, group) {\n        var _this = this;\n        this.policies = policies;\n        this.group = group;\n        this.data = Object.create(null);\n        // Maps root entity IDs to the number of times they have been retained, minus\n        // the number of times they have been released. Retained entities keep other\n        // entities they reference (even indirectly) from being garbage collected.\n        this.rootIds = Object.create(null);\n        // Lazily tracks { __ref: <dataId> } strings contained by this.data[dataId].\n        this.refs = Object.create(null);\n        // Bound function that can be passed around to provide easy access to fields\n        // of Reference objects as well as ordinary objects.\n        this.getFieldValue = function (objectOrReference, storeFieldName) {\n            return maybeDeepFreeze(isReference(objectOrReference) ?\n                _this.get(objectOrReference.__ref, storeFieldName)\n                : objectOrReference && objectOrReference[storeFieldName]);\n        };\n        // Returns true for non-normalized StoreObjects and non-dangling\n        // References, indicating that readField(name, objOrRef) has a chance of\n        // working. Useful for filtering out dangling references from lists.\n        this.canRead = function (objOrRef) {\n            return isReference(objOrRef) ?\n                _this.has(objOrRef.__ref)\n                : typeof objOrRef === \"object\";\n        };\n        // Bound function that converts an id or an object with a __typename and\n        // primary key fields to a Reference object. If called with a Reference object,\n        // that same Reference object is returned. Pass true for mergeIntoStore to persist\n        // an object into the store.\n        this.toReference = function (objOrIdOrRef, mergeIntoStore) {\n            if (typeof objOrIdOrRef === \"string\") {\n                return makeReference(objOrIdOrRef);\n            }\n            if (isReference(objOrIdOrRef)) {\n                return objOrIdOrRef;\n            }\n            var id = _this.policies.identify(objOrIdOrRef)[0];\n            if (id) {\n                var ref = makeReference(id);\n                if (mergeIntoStore) {\n                    _this.merge(id, objOrIdOrRef);\n                }\n                return ref;\n            }\n        };\n    }\n    // Although the EntityStore class is abstract, it contains concrete\n    // implementations of the various NormalizedCache interface methods that\n    // are inherited by the Root and Layer subclasses.\n    EntityStore.prototype.toObject = function () {\n        return __assign({}, this.data);\n    };\n    EntityStore.prototype.has = function (dataId) {\n        return this.lookup(dataId, true) !== void 0;\n    };\n    EntityStore.prototype.get = function (dataId, fieldName) {\n        this.group.depend(dataId, fieldName);\n        if (hasOwn.call(this.data, dataId)) {\n            var storeObject = this.data[dataId];\n            if (storeObject && hasOwn.call(storeObject, fieldName)) {\n                return storeObject[fieldName];\n            }\n        }\n        if (fieldName === \"__typename\" &&\n            hasOwn.call(this.policies.rootTypenamesById, dataId)) {\n            return this.policies.rootTypenamesById[dataId];\n        }\n        if (this instanceof Layer) {\n            return this.parent.get(dataId, fieldName);\n        }\n    };\n    EntityStore.prototype.lookup = function (dataId, dependOnExistence) {\n        // The has method (above) calls lookup with dependOnExistence = true, so\n        // that it can later be invalidated when we add or remove a StoreObject for\n        // this dataId. Any consumer who cares about the contents of the StoreObject\n        // should not rely on this dependency, since the contents could change\n        // without the object being added or removed.\n        if (dependOnExistence)\n            this.group.depend(dataId, \"__exists\");\n        if (hasOwn.call(this.data, dataId)) {\n            return this.data[dataId];\n        }\n        if (this instanceof Layer) {\n            return this.parent.lookup(dataId, dependOnExistence);\n        }\n        if (this.policies.rootTypenamesById[dataId]) {\n            return Object.create(null);\n        }\n    };\n    EntityStore.prototype.merge = function (older, newer) {\n        var _this = this;\n        var dataId;\n        // Convert unexpected references to ID strings.\n        if (isReference(older))\n            older = older.__ref;\n        if (isReference(newer))\n            newer = newer.__ref;\n        var existing = typeof older === \"string\" ? this.lookup((dataId = older)) : older;\n        var incoming = typeof newer === \"string\" ? this.lookup((dataId = newer)) : newer;\n        // If newer was a string ID, but that ID was not defined in this store,\n        // then there are no fields to be merged, so we're done.\n        if (!incoming)\n            return;\n        invariant(typeof dataId === \"string\", 2);\n        var merged = new DeepMerger(storeObjectReconciler).merge(existing, incoming);\n        // Even if merged === existing, existing may have come from a lower\n        // layer, so we always need to set this.data[dataId] on this level.\n        this.data[dataId] = merged;\n        if (merged !== existing) {\n            delete this.refs[dataId];\n            if (this.group.caching) {\n                var fieldsToDirty_1 = Object.create(null);\n                // If we added a new StoreObject where there was previously none, dirty\n                // anything that depended on the existence of this dataId, such as the\n                // EntityStore#has method.\n                if (!existing)\n                    fieldsToDirty_1.__exists = 1;\n                // Now invalidate dependents who called getFieldValue for any fields\n                // that are changing as a result of this merge.\n                Object.keys(incoming).forEach(function (storeFieldName) {\n                    if (!existing ||\n                        existing[storeFieldName] !== merged[storeFieldName]) {\n                        // Always dirty the full storeFieldName, which may include\n                        // serialized arguments following the fieldName prefix.\n                        fieldsToDirty_1[storeFieldName] = 1;\n                        // Also dirty fieldNameFromStoreName(storeFieldName) if it's\n                        // different from storeFieldName and this field does not have\n                        // keyArgs configured, because that means the cache can't make\n                        // any assumptions about how field values with the same field\n                        // name but different arguments might be interrelated, so it\n                        // must err on the side of invalidating all field values that\n                        // share the same short fieldName, regardless of arguments.\n                        var fieldName = fieldNameFromStoreName(storeFieldName);\n                        if (fieldName !== storeFieldName &&\n                            !_this.policies.hasKeyArgs(merged.__typename, fieldName)) {\n                            fieldsToDirty_1[fieldName] = 1;\n                        }\n                        // If merged[storeFieldName] has become undefined, and this is the\n                        // Root layer, actually delete the property from the merged object,\n                        // which is guaranteed to have been created fresh in this method.\n                        if (merged[storeFieldName] === void 0 && !(_this instanceof Layer)) {\n                            delete merged[storeFieldName];\n                        }\n                    }\n                });\n                if (fieldsToDirty_1.__typename &&\n                    !(existing && existing.__typename) &&\n                    // Since we return default root __typename strings\n                    // automatically from store.get, we don't need to dirty the\n                    // ROOT_QUERY.__typename field if merged.__typename is equal\n                    // to the default string (usually \"Query\").\n                    this.policies.rootTypenamesById[dataId] === merged.__typename) {\n                    delete fieldsToDirty_1.__typename;\n                }\n                Object.keys(fieldsToDirty_1).forEach(function (fieldName) {\n                    return _this.group.dirty(dataId, fieldName);\n                });\n            }\n        }\n    };\n    EntityStore.prototype.modify = function (dataId, fields) {\n        var _this = this;\n        var storeObject = this.lookup(dataId);\n        if (storeObject) {\n            var changedFields_1 = Object.create(null);\n            var needToMerge_1 = false;\n            var allDeleted_1 = true;\n            var sharedDetails_1 = {\n                DELETE: DELETE,\n                INVALIDATE: INVALIDATE,\n                isReference: isReference,\n                toReference: this.toReference,\n                canRead: this.canRead,\n                readField: function (fieldNameOrOptions, from) {\n                    return _this.policies.readField(typeof fieldNameOrOptions === \"string\" ?\n                        {\n                            fieldName: fieldNameOrOptions,\n                            from: from || makeReference(dataId),\n                        }\n                        : fieldNameOrOptions, { store: _this });\n                },\n            };\n            Object.keys(storeObject).forEach(function (storeFieldName) {\n                var fieldName = fieldNameFromStoreName(storeFieldName);\n                var fieldValue = storeObject[storeFieldName];\n                if (fieldValue === void 0)\n                    return;\n                var modify = typeof fields === \"function\" ? fields : (fields[storeFieldName] || fields[fieldName]);\n                if (modify) {\n                    var newValue = modify === delModifier ? DELETE : (modify(maybeDeepFreeze(fieldValue), __assign(__assign({}, sharedDetails_1), { fieldName: fieldName, storeFieldName: storeFieldName, storage: _this.getStorage(dataId, storeFieldName) })));\n                    if (newValue === INVALIDATE) {\n                        _this.group.dirty(dataId, storeFieldName);\n                    }\n                    else {\n                        if (newValue === DELETE)\n                            newValue = void 0;\n                        if (newValue !== fieldValue) {\n                            changedFields_1[storeFieldName] = newValue;\n                            needToMerge_1 = true;\n                            fieldValue = newValue;\n                            if (globalThis.__DEV__ !== false) {\n                                var checkReference = function (ref) {\n                                    if (_this.lookup(ref.__ref) === undefined) {\n                                        globalThis.__DEV__ !== false && invariant.warn(3, ref);\n                                        return true;\n                                    }\n                                };\n                                if (isReference(newValue)) {\n                                    checkReference(newValue);\n                                }\n                                else if (Array.isArray(newValue)) {\n                                    // Warn about writing \"mixed\" arrays of Reference and non-Reference objects\n                                    var seenReference = false;\n                                    var someNonReference = void 0;\n                                    for (var _i = 0, newValue_1 = newValue; _i < newValue_1.length; _i++) {\n                                        var value = newValue_1[_i];\n                                        if (isReference(value)) {\n                                            seenReference = true;\n                                            if (checkReference(value))\n                                                break;\n                                        }\n                                        else {\n                                            // Do not warn on primitive values, since those could never be represented\n                                            // by a reference. This is a valid (albeit uncommon) use case.\n                                            if (typeof value === \"object\" && !!value) {\n                                                var id = _this.policies.identify(value)[0];\n                                                // check if object could even be referenced, otherwise we are not interested in it for this warning\n                                                if (id) {\n                                                    someNonReference = value;\n                                                }\n                                            }\n                                        }\n                                        if (seenReference && someNonReference !== undefined) {\n                                            globalThis.__DEV__ !== false && invariant.warn(4, someNonReference);\n                                            break;\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                if (fieldValue !== void 0) {\n                    allDeleted_1 = false;\n                }\n            });\n            if (needToMerge_1) {\n                this.merge(dataId, changedFields_1);\n                if (allDeleted_1) {\n                    if (this instanceof Layer) {\n                        this.data[dataId] = void 0;\n                    }\n                    else {\n                        delete this.data[dataId];\n                    }\n                    this.group.dirty(dataId, \"__exists\");\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    // If called with only one argument, removes the entire entity\n    // identified by dataId. If called with a fieldName as well, removes all\n    // fields of that entity whose names match fieldName according to the\n    // fieldNameFromStoreName helper function. If called with a fieldName\n    // and variables, removes all fields of that entity whose names match fieldName\n    // and whose arguments when cached exactly match the variables passed.\n    EntityStore.prototype.delete = function (dataId, fieldName, args) {\n        var _a;\n        var storeObject = this.lookup(dataId);\n        if (storeObject) {\n            var typename = this.getFieldValue(storeObject, \"__typename\");\n            var storeFieldName = fieldName && args ?\n                this.policies.getStoreFieldName({ typename: typename, fieldName: fieldName, args: args })\n                : fieldName;\n            return this.modify(dataId, storeFieldName ? (_a = {},\n                _a[storeFieldName] = delModifier,\n                _a) : delModifier);\n        }\n        return false;\n    };\n    EntityStore.prototype.evict = function (options, limit) {\n        var evicted = false;\n        if (options.id) {\n            if (hasOwn.call(this.data, options.id)) {\n                evicted = this.delete(options.id, options.fieldName, options.args);\n            }\n            if (this instanceof Layer && this !== limit) {\n                evicted = this.parent.evict(options, limit) || evicted;\n            }\n            // Always invalidate the field to trigger rereading of watched\n            // queries, even if no cache data was modified by the eviction,\n            // because queries may depend on computed fields with custom read\n            // functions, whose values are not stored in the EntityStore.\n            if (options.fieldName || evicted) {\n                this.group.dirty(options.id, options.fieldName || \"__exists\");\n            }\n        }\n        return evicted;\n    };\n    EntityStore.prototype.clear = function () {\n        this.replace(null);\n    };\n    EntityStore.prototype.extract = function () {\n        var _this = this;\n        var obj = this.toObject();\n        var extraRootIds = [];\n        this.getRootIdSet().forEach(function (id) {\n            if (!hasOwn.call(_this.policies.rootTypenamesById, id)) {\n                extraRootIds.push(id);\n            }\n        });\n        if (extraRootIds.length) {\n            obj.__META = { extraRootIds: extraRootIds.sort() };\n        }\n        return obj;\n    };\n    EntityStore.prototype.replace = function (newData) {\n        var _this = this;\n        Object.keys(this.data).forEach(function (dataId) {\n            if (!(newData && hasOwn.call(newData, dataId))) {\n                _this.delete(dataId);\n            }\n        });\n        if (newData) {\n            var __META = newData.__META, rest_1 = __rest(newData, [\"__META\"]);\n            Object.keys(rest_1).forEach(function (dataId) {\n                _this.merge(dataId, rest_1[dataId]);\n            });\n            if (__META) {\n                __META.extraRootIds.forEach(this.retain, this);\n            }\n        }\n    };\n    EntityStore.prototype.retain = function (rootId) {\n        return (this.rootIds[rootId] = (this.rootIds[rootId] || 0) + 1);\n    };\n    EntityStore.prototype.release = function (rootId) {\n        if (this.rootIds[rootId] > 0) {\n            var count = --this.rootIds[rootId];\n            if (!count)\n                delete this.rootIds[rootId];\n            return count;\n        }\n        return 0;\n    };\n    // Return a Set<string> of all the ID strings that have been retained by\n    // this layer/root *and* any layers/roots beneath it.\n    EntityStore.prototype.getRootIdSet = function (ids) {\n        if (ids === void 0) { ids = new Set(); }\n        Object.keys(this.rootIds).forEach(ids.add, ids);\n        if (this instanceof Layer) {\n            this.parent.getRootIdSet(ids);\n        }\n        else {\n            // Official singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n            // always considered roots for garbage collection, regardless of\n            // their retainment counts in this.rootIds.\n            Object.keys(this.policies.rootTypenamesById).forEach(ids.add, ids);\n        }\n        return ids;\n    };\n    // The goal of garbage collection is to remove IDs from the Root layer of the\n    // store that are no longer reachable starting from any IDs that have been\n    // explicitly retained (see retain and release, above). Returns an array of\n    // dataId strings that were removed from the store.\n    EntityStore.prototype.gc = function () {\n        var _this = this;\n        var ids = this.getRootIdSet();\n        var snapshot = this.toObject();\n        ids.forEach(function (id) {\n            if (hasOwn.call(snapshot, id)) {\n                // Because we are iterating over an ECMAScript Set, the IDs we add here\n                // will be visited in later iterations of the forEach loop only if they\n                // were not previously contained by the Set.\n                Object.keys(_this.findChildRefIds(id)).forEach(ids.add, ids);\n                // By removing IDs from the snapshot object here, we protect them from\n                // getting removed from the root store layer below.\n                delete snapshot[id];\n            }\n        });\n        var idsToRemove = Object.keys(snapshot);\n        if (idsToRemove.length) {\n            var root_1 = this;\n            while (root_1 instanceof Layer)\n                root_1 = root_1.parent;\n            idsToRemove.forEach(function (id) { return root_1.delete(id); });\n        }\n        return idsToRemove;\n    };\n    EntityStore.prototype.findChildRefIds = function (dataId) {\n        if (!hasOwn.call(this.refs, dataId)) {\n            var found_1 = (this.refs[dataId] = Object.create(null));\n            var root = this.data[dataId];\n            if (!root)\n                return found_1;\n            var workSet_1 = new Set([root]);\n            // Within the store, only arrays and objects can contain child entity\n            // references, so we can prune the traversal using this predicate:\n            workSet_1.forEach(function (obj) {\n                if (isReference(obj)) {\n                    found_1[obj.__ref] = true;\n                    // In rare cases, a { __ref } Reference object may have other fields.\n                    // This often indicates a mismerging of References with StoreObjects,\n                    // but garbage collection should not be fooled by a stray __ref\n                    // property in a StoreObject (ignoring all the other fields just\n                    // because the StoreObject looks like a Reference). To avoid this\n                    // premature termination of findChildRefIds recursion, we fall through\n                    // to the code below, which will handle any other properties of obj.\n                }\n                if (isNonNullObject(obj)) {\n                    Object.keys(obj).forEach(function (key) {\n                        var child = obj[key];\n                        // No need to add primitive values to the workSet, since they cannot\n                        // contain reference objects.\n                        if (isNonNullObject(child)) {\n                            workSet_1.add(child);\n                        }\n                    });\n                }\n            });\n        }\n        return this.refs[dataId];\n    };\n    EntityStore.prototype.makeCacheKey = function () {\n        return this.group.keyMaker.lookupArray(arguments);\n    };\n    return EntityStore;\n}());\nexport { EntityStore };\n// A single CacheGroup represents a set of one or more EntityStore objects,\n// typically the Root store in a CacheGroup by itself, and all active Layer\n// stores in a group together. A single EntityStore object belongs to only\n// one CacheGroup, store.group. The CacheGroup is responsible for tracking\n// dependencies, so store.group is helpful for generating unique keys for\n// cached results that need to be invalidated when/if those dependencies\n// change. If we used the EntityStore objects themselves as cache keys (that\n// is, store rather than store.group), the cache would become unnecessarily\n// fragmented by all the different Layer objects. Instead, the CacheGroup\n// approach allows all optimistic Layer objects in the same linked list to\n// belong to one CacheGroup, with the non-optimistic Root object belonging\n// to another CacheGroup, allowing resultCaching dependencies to be tracked\n// separately for optimistic and non-optimistic entity data.\nvar CacheGroup = /** @class */ (function () {\n    function CacheGroup(caching, parent) {\n        if (parent === void 0) { parent = null; }\n        this.caching = caching;\n        this.parent = parent;\n        this.d = null;\n        this.resetCaching();\n    }\n    CacheGroup.prototype.resetCaching = function () {\n        this.d = this.caching ? dep() : null;\n        this.keyMaker = new Trie(canUseWeakMap);\n    };\n    CacheGroup.prototype.depend = function (dataId, storeFieldName) {\n        if (this.d) {\n            this.d(makeDepKey(dataId, storeFieldName));\n            var fieldName = fieldNameFromStoreName(storeFieldName);\n            if (fieldName !== storeFieldName) {\n                // Fields with arguments that contribute extra identifying\n                // information to the fieldName (thus forming the storeFieldName)\n                // depend not only on the full storeFieldName but also on the\n                // short fieldName, so the field can be invalidated using either\n                // level of specificity.\n                this.d(makeDepKey(dataId, fieldName));\n            }\n            if (this.parent) {\n                this.parent.depend(dataId, storeFieldName);\n            }\n        }\n    };\n    CacheGroup.prototype.dirty = function (dataId, storeFieldName) {\n        if (this.d) {\n            this.d.dirty(makeDepKey(dataId, storeFieldName), \n            // When storeFieldName === \"__exists\", that means the entity identified\n            // by dataId has either disappeared from the cache or was newly added,\n            // so the result caching system would do well to \"forget everything it\n            // knows\" about that object. To achieve that kind of invalidation, we\n            // not only dirty the associated result cache entry, but also remove it\n            // completely from the dependency graph. For the optimism implementation\n            // details, see https://github.com/benjamn/optimism/pull/195.\n            storeFieldName === \"__exists\" ? \"forget\" : \"setDirty\");\n        }\n    };\n    return CacheGroup;\n}());\nfunction makeDepKey(dataId, storeFieldName) {\n    // Since field names cannot have '#' characters in them, this method\n    // of joining the field name and the ID should be unambiguous, and much\n    // cheaper than JSON.stringify([dataId, fieldName]).\n    return storeFieldName + \"#\" + dataId;\n}\nexport function maybeDependOnExistenceOfEntity(store, entityId) {\n    if (supportsResultCaching(store)) {\n        // We use this pseudo-field __exists elsewhere in the EntityStore code to\n        // represent changes in the existence of the entity object identified by\n        // entityId. This dependency gets reliably dirtied whenever an object with\n        // this ID is deleted (or newly created) within this group, so any result\n        // cache entries (for example, StoreReader#executeSelectionSet results) that\n        // depend on __exists for this entityId will get dirtied as well, leading to\n        // the eventual recomputation (instead of reuse) of those result objects the\n        // next time someone reads them from the cache.\n        store.group.depend(entityId, \"__exists\");\n    }\n}\n(function (EntityStore) {\n    // Refer to this class as EntityStore.Root outside this namespace.\n    var Root = /** @class */ (function (_super) {\n        __extends(Root, _super);\n        function Root(_a) {\n            var policies = _a.policies, _b = _a.resultCaching, resultCaching = _b === void 0 ? true : _b, seed = _a.seed;\n            var _this = _super.call(this, policies, new CacheGroup(resultCaching)) || this;\n            _this.stump = new Stump(_this);\n            _this.storageTrie = new Trie(canUseWeakMap);\n            if (seed)\n                _this.replace(seed);\n            return _this;\n        }\n        Root.prototype.addLayer = function (layerId, replay) {\n            // Adding an optimistic Layer on top of the Root actually adds the Layer\n            // on top of the Stump, so the Stump always comes between the Root and\n            // any Layer objects that we've added.\n            return this.stump.addLayer(layerId, replay);\n        };\n        Root.prototype.removeLayer = function () {\n            // Never remove the root layer.\n            return this;\n        };\n        Root.prototype.getStorage = function () {\n            return this.storageTrie.lookupArray(arguments);\n        };\n        return Root;\n    }(EntityStore));\n    EntityStore.Root = Root;\n})(EntityStore || (EntityStore = {}));\n// Not exported, since all Layer instances are created by the addLayer method\n// of the EntityStore.Root class.\nvar Layer = /** @class */ (function (_super) {\n    __extends(Layer, _super);\n    function Layer(id, parent, replay, group) {\n        var _this = _super.call(this, parent.policies, group) || this;\n        _this.id = id;\n        _this.parent = parent;\n        _this.replay = replay;\n        _this.group = group;\n        replay(_this);\n        return _this;\n    }\n    Layer.prototype.addLayer = function (layerId, replay) {\n        return new Layer(layerId, this, replay, this.group);\n    };\n    Layer.prototype.removeLayer = function (layerId) {\n        var _this = this;\n        // Remove all instances of the given id, not just the first one.\n        var parent = this.parent.removeLayer(layerId);\n        if (layerId === this.id) {\n            if (this.group.caching) {\n                // Dirty every ID we're removing. Technically we might be able to avoid\n                // dirtying fields that have values in higher layers, but we don't have\n                // easy access to higher layers here, and we're about to recreate those\n                // layers anyway (see parent.addLayer below).\n                Object.keys(this.data).forEach(function (dataId) {\n                    var ownStoreObject = _this.data[dataId];\n                    var parentStoreObject = parent[\"lookup\"](dataId);\n                    if (!parentStoreObject) {\n                        // The StoreObject identified by dataId was defined in this layer\n                        // but will be undefined in the parent layer, so we can delete the\n                        // whole entity using this.delete(dataId). Since we're about to\n                        // throw this layer away, the only goal of this deletion is to dirty\n                        // the removed fields.\n                        _this.delete(dataId);\n                    }\n                    else if (!ownStoreObject) {\n                        // This layer had an entry for dataId but it was undefined, which\n                        // means the entity was deleted in this layer, and it's about to\n                        // become undeleted when we remove this layer, so we need to dirty\n                        // all fields that are about to be reexposed.\n                        _this.group.dirty(dataId, \"__exists\");\n                        Object.keys(parentStoreObject).forEach(function (storeFieldName) {\n                            _this.group.dirty(dataId, storeFieldName);\n                        });\n                    }\n                    else if (ownStoreObject !== parentStoreObject) {\n                        // If ownStoreObject is not exactly the same as parentStoreObject,\n                        // dirty any fields whose values will change as a result of this\n                        // removal.\n                        Object.keys(ownStoreObject).forEach(function (storeFieldName) {\n                            if (!equal(ownStoreObject[storeFieldName], parentStoreObject[storeFieldName])) {\n                                _this.group.dirty(dataId, storeFieldName);\n                            }\n                        });\n                    }\n                });\n            }\n            return parent;\n        }\n        // No changes are necessary if the parent chain remains identical.\n        if (parent === this.parent)\n            return this;\n        // Recreate this layer on top of the new parent.\n        return parent.addLayer(this.id, this.replay);\n    };\n    Layer.prototype.toObject = function () {\n        return __assign(__assign({}, this.parent.toObject()), this.data);\n    };\n    Layer.prototype.findChildRefIds = function (dataId) {\n        var fromParent = this.parent.findChildRefIds(dataId);\n        return hasOwn.call(this.data, dataId) ? __assign(__assign({}, fromParent), _super.prototype.findChildRefIds.call(this, dataId)) : fromParent;\n    };\n    Layer.prototype.getStorage = function () {\n        var p = this.parent;\n        while (p.parent)\n            p = p.parent;\n        return p.getStorage.apply(p, \n        // @ts-expect-error\n        arguments);\n    };\n    return Layer;\n}(EntityStore));\n// Represents a Layer permanently installed just above the Root, which allows\n// reading optimistically (and registering optimistic dependencies) even when\n// no optimistic layers are currently active. The stump.group CacheGroup object\n// is shared by any/all Layer objects added on top of the Stump.\nvar Stump = /** @class */ (function (_super) {\n    __extends(Stump, _super);\n    function Stump(root) {\n        return _super.call(this, \"EntityStore.Stump\", root, function () { }, new CacheGroup(root.group.caching, root.group)) || this;\n    }\n    Stump.prototype.removeLayer = function () {\n        // Never remove the Stump layer.\n        return this;\n    };\n    Stump.prototype.merge = function (older, newer) {\n        // We never want to write any data into the Stump, so we forward any merge\n        // calls to the Root instead. Another option here would be to throw an\n        // exception, but the toReference(object, true) function can sometimes\n        // trigger Stump writes (which used to be Root writes, before the Stump\n        // concept was introduced).\n        return this.parent.merge(older, newer);\n    };\n    return Stump;\n}(Layer));\nfunction storeObjectReconciler(existingObject, incomingObject, property) {\n    var existingValue = existingObject[property];\n    var incomingValue = incomingObject[property];\n    // Wherever there is a key collision, prefer the incoming value, unless\n    // it is deeply equal to the existing value. It's worth checking deep\n    // equality here (even though blindly returning incoming would be\n    // logically correct) because preserving the referential identity of\n    // existing data can prevent needless rereading and rerendering.\n    return equal(existingValue, incomingValue) ? existingValue : incomingValue;\n}\nexport function supportsResultCaching(store) {\n    // When result caching is disabled, store.depend will be null.\n    return !!(store instanceof EntityStore && store.group.caching);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,GAAG,QAAQ,UAAU;AAC9B,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,WAAW,EAAEC,aAAa,EAAEC,UAAU,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,QAAS,0BAA0B;AACnI,SAASC,MAAM,EAAEC,sBAAsB,QAAQ,cAAc;AAC7D,IAAIC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAChC,IAAIC,WAAW,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAOH,MAAM;AAAE,CAAC;AAChD,IAAII,UAAU,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACpC,IAAIG,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,QAAQ,EAAEC,KAAK,EAAE;IAClC,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,IAAI,GAAGR,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA;IACA;IACA,IAAI,CAACQ,OAAO,GAAGT,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAClC;IACA,IAAI,CAACS,IAAI,GAAGV,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA;IACA,IAAI,CAACU,aAAa,GAAG,UAAUC,iBAAiB,EAAEC,cAAc,EAAE;MAC9D,OAAOnB,eAAe,CAACH,WAAW,CAACqB,iBAAiB,CAAC,GACjDL,KAAK,CAACO,GAAG,CAACF,iBAAiB,CAACG,KAAK,EAAEF,cAAc,CAAC,GAChDD,iBAAiB,IAAIA,iBAAiB,CAACC,cAAc,CAAC,CAAC;IACjE,CAAC;IACD;IACA;IACA;IACA,IAAI,CAACG,OAAO,GAAG,UAAUC,QAAQ,EAAE;MAC/B,OAAO1B,WAAW,CAAC0B,QAAQ,CAAC,GACxBV,KAAK,CAACW,GAAG,CAACD,QAAQ,CAACF,KAAK,CAAC,GACvB,OAAOE,QAAQ,KAAK,QAAQ;IACtC,CAAC;IACD;IACA;IACA;IACA;IACA,IAAI,CAACE,WAAW,GAAG,UAAUC,YAAY,EAAEC,cAAc,EAAE;MACvD,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;QAClC,OAAO5B,aAAa,CAAC4B,YAAY,CAAC;MACtC;MACA,IAAI7B,WAAW,CAAC6B,YAAY,CAAC,EAAE;QAC3B,OAAOA,YAAY;MACvB;MACA,IAAIE,EAAE,GAAGf,KAAK,CAACF,QAAQ,CAACkB,QAAQ,CAACH,YAAY,CAAC,CAAC,CAAC,CAAC;MACjD,IAAIE,EAAE,EAAE;QACJ,IAAIE,GAAG,GAAGhC,aAAa,CAAC8B,EAAE,CAAC;QAC3B,IAAID,cAAc,EAAE;UAChBd,KAAK,CAACkB,KAAK,CAACH,EAAE,EAAEF,YAAY,CAAC;QACjC;QACA,OAAOI,GAAG;MACd;IACJ,CAAC;EACL;EACA;EACA;EACA;EACApB,WAAW,CAACsB,SAAS,CAACC,QAAQ,GAAG,YAAY;IACzC,OAAO3C,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACwB,IAAI,CAAC;EAClC,CAAC;EACDJ,WAAW,CAACsB,SAAS,CAACR,GAAG,GAAG,UAAUU,MAAM,EAAE;IAC1C,OAAO,IAAI,CAACC,MAAM,CAACD,MAAM,EAAE,IAAI,CAAC,KAAK,KAAK,CAAC;EAC/C,CAAC;EACDxB,WAAW,CAACsB,SAAS,CAACZ,GAAG,GAAG,UAAUc,MAAM,EAAEE,SAAS,EAAE;IACrD,IAAI,CAACxB,KAAK,CAACyB,MAAM,CAACH,MAAM,EAAEE,SAAS,CAAC;IACpC,IAAIjC,MAAM,CAACmC,IAAI,CAAC,IAAI,CAACxB,IAAI,EAAEoB,MAAM,CAAC,EAAE;MAChC,IAAIK,WAAW,GAAG,IAAI,CAACzB,IAAI,CAACoB,MAAM,CAAC;MACnC,IAAIK,WAAW,IAAIpC,MAAM,CAACmC,IAAI,CAACC,WAAW,EAAEH,SAAS,CAAC,EAAE;QACpD,OAAOG,WAAW,CAACH,SAAS,CAAC;MACjC;IACJ;IACA,IAAIA,SAAS,KAAK,YAAY,IAC1BjC,MAAM,CAACmC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC6B,iBAAiB,EAAEN,MAAM,CAAC,EAAE;MACtD,OAAO,IAAI,CAACvB,QAAQ,CAAC6B,iBAAiB,CAACN,MAAM,CAAC;IAClD;IACA,IAAI,IAAI,YAAYO,KAAK,EAAE;MACvB,OAAO,IAAI,CAACC,MAAM,CAACtB,GAAG,CAACc,MAAM,EAAEE,SAAS,CAAC;IAC7C;EACJ,CAAC;EACD1B,WAAW,CAACsB,SAAS,CAACG,MAAM,GAAG,UAAUD,MAAM,EAAES,iBAAiB,EAAE;IAChE;IACA;IACA;IACA;IACA;IACA,IAAIA,iBAAiB,EACjB,IAAI,CAAC/B,KAAK,CAACyB,MAAM,CAACH,MAAM,EAAE,UAAU,CAAC;IACzC,IAAI/B,MAAM,CAACmC,IAAI,CAAC,IAAI,CAACxB,IAAI,EAAEoB,MAAM,CAAC,EAAE;MAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,MAAM,CAAC;IAC5B;IACA,IAAI,IAAI,YAAYO,KAAK,EAAE;MACvB,OAAO,IAAI,CAACC,MAAM,CAACP,MAAM,CAACD,MAAM,EAAES,iBAAiB,CAAC;IACxD;IACA,IAAI,IAAI,CAAChC,QAAQ,CAAC6B,iBAAiB,CAACN,MAAM,CAAC,EAAE;MACzC,OAAO5B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC9B;EACJ,CAAC;EACDG,WAAW,CAACsB,SAAS,CAACD,KAAK,GAAG,UAAUa,KAAK,EAAEC,KAAK,EAAE;IAClD,IAAIhC,KAAK,GAAG,IAAI;IAChB,IAAIqB,MAAM;IACV;IACA,IAAIrC,WAAW,CAAC+C,KAAK,CAAC,EAClBA,KAAK,GAAGA,KAAK,CAACvB,KAAK;IACvB,IAAIxB,WAAW,CAACgD,KAAK,CAAC,EAClBA,KAAK,GAAGA,KAAK,CAACxB,KAAK;IACvB,IAAIyB,QAAQ,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACT,MAAM,CAAED,MAAM,GAAGU,KAAM,CAAC,GAAGA,KAAK;IAChF,IAAIG,QAAQ,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACV,MAAM,CAAED,MAAM,GAAGW,KAAM,CAAC,GAAGA,KAAK;IAChF;IACA;IACA,IAAI,CAACE,QAAQ,EACT;IACJtD,SAAS,CAAC,OAAOyC,MAAM,KAAK,QAAQ,EAAE,CAAC,CAAC;IACxC,IAAIc,MAAM,GAAG,IAAIjD,UAAU,CAACkD,qBAAqB,CAAC,CAAClB,KAAK,CAACe,QAAQ,EAAEC,QAAQ,CAAC;IAC5E;IACA;IACA,IAAI,CAACjC,IAAI,CAACoB,MAAM,CAAC,GAAGc,MAAM;IAC1B,IAAIA,MAAM,KAAKF,QAAQ,EAAE;MACrB,OAAO,IAAI,CAAC9B,IAAI,CAACkB,MAAM,CAAC;MACxB,IAAI,IAAI,CAACtB,KAAK,CAACsC,OAAO,EAAE;QACpB,IAAIC,eAAe,GAAG7C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACzC;QACA;QACA;QACA,IAAI,CAACuC,QAAQ,EACTK,eAAe,CAACC,QAAQ,GAAG,CAAC;QAChC;QACA;QACA9C,MAAM,CAAC+C,IAAI,CAACN,QAAQ,CAAC,CAACO,OAAO,CAAC,UAAUnC,cAAc,EAAE;UACpD,IAAI,CAAC2B,QAAQ,IACTA,QAAQ,CAAC3B,cAAc,CAAC,KAAK6B,MAAM,CAAC7B,cAAc,CAAC,EAAE;YACrD;YACA;YACAgC,eAAe,CAAChC,cAAc,CAAC,GAAG,CAAC;YACnC;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIiB,SAAS,GAAGhC,sBAAsB,CAACe,cAAc,CAAC;YACtD,IAAIiB,SAAS,KAAKjB,cAAc,IAC5B,CAACN,KAAK,CAACF,QAAQ,CAAC4C,UAAU,CAACP,MAAM,CAACQ,UAAU,EAAEpB,SAAS,CAAC,EAAE;cAC1De,eAAe,CAACf,SAAS,CAAC,GAAG,CAAC;YAClC;YACA;YACA;YACA;YACA,IAAIY,MAAM,CAAC7B,cAAc,CAAC,KAAK,KAAK,CAAC,IAAI,EAAEN,KAAK,YAAY4B,KAAK,CAAC,EAAE;cAChE,OAAOO,MAAM,CAAC7B,cAAc,CAAC;YACjC;UACJ;QACJ,CAAC,CAAC;QACF,IAAIgC,eAAe,CAACK,UAAU,IAC1B,EAAEV,QAAQ,IAAIA,QAAQ,CAACU,UAAU,CAAC;QAClC;QACA;QACA;QACA;QACA,IAAI,CAAC7C,QAAQ,CAAC6B,iBAAiB,CAACN,MAAM,CAAC,KAAKc,MAAM,CAACQ,UAAU,EAAE;UAC/D,OAAOL,eAAe,CAACK,UAAU;QACrC;QACAlD,MAAM,CAAC+C,IAAI,CAACF,eAAe,CAAC,CAACG,OAAO,CAAC,UAAUlB,SAAS,EAAE;UACtD,OAAOvB,KAAK,CAACD,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAEE,SAAS,CAAC;QAC/C,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EACD1B,WAAW,CAACsB,SAAS,CAAC0B,MAAM,GAAG,UAAUxB,MAAM,EAAEyB,MAAM,EAAE;IACrD,IAAI9C,KAAK,GAAG,IAAI;IAChB,IAAI0B,WAAW,GAAG,IAAI,CAACJ,MAAM,CAACD,MAAM,CAAC;IACrC,IAAIK,WAAW,EAAE;MACb,IAAIqB,eAAe,GAAGtD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACzC,IAAIsD,aAAa,GAAG,KAAK;MACzB,IAAIC,YAAY,GAAG,IAAI;MACvB,IAAIC,eAAe,GAAG;QAClB1D,MAAM,EAAEA,MAAM;QACdI,UAAU,EAAEA,UAAU;QACtBZ,WAAW,EAAEA,WAAW;QACxB4B,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BH,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB0C,SAAS,EAAE,SAAAA,CAAUC,kBAAkB,EAAEC,IAAI,EAAE;UAC3C,OAAOrD,KAAK,CAACF,QAAQ,CAACqD,SAAS,CAAC,OAAOC,kBAAkB,KAAK,QAAQ,GAClE;YACI7B,SAAS,EAAE6B,kBAAkB;YAC7BC,IAAI,EAAEA,IAAI,IAAIpE,aAAa,CAACoC,MAAM;UACtC,CAAC,GACC+B,kBAAkB,EAAE;YAAEE,KAAK,EAAEtD;UAAM,CAAC,CAAC;QAC/C;MACJ,CAAC;MACDP,MAAM,CAAC+C,IAAI,CAACd,WAAW,CAAC,CAACe,OAAO,CAAC,UAAUnC,cAAc,EAAE;QACvD,IAAIiB,SAAS,GAAGhC,sBAAsB,CAACe,cAAc,CAAC;QACtD,IAAIiD,UAAU,GAAG7B,WAAW,CAACpB,cAAc,CAAC;QAC5C,IAAIiD,UAAU,KAAK,KAAK,CAAC,EACrB;QACJ,IAAIV,MAAM,GAAG,OAAOC,MAAM,KAAK,UAAU,GAAGA,MAAM,GAAIA,MAAM,CAACxC,cAAc,CAAC,IAAIwC,MAAM,CAACvB,SAAS,CAAE;QAClG,IAAIsB,MAAM,EAAE;UACR,IAAIW,QAAQ,GAAGX,MAAM,KAAKlD,WAAW,GAAGH,MAAM,GAAIqD,MAAM,CAAC1D,eAAe,CAACoE,UAAU,CAAC,EAAE9E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyE,eAAe,CAAC,EAAE;YAAE3B,SAAS,EAAEA,SAAS;YAAEjB,cAAc,EAAEA,cAAc;YAAEmD,OAAO,EAAEzD,KAAK,CAAC0D,UAAU,CAACrC,MAAM,EAAEf,cAAc;UAAE,CAAC,CAAC,CAAE;UAC5O,IAAIkD,QAAQ,KAAK5D,UAAU,EAAE;YACzBI,KAAK,CAACD,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAEf,cAAc,CAAC;UAC7C,CAAC,MACI;YACD,IAAIkD,QAAQ,KAAKhE,MAAM,EACnBgE,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAIA,QAAQ,KAAKD,UAAU,EAAE;cACzBR,eAAe,CAACzC,cAAc,CAAC,GAAGkD,QAAQ;cAC1CR,aAAa,GAAG,IAAI;cACpBO,UAAU,GAAGC,QAAQ;cACrB,IAAIG,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;gBAC9B,IAAIC,cAAc,GAAG,SAAAA,CAAU5C,GAAG,EAAE;kBAChC,IAAIjB,KAAK,CAACsB,MAAM,CAACL,GAAG,CAACT,KAAK,CAAC,KAAKsD,SAAS,EAAE;oBACvCH,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIhF,SAAS,CAACmF,IAAI,CAAC,CAAC,EAAE9C,GAAG,CAAC;oBACtD,OAAO,IAAI;kBACf;gBACJ,CAAC;gBACD,IAAIjC,WAAW,CAACwE,QAAQ,CAAC,EAAE;kBACvBK,cAAc,CAACL,QAAQ,CAAC;gBAC5B,CAAC,MACI,IAAIQ,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,EAAE;kBAC9B;kBACA,IAAIU,aAAa,GAAG,KAAK;kBACzB,IAAIC,gBAAgB,GAAG,KAAK,CAAC;kBAC7B,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,UAAU,GAAGb,QAAQ,EAAEY,EAAE,GAAGC,UAAU,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;oBAClE,IAAIG,KAAK,GAAGF,UAAU,CAACD,EAAE,CAAC;oBAC1B,IAAIpF,WAAW,CAACuF,KAAK,CAAC,EAAE;sBACpBL,aAAa,GAAG,IAAI;sBACpB,IAAIL,cAAc,CAACU,KAAK,CAAC,EACrB;oBACR,CAAC,MACI;sBACD;sBACA;sBACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK,EAAE;wBACtC,IAAIxD,EAAE,GAAGf,KAAK,CAACF,QAAQ,CAACkB,QAAQ,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC1C;wBACA,IAAIxD,EAAE,EAAE;0BACJoD,gBAAgB,GAAGI,KAAK;wBAC5B;sBACJ;oBACJ;oBACA,IAAIL,aAAa,IAAIC,gBAAgB,KAAKL,SAAS,EAAE;sBACjDH,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIhF,SAAS,CAACmF,IAAI,CAAC,CAAC,EAAEI,gBAAgB,CAAC;sBACnE;oBACJ;kBACJ;gBACJ;cACJ;YACJ;UACJ;QACJ;QACA,IAAIZ,UAAU,KAAK,KAAK,CAAC,EAAE;UACvBN,YAAY,GAAG,KAAK;QACxB;MACJ,CAAC,CAAC;MACF,IAAID,aAAa,EAAE;QACf,IAAI,CAAC9B,KAAK,CAACG,MAAM,EAAE0B,eAAe,CAAC;QACnC,IAAIE,YAAY,EAAE;UACd,IAAI,IAAI,YAAYrB,KAAK,EAAE;YACvB,IAAI,CAAC3B,IAAI,CAACoB,MAAM,CAAC,GAAG,KAAK,CAAC;UAC9B,CAAC,MACI;YACD,OAAO,IAAI,CAACpB,IAAI,CAACoB,MAAM,CAAC;UAC5B;UACA,IAAI,CAACtB,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAE,UAAU,CAAC;QACxC;QACA,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACAxB,WAAW,CAACsB,SAAS,CAACqD,MAAM,GAAG,UAAUnD,MAAM,EAAEE,SAAS,EAAEkD,IAAI,EAAE;IAC9D,IAAIC,EAAE;IACN,IAAIhD,WAAW,GAAG,IAAI,CAACJ,MAAM,CAACD,MAAM,CAAC;IACrC,IAAIK,WAAW,EAAE;MACb,IAAIiD,QAAQ,GAAG,IAAI,CAACvE,aAAa,CAACsB,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAIpB,cAAc,GAAGiB,SAAS,IAAIkD,IAAI,GAClC,IAAI,CAAC3E,QAAQ,CAAC8E,iBAAiB,CAAC;QAAED,QAAQ,EAAEA,QAAQ;QAAEpD,SAAS,EAAEA,SAAS;QAAEkD,IAAI,EAAEA;MAAK,CAAC,CAAC,GACvFlD,SAAS;MACf,OAAO,IAAI,CAACsB,MAAM,CAACxB,MAAM,EAAEf,cAAc,IAAIoE,EAAE,GAAG,CAAC,CAAC,EAChDA,EAAE,CAACpE,cAAc,CAAC,GAAGX,WAAW,EAChC+E,EAAE,IAAI/E,WAAW,CAAC;IAC1B;IACA,OAAO,KAAK;EAChB,CAAC;EACDE,WAAW,CAACsB,SAAS,CAAC0D,KAAK,GAAG,UAAUC,OAAO,EAAEC,KAAK,EAAE;IACpD,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIF,OAAO,CAAC/D,EAAE,EAAE;MACZ,IAAIzB,MAAM,CAACmC,IAAI,CAAC,IAAI,CAACxB,IAAI,EAAE6E,OAAO,CAAC/D,EAAE,CAAC,EAAE;QACpCiE,OAAO,GAAG,IAAI,CAACR,MAAM,CAACM,OAAO,CAAC/D,EAAE,EAAE+D,OAAO,CAACvD,SAAS,EAAEuD,OAAO,CAACL,IAAI,CAAC;MACtE;MACA,IAAI,IAAI,YAAY7C,KAAK,IAAI,IAAI,KAAKmD,KAAK,EAAE;QACzCC,OAAO,GAAG,IAAI,CAACnD,MAAM,CAACgD,KAAK,CAACC,OAAO,EAAEC,KAAK,CAAC,IAAIC,OAAO;MAC1D;MACA;MACA;MACA;MACA;MACA,IAAIF,OAAO,CAACvD,SAAS,IAAIyD,OAAO,EAAE;QAC9B,IAAI,CAACjF,KAAK,CAAC6C,KAAK,CAACkC,OAAO,CAAC/D,EAAE,EAAE+D,OAAO,CAACvD,SAAS,IAAI,UAAU,CAAC;MACjE;IACJ;IACA,OAAOyD,OAAO;EAClB,CAAC;EACDnF,WAAW,CAACsB,SAAS,CAAC8D,KAAK,GAAG,YAAY;IACtC,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC;EACtB,CAAC;EACDrF,WAAW,CAACsB,SAAS,CAACgE,OAAO,GAAG,YAAY;IACxC,IAAInF,KAAK,GAAG,IAAI;IAChB,IAAIoF,GAAG,GAAG,IAAI,CAAChE,QAAQ,CAAC,CAAC;IACzB,IAAIiE,YAAY,GAAG,EAAE;IACrB,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC7C,OAAO,CAAC,UAAU1B,EAAE,EAAE;MACtC,IAAI,CAACzB,MAAM,CAACmC,IAAI,CAACzB,KAAK,CAACF,QAAQ,CAAC6B,iBAAiB,EAAEZ,EAAE,CAAC,EAAE;QACpDsE,YAAY,CAACE,IAAI,CAACxE,EAAE,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,IAAIsE,YAAY,CAACf,MAAM,EAAE;MACrBc,GAAG,CAACI,MAAM,GAAG;QAAEH,YAAY,EAAEA,YAAY,CAACI,IAAI,CAAC;MAAE,CAAC;IACtD;IACA,OAAOL,GAAG;EACd,CAAC;EACDvF,WAAW,CAACsB,SAAS,CAAC+D,OAAO,GAAG,UAAUQ,OAAO,EAAE;IAC/C,IAAI1F,KAAK,GAAG,IAAI;IAChBP,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAACvC,IAAI,CAAC,CAACwC,OAAO,CAAC,UAAUpB,MAAM,EAAE;MAC7C,IAAI,EAAEqE,OAAO,IAAIpG,MAAM,CAACmC,IAAI,CAACiE,OAAO,EAAErE,MAAM,CAAC,CAAC,EAAE;QAC5CrB,KAAK,CAACwE,MAAM,CAACnD,MAAM,CAAC;MACxB;IACJ,CAAC,CAAC;IACF,IAAIqE,OAAO,EAAE;MACT,IAAIF,MAAM,GAAGE,OAAO,CAACF,MAAM;QAAEG,MAAM,GAAGhH,MAAM,CAAC+G,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;MACjEjG,MAAM,CAAC+C,IAAI,CAACmD,MAAM,CAAC,CAAClD,OAAO,CAAC,UAAUpB,MAAM,EAAE;QAC1CrB,KAAK,CAACkB,KAAK,CAACG,MAAM,EAAEsE,MAAM,CAACtE,MAAM,CAAC,CAAC;MACvC,CAAC,CAAC;MACF,IAAImE,MAAM,EAAE;QACRA,MAAM,CAACH,YAAY,CAAC5C,OAAO,CAAC,IAAI,CAACmD,MAAM,EAAE,IAAI,CAAC;MAClD;IACJ;EACJ,CAAC;EACD/F,WAAW,CAACsB,SAAS,CAACyE,MAAM,GAAG,UAAUC,MAAM,EAAE;IAC7C,OAAQ,IAAI,CAAC3F,OAAO,CAAC2F,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC3F,OAAO,CAAC2F,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;EAClE,CAAC;EACDhG,WAAW,CAACsB,SAAS,CAAC2E,OAAO,GAAG,UAAUD,MAAM,EAAE;IAC9C,IAAI,IAAI,CAAC3F,OAAO,CAAC2F,MAAM,CAAC,GAAG,CAAC,EAAE;MAC1B,IAAIE,KAAK,GAAG,EAAE,IAAI,CAAC7F,OAAO,CAAC2F,MAAM,CAAC;MAClC,IAAI,CAACE,KAAK,EACN,OAAO,IAAI,CAAC7F,OAAO,CAAC2F,MAAM,CAAC;MAC/B,OAAOE,KAAK;IAChB;IACA,OAAO,CAAC;EACZ,CAAC;EACD;EACA;EACAlG,WAAW,CAACsB,SAAS,CAACmE,YAAY,GAAG,UAAUU,GAAG,EAAE;IAChD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;IAAE;IACvCxG,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAACtC,OAAO,CAAC,CAACuC,OAAO,CAACuD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;IAC/C,IAAI,IAAI,YAAYpE,KAAK,EAAE;MACvB,IAAI,CAACC,MAAM,CAACyD,YAAY,CAACU,GAAG,CAAC;IACjC,CAAC,MACI;MACD;MACA;MACA;MACAvG,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAAC1C,QAAQ,CAAC6B,iBAAiB,CAAC,CAACc,OAAO,CAACuD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;IACtE;IACA,OAAOA,GAAG;EACd,CAAC;EACD;EACA;EACA;EACA;EACAnG,WAAW,CAACsB,SAAS,CAACgF,EAAE,GAAG,YAAY;IACnC,IAAInG,KAAK,GAAG,IAAI;IAChB,IAAIgG,GAAG,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC;IAC7B,IAAIc,QAAQ,GAAG,IAAI,CAAChF,QAAQ,CAAC,CAAC;IAC9B4E,GAAG,CAACvD,OAAO,CAAC,UAAU1B,EAAE,EAAE;MACtB,IAAIzB,MAAM,CAACmC,IAAI,CAAC2E,QAAQ,EAAErF,EAAE,CAAC,EAAE;QAC3B;QACA;QACA;QACAtB,MAAM,CAAC+C,IAAI,CAACxC,KAAK,CAACqG,eAAe,CAACtF,EAAE,CAAC,CAAC,CAAC0B,OAAO,CAACuD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAAC;QAC5D;QACA;QACA,OAAOI,QAAQ,CAACrF,EAAE,CAAC;MACvB;IACJ,CAAC,CAAC;IACF,IAAIuF,WAAW,GAAG7G,MAAM,CAAC+C,IAAI,CAAC4D,QAAQ,CAAC;IACvC,IAAIE,WAAW,CAAChC,MAAM,EAAE;MACpB,IAAIiC,MAAM,GAAG,IAAI;MACjB,OAAOA,MAAM,YAAY3E,KAAK,EAC1B2E,MAAM,GAAGA,MAAM,CAAC1E,MAAM;MAC1ByE,WAAW,CAAC7D,OAAO,CAAC,UAAU1B,EAAE,EAAE;QAAE,OAAOwF,MAAM,CAAC/B,MAAM,CAACzD,EAAE,CAAC;MAAE,CAAC,CAAC;IACpE;IACA,OAAOuF,WAAW;EACtB,CAAC;EACDzG,WAAW,CAACsB,SAAS,CAACkF,eAAe,GAAG,UAAUhF,MAAM,EAAE;IACtD,IAAI,CAAC/B,MAAM,CAACmC,IAAI,CAAC,IAAI,CAACtB,IAAI,EAAEkB,MAAM,CAAC,EAAE;MACjC,IAAImF,OAAO,GAAI,IAAI,CAACrG,IAAI,CAACkB,MAAM,CAAC,GAAG5B,MAAM,CAACC,MAAM,CAAC,IAAI,CAAE;MACvD,IAAI+G,IAAI,GAAG,IAAI,CAACxG,IAAI,CAACoB,MAAM,CAAC;MAC5B,IAAI,CAACoF,IAAI,EACL,OAAOD,OAAO;MAClB,IAAIE,SAAS,GAAG,IAAIT,GAAG,CAAC,CAACQ,IAAI,CAAC,CAAC;MAC/B;MACA;MACAC,SAAS,CAACjE,OAAO,CAAC,UAAU2C,GAAG,EAAE;QAC7B,IAAIpG,WAAW,CAACoG,GAAG,CAAC,EAAE;UAClBoB,OAAO,CAACpB,GAAG,CAAC5E,KAAK,CAAC,GAAG,IAAI;UACzB;UACA;UACA;UACA;UACA;UACA;UACA;QACJ;;QACA,IAAInB,eAAe,CAAC+F,GAAG,CAAC,EAAE;UACtB3F,MAAM,CAAC+C,IAAI,CAAC4C,GAAG,CAAC,CAAC3C,OAAO,CAAC,UAAUkE,GAAG,EAAE;YACpC,IAAIC,KAAK,GAAGxB,GAAG,CAACuB,GAAG,CAAC;YACpB;YACA;YACA,IAAItH,eAAe,CAACuH,KAAK,CAAC,EAAE;cACxBF,SAAS,CAACR,GAAG,CAACU,KAAK,CAAC;YACxB;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACzG,IAAI,CAACkB,MAAM,CAAC;EAC5B,CAAC;EACDxB,WAAW,CAACsB,SAAS,CAAC0F,YAAY,GAAG,YAAY;IAC7C,OAAO,IAAI,CAAC9G,KAAK,CAAC+G,QAAQ,CAACC,WAAW,CAACC,SAAS,CAAC;EACrD,CAAC;EACD,OAAOnH,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoH,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAAC5E,OAAO,EAAER,MAAM,EAAE;IACjC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,IAAI;IAAE;IACxC,IAAI,CAACQ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACR,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqF,CAAC,GAAG,IAAI;IACb,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACAF,UAAU,CAAC9F,SAAS,CAACgG,YAAY,GAAG,YAAY;IAC5C,IAAI,CAACD,CAAC,GAAG,IAAI,CAAC7E,OAAO,GAAGxD,GAAG,CAAC,CAAC,GAAG,IAAI;IACpC,IAAI,CAACiI,QAAQ,GAAG,IAAI/H,IAAI,CAACK,aAAa,CAAC;EAC3C,CAAC;EACD6H,UAAU,CAAC9F,SAAS,CAACK,MAAM,GAAG,UAAUH,MAAM,EAAEf,cAAc,EAAE;IAC5D,IAAI,IAAI,CAAC4G,CAAC,EAAE;MACR,IAAI,CAACA,CAAC,CAACE,UAAU,CAAC/F,MAAM,EAAEf,cAAc,CAAC,CAAC;MAC1C,IAAIiB,SAAS,GAAGhC,sBAAsB,CAACe,cAAc,CAAC;MACtD,IAAIiB,SAAS,KAAKjB,cAAc,EAAE;QAC9B;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC4G,CAAC,CAACE,UAAU,CAAC/F,MAAM,EAAEE,SAAS,CAAC,CAAC;MACzC;MACA,IAAI,IAAI,CAACM,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACL,MAAM,CAACH,MAAM,EAAEf,cAAc,CAAC;MAC9C;IACJ;EACJ,CAAC;EACD2G,UAAU,CAAC9F,SAAS,CAACyB,KAAK,GAAG,UAAUvB,MAAM,EAAEf,cAAc,EAAE;IAC3D,IAAI,IAAI,CAAC4G,CAAC,EAAE;MACR,IAAI,CAACA,CAAC,CAACtE,KAAK,CAACwE,UAAU,CAAC/F,MAAM,EAAEf,cAAc,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA;MACA;MACAA,cAAc,KAAK,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC;IAC1D;EACJ,CAAC;EACD,OAAO2G,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASG,UAAUA,CAAC/F,MAAM,EAAEf,cAAc,EAAE;EACxC;EACA;EACA;EACA,OAAOA,cAAc,GAAG,GAAG,GAAGe,MAAM;AACxC;AACA,OAAO,SAASgG,8BAA8BA,CAAC/D,KAAK,EAAEgE,QAAQ,EAAE;EAC5D,IAAIC,qBAAqB,CAACjE,KAAK,CAAC,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACvD,KAAK,CAACyB,MAAM,CAAC8F,QAAQ,EAAE,UAAU,CAAC;EAC5C;AACJ;AACA,CAAC,UAAUzH,WAAW,EAAE;EACpB;EACA,IAAI2H,IAAI,GAAG,aAAe,UAAUC,MAAM,EAAE;IACxC/I,SAAS,CAAC8I,IAAI,EAAEC,MAAM,CAAC;IACvB,SAASD,IAAIA,CAAC9C,EAAE,EAAE;MACd,IAAI5E,QAAQ,GAAG4E,EAAE,CAAC5E,QAAQ;QAAE4H,EAAE,GAAGhD,EAAE,CAACiD,aAAa;QAAEA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;QAAEE,IAAI,GAAGlD,EAAE,CAACkD,IAAI;MAC5G,IAAI5H,KAAK,GAAGyH,MAAM,CAAChG,IAAI,CAAC,IAAI,EAAE3B,QAAQ,EAAE,IAAImH,UAAU,CAACU,aAAa,CAAC,CAAC,IAAI,IAAI;MAC9E3H,KAAK,CAAC6H,KAAK,GAAG,IAAIC,KAAK,CAAC9H,KAAK,CAAC;MAC9BA,KAAK,CAAC+H,WAAW,GAAG,IAAIhJ,IAAI,CAACK,aAAa,CAAC;MAC3C,IAAIwI,IAAI,EACJ5H,KAAK,CAACkF,OAAO,CAAC0C,IAAI,CAAC;MACvB,OAAO5H,KAAK;IAChB;IACAwH,IAAI,CAACrG,SAAS,CAAC6G,QAAQ,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAE;MACjD;MACA;MACA;MACA,OAAO,IAAI,CAACL,KAAK,CAACG,QAAQ,CAACC,OAAO,EAAEC,MAAM,CAAC;IAC/C,CAAC;IACDV,IAAI,CAACrG,SAAS,CAACgH,WAAW,GAAG,YAAY;MACrC;MACA,OAAO,IAAI;IACf,CAAC;IACDX,IAAI,CAACrG,SAAS,CAACuC,UAAU,GAAG,YAAY;MACpC,OAAO,IAAI,CAACqE,WAAW,CAAChB,WAAW,CAACC,SAAS,CAAC;IAClD,CAAC;IACD,OAAOQ,IAAI;EACf,CAAC,CAAC3H,WAAW,CAAE;EACfA,WAAW,CAAC2H,IAAI,GAAGA,IAAI;AAC3B,CAAC,EAAE3H,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA,IAAI+B,KAAK,GAAG,aAAe,UAAU6F,MAAM,EAAE;EACzC/I,SAAS,CAACkD,KAAK,EAAE6F,MAAM,CAAC;EACxB,SAAS7F,KAAKA,CAACb,EAAE,EAAEc,MAAM,EAAEqG,MAAM,EAAEnI,KAAK,EAAE;IACtC,IAAIC,KAAK,GAAGyH,MAAM,CAAChG,IAAI,CAAC,IAAI,EAAEI,MAAM,CAAC/B,QAAQ,EAAEC,KAAK,CAAC,IAAI,IAAI;IAC7DC,KAAK,CAACe,EAAE,GAAGA,EAAE;IACbf,KAAK,CAAC6B,MAAM,GAAGA,MAAM;IACrB7B,KAAK,CAACkI,MAAM,GAAGA,MAAM;IACrBlI,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnBmI,MAAM,CAAClI,KAAK,CAAC;IACb,OAAOA,KAAK;EAChB;EACA4B,KAAK,CAACT,SAAS,CAAC6G,QAAQ,GAAG,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAClD,OAAO,IAAItG,KAAK,CAACqG,OAAO,EAAE,IAAI,EAAEC,MAAM,EAAE,IAAI,CAACnI,KAAK,CAAC;EACvD,CAAC;EACD6B,KAAK,CAACT,SAAS,CAACgH,WAAW,GAAG,UAAUF,OAAO,EAAE;IAC7C,IAAIjI,KAAK,GAAG,IAAI;IAChB;IACA,IAAI6B,MAAM,GAAG,IAAI,CAACA,MAAM,CAACsG,WAAW,CAACF,OAAO,CAAC;IAC7C,IAAIA,OAAO,KAAK,IAAI,CAAClH,EAAE,EAAE;MACrB,IAAI,IAAI,CAAChB,KAAK,CAACsC,OAAO,EAAE;QACpB;QACA;QACA;QACA;QACA5C,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAACvC,IAAI,CAAC,CAACwC,OAAO,CAAC,UAAUpB,MAAM,EAAE;UAC7C,IAAI+G,cAAc,GAAGpI,KAAK,CAACC,IAAI,CAACoB,MAAM,CAAC;UACvC,IAAIgH,iBAAiB,GAAGxG,MAAM,CAAC,QAAQ,CAAC,CAACR,MAAM,CAAC;UAChD,IAAI,CAACgH,iBAAiB,EAAE;YACpB;YACA;YACA;YACA;YACA;YACArI,KAAK,CAACwE,MAAM,CAACnD,MAAM,CAAC;UACxB,CAAC,MACI,IAAI,CAAC+G,cAAc,EAAE;YACtB;YACA;YACA;YACA;YACApI,KAAK,CAACD,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAE,UAAU,CAAC;YACrC5B,MAAM,CAAC+C,IAAI,CAAC6F,iBAAiB,CAAC,CAAC5F,OAAO,CAAC,UAAUnC,cAAc,EAAE;cAC7DN,KAAK,CAACD,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAEf,cAAc,CAAC;YAC7C,CAAC,CAAC;UACN,CAAC,MACI,IAAI8H,cAAc,KAAKC,iBAAiB,EAAE;YAC3C;YACA;YACA;YACA5I,MAAM,CAAC+C,IAAI,CAAC4F,cAAc,CAAC,CAAC3F,OAAO,CAAC,UAAUnC,cAAc,EAAE;cAC1D,IAAI,CAACxB,KAAK,CAACsJ,cAAc,CAAC9H,cAAc,CAAC,EAAE+H,iBAAiB,CAAC/H,cAAc,CAAC,CAAC,EAAE;gBAC3EN,KAAK,CAACD,KAAK,CAAC6C,KAAK,CAACvB,MAAM,EAAEf,cAAc,CAAC;cAC7C;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,OAAOuB,MAAM;IACjB;IACA;IACA,IAAIA,MAAM,KAAK,IAAI,CAACA,MAAM,EACtB,OAAO,IAAI;IACf;IACA,OAAOA,MAAM,CAACmG,QAAQ,CAAC,IAAI,CAACjH,EAAE,EAAE,IAAI,CAACmH,MAAM,CAAC;EAChD,CAAC;EACDtG,KAAK,CAACT,SAAS,CAACC,QAAQ,GAAG,YAAY;IACnC,OAAO3C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoD,MAAM,CAACT,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnB,IAAI,CAAC;EACpE,CAAC;EACD2B,KAAK,CAACT,SAAS,CAACkF,eAAe,GAAG,UAAUhF,MAAM,EAAE;IAChD,IAAIiH,UAAU,GAAG,IAAI,CAACzG,MAAM,CAACwE,eAAe,CAAChF,MAAM,CAAC;IACpD,OAAO/B,MAAM,CAACmC,IAAI,CAAC,IAAI,CAACxB,IAAI,EAAEoB,MAAM,CAAC,GAAG5C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6J,UAAU,CAAC,EAAEb,MAAM,CAACtG,SAAS,CAACkF,eAAe,CAAC5E,IAAI,CAAC,IAAI,EAAEJ,MAAM,CAAC,CAAC,GAAGiH,UAAU;EAChJ,CAAC;EACD1G,KAAK,CAACT,SAAS,CAACuC,UAAU,GAAG,YAAY;IACrC,IAAI6E,CAAC,GAAG,IAAI,CAAC1G,MAAM;IACnB,OAAO0G,CAAC,CAAC1G,MAAM,EACX0G,CAAC,GAAGA,CAAC,CAAC1G,MAAM;IAChB,OAAO0G,CAAC,CAAC7E,UAAU,CAAC8E,KAAK,CAACD,CAAC;IAC3B;IACAvB,SAAS,CAAC;EACd,CAAC;EACD,OAAOpF,KAAK;AAChB,CAAC,CAAC/B,WAAW,CAAE;AACf;AACA;AACA;AACA;AACA,IAAIiI,KAAK,GAAG,aAAe,UAAUL,MAAM,EAAE;EACzC/I,SAAS,CAACoJ,KAAK,EAAEL,MAAM,CAAC;EACxB,SAASK,KAAKA,CAACrB,IAAI,EAAE;IACjB,OAAOgB,MAAM,CAAChG,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAEgF,IAAI,EAAE,YAAY,CAAE,CAAC,EAAE,IAAIQ,UAAU,CAACR,IAAI,CAAC1G,KAAK,CAACsC,OAAO,EAAEoE,IAAI,CAAC1G,KAAK,CAAC,CAAC,IAAI,IAAI;EAChI;EACA+H,KAAK,CAAC3G,SAAS,CAACgH,WAAW,GAAG,YAAY;IACtC;IACA,OAAO,IAAI;EACf,CAAC;EACDL,KAAK,CAAC3G,SAAS,CAACD,KAAK,GAAG,UAAUa,KAAK,EAAEC,KAAK,EAAE;IAC5C;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACH,MAAM,CAACX,KAAK,CAACa,KAAK,EAAEC,KAAK,CAAC;EAC1C,CAAC;EACD,OAAO8F,KAAK;AAChB,CAAC,CAAClG,KAAK,CAAE;AACT,SAASQ,qBAAqBA,CAACqG,cAAc,EAAEC,cAAc,EAAEC,QAAQ,EAAE;EACrE,IAAIC,aAAa,GAAGH,cAAc,CAACE,QAAQ,CAAC;EAC5C,IAAIE,aAAa,GAAGH,cAAc,CAACC,QAAQ,CAAC;EAC5C;EACA;EACA;EACA;EACA;EACA,OAAO7J,KAAK,CAAC8J,aAAa,EAAEC,aAAa,CAAC,GAAGD,aAAa,GAAGC,aAAa;AAC9E;AACA,OAAO,SAAStB,qBAAqBA,CAACjE,KAAK,EAAE;EACzC;EACA,OAAO,CAAC,EAAEA,KAAK,YAAYzD,WAAW,IAAIyD,KAAK,CAACvD,KAAK,CAACsC,OAAO,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}