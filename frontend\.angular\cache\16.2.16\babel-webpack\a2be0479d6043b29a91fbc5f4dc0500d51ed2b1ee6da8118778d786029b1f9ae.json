{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TaskService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}tasks`;\n    console.log('Task API URL:', this.apiUrl);\n  }\n  // Récupérer toutes les tâches\n  getTasks() {\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Tasks received:', data)), catchError(this.handleError));\n  }\n  // Récupérer les tâches d'une équipe spécifique\n  getTasksByTeam(teamId) {\n    return this.http.get(`${this.apiUrl}/team/${teamId}`).pipe(tap(data => console.log(`Tasks for team ${teamId} received:`, data)), catchError(this.handleError));\n  }\n  // Récupérer une tâche par son ID\n  getTask(id) {\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task received:', data)), catchError(this.handleError));\n  }\n  // Créer une nouvelle tâche\n  createTask(task) {\n    return this.http.post(this.apiUrl, task).pipe(tap(data => console.log('Task created:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour une tâche existante\n  updateTask(id, task) {\n    return this.http.put(`${this.apiUrl}/${id}`, task).pipe(tap(data => console.log('Task updated:', data)), catchError(this.handleError));\n  }\n  // Supprimer une tâche\n  deleteTask(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task deleted:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour le statut d'une tâche\n  updateTaskStatus(id, status) {\n    return this.http.patch(`${this.apiUrl}/${id}/status`, {\n      status\n    }).pipe(tap(data => console.log('Task status updated:', data)), catchError(this.handleError));\n  }\n  // Gérer les erreurs HTTP\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Error: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\n    }\n    console.error(errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function TaskService_Factory(t) {\n      return new (t || TaskService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TaskService,\n      factory: TaskService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "tap", "environment", "TaskService", "constructor", "http", "apiUrl", "urlBackend", "console", "log", "getTasks", "get", "pipe", "data", "handleError", "getTasksByTeam", "teamId", "getTask", "id", "createTask", "task", "post", "updateTask", "put", "deleteTask", "delete", "updateTaskStatus", "status", "patch", "error", "errorMessage", "ErrorEvent", "message", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\task.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\nimport { Task } from '../models/task.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TaskService {\r\n  private apiUrl = `${environment.urlBackend}tasks`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('Task API URL:', this.apiUrl);\r\n  }\r\n\r\n  // Récupérer toutes les tâches\r\n  getTasks(): Observable<Task[]> {\r\n    return this.http.get<Task[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer les tâches d'une équipe spécifique\r\n  getTasksByTeam(teamId: string): Observable<Task[]> {\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`).pipe(\r\n      tap((data) => console.log(`Tasks for team ${teamId} received:`, data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer une tâche par son ID\r\n  getTask(id: string): Observable<Task> {\r\n    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer une nouvelle tâche\r\n  createTask(task: Task): Observable<Task> {\r\n    return this.http.post<Task>(this.apiUrl, task).pipe(\r\n      tap((data) => console.log('Task created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour une tâche existante\r\n  updateTask(id: string, task: Task): Observable<Task> {\r\n    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(\r\n      tap((data) => console.log('Task updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer une tâche\r\n  deleteTask(id: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task deleted:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour le statut d'une tâche\r\n  updateTaskStatus(\r\n    id: string,\r\n    status: 'todo' | 'in-progress' | 'done'\r\n  ): Observable<Task> {\r\n    return this.http\r\n      .patch<Task>(`${this.apiUrl}/${id}/status`, { status })\r\n      .pipe(\r\n        tap((data) => console.log('Task status updated:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Gérer les erreurs HTTP\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Error: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\r\n    }\r\n    console.error(errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;;;AAM5D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,OAAO;IAG/CC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACH,MAAM,CAAC;EAC3C;EAEA;EACAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAS,IAAI,CAACL,MAAM,CAAC,CAACM,IAAI,CAC5CX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,IAAI,CAAC,CAAC,EACnDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAI,CAACX,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,SAASU,MAAM,EAAE,CAAC,CAACJ,IAAI,CAChEX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,kBAAkBO,MAAM,YAAY,EAAEH,IAAI,CAAC,CAAC,EACtEb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAG,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACb,IAAI,CAACM,GAAG,CAAO,GAAG,IAAI,CAACL,MAAM,IAAIY,EAAE,EAAE,CAAC,CAACN,IAAI,CACrDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC,CAAC,EAClDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAO,IAAI,CAACf,MAAM,EAAEc,IAAI,CAAC,CAACR,IAAI,CACjDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAQ,UAAUA,CAACJ,EAAU,EAAEE,IAAU;IAC/B,OAAO,IAAI,CAACf,IAAI,CAACkB,GAAG,CAAO,GAAG,IAAI,CAACjB,MAAM,IAAIY,EAAE,EAAE,EAAEE,IAAI,CAAC,CAACR,IAAI,CAC3DX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAU,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAACb,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAACnB,MAAM,IAAIY,EAAE,EAAE,CAAC,CAACN,IAAI,CAClDX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,IAAI,CAAC,CAAC,EACjDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EACAY,gBAAgBA,CACdR,EAAU,EACVS,MAAuC;IAEvC,OAAO,IAAI,CAACtB,IAAI,CACbuB,KAAK,CAAO,GAAG,IAAI,CAACtB,MAAM,IAAIY,EAAE,SAAS,EAAE;MAAES;IAAM,CAAE,CAAC,CACtDf,IAAI,CACHX,GAAG,CAAEY,IAAI,IAAKL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,IAAI,CAAC,CAAC,EACxDb,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACL;EAEA;EACQA,WAAWA,CAACe,KAAwB;IAC1C,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAC/C,MAAM;MACL;MACAF,YAAY,GAAG,eAAeD,KAAK,CAACF,MAAM,cAAcE,KAAK,CAACG,OAAO,EAAE;;IAEzExB,OAAO,CAACqB,KAAK,CAACC,YAAY,CAAC;IAC3B,OAAO/B,UAAU,CAAC,MAAM,IAAIkC,KAAK,CAACH,YAAY,CAAC,CAAC;EAClD;;;uBAhFW3B,WAAW,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXlC,WAAW;MAAAmC,OAAA,EAAXnC,WAAW,CAAAoC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}