{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ListProjectComponent } from './list-project/list-project.component';\nimport { AddProjectComponent } from './add-project/add-project.component';\nimport { UpdateProjectComponent } from './update-project/update-project.component';\nimport { DetailProjectComponent } from './detail-project/detail-project.component';\nimport { ListRendusComponent } from './list-rendus/list-rendus.component';\nimport { ProjectEvaluationComponent } from './project-evaluation/project-evaluation.component';\nimport { EvaluationDetailsComponent } from './evaluation-details/evaluation-details.component';\nimport { EvaluationsListComponent } from './evaluations-list/evaluations-list.component';\nimport { EditEvaluationComponent } from './edit-evaluation/edit-evaluation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ListProjectComponent\n}, {\n  path: 'new',\n  component: AddProjectComponent\n}, {\n  path: 'editProjet/:id',\n  component: UpdateProjectComponent\n}, {\n  path: 'details/:id',\n  component: DetailProjectComponent\n}, {\n  path: 'rendus',\n  component: ListRendusComponent\n}, {\n  path: 'evaluate/:renduId',\n  component: ProjectEvaluationComponent\n}, {\n  path: 'evaluation-details/:renduId',\n  component: EvaluationDetailsComponent\n}, {\n  path: 'evaluations',\n  component: EvaluationsListComponent\n}, {\n  path: 'edit-evaluation/:renduId',\n  component: EditEvaluationComponent\n}];\nexport class ProjectsRoutingModule {\n  static {\n    this.ɵfac = function ProjectsRoutingModule_Factory(t) {\n      return new (t || ProjectsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProjectsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProjectsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ListProjectComponent", "AddProjectComponent", "UpdateProjectComponent", "DetailProjectComponent", "ListRendusComponent", "ProjectEvaluationComponent", "EvaluationDetailsComponent", "EvaluationsListComponent", "EditEvaluationComponent", "routes", "path", "component", "ProjectsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\projects-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ListProjectComponent } from './list-project/list-project.component';\r\nimport { AddProjectComponent } from './add-project/add-project.component';\r\nimport { UpdateProjectComponent } from './update-project/update-project.component';\r\nimport { DetailProjectComponent } from './detail-project/detail-project.component';\r\nimport { ListRendusComponent } from './list-rendus/list-rendus.component';\r\nimport { ProjectEvaluationComponent } from './project-evaluation/project-evaluation.component';\r\nimport { EvaluationDetailsComponent } from './evaluation-details/evaluation-details.component';\r\nimport { EvaluationsListComponent } from './evaluations-list/evaluations-list.component';\r\nimport { EditEvaluationComponent } from './edit-evaluation/edit-evaluation.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ListProjectComponent },\r\n  { path: 'new', component: AddProjectComponent },\r\n  { path: 'editProjet/:id', component: UpdateProjectComponent },\r\n  { path: 'details/:id', component: DetailProjectComponent },\r\n  { path: 'rendus', component: ListRendusComponent },\r\n  { path: 'evaluate/:renduId', component: ProjectEvaluationComponent },\r\n  { path: 'evaluation-details/:renduId', component: EvaluationDetailsComponent },\r\n  { path: 'evaluations', component: EvaluationsListComponent },\r\n  { path: 'edit-evaluation/:renduId', component: EditEvaluationComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ProjectsRoutingModule { }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,uBAAuB,QAAQ,6CAA6C;;;AAErF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEX;AAAoB,CAAE,EAC7C;EAAEU,IAAI,EAAE,KAAK;EAAEC,SAAS,EAAEV;AAAmB,CAAE,EAC/C;EAAES,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAET;AAAsB,CAAE,EAC7D;EAAEQ,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAER;AAAsB,CAAE,EAC1D;EAAEO,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEP;AAAmB,CAAE,EAClD;EAAEM,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEN;AAA0B,CAAE,EACpE;EAAEK,IAAI,EAAE,6BAA6B;EAAEC,SAAS,EAAEL;AAA0B,CAAE,EAC9E;EAAEI,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEJ;AAAwB,CAAE,EAC5D;EAAEG,IAAI,EAAE,0BAA0B;EAAEC,SAAS,EAAEH;AAAuB,CAAE,CACzE;AAMD,OAAM,MAAOI,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBb,YAAY,CAACc,QAAQ,CAACJ,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXa,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFtBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}