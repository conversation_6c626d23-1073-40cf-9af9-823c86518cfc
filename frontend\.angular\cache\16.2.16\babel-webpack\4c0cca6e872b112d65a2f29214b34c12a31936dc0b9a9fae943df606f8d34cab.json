{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/ai.service\";\nimport * as i2 from \"src/app/services/task.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"assistant-message\": a1\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    \"flex-row-reverse\": a0\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"user-bubble\": a0,\n    \"assistant-bubble\": a1\n  };\n};\nfunction AiChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵelement(5, \"p\", 23);\n    i0.ɵɵelementStart(6, \"div\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const message_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c0, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, message_r6.role === \"user\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c2, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", message_r6.role === \"user\" ? \"bi-person-fill\" : \"bi-robot\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(16, _c3, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", message_r6.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", message_r6.role === \"user\" ? \"Vous\" : \"Assistant IA\", \" \\u2022 \", ctx_r1.getCurrentTime(), \" \");\n  }\n}\nfunction AiChatComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵelement(6, \"span\")(7, \"span\")(8, \"span\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nconst _c4 = \"linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))\";\nconst _c5 = function (a1) {\n  return {\n    \"background\": _c4,\n    \"border-left\": a1\n  };\n};\nconst _c6 = function (a0) {\n  return {\n    \"background\": a0\n  };\n};\nconst _c7 = function (a0) {\n  return {\n    \"color\": a0\n  };\n};\nfunction AiChatComponent_div_7_div_14_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 81);\n    i0.ɵɵtext(5, \"Responsable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 82);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    const i_r11 = ctx_r14.index;\n    const entity_r10 = ctx_r14.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c5, \"4px solid \" + ctx_r12.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c6, ctx_r12.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(8, _c7, ctx_r12.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(entity_r10.assignedTo);\n  }\n}\nconst _c8 = function (a0, a1, a2) {\n  return {\n    \"high-priority\": a0,\n    \"medium-priority\": a1,\n    \"low-priority\": a2\n  };\n};\nconst _c9 = function (a0, a1, a2) {\n  return {\n    \"bg-danger\": a0,\n    \"bg-warning text-dark\": a1,\n    \"bg-info text-dark\": a2\n  };\n};\nfunction AiChatComponent_div_7_div_14_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"h6\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 87);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c8, task_r15.priority === \"high\", task_r15.priority === \"medium\", task_r15.priority === \"low\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r15.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c9, task_r15.priority === \"high\", task_r15.priority === \"medium\", task_r15.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r15.priority === \"high\" ? \"Haute\" : task_r15.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", task_r15.description, \" \");\n  }\n}\nfunction AiChatComponent_div_7_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 63)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 64)(6, \"div\", 65);\n    i0.ɵɵelement(7, \"i\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"h5\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 68)(11, \"div\", 69)(12, \"p\", 70);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AiChatComponent_div_7_div_14_div_14_Template, 8, 10, \"div\", 71);\n    i0.ɵɵelementStart(15, \"div\", 72)(16, \"h6\", 73);\n    i0.ɵɵelement(17, \"i\", 74);\n    i0.ɵɵtext(18, \" T\\u00E2ches \\u00E0 r\\u00E9aliser \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 75);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 76);\n    i0.ɵɵtemplate(22, AiChatComponent_div_7_div_14_div_22_Template, 8, 13, \"div\", 77);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const entity_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Module \", i_r11 + 1, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(14, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getIconForModule(entity_r10.name))(\"ngStyle\", i0.ɵɵpureFunction1(16, _c7, ctx_r8.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entity_r10.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(entity_r10.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", entity_r10.assignedTo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(18, _c7, ctx_r8.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", entity_r10.tasks.length, \" t\\u00E2ches \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", entity_r10.tasks);\n  }\n}\nfunction AiChatComponent_div_7_div_16_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"h6\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 99);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 100);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r20.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(4, _c9, task_r20.priority === \"high\", task_r20.priority === \"medium\", task_r20.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r20.priority === \"high\" ? \"Haute\" : task_r20.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(task_r20.description);\n  }\n}\nfunction AiChatComponent_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"h2\", 89)(2, \"button\", 90)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 91);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 92)(8, \"div\", 93)(9, \"p\", 94);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 95);\n    i0.ɵɵtemplate(12, AiChatComponent_div_7_div_16_div_12_Template, 8, 8, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const entity_r17 = ctx.$implicit;\n    const i_r18 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"heading\" + i_r18);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-bs-target\", \"#collapse\" + i_r18)(\"aria-expanded\", i_r18 === 0)(\"aria-controls\", \"collapse\" + i_r18);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entity_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entity_r17.tasks.length, \" t\\u00E2ches\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"collapse\" + i_r18);\n    i0.ɵɵattribute(\"aria-labelledby\", \"heading\" + i_r18);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(entity_r17.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", entity_r17.tasks);\n  }\n}\nfunction AiChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\")(4, \"h5\", 34);\n    i0.ɵɵelement(5, \"i\", 35);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 36);\n    i0.ɵɵelement(8, \"i\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 40);\n    i0.ɵɵtemplate(14, AiChatComponent_div_7_div_14_Template, 23, 22, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 42);\n    i0.ɵɵtemplate(16, AiChatComponent_div_7_div_16_Template, 13, 10, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"div\", 45)(19, \"div\", 46)(20, \"div\", 47)(21, \"div\", 48)(22, \"h5\", 49);\n    i0.ɵɵelement(23, \"i\", 50);\n    i0.ɵɵtext(24, \" Plan de projet pr\\u00EAt \\u00E0 \\u00EAtre impl\\u00E9ment\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 51)(26, \"div\", 52);\n    i0.ɵɵtext(27, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\")(29, \"h6\", 53);\n    i0.ɵɵtext(30, \"Cr\\u00E9ation des t\\u00E2ches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 54);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 51)(34, \"div\", 55);\n    i0.ɵɵtext(35, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\")(37, \"h6\", 53);\n    i0.ɵɵtext(38, \"Assignation aux membres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 54);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 26)(42, \"div\", 56);\n    i0.ɵɵtext(43, \"3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"h6\", 53);\n    i0.ɵɵtext(46, \"Suivi du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 54);\n    i0.ɵɵtext(48, \"Vous pourrez suivre l'avancement dans le tableau de bord des t\\u00E2ches\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 57)(50, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_7_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.createTasks());\n    });\n    i0.ɵɵelement(51, \"i\", 59);\n    i0.ɵɵtext(52, \" Cr\\u00E9er les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 60);\n    i0.ɵɵelement(54, \"i\", 37);\n    i0.ɵɵtext(55, \" Cette action est irr\\u00E9versible \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Plan du projet \\\"\", ctx_r3.generatedContent.projectTitle, \"\\\" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.generatedContent.entities.length, \" modules g\\u00E9n\\u00E9r\\u00E9s avec \", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches au total \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.generatedContent.entities);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.generatedContent.entities);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches seront cr\\u00E9\\u00E9es dans le syst\\u00E8me\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Les t\\u00E2ches seront assign\\u00E9es aux \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres de l'\\u00E9quipe\");\n  }\n}\nfunction AiChatComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.error, \" \");\n  }\n}\nfunction AiChatComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"div\", 104)(3, \"h6\", 105);\n    i0.ɵɵelement(4, \"i\", 106);\n    i0.ɵɵtext(5, \" G\\u00E9n\\u00E9rer des t\\u00E2ches avec l'IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"label\", 108);\n    i0.ɵɵtext(8, \"Entrez le titre de votre projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13)(10, \"span\", 14);\n    i0.ɵɵelement(11, \"i\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 110);\n    i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_div_10_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.projectTitle = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"small\", 111);\n    i0.ɵɵelement(14, \"i\", 37);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 112)(17, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_10_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.generateTasks());\n    });\n    i0.ɵɵelement(18, \"i\", 18);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.projectTitle)(\"disabled\", ctx_r5.isGenerating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" L'IA g\\u00E9n\\u00E9rera \", ctx_r5.team && ctx_r5.team.members ? ctx_r5.team.members.length : 3, \" modules, un pour chaque membre de l'\\u00E9quipe. \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.isGenerating || !ctx_r5.projectTitle.trim());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.isGenerating ? \"bi-hourglass-split spin\" : \"bi-magic\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.isGenerating ? \"G\\u00E9n\\u00E9ration en cours...\" : \"G\\u00E9n\\u00E9rer des t\\u00E2ches\", \" \");\n  }\n}\nexport class AiChatComponent {\n  constructor(aiService, taskService, notificationService) {\n    this.aiService = aiService;\n    this.taskService = taskService;\n    this.notificationService = notificationService;\n    this.projectTitle = '';\n    this.isGenerating = false;\n    this.generatedContent = null;\n    this.error = null;\n    // Pour le chat\n    this.messages = [];\n    this.userQuestion = '';\n    this.isAskingQuestion = false;\n  }\n  ngOnInit() {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content: 'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.'\n    });\n  }\n  generateTasks() {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount = this.team && this.team.members ? this.team.members.length : 3;\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\"L'équipe n'a pas de membres. Des tâches génériques seront créées.\");\n    }\n    this.isGenerating = true;\n    this.error = null;\n    console.log(`Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`);\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`\n    });\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...'\n    });\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId, index) => {\n        return {\n          id: memberId,\n          name: `Membre ${index + 1}`,\n          role: 'membre'\n        };\n      });\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n    this.aiService.generateProjectTasks(this.projectTitle, memberCount, teamMembers).pipe(finalize(() => this.isGenerating = false)).subscribe({\n      next: result => {\n        if (!result || !result.entities || result.entities.length === 0) {\n          console.error(\"Résultat invalide reçu de l'API:\", result);\n          this.handleGenerationError(loadingMessageIndex, 'Format de réponse invalide');\n          return;\n        }\n        this.generatedContent = result;\n        // Remplacer le message de chargement par la réponse\n        this.messages[loadingMessageIndex] = {\n          role: 'assistant',\n          content: `J'ai généré ${result.entities.length} entités pour votre projet \"${result.projectTitle}\" avec un total de ${this.countTasks(result)} tâches.`\n        };\n        this.notificationService.showSuccess('Tâches générées avec succès');\n      },\n      error: error => {\n        console.error('Erreur lors de la génération des tâches:', error);\n        this.handleGenerationError(loadingMessageIndex, error.message || 'Erreur inconnue');\n      }\n    });\n  }\n  // Méthode pour gérer les erreurs de génération\n  handleGenerationError(messageIndex, errorDetails) {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content: \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\"\n    };\n    this.notificationService.showError('Erreur lors de la génération des tâches: ' + errorDetails);\n  }\n  askQuestion() {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question\n    });\n    const projectContext = {\n      title: this.projectTitle || (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description: \"Projet géré par l'équipe \" + (this.team ? this.team.name : '')\n    };\n    this.aiService.askProjectQuestion(question, projectContext).pipe(finalize(() => this.isAskingQuestion = false)).subscribe({\n      next: response => {\n        // Ajouter la réponse aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: response\n        });\n      },\n      error: error => {\n        console.error(\"Erreur lors de la demande à l'IA:\", error);\n        // Ajouter l'erreur aux messages\n        this.messages.push({\n          role: 'assistant',\n          content: \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\"\n        });\n        this.notificationService.showError(\"Erreur lors de la communication avec l'IA\");\n      }\n    });\n  }\n  createTasks() {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError('Aucune tâche générée ou équipe invalide');\n      return;\n    }\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\"L'équipe n'a pas de membres pour assigner les tâches\");\n      return;\n    }\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map(member => {\n      return typeof member === 'string' ? member : member.userId;\n    });\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n    console.log(\"Membres de l'équipe disponibles pour l'assignation:\", teamMembers);\n    console.log('Mapping des noms de membres vers leurs IDs:', memberNameToIdMap);\n    // Pour chaque entité\n    this.generatedContent.entities.forEach(entity => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId;\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(`Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`);\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(`Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`);\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(`Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`);\n      }\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach(taskData => {\n        const task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId\n        };\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(`${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`);\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError('Erreur lors de la création des tâches');\n          }\n        });\n      });\n    });\n  }\n  countTasks(content) {\n    if (!content || !content.entities) return 0;\n    return content.entities.reduce((total, entity) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index) {\n    // Liste de dégradés prédéfinis\n    const gradients = ['linear-gradient(45deg, #007bff, #6610f2)', 'linear-gradient(45deg, #11998e, #38ef7d)', 'linear-gradient(45deg, #FC5C7D, #6A82FB)', 'linear-gradient(45deg, #FF8008, #FFC837)', 'linear-gradient(45deg, #8E2DE2, #4A00E0)', 'linear-gradient(45deg, #2193b0, #6dd5ed)', 'linear-gradient(45deg, #373B44, #4286f4)', 'linear-gradient(45deg, #834d9b, #d04ed6)', 'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index) {\n    // Liste de couleurs prédéfinies\n    const colors = ['#007bff', '#11998e', '#FC5C7D', '#FF8008', '#8E2DE2', '#2193b0', '#373B44', '#834d9b', '#0cebeb' // Turquoise\n    ];\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName) {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (name.includes('crud') || name.includes('api') || name.includes('données') || name.includes('base')) {\n      return 'bi-database-fill';\n    } else if (name.includes('interface') || name.includes('ui') || name.includes('front') || name.includes('utilisateur')) {\n      return 'bi-window';\n    } else if (name.includes('déploiement') || name.includes('serveur') || name.includes('cloud')) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (name.includes('test') || name.includes('qualité') || name.includes('qa')) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (name.includes('utilisateur') || name.includes('user') || name.includes('profil')) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (name.includes('analytics') || name.includes('statistique') || name.includes('seo')) {\n      return 'bi-graph-up';\n    }\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime() {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n  static {\n    this.ɵfac = function AiChatComponent_Factory(t) {\n      return new (t || AiChatComponent)(i0.ɵɵdirectiveInject(i1.AiService), i0.ɵɵdirectiveInject(i2.TaskService), i0.ɵɵdirectiveInject(i3.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AiChatComponent,\n      selectors: [[\"app-ai-chat\"]],\n      inputs: {\n        team: \"team\"\n      },\n      decls: 19,\n      vars: 9,\n      consts: [[1, \"ai-chat-container\", \"w-100\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"w-100\"], [1, \"card-body\", \"p-0\"], [1, \"chat-messages\", \"p-3\"], [\"chatContainer\", \"\"], [\"class\", \"message mb-3\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message assistant-message mb-3\", 4, \"ngIf\"], [\"class\", \"generated-content p-4 border-top\", 4, \"ngIf\"], [\"class\", \"alert alert-danger m-3\", 4, \"ngIf\"], [1, \"chat-input\", \"p-3\", \"border-top\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-4\", \"shadow-sm\"], [1, \"card-body\", \"p-2\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-white\", \"border-0\"], [1, \"bi\", \"bi-chat-dots\", \"text-primary\"], [\"type\", \"text\", \"placeholder\", \"Posez une question sur la gestion de projet...\", 1, \"form-control\", \"border-0\", \"bg-white\", \"shadow-none\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keyup.enter\"], [1, \"btn\", \"btn-primary\", \"rounded-circle\", 2, \"width\", \"38px\", \"height\", \"38px\", 3, \"disabled\", \"click\"], [1, \"bi\", 3, \"ngClass\"], [1, \"message\", \"mb-3\", 3, \"ngClass\"], [1, \"d-flex\", 3, \"ngClass\"], [1, \"message-avatar\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", 3, \"ngClass\"], [1, \"message-bubble\", \"p-3\", \"rounded-4\", \"shadow-sm\", 3, \"ngClass\"], [1, \"mb-0\", 3, \"innerHTML\"], [1, \"message-time\", \"small\", \"text-muted\", \"mt-1\", \"text-end\"], [1, \"message\", \"assistant-message\", \"mb-3\"], [1, \"d-flex\"], [1, \"message-avatar\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", \"bg-success\"], [1, \"bi\", \"bi-robot\"], [1, \"message-bubble\", \"assistant-bubble\", \"p-3\", \"rounded-4\", \"shadow-sm\"], [1, \"typing-indicator\"], [1, \"generated-content\", \"p-4\", \"border-top\"], [1, \"generated-header\", \"mb-4\", \"p-3\", \"rounded-4\", \"shadow-sm\", 2, \"background\", \"linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-primary\", \"mb-1\"], [1, \"bi\", \"bi-diagram-3-fill\", \"me-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"bi\", \"bi-info-circle\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-people-fill\", \"me-1\"], [1, \"row\", \"mb-4\"], [\"class\", \"col-lg-3 col-md-4 col-sm-6 mb-4\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"generatedTasksAccordion\", 1, \"accordion\", \"d-none\"], [\"class\", \"accordion-item border-0 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-5\"], [1, \"card\", \"border-0\", \"rounded-4\", \"shadow-sm\", \"create-tasks-card\"], [1, \"card-body\", \"p-4\"], [1, \"row\", \"align-items-center\"], [1, \"col-lg-8\"], [1, \"mb-3\", \"text-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [1, \"d-flex\", \"mb-3\"], [1, \"step-circle\", \"bg-success\", \"text-white\", \"me-3\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-0\", \"small\"], [1, \"step-circle\", \"bg-primary\", \"text-white\", \"me-3\"], [1, \"step-circle\", \"bg-info\", \"text-white\", \"me-3\"], [1, \"col-lg-4\", \"text-center\", \"mt-4\", \"mt-lg-0\"], [1, \"btn\", \"btn-success\", \"btn-lg\", \"rounded-pill\", \"px-5\", \"py-3\", \"shadow\", \"create-button\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\", \"me-2\"], [1, \"text-muted\", \"small\", \"mt-2\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"mb-4\"], [1, \"module-card\", \"card\", \"h-100\", \"border-0\", \"shadow-sm\"], [1, \"module-ribbon\", 3, \"ngStyle\"], [1, \"card-header\", \"text-white\", \"position-relative\", \"py-4\", 3, \"ngStyle\"], [1, \"module-icon-large\", \"rounded-circle\", \"bg-white\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"shadow\"], [1, \"bi\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-3\", \"mb-0\", \"text-center\"], [1, \"card-body\"], [1, \"description-box\", \"p-3\", \"rounded-3\", \"bg-light\", \"mb-3\"], [1, \"mb-0\"], [\"class\", \"assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"task-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"pb-2\", \"border-bottom\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-list-check\", \"me-2\", 3, \"ngStyle\"], [1, \"badge\", \"rounded-pill\", 3, \"ngStyle\"], [1, \"task-list\"], [\"class\", \"task-item mb-3 p-3 rounded-3 shadow-sm\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"assignation-badge\", \"mb-3\", \"p-3\", \"rounded-3\", \"d-flex\", \"align-items-center\", 3, \"ngStyle\"], [1, \"member-avatar\", \"rounded-circle\", \"me-3\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"text-white\", 3, \"ngStyle\"], [1, \"bi\", \"bi-person-fill\"], [1, \"small\", \"text-muted\"], [1, \"fw-bold\", 3, \"ngStyle\"], [1, \"task-item\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", 3, \"ngClass\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\", \"task-title\"], [1, \"badge\", \"rounded-pill\", 3, \"ngClass\"], [1, \"task-description\", \"text-muted\", \"small\"], [1, \"accordion-item\", \"border-0\", \"mb-2\"], [1, \"accordion-header\", 3, \"id\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", 1, \"accordion-button\", \"collapsed\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"ms-2\"], [\"data-bs-parent\", \"#generatedTasksAccordion\", 1, \"accordion-collapse\", \"collapse\", 3, \"id\"], [1, \"accordion-body\"], [1, \"text-muted\", \"mb-3\"], [1, \"list-group\"], [\"class\", \"list-group-item list-group-item-action\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"list-group-item-action\"], [1, \"d-flex\", \"w-100\", \"justify-content-between\"], [1, \"badge\", 3, \"ngClass\"], [1, \"mb-1\", \"small\"], [1, \"alert\", \"alert-danger\", \"m-3\"], [1, \"mb-4\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-4\", \"shadow-sm\", \"mb-3\"], [1, \"card-body\", \"p-3\"], [1, \"mb-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-stars\", \"me-2\", \"text-primary\"], [1, \"mb-3\"], [\"for\", \"projectTitle\", 1, \"form-label\", \"small\", \"text-muted\"], [1, \"bi\", \"bi-lightbulb\", \"text-primary\"], [\"type\", \"text\", \"id\", \"projectTitle\", \"placeholder\", \"Ex: Site e-commerce, Application mobile, Syst\\u00E8me de gestion...\", 1, \"form-control\", \"border-0\", \"bg-white\", \"shadow-none\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"text-muted\", \"mt-1\", \"d-block\"], [1, \"d-grid\"], [1, \"btn\", \"btn-primary\", \"rounded-3\", 3, \"disabled\", \"click\"]],\n      template: function AiChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3, 4);\n          i0.ɵɵtemplate(5, AiChatComponent_div_5_Template, 8, 19, \"div\", 5);\n          i0.ɵɵtemplate(6, AiChatComponent_div_6_Template, 9, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, AiChatComponent_div_7_Template, 56, 8, \"div\", 7);\n          i0.ɵɵtemplate(8, AiChatComponent_div_8_Template, 2, 1, \"div\", 8);\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵtemplate(10, AiChatComponent_div_10_Template, 20, 6, \"div\", 10);\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"span\", 14);\n          i0.ɵɵelement(15, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_Template_input_ngModelChange_16_listener($event) {\n            return ctx.userQuestion = $event;\n          })(\"keyup.enter\", function AiChatComponent_Template_input_keyup_enter_16_listener() {\n            return ctx.askQuestion();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AiChatComponent_Template_button_click_17_listener() {\n            return ctx.askQuestion();\n          });\n          i0.ɵɵelement(18, \"i\", 18);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isGenerating || ctx.isAskingQuestion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.generatedContent);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.generatedContent);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.userQuestion)(\"disabled\", ctx.isAskingQuestion);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.isAskingQuestion || !ctx.userQuestion.trim());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", ctx.isAskingQuestion ? \"bi-hourglass-split spin\" : \"bi-send-fill\");\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgStyle, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".ai-chat-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow: hidden;\\n}\\n\\n.chat-messages[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  max-height: 500px; \\n\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 15px;\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.assistant-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n\\n\\n.message-avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 1rem;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  word-wrap: break-word;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.user-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #007bff, #6610f2);\\n  color: white;\\n  border-top-right-radius: 0 !important;\\n}\\n\\n.assistant-bubble[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #343a40;\\n  border-top-left-radius: 0 !important;\\n}\\n\\n\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  opacity: 0.7;\\n}\\n\\n.user-bubble[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8) !important;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  height: 8px;\\n  width: 8px;\\n  background-color: #343a40;\\n  border-radius: 50%;\\n  display: inline-block;\\n  margin-right: 5px;\\n  animation: _ngcontent-%COMP%_typing 1s infinite ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n  margin-right: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0% {\\n    transform: translateY(0px);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: translateY(0px);\\n    opacity: 0.4;\\n  }\\n}\\n\\n\\n\\n.spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1.5s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.generated-content[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n  max-height: 800px; \\n\\n  overflow-y: auto;\\n  width: 100%; \\n\\n}\\n\\n\\n\\n.generated-header[_ngcontent-%COMP%] {\\n  border-left: 5px solid #007bff;\\n}\\n\\n\\n\\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border: none;\\n  height: 100%;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;\\n}\\n\\n\\n\\n.module-ribbon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: -35px;\\n  transform: rotate(45deg);\\n  width: 150px;\\n  text-align: center;\\n  padding: 5px;\\n  font-size: 0.8rem;\\n  font-weight: bold;\\n  color: white;\\n  z-index: 10;\\n  box-shadow: 0 3px 10px rgba(0,0,0,0.1);\\n}\\n\\n.generated-content[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n  font-weight: 600;\\n  padding: 30px 15px;\\n  text-align: center;\\n}\\n\\n\\n\\n.module-icon-large[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  font-size: 2rem;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n\\n\\n.member-avatar[_ngcontent-%COMP%] {\\n  width: 45px;\\n  height: 45px;\\n  font-size: 1.2rem;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.assignation-badge[_ngcontent-%COMP%] {\\n  box-shadow: 0 3px 10px rgba(0,0,0,0.05);\\n  transition: all 0.3s ease;\\n}\\n\\n.assignation-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.08);\\n}\\n\\n\\n\\n.description-box[_ngcontent-%COMP%] {\\n  border-left: 4px solid #e9ecef;\\n  font-style: italic;\\n}\\n\\n\\n\\n.task-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  padding-right: 5px;\\n  margin-bottom: 10px;\\n}\\n\\n.task-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  background-color: white;\\n  z-index: 5;\\n}\\n\\n.task-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-left: 4px solid transparent;\\n  background-color: white;\\n}\\n\\n.task-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\\n}\\n\\n.high-priority[_ngcontent-%COMP%] {\\n  border-left-color: #dc3545;\\n}\\n\\n.medium-priority[_ngcontent-%COMP%] {\\n  border-left-color: #ffc107;\\n}\\n\\n.low-priority[_ngcontent-%COMP%] {\\n  border-left-color: #17a2b8;\\n}\\n\\n.task-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #343a40;\\n}\\n\\n.task-description[_ngcontent-%COMP%] {\\n  padding-top: 8px;\\n  border-top: 1px dashed #dee2e6;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from { opacity: 0; transform: translateY(10px); }\\n  to { opacity: 1; transform: translateY(0); }\\n}\\n\\n\\n\\n.create-tasks-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(120deg, rgba(255,255,255,1), rgba(248,249,250,1));\\n  border-left: 5px solid #28a745;\\n}\\n\\n\\n\\n.step-circle[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.create-button[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3) !important;\\n}\\n\\n.create-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4) !important;\\n}\\n\\n.accordion-button[_ngcontent-%COMP%]:not(.collapsed) {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n}\\n\\n.accordion-button[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: rgba(0,0,0,.125);\\n}\\n\\n.chat-input[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-top: 1px solid #dee2e6;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r6", "role", "ɵɵadvance", "ɵɵpureFunction1", "_c1", "_c2", "_c3", "content", "ɵɵsanitizeHtml", "ɵɵtextInterpolate2", "ctx_r1", "getCurrentTime", "_c5", "ctx_r12", "getColorForIndex", "i_r11", "_c6", "getGradientForIndex", "_c7", "ɵɵtextInterpolate", "entity_r10", "assignedTo", "ɵɵpureFunction3", "_c8", "task_r15", "priority", "title", "_c9", "ɵɵtextInterpolate1", "description", "ɵɵtemplate", "AiChatComponent_div_7_div_14_div_14_Template", "AiChatComponent_div_7_div_14_div_22_Template", "ctx_r8", "getIconForModule", "name", "tasks", "length", "task_r20", "AiChatComponent_div_7_div_16_div_12_Template", "i_r18", "ɵɵattribute", "entity_r17", "AiChatComponent_div_7_div_14_Template", "AiChatComponent_div_7_div_16_Template", "ɵɵlistener", "AiChatComponent_div_7_Template_button_click_50_listener", "ɵɵrestoreView", "_r22", "ctx_r21", "ɵɵnextContext", "ɵɵresetView", "createTasks", "ctx_r3", "generatedContent", "projectTitle", "entities", "countTasks", "team", "members", "ctx_r4", "error", "AiChatComponent_div_10_Template_input_ngModelChange_12_listener", "$event", "_r24", "ctx_r23", "AiChatComponent_div_10_Template_button_click_17_listener", "ctx_r25", "generateTasks", "ctx_r5", "isGenerating", "trim", "AiChatComponent", "constructor", "aiService", "taskService", "notificationService", "messages", "userQuestion", "isAskingQuestion", "ngOnInit", "push", "showError", "memberCount", "effectiveMemberCount", "Math", "max", "showWarning", "console", "log", "loadingMessageIndex", "teamMembers", "map", "memberId", "index", "id", "generateProjectTasks", "pipe", "subscribe", "next", "result", "handleGenerationError", "showSuccess", "message", "messageIndex", "errorDetails", "askQuestion", "question", "projectContext", "askProjectQuestion", "response", "_id", "createdCount", "totalTasks", "member", "userId", "memberNameToIdMap", "for<PERSON>ach", "entity", "assignedMemberId", "memberName", "randomMemberIndex", "floor", "random", "taskData", "task", "status", "teamId", "createTask", "reduce", "total", "gradients", "colors", "moduleName", "toLowerCase", "includes", "now", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "ɵɵdirectiveInject", "i1", "AiService", "i2", "TaskService", "i3", "NotificationService", "selectors", "inputs", "decls", "vars", "consts", "template", "AiChatComponent_Template", "rf", "ctx", "AiChatComponent_div_5_Template", "AiChatComponent_div_6_Template", "AiChatComponent_div_7_Template", "AiChatComponent_div_8_Template", "AiChatComponent_div_10_Template", "AiChatComponent_Template_input_ngModelChange_16_listener", "AiChatComponent_Template_input_keyup_enter_16_listener", "AiChatComponent_Template_button_click_17_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\ai-chat\\ai-chat.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\ai-chat\\ai-chat.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { AiService } from 'src/app/services/ai.service';\r\nimport { TaskService } from 'src/app/services/task.service';\r\nimport { NotificationService } from 'src/app/services/notification.service';\r\nimport { Equipe } from 'src/app/models/equipe.model';\r\nimport { Task } from 'src/app/models/task.model';\r\nimport { finalize } from 'rxjs/operators';\r\n@Component({\r\n  selector: 'app-ai-chat',\r\n  templateUrl: './ai-chat.component.html',\r\n  styleUrls: ['./ai-chat.component.css'],\r\n})\r\nexport class AiChatComponent implements OnInit {\r\n  @Input() team!: Equipe;\r\n\r\n  projectTitle: string = '';\r\n  isGenerating: boolean = false;\r\n  generatedContent: any = null;\r\n  error: string | null = null;\r\n\r\n  // Pour le chat\r\n  messages: { role: 'user' | 'assistant'; content: string }[] = [];\r\n  userQuestion: string = '';\r\n  isAskingQuestion: boolean = false;\r\n\r\n  constructor(\r\n    private aiService: AiService,\r\n    private taskService: TaskService,\r\n    private notificationService: NotificationService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Ajouter un message de bienvenue\r\n    this.messages.push({\r\n      role: 'assistant',\r\n      content:\r\n        'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.',\r\n    });\r\n  }\r\n\r\n  generateTasks(): void {\r\n    if (!this.projectTitle.trim()) {\r\n      this.notificationService.showError('Veuillez entrer un titre de projet');\r\n      return;\r\n    }\r\n\r\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\r\n    let memberCount =\r\n      this.team && this.team.members ? this.team.members.length : 3;\r\n\r\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\r\n    const effectiveMemberCount = Math.max(memberCount, 3);\r\n\r\n    if (memberCount === 0) {\r\n      this.notificationService.showWarning(\r\n        \"L'équipe n'a pas de membres. Des tâches génériques seront créées.\"\r\n      );\r\n    }\r\n\r\n    this.isGenerating = true;\r\n    this.error = null;\r\n\r\n    console.log(\r\n      `Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`\r\n    );\r\n\r\n    // Ajouter la demande aux messages\r\n    this.messages.push({\r\n      role: 'user',\r\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`,\r\n    });\r\n\r\n    // Ajouter un message de chargement\r\n    const loadingMessageIndex = this.messages.length;\r\n    this.messages.push({\r\n      role: 'assistant',\r\n      content: 'Je génère des tâches pour votre projet...',\r\n    });\r\n\r\n    // Récupérer les informations sur les membres de l'équipe\r\n    let teamMembers: any[] = [];\r\n    if (this.team && this.team.members) {\r\n      // Utiliser les IDs des membres\r\n      teamMembers = this.team.members.map((memberId: string, index: number) => {\r\n        return { id: memberId, name: `Membre ${index + 1}`, role: 'membre' };\r\n      });\r\n\r\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\r\n    }\r\n\r\n    this.aiService\r\n      .generateProjectTasks(this.projectTitle, memberCount, teamMembers)\r\n      .pipe(finalize(() => (this.isGenerating = false)))\r\n      .subscribe({\r\n        next: (result: any) => {\r\n          if (!result || !result.entities || result.entities.length === 0) {\r\n            console.error(\"Résultat invalide reçu de l'API:\", result);\r\n            this.handleGenerationError(\r\n              loadingMessageIndex,\r\n              'Format de réponse invalide'\r\n            );\r\n            return;\r\n          }\r\n\r\n          this.generatedContent = result;\r\n\r\n          // Remplacer le message de chargement par la réponse\r\n          this.messages[loadingMessageIndex] = {\r\n            role: 'assistant',\r\n            content: `J'ai généré ${\r\n              result.entities.length\r\n            } entités pour votre projet \"${\r\n              result.projectTitle\r\n            }\" avec un total de ${this.countTasks(result)} tâches.`,\r\n          };\r\n\r\n          this.notificationService.showSuccess('Tâches générées avec succès');\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Erreur lors de la génération des tâches:', error);\r\n          this.handleGenerationError(\r\n            loadingMessageIndex,\r\n            error.message || 'Erreur inconnue'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  // Méthode pour gérer les erreurs de génération\r\n  private handleGenerationError(\r\n    messageIndex: number,\r\n    errorDetails: string\r\n  ): void {\r\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\r\n\r\n    // Remplacer le message de chargement par le message d'erreur\r\n    this.messages[messageIndex] = {\r\n      role: 'assistant',\r\n      content:\r\n        \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\",\r\n    };\r\n\r\n    this.notificationService.showError(\r\n      'Erreur lors de la génération des tâches: ' + errorDetails\r\n    );\r\n  }\r\n\r\n  askQuestion(): void {\r\n    if (!this.userQuestion.trim()) {\r\n      return;\r\n    }\r\n\r\n    const question = this.userQuestion.trim();\r\n    this.userQuestion = '';\r\n    this.isAskingQuestion = true;\r\n\r\n    // Ajouter la question aux messages\r\n    this.messages.push({\r\n      role: 'user',\r\n      content: question,\r\n    });\r\n\r\n    const projectContext = {\r\n      title:\r\n        this.projectTitle ||\r\n        (this.generatedContent ? this.generatedContent.projectTitle : ''),\r\n      description:\r\n        \"Projet géré par l'équipe \" + (this.team ? this.team.name : ''),\r\n    };\r\n\r\n    this.aiService\r\n      .askProjectQuestion(question, projectContext)\r\n      .pipe(finalize(() => (this.isAskingQuestion = false)))\r\n      .subscribe({\r\n        next: (response: string) => {\r\n          // Ajouter la réponse aux messages\r\n          this.messages.push({\r\n            role: 'assistant',\r\n            content: response,\r\n          });\r\n        },\r\n        error: (error: any) => {\r\n          console.error(\"Erreur lors de la demande à l'IA:\", error);\r\n\r\n          // Ajouter l'erreur aux messages\r\n          this.messages.push({\r\n            role: 'assistant',\r\n            content:\r\n              \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\",\r\n          });\r\n\r\n          this.notificationService.showError(\r\n            \"Erreur lors de la communication avec l'IA\"\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  createTasks(): void {\r\n    if (!this.generatedContent || !this.team || !this.team._id) {\r\n      this.notificationService.showError(\r\n        'Aucune tâche générée ou équipe invalide'\r\n      );\r\n      return;\r\n    }\r\n\r\n    let createdCount = 0;\r\n    const totalTasks = this.countTasks(this.generatedContent);\r\n\r\n    // Vérifier si l'équipe a des membres\r\n    if (!this.team.members || this.team.members.length === 0) {\r\n      this.notificationService.showError(\r\n        \"L'équipe n'a pas de membres pour assigner les tâches\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Préparer la liste des membres de l'équipe\r\n    const teamMembers = this.team.members.map((member) => {\r\n      return typeof member === 'string' ? member : (member as any).userId;\r\n    });\r\n\r\n    // Créer un mapping des noms de membres vers leurs IDs\r\n    const memberNameToIdMap: { [key: string]: string } = {};\r\n    teamMembers.forEach((memberId, index) => {\r\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\r\n    });\r\n\r\n    console.log(\r\n      \"Membres de l'équipe disponibles pour l'assignation:\",\r\n      teamMembers\r\n    );\r\n    console.log(\r\n      'Mapping des noms de membres vers leurs IDs:',\r\n      memberNameToIdMap\r\n    );\r\n\r\n    // Pour chaque entité\r\n    this.generatedContent.entities.forEach((entity: any) => {\r\n      // Déterminer le membre assigné à cette entité\r\n      let assignedMemberId: string | undefined;\r\n\r\n      // Si l'IA a suggéré une assignation\r\n      if (entity.assignedTo) {\r\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\r\n        const memberName = entity.assignedTo;\r\n        if (memberNameToIdMap[memberName]) {\r\n          assignedMemberId = memberNameToIdMap[memberName];\r\n          console.log(\r\n            `Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`\r\n          );\r\n        } else {\r\n          // Si le nom n'est pas trouvé, assigner aléatoirement\r\n          const randomMemberIndex = Math.floor(\r\n            Math.random() * teamMembers.length\r\n          );\r\n          assignedMemberId = teamMembers[randomMemberIndex];\r\n          console.log(\r\n            `Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`\r\n          );\r\n        }\r\n      } else {\r\n        // Si pas d'assignation suggérée, assigner aléatoirement\r\n        const randomMemberIndex = Math.floor(\r\n          Math.random() * teamMembers.length\r\n        );\r\n        assignedMemberId = teamMembers[randomMemberIndex];\r\n        console.log(\r\n          `Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`\r\n        );\r\n      }\r\n\r\n      // Pour chaque tâche dans l'entité\r\n      entity.tasks.forEach((taskData: any) => {\r\n        const task: Task = {\r\n          title: taskData.title,\r\n          description: `[${entity.name}] ${taskData.description}`,\r\n          status: taskData.status || 'todo',\r\n          priority: taskData.priority || 'medium',\r\n          teamId: this.team._id || '',\r\n          // Utiliser l'ID du membre assigné à l'entité\r\n          assignedTo: assignedMemberId,\r\n        };\r\n\r\n        this.taskService.createTask(task).subscribe({\r\n          next: () => {\r\n            createdCount++;\r\n            if (createdCount === totalTasks) {\r\n              this.notificationService.showSuccess(\r\n                `${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`\r\n              );\r\n              // Réinitialiser après création\r\n              this.generatedContent = null;\r\n              this.projectTitle = '';\r\n            }\r\n          },\r\n          error: (error) => {\r\n            console.error('Erreur lors de la création de la tâche:', error);\r\n            this.notificationService.showError(\r\n              'Erreur lors de la création des tâches'\r\n            );\r\n          },\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  countTasks(content: any): number {\r\n    if (!content || !content.entities) return 0;\r\n\r\n    return content.entities.reduce((total: number, entity: any) => {\r\n      return total + (entity.tasks ? entity.tasks.length : 0);\r\n    }, 0);\r\n  }\r\n\r\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\r\n  getGradientForIndex(index: number): string {\r\n    // Liste de dégradés prédéfinis\r\n    const gradients = [\r\n      'linear-gradient(45deg, #007bff, #6610f2)', // Bleu-Violet\r\n      'linear-gradient(45deg, #11998e, #38ef7d)', // Vert\r\n      'linear-gradient(45deg, #FC5C7D, #6A82FB)', // Rose-Bleu\r\n      'linear-gradient(45deg, #FF8008, #FFC837)', // Orange-Jaune\r\n      'linear-gradient(45deg, #8E2DE2, #4A00E0)', // Violet\r\n      'linear-gradient(45deg, #2193b0, #6dd5ed)', // Bleu clair\r\n      'linear-gradient(45deg, #373B44, #4286f4)', // Gris-Bleu\r\n      'linear-gradient(45deg, #834d9b, #d04ed6)', // Violet-Rose\r\n      'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)', // Turquoise\r\n    ];\r\n\r\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\r\n    return gradients[index % gradients.length];\r\n  }\r\n\r\n  // Méthode pour obtenir une couleur unique basée sur l'index\r\n  getColorForIndex(index: number): string {\r\n    // Liste de couleurs prédéfinies\r\n    const colors = [\r\n      '#007bff', // Bleu\r\n      '#11998e', // Vert\r\n      '#FC5C7D', // Rose\r\n      '#FF8008', // Orange\r\n      '#8E2DE2', // Violet\r\n      '#2193b0', // Bleu clair\r\n      '#373B44', // Gris foncé\r\n      '#834d9b', // Violet\r\n      '#0cebeb', // Turquoise\r\n    ];\r\n\r\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\r\n    return colors[index % colors.length];\r\n  }\r\n\r\n  // Méthode pour obtenir une icône en fonction du nom du module\r\n  getIconForModule(moduleName: string): string {\r\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\r\n    const name = moduleName.toLowerCase();\r\n\r\n    // Mapper les noms de modules courants à des icônes Bootstrap\r\n    if (\r\n      name.includes('crud') ||\r\n      name.includes('api') ||\r\n      name.includes('données') ||\r\n      name.includes('base')\r\n    ) {\r\n      return 'bi-database-fill';\r\n    } else if (\r\n      name.includes('interface') ||\r\n      name.includes('ui') ||\r\n      name.includes('front') ||\r\n      name.includes('utilisateur')\r\n    ) {\r\n      return 'bi-window';\r\n    } else if (\r\n      name.includes('déploiement') ||\r\n      name.includes('serveur') ||\r\n      name.includes('cloud')\r\n    ) {\r\n      return 'bi-cloud-arrow-up-fill';\r\n    } else if (\r\n      name.includes('test') ||\r\n      name.includes('qualité') ||\r\n      name.includes('qa')\r\n    ) {\r\n      return 'bi-bug-fill';\r\n    } else if (name.includes('sécurité') || name.includes('auth')) {\r\n      return 'bi-shield-lock-fill';\r\n    } else if (name.includes('paiement') || name.includes('transaction')) {\r\n      return 'bi-credit-card-fill';\r\n    } else if (\r\n      name.includes('utilisateur') ||\r\n      name.includes('user') ||\r\n      name.includes('profil')\r\n    ) {\r\n      return 'bi-person-fill';\r\n    } else if (name.includes('doc') || name.includes('documentation')) {\r\n      return 'bi-file-text-fill';\r\n    } else if (name.includes('mobile') || name.includes('app')) {\r\n      return 'bi-phone-fill';\r\n    } else if (name.includes('backend') || name.includes('serveur')) {\r\n      return 'bi-server';\r\n    } else if (\r\n      name.includes('analytics') ||\r\n      name.includes('statistique') ||\r\n      name.includes('seo')\r\n    ) {\r\n      return 'bi-graph-up';\r\n    }\r\n\r\n    // Icône par défaut si aucune correspondance n'est trouvée\r\n    return 'bi-code-square';\r\n  }\r\n\r\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\r\n  getCurrentTime(): string {\r\n    const now = new Date();\r\n    const hours = now.getHours().toString().padStart(2, '0');\r\n    const minutes = now.getMinutes().toString().padStart(2, '0');\r\n    return `${hours}:${minutes}`;\r\n  }\r\n}\r\n", "<div class=\"ai-chat-container w-100\">\r\n  <div class=\"card border-0 shadow-sm w-100\">\r\n    <!-- Suppression de l'en-tête redondant car il est déjà présent dans le composant parent -->\r\n    <div class=\"card-body p-0\">\r\n      <!-- Messages du chat améliorés -->\r\n      <div class=\"chat-messages p-3\" #chatContainer>\r\n        <div *ngFor=\"let message of messages; let i = index\"\r\n             class=\"message mb-3\"\r\n             [ngClass]=\"{'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant'}\">\r\n          <!-- Avatar et contenu du message -->\r\n          <div class=\"d-flex\" [ngClass]=\"{'flex-row-reverse': message.role === 'user'}\">\r\n            <!-- Avatar -->\r\n            <div class=\"message-avatar rounded-circle d-flex align-items-center justify-content-center me-2\"\r\n                 [ngClass]=\"{'bg-primary': message.role === 'user', 'bg-success': message.role === 'assistant'}\">\r\n              <i class=\"bi\" [ngClass]=\"message.role === 'user' ? 'bi-person-fill' : 'bi-robot'\"></i>\r\n            </div>\r\n\r\n            <!-- Contenu du message -->\r\n            <div class=\"message-bubble p-3 rounded-4 shadow-sm\"\r\n                 [ngClass]=\"{'user-bubble': message.role === 'user', 'assistant-bubble': message.role === 'assistant'}\">\r\n              <p class=\"mb-0\" [innerHTML]=\"message.content\"></p>\r\n\r\n              <!-- Horodatage -->\r\n              <div class=\"message-time small text-muted mt-1 text-end\">\r\n                {{ message.role === 'user' ? 'Vous' : 'Assistant IA' }} • {{ getCurrentTime() }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Indicateur de chargement amélioré -->\r\n        <div *ngIf=\"isGenerating || isAskingQuestion\" class=\"message assistant-message mb-3\">\r\n          <div class=\"d-flex\">\r\n            <!-- Avatar -->\r\n            <div class=\"message-avatar rounded-circle d-flex align-items-center justify-content-center me-2 bg-success\">\r\n              <i class=\"bi bi-robot\"></i>\r\n            </div>\r\n\r\n            <!-- Indicateur de chargement -->\r\n            <div class=\"message-bubble assistant-bubble p-3 rounded-4 shadow-sm\">\r\n              <div class=\"typing-indicator\">\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Résultats générés avec en-tête amélioré -->\r\n      <div *ngIf=\"generatedContent\" class=\"generated-content p-4 border-top\">\r\n        <div class=\"generated-header mb-4 p-3 rounded-4 shadow-sm\"\r\n             style=\"background: linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div>\r\n              <h5 class=\"text-primary mb-1\">\r\n                <i class=\"bi bi-diagram-3-fill me-2\"></i>\r\n                Plan du projet \"{{ generatedContent.projectTitle }}\"\r\n              </h5>\r\n              <p class=\"text-muted mb-0\">\r\n                <i class=\"bi bi-info-circle me-1\"></i>\r\n                {{ generatedContent.entities.length }} modules générés avec {{ countTasks(generatedContent) }} tâches au total\r\n              </p>\r\n            </div>\r\n            <span class=\"badge bg-primary rounded-pill px-3 py-2\">\r\n              <i class=\"bi bi-people-fill me-1\"></i>\r\n              {{ team && team.members ? team.members.length : 0 }} membres\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Affichage des modules avec une présentation plus claire et adaptée à la largeur complète -->\r\n        <div class=\"row mb-4\">\r\n          <div *ngFor=\"let entity of generatedContent.entities; let i = index\" class=\"col-lg-3 col-md-4 col-sm-6 mb-4\">\r\n            <div class=\"module-card card h-100 border-0 shadow-sm\">\r\n              <!-- Ruban indiquant le numéro du module -->\r\n              <div class=\"module-ribbon\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\r\n                <span>Module {{ i + 1 }}</span>\r\n              </div>\r\n\r\n              <!-- En-tête avec couleur dynamique basée sur l'index -->\r\n              <div class=\"card-header text-white position-relative py-4\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\r\n                <div class=\"module-icon-large rounded-circle bg-white d-flex align-items-center justify-content-center shadow\">\r\n                  <i class=\"bi\" [ngClass]=\"getIconForModule(entity.name)\" [ngStyle]=\"{'color': getColorForIndex(i)}\"></i>\r\n                </div>\r\n                <h5 class=\"mt-3 mb-0 text-center\">{{ entity.name }}</h5>\r\n              </div>\r\n\r\n              <div class=\"card-body\">\r\n                <!-- Description -->\r\n                <div class=\"description-box p-3 rounded-3 bg-light mb-3\">\r\n                  <p class=\"mb-0\">{{ entity.description }}</p>\r\n                </div>\r\n\r\n                <!-- Assignation avec style amélioré -->\r\n                <div *ngIf=\"entity.assignedTo\" class=\"assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center\"\r\n                     [ngStyle]=\"{'background': 'linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',\r\n                                'border-left': '4px solid ' + getColorForIndex(i)}\">\r\n                  <div class=\"member-avatar rounded-circle me-3 d-flex align-items-center justify-content-center text-white\"\r\n                       [ngStyle]=\"{'background': getGradientForIndex(i)}\">\r\n                    <i class=\"bi bi-person-fill\"></i>\r\n                  </div>\r\n                  <div>\r\n                    <div class=\"small text-muted\">Responsable</div>\r\n                    <div class=\"fw-bold\" [ngStyle]=\"{'color': getColorForIndex(i)}\">{{ entity.assignedTo }}</div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- En-tête de la liste des tâches -->\r\n                <div class=\"task-header d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom\">\r\n                  <h6 class=\"mb-0 d-flex align-items-center\">\r\n                    <i class=\"bi bi-list-check me-2\" [ngStyle]=\"{'color': getColorForIndex(i)}\"></i>\r\n                    Tâches à réaliser\r\n                  </h6>\r\n                  <span class=\"badge rounded-pill\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\r\n                    {{ entity.tasks.length }} tâches\r\n                  </span>\r\n                </div>\r\n\r\n                <!-- Liste des tâches avec design amélioré -->\r\n                <div class=\"task-list\">\r\n                  <div *ngFor=\"let task of entity.tasks; let j = index\"\r\n                       class=\"task-item mb-3 p-3 rounded-3 shadow-sm\"\r\n                       [ngClass]=\"{'high-priority': task.priority === 'high',\r\n                                  'medium-priority': task.priority === 'medium',\r\n                                  'low-priority': task.priority === 'low'}\">\r\n                    <!-- Titre et priorité -->\r\n                    <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <h6 class=\"mb-0 task-title\">{{ task.title }}</h6>\r\n                      <span class=\"badge rounded-pill\" [ngClass]=\"{\r\n                        'bg-danger': task.priority === 'high',\r\n                        'bg-warning text-dark': task.priority === 'medium',\r\n                        'bg-info text-dark': task.priority === 'low'\r\n                      }\">\r\n                        {{ task.priority === 'high' ? 'Haute' :\r\n                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <!-- Description de la tâche (toujours visible) -->\r\n                    <div class=\"task-description text-muted small\">\r\n                      {{ task.description }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Affichage détaillé (accordéon) -->\r\n        <div class=\"accordion d-none\" id=\"generatedTasksAccordion\">\r\n          <div *ngFor=\"let entity of generatedContent.entities; let i = index\" class=\"accordion-item border-0 mb-2\">\r\n            <h2 class=\"accordion-header\" [id]=\"'heading' + i\">\r\n              <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\"\r\n                      [attr.data-bs-target]=\"'#collapse' + i\" [attr.aria-expanded]=\"i === 0\" [attr.aria-controls]=\"'collapse' + i\">\r\n                <strong>{{ entity.name }}</strong>\r\n                <span class=\"badge bg-primary rounded-pill ms-2\">{{ entity.tasks.length }} tâches</span>\r\n              </button>\r\n            </h2>\r\n            <div [id]=\"'collapse' + i\" class=\"accordion-collapse collapse\" [attr.aria-labelledby]=\"'heading' + i\" data-bs-parent=\"#generatedTasksAccordion\">\r\n              <div class=\"accordion-body\">\r\n                <p class=\"text-muted mb-3\">{{ entity.description }}</p>\r\n\r\n                <div class=\"list-group\">\r\n                  <div *ngFor=\"let task of entity.tasks\" class=\"list-group-item list-group-item-action\">\r\n                    <div class=\"d-flex w-100 justify-content-between\">\r\n                      <h6 class=\"mb-1\">{{ task.title }}</h6>\r\n                      <span class=\"badge\" [ngClass]=\"{\r\n                        'bg-danger': task.priority === 'high',\r\n                        'bg-warning text-dark': task.priority === 'medium',\r\n                        'bg-info text-dark': task.priority === 'low'\r\n                      }\">\r\n                        {{ task.priority === 'high' ? 'Haute' :\r\n                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}\r\n                      </span>\r\n                    </div>\r\n                    <p class=\"mb-1 small\">{{ task.description }}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton de création de tâches amélioré -->\r\n        <div class=\"mt-5\">\r\n          <div class=\"card border-0 rounded-4 shadow-sm create-tasks-card\">\r\n            <div class=\"card-body p-4\">\r\n              <div class=\"row align-items-center\">\r\n                <div class=\"col-lg-8\">\r\n                  <h5 class=\"mb-3 text-success\">\r\n                    <i class=\"bi bi-check-circle-fill me-2\"></i>\r\n                    Plan de projet prêt à être implémenté\r\n                  </h5>\r\n                  <div class=\"d-flex mb-3\">\r\n                    <div class=\"step-circle bg-success text-white me-3\">1</div>\r\n                    <div>\r\n                      <h6 class=\"mb-1\">Création des tâches</h6>\r\n                      <p class=\"text-muted mb-0 small\">{{ countTasks(generatedContent) }} tâches seront créées dans le système</p>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"d-flex mb-3\">\r\n                    <div class=\"step-circle bg-primary text-white me-3\">2</div>\r\n                    <div>\r\n                      <h6 class=\"mb-1\">Assignation aux membres</h6>\r\n                      <p class=\"text-muted mb-0 small\">Les tâches seront assignées aux {{ team && team.members ? team.members.length : 0 }} membres de l'équipe</p>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"d-flex\">\r\n                    <div class=\"step-circle bg-info text-white me-3\">3</div>\r\n                    <div>\r\n                      <h6 class=\"mb-1\">Suivi du projet</h6>\r\n                      <p class=\"text-muted mb-0 small\">Vous pourrez suivre l'avancement dans le tableau de bord des tâches</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-lg-4 text-center mt-4 mt-lg-0\">\r\n                  <button class=\"btn btn-success btn-lg rounded-pill px-5 py-3 shadow create-button\" (click)=\"createTasks()\">\r\n                    <i class=\"bi bi-plus-circle-fill me-2\"></i> Créer les tâches\r\n                  </button>\r\n                  <div class=\"text-muted small mt-2\">\r\n                    <i class=\"bi bi-info-circle me-1\"></i>\r\n                    Cette action est irréversible\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Message d'erreur -->\r\n      <div *ngIf=\"error\" class=\"alert alert-danger m-3\">\r\n        {{ error }}\r\n      </div>\r\n\r\n      <!-- Entrée utilisateur améliorée -->\r\n      <div class=\"chat-input p-3 border-top\">\r\n        <div *ngIf=\"!generatedContent\" class=\"mb-4\">\r\n          <div class=\"card border-0 bg-light rounded-4 shadow-sm mb-3\">\r\n            <div class=\"card-body p-3\">\r\n              <h6 class=\"mb-3 d-flex align-items-center\">\r\n                <i class=\"bi bi-stars me-2 text-primary\"></i>\r\n                Générer des tâches avec l'IA\r\n              </h6>\r\n\r\n              <div class=\"mb-3\">\r\n                <label for=\"projectTitle\" class=\"form-label small text-muted\">Entrez le titre de votre projet</label>\r\n                <div class=\"input-group\">\r\n                  <span class=\"input-group-text bg-white border-0\">\r\n                    <i class=\"bi bi-lightbulb text-primary\"></i>\r\n                  </span>\r\n                  <input type=\"text\" id=\"projectTitle\" class=\"form-control border-0 bg-white shadow-none\"\r\n                         placeholder=\"Ex: Site e-commerce, Application mobile, Système de gestion...\"\r\n                         [(ngModel)]=\"projectTitle\" [disabled]=\"isGenerating\">\r\n                </div>\r\n                <small class=\"text-muted mt-1 d-block\">\r\n                  <i class=\"bi bi-info-circle me-1\"></i>\r\n                  L'IA générera {{ team && team.members ? team.members.length : 3 }} modules, un pour chaque membre de l'équipe.\r\n                </small>\r\n              </div>\r\n\r\n              <div class=\"d-grid\">\r\n                <button class=\"btn btn-primary rounded-3\"\r\n                        (click)=\"generateTasks()\"\r\n                        [disabled]=\"isGenerating || !projectTitle.trim()\">\r\n                  <i class=\"bi\" [ngClass]=\"isGenerating ? 'bi-hourglass-split spin' : 'bi-magic'\"></i>\r\n                  {{ isGenerating ? 'Génération en cours...' : 'Générer des tâches' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"card border-0 bg-light rounded-4 shadow-sm\">\r\n          <div class=\"card-body p-2\">\r\n            <div class=\"input-group\">\r\n              <span class=\"input-group-text bg-white border-0\">\r\n                <i class=\"bi bi-chat-dots text-primary\"></i>\r\n              </span>\r\n              <input type=\"text\" class=\"form-control border-0 bg-white shadow-none\"\r\n                     placeholder=\"Posez une question sur la gestion de projet...\"\r\n                     [(ngModel)]=\"userQuestion\" (keyup.enter)=\"askQuestion()\"\r\n                     [disabled]=\"isAskingQuestion\">\r\n              <button class=\"btn btn-primary rounded-circle\" style=\"width: 38px; height: 38px;\"\r\n                      (click)=\"askQuestion()\" [disabled]=\"isAskingQuestion || !userQuestion.trim()\">\r\n                <i class=\"bi\" [ngClass]=\"isAskingQuestion ? 'bi-hourglass-split spin' : 'bi-send-fill'\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,SAASA,QAAQ,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAjCC,EAAA,CAAAC,cAAA,cAE8G;IAMxGD,EAAA,CAAAE,SAAA,YAAsF;IACxFF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAC4G;IAC1GD,EAAA,CAAAE,SAAA,YAAkD;IAGlDF,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAjBPH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAAwG;IAEvFT,EAAA,CAAAU,SAAA,GAAyD;IAAzDV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,KAAAC,GAAA,EAAAJ,UAAA,CAAAC,IAAA,aAAyD;IAGtET,EAAA,CAAAU,SAAA,GAA+F;IAA/FV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,KAAAO,GAAA,EAAAL,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAA+F;IACpFT,EAAA,CAAAU,SAAA,GAAmE;IAAnEV,EAAA,CAAAK,UAAA,YAAAG,UAAA,CAAAC,IAAA,4CAAmE;IAK9ET,EAAA,CAAAU,SAAA,GAAsG;IAAtGV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,KAAAQ,GAAA,EAAAN,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAAsG;IACzFT,EAAA,CAAAU,SAAA,GAA6B;IAA7BV,EAAA,CAAAK,UAAA,cAAAG,UAAA,CAAAO,OAAA,EAAAf,EAAA,CAAAgB,cAAA,CAA6B;IAI3ChB,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAiB,kBAAA,MAAAT,UAAA,CAAAC,IAAA,mDAAAS,MAAA,CAAAC,cAAA,QACF;;;;;IAMNnB,EAAA,CAAAC,cAAA,cAAqF;IAI/ED,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAqE;IAEjED,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;;;;;;;;;IAoDJH,EAAA,CAAAC,cAAA,cAEoE;IAGhED,EAAA,CAAAE,SAAA,YAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAC2BD,EAAA,CAAAI,MAAA,kBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAAgE;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAR5FH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAS,GAAA,iBAAAC,OAAA,CAAAC,gBAAA,CAAAC,KAAA,GAC8D;IAE5DvB,EAAA,CAAAU,SAAA,GAAkD;IAAlDV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAa,GAAA,EAAAH,OAAA,CAAAI,mBAAA,CAAAF,KAAA,GAAkD;IAKhCvB,EAAA,CAAAU,SAAA,GAA0C;IAA1CV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,IAAAe,GAAA,EAAAL,OAAA,CAAAC,gBAAA,CAAAC,KAAA,GAA0C;IAACvB,EAAA,CAAAU,SAAA,GAAuB;IAAvBV,EAAA,CAAA2B,iBAAA,CAAAC,UAAA,CAAAC,UAAA,CAAuB;;;;;;;;;;;;;;;;;;;IAiBzF7B,EAAA,CAAAC,cAAA,cAI0D;IAG1BD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,eAIG;IACDD,EAAA,CAAAI,MAAA,GAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAnBHH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAEoD;IAGzBjC,EAAA,CAAAU,SAAA,GAAgB;IAAhBV,EAAA,CAAA2B,iBAAA,CAAAK,QAAA,CAAAE,KAAA,CAAgB;IACXlC,EAAA,CAAAU,SAAA,GAI/B;IAJ+BV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA8B,eAAA,IAAAK,GAAA,EAAAH,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAI/B;IACAjC,EAAA,CAAAU,SAAA,GAEF;IAFEV,EAAA,CAAAoC,kBAAA,MAAAJ,QAAA,CAAAC,QAAA,wBAAAD,QAAA,CAAAC,QAAA,yCAEF;IAKAjC,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,MAAAJ,QAAA,CAAAK,WAAA,MACF;;;;;IArEVrC,EAAA,CAAAC,cAAA,cAA6G;IAIjGD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIjCH,EAAA,CAAAC,cAAA,cAA8G;IAE1GD,EAAA,CAAAE,SAAA,YAAuG;IACzGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAkC;IAAAD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAG1DH,EAAA,CAAAC,cAAA,eAAuB;IAGHD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI9CH,EAAA,CAAAsC,UAAA,KAAAC,4CAAA,mBAWM;IAGNvC,EAAA,CAAAC,cAAA,eAAmG;IAE/FD,EAAA,CAAAE,SAAA,aAAgF;IAChFF,EAAA,CAAAI,MAAA,0CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAAoF;IAClFD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAsC,UAAA,KAAAE,4CAAA,mBAsBM;IACRxC,EAAA,CAAAG,YAAA,EAAM;;;;;;IApEmBH,EAAA,CAAAU,SAAA,GAAkD;IAAlDV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,KAAAa,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IACrEvB,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAoC,kBAAA,YAAAb,KAAA,SAAkB;IAIiCvB,EAAA,CAAAU,SAAA,GAAkD;IAAlDV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,KAAAa,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IAE3FvB,EAAA,CAAAU,SAAA,GAAyC;IAAzCV,EAAA,CAAAK,UAAA,YAAAoC,MAAA,CAAAC,gBAAA,CAAAd,UAAA,CAAAe,IAAA,EAAyC,YAAA3C,EAAA,CAAAW,eAAA,KAAAe,GAAA,EAAAe,MAAA,CAAAnB,gBAAA,CAAAC,KAAA;IAEvBvB,EAAA,CAAAU,SAAA,GAAiB;IAAjBV,EAAA,CAAA2B,iBAAA,CAAAC,UAAA,CAAAe,IAAA,CAAiB;IAMjC3C,EAAA,CAAAU,SAAA,GAAwB;IAAxBV,EAAA,CAAA2B,iBAAA,CAAAC,UAAA,CAAAS,WAAA,CAAwB;IAIpCrC,EAAA,CAAAU,SAAA,GAAuB;IAAvBV,EAAA,CAAAK,UAAA,SAAAuB,UAAA,CAAAC,UAAA,CAAuB;IAgBQ7B,EAAA,CAAAU,SAAA,GAA0C;IAA1CV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,KAAAe,GAAA,EAAAe,MAAA,CAAAnB,gBAAA,CAAAC,KAAA,GAA0C;IAG5CvB,EAAA,CAAAU,SAAA,GAAkD;IAAlDV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAW,eAAA,KAAAa,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IACjFvB,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,MAAAR,UAAA,CAAAgB,KAAA,CAAAC,MAAA,kBACF;IAKsB7C,EAAA,CAAAU,SAAA,GAAiB;IAAjBV,EAAA,CAAAK,UAAA,YAAAuB,UAAA,CAAAgB,KAAA,CAAiB;;;;;IA4CvC5C,EAAA,CAAAC,cAAA,cAAsF;IAEjED,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,eAIG;IACDD,EAAA,CAAAI,MAAA,GAEF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAV7BH,EAAA,CAAAU,SAAA,GAAgB;IAAhBV,EAAA,CAAA2B,iBAAA,CAAAmB,QAAA,CAAAZ,KAAA,CAAgB;IACblC,EAAA,CAAAU,SAAA,GAIlB;IAJkBV,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA8B,eAAA,IAAAK,GAAA,EAAAW,QAAA,CAAAb,QAAA,aAAAa,QAAA,CAAAb,QAAA,eAAAa,QAAA,CAAAb,QAAA,YAIlB;IACAjC,EAAA,CAAAU,SAAA,GAEF;IAFEV,EAAA,CAAAoC,kBAAA,MAAAU,QAAA,CAAAb,QAAA,wBAAAa,QAAA,CAAAb,QAAA,yCAEF;IAEoBjC,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAA2B,iBAAA,CAAAmB,QAAA,CAAAT,WAAA,CAAsB;;;;;IAzBtDrC,EAAA,CAAAC,cAAA,cAA0G;IAI5FD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAI,MAAA,GAAgC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG5FH,EAAA,CAAAC,cAAA,cAAgJ;IAEjHD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEvDH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAsC,UAAA,KAAAS,4CAAA,kBAaM;IACR/C,EAAA,CAAAG,YAAA,EAAM;;;;;IA1BmBH,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAK,UAAA,mBAAA2C,KAAA,CAAoB;IAEvChD,EAAA,CAAAU,SAAA,GAAuC;IAAvCV,EAAA,CAAAiD,WAAA,iCAAAD,KAAA,CAAuC,kBAAAA,KAAA,sCAAAA,KAAA;IACrChD,EAAA,CAAAU,SAAA,GAAiB;IAAjBV,EAAA,CAAA2B,iBAAA,CAAAuB,UAAA,CAAAP,IAAA,CAAiB;IACwB3C,EAAA,CAAAU,SAAA,GAAgC;IAAhCV,EAAA,CAAAoC,kBAAA,KAAAc,UAAA,CAAAN,KAAA,CAAAC,MAAA,iBAAgC;IAGhF7C,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAK,UAAA,oBAAA2C,KAAA,CAAqB;IAAqChD,EAAA,CAAAiD,WAAA,gCAAAD,KAAA,CAAsC;IAEtEhD,EAAA,CAAAU,SAAA,GAAwB;IAAxBV,EAAA,CAAA2B,iBAAA,CAAAuB,UAAA,CAAAb,WAAA,CAAwB;IAG3BrC,EAAA,CAAAU,SAAA,GAAe;IAAfV,EAAA,CAAAK,UAAA,YAAA6C,UAAA,CAAAN,KAAA,CAAe;;;;;;IAnHjD5C,EAAA,CAAAC,cAAA,cAAuE;IAM7DD,EAAA,CAAAE,SAAA,YAAyC;IACzCF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAE,SAAA,YAAsC;IACtCF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAsC,UAAA,KAAAa,qCAAA,oBA0EM;IACRnD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAsC,UAAA,KAAAc,qCAAA,oBA8BM;IACRpD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IAMND,EAAA,CAAAE,SAAA,aAA4C;IAC5CF,EAAA,CAAAI,MAAA,wEACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAyB;IAC6BD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,WAAK;IACcD,EAAA,CAAAI,MAAA,qCAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,IAAuE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGhHH,EAAA,CAAAC,cAAA,eAAyB;IAC6BD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,WAAK;IACcD,EAAA,CAAAI,MAAA,+BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,IAAwG;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGjJH,EAAA,CAAAC,cAAA,eAAoB;IAC+BD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,WAAK;IACcD,EAAA,CAAAI,MAAA,uBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAI,MAAA,gFAAmE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI9GH,EAAA,CAAAC,cAAA,eAA+C;IACsCD,EAAA,CAAAqD,UAAA,mBAAAC,wDAAA;MAAAtD,EAAA,CAAAuD,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAA0D,aAAA;MAAA,OAAS1D,EAAA,CAAA2D,WAAA,CAAAF,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACxG5D,EAAA,CAAAE,SAAA,aAA2C;IAACF,EAAA,CAAAI,MAAA,oCAC9C;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,4CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAvKRH,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,uBAAAyB,MAAA,CAAAC,gBAAA,CAAAC,YAAA,QACF;IAGE/D,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAiB,kBAAA,MAAA4C,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAAAnB,MAAA,2CAAAgB,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,gBAAA,4BACF;IAIA9D,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,MAAAyB,MAAA,CAAAK,IAAA,IAAAL,MAAA,CAAAK,IAAA,CAAAC,OAAA,GAAAN,MAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAtB,MAAA,kBACF;IAMsB7C,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAK,UAAA,YAAAwD,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAA8B;IA+E9BhE,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAK,UAAA,YAAAwD,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAA8B;IA+CThE,EAAA,CAAAU,SAAA,IAAuE;IAAvEV,EAAA,CAAAoC,kBAAA,KAAAyB,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,gBAAA,+DAAuE;IAOvE9D,EAAA,CAAAU,SAAA,GAAwG;IAAxGV,EAAA,CAAAoC,kBAAA,+CAAAyB,MAAA,CAAAK,IAAA,IAAAL,MAAA,CAAAK,IAAA,CAAAC,OAAA,GAAAN,MAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAtB,MAAA,kCAAwG;;;;;IA2BzJ7C,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,MAAAgC,MAAA,CAAAC,KAAA,MACF;;;;;;IAIErE,EAAA,CAAAC,cAAA,eAA4C;IAIpCD,EAAA,CAAAE,SAAA,aAA6C;IAC7CF,EAAA,CAAAI,MAAA,oDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAkB;IAC8CD,EAAA,CAAAI,MAAA,sCAA+B;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACrGH,EAAA,CAAAC,cAAA,cAAyB;IAErBD,EAAA,CAAAE,SAAA,cAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,kBAE4D;IAArDD,EAAA,CAAAqD,UAAA,2BAAAiB,gEAAAC,MAAA;MAAAvE,EAAA,CAAAuD,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAAzE,EAAA,CAAA0D,aAAA;MAAA,OAAA1D,EAAA,CAAA2D,WAAA,CAAAc,OAAA,CAAAV,YAAA,GAAAQ,MAAA;IAAA,EAA0B;IAFjCvE,EAAA,CAAAG,YAAA,EAE4D;IAE9DH,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAGVH,EAAA,CAAAC,cAAA,gBAAoB;IAEVD,EAAA,CAAAqD,UAAA,mBAAAqB,yDAAA;MAAA1E,EAAA,CAAAuD,aAAA,CAAAiB,IAAA;MAAA,MAAAG,OAAA,GAAA3E,EAAA,CAAA0D,aAAA;MAAA,OAAS1D,EAAA,CAAA2D,WAAA,CAAAgB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/B5E,EAAA,CAAAE,SAAA,aAAoF;IACpFF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAdAH,EAAA,CAAAU,SAAA,IAA0B;IAA1BV,EAAA,CAAAK,UAAA,YAAAwE,MAAA,CAAAd,YAAA,CAA0B,aAAAc,MAAA,CAAAC,YAAA;IAIjC9E,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,8BAAAyC,MAAA,CAAAX,IAAA,IAAAW,MAAA,CAAAX,IAAA,CAAAC,OAAA,GAAAU,MAAA,CAAAX,IAAA,CAAAC,OAAA,CAAAtB,MAAA,2DACF;IAMQ7C,EAAA,CAAAU,SAAA,GAAiD;IAAjDV,EAAA,CAAAK,UAAA,aAAAwE,MAAA,CAAAC,YAAA,KAAAD,MAAA,CAAAd,YAAA,CAAAgB,IAAA,GAAiD;IACzC/E,EAAA,CAAAU,SAAA,GAAiE;IAAjEV,EAAA,CAAAK,UAAA,YAAAwE,MAAA,CAAAC,YAAA,0CAAiE;IAC/E9E,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAoC,kBAAA,MAAAyC,MAAA,CAAAC,YAAA,iFACF;;;ADlQhB,OAAM,MAAOE,eAAe;EAa1BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,mBAAwC;IAFxC,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAb7B,KAAArB,YAAY,GAAW,EAAE;IACzB,KAAAe,YAAY,GAAY,KAAK;IAC7B,KAAAhB,gBAAgB,GAAQ,IAAI;IAC5B,KAAAO,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAgB,QAAQ,GAAsD,EAAE;IAChE,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,gBAAgB,GAAY,KAAK;EAM9B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,WAAW;MACjBM,OAAO,EACL;KACH,CAAC;EACJ;EAEA6D,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACb,YAAY,CAACgB,IAAI,EAAE,EAAE;MAC7B,IAAI,CAACK,mBAAmB,CAACM,SAAS,CAAC,oCAAoC,CAAC;MACxE;;IAGF;IACA,IAAIC,WAAW,GACb,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO,CAACtB,MAAM,GAAG,CAAC;IAE/D;IACA,MAAM+C,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;IAErD,IAAIA,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAACP,mBAAmB,CAACW,WAAW,CAClC,mEAAmE,CACpE;;IAGH,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACT,KAAK,GAAG,IAAI;IAEjB2B,OAAO,CAACC,GAAG,CACT,6BAA6BL,oBAAoB,uBAAuBD,WAAW,WAAW,CAC/F;IAED;IACA,IAAI,CAACN,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,MAAM;MACZM,OAAO,EAAE,qCAAqC,IAAI,CAACgD,YAAY,qBAAqB6B,oBAAoB;KACzG,CAAC;IAEF;IACA,MAAMM,mBAAmB,GAAG,IAAI,CAACb,QAAQ,CAACxC,MAAM;IAChD,IAAI,CAACwC,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,WAAW;MACjBM,OAAO,EAAE;KACV,CAAC;IAEF;IACA,IAAIoF,WAAW,GAAU,EAAE;IAC3B,IAAI,IAAI,CAACjC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MAClC;MACAgC,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACC,OAAO,CAACiC,GAAG,CAAC,CAACC,QAAgB,EAAEC,KAAa,KAAI;QACtE,OAAO;UAAEC,EAAE,EAAEF,QAAQ;UAAE1D,IAAI,EAAE,UAAU2D,KAAK,GAAG,CAAC,EAAE;UAAE7F,IAAI,EAAE;QAAQ,CAAE;MACtE,CAAC,CAAC;MAEFuF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEE,WAAW,CAAC;;IAG1E,IAAI,CAACjB,SAAS,CACXsB,oBAAoB,CAAC,IAAI,CAACzC,YAAY,EAAE4B,WAAW,EAAEQ,WAAW,CAAC,CACjEM,IAAI,CAAC1G,QAAQ,CAAC,MAAO,IAAI,CAAC+E,YAAY,GAAG,KAAM,CAAC,CAAC,CACjD4B,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAAC5C,QAAQ,IAAI4C,MAAM,CAAC5C,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;UAC/DmD,OAAO,CAAC3B,KAAK,CAAC,kCAAkC,EAAEuC,MAAM,CAAC;UACzD,IAAI,CAACC,qBAAqB,CACxBX,mBAAmB,EACnB,4BAA4B,CAC7B;UACD;;QAGF,IAAI,CAACpC,gBAAgB,GAAG8C,MAAM;QAE9B;QACA,IAAI,CAACvB,QAAQ,CAACa,mBAAmB,CAAC,GAAG;UACnCzF,IAAI,EAAE,WAAW;UACjBM,OAAO,EAAE,eACP6F,MAAM,CAAC5C,QAAQ,CAACnB,MAClB,+BACE+D,MAAM,CAAC7C,YACT,sBAAsB,IAAI,CAACE,UAAU,CAAC2C,MAAM,CAAC;SAC9C;QAED,IAAI,CAACxB,mBAAmB,CAAC0B,WAAW,CAAC,6BAA6B,CAAC;MACrE,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACwC,qBAAqB,CACxBX,mBAAmB,EACnB7B,KAAK,CAAC0C,OAAO,IAAI,iBAAiB,CACnC;MACH;KACD,CAAC;EACN;EAEA;EACQF,qBAAqBA,CAC3BG,YAAoB,EACpBC,YAAoB;IAEpB,IAAI,CAAC5C,KAAK,GAAG,uDAAuD;IAEpE;IACA,IAAI,CAACgB,QAAQ,CAAC2B,YAAY,CAAC,GAAG;MAC5BvG,IAAI,EAAE,WAAW;MACjBM,OAAO,EACL;KACH;IAED,IAAI,CAACqE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,GAAGuB,YAAY,CAC3D;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5B,YAAY,CAACP,IAAI,EAAE,EAAE;MAC7B;;IAGF,MAAMoC,QAAQ,GAAG,IAAI,CAAC7B,YAAY,CAACP,IAAI,EAAE;IACzC,IAAI,CAACO,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACF,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,MAAM;MACZM,OAAO,EAAEoG;KACV,CAAC;IAEF,MAAMC,cAAc,GAAG;MACrBlF,KAAK,EACH,IAAI,CAAC6B,YAAY,KAChB,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACC,YAAY,GAAG,EAAE,CAAC;MACnE1B,WAAW,EACT,2BAA2B,IAAI,IAAI,CAAC6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvB,IAAI,GAAG,EAAE;KACjE;IAED,IAAI,CAACuC,SAAS,CACXmC,kBAAkB,CAACF,QAAQ,EAAEC,cAAc,CAAC,CAC5CX,IAAI,CAAC1G,QAAQ,CAAC,MAAO,IAAI,CAACwF,gBAAgB,GAAG,KAAM,CAAC,CAAC,CACrDmB,SAAS,CAAC;MACTC,IAAI,EAAGW,QAAgB,IAAI;QACzB;QACA,IAAI,CAACjC,QAAQ,CAACI,IAAI,CAAC;UACjBhF,IAAI,EAAE,WAAW;UACjBM,OAAO,EAAEuG;SACV,CAAC;MACJ,CAAC;MACDjD,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAEzD;QACA,IAAI,CAACgB,QAAQ,CAACI,IAAI,CAAC;UACjBhF,IAAI,EAAE,WAAW;UACjBM,OAAO,EACL;SACH,CAAC;QAEF,IAAI,CAACqE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEA9B,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACI,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACqD,GAAG,EAAE;MAC1D,IAAI,CAACnC,mBAAmB,CAACM,SAAS,CAChC,yCAAyC,CAC1C;MACD;;IAGF,IAAI8B,YAAY,GAAG,CAAC;IACpB,MAAMC,UAAU,GAAG,IAAI,CAACxD,UAAU,CAAC,IAAI,CAACH,gBAAgB,CAAC;IAEzD;IACA,IAAI,CAAC,IAAI,CAACI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACD,IAAI,CAACC,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAACuC,mBAAmB,CAACM,SAAS,CAChC,sDAAsD,CACvD;MACD;;IAGF;IACA,MAAMS,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACC,OAAO,CAACiC,GAAG,CAAEsB,MAAM,IAAI;MACnD,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAIA,MAAc,CAACC,MAAM;IACrE,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAA8B,EAAE;IACvDzB,WAAW,CAAC0B,OAAO,CAAC,CAACxB,QAAQ,EAAEC,KAAK,KAAI;MACtCsB,iBAAiB,CAAC,UAAUtB,KAAK,GAAG,CAAC,EAAE,CAAC,GAAGD,QAAQ;IACrD,CAAC,CAAC;IAEFL,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDE,WAAW,CACZ;IACDH,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7C2B,iBAAiB,CAClB;IAED;IACA,IAAI,CAAC9D,gBAAgB,CAACE,QAAQ,CAAC6D,OAAO,CAAEC,MAAW,IAAI;MACrD;MACA,IAAIC,gBAAoC;MAExC;MACA,IAAID,MAAM,CAACjG,UAAU,EAAE;QACrB;QACA,MAAMmG,UAAU,GAAGF,MAAM,CAACjG,UAAU;QACpC,IAAI+F,iBAAiB,CAACI,UAAU,CAAC,EAAE;UACjCD,gBAAgB,GAAGH,iBAAiB,CAACI,UAAU,CAAC;UAChDhC,OAAO,CAACC,GAAG,CACT,0CAA0C6B,MAAM,CAACnF,IAAI,iBAAiBqF,UAAU,UAAUD,gBAAgB,GAAG,CAC9G;SACF,MAAM;UACL;UACA,MAAME,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACtD,MAAM,CACnC;UACDkF,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;UACjDjC,OAAO,CAACC,GAAG,CACT,kBAAkB+B,UAAU,iDAAiDC,iBAAiB,EAAE,CACjG;;OAEJ,MAAM;QACL;QACA,MAAMA,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACtD,MAAM,CACnC;QACDkF,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;QACjDjC,OAAO,CAACC,GAAG,CACT,+DAA+DgC,iBAAiB,EAAE,CACnF;;MAGH;MACAH,MAAM,CAAClF,KAAK,CAACiF,OAAO,CAAEO,QAAa,IAAI;QACrC,MAAMC,IAAI,GAAS;UACjBnG,KAAK,EAAEkG,QAAQ,CAAClG,KAAK;UACrBG,WAAW,EAAE,IAAIyF,MAAM,CAACnF,IAAI,KAAKyF,QAAQ,CAAC/F,WAAW,EAAE;UACvDiG,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAI,MAAM;UACjCrG,QAAQ,EAAEmG,QAAQ,CAACnG,QAAQ,IAAI,QAAQ;UACvCsG,MAAM,EAAE,IAAI,CAACrE,IAAI,CAACqD,GAAG,IAAI,EAAE;UAC3B;UACA1F,UAAU,EAAEkG;SACb;QAED,IAAI,CAAC5C,WAAW,CAACqD,UAAU,CAACH,IAAI,CAAC,CAAC3B,SAAS,CAAC;UAC1CC,IAAI,EAAEA,CAAA,KAAK;YACTa,YAAY,EAAE;YACd,IAAIA,YAAY,KAAKC,UAAU,EAAE;cAC/B,IAAI,CAACrC,mBAAmB,CAAC0B,WAAW,CAClC,GAAGU,YAAY,iEAAiE,CACjF;cACD;cACA,IAAI,CAAC1D,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;;UAE1B,CAAC;UACDM,KAAK,EAAGA,KAAK,IAAI;YACf2B,OAAO,CAAC3B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;YAC/D,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,uCAAuC,CACxC;UACH;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAzB,UAAUA,CAAClD,OAAY;IACrB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACiD,QAAQ,EAAE,OAAO,CAAC;IAE3C,OAAOjD,OAAO,CAACiD,QAAQ,CAACyE,MAAM,CAAC,CAACC,KAAa,EAAEZ,MAAW,KAAI;MAC5D,OAAOY,KAAK,IAAIZ,MAAM,CAAClF,KAAK,GAAGkF,MAAM,CAAClF,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACApB,mBAAmBA,CAAC6E,KAAa;IAC/B;IACA,MAAMqC,SAAS,GAAG,CAChB,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,mDAAmD,CAAE;IAAA,CACtD;IAED;IACA,OAAOA,SAAS,CAACrC,KAAK,GAAGqC,SAAS,CAAC9F,MAAM,CAAC;EAC5C;EAEA;EACAvB,gBAAgBA,CAACgF,KAAa;IAC5B;IACA,MAAMsC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;IAED;IACA,OAAOA,MAAM,CAACtC,KAAK,GAAGsC,MAAM,CAAC/F,MAAM,CAAC;EACtC;EAEA;EACAH,gBAAgBA,CAACmG,UAAkB;IACjC;IACA,MAAMlG,IAAI,GAAGkG,UAAU,CAACC,WAAW,EAAE;IAErC;IACA,IACEnG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,IACpBpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,EACrB;MACA,OAAO,kBAAkB;KAC1B,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,WAAW,CAAC,IAC1BpG,IAAI,CAACoG,QAAQ,CAAC,IAAI,CAAC,IACnBpG,IAAI,CAACoG,QAAQ,CAAC,OAAO,CAAC,IACtBpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,EAC5B;MACA,OAAO,WAAW;KACnB,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,OAAO,CAAC,EACtB;MACA,OAAO,wBAAwB;KAChC,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,IAAI,CAAC,EACnB;MACA,OAAO,aAAa;KACrB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,UAAU,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7D,OAAO,qBAAqB;KAC7B,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,UAAU,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpE,OAAO,qBAAqB;KAC7B,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,QAAQ,CAAC,EACvB;MACA,OAAO,gBAAgB;KACxB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,eAAe,CAAC,EAAE;MACjE,OAAO,mBAAmB;KAC3B,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,QAAQ,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1D,OAAO,eAAe;KACvB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/D,OAAO,WAAW;KACnB,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,WAAW,CAAC,IAC1BpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,EACpB;MACA,OAAO,aAAa;;IAGtB;IACA,OAAO,gBAAgB;EACzB;EAEA;EACA5H,cAAcA,CAAA;IACZ,MAAM6H,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,KAAK,GAAGF,GAAG,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,MAAMC,OAAO,GAAGN,GAAG,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAC9B;;;uBAvZWtE,eAAe,EAAAhF,EAAA,CAAAwJ,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA1J,EAAA,CAAAwJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA5J,EAAA,CAAAwJ,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAf9E,eAAe;MAAA+E,SAAA;MAAAC,MAAA;QAAA9F,IAAA;MAAA;MAAA+F,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BtK,EAAA,CAAAC,cAAA,aAAqC;UAM7BD,EAAA,CAAAsC,UAAA,IAAAkI,8BAAA,kBAsBM;UAGNxK,EAAA,CAAAsC,UAAA,IAAAmI,8BAAA,iBAgBM;UACRzK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsC,UAAA,IAAAoI,8BAAA,kBAoLM;UAGN1K,EAAA,CAAAsC,UAAA,IAAAqI,8BAAA,iBAEM;UAGN3K,EAAA,CAAAC,cAAA,aAAuC;UACrCD,EAAA,CAAAsC,UAAA,KAAAsI,+BAAA,mBAkCM;UAEN5K,EAAA,CAAAC,cAAA,eAAwD;UAIhDD,EAAA,CAAAE,SAAA,aAA4C;UAC9CF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,iBAGqC;UAD9BD,EAAA,CAAAqD,UAAA,2BAAAwH,yDAAAtG,MAAA;YAAA,OAAAgG,GAAA,CAAAjF,YAAA,GAAAf,MAAA;UAAA,EAA0B,yBAAAuG,uDAAA;YAAA,OAAgBP,GAAA,CAAArD,WAAA,EAAa;UAAA,EAA7B;UAFjClH,EAAA,CAAAG,YAAA,EAGqC;UACrCH,EAAA,CAAAC,cAAA,kBACsF;UAA9ED,EAAA,CAAAqD,UAAA,mBAAA0H,kDAAA;YAAA,OAASR,GAAA,CAAArD,WAAA,EAAa;UAAA,EAAC;UAC7BlH,EAAA,CAAAE,SAAA,aAA4F;UAC9FF,EAAA,CAAAG,YAAA,EAAS;;;UA3RUH,EAAA,CAAAU,SAAA,GAAa;UAAbV,EAAA,CAAAK,UAAA,YAAAkK,GAAA,CAAAlF,QAAA,CAAa;UAyBhCrF,EAAA,CAAAU,SAAA,GAAsC;UAAtCV,EAAA,CAAAK,UAAA,SAAAkK,GAAA,CAAAzF,YAAA,IAAAyF,GAAA,CAAAhF,gBAAA,CAAsC;UAoBxCvF,EAAA,CAAAU,SAAA,GAAsB;UAAtBV,EAAA,CAAAK,UAAA,SAAAkK,GAAA,CAAAzG,gBAAA,CAAsB;UAuLtB9D,EAAA,CAAAU,SAAA,GAAW;UAAXV,EAAA,CAAAK,UAAA,SAAAkK,GAAA,CAAAlG,KAAA,CAAW;UAMTrE,EAAA,CAAAU,SAAA,GAAuB;UAAvBV,EAAA,CAAAK,UAAA,UAAAkK,GAAA,CAAAzG,gBAAA,CAAuB;UA4ChB9D,EAAA,CAAAU,SAAA,GAA0B;UAA1BV,EAAA,CAAAK,UAAA,YAAAkK,GAAA,CAAAjF,YAAA,CAA0B,aAAAiF,GAAA,CAAAhF,gBAAA;UAGDvF,EAAA,CAAAU,SAAA,GAAqD;UAArDV,EAAA,CAAAK,UAAA,aAAAkK,GAAA,CAAAhF,gBAAA,KAAAgF,GAAA,CAAAjF,YAAA,CAAAP,IAAA,GAAqD;UACrE/E,EAAA,CAAAU,SAAA,GAAyE;UAAzEV,EAAA,CAAAK,UAAA,YAAAkK,GAAA,CAAAhF,gBAAA,8CAAyE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}