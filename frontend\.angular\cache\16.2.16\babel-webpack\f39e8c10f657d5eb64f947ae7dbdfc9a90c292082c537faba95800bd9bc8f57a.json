{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class NotificationService {\n  constructor() {\n    this.notificationSubject = new BehaviorSubject(null);\n  }\n  getNotifications() {\n    return this.notificationSubject.asObservable();\n  }\n  showSuccess(message, timeout = 5000) {\n    this.show({\n      message,\n      type: 'success',\n      timeout\n    });\n  }\n  showError(message, timeout = 5000) {\n    this.show({\n      message,\n      type: 'error',\n      timeout\n    });\n  }\n  showInfo(message, timeout = 5000) {\n    this.show({\n      message,\n      type: 'info',\n      timeout\n    });\n  }\n  showWarning(message, timeout = 5000) {\n    this.show({\n      message,\n      type: 'warning',\n      timeout\n    });\n  }\n  show(notification) {\n    this.notificationSubject.next(notification);\n    if (notification.timeout) {\n      setTimeout(() => {\n        // Effacer la notification seulement si c'est toujours la même\n        if (this.notificationSubject.value === notification) {\n          this.notificationSubject.next(null);\n        }\n      }, notification.timeout);\n    }\n  }\n  clear() {\n    this.notificationSubject.next(null);\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "NotificationService", "constructor", "notificationSubject", "getNotifications", "asObservable", "showSuccess", "message", "timeout", "show", "type", "showError", "showInfo", "showWarning", "notification", "next", "setTimeout", "value", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\nexport interface Notification {\r\n  message: string;\r\n  type: 'success' | 'error' | 'info' | 'warning';\r\n  timeout?: number;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  private notificationSubject = new BehaviorSubject<Notification | null>(null);\r\n\r\n  constructor() {}\r\n\r\n  getNotifications(): Observable<Notification | null> {\r\n    return this.notificationSubject.asObservable();\r\n  }\r\n\r\n  showSuccess(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'success', timeout });\r\n  }\r\n\r\n  showError(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'error', timeout });\r\n  }\r\n\r\n  showInfo(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'info', timeout });\r\n  }\r\n\r\n  showWarning(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'warning', timeout });\r\n  }\r\n\r\n  private show(notification: Notification): void {\r\n    this.notificationSubject.next(notification);\r\n\r\n    if (notification.timeout) {\r\n      setTimeout(() => {\r\n        // Effacer la notification seulement si c'est toujours la même\r\n        if (this.notificationSubject.value === notification) {\r\n          this.notificationSubject.next(null);\r\n        }\r\n      }, notification.timeout);\r\n    }\r\n  }\r\n\r\n  clear(): void {\r\n    this.notificationSubject.next(null);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAWlD,OAAM,MAAOC,mBAAmB;EAG9BC,YAAA;IAFQ,KAAAC,mBAAmB,GAAG,IAAIH,eAAe,CAAsB,IAAI,CAAC;EAE7D;EAEfI,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;EAChD;EAEAC,WAAWA,CAACC,OAAe,EAAEC,OAAA,GAAkB,IAAI;IACjD,IAAI,CAACC,IAAI,CAAC;MAAEF,OAAO;MAAEG,IAAI,EAAE,SAAS;MAAEF;IAAO,CAAE,CAAC;EAClD;EAEAG,SAASA,CAACJ,OAAe,EAAEC,OAAA,GAAkB,IAAI;IAC/C,IAAI,CAACC,IAAI,CAAC;MAAEF,OAAO;MAAEG,IAAI,EAAE,OAAO;MAAEF;IAAO,CAAE,CAAC;EAChD;EAEAI,QAAQA,CAACL,OAAe,EAAEC,OAAA,GAAkB,IAAI;IAC9C,IAAI,CAACC,IAAI,CAAC;MAAEF,OAAO;MAAEG,IAAI,EAAE,MAAM;MAAEF;IAAO,CAAE,CAAC;EAC/C;EAEAK,WAAWA,CAACN,OAAe,EAAEC,OAAA,GAAkB,IAAI;IACjD,IAAI,CAACC,IAAI,CAAC;MAAEF,OAAO;MAAEG,IAAI,EAAE,SAAS;MAAEF;IAAO,CAAE,CAAC;EAClD;EAEQC,IAAIA,CAACK,YAA0B;IACrC,IAAI,CAACX,mBAAmB,CAACY,IAAI,CAACD,YAAY,CAAC;IAE3C,IAAIA,YAAY,CAACN,OAAO,EAAE;MACxBQ,UAAU,CAAC,MAAK;QACd;QACA,IAAI,IAAI,CAACb,mBAAmB,CAACc,KAAK,KAAKH,YAAY,EAAE;UACnD,IAAI,CAACX,mBAAmB,CAACY,IAAI,CAAC,IAAI,CAAC;;MAEvC,CAAC,EAAED,YAAY,CAACN,OAAO,CAAC;;EAE5B;EAEAU,KAAKA,CAAA;IACH,IAAI,CAACf,mBAAmB,CAACY,IAAI,CAAC,IAAI,CAAC;EACrC;;;uBAxCWd,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAkB,OAAA,EAAnBlB,mBAAmB,CAAAmB,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}