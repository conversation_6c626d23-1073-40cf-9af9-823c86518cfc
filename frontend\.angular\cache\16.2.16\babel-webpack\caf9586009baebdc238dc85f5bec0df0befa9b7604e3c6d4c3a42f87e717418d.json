{"ast": null, "code": "export function devAssert(condition, message) {\n  const booleanCondition = Boolean(condition);\n  if (!booleanCondition) {\n    throw new Error(message);\n  }\n}", "map": {"version": 3, "names": ["devAssert", "condition", "message", "booleanCondition", "Boolean", "Error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/jsutils/devAssert.mjs"], "sourcesContent": ["export function devAssert(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(message);\n  }\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC5C,MAAMC,gBAAgB,GAAGC,OAAO,CAACH,SAAS,CAAC;EAE3C,IAAI,CAACE,gBAAgB,EAAE;IACrB,MAAM,IAAIE,KAAK,CAACH,OAAO,CAAC;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}