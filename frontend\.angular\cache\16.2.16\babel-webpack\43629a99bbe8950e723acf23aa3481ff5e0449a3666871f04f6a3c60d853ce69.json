{"ast": null, "code": "import { newInvariantError } from \"../globals/index.js\";\nimport { isNonNullObject } from \"../common/objects.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\nexport function makeReference(id) {\n  return {\n    __ref: String(id)\n  };\n}\nexport function isReference(obj) {\n  return Boolean(obj && typeof obj === \"object\" && typeof obj.__ref === \"string\");\n}\nexport function isDocumentNode(value) {\n  return isNonNullObject(value) && value.kind === \"Document\" && Array.isArray(value.definitions);\n}\nfunction isStringValue(value) {\n  return value.kind === \"StringValue\";\n}\nfunction isBooleanValue(value) {\n  return value.kind === \"BooleanValue\";\n}\nfunction isIntValue(value) {\n  return value.kind === \"IntValue\";\n}\nfunction isFloatValue(value) {\n  return value.kind === \"FloatValue\";\n}\nfunction isVariable(value) {\n  return value.kind === \"Variable\";\n}\nfunction isObjectValue(value) {\n  return value.kind === \"ObjectValue\";\n}\nfunction isListValue(value) {\n  return value.kind === \"ListValue\";\n}\nfunction isEnumValue(value) {\n  return value.kind === \"EnumValue\";\n}\nfunction isNullValue(value) {\n  return value.kind === \"NullValue\";\n}\nexport function valueToObjectRepresentation(argObj, name, value, variables) {\n  if (isIntValue(value) || isFloatValue(value)) {\n    argObj[name.value] = Number(value.value);\n  } else if (isBooleanValue(value) || isStringValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isObjectValue(value)) {\n    var nestedArgObj_1 = {};\n    value.fields.map(function (obj) {\n      return valueToObjectRepresentation(nestedArgObj_1, obj.name, obj.value, variables);\n    });\n    argObj[name.value] = nestedArgObj_1;\n  } else if (isVariable(value)) {\n    var variableValue = (variables || {})[value.name.value];\n    argObj[name.value] = variableValue;\n  } else if (isListValue(value)) {\n    argObj[name.value] = value.values.map(function (listValue) {\n      var nestedArgArrayObj = {};\n      valueToObjectRepresentation(nestedArgArrayObj, name, listValue, variables);\n      return nestedArgArrayObj[name.value];\n    });\n  } else if (isEnumValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isNullValue(value)) {\n    argObj[name.value] = null;\n  } else {\n    throw newInvariantError(96, name.value, value.kind);\n  }\n}\nexport function storeKeyNameFromField(field, variables) {\n  var directivesObj = null;\n  if (field.directives) {\n    directivesObj = {};\n    field.directives.forEach(function (directive) {\n      directivesObj[directive.name.value] = {};\n      if (directive.arguments) {\n        directive.arguments.forEach(function (_a) {\n          var name = _a.name,\n            value = _a.value;\n          return valueToObjectRepresentation(directivesObj[directive.name.value], name, value, variables);\n        });\n      }\n    });\n  }\n  var argObj = null;\n  if (field.arguments && field.arguments.length) {\n    argObj = {};\n    field.arguments.forEach(function (_a) {\n      var name = _a.name,\n        value = _a.value;\n      return valueToObjectRepresentation(argObj, name, value, variables);\n    });\n  }\n  return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\nvar KNOWN_DIRECTIVES = [\"connection\", \"include\", \"skip\", \"client\", \"rest\", \"export\", \"nonreactive\"];\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nvar storeKeyNameStringify = canonicalStringify;\nexport var getStoreKeyName = Object.assign(function (fieldName, args, directives) {\n  if (args && directives && directives[\"connection\"] && directives[\"connection\"][\"key\"]) {\n    if (directives[\"connection\"][\"filter\"] && directives[\"connection\"][\"filter\"].length > 0) {\n      var filterKeys = directives[\"connection\"][\"filter\"] ? directives[\"connection\"][\"filter\"] : [];\n      filterKeys.sort();\n      var filteredArgs_1 = {};\n      filterKeys.forEach(function (key) {\n        filteredArgs_1[key] = args[key];\n      });\n      return \"\".concat(directives[\"connection\"][\"key\"], \"(\").concat(storeKeyNameStringify(filteredArgs_1), \")\");\n    } else {\n      return directives[\"connection\"][\"key\"];\n    }\n  }\n  var completeFieldName = fieldName;\n  if (args) {\n    // We can't use `JSON.stringify` here since it's non-deterministic,\n    // and can lead to different store key names being created even though\n    // the `args` object used during creation has the same properties/values.\n    var stringifiedArgs = storeKeyNameStringify(args);\n    completeFieldName += \"(\".concat(stringifiedArgs, \")\");\n  }\n  if (directives) {\n    Object.keys(directives).forEach(function (key) {\n      if (KNOWN_DIRECTIVES.indexOf(key) !== -1) return;\n      if (directives[key] && Object.keys(directives[key]).length) {\n        completeFieldName += \"@\".concat(key, \"(\").concat(storeKeyNameStringify(directives[key]), \")\");\n      } else {\n        completeFieldName += \"@\".concat(key);\n      }\n    });\n  }\n  return completeFieldName;\n}, {\n  setStringify: function (s) {\n    var previous = storeKeyNameStringify;\n    storeKeyNameStringify = s;\n    return previous;\n  }\n});\nexport function argumentsObjectFromField(field, variables) {\n  if (field.arguments && field.arguments.length) {\n    var argObj_1 = {};\n    field.arguments.forEach(function (_a) {\n      var name = _a.name,\n        value = _a.value;\n      return valueToObjectRepresentation(argObj_1, name, value, variables);\n    });\n    return argObj_1;\n  }\n  return null;\n}\nexport function resultKeyNameFromField(field) {\n  return field.alias ? field.alias.value : field.name.value;\n}\nexport function getTypenameFromResult(result, selectionSet, fragmentMap) {\n  var fragments;\n  for (var _i = 0, _a = selectionSet.selections; _i < _a.length; _i++) {\n    var selection = _a[_i];\n    if (isField(selection)) {\n      if (selection.name.value === \"__typename\") {\n        return result[resultKeyNameFromField(selection)];\n      }\n    } else if (fragments) {\n      fragments.push(selection);\n    } else {\n      fragments = [selection];\n    }\n  }\n  if (typeof result.__typename === \"string\") {\n    return result.__typename;\n  }\n  if (fragments) {\n    for (var _b = 0, fragments_1 = fragments; _b < fragments_1.length; _b++) {\n      var selection = fragments_1[_b];\n      var typename = getTypenameFromResult(result, getFragmentFromSelection(selection, fragmentMap).selectionSet, fragmentMap);\n      if (typeof typename === \"string\") {\n        return typename;\n      }\n    }\n  }\n}\nexport function isField(selection) {\n  return selection.kind === \"Field\";\n}\nexport function isInlineFragment(selection) {\n  return selection.kind === \"InlineFragment\";\n}", "map": {"version": 3, "names": ["newInvariantError", "isNonNullObject", "getFragmentFromSelection", "canonicalStringify", "makeReference", "id", "__ref", "String", "isReference", "obj", "Boolean", "isDocumentNode", "value", "kind", "Array", "isArray", "definitions", "isStringValue", "isBooleanValue", "isIntValue", "isFloatValue", "isVariable", "isObjectValue", "isListValue", "isEnumValue", "isNullValue", "valueToObjectRepresentation", "arg<PERSON><PERSON><PERSON>", "name", "variables", "Number", "nestedArgObj_1", "fields", "map", "variableValue", "values", "listValue", "nestedArgArrayObj", "storeKeyNameFromField", "field", "directivesObj", "directives", "for<PERSON>ach", "directive", "arguments", "_a", "length", "getStoreKeyName", "KNOWN_DIRECTIVES", "storeKeyNameStringify", "Object", "assign", "fieldName", "args", "filterKeys", "sort", "filteredArgs_1", "key", "concat", "completeFieldName", "stringifiedArgs", "keys", "indexOf", "setStringify", "s", "previous", "argumentsObjectFromField", "argObj_1", "resultKeyNameFromField", "alias", "getTypenameFromResult", "result", "selectionSet", "fragmentMap", "fragments", "_i", "selections", "selection", "isField", "push", "__typename", "_b", "fragments_1", "typename", "isInlineFragment"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/storeUtils.js"], "sourcesContent": ["import { newInvariantError } from \"../globals/index.js\";\nimport { isNonNullObject } from \"../common/objects.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\nexport function makeReference(id) {\n    return { __ref: String(id) };\n}\nexport function isReference(obj) {\n    return Boolean(obj && typeof obj === \"object\" && typeof obj.__ref === \"string\");\n}\nexport function isDocumentNode(value) {\n    return (isNonNullObject(value) &&\n        value.kind === \"Document\" &&\n        Array.isArray(value.definitions));\n}\nfunction isStringValue(value) {\n    return value.kind === \"StringValue\";\n}\nfunction isBooleanValue(value) {\n    return value.kind === \"BooleanValue\";\n}\nfunction isIntValue(value) {\n    return value.kind === \"IntValue\";\n}\nfunction isFloatValue(value) {\n    return value.kind === \"FloatValue\";\n}\nfunction isVariable(value) {\n    return value.kind === \"Variable\";\n}\nfunction isObjectValue(value) {\n    return value.kind === \"ObjectValue\";\n}\nfunction isListValue(value) {\n    return value.kind === \"ListValue\";\n}\nfunction isEnumValue(value) {\n    return value.kind === \"EnumValue\";\n}\nfunction isNullValue(value) {\n    return value.kind === \"NullValue\";\n}\nexport function valueToObjectRepresentation(argObj, name, value, variables) {\n    if (isIntValue(value) || isFloatValue(value)) {\n        argObj[name.value] = Number(value.value);\n    }\n    else if (isBooleanValue(value) || isStringValue(value)) {\n        argObj[name.value] = value.value;\n    }\n    else if (isObjectValue(value)) {\n        var nestedArgObj_1 = {};\n        value.fields.map(function (obj) {\n            return valueToObjectRepresentation(nestedArgObj_1, obj.name, obj.value, variables);\n        });\n        argObj[name.value] = nestedArgObj_1;\n    }\n    else if (isVariable(value)) {\n        var variableValue = (variables || {})[value.name.value];\n        argObj[name.value] = variableValue;\n    }\n    else if (isListValue(value)) {\n        argObj[name.value] = value.values.map(function (listValue) {\n            var nestedArgArrayObj = {};\n            valueToObjectRepresentation(nestedArgArrayObj, name, listValue, variables);\n            return nestedArgArrayObj[name.value];\n        });\n    }\n    else if (isEnumValue(value)) {\n        argObj[name.value] = value.value;\n    }\n    else if (isNullValue(value)) {\n        argObj[name.value] = null;\n    }\n    else {\n        throw newInvariantError(96, name.value, value.kind);\n    }\n}\nexport function storeKeyNameFromField(field, variables) {\n    var directivesObj = null;\n    if (field.directives) {\n        directivesObj = {};\n        field.directives.forEach(function (directive) {\n            directivesObj[directive.name.value] = {};\n            if (directive.arguments) {\n                directive.arguments.forEach(function (_a) {\n                    var name = _a.name, value = _a.value;\n                    return valueToObjectRepresentation(directivesObj[directive.name.value], name, value, variables);\n                });\n            }\n        });\n    }\n    var argObj = null;\n    if (field.arguments && field.arguments.length) {\n        argObj = {};\n        field.arguments.forEach(function (_a) {\n            var name = _a.name, value = _a.value;\n            return valueToObjectRepresentation(argObj, name, value, variables);\n        });\n    }\n    return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\nvar KNOWN_DIRECTIVES = [\n    \"connection\",\n    \"include\",\n    \"skip\",\n    \"client\",\n    \"rest\",\n    \"export\",\n    \"nonreactive\",\n];\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nvar storeKeyNameStringify = canonicalStringify;\nexport var getStoreKeyName = Object.assign(function (fieldName, args, directives) {\n    if (args &&\n        directives &&\n        directives[\"connection\"] &&\n        directives[\"connection\"][\"key\"]) {\n        if (directives[\"connection\"][\"filter\"] &&\n            directives[\"connection\"][\"filter\"].length > 0) {\n            var filterKeys = directives[\"connection\"][\"filter\"] ?\n                directives[\"connection\"][\"filter\"]\n                : [];\n            filterKeys.sort();\n            var filteredArgs_1 = {};\n            filterKeys.forEach(function (key) {\n                filteredArgs_1[key] = args[key];\n            });\n            return \"\".concat(directives[\"connection\"][\"key\"], \"(\").concat(storeKeyNameStringify(filteredArgs_1), \")\");\n        }\n        else {\n            return directives[\"connection\"][\"key\"];\n        }\n    }\n    var completeFieldName = fieldName;\n    if (args) {\n        // We can't use `JSON.stringify` here since it's non-deterministic,\n        // and can lead to different store key names being created even though\n        // the `args` object used during creation has the same properties/values.\n        var stringifiedArgs = storeKeyNameStringify(args);\n        completeFieldName += \"(\".concat(stringifiedArgs, \")\");\n    }\n    if (directives) {\n        Object.keys(directives).forEach(function (key) {\n            if (KNOWN_DIRECTIVES.indexOf(key) !== -1)\n                return;\n            if (directives[key] && Object.keys(directives[key]).length) {\n                completeFieldName += \"@\".concat(key, \"(\").concat(storeKeyNameStringify(directives[key]), \")\");\n            }\n            else {\n                completeFieldName += \"@\".concat(key);\n            }\n        });\n    }\n    return completeFieldName;\n}, {\n    setStringify: function (s) {\n        var previous = storeKeyNameStringify;\n        storeKeyNameStringify = s;\n        return previous;\n    },\n});\nexport function argumentsObjectFromField(field, variables) {\n    if (field.arguments && field.arguments.length) {\n        var argObj_1 = {};\n        field.arguments.forEach(function (_a) {\n            var name = _a.name, value = _a.value;\n            return valueToObjectRepresentation(argObj_1, name, value, variables);\n        });\n        return argObj_1;\n    }\n    return null;\n}\nexport function resultKeyNameFromField(field) {\n    return field.alias ? field.alias.value : field.name.value;\n}\nexport function getTypenameFromResult(result, selectionSet, fragmentMap) {\n    var fragments;\n    for (var _i = 0, _a = selectionSet.selections; _i < _a.length; _i++) {\n        var selection = _a[_i];\n        if (isField(selection)) {\n            if (selection.name.value === \"__typename\") {\n                return result[resultKeyNameFromField(selection)];\n            }\n        }\n        else if (fragments) {\n            fragments.push(selection);\n        }\n        else {\n            fragments = [selection];\n        }\n    }\n    if (typeof result.__typename === \"string\") {\n        return result.__typename;\n    }\n    if (fragments) {\n        for (var _b = 0, fragments_1 = fragments; _b < fragments_1.length; _b++) {\n            var selection = fragments_1[_b];\n            var typename = getTypenameFromResult(result, getFragmentFromSelection(selection, fragmentMap).selectionSet, fragmentMap);\n            if (typeof typename === \"string\") {\n                return typename;\n            }\n        }\n    }\n}\nexport function isField(selection) {\n    return selection.kind === \"Field\";\n}\nexport function isInlineFragment(selection) {\n    return selection.kind === \"InlineFragment\";\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,wBAAwB,QAAQ,gBAAgB;AACzD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAE;EAC9B,OAAO;IAAEC,KAAK,EAAEC,MAAM,CAACF,EAAE;EAAE,CAAC;AAChC;AACA,OAAO,SAASG,WAAWA,CAACC,GAAG,EAAE;EAC7B,OAAOC,OAAO,CAACD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,CAACH,KAAK,KAAK,QAAQ,CAAC;AACnF;AACA,OAAO,SAASK,cAAcA,CAACC,KAAK,EAAE;EAClC,OAAQX,eAAe,CAACW,KAAK,CAAC,IAC1BA,KAAK,CAACC,IAAI,KAAK,UAAU,IACzBC,KAAK,CAACC,OAAO,CAACH,KAAK,CAACI,WAAW,CAAC;AACxC;AACA,SAASC,aAAaA,CAACL,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACC,IAAI,KAAK,aAAa;AACvC;AACA,SAASK,cAAcA,CAACN,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACC,IAAI,KAAK,cAAc;AACxC;AACA,SAASM,UAAUA,CAACP,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU;AACpC;AACA,SAASO,YAAYA,CAACR,KAAK,EAAE;EACzB,OAAOA,KAAK,CAACC,IAAI,KAAK,YAAY;AACtC;AACA,SAASQ,UAAUA,CAACT,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU;AACpC;AACA,SAASS,aAAaA,CAACV,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACC,IAAI,KAAK,aAAa;AACvC;AACA,SAASU,WAAWA,CAACX,KAAK,EAAE;EACxB,OAAOA,KAAK,CAACC,IAAI,KAAK,WAAW;AACrC;AACA,SAASW,WAAWA,CAACZ,KAAK,EAAE;EACxB,OAAOA,KAAK,CAACC,IAAI,KAAK,WAAW;AACrC;AACA,SAASY,WAAWA,CAACb,KAAK,EAAE;EACxB,OAAOA,KAAK,CAACC,IAAI,KAAK,WAAW;AACrC;AACA,OAAO,SAASa,2BAA2BA,CAACC,MAAM,EAAEC,IAAI,EAAEhB,KAAK,EAAEiB,SAAS,EAAE;EACxE,IAAIV,UAAU,CAACP,KAAK,CAAC,IAAIQ,YAAY,CAACR,KAAK,CAAC,EAAE;IAC1Ce,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGkB,MAAM,CAAClB,KAAK,CAACA,KAAK,CAAC;EAC5C,CAAC,MACI,IAAIM,cAAc,CAACN,KAAK,CAAC,IAAIK,aAAa,CAACL,KAAK,CAAC,EAAE;IACpDe,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGA,KAAK,CAACA,KAAK;EACpC,CAAC,MACI,IAAIU,aAAa,CAACV,KAAK,CAAC,EAAE;IAC3B,IAAImB,cAAc,GAAG,CAAC,CAAC;IACvBnB,KAAK,CAACoB,MAAM,CAACC,GAAG,CAAC,UAAUxB,GAAG,EAAE;MAC5B,OAAOiB,2BAA2B,CAACK,cAAc,EAAEtB,GAAG,CAACmB,IAAI,EAAEnB,GAAG,CAACG,KAAK,EAAEiB,SAAS,CAAC;IACtF,CAAC,CAAC;IACFF,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGmB,cAAc;EACvC,CAAC,MACI,IAAIV,UAAU,CAACT,KAAK,CAAC,EAAE;IACxB,IAAIsB,aAAa,GAAG,CAACL,SAAS,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACgB,IAAI,CAAChB,KAAK,CAAC;IACvDe,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGsB,aAAa;EACtC,CAAC,MACI,IAAIX,WAAW,CAACX,KAAK,CAAC,EAAE;IACzBe,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGA,KAAK,CAACuB,MAAM,CAACF,GAAG,CAAC,UAAUG,SAAS,EAAE;MACvD,IAAIC,iBAAiB,GAAG,CAAC,CAAC;MAC1BX,2BAA2B,CAACW,iBAAiB,EAAET,IAAI,EAAEQ,SAAS,EAAEP,SAAS,CAAC;MAC1E,OAAOQ,iBAAiB,CAACT,IAAI,CAAChB,KAAK,CAAC;IACxC,CAAC,CAAC;EACN,CAAC,MACI,IAAIY,WAAW,CAACZ,KAAK,CAAC,EAAE;IACzBe,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAGA,KAAK,CAACA,KAAK;EACpC,CAAC,MACI,IAAIa,WAAW,CAACb,KAAK,CAAC,EAAE;IACzBe,MAAM,CAACC,IAAI,CAAChB,KAAK,CAAC,GAAG,IAAI;EAC7B,CAAC,MACI;IACD,MAAMZ,iBAAiB,CAAC,EAAE,EAAE4B,IAAI,CAAChB,KAAK,EAAEA,KAAK,CAACC,IAAI,CAAC;EACvD;AACJ;AACA,OAAO,SAASyB,qBAAqBA,CAACC,KAAK,EAAEV,SAAS,EAAE;EACpD,IAAIW,aAAa,GAAG,IAAI;EACxB,IAAID,KAAK,CAACE,UAAU,EAAE;IAClBD,aAAa,GAAG,CAAC,CAAC;IAClBD,KAAK,CAACE,UAAU,CAACC,OAAO,CAAC,UAAUC,SAAS,EAAE;MAC1CH,aAAa,CAACG,SAAS,CAACf,IAAI,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC;MACxC,IAAI+B,SAAS,CAACC,SAAS,EAAE;QACrBD,SAAS,CAACC,SAAS,CAACF,OAAO,CAAC,UAAUG,EAAE,EAAE;UACtC,IAAIjB,IAAI,GAAGiB,EAAE,CAACjB,IAAI;YAAEhB,KAAK,GAAGiC,EAAE,CAACjC,KAAK;UACpC,OAAOc,2BAA2B,CAACc,aAAa,CAACG,SAAS,CAACf,IAAI,CAAChB,KAAK,CAAC,EAAEgB,IAAI,EAAEhB,KAAK,EAAEiB,SAAS,CAAC;QACnG,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA,IAAIF,MAAM,GAAG,IAAI;EACjB,IAAIY,KAAK,CAACK,SAAS,IAAIL,KAAK,CAACK,SAAS,CAACE,MAAM,EAAE;IAC3CnB,MAAM,GAAG,CAAC,CAAC;IACXY,KAAK,CAACK,SAAS,CAACF,OAAO,CAAC,UAAUG,EAAE,EAAE;MAClC,IAAIjB,IAAI,GAAGiB,EAAE,CAACjB,IAAI;QAAEhB,KAAK,GAAGiC,EAAE,CAACjC,KAAK;MACpC,OAAOc,2BAA2B,CAACC,MAAM,EAAEC,IAAI,EAAEhB,KAAK,EAAEiB,SAAS,CAAC;IACtE,CAAC,CAAC;EACN;EACA,OAAOkB,eAAe,CAACR,KAAK,CAACX,IAAI,CAAChB,KAAK,EAAEe,MAAM,EAAEa,aAAa,CAAC;AACnE;AACA,IAAIQ,gBAAgB,GAAG,CACnB,YAAY,EACZ,SAAS,EACT,MAAM,EACN,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,aAAa,CAChB;AACD;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG9C,kBAAkB;AAC9C,OAAO,IAAI4C,eAAe,GAAGG,MAAM,CAACC,MAAM,CAAC,UAAUC,SAAS,EAAEC,IAAI,EAAEZ,UAAU,EAAE;EAC9E,IAAIY,IAAI,IACJZ,UAAU,IACVA,UAAU,CAAC,YAAY,CAAC,IACxBA,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,EAAE;IACjC,IAAIA,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAClCA,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAIQ,UAAU,GAAGb,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,GAC/CA,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,GAChC,EAAE;MACRa,UAAU,CAACC,IAAI,CAAC,CAAC;MACjB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvBF,UAAU,CAACZ,OAAO,CAAC,UAAUe,GAAG,EAAE;QAC9BD,cAAc,CAACC,GAAG,CAAC,GAAGJ,IAAI,CAACI,GAAG,CAAC;MACnC,CAAC,CAAC;MACF,OAAO,EAAE,CAACC,MAAM,CAACjB,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAACiB,MAAM,CAACT,qBAAqB,CAACO,cAAc,CAAC,EAAE,GAAG,CAAC;IAC7G,CAAC,MACI;MACD,OAAOf,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;IAC1C;EACJ;EACA,IAAIkB,iBAAiB,GAAGP,SAAS;EACjC,IAAIC,IAAI,EAAE;IACN;IACA;IACA;IACA,IAAIO,eAAe,GAAGX,qBAAqB,CAACI,IAAI,CAAC;IACjDM,iBAAiB,IAAI,GAAG,CAACD,MAAM,CAACE,eAAe,EAAE,GAAG,CAAC;EACzD;EACA,IAAInB,UAAU,EAAE;IACZS,MAAM,CAACW,IAAI,CAACpB,UAAU,CAAC,CAACC,OAAO,CAAC,UAAUe,GAAG,EAAE;MAC3C,IAAIT,gBAAgB,CAACc,OAAO,CAACL,GAAG,CAAC,KAAK,CAAC,CAAC,EACpC;MACJ,IAAIhB,UAAU,CAACgB,GAAG,CAAC,IAAIP,MAAM,CAACW,IAAI,CAACpB,UAAU,CAACgB,GAAG,CAAC,CAAC,CAACX,MAAM,EAAE;QACxDa,iBAAiB,IAAI,GAAG,CAACD,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,CAACT,qBAAqB,CAACR,UAAU,CAACgB,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;MACjG,CAAC,MACI;QACDE,iBAAiB,IAAI,GAAG,CAACD,MAAM,CAACD,GAAG,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA,OAAOE,iBAAiB;AAC5B,CAAC,EAAE;EACCI,YAAY,EAAE,SAAAA,CAAUC,CAAC,EAAE;IACvB,IAAIC,QAAQ,GAAGhB,qBAAqB;IACpCA,qBAAqB,GAAGe,CAAC;IACzB,OAAOC,QAAQ;EACnB;AACJ,CAAC,CAAC;AACF,OAAO,SAASC,wBAAwBA,CAAC3B,KAAK,EAAEV,SAAS,EAAE;EACvD,IAAIU,KAAK,CAACK,SAAS,IAAIL,KAAK,CAACK,SAAS,CAACE,MAAM,EAAE;IAC3C,IAAIqB,QAAQ,GAAG,CAAC,CAAC;IACjB5B,KAAK,CAACK,SAAS,CAACF,OAAO,CAAC,UAAUG,EAAE,EAAE;MAClC,IAAIjB,IAAI,GAAGiB,EAAE,CAACjB,IAAI;QAAEhB,KAAK,GAAGiC,EAAE,CAACjC,KAAK;MACpC,OAAOc,2BAA2B,CAACyC,QAAQ,EAAEvC,IAAI,EAAEhB,KAAK,EAAEiB,SAAS,CAAC;IACxE,CAAC,CAAC;IACF,OAAOsC,QAAQ;EACnB;EACA,OAAO,IAAI;AACf;AACA,OAAO,SAASC,sBAAsBA,CAAC7B,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAAC8B,KAAK,GAAG9B,KAAK,CAAC8B,KAAK,CAACzD,KAAK,GAAG2B,KAAK,CAACX,IAAI,CAAChB,KAAK;AAC7D;AACA,OAAO,SAAS0D,qBAAqBA,CAACC,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAE;EACrE,IAAIC,SAAS;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAE9B,EAAE,GAAG2B,YAAY,CAACI,UAAU,EAAED,EAAE,GAAG9B,EAAE,CAACC,MAAM,EAAE6B,EAAE,EAAE,EAAE;IACjE,IAAIE,SAAS,GAAGhC,EAAE,CAAC8B,EAAE,CAAC;IACtB,IAAIG,OAAO,CAACD,SAAS,CAAC,EAAE;MACpB,IAAIA,SAAS,CAACjD,IAAI,CAAChB,KAAK,KAAK,YAAY,EAAE;QACvC,OAAO2D,MAAM,CAACH,sBAAsB,CAACS,SAAS,CAAC,CAAC;MACpD;IACJ,CAAC,MACI,IAAIH,SAAS,EAAE;MAChBA,SAAS,CAACK,IAAI,CAACF,SAAS,CAAC;IAC7B,CAAC,MACI;MACDH,SAAS,GAAG,CAACG,SAAS,CAAC;IAC3B;EACJ;EACA,IAAI,OAAON,MAAM,CAACS,UAAU,KAAK,QAAQ,EAAE;IACvC,OAAOT,MAAM,CAACS,UAAU;EAC5B;EACA,IAAIN,SAAS,EAAE;IACX,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEC,WAAW,GAAGR,SAAS,EAAEO,EAAE,GAAGC,WAAW,CAACpC,MAAM,EAAEmC,EAAE,EAAE,EAAE;MACrE,IAAIJ,SAAS,GAAGK,WAAW,CAACD,EAAE,CAAC;MAC/B,IAAIE,QAAQ,GAAGb,qBAAqB,CAACC,MAAM,EAAErE,wBAAwB,CAAC2E,SAAS,EAAEJ,WAAW,CAAC,CAACD,YAAY,EAAEC,WAAW,CAAC;MACxH,IAAI,OAAOU,QAAQ,KAAK,QAAQ,EAAE;QAC9B,OAAOA,QAAQ;MACnB;IACJ;EACJ;AACJ;AACA,OAAO,SAASL,OAAOA,CAACD,SAAS,EAAE;EAC/B,OAAOA,SAAS,CAAChE,IAAI,KAAK,OAAO;AACrC;AACA,OAAO,SAASuE,gBAAgBA,CAACP,SAAS,EAAE;EACxC,OAAOA,SAAS,CAAChE,IAAI,KAAK,gBAAgB;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}