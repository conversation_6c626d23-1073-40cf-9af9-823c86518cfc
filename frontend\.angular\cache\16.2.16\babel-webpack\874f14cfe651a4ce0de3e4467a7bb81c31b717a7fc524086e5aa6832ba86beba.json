{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { ApolloLink, execute } from \"../link/core/index.js\";\nimport { version } from \"../version.js\";\nimport { HttpLink } from \"../link/http/index.js\";\nimport { QueryManager } from \"./QueryManager.js\";\nimport { LocalState } from \"./LocalState.js\";\nvar hasSuggestedDevtools = false;\n// Though mergeOptions now resides in @apollo/client/utilities, it was\n// previously declared and exported from this module, and then reexported from\n// @apollo/client/core. Since we need to preserve that API anyway, the easiest\n// solution is to reexport mergeOptions where it was previously declared (here).\nimport { mergeOptions } from \"../utilities/index.js\";\nimport { getApolloClientMemoryInternals } from \"../utilities/caching/getMemoryInternals.js\";\nexport { mergeOptions };\n/**\n * This is the primary Apollo Client class. It is used to send GraphQL documents (i.e. queries\n * and mutations) to a GraphQL spec-compliant server over an `ApolloLink` instance,\n * receive results from the server and cache the results in a store. It also delivers updates\n * to GraphQL queries through `Observable` instances.\n */\nvar ApolloClient = /** @class */function () {\n  /**\n   * Constructs an instance of `ApolloClient`.\n   *\n   * @example\n   * ```js\n   * import { ApolloClient, InMemoryCache } from '@apollo/client';\n   *\n   * const cache = new InMemoryCache();\n   *\n   * const client = new ApolloClient({\n   *   // Provide required constructor fields\n   *   cache: cache,\n   *   uri: 'http://localhost:4000/',\n   *\n   *   // Provide some optional constructor fields\n   *   name: 'react-web-client',\n   *   version: '1.3',\n   *   queryDeduplication: false,\n   *   defaultOptions: {\n   *     watchQuery: {\n   *       fetchPolicy: 'cache-and-network',\n   *     },\n   *   },\n   * });\n   * ```\n   */\n  function ApolloClient(options) {\n    var _this = this;\n    var _a;\n    this.resetStoreCallbacks = [];\n    this.clearStoreCallbacks = [];\n    if (!options.cache) {\n      throw newInvariantError(16);\n    }\n    var uri = options.uri,\n      credentials = options.credentials,\n      headers = options.headers,\n      cache = options.cache,\n      documentTransform = options.documentTransform,\n      _b = options.ssrMode,\n      ssrMode = _b === void 0 ? false : _b,\n      _c = options.ssrForceFetchDelay,\n      ssrForceFetchDelay = _c === void 0 ? 0 : _c,\n      // Expose the client instance as window.__APOLLO_CLIENT__ and call\n      // onBroadcast in queryManager.broadcastQueries to enable browser\n      // devtools, but disable them by default in production.\n      connectToDevTools = options.connectToDevTools,\n      _d = options.queryDeduplication,\n      queryDeduplication = _d === void 0 ? true : _d,\n      defaultOptions = options.defaultOptions,\n      defaultContext = options.defaultContext,\n      _e = options.assumeImmutableResults,\n      assumeImmutableResults = _e === void 0 ? cache.assumeImmutableResults : _e,\n      resolvers = options.resolvers,\n      typeDefs = options.typeDefs,\n      fragmentMatcher = options.fragmentMatcher,\n      clientAwarenessName = options.name,\n      clientAwarenessVersion = options.version,\n      devtools = options.devtools,\n      dataMasking = options.dataMasking;\n    var link = options.link;\n    if (!link) {\n      link = uri ? new HttpLink({\n        uri: uri,\n        credentials: credentials,\n        headers: headers\n      }) : ApolloLink.empty();\n    }\n    this.link = link;\n    this.cache = cache;\n    this.disableNetworkFetches = ssrMode || ssrForceFetchDelay > 0;\n    this.queryDeduplication = queryDeduplication;\n    this.defaultOptions = defaultOptions || Object.create(null);\n    this.typeDefs = typeDefs;\n    this.devtoolsConfig = __assign(__assign({}, devtools), {\n      enabled: (_a = devtools === null || devtools === void 0 ? void 0 : devtools.enabled) !== null && _a !== void 0 ? _a : connectToDevTools\n    });\n    if (this.devtoolsConfig.enabled === undefined) {\n      this.devtoolsConfig.enabled = globalThis.__DEV__ !== false;\n    }\n    if (ssrForceFetchDelay) {\n      setTimeout(function () {\n        return _this.disableNetworkFetches = false;\n      }, ssrForceFetchDelay);\n    }\n    this.watchQuery = this.watchQuery.bind(this);\n    this.query = this.query.bind(this);\n    this.mutate = this.mutate.bind(this);\n    this.watchFragment = this.watchFragment.bind(this);\n    this.resetStore = this.resetStore.bind(this);\n    this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this);\n    this.version = version;\n    this.localState = new LocalState({\n      cache: cache,\n      client: this,\n      resolvers: resolvers,\n      fragmentMatcher: fragmentMatcher\n    });\n    this.queryManager = new QueryManager({\n      cache: this.cache,\n      link: this.link,\n      defaultOptions: this.defaultOptions,\n      defaultContext: defaultContext,\n      documentTransform: documentTransform,\n      queryDeduplication: queryDeduplication,\n      ssrMode: ssrMode,\n      dataMasking: !!dataMasking,\n      clientAwareness: {\n        name: clientAwarenessName,\n        version: clientAwarenessVersion\n      },\n      localState: this.localState,\n      assumeImmutableResults: assumeImmutableResults,\n      onBroadcast: this.devtoolsConfig.enabled ? function () {\n        if (_this.devToolsHookCb) {\n          _this.devToolsHookCb({\n            action: {},\n            state: {\n              queries: _this.queryManager.getQueryStore(),\n              mutations: _this.queryManager.mutationStore || {}\n            },\n            dataWithOptimisticResults: _this.cache.extract(true)\n          });\n        }\n      } : void 0\n    });\n    if (this.devtoolsConfig.enabled) this.connectToDevTools();\n  }\n  ApolloClient.prototype.connectToDevTools = function () {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    var windowWithDevTools = window;\n    var devtoolsSymbol = Symbol.for(\"apollo.devtools\");\n    (windowWithDevTools[devtoolsSymbol] = windowWithDevTools[devtoolsSymbol] || []).push(this);\n    windowWithDevTools.__APOLLO_CLIENT__ = this;\n    /**\n     * Suggest installing the devtools for developers who don't have them\n     */\n    if (!hasSuggestedDevtools && globalThis.__DEV__ !== false) {\n      hasSuggestedDevtools = true;\n      if (window.document && window.top === window.self && /^(https?|file):$/.test(window.location.protocol)) {\n        setTimeout(function () {\n          if (!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__) {\n            var nav = window.navigator;\n            var ua = nav && nav.userAgent;\n            var url = void 0;\n            if (typeof ua === \"string\") {\n              if (ua.indexOf(\"Chrome/\") > -1) {\n                url = \"https://chrome.google.com/webstore/detail/\" + \"apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm\";\n              } else if (ua.indexOf(\"Firefox/\") > -1) {\n                url = \"https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/\";\n              }\n            }\n            if (url) {\n              globalThis.__DEV__ !== false && invariant.log(\"Download the Apollo DevTools for a better development \" + \"experience: %s\", url);\n            }\n          }\n        }, 10000);\n      }\n    }\n  };\n  Object.defineProperty(ApolloClient.prototype, \"documentTransform\", {\n    /**\n     * The `DocumentTransform` used to modify GraphQL documents before a request\n     * is made. If a custom `DocumentTransform` is not provided, this will be the\n     * default document transform.\n     */\n    get: function () {\n      return this.queryManager.documentTransform;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Call this method to terminate any active client processes, making it safe\n   * to dispose of this `ApolloClient` instance.\n   */\n  ApolloClient.prototype.stop = function () {\n    this.queryManager.stop();\n  };\n  /**\n   * This watches the cache store of the query according to the options specified and\n   * returns an `ObservableQuery`. We can subscribe to this `ObservableQuery` and\n   * receive updated results through an observer when the cache store changes.\n   *\n   * Note that this method is not an implementation of GraphQL subscriptions. Rather,\n   * it uses Apollo's store in order to reactively deliver updates to your query results.\n   *\n   * For example, suppose you call watchQuery on a GraphQL query that fetches a person's\n   * first and last name and this person has a particular object identifier, provided by\n   * dataIdFromObject. Later, a different query fetches that same person's\n   * first and last name and the first name has now changed. Then, any observers associated\n   * with the results of the first query will be updated with a new result object.\n   *\n   * Note that if the cache does not change, the subscriber will *not* be notified.\n   *\n   * See [here](https://medium.com/apollo-stack/the-concepts-of-graphql-bc68bd819be3#.3mb0cbcmc) for\n   * a description of store reactivity.\n   */\n  ApolloClient.prototype.watchQuery = function (options) {\n    if (this.defaultOptions.watchQuery) {\n      options = mergeOptions(this.defaultOptions.watchQuery, options);\n    }\n    // XXX Overwriting options is probably not the best way to do this long term...\n    if (this.disableNetworkFetches && (options.fetchPolicy === \"network-only\" || options.fetchPolicy === \"cache-and-network\")) {\n      options = __assign(__assign({}, options), {\n        fetchPolicy: \"cache-first\"\n      });\n    }\n    return this.queryManager.watchQuery(options);\n  };\n  /**\n   * This resolves a single query according to the options specified and\n   * returns a `Promise` which is either resolved with the resulting data\n   * or rejected with an error.\n   *\n   * @param options - An object of type `QueryOptions` that allows us to\n   * describe how this query should be treated e.g. whether it should hit the\n   * server at all or just resolve from the cache, etc.\n   */\n  ApolloClient.prototype.query = function (options) {\n    if (this.defaultOptions.query) {\n      options = mergeOptions(this.defaultOptions.query, options);\n    }\n    invariant(options.fetchPolicy !== \"cache-and-network\", 17);\n    if (this.disableNetworkFetches && options.fetchPolicy === \"network-only\") {\n      options = __assign(__assign({}, options), {\n        fetchPolicy: \"cache-first\"\n      });\n    }\n    return this.queryManager.query(options);\n  };\n  /**\n   * This resolves a single mutation according to the options specified and returns a\n   * Promise which is either resolved with the resulting data or rejected with an\n   * error. In some cases both `data` and `errors` might be undefined, for example\n   * when `errorPolicy` is set to `'ignore'`.\n   *\n   * It takes options as an object with the following keys and values:\n   */\n  ApolloClient.prototype.mutate = function (options) {\n    if (this.defaultOptions.mutate) {\n      options = mergeOptions(this.defaultOptions.mutate, options);\n    }\n    return this.queryManager.mutate(options);\n  };\n  /**\n   * This subscribes to a graphql subscription according to the options specified and returns an\n   * `Observable` which either emits received data or an error.\n   */\n  ApolloClient.prototype.subscribe = function (options) {\n    var _this = this;\n    var id = this.queryManager.generateQueryId();\n    return this.queryManager.startGraphQLSubscription(options).map(function (result) {\n      return __assign(__assign({}, result), {\n        data: _this.queryManager.maskOperation({\n          document: options.query,\n          data: result.data,\n          fetchPolicy: options.fetchPolicy,\n          id: id\n        })\n      });\n    });\n  };\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL query without making a network request. This method will start at\n   * the root query. To start at a specific id returned by `dataIdFromObject`\n   * use `readFragment`.\n   *\n   * @param optimistic - Set to `true` to allow `readQuery` to return\n   * optimistic results. Is `false` by default.\n   */\n  ApolloClient.prototype.readQuery = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return this.cache.readQuery(options, optimistic);\n  };\n  /**\n   * Watches the cache store of the fragment according to the options specified\n   * and returns an `Observable`. We can subscribe to this\n   * `Observable` and receive updated results through an\n   * observer when the cache store changes.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @since 3.10.0\n   * @param options - An object of type `WatchFragmentOptions` that allows\n   * the cache to identify the fragment and optionally specify whether to react\n   * to optimistic updates.\n   */\n  ApolloClient.prototype.watchFragment = function (options) {\n    var _a;\n    return this.cache.watchFragment(__assign(__assign({}, options), (_a = {}, _a[Symbol.for(\"apollo.dataMasking\")] = this.queryManager.dataMasking, _a)));\n  };\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL fragment without making a network request. This method will read a\n   * GraphQL fragment from any arbitrary id that is currently cached, unlike\n   * `readQuery` which will only read from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @param optimistic - Set to `true` to allow `readFragment` to return\n   * optimistic results. Is `false` by default.\n   */\n  ApolloClient.prototype.readFragment = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return this.cache.readFragment(options, optimistic);\n  };\n  /**\n   * Writes some data in the shape of the provided GraphQL query directly to\n   * the store. This method will start at the root query. To start at a\n   * specific id returned by `dataIdFromObject` then use `writeFragment`.\n   */\n  ApolloClient.prototype.writeQuery = function (options) {\n    var ref = this.cache.writeQuery(options);\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n    return ref;\n  };\n  /**\n   * Writes some data in the shape of the provided GraphQL fragment directly to\n   * the store. This method will write to a GraphQL fragment from any arbitrary\n   * id that is currently cached, unlike `writeQuery` which will only write\n   * from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are writing. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   */\n  ApolloClient.prototype.writeFragment = function (options) {\n    var ref = this.cache.writeFragment(options);\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n    return ref;\n  };\n  ApolloClient.prototype.__actionHookForDevTools = function (cb) {\n    this.devToolsHookCb = cb;\n  };\n  ApolloClient.prototype.__requestRaw = function (payload) {\n    return execute(this.link, payload);\n  };\n  /**\n   * Resets your entire store by clearing out your cache and then re-executing\n   * all of your active queries. This makes it so that you may guarantee that\n   * there is no data left in your store from a time before you called this\n   * method.\n   *\n   * `resetStore()` is useful when your user just logged out. You’ve removed the\n   * user session, and you now want to make sure that any references to data you\n   * might have fetched while the user session was active is gone.\n   *\n   * It is important to remember that `resetStore()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  ApolloClient.prototype.resetStore = function () {\n    var _this = this;\n    return Promise.resolve().then(function () {\n      return _this.queryManager.clearStore({\n        discardWatches: false\n      });\n    }).then(function () {\n      return Promise.all(_this.resetStoreCallbacks.map(function (fn) {\n        return fn();\n      }));\n    }).then(function () {\n      return _this.reFetchObservableQueries();\n    });\n  };\n  /**\n   * Remove all data from the store. Unlike `resetStore`, `clearStore` will\n   * not refetch any active queries.\n   */\n  ApolloClient.prototype.clearStore = function () {\n    var _this = this;\n    return Promise.resolve().then(function () {\n      return _this.queryManager.clearStore({\n        discardWatches: true\n      });\n    }).then(function () {\n      return Promise.all(_this.clearStoreCallbacks.map(function (fn) {\n        return fn();\n      }));\n    });\n  };\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * reset. `onResetStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  ApolloClient.prototype.onResetStore = function (cb) {\n    var _this = this;\n    this.resetStoreCallbacks.push(cb);\n    return function () {\n      _this.resetStoreCallbacks = _this.resetStoreCallbacks.filter(function (c) {\n        return c !== cb;\n      });\n    };\n  };\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * cleared. `onClearStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  ApolloClient.prototype.onClearStore = function (cb) {\n    var _this = this;\n    this.clearStoreCallbacks.push(cb);\n    return function () {\n      _this.clearStoreCallbacks = _this.clearStoreCallbacks.filter(function (c) {\n        return c !== cb;\n      });\n    };\n  };\n  /**\n   * Refetches all of your active queries.\n   *\n   * `reFetchObservableQueries()` is useful if you want to bring the client back to proper state in case of a network outage\n   *\n   * It is important to remember that `reFetchObservableQueries()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   * Takes optional parameter `includeStandby` which will include queries in standby-mode when refetching.\n   */\n  ApolloClient.prototype.reFetchObservableQueries = function (includeStandby) {\n    return this.queryManager.reFetchObservableQueries(includeStandby);\n  };\n  /**\n   * Refetches specified active queries. Similar to \"reFetchObservableQueries()\" but with a specific list of queries.\n   *\n   * `refetchQueries()` is useful for use cases to imperatively refresh a selection of queries.\n   *\n   * It is important to remember that `refetchQueries()` *will* refetch specified active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  ApolloClient.prototype.refetchQueries = function (options) {\n    var map = this.queryManager.refetchQueries(options);\n    var queries = [];\n    var results = [];\n    map.forEach(function (result, obsQuery) {\n      queries.push(obsQuery);\n      results.push(result);\n    });\n    var result = Promise.all(results);\n    // In case you need the raw results immediately, without awaiting\n    // Promise.all(results):\n    result.queries = queries;\n    result.results = results;\n    // If you decide to ignore the result Promise because you're using\n    // result.queries and result.results instead, you shouldn't have to worry\n    // about preventing uncaught rejections for the Promise.all result.\n    result.catch(function (error) {\n      globalThis.__DEV__ !== false && invariant.debug(18, error);\n    });\n    return result;\n  };\n  /**\n   * Get all currently active `ObservableQuery` objects, in a `Map` keyed by\n   * query ID strings.\n   *\n   * An \"active\" query is one that has observers and a `fetchPolicy` other than\n   * \"standby\" or \"cache-only\".\n   *\n   * You can include all `ObservableQuery` objects (including the inactive ones)\n   * by passing \"all\" instead of \"active\", or you can include just a subset of\n   * active queries by passing an array of query names or DocumentNode objects.\n   */\n  ApolloClient.prototype.getObservableQueries = function (include) {\n    if (include === void 0) {\n      include = \"active\";\n    }\n    return this.queryManager.getObservableQueries(include);\n  };\n  /**\n   * Exposes the cache's complete state, in a serializable format for later restoration.\n   */\n  ApolloClient.prototype.extract = function (optimistic) {\n    return this.cache.extract(optimistic);\n  };\n  /**\n   * Replaces existing state in the cache (if any) with the values expressed by\n   * `serializedState`.\n   *\n   * Called when hydrating a cache (server side rendering, or offline storage),\n   * and also (potentially) during hot reloads.\n   */\n  ApolloClient.prototype.restore = function (serializedState) {\n    return this.cache.restore(serializedState);\n  };\n  /**\n   * Add additional local resolvers.\n   */\n  ApolloClient.prototype.addResolvers = function (resolvers) {\n    this.localState.addResolvers(resolvers);\n  };\n  /**\n   * Set (override existing) local resolvers.\n   */\n  ApolloClient.prototype.setResolvers = function (resolvers) {\n    this.localState.setResolvers(resolvers);\n  };\n  /**\n   * Get all registered local resolvers.\n   */\n  ApolloClient.prototype.getResolvers = function () {\n    return this.localState.getResolvers();\n  };\n  /**\n   * Set a custom local state fragment matcher.\n   */\n  ApolloClient.prototype.setLocalStateFragmentMatcher = function (fragmentMatcher) {\n    this.localState.setFragmentMatcher(fragmentMatcher);\n  };\n  /**\n   * Define a new ApolloLink (or link chain) that Apollo Client will use.\n   */\n  ApolloClient.prototype.setLink = function (newLink) {\n    this.link = this.queryManager.link = newLink;\n  };\n  Object.defineProperty(ApolloClient.prototype, \"defaultContext\", {\n    get: function () {\n      return this.queryManager.defaultContext;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  return ApolloClient;\n}();\nexport { ApolloClient };\nif (globalThis.__DEV__ !== false) {\n  ApolloClient.prototype.getMemoryInternals = getApolloClientMemoryInternals;\n}", "map": {"version": 3, "names": ["__assign", "invariant", "newInvariantError", "ApolloLink", "execute", "version", "HttpLink", "QueryManager", "LocalState", "hasSuggestedDevtools", "mergeOptions", "getApolloClientMemoryInternals", "ApolloClient", "options", "_this", "_a", "resetStoreCallbacks", "clearStoreCallbacks", "cache", "uri", "credentials", "headers", "documentTransform", "_b", "ssrMode", "_c", "ssrForceFetchDelay", "connectToDevTools", "_d", "queryDeduplication", "defaultOptions", "defaultContext", "_e", "assumeImmutableResults", "resolvers", "typeDefs", "fragmentMatcher", "clientAwarenessName", "name", "clientAwarenessVersion", "devtools", "dataMasking", "link", "empty", "disableNetworkFetches", "Object", "create", "devtoolsConfig", "enabled", "undefined", "globalThis", "__DEV__", "setTimeout", "watch<PERSON><PERSON>y", "bind", "query", "mutate", "watchFragment", "resetStore", "reFetchObservableQueries", "localState", "client", "query<PERSON>anager", "clientAwareness", "onBroadcast", "devToolsHookCb", "action", "state", "queries", "getQueryStore", "mutations", "mutationStore", "dataWithOptimisticResults", "extract", "prototype", "window", "windowWithDevTools", "devtoolsSymbol", "Symbol", "for", "push", "__APOLLO_CLIENT__", "document", "top", "self", "test", "location", "protocol", "__APOLLO_DEVTOOLS_GLOBAL_HOOK__", "nav", "navigator", "ua", "userAgent", "url", "indexOf", "log", "defineProperty", "get", "enumerable", "configurable", "stop", "fetchPolicy", "subscribe", "id", "generateQueryId", "startGraphQLSubscription", "map", "result", "data", "maskOperation", "readQuery", "optimistic", "readFragment", "writeQuery", "ref", "broadcast", "broadcastQueries", "writeFragment", "__actionHookForDevTools", "cb", "__requestRaw", "payload", "Promise", "resolve", "then", "clearStore", "discardWatches", "all", "fn", "onResetStore", "filter", "c", "onClearStore", "includeStandby", "refetchQueries", "results", "for<PERSON>ach", "obsQuery", "catch", "error", "debug", "getObservableQueries", "include", "restore", "serializedState", "addResolvers", "setResolvers", "getResolvers", "setLocalStateFragmentMatcher", "setFragmentMatcher", "setLink", "newLink", "getMemoryInternals"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/ApolloClient.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { ApolloLink, execute } from \"../link/core/index.js\";\nimport { version } from \"../version.js\";\nimport { HttpLink } from \"../link/http/index.js\";\nimport { QueryManager } from \"./QueryManager.js\";\nimport { LocalState } from \"./LocalState.js\";\nvar hasSuggestedDevtools = false;\n// Though mergeOptions now resides in @apollo/client/utilities, it was\n// previously declared and exported from this module, and then reexported from\n// @apollo/client/core. Since we need to preserve that API anyway, the easiest\n// solution is to reexport mergeOptions where it was previously declared (here).\nimport { mergeOptions } from \"../utilities/index.js\";\nimport { getApolloClientMemoryInternals } from \"../utilities/caching/getMemoryInternals.js\";\nexport { mergeOptions };\n/**\n * This is the primary Apollo Client class. It is used to send GraphQL documents (i.e. queries\n * and mutations) to a GraphQL spec-compliant server over an `ApolloLink` instance,\n * receive results from the server and cache the results in a store. It also delivers updates\n * to GraphQL queries through `Observable` instances.\n */\nvar ApolloClient = /** @class */ (function () {\n    /**\n     * Constructs an instance of `ApolloClient`.\n     *\n     * @example\n     * ```js\n     * import { ApolloClient, InMemoryCache } from '@apollo/client';\n     *\n     * const cache = new InMemoryCache();\n     *\n     * const client = new ApolloClient({\n     *   // Provide required constructor fields\n     *   cache: cache,\n     *   uri: 'http://localhost:4000/',\n     *\n     *   // Provide some optional constructor fields\n     *   name: 'react-web-client',\n     *   version: '1.3',\n     *   queryDeduplication: false,\n     *   defaultOptions: {\n     *     watchQuery: {\n     *       fetchPolicy: 'cache-and-network',\n     *     },\n     *   },\n     * });\n     * ```\n     */\n    function ApolloClient(options) {\n        var _this = this;\n        var _a;\n        this.resetStoreCallbacks = [];\n        this.clearStoreCallbacks = [];\n        if (!options.cache) {\n            throw newInvariantError(16);\n        }\n        var uri = options.uri, credentials = options.credentials, headers = options.headers, cache = options.cache, documentTransform = options.documentTransform, _b = options.ssrMode, ssrMode = _b === void 0 ? false : _b, _c = options.ssrForceFetchDelay, ssrForceFetchDelay = _c === void 0 ? 0 : _c, \n        // Expose the client instance as window.__APOLLO_CLIENT__ and call\n        // onBroadcast in queryManager.broadcastQueries to enable browser\n        // devtools, but disable them by default in production.\n        connectToDevTools = options.connectToDevTools, _d = options.queryDeduplication, queryDeduplication = _d === void 0 ? true : _d, defaultOptions = options.defaultOptions, defaultContext = options.defaultContext, _e = options.assumeImmutableResults, assumeImmutableResults = _e === void 0 ? cache.assumeImmutableResults : _e, resolvers = options.resolvers, typeDefs = options.typeDefs, fragmentMatcher = options.fragmentMatcher, clientAwarenessName = options.name, clientAwarenessVersion = options.version, devtools = options.devtools, dataMasking = options.dataMasking;\n        var link = options.link;\n        if (!link) {\n            link =\n                uri ? new HttpLink({ uri: uri, credentials: credentials, headers: headers }) : ApolloLink.empty();\n        }\n        this.link = link;\n        this.cache = cache;\n        this.disableNetworkFetches = ssrMode || ssrForceFetchDelay > 0;\n        this.queryDeduplication = queryDeduplication;\n        this.defaultOptions = defaultOptions || Object.create(null);\n        this.typeDefs = typeDefs;\n        this.devtoolsConfig = __assign(__assign({}, devtools), { enabled: (_a = devtools === null || devtools === void 0 ? void 0 : devtools.enabled) !== null && _a !== void 0 ? _a : connectToDevTools });\n        if (this.devtoolsConfig.enabled === undefined) {\n            this.devtoolsConfig.enabled = globalThis.__DEV__ !== false;\n        }\n        if (ssrForceFetchDelay) {\n            setTimeout(function () { return (_this.disableNetworkFetches = false); }, ssrForceFetchDelay);\n        }\n        this.watchQuery = this.watchQuery.bind(this);\n        this.query = this.query.bind(this);\n        this.mutate = this.mutate.bind(this);\n        this.watchFragment = this.watchFragment.bind(this);\n        this.resetStore = this.resetStore.bind(this);\n        this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this);\n        this.version = version;\n        this.localState = new LocalState({\n            cache: cache,\n            client: this,\n            resolvers: resolvers,\n            fragmentMatcher: fragmentMatcher,\n        });\n        this.queryManager = new QueryManager({\n            cache: this.cache,\n            link: this.link,\n            defaultOptions: this.defaultOptions,\n            defaultContext: defaultContext,\n            documentTransform: documentTransform,\n            queryDeduplication: queryDeduplication,\n            ssrMode: ssrMode,\n            dataMasking: !!dataMasking,\n            clientAwareness: {\n                name: clientAwarenessName,\n                version: clientAwarenessVersion,\n            },\n            localState: this.localState,\n            assumeImmutableResults: assumeImmutableResults,\n            onBroadcast: this.devtoolsConfig.enabled ?\n                function () {\n                    if (_this.devToolsHookCb) {\n                        _this.devToolsHookCb({\n                            action: {},\n                            state: {\n                                queries: _this.queryManager.getQueryStore(),\n                                mutations: _this.queryManager.mutationStore || {},\n                            },\n                            dataWithOptimisticResults: _this.cache.extract(true),\n                        });\n                    }\n                }\n                : void 0,\n        });\n        if (this.devtoolsConfig.enabled)\n            this.connectToDevTools();\n    }\n    ApolloClient.prototype.connectToDevTools = function () {\n        if (typeof window === \"undefined\") {\n            return;\n        }\n        var windowWithDevTools = window;\n        var devtoolsSymbol = Symbol.for(\"apollo.devtools\");\n        (windowWithDevTools[devtoolsSymbol] =\n            windowWithDevTools[devtoolsSymbol] || []).push(this);\n        windowWithDevTools.__APOLLO_CLIENT__ = this;\n        /**\n         * Suggest installing the devtools for developers who don't have them\n         */\n        if (!hasSuggestedDevtools && globalThis.__DEV__ !== false) {\n            hasSuggestedDevtools = true;\n            if (window.document &&\n                window.top === window.self &&\n                /^(https?|file):$/.test(window.location.protocol)) {\n                setTimeout(function () {\n                    if (!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__) {\n                        var nav = window.navigator;\n                        var ua = nav && nav.userAgent;\n                        var url = void 0;\n                        if (typeof ua === \"string\") {\n                            if (ua.indexOf(\"Chrome/\") > -1) {\n                                url =\n                                    \"https://chrome.google.com/webstore/detail/\" +\n                                        \"apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm\";\n                            }\n                            else if (ua.indexOf(\"Firefox/\") > -1) {\n                                url =\n                                    \"https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/\";\n                            }\n                        }\n                        if (url) {\n                            globalThis.__DEV__ !== false && invariant.log(\"Download the Apollo DevTools for a better development \" +\n                                \"experience: %s\", url);\n                        }\n                    }\n                }, 10000);\n            }\n        }\n    };\n    Object.defineProperty(ApolloClient.prototype, \"documentTransform\", {\n        /**\n         * The `DocumentTransform` used to modify GraphQL documents before a request\n         * is made. If a custom `DocumentTransform` is not provided, this will be the\n         * default document transform.\n         */\n        get: function () {\n            return this.queryManager.documentTransform;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Call this method to terminate any active client processes, making it safe\n     * to dispose of this `ApolloClient` instance.\n     */\n    ApolloClient.prototype.stop = function () {\n        this.queryManager.stop();\n    };\n    /**\n     * This watches the cache store of the query according to the options specified and\n     * returns an `ObservableQuery`. We can subscribe to this `ObservableQuery` and\n     * receive updated results through an observer when the cache store changes.\n     *\n     * Note that this method is not an implementation of GraphQL subscriptions. Rather,\n     * it uses Apollo's store in order to reactively deliver updates to your query results.\n     *\n     * For example, suppose you call watchQuery on a GraphQL query that fetches a person's\n     * first and last name and this person has a particular object identifier, provided by\n     * dataIdFromObject. Later, a different query fetches that same person's\n     * first and last name and the first name has now changed. Then, any observers associated\n     * with the results of the first query will be updated with a new result object.\n     *\n     * Note that if the cache does not change, the subscriber will *not* be notified.\n     *\n     * See [here](https://medium.com/apollo-stack/the-concepts-of-graphql-bc68bd819be3#.3mb0cbcmc) for\n     * a description of store reactivity.\n     */\n    ApolloClient.prototype.watchQuery = function (options) {\n        if (this.defaultOptions.watchQuery) {\n            options = mergeOptions(this.defaultOptions.watchQuery, options);\n        }\n        // XXX Overwriting options is probably not the best way to do this long term...\n        if (this.disableNetworkFetches &&\n            (options.fetchPolicy === \"network-only\" ||\n                options.fetchPolicy === \"cache-and-network\")) {\n            options = __assign(__assign({}, options), { fetchPolicy: \"cache-first\" });\n        }\n        return this.queryManager.watchQuery(options);\n    };\n    /**\n     * This resolves a single query according to the options specified and\n     * returns a `Promise` which is either resolved with the resulting data\n     * or rejected with an error.\n     *\n     * @param options - An object of type `QueryOptions` that allows us to\n     * describe how this query should be treated e.g. whether it should hit the\n     * server at all or just resolve from the cache, etc.\n     */\n    ApolloClient.prototype.query = function (options) {\n        if (this.defaultOptions.query) {\n            options = mergeOptions(this.defaultOptions.query, options);\n        }\n        invariant(options.fetchPolicy !== \"cache-and-network\", 17);\n        if (this.disableNetworkFetches && options.fetchPolicy === \"network-only\") {\n            options = __assign(__assign({}, options), { fetchPolicy: \"cache-first\" });\n        }\n        return this.queryManager.query(options);\n    };\n    /**\n     * This resolves a single mutation according to the options specified and returns a\n     * Promise which is either resolved with the resulting data or rejected with an\n     * error. In some cases both `data` and `errors` might be undefined, for example\n     * when `errorPolicy` is set to `'ignore'`.\n     *\n     * It takes options as an object with the following keys and values:\n     */\n    ApolloClient.prototype.mutate = function (options) {\n        if (this.defaultOptions.mutate) {\n            options = mergeOptions(this.defaultOptions.mutate, options);\n        }\n        return this.queryManager.mutate(options);\n    };\n    /**\n     * This subscribes to a graphql subscription according to the options specified and returns an\n     * `Observable` which either emits received data or an error.\n     */\n    ApolloClient.prototype.subscribe = function (options) {\n        var _this = this;\n        var id = this.queryManager.generateQueryId();\n        return this.queryManager\n            .startGraphQLSubscription(options)\n            .map(function (result) { return (__assign(__assign({}, result), { data: _this.queryManager.maskOperation({\n                document: options.query,\n                data: result.data,\n                fetchPolicy: options.fetchPolicy,\n                id: id,\n            }) })); });\n    };\n    /**\n     * Tries to read some data from the store in the shape of the provided\n     * GraphQL query without making a network request. This method will start at\n     * the root query. To start at a specific id returned by `dataIdFromObject`\n     * use `readFragment`.\n     *\n     * @param optimistic - Set to `true` to allow `readQuery` to return\n     * optimistic results. Is `false` by default.\n     */\n    ApolloClient.prototype.readQuery = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return this.cache.readQuery(options, optimistic);\n    };\n    /**\n     * Watches the cache store of the fragment according to the options specified\n     * and returns an `Observable`. We can subscribe to this\n     * `Observable` and receive updated results through an\n     * observer when the cache store changes.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are reading. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     *\n     * @since 3.10.0\n     * @param options - An object of type `WatchFragmentOptions` that allows\n     * the cache to identify the fragment and optionally specify whether to react\n     * to optimistic updates.\n     */\n    ApolloClient.prototype.watchFragment = function (options) {\n        var _a;\n        return this.cache.watchFragment(__assign(__assign({}, options), (_a = {}, _a[Symbol.for(\"apollo.dataMasking\")] = this.queryManager.dataMasking, _a)));\n    };\n    /**\n     * Tries to read some data from the store in the shape of the provided\n     * GraphQL fragment without making a network request. This method will read a\n     * GraphQL fragment from any arbitrary id that is currently cached, unlike\n     * `readQuery` which will only read from the root query.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are reading. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     *\n     * @param optimistic - Set to `true` to allow `readFragment` to return\n     * optimistic results. Is `false` by default.\n     */\n    ApolloClient.prototype.readFragment = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return this.cache.readFragment(options, optimistic);\n    };\n    /**\n     * Writes some data in the shape of the provided GraphQL query directly to\n     * the store. This method will start at the root query. To start at a\n     * specific id returned by `dataIdFromObject` then use `writeFragment`.\n     */\n    ApolloClient.prototype.writeQuery = function (options) {\n        var ref = this.cache.writeQuery(options);\n        if (options.broadcast !== false) {\n            this.queryManager.broadcastQueries();\n        }\n        return ref;\n    };\n    /**\n     * Writes some data in the shape of the provided GraphQL fragment directly to\n     * the store. This method will write to a GraphQL fragment from any arbitrary\n     * id that is currently cached, unlike `writeQuery` which will only write\n     * from the root query.\n     *\n     * You must pass in a GraphQL document with a single fragment or a document\n     * with multiple fragments that represent what you are writing. If you pass\n     * in a document with multiple fragments then you must also specify a\n     * `fragmentName`.\n     */\n    ApolloClient.prototype.writeFragment = function (options) {\n        var ref = this.cache.writeFragment(options);\n        if (options.broadcast !== false) {\n            this.queryManager.broadcastQueries();\n        }\n        return ref;\n    };\n    ApolloClient.prototype.__actionHookForDevTools = function (cb) {\n        this.devToolsHookCb = cb;\n    };\n    ApolloClient.prototype.__requestRaw = function (payload) {\n        return execute(this.link, payload);\n    };\n    /**\n     * Resets your entire store by clearing out your cache and then re-executing\n     * all of your active queries. This makes it so that you may guarantee that\n     * there is no data left in your store from a time before you called this\n     * method.\n     *\n     * `resetStore()` is useful when your user just logged out. You’ve removed the\n     * user session, and you now want to make sure that any references to data you\n     * might have fetched while the user session was active is gone.\n     *\n     * It is important to remember that `resetStore()` *will* refetch any active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     */\n    ApolloClient.prototype.resetStore = function () {\n        var _this = this;\n        return Promise.resolve()\n            .then(function () {\n            return _this.queryManager.clearStore({\n                discardWatches: false,\n            });\n        })\n            .then(function () { return Promise.all(_this.resetStoreCallbacks.map(function (fn) { return fn(); })); })\n            .then(function () { return _this.reFetchObservableQueries(); });\n    };\n    /**\n     * Remove all data from the store. Unlike `resetStore`, `clearStore` will\n     * not refetch any active queries.\n     */\n    ApolloClient.prototype.clearStore = function () {\n        var _this = this;\n        return Promise.resolve()\n            .then(function () {\n            return _this.queryManager.clearStore({\n                discardWatches: true,\n            });\n        })\n            .then(function () { return Promise.all(_this.clearStoreCallbacks.map(function (fn) { return fn(); })); });\n    };\n    /**\n     * Allows callbacks to be registered that are executed when the store is\n     * reset. `onResetStore` returns an unsubscribe function that can be used\n     * to remove registered callbacks.\n     */\n    ApolloClient.prototype.onResetStore = function (cb) {\n        var _this = this;\n        this.resetStoreCallbacks.push(cb);\n        return function () {\n            _this.resetStoreCallbacks = _this.resetStoreCallbacks.filter(function (c) { return c !== cb; });\n        };\n    };\n    /**\n     * Allows callbacks to be registered that are executed when the store is\n     * cleared. `onClearStore` returns an unsubscribe function that can be used\n     * to remove registered callbacks.\n     */\n    ApolloClient.prototype.onClearStore = function (cb) {\n        var _this = this;\n        this.clearStoreCallbacks.push(cb);\n        return function () {\n            _this.clearStoreCallbacks = _this.clearStoreCallbacks.filter(function (c) { return c !== cb; });\n        };\n    };\n    /**\n     * Refetches all of your active queries.\n     *\n     * `reFetchObservableQueries()` is useful if you want to bring the client back to proper state in case of a network outage\n     *\n     * It is important to remember that `reFetchObservableQueries()` *will* refetch any active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     * Takes optional parameter `includeStandby` which will include queries in standby-mode when refetching.\n     */\n    ApolloClient.prototype.reFetchObservableQueries = function (includeStandby) {\n        return this.queryManager.reFetchObservableQueries(includeStandby);\n    };\n    /**\n     * Refetches specified active queries. Similar to \"reFetchObservableQueries()\" but with a specific list of queries.\n     *\n     * `refetchQueries()` is useful for use cases to imperatively refresh a selection of queries.\n     *\n     * It is important to remember that `refetchQueries()` *will* refetch specified active\n     * queries. This means that any components that might be mounted will execute\n     * their queries again using your network interface. If you do not want to\n     * re-execute any queries then you should make sure to stop watching any\n     * active queries.\n     */\n    ApolloClient.prototype.refetchQueries = function (options) {\n        var map = this.queryManager.refetchQueries(options);\n        var queries = [];\n        var results = [];\n        map.forEach(function (result, obsQuery) {\n            queries.push(obsQuery);\n            results.push(result);\n        });\n        var result = Promise.all(results);\n        // In case you need the raw results immediately, without awaiting\n        // Promise.all(results):\n        result.queries = queries;\n        result.results = results;\n        // If you decide to ignore the result Promise because you're using\n        // result.queries and result.results instead, you shouldn't have to worry\n        // about preventing uncaught rejections for the Promise.all result.\n        result.catch(function (error) {\n            globalThis.__DEV__ !== false && invariant.debug(18, error);\n        });\n        return result;\n    };\n    /**\n     * Get all currently active `ObservableQuery` objects, in a `Map` keyed by\n     * query ID strings.\n     *\n     * An \"active\" query is one that has observers and a `fetchPolicy` other than\n     * \"standby\" or \"cache-only\".\n     *\n     * You can include all `ObservableQuery` objects (including the inactive ones)\n     * by passing \"all\" instead of \"active\", or you can include just a subset of\n     * active queries by passing an array of query names or DocumentNode objects.\n     */\n    ApolloClient.prototype.getObservableQueries = function (include) {\n        if (include === void 0) { include = \"active\"; }\n        return this.queryManager.getObservableQueries(include);\n    };\n    /**\n     * Exposes the cache's complete state, in a serializable format for later restoration.\n     */\n    ApolloClient.prototype.extract = function (optimistic) {\n        return this.cache.extract(optimistic);\n    };\n    /**\n     * Replaces existing state in the cache (if any) with the values expressed by\n     * `serializedState`.\n     *\n     * Called when hydrating a cache (server side rendering, or offline storage),\n     * and also (potentially) during hot reloads.\n     */\n    ApolloClient.prototype.restore = function (serializedState) {\n        return this.cache.restore(serializedState);\n    };\n    /**\n     * Add additional local resolvers.\n     */\n    ApolloClient.prototype.addResolvers = function (resolvers) {\n        this.localState.addResolvers(resolvers);\n    };\n    /**\n     * Set (override existing) local resolvers.\n     */\n    ApolloClient.prototype.setResolvers = function (resolvers) {\n        this.localState.setResolvers(resolvers);\n    };\n    /**\n     * Get all registered local resolvers.\n     */\n    ApolloClient.prototype.getResolvers = function () {\n        return this.localState.getResolvers();\n    };\n    /**\n     * Set a custom local state fragment matcher.\n     */\n    ApolloClient.prototype.setLocalStateFragmentMatcher = function (fragmentMatcher) {\n        this.localState.setFragmentMatcher(fragmentMatcher);\n    };\n    /**\n     * Define a new ApolloLink (or link chain) that Apollo Client will use.\n     */\n    ApolloClient.prototype.setLink = function (newLink) {\n        this.link = this.queryManager.link = newLink;\n    };\n    Object.defineProperty(ApolloClient.prototype, \"defaultContext\", {\n        get: function () {\n            return this.queryManager.defaultContext;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ApolloClient;\n}());\nexport { ApolloClient };\nif (globalThis.__DEV__ !== false) {\n    ApolloClient.prototype.getMemoryInternals = getApolloClientMemoryInternals;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,+BAA+B;AAC5E,SAASC,UAAU,EAAEC,OAAO,QAAQ,uBAAuB;AAC3D,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,IAAIC,oBAAoB,GAAG,KAAK;AAChC;AACA;AACA;AACA;AACA,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASD,YAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,YAAY,GAAG,aAAe,YAAY;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,YAAYA,CAACC,OAAO,EAAE;IAC3B,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,EAAE;IACN,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACJ,OAAO,CAACK,KAAK,EAAE;MAChB,MAAMhB,iBAAiB,CAAC,EAAE,CAAC;IAC/B;IACA,IAAIiB,GAAG,GAAGN,OAAO,CAACM,GAAG;MAAEC,WAAW,GAAGP,OAAO,CAACO,WAAW;MAAEC,OAAO,GAAGR,OAAO,CAACQ,OAAO;MAAEH,KAAK,GAAGL,OAAO,CAACK,KAAK;MAAEI,iBAAiB,GAAGT,OAAO,CAACS,iBAAiB;MAAEC,EAAE,GAAGV,OAAO,CAACW,OAAO;MAAEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEE,EAAE,GAAGZ,OAAO,CAACa,kBAAkB;MAAEA,kBAAkB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;MACnS;MACA;MACA;MACAE,iBAAiB,GAAGd,OAAO,CAACc,iBAAiB;MAAEC,EAAE,GAAGf,OAAO,CAACgB,kBAAkB;MAAEA,kBAAkB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEE,cAAc,GAAGjB,OAAO,CAACiB,cAAc;MAAEC,cAAc,GAAGlB,OAAO,CAACkB,cAAc;MAAEC,EAAE,GAAGnB,OAAO,CAACoB,sBAAsB;MAAEA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGd,KAAK,CAACe,sBAAsB,GAAGD,EAAE;MAAEE,SAAS,GAAGrB,OAAO,CAACqB,SAAS;MAAEC,QAAQ,GAAGtB,OAAO,CAACsB,QAAQ;MAAEC,eAAe,GAAGvB,OAAO,CAACuB,eAAe;MAAEC,mBAAmB,GAAGxB,OAAO,CAACyB,IAAI;MAAEC,sBAAsB,GAAG1B,OAAO,CAACR,OAAO;MAAEmC,QAAQ,GAAG3B,OAAO,CAAC2B,QAAQ;MAAEC,WAAW,GAAG5B,OAAO,CAAC4B,WAAW;IACtjB,IAAIC,IAAI,GAAG7B,OAAO,CAAC6B,IAAI;IACvB,IAAI,CAACA,IAAI,EAAE;MACPA,IAAI,GACAvB,GAAG,GAAG,IAAIb,QAAQ,CAAC;QAAEa,GAAG,EAAEA,GAAG;QAAEC,WAAW,EAAEA,WAAW;QAAEC,OAAO,EAAEA;MAAQ,CAAC,CAAC,GAAGlB,UAAU,CAACwC,KAAK,CAAC,CAAC;IACzG;IACA,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACxB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0B,qBAAqB,GAAGpB,OAAO,IAAIE,kBAAkB,GAAG,CAAC;IAC9D,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc,IAAIe,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC3D,IAAI,CAACX,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACY,cAAc,GAAG/C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwC,QAAQ,CAAC,EAAE;MAAEQ,OAAO,EAAE,CAACjC,EAAE,GAAGyB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACQ,OAAO,MAAM,IAAI,IAAIjC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGY;IAAkB,CAAC,CAAC;IACnM,IAAI,IAAI,CAACoB,cAAc,CAACC,OAAO,KAAKC,SAAS,EAAE;MAC3C,IAAI,CAACF,cAAc,CAACC,OAAO,GAAGE,UAAU,CAACC,OAAO,KAAK,KAAK;IAC9D;IACA,IAAIzB,kBAAkB,EAAE;MACpB0B,UAAU,CAAC,YAAY;QAAE,OAAQtC,KAAK,CAAC8B,qBAAqB,GAAG,KAAK;MAAG,CAAC,EAAElB,kBAAkB,CAAC;IACjG;IACA,IAAI,CAAC2B,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACF,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACG,aAAa,GAAG,IAAI,CAACA,aAAa,CAACH,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACI,UAAU,GAAG,IAAI,CAACA,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACK,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACL,IAAI,CAAC,IAAI,CAAC;IACxE,IAAI,CAACjD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuD,UAAU,GAAG,IAAIpD,UAAU,CAAC;MAC7BU,KAAK,EAAEA,KAAK;MACZ2C,MAAM,EAAE,IAAI;MACZ3B,SAAS,EAAEA,SAAS;MACpBE,eAAe,EAAEA;IACrB,CAAC,CAAC;IACF,IAAI,CAAC0B,YAAY,GAAG,IAAIvD,YAAY,CAAC;MACjCW,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBwB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfZ,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCC,cAAc,EAAEA,cAAc;MAC9BT,iBAAiB,EAAEA,iBAAiB;MACpCO,kBAAkB,EAAEA,kBAAkB;MACtCL,OAAO,EAAEA,OAAO;MAChBiB,WAAW,EAAE,CAAC,CAACA,WAAW;MAC1BsB,eAAe,EAAE;QACbzB,IAAI,EAAED,mBAAmB;QACzBhC,OAAO,EAAEkC;MACb,CAAC;MACDqB,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B3B,sBAAsB,EAAEA,sBAAsB;MAC9C+B,WAAW,EAAE,IAAI,CAACjB,cAAc,CAACC,OAAO,GACpC,YAAY;QACR,IAAIlC,KAAK,CAACmD,cAAc,EAAE;UACtBnD,KAAK,CAACmD,cAAc,CAAC;YACjBC,MAAM,EAAE,CAAC,CAAC;YACVC,KAAK,EAAE;cACHC,OAAO,EAAEtD,KAAK,CAACgD,YAAY,CAACO,aAAa,CAAC,CAAC;cAC3CC,SAAS,EAAExD,KAAK,CAACgD,YAAY,CAACS,aAAa,IAAI,CAAC;YACpD,CAAC;YACDC,yBAAyB,EAAE1D,KAAK,CAACI,KAAK,CAACuD,OAAO,CAAC,IAAI;UACvD,CAAC,CAAC;QACN;MACJ,CAAC,GACC,KAAK;IACf,CAAC,CAAC;IACF,IAAI,IAAI,CAAC1B,cAAc,CAACC,OAAO,EAC3B,IAAI,CAACrB,iBAAiB,CAAC,CAAC;EAChC;EACAf,YAAY,CAAC8D,SAAS,CAAC/C,iBAAiB,GAAG,YAAY;IACnD,IAAI,OAAOgD,MAAM,KAAK,WAAW,EAAE;MAC/B;IACJ;IACA,IAAIC,kBAAkB,GAAGD,MAAM;IAC/B,IAAIE,cAAc,GAAGC,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAClD,CAACH,kBAAkB,CAACC,cAAc,CAAC,GAC/BD,kBAAkB,CAACC,cAAc,CAAC,IAAI,EAAE,EAAEG,IAAI,CAAC,IAAI,CAAC;IACxDJ,kBAAkB,CAACK,iBAAiB,GAAG,IAAI;IAC3C;AACR;AACA;IACQ,IAAI,CAACxE,oBAAoB,IAAIyC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;MACvD1C,oBAAoB,GAAG,IAAI;MAC3B,IAAIkE,MAAM,CAACO,QAAQ,IACfP,MAAM,CAACQ,GAAG,KAAKR,MAAM,CAACS,IAAI,IAC1B,kBAAkB,CAACC,IAAI,CAACV,MAAM,CAACW,QAAQ,CAACC,QAAQ,CAAC,EAAE;QACnDnC,UAAU,CAAC,YAAY;UACnB,IAAI,CAACuB,MAAM,CAACa,+BAA+B,EAAE;YACzC,IAAIC,GAAG,GAAGd,MAAM,CAACe,SAAS;YAC1B,IAAIC,EAAE,GAAGF,GAAG,IAAIA,GAAG,CAACG,SAAS;YAC7B,IAAIC,GAAG,GAAG,KAAK,CAAC;YAChB,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;cACxB,IAAIA,EAAE,CAACG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC5BD,GAAG,GACC,4CAA4C,GACxC,4DAA4D;cACxE,CAAC,MACI,IAAIF,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;gBAClCD,GAAG,GACC,wEAAwE;cAChF;YACJ;YACA,IAAIA,GAAG,EAAE;cACL3C,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIlD,SAAS,CAAC8F,GAAG,CAAC,wDAAwD,GAClG,gBAAgB,EAAEF,GAAG,CAAC;YAC9B;UACJ;QACJ,CAAC,EAAE,KAAK,CAAC;MACb;IACJ;EACJ,CAAC;EACDhD,MAAM,CAACmD,cAAc,CAACpF,YAAY,CAAC8D,SAAS,EAAE,mBAAmB,EAAE;IAC/D;AACR;AACA;AACA;AACA;IACQuB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACnC,YAAY,CAACxC,iBAAiB;IAC9C,CAAC;IACD4E,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACIvF,YAAY,CAAC8D,SAAS,CAAC0B,IAAI,GAAG,YAAY;IACtC,IAAI,CAACtC,YAAY,CAACsC,IAAI,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxF,YAAY,CAAC8D,SAAS,CAACrB,UAAU,GAAG,UAAUxC,OAAO,EAAE;IACnD,IAAI,IAAI,CAACiB,cAAc,CAACuB,UAAU,EAAE;MAChCxC,OAAO,GAAGH,YAAY,CAAC,IAAI,CAACoB,cAAc,CAACuB,UAAU,EAAExC,OAAO,CAAC;IACnE;IACA;IACA,IAAI,IAAI,CAAC+B,qBAAqB,KACzB/B,OAAO,CAACwF,WAAW,KAAK,cAAc,IACnCxF,OAAO,CAACwF,WAAW,KAAK,mBAAmB,CAAC,EAAE;MAClDxF,OAAO,GAAGb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC,EAAE;QAAEwF,WAAW,EAAE;MAAc,CAAC,CAAC;IAC7E;IACA,OAAO,IAAI,CAACvC,YAAY,CAACT,UAAU,CAACxC,OAAO,CAAC;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,YAAY,CAAC8D,SAAS,CAACnB,KAAK,GAAG,UAAU1C,OAAO,EAAE;IAC9C,IAAI,IAAI,CAACiB,cAAc,CAACyB,KAAK,EAAE;MAC3B1C,OAAO,GAAGH,YAAY,CAAC,IAAI,CAACoB,cAAc,CAACyB,KAAK,EAAE1C,OAAO,CAAC;IAC9D;IACAZ,SAAS,CAACY,OAAO,CAACwF,WAAW,KAAK,mBAAmB,EAAE,EAAE,CAAC;IAC1D,IAAI,IAAI,CAACzD,qBAAqB,IAAI/B,OAAO,CAACwF,WAAW,KAAK,cAAc,EAAE;MACtExF,OAAO,GAAGb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC,EAAE;QAAEwF,WAAW,EAAE;MAAc,CAAC,CAAC;IAC7E;IACA,OAAO,IAAI,CAACvC,YAAY,CAACP,KAAK,CAAC1C,OAAO,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACID,YAAY,CAAC8D,SAAS,CAAClB,MAAM,GAAG,UAAU3C,OAAO,EAAE;IAC/C,IAAI,IAAI,CAACiB,cAAc,CAAC0B,MAAM,EAAE;MAC5B3C,OAAO,GAAGH,YAAY,CAAC,IAAI,CAACoB,cAAc,CAAC0B,MAAM,EAAE3C,OAAO,CAAC;IAC/D;IACA,OAAO,IAAI,CAACiD,YAAY,CAACN,MAAM,CAAC3C,OAAO,CAAC;EAC5C,CAAC;EACD;AACJ;AACA;AACA;EACID,YAAY,CAAC8D,SAAS,CAAC4B,SAAS,GAAG,UAAUzF,OAAO,EAAE;IAClD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIyF,EAAE,GAAG,IAAI,CAACzC,YAAY,CAAC0C,eAAe,CAAC,CAAC;IAC5C,OAAO,IAAI,CAAC1C,YAAY,CACnB2C,wBAAwB,CAAC5F,OAAO,CAAC,CACjC6F,GAAG,CAAC,UAAUC,MAAM,EAAE;MAAE,OAAQ3G,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2G,MAAM,CAAC,EAAE;QAAEC,IAAI,EAAE9F,KAAK,CAACgD,YAAY,CAAC+C,aAAa,CAAC;UACrG3B,QAAQ,EAAErE,OAAO,CAAC0C,KAAK;UACvBqD,IAAI,EAAED,MAAM,CAACC,IAAI;UACjBP,WAAW,EAAExF,OAAO,CAACwF,WAAW;UAChCE,EAAE,EAAEA;QACR,CAAC;MAAE,CAAC,CAAC;IAAG,CAAC,CAAC;EAClB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3F,YAAY,CAAC8D,SAAS,CAACoC,SAAS,GAAG,UAAUjG,OAAO,EAAEkG,UAAU,EAAE;IAC9D,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO,IAAI,CAAC7F,KAAK,CAAC4F,SAAS,CAACjG,OAAO,EAAEkG,UAAU,CAAC;EACpD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInG,YAAY,CAAC8D,SAAS,CAACjB,aAAa,GAAG,UAAU5C,OAAO,EAAE;IACtD,IAAIE,EAAE;IACN,OAAO,IAAI,CAACG,KAAK,CAACuC,aAAa,CAACzD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC,GAAGE,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAAC+D,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACjB,YAAY,CAACrB,WAAW,EAAE1B,EAAE,CAAC,CAAC,CAAC;EACzJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,YAAY,CAAC8D,SAAS,CAACsC,YAAY,GAAG,UAAUnG,OAAO,EAAEkG,UAAU,EAAE;IACjE,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO,IAAI,CAAC7F,KAAK,CAAC8F,YAAY,CAACnG,OAAO,EAAEkG,UAAU,CAAC;EACvD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACInG,YAAY,CAAC8D,SAAS,CAACuC,UAAU,GAAG,UAAUpG,OAAO,EAAE;IACnD,IAAIqG,GAAG,GAAG,IAAI,CAAChG,KAAK,CAAC+F,UAAU,CAACpG,OAAO,CAAC;IACxC,IAAIA,OAAO,CAACsG,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAI,CAACrD,YAAY,CAACsD,gBAAgB,CAAC,CAAC;IACxC;IACA,OAAOF,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItG,YAAY,CAAC8D,SAAS,CAAC2C,aAAa,GAAG,UAAUxG,OAAO,EAAE;IACtD,IAAIqG,GAAG,GAAG,IAAI,CAAChG,KAAK,CAACmG,aAAa,CAACxG,OAAO,CAAC;IAC3C,IAAIA,OAAO,CAACsG,SAAS,KAAK,KAAK,EAAE;MAC7B,IAAI,CAACrD,YAAY,CAACsD,gBAAgB,CAAC,CAAC;IACxC;IACA,OAAOF,GAAG;EACd,CAAC;EACDtG,YAAY,CAAC8D,SAAS,CAAC4C,uBAAuB,GAAG,UAAUC,EAAE,EAAE;IAC3D,IAAI,CAACtD,cAAc,GAAGsD,EAAE;EAC5B,CAAC;EACD3G,YAAY,CAAC8D,SAAS,CAAC8C,YAAY,GAAG,UAAUC,OAAO,EAAE;IACrD,OAAOrH,OAAO,CAAC,IAAI,CAACsC,IAAI,EAAE+E,OAAO,CAAC;EACtC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7G,YAAY,CAAC8D,SAAS,CAAChB,UAAU,GAAG,YAAY;IAC5C,IAAI5C,KAAK,GAAG,IAAI;IAChB,OAAO4G,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,YAAY;MAClB,OAAO9G,KAAK,CAACgD,YAAY,CAAC+D,UAAU,CAAC;QACjCC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN,CAAC,CAAC,CACGF,IAAI,CAAC,YAAY;MAAE,OAAOF,OAAO,CAACK,GAAG,CAACjH,KAAK,CAACE,mBAAmB,CAAC0F,GAAG,CAAC,UAAUsB,EAAE,EAAE;QAAE,OAAOA,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,CACxGJ,IAAI,CAAC,YAAY;MAAE,OAAO9G,KAAK,CAAC6C,wBAAwB,CAAC,CAAC;IAAE,CAAC,CAAC;EACvE,CAAC;EACD;AACJ;AACA;AACA;EACI/C,YAAY,CAAC8D,SAAS,CAACmD,UAAU,GAAG,YAAY;IAC5C,IAAI/G,KAAK,GAAG,IAAI;IAChB,OAAO4G,OAAO,CAACC,OAAO,CAAC,CAAC,CACnBC,IAAI,CAAC,YAAY;MAClB,OAAO9G,KAAK,CAACgD,YAAY,CAAC+D,UAAU,CAAC;QACjCC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN,CAAC,CAAC,CACGF,IAAI,CAAC,YAAY;MAAE,OAAOF,OAAO,CAACK,GAAG,CAACjH,KAAK,CAACG,mBAAmB,CAACyF,GAAG,CAAC,UAAUsB,EAAE,EAAE;QAAE,OAAOA,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EACjH,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIpH,YAAY,CAAC8D,SAAS,CAACuD,YAAY,GAAG,UAAUV,EAAE,EAAE;IAChD,IAAIzG,KAAK,GAAG,IAAI;IAChB,IAAI,CAACE,mBAAmB,CAACgE,IAAI,CAACuC,EAAE,CAAC;IACjC,OAAO,YAAY;MACfzG,KAAK,CAACE,mBAAmB,GAAGF,KAAK,CAACE,mBAAmB,CAACkH,MAAM,CAAC,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC,KAAKZ,EAAE;MAAE,CAAC,CAAC;IACnG,CAAC;EACL,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI3G,YAAY,CAAC8D,SAAS,CAAC0D,YAAY,GAAG,UAAUb,EAAE,EAAE;IAChD,IAAIzG,KAAK,GAAG,IAAI;IAChB,IAAI,CAACG,mBAAmB,CAAC+D,IAAI,CAACuC,EAAE,CAAC;IACjC,OAAO,YAAY;MACfzG,KAAK,CAACG,mBAAmB,GAAGH,KAAK,CAACG,mBAAmB,CAACiH,MAAM,CAAC,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC,KAAKZ,EAAE;MAAE,CAAC,CAAC;IACnG,CAAC;EACL,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3G,YAAY,CAAC8D,SAAS,CAACf,wBAAwB,GAAG,UAAU0E,cAAc,EAAE;IACxE,OAAO,IAAI,CAACvE,YAAY,CAACH,wBAAwB,CAAC0E,cAAc,CAAC;EACrE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzH,YAAY,CAAC8D,SAAS,CAAC4D,cAAc,GAAG,UAAUzH,OAAO,EAAE;IACvD,IAAI6F,GAAG,GAAG,IAAI,CAAC5C,YAAY,CAACwE,cAAc,CAACzH,OAAO,CAAC;IACnD,IAAIuD,OAAO,GAAG,EAAE;IAChB,IAAImE,OAAO,GAAG,EAAE;IAChB7B,GAAG,CAAC8B,OAAO,CAAC,UAAU7B,MAAM,EAAE8B,QAAQ,EAAE;MACpCrE,OAAO,CAACY,IAAI,CAACyD,QAAQ,CAAC;MACtBF,OAAO,CAACvD,IAAI,CAAC2B,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,IAAIA,MAAM,GAAGe,OAAO,CAACK,GAAG,CAACQ,OAAO,CAAC;IACjC;IACA;IACA5B,MAAM,CAACvC,OAAO,GAAGA,OAAO;IACxBuC,MAAM,CAAC4B,OAAO,GAAGA,OAAO;IACxB;IACA;IACA;IACA5B,MAAM,CAAC+B,KAAK,CAAC,UAAUC,KAAK,EAAE;MAC1BzF,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIlD,SAAS,CAAC2I,KAAK,CAAC,EAAE,EAAED,KAAK,CAAC;IAC9D,CAAC,CAAC;IACF,OAAOhC,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/F,YAAY,CAAC8D,SAAS,CAACmE,oBAAoB,GAAG,UAAUC,OAAO,EAAE;IAC7D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,QAAQ;IAAE;IAC9C,OAAO,IAAI,CAAChF,YAAY,CAAC+E,oBAAoB,CAACC,OAAO,CAAC;EAC1D,CAAC;EACD;AACJ;AACA;EACIlI,YAAY,CAAC8D,SAAS,CAACD,OAAO,GAAG,UAAUsC,UAAU,EAAE;IACnD,OAAO,IAAI,CAAC7F,KAAK,CAACuD,OAAO,CAACsC,UAAU,CAAC;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACInG,YAAY,CAAC8D,SAAS,CAACqE,OAAO,GAAG,UAAUC,eAAe,EAAE;IACxD,OAAO,IAAI,CAAC9H,KAAK,CAAC6H,OAAO,CAACC,eAAe,CAAC;EAC9C,CAAC;EACD;AACJ;AACA;EACIpI,YAAY,CAAC8D,SAAS,CAACuE,YAAY,GAAG,UAAU/G,SAAS,EAAE;IACvD,IAAI,CAAC0B,UAAU,CAACqF,YAAY,CAAC/G,SAAS,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;EACItB,YAAY,CAAC8D,SAAS,CAACwE,YAAY,GAAG,UAAUhH,SAAS,EAAE;IACvD,IAAI,CAAC0B,UAAU,CAACsF,YAAY,CAAChH,SAAS,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;EACItB,YAAY,CAAC8D,SAAS,CAACyE,YAAY,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACvF,UAAU,CAACuF,YAAY,CAAC,CAAC;EACzC,CAAC;EACD;AACJ;AACA;EACIvI,YAAY,CAAC8D,SAAS,CAAC0E,4BAA4B,GAAG,UAAUhH,eAAe,EAAE;IAC7E,IAAI,CAACwB,UAAU,CAACyF,kBAAkB,CAACjH,eAAe,CAAC;EACvD,CAAC;EACD;AACJ;AACA;EACIxB,YAAY,CAAC8D,SAAS,CAAC4E,OAAO,GAAG,UAAUC,OAAO,EAAE;IAChD,IAAI,CAAC7G,IAAI,GAAG,IAAI,CAACoB,YAAY,CAACpB,IAAI,GAAG6G,OAAO;EAChD,CAAC;EACD1G,MAAM,CAACmD,cAAc,CAACpF,YAAY,CAAC8D,SAAS,EAAE,gBAAgB,EAAE;IAC5DuB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACnC,YAAY,CAAC/B,cAAc;IAC3C,CAAC;IACDmE,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF,OAAOvF,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,IAAIsC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;EAC9BvC,YAAY,CAAC8D,SAAS,CAAC8E,kBAAkB,GAAG7I,8BAA8B;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}