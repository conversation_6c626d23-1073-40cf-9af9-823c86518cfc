{"ast": null, "code": "/* Core */\nexport { ApolloClient, mergeOptions } from \"./ApolloClient.js\";\nexport { ObservableQuery } from \"./ObservableQuery.js\";\nexport { NetworkStatus, isNetworkRequestSettled } from \"./networkStatus.js\";\nexport { isApolloError, ApolloError } from \"../errors/index.js\";\nexport { Cache, ApolloCache, InMemoryCache, MissingFieldError, defaultDataIdFromObject, makeVar } from \"../cache/index.js\";\n/* Link */\nexport * from \"../link/core/index.js\";\nexport * from \"../link/http/index.js\";\nexport { fromError, toPromise, fromPromise, throwServerError } from \"../link/utils/index.js\";\nexport { DocumentTransform, Observable, isReference, makeReference } from \"../utilities/index.js\";\n/* Supporting */\n// The verbosity of invariant.{log,warn,error} can be controlled globally\n// (for anyone using the same ts-invariant package) by passing \"log\",\n// \"warn\", \"error\", or \"silent\" to setVerbosity (\"log\" is the default).\n// Note that all invariant.* logging is hidden in production.\nimport { setVerbosity } from \"ts-invariant\";\nexport { setVerbosity as setLogVerbosity };\nsetVerbosity(globalThis.__DEV__ !== false ? \"log\" : \"silent\");\n// Note that importing `gql` by itself, then destructuring\n// additional properties separately before exporting, is intentional.\n// Due to the way the `graphql-tag` library is setup, certain bundlers\n// can't find the properties added to the exported `gql` function without\n// additional guidance (e.g. Rollup - see\n// https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module).\n// Instead of having people that are using bundlers with `@apollo/client` add\n// extra bundler config to help `graphql-tag` exports be found (which would be\n// awkward since they aren't importing `graphql-tag` themselves), this\n// workaround of pulling the extra properties off the `gql` function,\n// then re-exporting them separately, helps keeps bundlers happy without any\n// additional config changes.\nexport { gql, resetCaches, disableFragmentWarnings, enableExperimentalFragmentVariables, disableExperimentalFragmentVariables } from \"graphql-tag\";", "map": {"version": 3, "names": ["ApolloClient", "mergeOptions", "ObservableQuery", "NetworkStatus", "isNetworkRequestSettled", "isApolloError", "ApolloError", "<PERSON><PERSON>", "Apollo<PERSON>ache", "InMemoryCache", "Missing<PERSON>ieldE<PERSON>r", "defaultDataIdFromObject", "makeVar", "fromError", "to<PERSON>romise", "fromPromise", "throwServerError", "DocumentTransform", "Observable", "isReference", "makeReference", "setVerbosity", "setLogVerbosity", "globalThis", "__DEV__", "gql", "resetCaches", "disableFragmentWarnings", "enableExperimentalFragmentVariables", "disableExperimentalFragmentVariables"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/index.js"], "sourcesContent": ["/* Core */\nexport { ApolloClient, mergeOptions } from \"./ApolloClient.js\";\nexport { ObservableQuery } from \"./ObservableQuery.js\";\nexport { NetworkStatus, isNetworkRequestSettled } from \"./networkStatus.js\";\nexport { isApolloError, ApolloError } from \"../errors/index.js\";\nexport { Cache, ApolloCache, InMemoryCache, MissingFieldError, defaultDataIdFromObject, makeVar, } from \"../cache/index.js\";\n/* Link */\nexport * from \"../link/core/index.js\";\nexport * from \"../link/http/index.js\";\nexport { fromError, toPromise, fromPromise, throwServerError, } from \"../link/utils/index.js\";\nexport { DocumentTransform, Observable, isReference, makeReference, } from \"../utilities/index.js\";\n/* Supporting */\n// The verbosity of invariant.{log,warn,error} can be controlled globally\n// (for anyone using the same ts-invariant package) by passing \"log\",\n// \"warn\", \"error\", or \"silent\" to setVerbosity (\"log\" is the default).\n// Note that all invariant.* logging is hidden in production.\nimport { setVerbosity } from \"ts-invariant\";\nexport { setVerbosity as setLogVerbosity };\nsetVerbosity(globalThis.__DEV__ !== false ? \"log\" : \"silent\");\n// Note that importing `gql` by itself, then destructuring\n// additional properties separately before exporting, is intentional.\n// Due to the way the `graphql-tag` library is setup, certain bundlers\n// can't find the properties added to the exported `gql` function without\n// additional guidance (e.g. Rollup - see\n// https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module).\n// Instead of having people that are using bundlers with `@apollo/client` add\n// extra bundler config to help `graphql-tag` exports be found (which would be\n// awkward since they aren't importing `graphql-tag` themselves), this\n// workaround of pulling the extra properties off the `gql` function,\n// then re-exporting them separately, helps keeps bundlers happy without any\n// additional config changes.\nexport { gql, resetCaches, disableFragmentWarnings, enableExperimentalFragmentVariables, disableExperimentalFragmentVariables, } from \"graphql-tag\";\n"], "mappings": "AAAA;AACA,SAASA,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,aAAa,EAAEC,uBAAuB,QAAQ,oBAAoB;AAC3E,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,KAAK,EAAEC,WAAW,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,OAAO,QAAS,mBAAmB;AAC3H;AACA,cAAc,uBAAuB;AACrC,cAAc,uBAAuB;AACrC,SAASC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,gBAAgB,QAAS,wBAAwB;AAC7F,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,aAAa,QAAS,uBAAuB;AAClG;AACA;AACA;AACA;AACA;AACA,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASA,YAAY,IAAIC,eAAe;AACxCD,YAAY,CAACE,UAAU,CAACC,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAG,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,mCAAmC,EAAEC,oCAAoC,QAAS,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}