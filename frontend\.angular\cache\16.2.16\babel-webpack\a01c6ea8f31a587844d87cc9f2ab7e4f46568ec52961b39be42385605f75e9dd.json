{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MessageLayoutComponent,\n  children: [{\n    path: '',\n    redirectTo: 'conversations',\n    pathMatch: 'full'\n  }, {\n    path: 'conversations',\n    component: MessagesListComponent,\n    data: {\n      title: 'Conversations'\n    }\n  }, {\n    path: 'conversations/chat/:id',\n    component: MessageChatComponent,\n    data: {\n      title: 'Chat'\n    }\n  }, {\n    path: 'chat/:id',\n    component: MessageChatComponent,\n    data: {\n      title: 'Chat'\n    }\n  }, {\n    path: 'users',\n    component: UserListComponent,\n    data: {\n      title: 'Utilisateurs'\n    }\n  }, {\n    path: 'new',\n    redirectTo: 'users',\n    pathMatch: 'full'\n  }]\n}];\nexport class MessagesRoutingModule {\n  static {\n    this.ɵfac = function MessagesRoutingModule_Factory(t) {\n      return new (t || MessagesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MessagesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MessagesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "MessageChatComponent", "MessagesListComponent", "UserListComponent", "MessageLayoutComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "data", "title", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { MessageChatComponent } from './message-chat/message-chat.component';\r\nimport { MessagesListComponent } from './messages-list/messages-list.component';\r\nimport { UserListComponent } from './user-list/user-list.component';\r\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: MessageLayoutComponent,\r\n    children: [\r\n      { path: '', redirectTo: 'conversations', pathMatch: 'full' },\r\n      {\r\n        path: 'conversations',\r\n        component: MessagesListComponent,\r\n        data: { title: 'Conversations' },\r\n      },\r\n      {\r\n        path: 'conversations/chat/:id',\r\n        component: MessageChatComponent,\r\n        data: { title: 'Chat' },\r\n      },\r\n      {\r\n        path: 'chat/:id',\r\n        component: MessageChatComponent,\r\n        data: { title: 'Chat' },\r\n      },\r\n      {\r\n        path: 'users',\r\n        component: UserListComponent,\r\n        data: { title: 'Utilisateurs' },\r\n      },\r\n\r\n      {\r\n        path: 'new',\r\n        redirectTo: 'users',\r\n        pathMatch: 'full',\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class MessagesRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,sBAAsB,QAAQ,2CAA2C;;;AAElF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,sBAAsB;EACjCI,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC5D;IACEJ,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEL,qBAAqB;IAChCS,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAe;GAC/B,EACD;IACEN,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAEN,oBAAoB;IAC/BU,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAM;GACtB,EACD;IACEN,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEN,oBAAoB;IAC/BU,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAM;GACtB,EACD;IACEN,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEJ,iBAAiB;IAC5BQ,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAc;GAC9B,EAED;IACEN,IAAI,EAAE,KAAK;IACXG,UAAU,EAAE,OAAO;IACnBC,SAAS,EAAE;GACZ;CAEJ,CACF;AAMD,OAAM,MAAOG,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBb,YAAY,CAACc,QAAQ,CAACT,MAAM,CAAC,EAC7BL,YAAY;IAAA;EAAA;;;2EAEXa,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFtBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}