{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n// Make builtins like Map and Set safe to use with non-extensible objects.\nimport \"./fixPolyfills.js\";\nimport { wrap } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { ApolloCache } from \"../core/cache.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { addTypenameToDocument, isReference, DocumentTransform, canonicalStringify, print, cacheSizes } from \"../../utilities/index.js\";\nimport { StoreReader } from \"./readFromStore.js\";\nimport { StoreWriter } from \"./writeToStore.js\";\nimport { EntityStore, supportsResultCaching } from \"./entityStore.js\";\nimport { makeVar, forgetCache, recallCache } from \"./reactiveVars.js\";\nimport { Policies } from \"./policies.js\";\nimport { hasOwn, normalizeConfig, shouldCanonizeResults } from \"./helpers.js\";\nimport { getInMemoryCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nvar InMemoryCache = /** @class */function (_super) {\n  __extends(InMemoryCache, _super);\n  function InMemoryCache(config) {\n    if (config === void 0) {\n      config = {};\n    }\n    var _this = _super.call(this) || this;\n    _this.watches = new Set();\n    _this.addTypenameTransform = new DocumentTransform(addTypenameToDocument);\n    // Override the default value, since InMemoryCache result objects are frozen\n    // in development and expected to remain logically immutable in production.\n    _this.assumeImmutableResults = true;\n    _this.makeVar = makeVar;\n    _this.txCount = 0;\n    _this.config = normalizeConfig(config);\n    _this.addTypename = !!_this.config.addTypename;\n    _this.policies = new Policies({\n      cache: _this,\n      dataIdFromObject: _this.config.dataIdFromObject,\n      possibleTypes: _this.config.possibleTypes,\n      typePolicies: _this.config.typePolicies\n    });\n    _this.init();\n    return _this;\n  }\n  InMemoryCache.prototype.init = function () {\n    // Passing { resultCaching: false } in the InMemoryCache constructor options\n    // will completely disable dependency tracking, which will improve memory\n    // usage but worsen the performance of repeated reads.\n    var rootStore = this.data = new EntityStore.Root({\n      policies: this.policies,\n      resultCaching: this.config.resultCaching\n    });\n    // When no optimistic writes are currently active, cache.optimisticData ===\n    // cache.data, so there are no additional layers on top of the actual data.\n    // When an optimistic update happens, this.optimisticData will become a\n    // linked list of EntityStore Layer objects that terminates with the\n    // original this.data cache object.\n    this.optimisticData = rootStore.stump;\n    this.resetResultCache();\n  };\n  InMemoryCache.prototype.resetResultCache = function (resetResultIdentities) {\n    var _this = this;\n    var previousReader = this.storeReader;\n    var fragments = this.config.fragments;\n    // The StoreWriter is mostly stateless and so doesn't really need to be\n    // reset, but it does need to have its writer.storeReader reference updated,\n    // so it's simpler to update this.storeWriter as well.\n    this.storeWriter = new StoreWriter(this, this.storeReader = new StoreReader({\n      cache: this,\n      addTypename: this.addTypename,\n      resultCacheMaxSize: this.config.resultCacheMaxSize,\n      canonizeResults: shouldCanonizeResults(this.config),\n      canon: resetResultIdentities ? void 0 : previousReader && previousReader.canon,\n      fragments: fragments\n    }), fragments);\n    this.maybeBroadcastWatch = wrap(function (c, options) {\n      return _this.broadcastWatch(c, options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] || 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n      makeCacheKey: function (c) {\n        // Return a cache key (thus enabling result caching) only if we're\n        // currently using a data store that can track cache dependencies.\n        var store = c.optimistic ? _this.optimisticData : _this.data;\n        if (supportsResultCaching(store)) {\n          var optimistic = c.optimistic,\n            id = c.id,\n            variables = c.variables;\n          return store.makeCacheKey(c.query,\n          // Different watches can have the same query, optimistic\n          // status, rootId, and variables, but if their callbacks are\n          // different, the (identical) result needs to be delivered to\n          // each distinct callback. The easiest way to achieve that\n          // separation is to include c.callback in the cache key for\n          // maybeBroadcastWatch calls. See issue #5733.\n          c.callback, canonicalStringify({\n            optimistic: optimistic,\n            id: id,\n            variables: variables\n          }));\n        }\n      }\n    });\n    // Since we have thrown away all the cached functions that depend on the\n    // CacheGroup dependencies maintained by EntityStore, we should also reset\n    // all CacheGroup dependency information.\n    new Set([this.data.group, this.optimisticData.group]).forEach(function (group) {\n      return group.resetCaching();\n    });\n  };\n  InMemoryCache.prototype.restore = function (data) {\n    this.init();\n    // Since calling this.init() discards/replaces the entire StoreReader, along\n    // with the result caches it maintains, this.data.replace(data) won't have\n    // to bother deleting the old data.\n    if (data) this.data.replace(data);\n    return this;\n  };\n  InMemoryCache.prototype.extract = function (optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return (optimistic ? this.optimisticData : this.data).extract();\n  };\n  InMemoryCache.prototype.read = function (options) {\n    var\n      // Since read returns data or null, without any additional metadata\n      // about whether/where there might have been missing fields, the\n      // default behavior cannot be returnPartialData = true (like it is\n      // for the diff method), since defaulting to true would violate the\n      // integrity of the T in the return type. However, partial data may\n      // be useful in some cases, so returnPartialData:true may be\n      // specified explicitly.\n      _a = options.returnPartialData,\n      // Since read returns data or null, without any additional metadata\n      // about whether/where there might have been missing fields, the\n      // default behavior cannot be returnPartialData = true (like it is\n      // for the diff method), since defaulting to true would violate the\n      // integrity of the T in the return type. However, partial data may\n      // be useful in some cases, so returnPartialData:true may be\n      // specified explicitly.\n      returnPartialData = _a === void 0 ? false : _a;\n    try {\n      return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), {\n        store: options.optimistic ? this.optimisticData : this.data,\n        config: this.config,\n        returnPartialData: returnPartialData\n      })).result || null;\n    } catch (e) {\n      if (e instanceof MissingFieldError) {\n        // Swallow MissingFieldError and return null, so callers do not need to\n        // worry about catching \"normal\" exceptions resulting from incomplete\n        // cache data. Unexpected errors will be re-thrown. If you need more\n        // information about which fields were missing, use cache.diff instead,\n        // and examine diffResult.missing.\n        return null;\n      }\n      throw e;\n    }\n  };\n  InMemoryCache.prototype.write = function (options) {\n    try {\n      ++this.txCount;\n      return this.storeWriter.writeToStore(this.data, options);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.modify = function (options) {\n    if (hasOwn.call(options, \"id\") && !options.id) {\n      // To my knowledge, TypeScript does not currently provide a way to\n      // enforce that an optional property?:type must *not* be undefined\n      // when present. That ability would be useful here, because we want\n      // options.id to default to ROOT_QUERY only when no options.id was\n      // provided. If the caller attempts to pass options.id with a\n      // falsy/undefined value (perhaps because cache.identify failed), we\n      // should not assume the goal was to modify the ROOT_QUERY object.\n      // We could throw, but it seems natural to return false to indicate\n      // that nothing was modified.\n      return false;\n    }\n    var store = options.optimistic // Defaults to false.\n    ? this.optimisticData : this.data;\n    try {\n      ++this.txCount;\n      return store.modify(options.id || \"ROOT_QUERY\", options.fields);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.diff = function (options) {\n    return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), {\n      store: options.optimistic ? this.optimisticData : this.data,\n      rootId: options.id || \"ROOT_QUERY\",\n      config: this.config\n    }));\n  };\n  InMemoryCache.prototype.watch = function (watch) {\n    var _this = this;\n    if (!this.watches.size) {\n      // In case we previously called forgetCache(this) because\n      // this.watches became empty (see below), reattach this cache to any\n      // reactive variables on which it previously depended. It might seem\n      // paradoxical that we're able to recall something we supposedly\n      // forgot, but the point of calling forgetCache(this) is to silence\n      // useless broadcasts while this.watches is empty, and to allow the\n      // cache to be garbage collected. If, however, we manage to call\n      // recallCache(this) here, this cache object must not have been\n      // garbage collected yet, and should resume receiving updates from\n      // reactive variables, now that it has a watcher to notify.\n      recallCache(this);\n    }\n    this.watches.add(watch);\n    if (watch.immediate) {\n      this.maybeBroadcastWatch(watch);\n    }\n    return function () {\n      // Once we remove the last watch from this.watches, cache.broadcastWatches\n      // no longer does anything, so we preemptively tell the reactive variable\n      // system to exclude this cache from future broadcasts.\n      if (_this.watches.delete(watch) && !_this.watches.size) {\n        forgetCache(_this);\n      }\n      // Remove this watch from the LRU cache managed by the\n      // maybeBroadcastWatch OptimisticWrapperFunction, to prevent memory\n      // leaks involving the closure of watch.callback.\n      _this.maybeBroadcastWatch.forget(watch);\n    };\n  };\n  InMemoryCache.prototype.gc = function (options) {\n    var _a;\n    canonicalStringify.reset();\n    print.reset();\n    this.addTypenameTransform.resetCache();\n    (_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.resetCaches();\n    var ids = this.optimisticData.gc();\n    if (options && !this.txCount) {\n      if (options.resetResultCache) {\n        this.resetResultCache(options.resetResultIdentities);\n      } else if (options.resetResultIdentities) {\n        this.storeReader.resetCanon();\n      }\n    }\n    return ids;\n  };\n  // Call this method to ensure the given root ID remains in the cache after\n  // garbage collection, along with its transitive child entities. Note that\n  // the cache automatically retains all directly written entities. By default,\n  // the retainment persists after optimistic updates are removed. Pass true\n  // for the optimistic argument if you would prefer for the retainment to be\n  // discarded when the top-most optimistic layer is removed. Returns the\n  // resulting (non-negative) retainment count.\n  InMemoryCache.prototype.retain = function (rootId, optimistic) {\n    return (optimistic ? this.optimisticData : this.data).retain(rootId);\n  };\n  // Call this method to undo the effect of the retain method, above. Once the\n  // retainment count falls to zero, the given ID will no longer be preserved\n  // during garbage collection, though it may still be preserved by other safe\n  // entities that refer to it. Returns the resulting (non-negative) retainment\n  // count, in case that's useful.\n  InMemoryCache.prototype.release = function (rootId, optimistic) {\n    return (optimistic ? this.optimisticData : this.data).release(rootId);\n  };\n  // Returns the canonical ID for a given StoreObject, obeying typePolicies\n  // and keyFields (and dataIdFromObject, if you still use that). At minimum,\n  // the object must contain a __typename and any primary key fields required\n  // to identify entities of that type. If you pass a query result object, be\n  // sure that none of the primary key fields have been renamed by aliasing.\n  // If you pass a Reference object, its __ref ID string will be returned.\n  InMemoryCache.prototype.identify = function (object) {\n    if (isReference(object)) return object.__ref;\n    try {\n      return this.policies.identify(object)[0];\n    } catch (e) {\n      globalThis.__DEV__ !== false && invariant.warn(e);\n    }\n  };\n  InMemoryCache.prototype.evict = function (options) {\n    if (!options.id) {\n      if (hasOwn.call(options, \"id\")) {\n        // See comment in modify method about why we return false when\n        // options.id exists but is falsy/undefined.\n        return false;\n      }\n      options = __assign(__assign({}, options), {\n        id: \"ROOT_QUERY\"\n      });\n    }\n    try {\n      // It's unlikely that the eviction will end up invoking any other\n      // cache update operations while it's running, but {in,de}crementing\n      // this.txCount still seems like a good idea, for uniformity with\n      // the other update methods.\n      ++this.txCount;\n      // Pass this.data as a limit on the depth of the eviction, so evictions\n      // during optimistic updates (when this.data is temporarily set equal to\n      // this.optimisticData) do not escape their optimistic Layer.\n      return this.optimisticData.evict(options, this.data);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.reset = function (options) {\n    var _this = this;\n    this.init();\n    canonicalStringify.reset();\n    if (options && options.discardWatches) {\n      // Similar to what happens in the unsubscribe function returned by\n      // cache.watch, applied to all current watches.\n      this.watches.forEach(function (watch) {\n        return _this.maybeBroadcastWatch.forget(watch);\n      });\n      this.watches.clear();\n      forgetCache(this);\n    } else {\n      // Calling this.init() above unblocks all maybeBroadcastWatch caching, so\n      // this.broadcastWatches() triggers a broadcast to every current watcher\n      // (letting them know their data is now missing). This default behavior is\n      // convenient because it means the watches do not have to be manually\n      // reestablished after resetting the cache. To prevent this broadcast and\n      // cancel all watches, pass true for options.discardWatches.\n      this.broadcastWatches();\n    }\n    return Promise.resolve();\n  };\n  InMemoryCache.prototype.removeOptimistic = function (idToRemove) {\n    var newOptimisticData = this.optimisticData.removeLayer(idToRemove);\n    if (newOptimisticData !== this.optimisticData) {\n      this.optimisticData = newOptimisticData;\n      this.broadcastWatches();\n    }\n  };\n  InMemoryCache.prototype.batch = function (options) {\n    var _this = this;\n    var update = options.update,\n      _a = options.optimistic,\n      optimistic = _a === void 0 ? true : _a,\n      removeOptimistic = options.removeOptimistic,\n      onWatchUpdated = options.onWatchUpdated;\n    var updateResult;\n    var perform = function (layer) {\n      var _a = _this,\n        data = _a.data,\n        optimisticData = _a.optimisticData;\n      ++_this.txCount;\n      if (layer) {\n        _this.data = _this.optimisticData = layer;\n      }\n      try {\n        return updateResult = update(_this);\n      } finally {\n        --_this.txCount;\n        _this.data = data;\n        _this.optimisticData = optimisticData;\n      }\n    };\n    var alreadyDirty = new Set();\n    if (onWatchUpdated && !this.txCount) {\n      // If an options.onWatchUpdated callback is provided, we want to call it\n      // with only the Cache.WatchOptions objects affected by options.update,\n      // but there might be dirty watchers already waiting to be broadcast that\n      // have nothing to do with the update. To prevent including those watchers\n      // in the post-update broadcast, we perform this initial broadcast to\n      // collect the dirty watchers, so we can re-dirty them later, after the\n      // post-update broadcast, allowing them to receive their pending\n      // broadcasts the next time broadcastWatches is called, just as they would\n      // if we never called cache.batch.\n      this.broadcastWatches(__assign(__assign({}, options), {\n        onWatchUpdated: function (watch) {\n          alreadyDirty.add(watch);\n          return false;\n        }\n      }));\n    }\n    if (typeof optimistic === \"string\") {\n      // Note that there can be multiple layers with the same optimistic ID.\n      // When removeOptimistic(id) is called for that id, all matching layers\n      // will be removed, and the remaining layers will be reapplied.\n      this.optimisticData = this.optimisticData.addLayer(optimistic, perform);\n    } else if (optimistic === false) {\n      // Ensure both this.data and this.optimisticData refer to the root\n      // (non-optimistic) layer of the cache during the update. Note that\n      // this.data could be a Layer if we are currently executing an optimistic\n      // update function, but otherwise will always be an EntityStore.Root\n      // instance.\n      perform(this.data);\n    } else {\n      // Otherwise, leave this.data and this.optimisticData unchanged and run\n      // the update with broadcast batching.\n      perform();\n    }\n    if (typeof removeOptimistic === \"string\") {\n      this.optimisticData = this.optimisticData.removeLayer(removeOptimistic);\n    }\n    // Note: if this.txCount > 0, then alreadyDirty.size === 0, so this code\n    // takes the else branch and calls this.broadcastWatches(options), which\n    // does nothing when this.txCount > 0.\n    if (onWatchUpdated && alreadyDirty.size) {\n      this.broadcastWatches(__assign(__assign({}, options), {\n        onWatchUpdated: function (watch, diff) {\n          var result = onWatchUpdated.call(this, watch, diff);\n          if (result !== false) {\n            // Since onWatchUpdated did not return false, this diff is\n            // about to be broadcast to watch.callback, so we don't need\n            // to re-dirty it with the other alreadyDirty watches below.\n            alreadyDirty.delete(watch);\n          }\n          return result;\n        }\n      }));\n      // Silently re-dirty any watches that were already dirty before the update\n      // was performed, and were not broadcast just now.\n      if (alreadyDirty.size) {\n        alreadyDirty.forEach(function (watch) {\n          return _this.maybeBroadcastWatch.dirty(watch);\n        });\n      }\n    } else {\n      // If alreadyDirty is empty or we don't have an onWatchUpdated\n      // function, we don't need to go to the trouble of wrapping\n      // options.onWatchUpdated.\n      this.broadcastWatches(options);\n    }\n    return updateResult;\n  };\n  InMemoryCache.prototype.performTransaction = function (update, optimisticId) {\n    return this.batch({\n      update: update,\n      optimistic: optimisticId || optimisticId !== null\n    });\n  };\n  InMemoryCache.prototype.transformDocument = function (document) {\n    return this.addTypenameToDocument(this.addFragmentsToDocument(document));\n  };\n  InMemoryCache.prototype.fragmentMatches = function (fragment, typename) {\n    return this.policies.fragmentMatches(fragment, typename);\n  };\n  InMemoryCache.prototype.lookupFragment = function (fragmentName) {\n    var _a;\n    return ((_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.lookup(fragmentName)) || null;\n  };\n  InMemoryCache.prototype.broadcastWatches = function (options) {\n    var _this = this;\n    if (!this.txCount) {\n      this.watches.forEach(function (c) {\n        return _this.maybeBroadcastWatch(c, options);\n      });\n    }\n  };\n  InMemoryCache.prototype.addFragmentsToDocument = function (document) {\n    var fragments = this.config.fragments;\n    return fragments ? fragments.transform(document) : document;\n  };\n  InMemoryCache.prototype.addTypenameToDocument = function (document) {\n    if (this.addTypename) {\n      return this.addTypenameTransform.transformDocument(document);\n    }\n    return document;\n  };\n  // This method is wrapped by maybeBroadcastWatch, which is called by\n  // broadcastWatches, so that we compute and broadcast results only when\n  // the data that would be broadcast might have changed. It would be\n  // simpler to check for changes after recomputing a result but before\n  // broadcasting it, but this wrapping approach allows us to skip both\n  // the recomputation and the broadcast, in most cases.\n  InMemoryCache.prototype.broadcastWatch = function (c, options) {\n    var lastDiff = c.lastDiff;\n    // Both WatchOptions and DiffOptions extend ReadOptions, and DiffOptions\n    // currently requires no additional properties, so we can use c (a\n    // WatchOptions object) as DiffOptions, without having to allocate a new\n    // object, and without having to enumerate the relevant properties (query,\n    // variables, etc.) explicitly. There will be some additional properties\n    // (lastDiff, callback, etc.), but cache.diff ignores them.\n    var diff = this.diff(c);\n    if (options) {\n      if (c.optimistic && typeof options.optimistic === \"string\") {\n        diff.fromOptimisticTransaction = true;\n      }\n      if (options.onWatchUpdated && options.onWatchUpdated.call(this, c, diff, lastDiff) === false) {\n        // Returning false from the onWatchUpdated callback will prevent\n        // calling c.callback(diff) for this watcher.\n        return;\n      }\n    }\n    if (!lastDiff || !equal(lastDiff.result, diff.result)) {\n      c.callback(c.lastDiff = diff, lastDiff);\n    }\n  };\n  return InMemoryCache;\n}(ApolloCache);\nexport { InMemoryCache };\nif (globalThis.__DEV__ !== false) {\n  InMemoryCache.prototype.getMemoryInternals = getInMemoryCacheMemoryInternals;\n}", "map": {"version": 3, "names": ["__assign", "__extends", "invariant", "wrap", "equal", "Apollo<PERSON>ache", "Missing<PERSON>ieldE<PERSON>r", "addTypenameToDocument", "isReference", "DocumentTransform", "canonicalStringify", "print", "cacheSizes", "StoreReader", "StoreWriter", "EntityStore", "supportsResultCaching", "makeVar", "forgetCache", "recallCache", "Policies", "hasOwn", "normalizeConfig", "shouldCanonizeResults", "getInMemoryCacheMemoryInternals", "InMemoryCache", "_super", "config", "_this", "call", "watches", "Set", "addTypenameTransform", "assumeImmutableResults", "txCount", "addTypename", "policies", "cache", "dataIdFromObject", "possibleTypes", "typePolicies", "init", "prototype", "rootStore", "data", "Root", "resultCaching", "optimisticData", "stump", "resetResultCache", "resetResultIdentities", "previousReader", "storeReader", "fragments", "storeWriter", "resultCacheMaxSize", "canon<PERSON><PERSON><PERSON><PERSON><PERSON>", "canon", "maybeBroadcastWatch", "c", "options", "broadcastWatch", "max", "make<PERSON><PERSON><PERSON><PERSON>", "store", "optimistic", "id", "variables", "query", "callback", "group", "for<PERSON>ach", "resetCaching", "restore", "replace", "extract", "read", "_a", "returnPartialData", "diffQueryAgainstStore", "result", "e", "write", "writeToStore", "broadcast", "broadcastWatches", "modify", "fields", "diff", "rootId", "watch", "size", "add", "immediate", "delete", "forget", "gc", "reset", "resetCache", "resetCaches", "ids", "resetCanon", "retain", "release", "identify", "object", "__ref", "globalThis", "__DEV__", "warn", "evict", "discardWatches", "clear", "Promise", "resolve", "removeOptimistic", "idToRemove", "newOptimisticData", "<PERSON><PERSON><PERSON>er", "batch", "update", "onWatchUpdated", "updateResult", "perform", "layer", "alreadyDirty", "add<PERSON><PERSON>er", "dirty", "performTransaction", "optimisticId", "transformDocument", "document", "addFragmentsToDocument", "fragmentMatches", "fragment", "typename", "lookupFragment", "fragmentName", "lookup", "transform", "lastDiff", "fromOptimisticTransaction", "getMemoryInternals"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/inMemoryCache.js"], "sourcesContent": ["import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n// Make builtins like Map and Set safe to use with non-extensible objects.\nimport \"./fixPolyfills.js\";\nimport { wrap } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { ApolloCache } from \"../core/cache.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { addTypenameToDocument, isReference, DocumentTransform, canonicalStringify, print, cacheSizes, } from \"../../utilities/index.js\";\nimport { StoreReader } from \"./readFromStore.js\";\nimport { StoreWriter } from \"./writeToStore.js\";\nimport { EntityStore, supportsResultCaching } from \"./entityStore.js\";\nimport { makeVar, forgetCache, recallCache } from \"./reactiveVars.js\";\nimport { Policies } from \"./policies.js\";\nimport { hasOwn, normalizeConfig, shouldCanonizeResults } from \"./helpers.js\";\nimport { getInMemoryCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nvar InMemoryCache = /** @class */ (function (_super) {\n    __extends(InMemoryCache, _super);\n    function InMemoryCache(config) {\n        if (config === void 0) { config = {}; }\n        var _this = _super.call(this) || this;\n        _this.watches = new Set();\n        _this.addTypenameTransform = new DocumentTransform(addTypenameToDocument);\n        // Override the default value, since InMemoryCache result objects are frozen\n        // in development and expected to remain logically immutable in production.\n        _this.assumeImmutableResults = true;\n        _this.makeVar = makeVar;\n        _this.txCount = 0;\n        _this.config = normalizeConfig(config);\n        _this.addTypename = !!_this.config.addTypename;\n        _this.policies = new Policies({\n            cache: _this,\n            dataIdFromObject: _this.config.dataIdFromObject,\n            possibleTypes: _this.config.possibleTypes,\n            typePolicies: _this.config.typePolicies,\n        });\n        _this.init();\n        return _this;\n    }\n    InMemoryCache.prototype.init = function () {\n        // Passing { resultCaching: false } in the InMemoryCache constructor options\n        // will completely disable dependency tracking, which will improve memory\n        // usage but worsen the performance of repeated reads.\n        var rootStore = (this.data = new EntityStore.Root({\n            policies: this.policies,\n            resultCaching: this.config.resultCaching,\n        }));\n        // When no optimistic writes are currently active, cache.optimisticData ===\n        // cache.data, so there are no additional layers on top of the actual data.\n        // When an optimistic update happens, this.optimisticData will become a\n        // linked list of EntityStore Layer objects that terminates with the\n        // original this.data cache object.\n        this.optimisticData = rootStore.stump;\n        this.resetResultCache();\n    };\n    InMemoryCache.prototype.resetResultCache = function (resetResultIdentities) {\n        var _this = this;\n        var previousReader = this.storeReader;\n        var fragments = this.config.fragments;\n        // The StoreWriter is mostly stateless and so doesn't really need to be\n        // reset, but it does need to have its writer.storeReader reference updated,\n        // so it's simpler to update this.storeWriter as well.\n        this.storeWriter = new StoreWriter(this, (this.storeReader = new StoreReader({\n            cache: this,\n            addTypename: this.addTypename,\n            resultCacheMaxSize: this.config.resultCacheMaxSize,\n            canonizeResults: shouldCanonizeResults(this.config),\n            canon: resetResultIdentities ? void 0 : (previousReader && previousReader.canon),\n            fragments: fragments,\n        })), fragments);\n        this.maybeBroadcastWatch = wrap(function (c, options) {\n            return _this.broadcastWatch(c, options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] ||\n                5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n            makeCacheKey: function (c) {\n                // Return a cache key (thus enabling result caching) only if we're\n                // currently using a data store that can track cache dependencies.\n                var store = c.optimistic ? _this.optimisticData : _this.data;\n                if (supportsResultCaching(store)) {\n                    var optimistic = c.optimistic, id = c.id, variables = c.variables;\n                    return store.makeCacheKey(c.query, \n                    // Different watches can have the same query, optimistic\n                    // status, rootId, and variables, but if their callbacks are\n                    // different, the (identical) result needs to be delivered to\n                    // each distinct callback. The easiest way to achieve that\n                    // separation is to include c.callback in the cache key for\n                    // maybeBroadcastWatch calls. See issue #5733.\n                    c.callback, canonicalStringify({ optimistic: optimistic, id: id, variables: variables }));\n                }\n            },\n        });\n        // Since we have thrown away all the cached functions that depend on the\n        // CacheGroup dependencies maintained by EntityStore, we should also reset\n        // all CacheGroup dependency information.\n        new Set([this.data.group, this.optimisticData.group]).forEach(function (group) {\n            return group.resetCaching();\n        });\n    };\n    InMemoryCache.prototype.restore = function (data) {\n        this.init();\n        // Since calling this.init() discards/replaces the entire StoreReader, along\n        // with the result caches it maintains, this.data.replace(data) won't have\n        // to bother deleting the old data.\n        if (data)\n            this.data.replace(data);\n        return this;\n    };\n    InMemoryCache.prototype.extract = function (optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return (optimistic ? this.optimisticData : this.data).extract();\n    };\n    InMemoryCache.prototype.read = function (options) {\n        var \n        // Since read returns data or null, without any additional metadata\n        // about whether/where there might have been missing fields, the\n        // default behavior cannot be returnPartialData = true (like it is\n        // for the diff method), since defaulting to true would violate the\n        // integrity of the T in the return type. However, partial data may\n        // be useful in some cases, so returnPartialData:true may be\n        // specified explicitly.\n        _a = options.returnPartialData, \n        // Since read returns data or null, without any additional metadata\n        // about whether/where there might have been missing fields, the\n        // default behavior cannot be returnPartialData = true (like it is\n        // for the diff method), since defaulting to true would violate the\n        // integrity of the T in the return type. However, partial data may\n        // be useful in some cases, so returnPartialData:true may be\n        // specified explicitly.\n        returnPartialData = _a === void 0 ? false : _a;\n        try {\n            return (this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), { store: options.optimistic ? this.optimisticData : this.data, config: this.config, returnPartialData: returnPartialData })).result || null);\n        }\n        catch (e) {\n            if (e instanceof MissingFieldError) {\n                // Swallow MissingFieldError and return null, so callers do not need to\n                // worry about catching \"normal\" exceptions resulting from incomplete\n                // cache data. Unexpected errors will be re-thrown. If you need more\n                // information about which fields were missing, use cache.diff instead,\n                // and examine diffResult.missing.\n                return null;\n            }\n            throw e;\n        }\n    };\n    InMemoryCache.prototype.write = function (options) {\n        try {\n            ++this.txCount;\n            return this.storeWriter.writeToStore(this.data, options);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.modify = function (options) {\n        if (hasOwn.call(options, \"id\") && !options.id) {\n            // To my knowledge, TypeScript does not currently provide a way to\n            // enforce that an optional property?:type must *not* be undefined\n            // when present. That ability would be useful here, because we want\n            // options.id to default to ROOT_QUERY only when no options.id was\n            // provided. If the caller attempts to pass options.id with a\n            // falsy/undefined value (perhaps because cache.identify failed), we\n            // should not assume the goal was to modify the ROOT_QUERY object.\n            // We could throw, but it seems natural to return false to indicate\n            // that nothing was modified.\n            return false;\n        }\n        var store = ((options.optimistic) // Defaults to false.\n        ) ?\n            this.optimisticData\n            : this.data;\n        try {\n            ++this.txCount;\n            return store.modify(options.id || \"ROOT_QUERY\", options.fields);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.diff = function (options) {\n        return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), { store: options.optimistic ? this.optimisticData : this.data, rootId: options.id || \"ROOT_QUERY\", config: this.config }));\n    };\n    InMemoryCache.prototype.watch = function (watch) {\n        var _this = this;\n        if (!this.watches.size) {\n            // In case we previously called forgetCache(this) because\n            // this.watches became empty (see below), reattach this cache to any\n            // reactive variables on which it previously depended. It might seem\n            // paradoxical that we're able to recall something we supposedly\n            // forgot, but the point of calling forgetCache(this) is to silence\n            // useless broadcasts while this.watches is empty, and to allow the\n            // cache to be garbage collected. If, however, we manage to call\n            // recallCache(this) here, this cache object must not have been\n            // garbage collected yet, and should resume receiving updates from\n            // reactive variables, now that it has a watcher to notify.\n            recallCache(this);\n        }\n        this.watches.add(watch);\n        if (watch.immediate) {\n            this.maybeBroadcastWatch(watch);\n        }\n        return function () {\n            // Once we remove the last watch from this.watches, cache.broadcastWatches\n            // no longer does anything, so we preemptively tell the reactive variable\n            // system to exclude this cache from future broadcasts.\n            if (_this.watches.delete(watch) && !_this.watches.size) {\n                forgetCache(_this);\n            }\n            // Remove this watch from the LRU cache managed by the\n            // maybeBroadcastWatch OptimisticWrapperFunction, to prevent memory\n            // leaks involving the closure of watch.callback.\n            _this.maybeBroadcastWatch.forget(watch);\n        };\n    };\n    InMemoryCache.prototype.gc = function (options) {\n        var _a;\n        canonicalStringify.reset();\n        print.reset();\n        this.addTypenameTransform.resetCache();\n        (_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.resetCaches();\n        var ids = this.optimisticData.gc();\n        if (options && !this.txCount) {\n            if (options.resetResultCache) {\n                this.resetResultCache(options.resetResultIdentities);\n            }\n            else if (options.resetResultIdentities) {\n                this.storeReader.resetCanon();\n            }\n        }\n        return ids;\n    };\n    // Call this method to ensure the given root ID remains in the cache after\n    // garbage collection, along with its transitive child entities. Note that\n    // the cache automatically retains all directly written entities. By default,\n    // the retainment persists after optimistic updates are removed. Pass true\n    // for the optimistic argument if you would prefer for the retainment to be\n    // discarded when the top-most optimistic layer is removed. Returns the\n    // resulting (non-negative) retainment count.\n    InMemoryCache.prototype.retain = function (rootId, optimistic) {\n        return (optimistic ? this.optimisticData : this.data).retain(rootId);\n    };\n    // Call this method to undo the effect of the retain method, above. Once the\n    // retainment count falls to zero, the given ID will no longer be preserved\n    // during garbage collection, though it may still be preserved by other safe\n    // entities that refer to it. Returns the resulting (non-negative) retainment\n    // count, in case that's useful.\n    InMemoryCache.prototype.release = function (rootId, optimistic) {\n        return (optimistic ? this.optimisticData : this.data).release(rootId);\n    };\n    // Returns the canonical ID for a given StoreObject, obeying typePolicies\n    // and keyFields (and dataIdFromObject, if you still use that). At minimum,\n    // the object must contain a __typename and any primary key fields required\n    // to identify entities of that type. If you pass a query result object, be\n    // sure that none of the primary key fields have been renamed by aliasing.\n    // If you pass a Reference object, its __ref ID string will be returned.\n    InMemoryCache.prototype.identify = function (object) {\n        if (isReference(object))\n            return object.__ref;\n        try {\n            return this.policies.identify(object)[0];\n        }\n        catch (e) {\n            globalThis.__DEV__ !== false && invariant.warn(e);\n        }\n    };\n    InMemoryCache.prototype.evict = function (options) {\n        if (!options.id) {\n            if (hasOwn.call(options, \"id\")) {\n                // See comment in modify method about why we return false when\n                // options.id exists but is falsy/undefined.\n                return false;\n            }\n            options = __assign(__assign({}, options), { id: \"ROOT_QUERY\" });\n        }\n        try {\n            // It's unlikely that the eviction will end up invoking any other\n            // cache update operations while it's running, but {in,de}crementing\n            // this.txCount still seems like a good idea, for uniformity with\n            // the other update methods.\n            ++this.txCount;\n            // Pass this.data as a limit on the depth of the eviction, so evictions\n            // during optimistic updates (when this.data is temporarily set equal to\n            // this.optimisticData) do not escape their optimistic Layer.\n            return this.optimisticData.evict(options, this.data);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.reset = function (options) {\n        var _this = this;\n        this.init();\n        canonicalStringify.reset();\n        if (options && options.discardWatches) {\n            // Similar to what happens in the unsubscribe function returned by\n            // cache.watch, applied to all current watches.\n            this.watches.forEach(function (watch) { return _this.maybeBroadcastWatch.forget(watch); });\n            this.watches.clear();\n            forgetCache(this);\n        }\n        else {\n            // Calling this.init() above unblocks all maybeBroadcastWatch caching, so\n            // this.broadcastWatches() triggers a broadcast to every current watcher\n            // (letting them know their data is now missing). This default behavior is\n            // convenient because it means the watches do not have to be manually\n            // reestablished after resetting the cache. To prevent this broadcast and\n            // cancel all watches, pass true for options.discardWatches.\n            this.broadcastWatches();\n        }\n        return Promise.resolve();\n    };\n    InMemoryCache.prototype.removeOptimistic = function (idToRemove) {\n        var newOptimisticData = this.optimisticData.removeLayer(idToRemove);\n        if (newOptimisticData !== this.optimisticData) {\n            this.optimisticData = newOptimisticData;\n            this.broadcastWatches();\n        }\n    };\n    InMemoryCache.prototype.batch = function (options) {\n        var _this = this;\n        var update = options.update, _a = options.optimistic, optimistic = _a === void 0 ? true : _a, removeOptimistic = options.removeOptimistic, onWatchUpdated = options.onWatchUpdated;\n        var updateResult;\n        var perform = function (layer) {\n            var _a = _this, data = _a.data, optimisticData = _a.optimisticData;\n            ++_this.txCount;\n            if (layer) {\n                _this.data = _this.optimisticData = layer;\n            }\n            try {\n                return (updateResult = update(_this));\n            }\n            finally {\n                --_this.txCount;\n                _this.data = data;\n                _this.optimisticData = optimisticData;\n            }\n        };\n        var alreadyDirty = new Set();\n        if (onWatchUpdated && !this.txCount) {\n            // If an options.onWatchUpdated callback is provided, we want to call it\n            // with only the Cache.WatchOptions objects affected by options.update,\n            // but there might be dirty watchers already waiting to be broadcast that\n            // have nothing to do with the update. To prevent including those watchers\n            // in the post-update broadcast, we perform this initial broadcast to\n            // collect the dirty watchers, so we can re-dirty them later, after the\n            // post-update broadcast, allowing them to receive their pending\n            // broadcasts the next time broadcastWatches is called, just as they would\n            // if we never called cache.batch.\n            this.broadcastWatches(__assign(__assign({}, options), { onWatchUpdated: function (watch) {\n                    alreadyDirty.add(watch);\n                    return false;\n                } }));\n        }\n        if (typeof optimistic === \"string\") {\n            // Note that there can be multiple layers with the same optimistic ID.\n            // When removeOptimistic(id) is called for that id, all matching layers\n            // will be removed, and the remaining layers will be reapplied.\n            this.optimisticData = this.optimisticData.addLayer(optimistic, perform);\n        }\n        else if (optimistic === false) {\n            // Ensure both this.data and this.optimisticData refer to the root\n            // (non-optimistic) layer of the cache during the update. Note that\n            // this.data could be a Layer if we are currently executing an optimistic\n            // update function, but otherwise will always be an EntityStore.Root\n            // instance.\n            perform(this.data);\n        }\n        else {\n            // Otherwise, leave this.data and this.optimisticData unchanged and run\n            // the update with broadcast batching.\n            perform();\n        }\n        if (typeof removeOptimistic === \"string\") {\n            this.optimisticData = this.optimisticData.removeLayer(removeOptimistic);\n        }\n        // Note: if this.txCount > 0, then alreadyDirty.size === 0, so this code\n        // takes the else branch and calls this.broadcastWatches(options), which\n        // does nothing when this.txCount > 0.\n        if (onWatchUpdated && alreadyDirty.size) {\n            this.broadcastWatches(__assign(__assign({}, options), { onWatchUpdated: function (watch, diff) {\n                    var result = onWatchUpdated.call(this, watch, diff);\n                    if (result !== false) {\n                        // Since onWatchUpdated did not return false, this diff is\n                        // about to be broadcast to watch.callback, so we don't need\n                        // to re-dirty it with the other alreadyDirty watches below.\n                        alreadyDirty.delete(watch);\n                    }\n                    return result;\n                } }));\n            // Silently re-dirty any watches that were already dirty before the update\n            // was performed, and were not broadcast just now.\n            if (alreadyDirty.size) {\n                alreadyDirty.forEach(function (watch) { return _this.maybeBroadcastWatch.dirty(watch); });\n            }\n        }\n        else {\n            // If alreadyDirty is empty or we don't have an onWatchUpdated\n            // function, we don't need to go to the trouble of wrapping\n            // options.onWatchUpdated.\n            this.broadcastWatches(options);\n        }\n        return updateResult;\n    };\n    InMemoryCache.prototype.performTransaction = function (update, optimisticId) {\n        return this.batch({\n            update: update,\n            optimistic: optimisticId || optimisticId !== null,\n        });\n    };\n    InMemoryCache.prototype.transformDocument = function (document) {\n        return this.addTypenameToDocument(this.addFragmentsToDocument(document));\n    };\n    InMemoryCache.prototype.fragmentMatches = function (fragment, typename) {\n        return this.policies.fragmentMatches(fragment, typename);\n    };\n    InMemoryCache.prototype.lookupFragment = function (fragmentName) {\n        var _a;\n        return ((_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.lookup(fragmentName)) || null;\n    };\n    InMemoryCache.prototype.broadcastWatches = function (options) {\n        var _this = this;\n        if (!this.txCount) {\n            this.watches.forEach(function (c) { return _this.maybeBroadcastWatch(c, options); });\n        }\n    };\n    InMemoryCache.prototype.addFragmentsToDocument = function (document) {\n        var fragments = this.config.fragments;\n        return fragments ? fragments.transform(document) : document;\n    };\n    InMemoryCache.prototype.addTypenameToDocument = function (document) {\n        if (this.addTypename) {\n            return this.addTypenameTransform.transformDocument(document);\n        }\n        return document;\n    };\n    // This method is wrapped by maybeBroadcastWatch, which is called by\n    // broadcastWatches, so that we compute and broadcast results only when\n    // the data that would be broadcast might have changed. It would be\n    // simpler to check for changes after recomputing a result but before\n    // broadcasting it, but this wrapping approach allows us to skip both\n    // the recomputation and the broadcast, in most cases.\n    InMemoryCache.prototype.broadcastWatch = function (c, options) {\n        var lastDiff = c.lastDiff;\n        // Both WatchOptions and DiffOptions extend ReadOptions, and DiffOptions\n        // currently requires no additional properties, so we can use c (a\n        // WatchOptions object) as DiffOptions, without having to allocate a new\n        // object, and without having to enumerate the relevant properties (query,\n        // variables, etc.) explicitly. There will be some additional properties\n        // (lastDiff, callback, etc.), but cache.diff ignores them.\n        var diff = this.diff(c);\n        if (options) {\n            if (c.optimistic && typeof options.optimistic === \"string\") {\n                diff.fromOptimisticTransaction = true;\n            }\n            if (options.onWatchUpdated &&\n                options.onWatchUpdated.call(this, c, diff, lastDiff) === false) {\n                // Returning false from the onWatchUpdated callback will prevent\n                // calling c.callback(diff) for this watcher.\n                return;\n            }\n        }\n        if (!lastDiff || !equal(lastDiff.result, diff.result)) {\n            c.callback((c.lastDiff = diff), lastDiff);\n        }\n    };\n    return InMemoryCache;\n}(ApolloCache));\nexport { InMemoryCache };\nif (globalThis.__DEV__ !== false) {\n    InMemoryCache.prototype.getMemoryInternals = getInMemoryCacheMemoryInternals;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,kCAAkC;AAC5D;AACA,OAAO,mBAAmB;AAC1B,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,qBAAqB,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEC,UAAU,QAAS,0BAA0B;AACxI,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,kBAAkB;AACrE,SAASC,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,mBAAmB;AACrE,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,cAAc;AAC7E,SAASC,+BAA+B,QAAQ,+CAA+C;AAC/F,IAAIC,aAAa,GAAG,aAAe,UAAUC,MAAM,EAAE;EACjDzB,SAAS,CAACwB,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAACE,MAAM,EAAE;IAC3B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,CAAC,CAAC;IAAE;IACtC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzBH,KAAK,CAACI,oBAAoB,GAAG,IAAIvB,iBAAiB,CAACF,qBAAqB,CAAC;IACzE;IACA;IACAqB,KAAK,CAACK,sBAAsB,GAAG,IAAI;IACnCL,KAAK,CAACX,OAAO,GAAGA,OAAO;IACvBW,KAAK,CAACM,OAAO,GAAG,CAAC;IACjBN,KAAK,CAACD,MAAM,GAAGL,eAAe,CAACK,MAAM,CAAC;IACtCC,KAAK,CAACO,WAAW,GAAG,CAAC,CAACP,KAAK,CAACD,MAAM,CAACQ,WAAW;IAC9CP,KAAK,CAACQ,QAAQ,GAAG,IAAIhB,QAAQ,CAAC;MAC1BiB,KAAK,EAAET,KAAK;MACZU,gBAAgB,EAAEV,KAAK,CAACD,MAAM,CAACW,gBAAgB;MAC/CC,aAAa,EAAEX,KAAK,CAACD,MAAM,CAACY,aAAa;MACzCC,YAAY,EAAEZ,KAAK,CAACD,MAAM,CAACa;IAC/B,CAAC,CAAC;IACFZ,KAAK,CAACa,IAAI,CAAC,CAAC;IACZ,OAAOb,KAAK;EAChB;EACAH,aAAa,CAACiB,SAAS,CAACD,IAAI,GAAG,YAAY;IACvC;IACA;IACA;IACA,IAAIE,SAAS,GAAI,IAAI,CAACC,IAAI,GAAG,IAAI7B,WAAW,CAAC8B,IAAI,CAAC;MAC9CT,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBU,aAAa,EAAE,IAAI,CAACnB,MAAM,CAACmB;IAC/B,CAAC,CAAE;IACH;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAGJ,SAAS,CAACK,KAAK;IACrC,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACDxB,aAAa,CAACiB,SAAS,CAACO,gBAAgB,GAAG,UAAUC,qBAAqB,EAAE;IACxE,IAAItB,KAAK,GAAG,IAAI;IAChB,IAAIuB,cAAc,GAAG,IAAI,CAACC,WAAW;IACrC,IAAIC,SAAS,GAAG,IAAI,CAAC1B,MAAM,CAAC0B,SAAS;IACrC;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAIxC,WAAW,CAAC,IAAI,EAAG,IAAI,CAACsC,WAAW,GAAG,IAAIvC,WAAW,CAAC;MACzEwB,KAAK,EAAE,IAAI;MACXF,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BoB,kBAAkB,EAAE,IAAI,CAAC5B,MAAM,CAAC4B,kBAAkB;MAClDC,eAAe,EAAEjC,qBAAqB,CAAC,IAAI,CAACI,MAAM,CAAC;MACnD8B,KAAK,EAAEP,qBAAqB,GAAG,KAAK,CAAC,GAAIC,cAAc,IAAIA,cAAc,CAACM,KAAM;MAChFJ,SAAS,EAAEA;IACf,CAAC,CAAC,EAAGA,SAAS,CAAC;IACf,IAAI,CAACK,mBAAmB,GAAGvD,IAAI,CAAC,UAAUwD,CAAC,EAAEC,OAAO,EAAE;MAClD,OAAOhC,KAAK,CAACiC,cAAc,CAACF,CAAC,EAAEC,OAAO,CAAC;IAC3C,CAAC,EAAE;MACCE,GAAG,EAAE,IAAI,CAACnC,MAAM,CAAC4B,kBAAkB,IAC/B3C,UAAU,CAAC,mCAAmC,CAAC,IAC/C,IAAI,CAAC;MACTmD,YAAY,EAAE,SAAAA,CAAUJ,CAAC,EAAE;QACvB;QACA;QACA,IAAIK,KAAK,GAAGL,CAAC,CAACM,UAAU,GAAGrC,KAAK,CAACmB,cAAc,GAAGnB,KAAK,CAACgB,IAAI;QAC5D,IAAI5B,qBAAqB,CAACgD,KAAK,CAAC,EAAE;UAC9B,IAAIC,UAAU,GAAGN,CAAC,CAACM,UAAU;YAAEC,EAAE,GAAGP,CAAC,CAACO,EAAE;YAAEC,SAAS,GAAGR,CAAC,CAACQ,SAAS;UACjE,OAAOH,KAAK,CAACD,YAAY,CAACJ,CAAC,CAACS,KAAK;UACjC;UACA;UACA;UACA;UACA;UACA;UACAT,CAAC,CAACU,QAAQ,EAAE3D,kBAAkB,CAAC;YAAEuD,UAAU,EAAEA,UAAU;YAAEC,EAAE,EAAEA,EAAE;YAAEC,SAAS,EAAEA;UAAU,CAAC,CAAC,CAAC;QAC7F;MACJ;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAIpC,GAAG,CAAC,CAAC,IAAI,CAACa,IAAI,CAAC0B,KAAK,EAAE,IAAI,CAACvB,cAAc,CAACuB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUD,KAAK,EAAE;MAC3E,OAAOA,KAAK,CAACE,YAAY,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN,CAAC;EACD/C,aAAa,CAACiB,SAAS,CAAC+B,OAAO,GAAG,UAAU7B,IAAI,EAAE;IAC9C,IAAI,CAACH,IAAI,CAAC,CAAC;IACX;IACA;IACA;IACA,IAAIG,IAAI,EACJ,IAAI,CAACA,IAAI,CAAC8B,OAAO,CAAC9B,IAAI,CAAC;IAC3B,OAAO,IAAI;EACf,CAAC;EACDnB,aAAa,CAACiB,SAAS,CAACiC,OAAO,GAAG,UAAUV,UAAU,EAAE;IACpD,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,KAAK;IAAE;IACjD,OAAO,CAACA,UAAU,GAAG,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACH,IAAI,EAAE+B,OAAO,CAAC,CAAC;EACnE,CAAC;EACDlD,aAAa,CAACiB,SAAS,CAACkC,IAAI,GAAG,UAAUhB,OAAO,EAAE;IAC9C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAiB,EAAE,GAAGjB,OAAO,CAACkB,iBAAiB;MAC9B;MACA;MACA;MACA;MACA;MACA;MACA;MACAA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IAC9C,IAAI;MACA,OAAQ,IAAI,CAACzB,WAAW,CAAC2B,qBAAqB,CAAC/E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4D,OAAO,CAAC,EAAE;QAAEI,KAAK,EAAEJ,OAAO,CAACK,UAAU,GAAG,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACH,IAAI;QAAEjB,MAAM,EAAE,IAAI,CAACA,MAAM;QAAEmD,iBAAiB,EAAEA;MAAkB,CAAC,CAAC,CAAC,CAACE,MAAM,IAAI,IAAI;IAC9N,CAAC,CACD,OAAOC,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY3E,iBAAiB,EAAE;QAChC;QACA;QACA;QACA;QACA;QACA,OAAO,IAAI;MACf;MACA,MAAM2E,CAAC;IACX;EACJ,CAAC;EACDxD,aAAa,CAACiB,SAAS,CAACwC,KAAK,GAAG,UAAUtB,OAAO,EAAE;IAC/C,IAAI;MACA,EAAE,IAAI,CAAC1B,OAAO;MACd,OAAO,IAAI,CAACoB,WAAW,CAAC6B,YAAY,CAAC,IAAI,CAACvC,IAAI,EAAEgB,OAAO,CAAC;IAC5D,CAAC,SACO;MACJ,IAAI,CAAC,GAAE,IAAI,CAAC1B,OAAO,IAAI0B,OAAO,CAACwB,SAAS,KAAK,KAAK,EAAE;QAChD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD5D,aAAa,CAACiB,SAAS,CAAC4C,MAAM,GAAG,UAAU1B,OAAO,EAAE;IAChD,IAAIvC,MAAM,CAACQ,IAAI,CAAC+B,OAAO,EAAE,IAAI,CAAC,IAAI,CAACA,OAAO,CAACM,EAAE,EAAE;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA,IAAIF,KAAK,GAAKJ,OAAO,CAACK,UAAU,CAAE;IAAA,EAE9B,IAAI,CAAClB,cAAc,GACjB,IAAI,CAACH,IAAI;IACf,IAAI;MACA,EAAE,IAAI,CAACV,OAAO;MACd,OAAO8B,KAAK,CAACsB,MAAM,CAAC1B,OAAO,CAACM,EAAE,IAAI,YAAY,EAAEN,OAAO,CAAC2B,MAAM,CAAC;IACnE,CAAC,SACO;MACJ,IAAI,CAAC,GAAE,IAAI,CAACrD,OAAO,IAAI0B,OAAO,CAACwB,SAAS,KAAK,KAAK,EAAE;QAChD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD5D,aAAa,CAACiB,SAAS,CAAC8C,IAAI,GAAG,UAAU5B,OAAO,EAAE;IAC9C,OAAO,IAAI,CAACR,WAAW,CAAC2B,qBAAqB,CAAC/E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4D,OAAO,CAAC,EAAE;MAAEI,KAAK,EAAEJ,OAAO,CAACK,UAAU,GAAG,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACH,IAAI;MAAE6C,MAAM,EAAE7B,OAAO,CAACM,EAAE,IAAI,YAAY;MAAEvC,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,CAAC,CAAC;EAC5M,CAAC;EACDF,aAAa,CAACiB,SAAS,CAACgD,KAAK,GAAG,UAAUA,KAAK,EAAE;IAC7C,IAAI9D,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACE,OAAO,CAAC6D,IAAI,EAAE;MACpB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAxE,WAAW,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,CAACW,OAAO,CAAC8D,GAAG,CAACF,KAAK,CAAC;IACvB,IAAIA,KAAK,CAACG,SAAS,EAAE;MACjB,IAAI,CAACnC,mBAAmB,CAACgC,KAAK,CAAC;IACnC;IACA,OAAO,YAAY;MACf;MACA;MACA;MACA,IAAI9D,KAAK,CAACE,OAAO,CAACgE,MAAM,CAACJ,KAAK,CAAC,IAAI,CAAC9D,KAAK,CAACE,OAAO,CAAC6D,IAAI,EAAE;QACpDzE,WAAW,CAACU,KAAK,CAAC;MACtB;MACA;MACA;MACA;MACAA,KAAK,CAAC8B,mBAAmB,CAACqC,MAAM,CAACL,KAAK,CAAC;IAC3C,CAAC;EACL,CAAC;EACDjE,aAAa,CAACiB,SAAS,CAACsD,EAAE,GAAG,UAAUpC,OAAO,EAAE;IAC5C,IAAIiB,EAAE;IACNnE,kBAAkB,CAACuF,KAAK,CAAC,CAAC;IAC1BtF,KAAK,CAACsF,KAAK,CAAC,CAAC;IACb,IAAI,CAACjE,oBAAoB,CAACkE,UAAU,CAAC,CAAC;IACtC,CAACrB,EAAE,GAAG,IAAI,CAAClD,MAAM,CAAC0B,SAAS,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,WAAW,CAAC,CAAC;IAClF,IAAIC,GAAG,GAAG,IAAI,CAACrD,cAAc,CAACiD,EAAE,CAAC,CAAC;IAClC,IAAIpC,OAAO,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE;MAC1B,IAAI0B,OAAO,CAACX,gBAAgB,EAAE;QAC1B,IAAI,CAACA,gBAAgB,CAACW,OAAO,CAACV,qBAAqB,CAAC;MACxD,CAAC,MACI,IAAIU,OAAO,CAACV,qBAAqB,EAAE;QACpC,IAAI,CAACE,WAAW,CAACiD,UAAU,CAAC,CAAC;MACjC;IACJ;IACA,OAAOD,GAAG;EACd,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA3E,aAAa,CAACiB,SAAS,CAAC4D,MAAM,GAAG,UAAUb,MAAM,EAAExB,UAAU,EAAE;IAC3D,OAAO,CAACA,UAAU,GAAG,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACH,IAAI,EAAE0D,MAAM,CAACb,MAAM,CAAC;EACxE,CAAC;EACD;EACA;EACA;EACA;EACA;EACAhE,aAAa,CAACiB,SAAS,CAAC6D,OAAO,GAAG,UAAUd,MAAM,EAAExB,UAAU,EAAE;IAC5D,OAAO,CAACA,UAAU,GAAG,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACH,IAAI,EAAE2D,OAAO,CAACd,MAAM,CAAC;EACzE,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACAhE,aAAa,CAACiB,SAAS,CAAC8D,QAAQ,GAAG,UAAUC,MAAM,EAAE;IACjD,IAAIjG,WAAW,CAACiG,MAAM,CAAC,EACnB,OAAOA,MAAM,CAACC,KAAK;IACvB,IAAI;MACA,OAAO,IAAI,CAACtE,QAAQ,CAACoE,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CACD,OAAOxB,CAAC,EAAE;MACN0B,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI1G,SAAS,CAAC2G,IAAI,CAAC5B,CAAC,CAAC;IACrD;EACJ,CAAC;EACDxD,aAAa,CAACiB,SAAS,CAACoE,KAAK,GAAG,UAAUlD,OAAO,EAAE;IAC/C,IAAI,CAACA,OAAO,CAACM,EAAE,EAAE;MACb,IAAI7C,MAAM,CAACQ,IAAI,CAAC+B,OAAO,EAAE,IAAI,CAAC,EAAE;QAC5B;QACA;QACA,OAAO,KAAK;MAChB;MACAA,OAAO,GAAG5D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4D,OAAO,CAAC,EAAE;QAAEM,EAAE,EAAE;MAAa,CAAC,CAAC;IACnE;IACA,IAAI;MACA;MACA;MACA;MACA;MACA,EAAE,IAAI,CAAChC,OAAO;MACd;MACA;MACA;MACA,OAAO,IAAI,CAACa,cAAc,CAAC+D,KAAK,CAAClD,OAAO,EAAE,IAAI,CAAChB,IAAI,CAAC;IACxD,CAAC,SACO;MACJ,IAAI,CAAC,GAAE,IAAI,CAACV,OAAO,IAAI0B,OAAO,CAACwB,SAAS,KAAK,KAAK,EAAE;QAChD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD5D,aAAa,CAACiB,SAAS,CAACuD,KAAK,GAAG,UAAUrC,OAAO,EAAE;IAC/C,IAAIhC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACa,IAAI,CAAC,CAAC;IACX/B,kBAAkB,CAACuF,KAAK,CAAC,CAAC;IAC1B,IAAIrC,OAAO,IAAIA,OAAO,CAACmD,cAAc,EAAE;MACnC;MACA;MACA,IAAI,CAACjF,OAAO,CAACyC,OAAO,CAAC,UAAUmB,KAAK,EAAE;QAAE,OAAO9D,KAAK,CAAC8B,mBAAmB,CAACqC,MAAM,CAACL,KAAK,CAAC;MAAE,CAAC,CAAC;MAC1F,IAAI,CAAC5D,OAAO,CAACkF,KAAK,CAAC,CAAC;MACpB9F,WAAW,CAAC,IAAI,CAAC;IACrB,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACmE,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO4B,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B,CAAC;EACDzF,aAAa,CAACiB,SAAS,CAACyE,gBAAgB,GAAG,UAAUC,UAAU,EAAE;IAC7D,IAAIC,iBAAiB,GAAG,IAAI,CAACtE,cAAc,CAACuE,WAAW,CAACF,UAAU,CAAC;IACnE,IAAIC,iBAAiB,KAAK,IAAI,CAACtE,cAAc,EAAE;MAC3C,IAAI,CAACA,cAAc,GAAGsE,iBAAiB;MACvC,IAAI,CAAChC,gBAAgB,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD5D,aAAa,CAACiB,SAAS,CAAC6E,KAAK,GAAG,UAAU3D,OAAO,EAAE;IAC/C,IAAIhC,KAAK,GAAG,IAAI;IAChB,IAAI4F,MAAM,GAAG5D,OAAO,CAAC4D,MAAM;MAAE3C,EAAE,GAAGjB,OAAO,CAACK,UAAU;MAAEA,UAAU,GAAGY,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEsC,gBAAgB,GAAGvD,OAAO,CAACuD,gBAAgB;MAAEM,cAAc,GAAG7D,OAAO,CAAC6D,cAAc;IAClL,IAAIC,YAAY;IAChB,IAAIC,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAC3B,IAAI/C,EAAE,GAAGjD,KAAK;QAAEgB,IAAI,GAAGiC,EAAE,CAACjC,IAAI;QAAEG,cAAc,GAAG8B,EAAE,CAAC9B,cAAc;MAClE,EAAEnB,KAAK,CAACM,OAAO;MACf,IAAI0F,KAAK,EAAE;QACPhG,KAAK,CAACgB,IAAI,GAAGhB,KAAK,CAACmB,cAAc,GAAG6E,KAAK;MAC7C;MACA,IAAI;QACA,OAAQF,YAAY,GAAGF,MAAM,CAAC5F,KAAK,CAAC;MACxC,CAAC,SACO;QACJ,EAAEA,KAAK,CAACM,OAAO;QACfN,KAAK,CAACgB,IAAI,GAAGA,IAAI;QACjBhB,KAAK,CAACmB,cAAc,GAAGA,cAAc;MACzC;IACJ,CAAC;IACD,IAAI8E,YAAY,GAAG,IAAI9F,GAAG,CAAC,CAAC;IAC5B,IAAI0F,cAAc,IAAI,CAAC,IAAI,CAACvF,OAAO,EAAE;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACmD,gBAAgB,CAACrF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4D,OAAO,CAAC,EAAE;QAAE6D,cAAc,EAAE,SAAAA,CAAU/B,KAAK,EAAE;UACjFmC,YAAY,CAACjC,GAAG,CAACF,KAAK,CAAC;UACvB,OAAO,KAAK;QAChB;MAAE,CAAC,CAAC,CAAC;IACb;IACA,IAAI,OAAOzB,UAAU,KAAK,QAAQ,EAAE;MAChC;MACA;MACA;MACA,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC+E,QAAQ,CAAC7D,UAAU,EAAE0D,OAAO,CAAC;IAC3E,CAAC,MACI,IAAI1D,UAAU,KAAK,KAAK,EAAE;MAC3B;MACA;MACA;MACA;MACA;MACA0D,OAAO,CAAC,IAAI,CAAC/E,IAAI,CAAC;IACtB,CAAC,MACI;MACD;MACA;MACA+E,OAAO,CAAC,CAAC;IACb;IACA,IAAI,OAAOR,gBAAgB,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACpE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACuE,WAAW,CAACH,gBAAgB,CAAC;IAC3E;IACA;IACA;IACA;IACA,IAAIM,cAAc,IAAII,YAAY,CAAClC,IAAI,EAAE;MACrC,IAAI,CAACN,gBAAgB,CAACrF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4D,OAAO,CAAC,EAAE;QAAE6D,cAAc,EAAE,SAAAA,CAAU/B,KAAK,EAAEF,IAAI,EAAE;UACvF,IAAIR,MAAM,GAAGyC,cAAc,CAAC5F,IAAI,CAAC,IAAI,EAAE6D,KAAK,EAAEF,IAAI,CAAC;UACnD,IAAIR,MAAM,KAAK,KAAK,EAAE;YAClB;YACA;YACA;YACA6C,YAAY,CAAC/B,MAAM,CAACJ,KAAK,CAAC;UAC9B;UACA,OAAOV,MAAM;QACjB;MAAE,CAAC,CAAC,CAAC;MACT;MACA;MACA,IAAI6C,YAAY,CAAClC,IAAI,EAAE;QACnBkC,YAAY,CAACtD,OAAO,CAAC,UAAUmB,KAAK,EAAE;UAAE,OAAO9D,KAAK,CAAC8B,mBAAmB,CAACqE,KAAK,CAACrC,KAAK,CAAC;QAAE,CAAC,CAAC;MAC7F;IACJ,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACL,gBAAgB,CAACzB,OAAO,CAAC;IAClC;IACA,OAAO8D,YAAY;EACvB,CAAC;EACDjG,aAAa,CAACiB,SAAS,CAACsF,kBAAkB,GAAG,UAAUR,MAAM,EAAES,YAAY,EAAE;IACzE,OAAO,IAAI,CAACV,KAAK,CAAC;MACdC,MAAM,EAAEA,MAAM;MACdvD,UAAU,EAAEgE,YAAY,IAAIA,YAAY,KAAK;IACjD,CAAC,CAAC;EACN,CAAC;EACDxG,aAAa,CAACiB,SAAS,CAACwF,iBAAiB,GAAG,UAAUC,QAAQ,EAAE;IAC5D,OAAO,IAAI,CAAC5H,qBAAqB,CAAC,IAAI,CAAC6H,sBAAsB,CAACD,QAAQ,CAAC,CAAC;EAC5E,CAAC;EACD1G,aAAa,CAACiB,SAAS,CAAC2F,eAAe,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;IACpE,OAAO,IAAI,CAACnG,QAAQ,CAACiG,eAAe,CAACC,QAAQ,EAAEC,QAAQ,CAAC;EAC5D,CAAC;EACD9G,aAAa,CAACiB,SAAS,CAAC8F,cAAc,GAAG,UAAUC,YAAY,EAAE;IAC7D,IAAI5D,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAG,IAAI,CAAClD,MAAM,CAAC0B,SAAS,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6D,MAAM,CAACD,YAAY,CAAC,KAAK,IAAI;EAC9G,CAAC;EACDhH,aAAa,CAACiB,SAAS,CAAC2C,gBAAgB,GAAG,UAAUzB,OAAO,EAAE;IAC1D,IAAIhC,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;MACf,IAAI,CAACJ,OAAO,CAACyC,OAAO,CAAC,UAAUZ,CAAC,EAAE;QAAE,OAAO/B,KAAK,CAAC8B,mBAAmB,CAACC,CAAC,EAAEC,OAAO,CAAC;MAAE,CAAC,CAAC;IACxF;EACJ,CAAC;EACDnC,aAAa,CAACiB,SAAS,CAAC0F,sBAAsB,GAAG,UAAUD,QAAQ,EAAE;IACjE,IAAI9E,SAAS,GAAG,IAAI,CAAC1B,MAAM,CAAC0B,SAAS;IACrC,OAAOA,SAAS,GAAGA,SAAS,CAACsF,SAAS,CAACR,QAAQ,CAAC,GAAGA,QAAQ;EAC/D,CAAC;EACD1G,aAAa,CAACiB,SAAS,CAACnC,qBAAqB,GAAG,UAAU4H,QAAQ,EAAE;IAChE,IAAI,IAAI,CAAChG,WAAW,EAAE;MAClB,OAAO,IAAI,CAACH,oBAAoB,CAACkG,iBAAiB,CAACC,QAAQ,CAAC;IAChE;IACA,OAAOA,QAAQ;EACnB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA1G,aAAa,CAACiB,SAAS,CAACmB,cAAc,GAAG,UAAUF,CAAC,EAAEC,OAAO,EAAE;IAC3D,IAAIgF,QAAQ,GAAGjF,CAAC,CAACiF,QAAQ;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIpD,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC7B,CAAC,CAAC;IACvB,IAAIC,OAAO,EAAE;MACT,IAAID,CAAC,CAACM,UAAU,IAAI,OAAOL,OAAO,CAACK,UAAU,KAAK,QAAQ,EAAE;QACxDuB,IAAI,CAACqD,yBAAyB,GAAG,IAAI;MACzC;MACA,IAAIjF,OAAO,CAAC6D,cAAc,IACtB7D,OAAO,CAAC6D,cAAc,CAAC5F,IAAI,CAAC,IAAI,EAAE8B,CAAC,EAAE6B,IAAI,EAAEoD,QAAQ,CAAC,KAAK,KAAK,EAAE;QAChE;QACA;QACA;MACJ;IACJ;IACA,IAAI,CAACA,QAAQ,IAAI,CAACxI,KAAK,CAACwI,QAAQ,CAAC5D,MAAM,EAAEQ,IAAI,CAACR,MAAM,CAAC,EAAE;MACnDrB,CAAC,CAACU,QAAQ,CAAEV,CAAC,CAACiF,QAAQ,GAAGpD,IAAI,EAAGoD,QAAQ,CAAC;IAC7C;EACJ,CAAC;EACD,OAAOnH,aAAa;AACxB,CAAC,CAACpB,WAAW,CAAE;AACf,SAASoB,aAAa;AACtB,IAAIkF,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;EAC9BnF,aAAa,CAACiB,SAAS,CAACoG,kBAAkB,GAAGtH,+BAA+B;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}