{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No fragment cycles\n *\n * The graph of fragment spreads must not form any cycles including spreading itself.\n * Otherwise an operation could infinitely spread or infinitely execute on cycles in the underlying data.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spreads-must-not-form-cycles\n */\nexport function NoFragmentCyclesRule(context) {\n  // Tracks already visited fragments to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedFrags = Object.create(null); // Array of AST nodes used to produce meaningful errors\n\n  const spreadPath = []; // Position in the spread path\n\n  const spreadPathIndexByName = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n    FragmentDefinition(node) {\n      detectCycleRecursive(node);\n      return false;\n    }\n  }; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(fragment) {\n    if (visitedFrags[fragment.name.value]) {\n      return;\n    }\n    const fragmentName = fragment.name.value;\n    visitedFrags[fragmentName] = true;\n    const spreadNodes = context.getFragmentSpreads(fragment.selectionSet);\n    if (spreadNodes.length === 0) {\n      return;\n    }\n    spreadPathIndexByName[fragmentName] = spreadPath.length;\n    for (const spreadNode of spreadNodes) {\n      const spreadName = spreadNode.name.value;\n      const cycleIndex = spreadPathIndexByName[spreadName];\n      spreadPath.push(spreadNode);\n      if (cycleIndex === undefined) {\n        const spreadFragment = context.getFragment(spreadName);\n        if (spreadFragment) {\n          detectCycleRecursive(spreadFragment);\n        }\n      } else {\n        const cyclePath = spreadPath.slice(cycleIndex);\n        const viaPath = cyclePath.slice(0, -1).map(s => '\"' + s.name.value + '\"').join(', ');\n        context.reportError(new GraphQLError(`Cannot spread fragment \"${spreadName}\" within itself` + (viaPath !== '' ? ` via ${viaPath}.` : '.'), {\n          nodes: cyclePath\n        }));\n      }\n      spreadPath.pop();\n    }\n    spreadPathIndexByName[fragmentName] = undefined;\n  }\n}", "map": {"version": 3, "names": ["GraphQLError", "NoFragmentCyclesRule", "context", "visitedFrags", "Object", "create", "spreadPath", "spreadPathIndexByName", "OperationDefinition", "FragmentDefinition", "node", "detectCycleRecursive", "fragment", "name", "value", "fragmentName", "spreadNodes", "getFragmentSpreads", "selectionSet", "length", "spreadNode", "spreadName", "cycleIndex", "push", "undefined", "spreadFragment", "getFragment", "cyclePath", "slice", "viaPath", "map", "s", "join", "reportError", "nodes", "pop"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/NoFragmentCyclesRule.mjs"], "sourcesContent": ["import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No fragment cycles\n *\n * The graph of fragment spreads must not form any cycles including spreading itself.\n * Otherwise an operation could infinitely spread or infinitely execute on cycles in the underlying data.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spreads-must-not-form-cycles\n */\nexport function NoFragmentCyclesRule(context) {\n  // Tracks already visited fragments to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedFrags = Object.create(null); // Array of AST nodes used to produce meaningful errors\n\n  const spreadPath = []; // Position in the spread path\n\n  const spreadPathIndexByName = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n\n    FragmentDefinition(node) {\n      detectCycleRecursive(node);\n      return false;\n    },\n  }; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(fragment) {\n    if (visitedFrags[fragment.name.value]) {\n      return;\n    }\n\n    const fragmentName = fragment.name.value;\n    visitedFrags[fragmentName] = true;\n    const spreadNodes = context.getFragmentSpreads(fragment.selectionSet);\n\n    if (spreadNodes.length === 0) {\n      return;\n    }\n\n    spreadPathIndexByName[fragmentName] = spreadPath.length;\n\n    for (const spreadNode of spreadNodes) {\n      const spreadName = spreadNode.name.value;\n      const cycleIndex = spreadPathIndexByName[spreadName];\n      spreadPath.push(spreadNode);\n\n      if (cycleIndex === undefined) {\n        const spreadFragment = context.getFragment(spreadName);\n\n        if (spreadFragment) {\n          detectCycleRecursive(spreadFragment);\n        }\n      } else {\n        const cyclePath = spreadPath.slice(cycleIndex);\n        const viaPath = cyclePath\n          .slice(0, -1)\n          .map((s) => '\"' + s.name.value + '\"')\n          .join(', ');\n        context.reportError(\n          new GraphQLError(\n            `Cannot spread fragment \"${spreadName}\" within itself` +\n              (viaPath !== '' ? ` via ${viaPath}.` : '.'),\n            {\n              nodes: cyclePath,\n            },\n          ),\n        );\n      }\n\n      spreadPath.pop();\n    }\n\n    spreadPathIndexByName[fragmentName] = undefined;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EAC5C;EACA;EACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1C,MAAMC,UAAU,GAAG,EAAE,CAAC,CAAC;;EAEvB,MAAMC,qBAAqB,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACjD,OAAO;IACLG,mBAAmB,EAAEA,CAAA,KAAM,KAAK;IAEhCC,kBAAkBA,CAACC,IAAI,EAAE;MACvBC,oBAAoB,CAACD,IAAI,CAAC;MAC1B,OAAO,KAAK;IACd;EACF,CAAC,CAAC,CAAC;EACH;EACA;;EAEA,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;IACtC,IAAIT,YAAY,CAACS,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE;MACrC;IACF;IAEA,MAAMC,YAAY,GAAGH,QAAQ,CAACC,IAAI,CAACC,KAAK;IACxCX,YAAY,CAACY,YAAY,CAAC,GAAG,IAAI;IACjC,MAAMC,WAAW,GAAGd,OAAO,CAACe,kBAAkB,CAACL,QAAQ,CAACM,YAAY,CAAC;IAErE,IAAIF,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;MAC5B;IACF;IAEAZ,qBAAqB,CAACQ,YAAY,CAAC,GAAGT,UAAU,CAACa,MAAM;IAEvD,KAAK,MAAMC,UAAU,IAAIJ,WAAW,EAAE;MACpC,MAAMK,UAAU,GAAGD,UAAU,CAACP,IAAI,CAACC,KAAK;MACxC,MAAMQ,UAAU,GAAGf,qBAAqB,CAACc,UAAU,CAAC;MACpDf,UAAU,CAACiB,IAAI,CAACH,UAAU,CAAC;MAE3B,IAAIE,UAAU,KAAKE,SAAS,EAAE;QAC5B,MAAMC,cAAc,GAAGvB,OAAO,CAACwB,WAAW,CAACL,UAAU,CAAC;QAEtD,IAAII,cAAc,EAAE;UAClBd,oBAAoB,CAACc,cAAc,CAAC;QACtC;MACF,CAAC,MAAM;QACL,MAAME,SAAS,GAAGrB,UAAU,CAACsB,KAAK,CAACN,UAAU,CAAC;QAC9C,MAAMO,OAAO,GAAGF,SAAS,CACtBC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACZE,GAAG,CAAEC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAAClB,IAAI,CAACC,KAAK,GAAG,GAAG,CAAC,CACpCkB,IAAI,CAAC,IAAI,CAAC;QACb9B,OAAO,CAAC+B,WAAW,CACjB,IAAIjC,YAAY,CACb,2BAA0BqB,UAAW,iBAAgB,IACnDQ,OAAO,KAAK,EAAE,GAAI,QAAOA,OAAQ,GAAE,GAAG,GAAG,CAAC,EAC7C;UACEK,KAAK,EAAEP;QACT,CACF,CACF,CAAC;MACH;MAEArB,UAAU,CAAC6B,GAAG,CAAC,CAAC;IAClB;IAEA5B,qBAAqB,CAACQ,YAAY,CAAC,GAAGS,SAAS;EACjD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}