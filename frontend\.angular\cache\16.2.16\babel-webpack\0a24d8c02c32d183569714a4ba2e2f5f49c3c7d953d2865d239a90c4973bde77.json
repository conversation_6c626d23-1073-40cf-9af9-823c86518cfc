{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/data.service\";\nimport * as i3 from \"@angular/common\";\nfunction UserdetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementStart(2, \"p\", 7);\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es utilisateur...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserdetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function UserdetailsComponent_div_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.messageSuccess = \"\");\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 10);\n    i0.ɵɵelement(5, \"path\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.messageSuccess);\n  }\n}\nfunction UserdetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.messageErr, \" \");\n  }\n}\nfunction UserdetailsComponent_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"img\", 60);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4, \"Voir\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r6.userObject.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserdetailsComponent_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r7.userObject.fullName ? ctx_r7.userObject.fullName.charAt(0) : \"\") || (ctx_r7.userObject.username ? ctx_r7.userObject.username.charAt(0) : \"\") || \"U\", \" \");\n  }\n}\nfunction UserdetailsComponent_div_4_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵtext(2, \" En ligne \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-[#dac4ea]/30 text-[#7826b5]\": a0,\n    \"bg-[#afcf75]/20 text-[#2a5a03]\": a1,\n    \"bg-[#4a89ce]/20 text-[#4f5fad]\": a2\n  };\n};\nfunction UserdetailsComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16)(3, \"div\")(4, \"h1\", 17);\n    i0.ɵɵtext(5, \" D\\u00E9tails de l'utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 18);\n    i0.ɵɵtext(7, \" Informations compl\\u00E8tes sur le profil \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 19)(9, \"span\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"div\", 22);\n    i0.ɵɵtemplate(13, UserdetailsComponent_div_4_div_13_Template, 5, 1, \"div\", 23);\n    i0.ɵɵtemplate(14, UserdetailsComponent_div_4_div_14_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"h2\", 26);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 27)(19, \"span\", 28);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 29);\n    i0.ɵɵelement(23, \"span\", 30);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, UserdetailsComponent_div_4_span_25_Template, 3, 0, \"span\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 32)(27, \"a\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 34);\n    i0.ɵɵelement(29, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"div\", 36)(32, \"div\", 37)(33, \"h3\", 38);\n    i0.ɵɵtext(34, \" Informations de base \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 16)(36, \"span\", 39);\n    i0.ɵɵtext(37, \"Nom d'utilisateur :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 40);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 16)(41, \"span\", 39);\n    i0.ɵɵtext(42, \"Nom complet :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 40);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 16)(46, \"span\", 39);\n    i0.ɵɵtext(47, \"Email :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\", 40);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 16)(51, \"span\", 39);\n    i0.ɵɵtext(52, \"R\\u00F4le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"span\", 40);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 16)(57, \"span\", 39);\n    i0.ɵɵtext(58, \"Groupe :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"span\", 40);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 16)(62, \"span\", 39);\n    i0.ɵɵtext(63, \"V\\u00E9rifi\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"span\", 41);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 37)(67, \"h3\", 38);\n    i0.ɵɵtext(68, \" Statut et dates \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 16)(70, \"span\", 39);\n    i0.ɵɵtext(71, \"Statut :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\", 41);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 16)(75, \"span\", 39);\n    i0.ɵɵtext(76, \"En ligne :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"span\", 41);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"div\", 16)(80, \"span\", 39);\n    i0.ɵɵtext(81, \"Derni\\u00E8re activit\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"span\", 40);\n    i0.ɵɵtext(83);\n    i0.ɵɵpipe(84, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 16)(86, \"span\", 39);\n    i0.ɵɵtext(87, \"Cr\\u00E9\\u00E9 le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\", 40);\n    i0.ɵɵtext(89);\n    i0.ɵɵpipe(90, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(91, \"div\", 16)(92, \"span\", 39);\n    i0.ɵɵtext(93, \"Mis \\u00E0 jour le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\", 40);\n    i0.ɵɵtext(95);\n    i0.ɵɵpipe(96, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(97, \"div\", 42)(98, \"details\", 43)(99, \"summary\", 44)(100, \"span\");\n    i0.ɵɵtext(101, \"D\\u00E9tails techniques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(102, \"svg\", 45);\n    i0.ɵɵelement(103, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(104, \"div\", 47)(105, \"div\", 48)(106, \"div\")(107, \"p\", 49);\n    i0.ɵɵtext(108, \" ID: \");\n    i0.ɵɵelementStart(109, \"span\", 40);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(111, \"p\", 49);\n    i0.ɵɵtext(112, \" Version: \");\n    i0.ɵɵelementStart(113, \"span\", 40);\n    i0.ɵɵtext(114);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\")(116, \"p\", 49);\n    i0.ɵɵtext(117, \" Image URL: \");\n    i0.ɵɵelementStart(118, \"span\", 50);\n    i0.ɵɵtext(119);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(120, \"p\", 49);\n    i0.ɵɵtext(121, \" Profile Image URL: \");\n    i0.ɵɵelementStart(122, \"span\", 50);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(124, \"div\", 51)(125, \"div\", 52)(126, \"span\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(127, \"svg\", 54);\n    i0.ɵɵelement(128, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(129);\n    i0.ɵɵpipe(130, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(131, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function UserdetailsComponent_div_4_Template_button_click_131_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.goBack());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(132, \"svg\", 57);\n    i0.ɵɵelement(133, \"path\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(134, \" Retour au tableau de bord \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" ID: \", ctx_r3.userObject._id, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userObject.image);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.userObject.image);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.fullName || ctx_r3.userObject.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(47, _c0, ctx_r3.userObject.role === \"admin\", ctx_r3.userObject.role === \"teacher\", ctx_r3.userObject.role === \"student\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(21, 31, ctx_r3.userObject.role), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"bg-[#afcf75]/20 text-[#2a5a03]\" : \"bg-[#ff6b69]/20 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"bg-[#2a5a03]\" : \"bg-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isActive ? \"Actif\" : \"Inactif\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userObject.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r3.userObject.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.email, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.fullName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 33, ctx_r3.userObject.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.group ? ctx_r3.userObject.group : \"Aucun groupe\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.verified ? \"text-[#2a5a03]\" : \"text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.verified ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"text-[#2a5a03]\" : \"text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isActive ? \"Actif\" : \"Inactif\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isOnline ? \"text-[#4a89ce]\" : \"text-[#6d6870]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isOnline ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(84, 35, ctx_r3.userObject.lastActive, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(90, 38, ctx_r3.userObject.createdAt, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(96, 41, ctx_r3.userObject.updatedAt, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject._id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.__v);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.image);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.profileImage);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Derni\\u00E8re activit\\u00E9: \", i0.ɵɵpipeBind2(130, 44, ctx_r3.userObject.lastActive, \"dd/MM/yyyy HH:mm\"), \" \");\n  }\n}\nexport class UserdetailsComponent {\n  constructor(route, ds, router) {\n    this.route = route;\n    this.ds = ds;\n    this.router = router;\n    this.messageErr = '';\n    this.messageSuccess = '';\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    const userId = this.route.snapshot.paramMap.get('id');\n    if (userId) {\n      this.loadUser(userId);\n    } else {\n      this.messageErr = 'ID utilisateur non valide';\n      this.isLoading = false;\n    }\n  }\n  loadUser(id) {\n    this.isLoading = true;\n    this.messageErr = '';\n    this.subscription = this.ds.getOneUser(id).subscribe({\n      next: response => {\n        this.userObject = response;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.messageErr = err.status === 404 ? 'Utilisateur non trouvé' : 'Erreur lors du chargement des données';\n        this.isLoading = false;\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/admin/dashboard']);\n  }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function UserdetailsComponent_Factory(t) {\n      return new (t || UserdetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserdetailsComponent,\n      selectors: [[\"app-userdetails\"]],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [\"class\", \"flex flex-col items-center justify-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-[#afcf75]/20 border border-[#afcf75] text-[#2a5a03] p-4 rounded-lg mb-6 flex justify-between items-center\", 4, \"ngIf\"], [\"class\", \"flex justify-center mb-6\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden max-w-5xl mx-auto\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"mt-4\", \"text-[#6d6870]\"], [1, \"bg-[#afcf75]/20\", \"border\", \"border-[#afcf75]\", \"text-[#2a5a03]\", \"p-4\", \"rounded-lg\", \"mb-6\", \"flex\", \"justify-between\", \"items-center\"], [1, \"text-[#2a5a03]\", \"hover:text-[#1a3a01]\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"justify-center\", \"mb-6\"], [1, \"bg-[#ff6b69]/20\", \"border\", \"border-[#ff6b69]\", \"text-[#ff6b69]\", \"p-4\", \"rounded-lg\", \"max-w-md\", \"w-full\", \"text-center\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\", \"max-w-5xl\", \"mx-auto\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white/80\", \"text-sm\"], [1, \"flex\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"rounded-full\", \"backdrop-blur-sm\", \"bg-white/20\", \"text-white\", \"font-medium\"], [1, \"px-6\", \"py-6\", \"flex\", \"items-center\", \"border-b\", \"border-[#edf1f4]\"], [1, \"flex-shrink-0\"], [\"class\", \"relative group\", 4, \"ngIf\"], [\"class\", \"h-24 w-24 rounded-full bg-gradient-to-br from-[#4f5fad] to-[#3d4a85] flex items-center justify-center text-white text-3xl font-bold\", 4, \"ngIf\"], [1, \"ml-6\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\"], [1, \"flex\", \"items-center\", \"mt-2\", \"space-x-3\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"font-medium\", 3, \"ngClass\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"font-medium\", \"flex\", \"items-center\", 3, \"ngClass\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"mr-2\", 3, \"ngClass\"], [\"class\", \"px-3 py-1 text-sm rounded-full bg-[#4a89ce]/20 text-[#4a89ce] font-medium flex items-center\", 4, \"ngIf\"], [1, \"mt-2\"], [1, \"text-[#4a89ce]\", \"hover:text-[#7826b5]\", \"transition-colors\", \"flex\", \"items-center\", 3, \"href\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"], [1, \"p-6\", \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-x-8\", \"gap-y-4\"], [1, \"space-y-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"mb-4\", \"pb-2\", \"border-b\", \"border-[#edf1f4]\"], [1, \"font-medium\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\"], [3, \"ngClass\"], [1, \"px-6\", \"pb-6\"], [1, \"group\"], [1, \"flex\", \"justify-between\", \"items-center\", \"cursor-pointer\", \"text-[#3d4a85]\", \"font-medium\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"transform\", \"group-open:rotate-180\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"mt-4\", \"p-4\", \"bg-[#f8f9fa]\", \"rounded-lg\", \"text-sm\", \"font-mono\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\", \"break-all\"], [1, \"p-6\", \"border-t\", \"border-[#edf1f4]\", \"flex\", \"justify-between\", \"items-center\", \"bg-[#f8f9fa]\"], [1, \"flex\", \"items-center\"], [1, \"text-sm\", \"text-[#6d6870]\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"px-4\", \"py-2\", \"text-sm\", \"rounded-lg\", \"bg-[#4f5fad]\", \"text-white\", \"hover:bg-[#3d4a85]\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [1, \"relative\", \"group\"], [\"alt\", \"Profile\", 1, \"h-24\", \"w-24\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"bg-black/40\", \"opacity-0\", \"group-hover:opacity-100\", \"flex\", \"items-center\", \"justify-center\", \"transition-opacity\"], [1, \"text-white\", \"text-xs\"], [1, \"h-24\", \"w-24\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#3d4a85]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-3xl\", \"font-bold\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"bg-[#4a89ce]/20\", \"text-[#4a89ce]\", \"font-medium\", \"flex\", \"items-center\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4a89ce]\", \"mr-2\", \"animate-pulse\"]],\n      template: function UserdetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, UserdetailsComponent_div_1_Template, 4, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, UserdetailsComponent_div_2_Template, 6, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, UserdetailsComponent_div_3_Template, 3, 1, \"div\", 3);\n          i0.ɵɵtemplate(4, UserdetailsComponent_div_4_Template, 135, 51, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageSuccess);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageErr && !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userObject && !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf, i3.TitleCasePipe, i3.DatePipe],\n      styles: [\".loading-spinner[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 50%;\\n    left: 50%;\\n    transform: translate(-50%, -50%);\\n    z-index: 9999;\\n  }\\n  \\n  .spinner-border[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInVzZXJkZXRhaWxzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxlQUFlO0lBQ2YsUUFBUTtJQUNSLFNBQVM7SUFDVCxnQ0FBZ0M7SUFDaEMsYUFBYTtFQUNmOztFQUVBO0lBQ0UsV0FBVztJQUNYLFlBQVk7RUFDZCIsImZpbGUiOiJ1c2VyZGV0YWlscy5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDUwJTtcclxuICAgIGxlZnQ6IDUwJTtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpO1xyXG4gICAgei1pbmRleDogOTk5OTtcclxuICB9XHJcbiAgXHJcbiAgLnNwaW5uZXItYm9yZGVyIHtcclxuICAgIHdpZHRoOiAzcmVtO1xyXG4gICAgaGVpZ2h0OiAzcmVtO1xyXG4gIH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vdXNlcmRldGFpbHMvdXNlcmRldGFpbHMuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGVBQWU7SUFDZixRQUFRO0lBQ1IsU0FBUztJQUNULGdDQUFnQztJQUNoQyxhQUFhO0VBQ2Y7O0VBRUE7SUFDRSxXQUFXO0lBQ1gsWUFBWTtFQUNkO0FBQ0YsZ3BCQUFncEIiLCJzb3VyY2VzQ29udGVudCI6WyIubG9hZGluZy1zcGlubmVyIHtcclxuICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgIHRvcDogNTAlO1xyXG4gICAgbGVmdDogNTAlO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XHJcbiAgICB6LWluZGV4OiA5OTk5O1xyXG4gIH1cclxuICBcclxuICAuc3Bpbm5lci1ib3JkZXIge1xyXG4gICAgd2lkdGg6IDNyZW07XHJcbiAgICBoZWlnaHQ6IDNyZW07XHJcbiAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UserdetailsComponent_div_2_Template_button_click_3_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "messageSuccess", "ɵɵnamespaceSVG", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "ɵɵtextInterpolate1", "ctx_r2", "messageErr", "ɵɵproperty", "ctx_r6", "userObject", "image", "ɵɵsanitizeUrl", "ctx_r7", "fullName", "char<PERSON>t", "username", "ɵɵtemplate", "UserdetailsComponent_div_4_div_13_Template", "UserdetailsComponent_div_4_div_14_Template", "UserdetailsComponent_div_4_span_25_Template", "ɵɵnamespaceHTML", "UserdetailsComponent_div_4_Template_button_click_131_listener", "_r10", "ctx_r9", "goBack", "ctx_r3", "_id", "ɵɵpureFunction3", "_c0", "role", "ɵɵpipeBind1", "isActive", "isOnline", "ɵɵpropertyInterpolate1", "email", "group", "verified", "ɵɵpipeBind2", "lastActive", "createdAt", "updatedAt", "__v", "profileImage", "UserdetailsComponent", "constructor", "route", "ds", "router", "isLoading", "ngOnInit", "userId", "snapshot", "paramMap", "get", "loadUser", "id", "subscription", "getOneUser", "subscribe", "next", "response", "error", "err", "status", "navigate", "ngOnDestroy", "unsubscribe", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "DataService", "Router", "selectors", "decls", "vars", "consts", "template", "UserdetailsComponent_Template", "rf", "ctx", "UserdetailsComponent_div_1_Template", "UserdetailsComponent_div_2_Template", "UserdetailsComponent_div_3_Template", "UserdetailsComponent_div_4_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\userdetails\\userdetails.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\userdetails\\userdetails.component.html"], "sourcesContent": ["import { HttpErrorResponse } from '@angular/common/http';\r\nimport { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { Subscription } from 'rxjs';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-userdetails',\r\n  templateUrl: './userdetails.component.html',\r\n  styleUrls: ['./userdetails.component.css'],\r\n})\r\nexport class UserdetailsComponent implements OnInit, OnDestroy {\r\n  userObject?: User;\r\n  messageErr: string = '';\r\n  messageSuccess: string = '';\r\n  isLoading: boolean = true;\r\n  private subscription?: Subscription;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private ds: DataService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const userId = this.route.snapshot.paramMap.get('id');\r\n    if (userId) {\r\n      this.loadUser(userId);\r\n    } else {\r\n      this.messageErr = 'ID utilisateur non valide';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadUser(id: string): void {\r\n    this.isLoading = true;\r\n    this.messageErr = '';\r\n    this.subscription = this.ds.getOneUser(id).subscribe({\r\n      next: (response: User) => {\r\n        this.userObject = response;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: HttpErrorResponse) => {\r\n        this.messageErr =\r\n          err.status === 404\r\n            ? 'Utilisateur non trouvé'\r\n            : 'Erreur lors du chargement des données';\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n  goBack(): void {\r\n    this.router.navigate(['/admin/dashboard']);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.subscription) {\r\n      this.subscription.unsubscribe();\r\n    }\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] min-h-screen\">\r\n  <!-- Loading Spinner -->\r\n  <div\r\n    *ngIf=\"isLoading\"\r\n    class=\"flex flex-col items-center justify-center py-12\"\r\n  >\r\n    <div\r\n      class=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad]\"\r\n    ></div>\r\n    <p class=\"mt-4 text-[#6d6870]\">Chargement des données utilisateur...</p>\r\n  </div>\r\n\r\n  <!-- Success Message -->\r\n  <div\r\n    *ngIf=\"messageSuccess\"\r\n    class=\"bg-[#afcf75]/20 border border-[#afcf75] text-[#2a5a03] p-4 rounded-lg mb-6 flex justify-between items-center\"\r\n  >\r\n    <span>{{ messageSuccess }}</span>\r\n    <button\r\n      (click)=\"messageSuccess = ''\"\r\n      class=\"text-[#2a5a03] hover:text-[#1a3a01]\"\r\n    >\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        class=\"h-5 w-5\"\r\n        viewBox=\"0 0 20 20\"\r\n        fill=\"currentColor\"\r\n      >\r\n        <path\r\n          fill-rule=\"evenodd\"\r\n          d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\r\n          clip-rule=\"evenodd\"\r\n        />\r\n      </svg>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div *ngIf=\"messageErr && !isLoading\" class=\"flex justify-center mb-6\">\r\n    <div\r\n      class=\"bg-[#ff6b69]/20 border border-[#ff6b69] text-[#ff6b69] p-4 rounded-lg max-w-md w-full text-center\"\r\n    >\r\n      {{ messageErr }}\r\n    </div>\r\n  </div>\r\n\r\n  <!-- User Details Card -->\r\n  <div\r\n    *ngIf=\"userObject && !isLoading\"\r\n    class=\"bg-white rounded-xl shadow-md overflow-hidden max-w-5xl mx-auto\"\r\n  >\r\n    <!-- Header with gradient -->\r\n    <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] p-6\">\r\n      <div class=\"flex justify-between items-center\">\r\n        <div>\r\n          <h1 class=\"text-2xl font-bold text-white\">\r\n            Détails de l'utilisateur\r\n          </h1>\r\n          <p class=\"text-white/80 text-sm\">\r\n            Informations complètes sur le profil\r\n          </p>\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <span\r\n            class=\"px-3 py-1 text-xs rounded-full backdrop-blur-sm bg-white/20 text-white font-medium\"\r\n          >\r\n            ID: {{ userObject._id }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Profile Header -->\r\n    <div class=\"px-6 py-6 flex items-center border-b border-[#edf1f4]\">\r\n      <div class=\"flex-shrink-0\">\r\n        <div *ngIf=\"userObject.image\" class=\"relative group\">\r\n          <img\r\n            [src]=\"userObject.image\"\r\n            alt=\"Profile\"\r\n            class=\"h-24 w-24 rounded-full object-cover border-2 border-[#4f5fad]\"\r\n          />\r\n          <div\r\n            class=\"absolute inset-0 rounded-full bg-black/40 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity\"\r\n          >\r\n            <span class=\"text-white text-xs\">Voir</span>\r\n          </div>\r\n        </div>\r\n        <div\r\n          *ngIf=\"!userObject.image\"\r\n          class=\"h-24 w-24 rounded-full bg-gradient-to-br from-[#4f5fad] to-[#3d4a85] flex items-center justify-center text-white text-3xl font-bold\"\r\n        >\r\n          {{\r\n            (userObject.fullName ? userObject.fullName.charAt(0) : \"\") ||\r\n              (userObject.username ? userObject.username.charAt(0) : \"\") ||\r\n              \"U\"\r\n          }}\r\n        </div>\r\n      </div>\r\n      <div class=\"ml-6\">\r\n        <h2 class=\"text-2xl font-bold text-[#3d4a85]\">\r\n          {{ userObject.fullName || userObject.username }}\r\n        </h2>\r\n        <div class=\"flex items-center mt-2 space-x-3\">\r\n          <span\r\n            class=\"px-3 py-1 text-sm rounded-full font-medium\"\r\n            [ngClass]=\"{\r\n              'bg-[#dac4ea]/30 text-[#7826b5]': userObject.role === 'admin',\r\n              'bg-[#afcf75]/20 text-[#2a5a03]': userObject.role === 'teacher',\r\n              'bg-[#4a89ce]/20 text-[#4f5fad]': userObject.role === 'student'\r\n            }\"\r\n          >\r\n            {{ userObject.role | titlecase }}\r\n          </span>\r\n          <span\r\n            class=\"px-3 py-1 text-sm rounded-full font-medium flex items-center\"\r\n            [ngClass]=\"\r\n              userObject.isActive\r\n                ? 'bg-[#afcf75]/20 text-[#2a5a03]'\r\n                : 'bg-[#ff6b69]/20 text-[#ff6b69]'\r\n            \"\r\n          >\r\n            <span\r\n              class=\"w-2 h-2 rounded-full mr-2\"\r\n              [ngClass]=\"userObject.isActive ? 'bg-[#2a5a03]' : 'bg-[#ff6b69]'\"\r\n            ></span>\r\n            {{ userObject.isActive ? \"Actif\" : \"Inactif\" }}\r\n          </span>\r\n          <span\r\n            *ngIf=\"userObject.isOnline\"\r\n            class=\"px-3 py-1 text-sm rounded-full bg-[#4a89ce]/20 text-[#4a89ce] font-medium flex items-center\"\r\n          >\r\n            <span\r\n              class=\"w-2 h-2 rounded-full bg-[#4a89ce] mr-2 animate-pulse\"\r\n            ></span>\r\n            En ligne\r\n          </span>\r\n        </div>\r\n        <div class=\"mt-2\">\r\n          <a\r\n            href=\"mailto:{{ userObject.email }}\"\r\n            class=\"text-[#4a89ce] hover:text-[#7826b5] transition-colors flex items-center\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\r\n              />\r\n            </svg>\r\n            {{ userObject.email }}\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Details Grid -->\r\n    <div class=\"p-6 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4\">\r\n      <!-- Column 1: Basic Info -->\r\n      <div class=\"space-y-4\">\r\n        <h3\r\n          class=\"text-lg font-semibold text-[#3d4a85] mb-4 pb-2 border-b border-[#edf1f4]\"\r\n        >\r\n          Informations de base\r\n        </h3>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Nom d'utilisateur :</span>\r\n          <span class=\"text-[#4f5fad]\">{{ userObject.username }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Nom complet :</span>\r\n          <span class=\"text-[#4f5fad]\">{{ userObject.fullName }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Email :</span>\r\n          <span class=\"text-[#4f5fad]\">{{ userObject.email }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Rôle :</span>\r\n          <span class=\"text-[#4f5fad]\">{{ userObject.role | titlecase }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Groupe :</span>\r\n          <span class=\"text-[#4f5fad]\">{{\r\n            userObject.group ? userObject.group : \"Aucun groupe\"\r\n          }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Vérifié :</span>\r\n          <span\r\n            [ngClass]=\"\r\n              userObject.verified ? 'text-[#2a5a03]' : 'text-[#ff6b69]'\r\n            \"\r\n          >\r\n            {{ userObject.verified ? \"Oui\" : \"Non\" }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Column 2: Status & Dates -->\r\n      <div class=\"space-y-4\">\r\n        <h3\r\n          class=\"text-lg font-semibold text-[#3d4a85] mb-4 pb-2 border-b border-[#edf1f4]\"\r\n        >\r\n          Statut et dates\r\n        </h3>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Statut :</span>\r\n          <span\r\n            [ngClass]=\"\r\n              userObject.isActive ? 'text-[#2a5a03]' : 'text-[#ff6b69]'\r\n            \"\r\n          >\r\n            {{ userObject.isActive ? \"Actif\" : \"Inactif\" }}\r\n          </span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">En ligne :</span>\r\n          <span\r\n            [ngClass]=\"\r\n              userObject.isOnline ? 'text-[#4a89ce]' : 'text-[#6d6870]'\r\n            \"\r\n          >\r\n            {{ userObject.isOnline ? \"Oui\" : \"Non\" }}\r\n          </span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Dernière activité :</span>\r\n          <span class=\"text-[#4f5fad]\">{{\r\n            userObject.lastActive | date : \"dd/MM/yyyy HH:mm\"\r\n          }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Créé le :</span>\r\n          <span class=\"text-[#4f5fad]\">{{\r\n            userObject.createdAt | date : \"dd/MM/yyyy HH:mm\"\r\n          }}</span>\r\n        </div>\r\n\r\n        <div class=\"flex justify-between items-center\">\r\n          <span class=\"font-medium text-[#6d6870]\">Mis à jour le :</span>\r\n          <span class=\"text-[#4f5fad]\">{{\r\n            userObject.updatedAt | date : \"dd/MM/yyyy HH:mm\"\r\n          }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Technical Details (Collapsible) -->\r\n    <div class=\"px-6 pb-6\">\r\n      <details class=\"group\">\r\n        <summary\r\n          class=\"flex justify-between items-center cursor-pointer text-[#3d4a85] font-medium\"\r\n        >\r\n          <span>Détails techniques</span>\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            class=\"h-5 w-5 transform group-open:rotate-180 transition-transform\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke=\"currentColor\"\r\n          >\r\n            <path\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n              stroke-width=\"2\"\r\n              d=\"M19 9l-7 7-7-7\"\r\n            />\r\n          </svg>\r\n        </summary>\r\n        <div class=\"mt-4 p-4 bg-[#f8f9fa] rounded-lg text-sm font-mono\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div>\r\n              <p class=\"text-[#6d6870]\">\r\n                ID: <span class=\"text-[#4f5fad]\">{{ userObject._id }}</span>\r\n              </p>\r\n              <p class=\"text-[#6d6870]\">\r\n                Version:\r\n                <span class=\"text-[#4f5fad]\">{{ userObject.__v }}</span>\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <p class=\"text-[#6d6870]\">\r\n                Image URL:\r\n                <span class=\"text-[#4f5fad] break-all\">{{\r\n                  userObject.image\r\n                }}</span>\r\n              </p>\r\n              <p class=\"text-[#6d6870]\">\r\n                Profile Image URL:\r\n                <span class=\"text-[#4f5fad] break-all\">{{\r\n                  userObject.profileImage\r\n                }}</span>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </details>\r\n    </div>\r\n\r\n    <!-- Footer with actions -->\r\n    <div\r\n      class=\"p-6 border-t border-[#edf1f4] flex justify-between items-center bg-[#f8f9fa]\"\r\n    >\r\n      <div class=\"flex items-center\">\r\n        <span class=\"text-sm text-[#6d6870]\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            class=\"h-4 w-4 inline mr-1\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke=\"currentColor\"\r\n          >\r\n            <path\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n              stroke-width=\"2\"\r\n              d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n            />\r\n          </svg>\r\n          Dernière activité:\r\n          {{ userObject.lastActive | date : \"dd/MM/yyyy HH:mm\" }}\r\n        </span>\r\n      </div>\r\n\r\n      <!-- Bouton Retour -->\r\n      <button\r\n        (click)=\"goBack()\"\r\n        class=\"px-4 py-2 text-sm rounded-lg bg-[#4f5fad] text-white hover:bg-[#3d4a85] font-medium flex items-center transition-colors\"\r\n      >\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          class=\"h-5 w-5 mr-2\"\r\n          viewBox=\"0 0 20 20\"\r\n          fill=\"currentColor\"\r\n        >\r\n          <path\r\n            fill-rule=\"evenodd\"\r\n            d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\"\r\n            clip-rule=\"evenodd\"\r\n          />\r\n        </svg>\r\n        Retour au tableau de bord\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;ICGEA,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAAE,SAAA,aAEO;IACPF,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAG,MAAA,iDAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAI1EJ,EAAA,CAAAC,cAAA,aAGC;IACOD,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjCJ,EAAA,CAAAC,cAAA,gBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,4DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,cAAA,GAA0B,EAAE;IAAA,EAAC;IAG7BZ,EAAA,CAAAa,cAAA,EAKC;IALDb,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,eAIE;IACJF,EAAA,CAAAI,YAAA,EAAM;;;;IAhBFJ,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAJ,cAAA,CAAoB;;;;;IAqB5BZ,EAAA,CAAAC,cAAA,cAAuE;IAInED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAC,MAAA,CAAAC,UAAA,MACF;;;;;IAgCInB,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,SAAA,cAIE;IACFF,EAAA,CAAAC,cAAA,cAEC;IACkCD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAP5CJ,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAoB,UAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,KAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAwB;;;;;IAU5BxB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IALJJ,EAAA,CAAAc,SAAA,GAKF;IALEd,EAAA,CAAAiB,kBAAA,OAAAQ,MAAA,CAAAH,UAAA,CAAAI,QAAA,GAAAD,MAAA,CAAAH,UAAA,CAAAI,QAAA,CAAAC,MAAA,cAAAF,MAAA,CAAAH,UAAA,CAAAM,QAAA,GAAAH,MAAA,CAAAH,UAAA,CAAAM,QAAA,CAAAD,MAAA,sBAKF;;;;;IA+BE3B,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,SAAA,eAEQ;IACRF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;;;;;;;;IAxFfJ,EAAA,CAAAC,cAAA,cAGC;IAMSD,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAG,MAAA,kDACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAC,cAAA,cAA4B;IAIxBD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAMbJ,EAAA,CAAAC,cAAA,eAAmE;IAE/DD,EAAA,CAAA6B,UAAA,KAAAC,0CAAA,kBAWM;IACN9B,EAAA,CAAA6B,UAAA,KAAAE,0CAAA,kBASM;IACR/B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,eAA8C;IAS1CD,EAAA,CAAAG,MAAA,IACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAOC;IACCD,EAAA,CAAAE,SAAA,gBAGQ;IACRF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAA6B,UAAA,KAAAG,2CAAA,mBAQO;IACThC,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAkB;IAKdD,EAAA,CAAAa,cAAA,EAMC;IANDb,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMVJ,EAAA,CAAAiC,eAAA,EAAiE;IAAjEjC,EAAA,CAAAC,cAAA,eAAiE;IAM3DD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG/DJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7DJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG/DJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG5DJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAiC;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGvEJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAE3B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGXJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,2BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAC,cAAA,gBAIC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKXJ,EAAA,CAAAC,cAAA,eAAuB;IAInBD,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxDJ,EAAA,CAAAC,cAAA,gBAIC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGTJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC1DJ,EAAA,CAAAC,cAAA,gBAIC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGTJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,qCAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAE3B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGXJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,2BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAE3B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGXJ,EAAA,CAAAC,cAAA,eAA+C;IACJD,EAAA,CAAAG,MAAA,4BAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/DJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAE3B;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAMfJ,EAAA,CAAAC,cAAA,eAAuB;IAKXD,EAAA,CAAAG,MAAA,gCAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAa,cAAA,EAMC;IANDb,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAiC,eAAA,EAAgE;IAAhEjC,EAAA,CAAAC,cAAA,gBAAgE;IAIxDD,EAAA,CAAAG,MAAA,cAAI;IAAAH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAG,MAAA,KAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9DJ,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAG,MAAA,mBACA;IAAAH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAG,MAAA,KAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG5DJ,EAAA,CAAAC,cAAA,YAAK;IAEDD,EAAA,CAAAG,MAAA,qBACA;IAAAH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAG,MAAA,KAErC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEXJ,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAG,MAAA,6BACA;IAAAH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAG,MAAA,KAErC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IASrBJ,EAAA,CAAAC,cAAA,gBAEC;IAGKD,EAAA,CAAAa,cAAA,EAMC;IANDb,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,KAEF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAITJ,EAAA,CAAAiC,eAAA,EAGC;IAHDjC,EAAA,CAAAC,cAAA,mBAGC;IAFCD,EAAA,CAAAK,UAAA,mBAAA6B,8DAAA;MAAAlC,EAAA,CAAAO,aAAA,CAAA4B,IAAA;MAAA,MAAAC,MAAA,GAAApC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAyB,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBrC,EAAA,CAAAa,cAAA,EAKC;IALDb,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAE,SAAA,iBAIE;IACJF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IArSHJ,EAAA,CAAAc,SAAA,IACF;IADEd,EAAA,CAAAiB,kBAAA,UAAAqB,MAAA,CAAAhB,UAAA,CAAAiB,GAAA,MACF;IAQIvC,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAoB,UAAA,SAAAkB,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAAsB;IAazBvB,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAoB,UAAA,UAAAkB,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAAuB;IAYxBvB,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAAI,QAAA,IAAAY,MAAA,CAAAhB,UAAA,CAAAM,QAAA,MACF;IAII5B,EAAA,CAAAc,SAAA,GAIE;IAJFd,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAwC,eAAA,KAAAC,GAAA,EAAAH,MAAA,CAAAhB,UAAA,CAAAoB,IAAA,cAAAJ,MAAA,CAAAhB,UAAA,CAAAoB,IAAA,gBAAAJ,MAAA,CAAAhB,UAAA,CAAAoB,IAAA,gBAIE;IAEF1C,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAA2C,WAAA,SAAAL,MAAA,CAAAhB,UAAA,CAAAoB,IAAA,OACF;IAGE1C,EAAA,CAAAc,SAAA,GAIC;IAJDd,EAAA,CAAAoB,UAAA,YAAAkB,MAAA,CAAAhB,UAAA,CAAAsB,QAAA,uEAIC;IAIC5C,EAAA,CAAAc,SAAA,GAAiE;IAAjEd,EAAA,CAAAoB,UAAA,YAAAkB,MAAA,CAAAhB,UAAA,CAAAsB,QAAA,mCAAiE;IAEnE5C,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAAsB,QAAA,4BACF;IAEG5C,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAoB,UAAA,SAAAkB,MAAA,CAAAhB,UAAA,CAAAuB,QAAA,CAAyB;IAW1B7C,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAA8C,sBAAA,oBAAAR,MAAA,CAAAhB,UAAA,CAAAyB,KAAA,MAAA/C,EAAA,CAAAwB,aAAA,CAAoC;IAiBpCxB,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAAyB,KAAA,MACF;IAiB6B/C,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAM,QAAA,CAAyB;IAKzB5B,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAI,QAAA,CAAyB;IAKzB1B,EAAA,CAAAc,SAAA,GAAsB;IAAtBd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAyB,KAAA,CAAsB;IAKtB/C,EAAA,CAAAc,SAAA,GAAiC;IAAjCd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAA2C,WAAA,SAAAL,MAAA,CAAAhB,UAAA,CAAAoB,IAAA,EAAiC;IAKjC1C,EAAA,CAAAc,SAAA,GAE3B;IAF2Bd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAA0B,KAAA,GAAAV,MAAA,CAAAhB,UAAA,CAAA0B,KAAA,kBAE3B;IAMAhD,EAAA,CAAAc,SAAA,GAEC;IAFDd,EAAA,CAAAoB,UAAA,YAAAkB,MAAA,CAAAhB,UAAA,CAAA2B,QAAA,uCAEC;IAEDjD,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAA2B,QAAA,sBACF;IAeEjD,EAAA,CAAAc,SAAA,GAEC;IAFDd,EAAA,CAAAoB,UAAA,YAAAkB,MAAA,CAAAhB,UAAA,CAAAsB,QAAA,uCAEC;IAED5C,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAAsB,QAAA,4BACF;IAME5C,EAAA,CAAAc,SAAA,GAEC;IAFDd,EAAA,CAAAoB,UAAA,YAAAkB,MAAA,CAAAhB,UAAA,CAAAuB,QAAA,uCAEC;IAED7C,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAqB,MAAA,CAAAhB,UAAA,CAAAuB,QAAA,sBACF;IAK6B7C,EAAA,CAAAc,SAAA,GAE3B;IAF2Bd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAkD,WAAA,SAAAZ,MAAA,CAAAhB,UAAA,CAAA6B,UAAA,sBAE3B;IAK2BnD,EAAA,CAAAc,SAAA,GAE3B;IAF2Bd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAkD,WAAA,SAAAZ,MAAA,CAAAhB,UAAA,CAAA8B,SAAA,sBAE3B;IAK2BpD,EAAA,CAAAc,SAAA,GAE3B;IAF2Bd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAkD,WAAA,SAAAZ,MAAA,CAAAhB,UAAA,CAAA+B,SAAA,sBAE3B;IA+BqCrD,EAAA,CAAAc,SAAA,IAAoB;IAApBd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAiB,GAAA,CAAoB;IAIxBvC,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAgC,GAAA,CAAoB;IAMVtD,EAAA,CAAAc,SAAA,GAErC;IAFqCd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAC,KAAA,CAErC;IAIqCvB,EAAA,CAAAc,SAAA,GAErC;IAFqCd,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAAhB,UAAA,CAAAiC,YAAA,CAErC;IA4BRvD,EAAA,CAAAc,SAAA,GAEF;IAFEd,EAAA,CAAAiB,kBAAA,mCAAAjB,EAAA,CAAAkD,WAAA,UAAAZ,MAAA,CAAAhB,UAAA,CAAA6B,UAAA,2BAEF;;;ADvUR,OAAM,MAAOK,oBAAoB;EAO/BC,YACUC,KAAqB,EACrBC,EAAe,EACfC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAzC,UAAU,GAAW,EAAE;IACvB,KAAAP,cAAc,GAAW,EAAE;IAC3B,KAAAiD,SAAS,GAAY,IAAI;EAOtB;EAEHC,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACrD,IAAIH,MAAM,EAAE;MACV,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;KACtB,MAAM;MACL,IAAI,CAAC5C,UAAU,GAAG,2BAA2B;MAC7C,IAAI,CAAC0C,SAAS,GAAG,KAAK;;EAE1B;EAEAM,QAAQA,CAACC,EAAU;IACjB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC1C,UAAU,GAAG,EAAE;IACpB,IAAI,CAACkD,YAAY,GAAG,IAAI,CAACV,EAAE,CAACW,UAAU,CAACF,EAAE,CAAC,CAACG,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAc,IAAI;QACvB,IAAI,CAACnD,UAAU,GAAGmD,QAAQ;QAC1B,IAAI,CAACZ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDa,KAAK,EAAGC,GAAsB,IAAI;QAChC,IAAI,CAACxD,UAAU,GACbwD,GAAG,CAACC,MAAM,KAAK,GAAG,GACd,wBAAwB,GACxB,uCAAuC;QAC7C,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EACAxB,MAAMA,CAAA;IACJ,IAAI,CAACuB,MAAM,CAACiB,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACU,WAAW,EAAE;;EAEnC;;;uBAhDWvB,oBAAoB,EAAAxD,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApF,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAApB7B,oBAAoB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXjC5F,EAAA,CAAAC,cAAA,aAAkE;UAEhED,EAAA,CAAA6B,UAAA,IAAAiE,mCAAA,iBAQM;UAGN9F,EAAA,CAAA6B,UAAA,IAAAkE,mCAAA,iBAsBM;UAGN/F,EAAA,CAAA6B,UAAA,IAAAmE,mCAAA,iBAMM;UAGNhG,EAAA,CAAA6B,UAAA,IAAAoE,mCAAA,oBA0TM;UACRjG,EAAA,CAAAI,YAAA,EAAM;;;UAvWDJ,EAAA,CAAAc,SAAA,GAAe;UAAfd,EAAA,CAAAoB,UAAA,SAAAyE,GAAA,CAAAhC,SAAA,CAAe;UAWf7D,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAAoB,UAAA,SAAAyE,GAAA,CAAAjF,cAAA,CAAoB;UAwBjBZ,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAoB,UAAA,SAAAyE,GAAA,CAAA1E,UAAA,KAAA0E,GAAA,CAAAhC,SAAA,CAA8B;UAUjC7D,EAAA,CAAAc,SAAA,GAA8B;UAA9Bd,EAAA,CAAAoB,UAAA,SAAAyE,GAAA,CAAAvE,UAAA,KAAAuE,GAAA,CAAAhC,SAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}