{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authuser.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nfunction HomeComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Project Management \");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" Made Simple \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1, \"(Administrator)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵtemplate(1, HomeComponent_ng_template_6_span_1_Template, 2, 0, \"span\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵtextInterpolate1(\" Welcome Back, \", (tmp_0_0 = ctx_r2.authService.getCurrentUser()) == null ? null : tmp_0_0.username, \"! \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n  }\n}\nfunction HomeComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Streamline your workflow, collaborate with your team, and deliver projects on time with DevBridge. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" You have \");\n    i0.ɵɵelementStart(1, \"strong\");\n    i0.ɵɵtext(2, \"3 active projects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" with \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"5 pending tasks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \". \");\n  }\n}\nfunction HomeComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 39);\n    i0.ɵɵtext(2, \" Get Started \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 40);\n    i0.ɵɵtext(4, \" Learn More \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_14_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵtext(1, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, HomeComponent_ng_template_14_a_0_Template, 2, 0, \"a\", 41);\n    i0.ɵɵelementStart(1, \"a\", 42);\n    i0.ɵɵtext(2, \" View Projects \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isAdmin());\n  }\n}\nfunction HomeComponent_section_46_div_7_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const member_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"src\", member_r19.avatar, i0.ɵɵsanitizeUrl)(\"alt\", member_r19.name);\n  }\n}\nfunction HomeComponent_section_46_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h3\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56);\n    i0.ɵɵtemplate(10, HomeComponent_section_46_div_7_img_10_Template, 1, 2, \"img\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 58);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r17 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", project_r17.team);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.dueDate);\n  }\n}\nfunction HomeComponent_section_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 44)(1, \"div\", 45)(2, \"h2\", 46);\n    i0.ɵɵtext(3, \"Recent Activity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 47);\n    i0.ɵɵtext(5, \"Here's what's been happening with your projects.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtemplate(7, HomeComponent_section_46_div_7_Template, 13, 5, \"div\", 49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.recentProjects);\n  }\n}\nfunction HomeComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 63);\n    i0.ɵɵelement(4, \"path\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h4\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 66);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"p\", 21);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const testimonial_r20 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(testimonial_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(testimonial_r20.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\\"\", testimonial_r20.quote, \"\\\"\");\n  }\n}\nfunction HomeComponent_ng_container_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 67);\n    i0.ɵɵtext(2, \" Start Free Trial \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 68);\n    i0.ɵɵtext(4, \" Schedule Demo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵtext(1, \" Contact Support \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 70);\n    i0.ɵɵtext(3, \" Upgrade Plan \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HomeComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.recentProjects = [{\n      name: 'E-Commerce Platform',\n      category: 'WEB DESIGN',\n      description: 'Create a user-friendly e-commerce platform with a sleek design and intuitive navigation.',\n      team: [{\n        name: 'Team member 1',\n        avatar: 'https://randomuser.me/api/portraits/women/44.jpg'\n      }, {\n        name: 'Team member 2',\n        avatar: 'https://randomuser.me/api/portraits/men/32.jpg'\n      }],\n      dueDate: 'Due in 3 days'\n    }\n    // ... autres projets\n    ];\n\n    this.testimonials = [{\n      name: 'Margot Henschke',\n      position: 'Project Manager',\n      quote: 'DevBridge has transformed how our team collaborates...'\n    }\n    // ... autres témoignages\n    ];\n  }\n  // Ajoutez cette méthode pour vérifier si l'utilisateur est admin\n  isAdmin() {\n    const user = this.authService.getCurrentUser();\n    return user && user.role === 'admin'; // Adaptez selon votre structure de données\n  }\n\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 61,\n      vars: 16,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"hero\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-2xl\", \"p-8\", \"md:p-12\", \"mb-12\", \"shadow-lg\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"items-center\"], [1, \"md:w-1/2\", \"mb-8\", \"md:mb-0\"], [1, \"text-4xl\", \"md:text-5xl\", \"font-bold\", \"mb-4\"], [4, \"ngIf\", \"ngIfElse\"], [\"welcomeBack\", \"\"], [1, \"text-xl\", \"mb-6\", \"text-[#dac4ea]\"], [\"userStats\", \"\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\"], [\"dashboardLink\", \"\"], [1, \"md:w-1/2\"], [1, \"rounded-xl\", \"shadow-2xl\", \"border-4\", \"border-white\", 3, \"src\", \"alt\"], [\"id\", \"features\", 1, \"mb-16\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-8\", \"text-center\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#7826b5]\"], [1, \"text-[#7826b5]\", \"mb-4\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-12\", \"w-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"mb-3\", \"text-[#6d6870]\"], [1, \"text-[#6d6870]\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#4a89ce]\"], [1, \"text-[#4a89ce]\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#afcf75]\"], [1, \"text-[#afcf75]\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"class\", \"mb-16 bg-white rounded-2xl shadow-lg overflow-hidden\", 4, \"ngIf\"], [1, \"mb-16\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-8\"], [\"class\", \"bg-white p-6 rounded-xl shadow-md\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-2xl\", \"p-8\", \"md:p-12\", \"text-center\"], [1, \"text-3xl\", \"md:text-4xl\", \"font-bold\", \"mb-4\"], [1, \"text-xl\", \"mb-8\", \"text-[#dac4ea]\", \"max-w-2xl\", \"mx-auto\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"justify-center\", \"gap-4\"], [\"supportCta\", \"\"], [\"class\", \"block text-sm text-[#dac4ea]\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"text-[#dac4ea]\"], [\"routerLink\", \"/registeruser\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"href\", \"#features\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"routerLink\", \"/admin/dashboard\", \"class\", \"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-6 rounded-lg text-center transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/projects\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"routerLink\", \"/admin/dashboard\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [1, \"mb-16\", \"bg-white\", \"rounded-2xl\", \"shadow-lg\", \"overflow-hidden\"], [1, \"p-8\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"text-[#6d6870]\", \"mb-6\", \"max-w-2xl\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"border border-[#bdc6cc] rounded-lg p-4 hover:shadow-md transition-all\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"border-[#bdc6cc]\", \"rounded-lg\", \"p-4\", \"hover:shadow-md\", \"transition-all\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-3\"], [1, \"font-bold\", \"text-lg\", \"text-[#4f5fad]\"], [1, \"bg-[#dac4ea]\", \"text-[#7826b5]\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"text-sm\", \"text-[#6d6870]\", \"mb-4\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"-space-x-2\"], [\"class\", \"w-8 h-8 rounded-full border-2 border-white\", 3, \"src\", \"alt\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-[#6d6870]\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"border-2\", \"border-white\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"text-[#7826b5]\", \"mr-3\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"h-8\", \"w-8\"], [\"d\", \"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z\"], [1, \"font-bold\", \"text-[#4f5fad]\"], [1, \"text-sm\", \"text-[#6d6870]\"], [\"routerLink\", \"/registeruser\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"href\", \"#\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"routerLink\", \"/support\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"routerLink\", \"/upgrade\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtemplate(5, HomeComponent_ng_container_5_Template, 4, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(6, HomeComponent_ng_template_6_Template, 2, 2, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtemplate(9, HomeComponent_ng_container_9_Template, 2, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(10, HomeComponent_ng_template_10_Template, 7, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9);\n          i0.ɵɵtemplate(13, HomeComponent_ng_container_13_Template, 5, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(14, HomeComponent_ng_template_14_Template, 3, 1, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 11);\n          i0.ɵɵelement(17, \"img\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"section\", 13)(19, \"h2\", 14);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(24, \"svg\", 18);\n          i0.ɵɵelement(25, \"path\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(26, \"h3\", 20);\n          i0.ɵɵtext(27, \"Real-time Analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"p\", 21);\n          i0.ɵɵtext(29, \"Get instant insights into your project's progress with our comprehensive analytics dashboard.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 22)(31, \"div\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(32, \"svg\", 18);\n          i0.ɵɵelement(33, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(34, \"h3\", 20);\n          i0.ɵɵtext(35, \"Team Collaboration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\", 21);\n          i0.ɵɵtext(37, \"Seamlessly collaborate with your team through integrated chat, file sharing, and task management.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 25)(39, \"div\", 26);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(40, \"svg\", 18);\n          i0.ɵɵelement(41, \"path\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"h3\", 20);\n          i0.ɵɵtext(43, \"Project Scheduling\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\", 21);\n          i0.ɵɵtext(45, \"Plan and track your projects with our intuitive calendar and milestone tracking system.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(46, HomeComponent_section_46_Template, 8, 1, \"section\", 28);\n          i0.ɵɵelementStart(47, \"section\", 29)(48, \"h2\", 14);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 30);\n          i0.ɵɵtemplate(51, HomeComponent_div_51_Template, 12, 3, \"div\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"section\", 32)(53, \"h2\", 33);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\", 34);\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 35);\n          i0.ɵɵtemplate(58, HomeComponent_ng_container_58_Template, 5, 0, \"ng-container\", 5);\n          i0.ɵɵtemplate(59, HomeComponent_ng_template_59_Template, 4, 0, \"ng-template\", null, 36, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(7);\n          const _r4 = i0.ɵɵreference(11);\n          const _r7 = i0.ɵɵreference(15);\n          const _r12 = i0.ɵɵreference(60);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r4);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r7);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.authService.userLoggedIn() ? \"assets/images/dashboard-preview.png\" : \"assets/images/project-management.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.authService.userLoggedIn() ? \"Dashboard Preview\" : \"Project Management Illustration\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Your Productivity Tools\" : \"Powerful Features\", \" \");\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"What Our Users Say\" : \"Trusted by Teams Worldwide\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.testimonials);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Need Help With Your Projects?\" : \"Ready to Transform Your Workflow?\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Our support team is available 24/7 to help you get the most out of DevBridge.\" : \"Join thousands of teams who are already managing their projects more efficiently with DevBridge.\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r12);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJob21lLmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvaG9tZS9ob21lLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLGdLQUFnSyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelement", "ɵɵelementContainerEnd", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "HomeComponent_ng_template_6_span_1_Template", "ɵɵtextInterpolate1", "tmp_0_0", "ctx_r2", "authService", "getCurrentUser", "username", "ɵɵadvance", "ɵɵproperty", "isAdmin", "HomeComponent_ng_template_14_a_0_Template", "ctx_r8", "member_r19", "avatar", "ɵɵsanitizeUrl", "name", "HomeComponent_section_46_div_7_img_10_Template", "ɵɵtextInterpolate", "project_r17", "category", "description", "team", "dueDate", "HomeComponent_section_46_div_7_Template", "ctx_r9", "recentProjects", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "testimonial_r20", "position", "quote", "HomeComponent", "constructor", "testimonials", "user", "role", "ɵɵdirectiveInject", "i1", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_ng_container_5_Template", "HomeComponent_ng_template_6_Template", "ɵɵtemplateRefExtractor", "HomeComponent_ng_container_9_Template", "HomeComponent_ng_template_10_Template", "HomeComponent_ng_container_13_Template", "HomeComponent_ng_template_14_Template", "HomeComponent_section_46_Template", "HomeComponent_div_51_Template", "HomeComponent_ng_container_58_Template", "HomeComponent_ng_template_59_Template", "userLoggedIn", "_r1", "_r4", "_r7", "_r12"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\home\\home.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.css']\r\n})\r\nexport class HomeComponent {\r\n  recentProjects = [\r\n    {\r\n      name: 'E-Commerce Platform',\r\n      category: 'WEB DESIGN',\r\n      description: 'Create a user-friendly e-commerce platform with a sleek design and intuitive navigation.',\r\n      team: [\r\n        { name: 'Team member 1', avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },\r\n        { name: 'Team member 2', avatar: 'https://randomuser.me/api/portraits/men/32.jpg' }\r\n      ],\r\n      dueDate: 'Due in 3 days'\r\n    },\r\n    // ... autres projets\r\n  ];\r\n\r\n  testimonials = [\r\n    {\r\n      name: '<PERSON><PERSON>',\r\n      position: 'Project Manager',\r\n      quote: '<PERSON><PERSON><PERSON> has transformed how our team collaborates...'\r\n    },\r\n    // ... autres témoignages\r\n  ];\r\n\r\n  constructor(public authService: AuthuserService) {}\r\n\r\n  // Ajoutez cette méthode pour vérifier si l'utilisateur est admin\r\n  isAdmin(): boolean {\r\n    const user = this.authService.getCurrentUser();\r\n    return user && user.role === 'admin'; // Adaptez selon votre structure de données\r\n  }\r\n}\r\n", "<div class=\"container mx-auto px-4 py-8\">\r\n  <!-- Hero Section -->\r\n  <section class=\"hero bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-2xl p-8 md:p-12 mb-12 shadow-lg\">\r\n    <div class=\"flex flex-col md:flex-row items-center\">\r\n      <div class=\"md:w-1/2 mb-8 md:mb-0\">\r\n        <h1 class=\"text-4xl md:text-5xl font-bold mb-4\">\r\n          <ng-container *ngIf=\"!authService.userLoggedIn(); else welcomeBack\">\r\n            Project Management <br> Made Simple\r\n          </ng-container>\r\n          <ng-template #welcomeBack>\r\n            Welcome Back, {{ authService.getCurrentUser()?.username }}!\r\n            <span *ngIf=\"isAdmin()\" class=\"block text-sm text-[#dac4ea]\">(Administrator)</span>\r\n          </ng-template>\r\n        </h1>\r\n        \r\n        <p class=\"text-xl mb-6 text-[#dac4ea]\">\r\n          <ng-container *ngIf=\"!authService.userLoggedIn(); else userStats\">\r\n            Streamline your workflow, collaborate with your team, and deliver projects on time with DevBridge.\r\n          </ng-container>\r\n          <ng-template #userStats>\r\n            You have <strong>3 active projects</strong> with <strong>5 pending tasks</strong>.\r\n          </ng-template>\r\n        </p>\r\n        \r\n        <div class=\"flex flex-col sm:flex-row gap-4\">\r\n          <ng-container *ngIf=\"!authService.userLoggedIn(); else dashboardLink\">\r\n            <a routerLink=\"/registeruser\" \r\n               class=\"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-6 rounded-lg text-center transition-all\">\r\n              Get Started\r\n            </a>\r\n            <a href=\"#features\" \r\n               class=\"border-2 border-white text-white hover:bg-white hover:text-[#4f5fad] font-bold py-3 px-6 rounded-lg text-center transition-all\">\r\n              Learn More\r\n            </a>\r\n\r\n          </ng-container>\r\n\r\n      <ng-template #dashboardLink>\r\n  <!-- Affiche le bouton Dashboard seulement pour les admins -->\r\n  <a *ngIf=\"isAdmin()\" \r\n     routerLink=\"/admin/dashboard\" \r\n     class=\"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-6 rounded-lg text-center transition-all\">\r\n    Go to Dashboard\r\n  </a>\r\n  \r\n  <!-- Bouton View Projects pour tous les utilisateurs connectés -->\r\n  <a routerLink=\"/projects\" \r\n     class=\"border-2 border-white text-white hover:bg-white hover:text-[#4f5fad] font-bold py-3 px-6 rounded-lg text-center transition-all\">\r\n    View Projects\r\n  </a>\r\n</ng-template>\r\n\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"md:w-1/2\">\r\n        <img \r\n          [src]=\"authService.userLoggedIn() ? 'assets/images/dashboard-preview.png' : 'assets/images/project-management.png'\" \r\n          [alt]=\"authService.userLoggedIn() ? 'Dashboard Preview' : 'Project Management Illustration'\"\r\n          class=\"rounded-xl shadow-2xl border-4 border-white\"\r\n        >\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Features Section -->\r\n  <section id=\"features\" class=\"mb-16\">\r\n    <h2 class=\"text-3xl font-bold text-[#4f5fad] mb-8 text-center\">\r\n      {{ authService.userLoggedIn() ? 'Your Productivity Tools' : 'Powerful Features' }}\r\n    </h2>\r\n    \r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n      <!-- Feature 1 -->\r\n      <div class=\"bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-t-4 border-[#7826b5]\">\r\n        <div class=\"text-[#7826b5] mb-4\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"text-xl font-bold mb-3 text-[#6d6870]\">Real-time Analytics</h3>\r\n        <p class=\"text-[#6d6870]\">Get instant insights into your project's progress with our comprehensive analytics dashboard.</p>\r\n      </div>\r\n      \r\n      <!-- Feature 2 -->\r\n      <div class=\"bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-t-4 border-[#4a89ce]\">\r\n        <div class=\"text-[#4a89ce] mb-4\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"text-xl font-bold mb-3 text-[#6d6870]\">Team Collaboration</h3>\r\n        <p class=\"text-[#6d6870]\">Seamlessly collaborate with your team through integrated chat, file sharing, and task management.</p>\r\n      </div>\r\n      \r\n      <!-- Feature 3 -->\r\n      <div class=\"bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all border-t-4 border-[#afcf75]\">\r\n        <div class=\"text-[#afcf75] mb-4\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"text-xl font-bold mb-3 text-[#6d6870]\">Project Scheduling</h3>\r\n        <p class=\"text-[#6d6870]\">Plan and track your projects with our intuitive calendar and milestone tracking system.</p>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Dashboard Preview - Only for logged-in users -->\r\n  <section *ngIf=\"authService.userLoggedIn()\" class=\"mb-16 bg-white rounded-2xl shadow-lg overflow-hidden\">\r\n    <div class=\"p-8\">\r\n      <h2 class=\"text-3xl font-bold text-[#4f5fad] mb-4\">Recent Activity</h2>\r\n      <p class=\"text-[#6d6870] mb-6 max-w-2xl\">Here's what's been happening with your projects.</p>\r\n      \r\n      <!-- Recent Projects -->\r\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        <div *ngFor=\"let project of recentProjects\" \r\n             class=\"border border-[#bdc6cc] rounded-lg p-4 hover:shadow-md transition-all\">\r\n          <div class=\"flex justify-between items-start mb-3\">\r\n            <h3 class=\"font-bold text-lg text-[#4f5fad]\">{{ project.name }}</h3>\r\n            <span class=\"bg-[#dac4ea] text-[#7826b5] text-xs px-2 py-1 rounded-full\">{{ project.category }}</span>\r\n          </div>\r\n          <p class=\"text-sm text-[#6d6870] mb-4\">{{ project.description }}</p>\r\n          <div class=\"flex justify-between items-center\">\r\n            <div class=\"flex -space-x-2\">\r\n              <img *ngFor=\"let member of project.team\" \r\n                   [src]=\"member.avatar\" \r\n                   [alt]=\"member.name\" \r\n                   class=\"w-8 h-8 rounded-full border-2 border-white\">\r\n            </div>\r\n            <div class=\"text-xs text-[#6d6870]\">{{ project.dueDate }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Testimonials -->\r\n  <section class=\"mb-16\">\r\n    <h2 class=\"text-3xl font-bold text-[#4f5fad] mb-8 text-center\">\r\n      {{ authService.userLoggedIn() ? 'What Our Users Say' : 'Trusted by Teams Worldwide' }}\r\n    </h2>\r\n    <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n      <div *ngFor=\"let testimonial of testimonials\" \r\n           class=\"bg-white p-6 rounded-xl shadow-md\">\r\n        <div class=\"flex items-center mb-4\">\r\n          <div class=\"text-[#7826b5] mr-3\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-8 w-8\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z\" />\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h4 class=\"font-bold text-[#4f5fad]\">{{ testimonial.name }}</h4>\r\n            <p class=\"text-sm text-[#6d6870]\">{{ testimonial.position }}</p>\r\n          </div>\r\n        </div>\r\n        <p class=\"text-[#6d6870]\">\"{{ testimonial.quote }}\"</p>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- CTA Section - Different for logged-in users -->\r\n  <section class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-2xl p-8 md:p-12 text-center\">\r\n    <h2 class=\"text-3xl md:text-4xl font-bold mb-4\">\r\n      {{ authService.userLoggedIn() ? 'Need Help With Your Projects?' : 'Ready to Transform Your Workflow?' }}\r\n    </h2>\r\n    <p class=\"text-xl mb-8 text-[#dac4ea] max-w-2xl mx-auto\">\r\n      {{ authService.userLoggedIn() ? 'Our support team is available 24/7 to help you get the most out of DevBridge.' : 'Join thousands of teams who are already managing their projects more efficiently with DevBridge.' }}\r\n    </p>\r\n    <div class=\"flex flex-col sm:flex-row justify-center gap-4\">\r\n      <ng-container *ngIf=\"!authService.userLoggedIn(); else supportCta\">\r\n        <a routerLink=\"/registeruser\" \r\n           class=\"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-8 rounded-lg transition-all\">\r\n          Start Free Trial\r\n        </a>\r\n        <a href=\"#\" \r\n           class=\"border-2 border-white text-white hover:bg-white hover:text-[#4f5fad] font-bold py-3 px-8 rounded-lg transition-all\">\r\n          Schedule Demo\r\n        </a>\r\n      </ng-container>\r\n      <ng-template #supportCta>\r\n        <a routerLink=\"/support\" \r\n           class=\"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-8 rounded-lg transition-all\">\r\n          Contact Support\r\n        </a>\r\n        <a routerLink=\"/upgrade\" \r\n           class=\"border-2 border-white text-white hover:bg-white hover:text-[#4f5fad] font-bold py-3 px-8 rounded-lg transition-all\">\r\n          Upgrade Plan\r\n        </a>\r\n      </ng-template>\r\n    </div>\r\n  </section>\r\n</div>"], "mappings": ";;;;;;ICMUA,EAAA,CAAAC,uBAAA,GAAoE;IAClED,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,SAAA,SAAI;IAACH,EAAA,CAAAE,MAAA,oBAC1B;IAAAF,EAAA,CAAAI,qBAAA,EAAe;;;;;IAGbJ,EAAA,CAAAK,cAAA,eAA6D;IAAAL,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAM,YAAA,EAAO;;;;;IADnFN,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAO,UAAA,IAAAC,2CAAA,mBAAmF;;;;;IADnFR,EAAA,CAAAS,kBAAA,qBAAAC,OAAA,GAAAC,MAAA,CAAAC,WAAA,CAAAC,cAAA,qBAAAH,OAAA,CAAAI,QAAA,OACA;IAAOd,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,SAAAL,MAAA,CAAAM,OAAA,GAAe;;;;;IAKxBjB,EAAA,CAAAC,uBAAA,GAAkE;IAChED,EAAA,CAAAE,MAAA,2GACF;IAAAF,EAAA,CAAAI,qBAAA,EAAe;;;;;IAEbJ,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAK,cAAA,aAAQ;IAAAL,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAM,YAAA,EAAS;IAACN,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAK,cAAA,aAAQ;IAAAL,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAM,YAAA,EAAS;IAAAN,EAAA,CAAAE,MAAA,SACnF;;;;;IAIAF,EAAA,CAAAC,uBAAA,GAAsE;IACpED,EAAA,CAAAK,cAAA,YACgH;IAC9GL,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;IACJN,EAAA,CAAAK,cAAA,YAC0I;IACxIL,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;IAENN,EAAA,CAAAI,qBAAA,EAAe;;;;;IAIvBJ,EAAA,CAAAK,cAAA,YAEgH;IAC9GL,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;;;;;IAJJN,EAAA,CAAAO,UAAA,IAAAW,yCAAA,gBAII;IAGJlB,EAAA,CAAAK,cAAA,YAC0I;IACxIL,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;;;;IAVAN,EAAA,CAAAgB,UAAA,SAAAG,MAAA,CAAAF,OAAA,GAAe;;;;;IAqFPjB,EAAA,CAAAG,SAAA,cAGwD;;;;IAFnDH,EAAA,CAAAgB,UAAA,QAAAI,UAAA,CAAAC,MAAA,EAAArB,EAAA,CAAAsB,aAAA,CAAqB,QAAAF,UAAA,CAAAG,IAAA;;;;;IAVhCvB,EAAA,CAAAK,cAAA,cACmF;IAElCL,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAM,YAAA,EAAK;IACpEN,EAAA,CAAAK,cAAA,eAAyE;IAAAL,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAM,YAAA,EAAO;IAExGN,EAAA,CAAAK,cAAA,YAAuC;IAAAL,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAM,YAAA,EAAI;IACpEN,EAAA,CAAAK,cAAA,cAA+C;IAE3CL,EAAA,CAAAO,UAAA,KAAAiB,8CAAA,kBAGwD;IAC1DxB,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAK,cAAA,eAAoC;IAAAL,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAM,YAAA,EAAM;;;;IAXlBN,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAyB,iBAAA,CAAAC,WAAA,CAAAH,IAAA,CAAkB;IACUvB,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAyB,iBAAA,CAAAC,WAAA,CAAAC,QAAA,CAAsB;IAE1D3B,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAyB,iBAAA,CAAAC,WAAA,CAAAE,WAAA,CAAyB;IAGpC5B,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,YAAAU,WAAA,CAAAG,IAAA,CAAe;IAKL7B,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAyB,iBAAA,CAAAC,WAAA,CAAAI,OAAA,CAAqB;;;;;IArBnE9B,EAAA,CAAAK,cAAA,kBAAyG;IAElDL,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAM,YAAA,EAAK;IACvEN,EAAA,CAAAK,cAAA,YAAyC;IAAAL,EAAA,CAAAE,MAAA,uDAAgD;IAAAF,EAAA,CAAAM,YAAA,EAAI;IAG7FN,EAAA,CAAAK,cAAA,cAAkE;IAChEL,EAAA,CAAAO,UAAA,IAAAwB,uCAAA,mBAgBM;IACR/B,EAAA,CAAAM,YAAA,EAAM;;;;IAjBqBN,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAgB,UAAA,YAAAgB,MAAA,CAAAC,cAAA,CAAiB;;;;;IA2B5CjC,EAAA,CAAAK,cAAA,cAC+C;IAGzCL,EAAA,CAAAkC,cAAA,EAAgG;IAAhGlC,EAAA,CAAAK,cAAA,cAAgG;IAC9FL,EAAA,CAAAG,SAAA,eAAqO;IACvOH,EAAA,CAAAM,YAAA,EAAM;IAERN,EAAA,CAAAmC,eAAA,EAAK;IAALnC,EAAA,CAAAK,cAAA,UAAK;IACkCL,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAM,YAAA,EAAK;IAChEN,EAAA,CAAAK,cAAA,YAAkC;IAAAL,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAM,YAAA,EAAI;IAGpEN,EAAA,CAAAK,cAAA,aAA0B;IAAAL,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAM,YAAA,EAAI;;;;IAJdN,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAyB,iBAAA,CAAAW,eAAA,CAAAb,IAAA,CAAsB;IACzBvB,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAyB,iBAAA,CAAAW,eAAA,CAAAC,QAAA,CAA0B;IAGtCrC,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAS,kBAAA,OAAA2B,eAAA,CAAAE,KAAA,OAAyB;;;;;IAcrDtC,EAAA,CAAAC,uBAAA,GAAmE;IACjED,EAAA,CAAAK,cAAA,YACoG;IAClGL,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;IACJN,EAAA,CAAAK,cAAA,YAC8H;IAC5HL,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;IACNN,EAAA,CAAAI,qBAAA,EAAe;;;;;IAEbJ,EAAA,CAAAK,cAAA,YACoG;IAClGL,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;IACJN,EAAA,CAAAK,cAAA,YAC8H;IAC5HL,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAM,YAAA,EAAI;;;ADnLZ,OAAM,MAAOiC,aAAa;EAwBxBC,YAAmB5B,WAA4B;IAA5B,KAAAA,WAAW,GAAXA,WAAW;IAvB9B,KAAAqB,cAAc,GAAG,CACf;MACEV,IAAI,EAAE,qBAAqB;MAC3BI,QAAQ,EAAE,YAAY;MACtBC,WAAW,EAAE,0FAA0F;MACvGC,IAAI,EAAE,CACJ;QAAEN,IAAI,EAAE,eAAe;QAAEF,MAAM,EAAE;MAAkD,CAAE,EACrF;QAAEE,IAAI,EAAE,eAAe;QAAEF,MAAM,EAAE;MAAgD,CAAE,CACpF;MACDS,OAAO,EAAE;;IAEX;IAAA,CACD;;IAED,KAAAW,YAAY,GAAG,CACb;MACElB,IAAI,EAAE,iBAAiB;MACvBc,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE;;IAET;IAAA,CACD;EAEiD;EAElD;EACArB,OAAOA,CAAA;IACL,MAAMyB,IAAI,GAAG,IAAI,CAAC9B,WAAW,CAACC,cAAc,EAAE;IAC9C,OAAO6B,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,OAAO,CAAC,CAAC;EACxC;;;;uBA9BWJ,aAAa,EAAAvC,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAbP,aAAa;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR1BrD,EAAA,CAAAK,cAAA,aAAyC;UAM/BL,EAAA,CAAAO,UAAA,IAAAgD,qCAAA,0BAEe;UACfvD,EAAA,CAAAO,UAAA,IAAAiD,oCAAA,gCAAAxD,EAAA,CAAAyD,sBAAA,CAGc;UAChBzD,EAAA,CAAAM,YAAA,EAAK;UAELN,EAAA,CAAAK,cAAA,WAAuC;UACrCL,EAAA,CAAAO,UAAA,IAAAmD,qCAAA,0BAEe;UACf1D,EAAA,CAAAO,UAAA,KAAAoD,qCAAA,gCAAA3D,EAAA,CAAAyD,sBAAA,CAEc;UAChBzD,EAAA,CAAAM,YAAA,EAAI;UAEJN,EAAA,CAAAK,cAAA,cAA6C;UAC3CL,EAAA,CAAAO,UAAA,KAAAqD,sCAAA,0BAUe;UAEnB5D,EAAA,CAAAO,UAAA,KAAAsD,qCAAA,iCAAA7D,EAAA,CAAAyD,sBAAA,CAaQ;UAENzD,EAAA,CAAAM,YAAA,EAAM;UAGRN,EAAA,CAAAK,cAAA,eAAsB;UACpBL,EAAA,CAAAG,SAAA,eAIC;UACHH,EAAA,CAAAM,YAAA,EAAM;UAKVN,EAAA,CAAAK,cAAA,mBAAqC;UAEjCL,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAM,YAAA,EAAK;UAELN,EAAA,CAAAK,cAAA,eAAkE;UAI5DL,EAAA,CAAAkC,cAAA,EAAgH;UAAhHlC,EAAA,CAAAK,cAAA,eAAgH;UAC9GL,EAAA,CAAAG,SAAA,gBAA0M;UAC5MH,EAAA,CAAAM,YAAA,EAAM;UAERN,EAAA,CAAAmC,eAAA,EAAkD;UAAlDnC,EAAA,CAAAK,cAAA,cAAkD;UAAAL,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAM,YAAA,EAAK;UAC1EN,EAAA,CAAAK,cAAA,aAA0B;UAAAL,EAAA,CAAAE,MAAA,qGAA6F;UAAAF,EAAA,CAAAM,YAAA,EAAI;UAI7HN,EAAA,CAAAK,cAAA,eAA0G;UAEtGL,EAAA,CAAAkC,cAAA,EAAgH;UAAhHlC,EAAA,CAAAK,cAAA,eAAgH;UAC9GL,EAAA,CAAAG,SAAA,gBAAmV;UACrVH,EAAA,CAAAM,YAAA,EAAM;UAERN,EAAA,CAAAmC,eAAA,EAAkD;UAAlDnC,EAAA,CAAAK,cAAA,cAAkD;UAAAL,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAM,YAAA,EAAK;UACzEN,EAAA,CAAAK,cAAA,aAA0B;UAAAL,EAAA,CAAAE,MAAA,yGAAiG;UAAAF,EAAA,CAAAM,YAAA,EAAI;UAIjIN,EAAA,CAAAK,cAAA,eAA0G;UAEtGL,EAAA,CAAAkC,cAAA,EAAgH;UAAhHlC,EAAA,CAAAK,cAAA,eAAgH;UAC9GL,EAAA,CAAAG,SAAA,gBAAmK;UACrKH,EAAA,CAAAM,YAAA,EAAM;UAERN,EAAA,CAAAmC,eAAA,EAAkD;UAAlDnC,EAAA,CAAAK,cAAA,cAAkD;UAAAL,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAM,YAAA,EAAK;UACzEN,EAAA,CAAAK,cAAA,aAA0B;UAAAL,EAAA,CAAAE,MAAA,+FAAuF;UAAAF,EAAA,CAAAM,YAAA,EAAI;UAM3HN,EAAA,CAAAO,UAAA,KAAAuD,iCAAA,sBA0BU;UAGV9D,EAAA,CAAAK,cAAA,mBAAuB;UAEnBL,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAM,YAAA,EAAK;UACLN,EAAA,CAAAK,cAAA,eAAmD;UACjDL,EAAA,CAAAO,UAAA,KAAAwD,6BAAA,mBAcM;UACR/D,EAAA,CAAAM,YAAA,EAAM;UAIRN,EAAA,CAAAK,cAAA,mBAA6G;UAEzGL,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAM,YAAA,EAAK;UACLN,EAAA,CAAAK,cAAA,aAAyD;UACvDL,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAM,YAAA,EAAI;UACJN,EAAA,CAAAK,cAAA,eAA4D;UAC1DL,EAAA,CAAAO,UAAA,KAAAyD,sCAAA,0BASe;UACfhE,EAAA,CAAAO,UAAA,KAAA0D,qCAAA,iCAAAjE,EAAA,CAAAyD,sBAAA,CASc;UAChBzD,EAAA,CAAAM,YAAA,EAAM;;;;;;;UAvLeN,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,GAAmC,aAAAC,GAAA;UAUnCnE,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,GAAmC,aAAAE,GAAA;UASnCpE,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,GAAmC,aAAAG,GAAA;UAgClDrE,EAAA,CAAAe,SAAA,GAAmH;UAAnHf,EAAA,CAAAgB,UAAA,QAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,qFAAAlE,EAAA,CAAAsB,aAAA,CAAmH,QAAAgC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA;UAWvHlE,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAS,kBAAA,MAAA6C,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,0DACF;UAuCQlE,EAAA,CAAAe,SAAA,IAAgC;UAAhCf,EAAA,CAAAgB,UAAA,SAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,GAAgC;UA+BtClE,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAS,kBAAA,MAAA6C,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,8DACF;UAE+BlE,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAgB,UAAA,YAAAsC,GAAA,CAAAb,YAAA,CAAe;UAqB5CzC,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAS,kBAAA,MAAA6C,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,gFACF;UAEElE,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAS,kBAAA,MAAA6C,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,+LACF;UAEiBlE,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAAsC,GAAA,CAAA1C,WAAA,CAAAsD,YAAA,GAAmC,aAAAI,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}