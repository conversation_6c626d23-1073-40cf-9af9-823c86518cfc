{"ast": null, "code": "import { filter } from 'rxjs/operators';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./message.service\";\nimport * as i2 from \"./logger.service\";\nexport class UserStatusService {\n  constructor(messageService, logger, ngZone) {\n    this.messageService = messageService;\n    this.logger = logger;\n    this.ngZone = ngZone;\n    this.onlineUsers = new Map();\n    this.reconnectionAttempts = 0;\n    this.maxReconnectionAttempts = 5;\n    this.reconnectionDelay = 1000;\n    this.initStatusSubscription();\n  }\n  //helper methode\n  initStatusSubscription() {\n    this.logger.debug('Initializing user status subscription');\n    this.ngZone.runOutsideAngular(() => {\n      try {\n        this.statusSub?.unsubscribe(); // Unsubscribe from any existing subscription\n        this.statusSub = this.messageService.subscribeToUserStatus().subscribe({\n          next: user => this.handleUserStatusUpdate(user),\n          error: error => this.handleSubscriptionError(error),\n          complete: () => this.logger.debug('User status subscription completed')\n        });\n        this.logger.debug('User status subscription initialized successfully');\n      } catch (error) {\n        this.logger.error('Error initializing user status subscription:', error);\n        // Schedule a retry after a delay\n        setTimeout(() => this.initStatusSubscription(), 5000);\n      }\n    });\n  }\n  handleUserStatusUpdate(user) {\n    this.ngZone.run(() => {\n      const isOnline = user.isOnline ?? false;\n      if (isOnline) {\n        this.onlineUsers.set(user._id, user);\n        this.logger.debug(`User ${user.username} is now online`, {\n          userId: user._id\n        });\n      } else {\n        this.onlineUsers.delete(user._id);\n        this.logger.debug(`User ${user.username} is now offline`, {\n          userId: user._id\n        });\n      }\n      this.reconnectionAttempts = 0;\n    });\n  }\n  handleSubscriptionError(error) {\n    this.logger.error('Status subscription error', error, {\n      attempt: this.reconnectionAttempts,\n      maxAttempts: this.maxReconnectionAttempts\n    });\n    if (this.reconnectionAttempts < this.maxReconnectionAttempts) {\n      this.reconnectionAttempts++;\n      const delay = this.reconnectionDelay * Math.pow(2, this.reconnectionAttempts - 1);\n      this.logger.debug(`Attempting reconnection in ${delay}ms`, {\n        attempt: this.reconnectionAttempts,\n        maxAttempts: this.maxReconnectionAttempts\n      });\n      setTimeout(() => {\n        this.initStatusSubscription();\n      }, delay);\n    } else {\n      this.logger.error('Max reconnection attempts reached', undefined, {\n        maxAttempts: this.maxReconnectionAttempts\n      });\n    }\n  }\n  //  méthodes\n  trackUserPresence(userId) {\n    return new Observable(observer => {\n      // État initial - avec vérification que isOnline est défini\n      const user = this.onlineUsers.get(userId);\n      observer.next(user?.isOnline ?? false);\n      // Abonnement aux changements\n      const sub = this.messageService.subscribeToUserStatus().pipe(filter(user => user._id === userId)).subscribe({\n        next: user => observer.next(user.isOnline ?? false),\n        error: err => observer.error(err)\n      });\n      return () => sub.unsubscribe();\n    });\n  }\n  isUserOnline(userId) {\n    const user = this.onlineUsers.get(userId);\n    return user?.isOnline ?? false;\n  }\n  getOnlineUsers() {\n    return Array.from(this.onlineUsers.values());\n  }\n  getUserStatus(userId) {\n    const user = this.onlineUsers.get(userId);\n    return {\n      isOnline: user?.isOnline ?? false,\n      lastSeen: user?.lastActive ? new Date(user.lastActive) : undefined\n    };\n  }\n  ngOnDestroy() {\n    this.statusSub?.unsubscribe();\n    this.onlineUsers.clear();\n    this.logger.debug('UserStatusService destroyed');\n  }\n  static {\n    this.ɵfac = function UserStatusService_Factory(t) {\n      return new (t || UserStatusService)(i0.ɵɵinject(i1.MessageService), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserStatusService,\n      factory: UserStatusService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["filter", "Observable", "UserStatusService", "constructor", "messageService", "logger", "ngZone", "onlineUsers", "Map", "reconnectionAttempts", "maxReconnectionAttempts", "reconnectionDelay", "initStatusSubscription", "debug", "runOutsideAngular", "statusSub", "unsubscribe", "subscribeToUserStatus", "subscribe", "next", "user", "handleUserStatusUpdate", "error", "handleSubscriptionError", "complete", "setTimeout", "run", "isOnline", "set", "_id", "username", "userId", "delete", "attempt", "maxAttempts", "delay", "Math", "pow", "undefined", "trackUserPresence", "observer", "get", "sub", "pipe", "err", "isUserOnline", "getOnlineUsers", "Array", "from", "values", "getUserStatus", "lastSeen", "lastActive", "Date", "ngOnDestroy", "clear", "i0", "ɵɵinject", "i1", "MessageService", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\user-status.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { filter } from 'rxjs/operators';\r\nimport { User } from '@app/models/user.model';\r\nimport { LoggerService } from './logger.service';\r\nimport { MessageService } from './message.service';\r\nimport { Observable, Subscription } from 'rxjs';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class UserStatusService implements OnDestroy {\r\n  private statusSub?: Subscription;\r\n  private onlineUsers = new Map<string, User>();\r\n  private reconnectionAttempts = 0;\r\n  private maxReconnectionAttempts = 5;\r\n  private reconnectionDelay = 1000;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private logger: LoggerService,\r\n    private ngZone: NgZone\r\n  ) {\r\n    this.initStatusSubscription();\r\n  }\r\n  //helper methode\r\n  private initStatusSubscription(): void {\r\n    this.logger.debug('Initializing user status subscription');\r\n\r\n    this.ngZone.runOutsideAngular(() => {\r\n      try {\r\n        this.statusSub?.unsubscribe(); // Unsubscribe from any existing subscription\r\n\r\n        this.statusSub = this.messageService.subscribeToUserStatus().subscribe({\r\n          next: (user: User) => this.handleUserStatusUpdate(user),\r\n          error: (error: Error) => this.handleSubscriptionError(error),\r\n          complete: () =>\r\n            this.logger.debug('User status subscription completed'),\r\n        });\r\n\r\n        this.logger.debug('User status subscription initialized successfully');\r\n      } catch (error) {\r\n        this.logger.error(\r\n          'Error initializing user status subscription:',\r\n          error as Error\r\n        );\r\n        // Schedule a retry after a delay\r\n        setTimeout(() => this.initStatusSubscription(), 5000);\r\n      }\r\n    });\r\n  }\r\n  private handleUserStatusUpdate(user: User): void {\r\n    this.ngZone.run(() => {\r\n      const isOnline = user.isOnline ?? false;\r\n\r\n      if (isOnline) {\r\n        this.onlineUsers.set(user._id, user);\r\n        this.logger.debug(`User ${user.username} is now online`, {\r\n          userId: user._id,\r\n        });\r\n      } else {\r\n        this.onlineUsers.delete(user._id);\r\n        this.logger.debug(`User ${user.username} is now offline`, {\r\n          userId: user._id,\r\n        });\r\n      }\r\n      this.reconnectionAttempts = 0;\r\n    });\r\n  }\r\n  private handleSubscriptionError(error: Error): void {\r\n    this.logger.error('Status subscription error', error, {\r\n      attempt: this.reconnectionAttempts,\r\n      maxAttempts: this.maxReconnectionAttempts,\r\n    });\r\n\r\n    if (this.reconnectionAttempts < this.maxReconnectionAttempts) {\r\n      this.reconnectionAttempts++;\r\n      const delay =\r\n        this.reconnectionDelay * Math.pow(2, this.reconnectionAttempts - 1);\r\n\r\n      this.logger.debug(`Attempting reconnection in ${delay}ms`, {\r\n        attempt: this.reconnectionAttempts,\r\n        maxAttempts: this.maxReconnectionAttempts,\r\n      });\r\n\r\n      setTimeout(() => {\r\n        this.initStatusSubscription();\r\n      }, delay);\r\n    } else {\r\n      this.logger.error('Max reconnection attempts reached', undefined, {\r\n        maxAttempts: this.maxReconnectionAttempts,\r\n      });\r\n    }\r\n  }\r\n  //  méthodes\r\n  trackUserPresence(userId: string): Observable<boolean> {\r\n    return new Observable<boolean>((observer) => {\r\n      // État initial - avec vérification que isOnline est défini\r\n      const user = this.onlineUsers.get(userId);\r\n      observer.next(user?.isOnline ?? false);\r\n      // Abonnement aux changements\r\n      const sub = this.messageService\r\n        .subscribeToUserStatus()\r\n        .pipe(filter((user) => user._id === userId))\r\n        .subscribe({\r\n          next: (user) => observer.next(user.isOnline ?? false),\r\n          error: (err) => observer.error(err),\r\n        });\r\n\r\n      return () => sub.unsubscribe();\r\n    });\r\n  }\r\n  isUserOnline(userId: string): boolean {\r\n    const user = this.onlineUsers.get(userId);\r\n    return user?.isOnline ?? false;\r\n  }\r\n\r\n  getOnlineUsers(): User[] {\r\n    return Array.from(this.onlineUsers.values());\r\n  }\r\n  getUserStatus(userId: string): { isOnline: boolean; lastSeen?: Date } {\r\n    const user = this.onlineUsers.get(userId);\r\n    return {\r\n      isOnline: user?.isOnline ?? false,\r\n      lastSeen: user?.lastActive ? new Date(user.lastActive) : undefined,\r\n    };\r\n  }\r\n  ngOnDestroy(): void {\r\n    this.statusSub?.unsubscribe();\r\n    this.onlineUsers.clear();\r\n    this.logger.debug('UserStatusService destroyed');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,gBAAgB;AAIvC,SAASC,UAAU,QAAsB,MAAM;;;;AAI/C,OAAM,MAAOC,iBAAiB;EAO5BC,YACUC,cAA8B,EAC9BC,MAAqB,EACrBC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IARR,KAAAC,WAAW,GAAG,IAAIC,GAAG,EAAgB;IACrC,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,uBAAuB,GAAG,CAAC;IAC3B,KAAAC,iBAAiB,GAAG,IAAI;IAO9B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACA;EACQA,sBAAsBA,CAAA;IAC5B,IAAI,CAACP,MAAM,CAACQ,KAAK,CAAC,uCAAuC,CAAC;IAE1D,IAAI,CAACP,MAAM,CAACQ,iBAAiB,CAAC,MAAK;MACjC,IAAI;QACF,IAAI,CAACC,SAAS,EAAEC,WAAW,EAAE,CAAC,CAAC;QAE/B,IAAI,CAACD,SAAS,GAAG,IAAI,CAACX,cAAc,CAACa,qBAAqB,EAAE,CAACC,SAAS,CAAC;UACrEC,IAAI,EAAGC,IAAU,IAAK,IAAI,CAACC,sBAAsB,CAACD,IAAI,CAAC;UACvDE,KAAK,EAAGA,KAAY,IAAK,IAAI,CAACC,uBAAuB,CAACD,KAAK,CAAC;UAC5DE,QAAQ,EAAEA,CAAA,KACR,IAAI,CAACnB,MAAM,CAACQ,KAAK,CAAC,oCAAoC;SACzD,CAAC;QAEF,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,mDAAmD,CAAC;OACvE,CAAC,OAAOS,KAAK,EAAE;QACd,IAAI,CAACjB,MAAM,CAACiB,KAAK,CACf,8CAA8C,EAC9CA,KAAc,CACf;QACD;QACAG,UAAU,CAAC,MAAM,IAAI,CAACb,sBAAsB,EAAE,EAAE,IAAI,CAAC;;IAEzD,CAAC,CAAC;EACJ;EACQS,sBAAsBA,CAACD,IAAU;IACvC,IAAI,CAACd,MAAM,CAACoB,GAAG,CAAC,MAAK;MACnB,MAAMC,QAAQ,GAAGP,IAAI,CAACO,QAAQ,IAAI,KAAK;MAEvC,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACpB,WAAW,CAACqB,GAAG,CAACR,IAAI,CAACS,GAAG,EAAET,IAAI,CAAC;QACpC,IAAI,CAACf,MAAM,CAACQ,KAAK,CAAC,QAAQO,IAAI,CAACU,QAAQ,gBAAgB,EAAE;UACvDC,MAAM,EAAEX,IAAI,CAACS;SACd,CAAC;OACH,MAAM;QACL,IAAI,CAACtB,WAAW,CAACyB,MAAM,CAACZ,IAAI,CAACS,GAAG,CAAC;QACjC,IAAI,CAACxB,MAAM,CAACQ,KAAK,CAAC,QAAQO,IAAI,CAACU,QAAQ,iBAAiB,EAAE;UACxDC,MAAM,EAAEX,IAAI,CAACS;SACd,CAAC;;MAEJ,IAAI,CAACpB,oBAAoB,GAAG,CAAC;IAC/B,CAAC,CAAC;EACJ;EACQc,uBAAuBA,CAACD,KAAY;IAC1C,IAAI,CAACjB,MAAM,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,EAAE;MACpDW,OAAO,EAAE,IAAI,CAACxB,oBAAoB;MAClCyB,WAAW,EAAE,IAAI,CAACxB;KACnB,CAAC;IAEF,IAAI,IAAI,CAACD,oBAAoB,GAAG,IAAI,CAACC,uBAAuB,EAAE;MAC5D,IAAI,CAACD,oBAAoB,EAAE;MAC3B,MAAM0B,KAAK,GACT,IAAI,CAACxB,iBAAiB,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5B,oBAAoB,GAAG,CAAC,CAAC;MAErE,IAAI,CAACJ,MAAM,CAACQ,KAAK,CAAC,8BAA8BsB,KAAK,IAAI,EAAE;QACzDF,OAAO,EAAE,IAAI,CAACxB,oBAAoB;QAClCyB,WAAW,EAAE,IAAI,CAACxB;OACnB,CAAC;MAEFe,UAAU,CAAC,MAAK;QACd,IAAI,CAACb,sBAAsB,EAAE;MAC/B,CAAC,EAAEuB,KAAK,CAAC;KACV,MAAM;MACL,IAAI,CAAC9B,MAAM,CAACiB,KAAK,CAAC,mCAAmC,EAAEgB,SAAS,EAAE;QAChEJ,WAAW,EAAE,IAAI,CAACxB;OACnB,CAAC;;EAEN;EACA;EACA6B,iBAAiBA,CAACR,MAAc;IAC9B,OAAO,IAAI9B,UAAU,CAAWuC,QAAQ,IAAI;MAC1C;MACA,MAAMpB,IAAI,GAAG,IAAI,CAACb,WAAW,CAACkC,GAAG,CAACV,MAAM,CAAC;MACzCS,QAAQ,CAACrB,IAAI,CAACC,IAAI,EAAEO,QAAQ,IAAI,KAAK,CAAC;MACtC;MACA,MAAMe,GAAG,GAAG,IAAI,CAACtC,cAAc,CAC5Ba,qBAAqB,EAAE,CACvB0B,IAAI,CAAC3C,MAAM,CAAEoB,IAAI,IAAKA,IAAI,CAACS,GAAG,KAAKE,MAAM,CAAC,CAAC,CAC3Cb,SAAS,CAAC;QACTC,IAAI,EAAGC,IAAI,IAAKoB,QAAQ,CAACrB,IAAI,CAACC,IAAI,CAACO,QAAQ,IAAI,KAAK,CAAC;QACrDL,KAAK,EAAGsB,GAAG,IAAKJ,QAAQ,CAAClB,KAAK,CAACsB,GAAG;OACnC,CAAC;MAEJ,OAAO,MAAMF,GAAG,CAAC1B,WAAW,EAAE;IAChC,CAAC,CAAC;EACJ;EACA6B,YAAYA,CAACd,MAAc;IACzB,MAAMX,IAAI,GAAG,IAAI,CAACb,WAAW,CAACkC,GAAG,CAACV,MAAM,CAAC;IACzC,OAAOX,IAAI,EAAEO,QAAQ,IAAI,KAAK;EAChC;EAEAmB,cAAcA,CAAA;IACZ,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzC,WAAW,CAAC0C,MAAM,EAAE,CAAC;EAC9C;EACAC,aAAaA,CAACnB,MAAc;IAC1B,MAAMX,IAAI,GAAG,IAAI,CAACb,WAAW,CAACkC,GAAG,CAACV,MAAM,CAAC;IACzC,OAAO;MACLJ,QAAQ,EAAEP,IAAI,EAAEO,QAAQ,IAAI,KAAK;MACjCwB,QAAQ,EAAE/B,IAAI,EAAEgC,UAAU,GAAG,IAAIC,IAAI,CAACjC,IAAI,CAACgC,UAAU,CAAC,GAAGd;KAC1D;EACH;EACAgB,WAAWA,CAAA;IACT,IAAI,CAACvC,SAAS,EAAEC,WAAW,EAAE;IAC7B,IAAI,CAACT,WAAW,CAACgD,KAAK,EAAE;IACxB,IAAI,CAAClD,MAAM,CAACQ,KAAK,CAAC,6BAA6B,CAAC;EAClD;;;uBAxHWX,iBAAiB,EAAAsD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAjB5D,iBAAiB;MAAA6D,OAAA,EAAjB7D,iBAAiB,CAAA8D,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}