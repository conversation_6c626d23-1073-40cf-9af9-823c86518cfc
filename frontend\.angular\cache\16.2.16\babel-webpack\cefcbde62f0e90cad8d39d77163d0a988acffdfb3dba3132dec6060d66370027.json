{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { NotificationListComponent } from './notification-list/notification-list.component';\nimport { MessageLayoutComponent } from '../messages/message-layout/message-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MessageLayoutComponent,\n  data: {\n    context: 'notifications'\n  },\n  children: [{\n    path: '',\n    component: NotificationListComponent,\n    data: {\n      title: 'Notifications'\n    }\n  }]\n}];\nexport class NotificationsRoutingModule {\n  static {\n    this.ɵfac = function NotificationsRoutingModule_Factory(t) {\n      return new (t || NotificationsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NotificationsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotificationsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "NotificationListComponent", "MessageLayoutComponent", "routes", "path", "component", "data", "context", "children", "title", "NotificationsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notifications-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { NotificationListComponent } from './notification-list/notification-list.component';\r\nimport { MessageLayoutComponent } from '../messages/message-layout/message-layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: MessageLayoutComponent,\r\n    data: { context: 'notifications' },\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: NotificationListComponent,\r\n        data: { title: 'Notifications' },\r\n      },\r\n    ],\r\n  },\r\n];\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class NotificationsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,sBAAsB,QAAQ,qDAAqD;;;AAE5F,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,sBAAsB;EACjCI,IAAI,EAAE;IAAEC,OAAO,EAAE;EAAe,CAAE;EAClCC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEJ,yBAAyB;IACpCK,IAAI,EAAE;MAAEG,KAAK,EAAE;IAAe;GAC/B;CAEJ,CACF;AAKD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BV,YAAY,CAACW,QAAQ,CAACR,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXU,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAF3Bd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}