{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant } from \"../globals/index.js\";\nimport { visit, Kind } from \"graphql\";\nimport { checkDocument, getOperationDefinition, getFragmentDefinition, getFragmentDefinitions, getMainDefinition } from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\nvar TYPENAME_FIELD = {\n  kind: Kind.FIELD,\n  name: {\n    kind: Kind.NAME,\n    value: \"__typename\"\n  }\n};\nfunction isEmpty(op, fragmentMap) {\n  return !op || op.selectionSet.selections.every(function (selection) {\n    return selection.kind === Kind.FRAGMENT_SPREAD && isEmpty(fragmentMap[selection.name.value], fragmentMap);\n  });\n}\nfunction nullIfDocIsEmpty(doc) {\n  return isEmpty(getOperationDefinition(doc) || getFragmentDefinition(doc), createFragmentMap(getFragmentDefinitions(doc))) ? null : doc;\n}\nfunction getDirectiveMatcher(configs) {\n  var names = new Map();\n  var tests = new Map();\n  configs.forEach(function (directive) {\n    if (directive) {\n      if (directive.name) {\n        names.set(directive.name, directive);\n      } else if (directive.test) {\n        tests.set(directive.test, directive);\n      }\n    }\n  });\n  return function (directive) {\n    var config = names.get(directive.name.value);\n    if (!config && tests.size) {\n      tests.forEach(function (testConfig, test) {\n        if (test(directive)) {\n          config = testConfig;\n        }\n      });\n    }\n    return config;\n  };\n}\nfunction makeInUseGetterFunction(defaultKey) {\n  var map = new Map();\n  return function inUseGetterFunction(key) {\n    if (key === void 0) {\n      key = defaultKey;\n    }\n    var inUse = map.get(key);\n    if (!inUse) {\n      map.set(key, inUse = {\n        // Variable and fragment spread names used directly within this\n        // operation or fragment definition, as identified by key. These sets\n        // will be populated during the first traversal of the document in\n        // removeDirectivesFromDocument below.\n        variables: new Set(),\n        fragmentSpreads: new Set()\n      });\n    }\n    return inUse;\n  };\n}\nexport function removeDirectivesFromDocument(directives, doc) {\n  checkDocument(doc);\n  // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n  // operations as if their names were \"\". Anonymous fragment definitions are\n  // not supposed to be possible, but the same default naming strategy seems\n  // appropriate for that case as well.\n  var getInUseByOperationName = makeInUseGetterFunction(\"\");\n  var getInUseByFragmentName = makeInUseGetterFunction(\"\");\n  var getInUse = function (ancestors) {\n    for (var p = 0, ancestor = void 0; p < ancestors.length && (ancestor = ancestors[p]); ++p) {\n      if (isArray(ancestor)) continue;\n      if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n        // If an operation is anonymous, we use the empty string as its key.\n        return getInUseByOperationName(ancestor.name && ancestor.name.value);\n      }\n      if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n        return getInUseByFragmentName(ancestor.name.value);\n      }\n    }\n    globalThis.__DEV__ !== false && invariant.error(97);\n    return null;\n  };\n  var operationCount = 0;\n  for (var i = doc.definitions.length - 1; i >= 0; --i) {\n    if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n      ++operationCount;\n    }\n  }\n  var directiveMatcher = getDirectiveMatcher(directives);\n  var shouldRemoveField = function (nodeDirectives) {\n    return isNonEmptyArray(nodeDirectives) && nodeDirectives.map(directiveMatcher).some(function (config) {\n      return config && config.remove;\n    });\n  };\n  var originalFragmentDefsByPath = new Map();\n  // Any time the first traversal of the document below makes a change like\n  // removing a fragment (by returning null), this variable should be set to\n  // true. Once it becomes true, it should never be set to false again. If this\n  // variable remains false throughout the traversal, then we can return the\n  // original doc immediately without any modifications.\n  var firstVisitMadeChanges = false;\n  var fieldOrInlineFragmentVisitor = {\n    enter: function (node) {\n      if (shouldRemoveField(node.directives)) {\n        firstVisitMadeChanges = true;\n        return null;\n      }\n    }\n  };\n  var docWithoutDirectiveSubtrees = visit(doc, {\n    // These two AST node types share the same implementation, defined above.\n    Field: fieldOrInlineFragmentVisitor,\n    InlineFragment: fieldOrInlineFragmentVisitor,\n    VariableDefinition: {\n      enter: function () {\n        // VariableDefinition nodes do not count as variables in use, though\n        // they do contain Variable nodes that might be visited below. To avoid\n        // counting variable declarations as usages, we skip visiting the\n        // contents of this VariableDefinition node by returning false.\n        return false;\n      }\n    },\n    Variable: {\n      enter: function (node, _key, _parent, _path, ancestors) {\n        var inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.variables.add(node.name.value);\n        }\n      }\n    },\n    FragmentSpread: {\n      enter: function (node, _key, _parent, _path, ancestors) {\n        if (shouldRemoveField(node.directives)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n        var inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.fragmentSpreads.add(node.name.value);\n        }\n        // We might like to remove this FragmentSpread by returning null here if\n        // the corresponding FragmentDefinition node is also going to be removed\n        // by the logic below, but we can't control the relative order of those\n        // events, so we have to postpone the removal of dangling FragmentSpread\n        // nodes until after the current visit of the document has finished.\n      }\n    },\n\n    FragmentDefinition: {\n      enter: function (node, _key, _parent, path) {\n        originalFragmentDefsByPath.set(JSON.stringify(path), node);\n      },\n      leave: function (node, _key, _parent, path) {\n        var originalNode = originalFragmentDefsByPath.get(JSON.stringify(path));\n        if (node === originalNode) {\n          // If the FragmentNode received by this leave function is identical to\n          // the one received by the corresponding enter function (above), then\n          // the visitor must not have made any changes within this\n          // FragmentDefinition node. This fragment definition may still be\n          // removed if there are no ...spread references to it, but it won't be\n          // removed just because it has only a __typename field.\n          return node;\n        }\n        if (\n        // This logic applies only if the document contains one or more\n        // operations, since removing all fragments from a document containing\n        // only fragments makes the document useless.\n        operationCount > 0 && node.selectionSet.selections.every(function (selection) {\n          return selection.kind === Kind.FIELD && selection.name.value === \"__typename\";\n        })) {\n          // This is a somewhat opinionated choice: if a FragmentDefinition ends\n          // up having no fields other than __typename, we remove the whole\n          // fragment definition, and later prune ...spread references to it.\n          getInUseByFragmentName(node.name.value).removed = true;\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      }\n    },\n    Directive: {\n      leave: function (node) {\n        // If a matching directive is found, remove the directive itself. Note\n        // that this does not remove the target (field, argument, etc) of the\n        // directive, but only the directive itself.\n        if (directiveMatcher(node)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      }\n    }\n  });\n  if (!firstVisitMadeChanges) {\n    // If our first pass did not change anything about the document, then there\n    // is no cleanup we need to do, and we can return the original doc.\n    return doc;\n  }\n  // Utility for making sure inUse.transitiveVars is recursively populated.\n  // Because this logic assumes inUse.fragmentSpreads has been completely\n  // populated and inUse.removed has been set if appropriate,\n  // populateTransitiveVars must be called after that information has been\n  // collected by the first traversal of the document.\n  var populateTransitiveVars = function (inUse) {\n    if (!inUse.transitiveVars) {\n      inUse.transitiveVars = new Set(inUse.variables);\n      if (!inUse.removed) {\n        inUse.fragmentSpreads.forEach(function (childFragmentName) {\n          populateTransitiveVars(getInUseByFragmentName(childFragmentName)).transitiveVars.forEach(function (varName) {\n            inUse.transitiveVars.add(varName);\n          });\n        });\n      }\n    }\n    return inUse;\n  };\n  // Since we've been keeping track of fragment spreads used by particular\n  // operations and fragment definitions, we now need to compute the set of all\n  // spreads used (transitively) by any operations in the document.\n  var allFragmentNamesUsed = new Set();\n  docWithoutDirectiveSubtrees.definitions.forEach(function (def) {\n    if (def.kind === Kind.OPERATION_DEFINITION) {\n      populateTransitiveVars(getInUseByOperationName(def.name && def.name.value)).fragmentSpreads.forEach(function (childFragmentName) {\n        allFragmentNamesUsed.add(childFragmentName);\n      });\n    } else if (def.kind === Kind.FRAGMENT_DEFINITION &&\n    // If there are no operations in the document, then all fragment\n    // definitions count as usages of their own fragment names. This heuristic\n    // prevents accidentally removing all fragment definitions from the\n    // document just because it contains no operations that use the fragments.\n    operationCount === 0 && !getInUseByFragmentName(def.name.value).removed) {\n      allFragmentNamesUsed.add(def.name.value);\n    }\n  });\n  // Now that we have added all fragment spreads used by operations to the\n  // allFragmentNamesUsed set, we can complete the set by transitively adding\n  // all fragment spreads used by those fragments, and so on.\n  allFragmentNamesUsed.forEach(function (fragmentName) {\n    // Once all the childFragmentName strings added here have been seen already,\n    // the top-level allFragmentNamesUsed.forEach loop will terminate.\n    populateTransitiveVars(getInUseByFragmentName(fragmentName)).fragmentSpreads.forEach(function (childFragmentName) {\n      allFragmentNamesUsed.add(childFragmentName);\n    });\n  });\n  var fragmentWillBeRemoved = function (fragmentName) {\n    return !!(\n    // A fragment definition will be removed if there are no spreads that refer\n    // to it, or the fragment was explicitly removed because it had no fields\n    // other than __typename.\n    !allFragmentNamesUsed.has(fragmentName) || getInUseByFragmentName(fragmentName).removed);\n  };\n  var enterVisitor = {\n    enter: function (node) {\n      if (fragmentWillBeRemoved(node.name.value)) {\n        return null;\n      }\n    }\n  };\n  return nullIfDocIsEmpty(visit(docWithoutDirectiveSubtrees, {\n    // If the fragment is going to be removed, then leaving any dangling\n    // FragmentSpread nodes with the same name would be a mistake.\n    FragmentSpread: enterVisitor,\n    // This is where the fragment definition is actually removed.\n    FragmentDefinition: enterVisitor,\n    OperationDefinition: {\n      leave: function (node) {\n        // Upon leaving each operation in the depth-first AST traversal, prune\n        // any variables that are declared by the operation but unused within.\n        if (node.variableDefinitions) {\n          var usedVariableNames_1 = populateTransitiveVars(\n          // If an operation is anonymous, we use the empty string as its key.\n          getInUseByOperationName(node.name && node.name.value)).transitiveVars;\n          // According to the GraphQL spec, all variables declared by an\n          // operation must either be used by that operation or used by some\n          // fragment included transitively into that operation:\n          // https://spec.graphql.org/draft/#sec-All-Variables-Used\n          //\n          // To stay on the right side of this validation rule, if/when we\n          // remove the last $var references from an operation or its fragments,\n          // we must also remove the corresponding $var declaration from the\n          // enclosing operation. This pruning applies only to operations and\n          // not fragment definitions, at the moment. Fragments may be able to\n          // declare variables eventually, but today they can only consume them.\n          if (usedVariableNames_1.size < node.variableDefinitions.length) {\n            return __assign(__assign({}, node), {\n              variableDefinitions: node.variableDefinitions.filter(function (varDef) {\n                return usedVariableNames_1.has(varDef.variable.name.value);\n              })\n            });\n          }\n        }\n      }\n    }\n  }));\n}\nexport var addTypenameToDocument = Object.assign(function (doc) {\n  return visit(doc, {\n    SelectionSet: {\n      enter: function (node, _key, parent) {\n        // Don't add __typename to OperationDefinitions.\n        if (parent && parent.kind === Kind.OPERATION_DEFINITION) {\n          return;\n        }\n        // No changes if no selections.\n        var selections = node.selections;\n        if (!selections) {\n          return;\n        }\n        // If selections already have a __typename, or are part of an\n        // introspection query, do nothing.\n        var skip = selections.some(function (selection) {\n          return isField(selection) && (selection.name.value === \"__typename\" || selection.name.value.lastIndexOf(\"__\", 0) === 0);\n        });\n        if (skip) {\n          return;\n        }\n        // If this SelectionSet is @export-ed as an input variable, it should\n        // not have a __typename field (see issue #4691).\n        var field = parent;\n        if (isField(field) && field.directives && field.directives.some(function (d) {\n          return d.name.value === \"export\";\n        })) {\n          return;\n        }\n        // Create and return a new SelectionSet with a __typename Field.\n        return __assign(__assign({}, node), {\n          selections: __spreadArray(__spreadArray([], selections, true), [TYPENAME_FIELD], false)\n        });\n      }\n    }\n  });\n}, {\n  added: function (field) {\n    return field === TYPENAME_FIELD;\n  }\n});\nvar connectionRemoveConfig = {\n  test: function (directive) {\n    var willRemove = directive.name.value === \"connection\";\n    if (willRemove) {\n      if (!directive.arguments || !directive.arguments.some(function (arg) {\n        return arg.name.value === \"key\";\n      })) {\n        globalThis.__DEV__ !== false && invariant.warn(98);\n      }\n    }\n    return willRemove;\n  }\n};\nexport function removeConnectionDirectiveFromDocument(doc) {\n  return removeDirectivesFromDocument([connectionRemoveConfig], checkDocument(doc));\n}\nfunction hasDirectivesInSelectionSet(directives, selectionSet, nestedCheck) {\n  if (nestedCheck === void 0) {\n    nestedCheck = true;\n  }\n  return !!selectionSet && selectionSet.selections && selectionSet.selections.some(function (selection) {\n    return hasDirectivesInSelection(directives, selection, nestedCheck);\n  });\n}\nfunction hasDirectivesInSelection(directives, selection, nestedCheck) {\n  if (nestedCheck === void 0) {\n    nestedCheck = true;\n  }\n  if (!isField(selection)) {\n    return true;\n  }\n  if (!selection.directives) {\n    return false;\n  }\n  return selection.directives.some(getDirectiveMatcher(directives)) || nestedCheck && hasDirectivesInSelectionSet(directives, selection.selectionSet, nestedCheck);\n}\nfunction getArgumentMatcher(config) {\n  return function argumentMatcher(argument) {\n    return config.some(function (aConfig) {\n      return argument.value && argument.value.kind === Kind.VARIABLE && argument.value.name && (aConfig.name === argument.value.name.value || aConfig.test && aConfig.test(argument));\n    });\n  };\n}\nexport function removeArgumentsFromDocument(config, doc) {\n  var argMatcher = getArgumentMatcher(config);\n  return nullIfDocIsEmpty(visit(doc, {\n    OperationDefinition: {\n      enter: function (node) {\n        return __assign(__assign({}, node), {\n          // Remove matching top level variables definitions.\n          variableDefinitions: node.variableDefinitions ? node.variableDefinitions.filter(function (varDef) {\n            return !config.some(function (arg) {\n              return arg.name === varDef.variable.name.value;\n            });\n          }) : []\n        });\n      }\n    },\n    Field: {\n      enter: function (node) {\n        // If `remove` is set to true for an argument, and an argument match\n        // is found for a field, remove the field as well.\n        var shouldRemoveField = config.some(function (argConfig) {\n          return argConfig.remove;\n        });\n        if (shouldRemoveField) {\n          var argMatchCount_1 = 0;\n          if (node.arguments) {\n            node.arguments.forEach(function (arg) {\n              if (argMatcher(arg)) {\n                argMatchCount_1 += 1;\n              }\n            });\n          }\n          if (argMatchCount_1 === 1) {\n            return null;\n          }\n        }\n      }\n    },\n    Argument: {\n      enter: function (node) {\n        // Remove all matching arguments.\n        if (argMatcher(node)) {\n          return null;\n        }\n      }\n    }\n  }));\n}\nexport function removeFragmentSpreadFromDocument(config, doc) {\n  function enter(node) {\n    if (config.some(function (def) {\n      return def.name === node.name.value;\n    })) {\n      return null;\n    }\n  }\n  return nullIfDocIsEmpty(visit(doc, {\n    FragmentSpread: {\n      enter: enter\n    },\n    FragmentDefinition: {\n      enter: enter\n    }\n  }));\n}\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(document) {\n  var definition = getMainDefinition(document);\n  var definitionOperation = definition.operation;\n  if (definitionOperation === \"query\") {\n    // Already a query, so return the existing document.\n    return document;\n  }\n  // Build a new query using the selection set of the main operation.\n  var modifiedDoc = visit(document, {\n    OperationDefinition: {\n      enter: function (node) {\n        return __assign(__assign({}, node), {\n          operation: \"query\"\n        });\n      }\n    }\n  });\n  return modifiedDoc;\n}\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(document) {\n  checkDocument(document);\n  var modifiedDoc = removeDirectivesFromDocument([{\n    test: function (directive) {\n      return directive.name.value === \"client\";\n    },\n    remove: true\n  }], document);\n  return modifiedDoc;\n}\nexport function addNonReactiveToNamedFragments(document) {\n  checkDocument(document);\n  return visit(document, {\n    FragmentSpread: function (node) {\n      var _a;\n      // Do not add `@nonreactive` if the fragment is marked with `@unmask`\n      // since we want to react to changes in this fragment.\n      if ((_a = node.directives) === null || _a === void 0 ? void 0 : _a.some(function (directive) {\n        return directive.name.value === \"unmask\";\n      })) {\n        return;\n      }\n      return __assign(__assign({}, node), {\n        directives: __spreadArray(__spreadArray([], node.directives || [], true), [{\n          kind: Kind.DIRECTIVE,\n          name: {\n            kind: Kind.NAME,\n            value: \"nonreactive\"\n          }\n        }], false)\n      });\n    }\n  });\n}", "map": {"version": 3, "names": ["__assign", "__spread<PERSON><PERSON>y", "invariant", "visit", "Kind", "checkDocument", "getOperationDefinition", "getFragmentDefinition", "getFragmentDefinitions", "getMainDefinition", "isField", "createFragmentMap", "isArray", "isNonEmptyArray", "TYPENAME_FIELD", "kind", "FIELD", "name", "NAME", "value", "isEmpty", "op", "fragmentMap", "selectionSet", "selections", "every", "selection", "FRAGMENT_SPREAD", "nullIfDocIsEmpty", "doc", "getDirectiveMatcher", "configs", "names", "Map", "tests", "for<PERSON>ach", "directive", "set", "test", "config", "get", "size", "testConfig", "makeInUseGetterFunction", "defaultKey", "map", "inUseGetterFunction", "key", "inUse", "variables", "Set", "fragmentSpreads", "removeDirectivesFromDocument", "directives", "getInUseByOperationName", "getInUseByFragmentName", "getInUse", "ancestors", "p", "ancestor", "length", "OPERATION_DEFINITION", "FRAGMENT_DEFINITION", "globalThis", "__DEV__", "error", "operationCount", "i", "definitions", "directive<PERSON><PERSON><PERSON>", "shouldRemoveField", "nodeDirectives", "some", "remove", "originalFragmentDefsByPath", "firstVisitMadeChanges", "fieldOrInlineFragmentVisitor", "enter", "node", "docWithoutDirectiveSubtrees", "Field", "InlineFragment", "VariableDefinition", "Variable", "_key", "_parent", "_path", "add", "FragmentSpread", "FragmentDefinition", "path", "JSON", "stringify", "leave", "originalNode", "removed", "Directive", "populateTransitiveVars", "transitiveVars", "childFragmentName", "varName", "allFragmentNamesUsed", "def", "fragmentName", "fragmentWillBeRemoved", "has", "enterVisitor", "OperationDefinition", "variableDefinitions", "usedVariableNames_1", "filter", "varDef", "variable", "addTypenameToDocument", "Object", "assign", "SelectionSet", "parent", "skip", "lastIndexOf", "field", "d", "added", "connectionRemoveConfig", "will<PERSON><PERSON><PERSON>", "arguments", "arg", "warn", "removeConnectionDirectiveFromDocument", "hasDirectivesInSelectionSet", "nested<PERSON><PERSON><PERSON>", "hasDirectivesInSelection", "getArgumentMatcher", "argumentMatcher", "argument", "aConfig", "VARIABLE", "removeArgumentsFromDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argConfig", "argMatchCount_1", "Argument", "removeFragmentSpreadFromDocument", "buildQueryFromSelectionSet", "document", "definition", "definitionOperation", "operation", "modifiedDoc", "removeClientSetsFromDocument", "addNonReactiveToNamedFragments", "_a", "DIRECTIVE"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/transform.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { invariant } from \"../globals/index.js\";\nimport { visit, Kind } from \"graphql\";\nimport { checkDocument, getOperationDefinition, getFragmentDefinition, getFragmentDefinitions, getMainDefinition, } from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\nvar TYPENAME_FIELD = {\n    kind: Kind.FIELD,\n    name: {\n        kind: Kind.NAME,\n        value: \"__typename\",\n    },\n};\nfunction isEmpty(op, fragmentMap) {\n    return (!op ||\n        op.selectionSet.selections.every(function (selection) {\n            return selection.kind === Kind.FRAGMENT_SPREAD &&\n                isEmpty(fragmentMap[selection.name.value], fragmentMap);\n        }));\n}\nfunction nullIfDocIsEmpty(doc) {\n    return (isEmpty(getOperationDefinition(doc) || getFragmentDefinition(doc), createFragmentMap(getFragmentDefinitions(doc)))) ?\n        null\n        : doc;\n}\nfunction getDirectiveMatcher(configs) {\n    var names = new Map();\n    var tests = new Map();\n    configs.forEach(function (directive) {\n        if (directive) {\n            if (directive.name) {\n                names.set(directive.name, directive);\n            }\n            else if (directive.test) {\n                tests.set(directive.test, directive);\n            }\n        }\n    });\n    return function (directive) {\n        var config = names.get(directive.name.value);\n        if (!config && tests.size) {\n            tests.forEach(function (testConfig, test) {\n                if (test(directive)) {\n                    config = testConfig;\n                }\n            });\n        }\n        return config;\n    };\n}\nfunction makeInUseGetterFunction(defaultKey) {\n    var map = new Map();\n    return function inUseGetterFunction(key) {\n        if (key === void 0) { key = defaultKey; }\n        var inUse = map.get(key);\n        if (!inUse) {\n            map.set(key, (inUse = {\n                // Variable and fragment spread names used directly within this\n                // operation or fragment definition, as identified by key. These sets\n                // will be populated during the first traversal of the document in\n                // removeDirectivesFromDocument below.\n                variables: new Set(),\n                fragmentSpreads: new Set(),\n            }));\n        }\n        return inUse;\n    };\n}\nexport function removeDirectivesFromDocument(directives, doc) {\n    checkDocument(doc);\n    // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n    // operations as if their names were \"\". Anonymous fragment definitions are\n    // not supposed to be possible, but the same default naming strategy seems\n    // appropriate for that case as well.\n    var getInUseByOperationName = makeInUseGetterFunction(\"\");\n    var getInUseByFragmentName = makeInUseGetterFunction(\"\");\n    var getInUse = function (ancestors) {\n        for (var p = 0, ancestor = void 0; p < ancestors.length && (ancestor = ancestors[p]); ++p) {\n            if (isArray(ancestor))\n                continue;\n            if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n                // If an operation is anonymous, we use the empty string as its key.\n                return getInUseByOperationName(ancestor.name && ancestor.name.value);\n            }\n            if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n                return getInUseByFragmentName(ancestor.name.value);\n            }\n        }\n        globalThis.__DEV__ !== false && invariant.error(97);\n        return null;\n    };\n    var operationCount = 0;\n    for (var i = doc.definitions.length - 1; i >= 0; --i) {\n        if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n            ++operationCount;\n        }\n    }\n    var directiveMatcher = getDirectiveMatcher(directives);\n    var shouldRemoveField = function (nodeDirectives) {\n        return isNonEmptyArray(nodeDirectives) &&\n            nodeDirectives\n                .map(directiveMatcher)\n                .some(function (config) { return config && config.remove; });\n    };\n    var originalFragmentDefsByPath = new Map();\n    // Any time the first traversal of the document below makes a change like\n    // removing a fragment (by returning null), this variable should be set to\n    // true. Once it becomes true, it should never be set to false again. If this\n    // variable remains false throughout the traversal, then we can return the\n    // original doc immediately without any modifications.\n    var firstVisitMadeChanges = false;\n    var fieldOrInlineFragmentVisitor = {\n        enter: function (node) {\n            if (shouldRemoveField(node.directives)) {\n                firstVisitMadeChanges = true;\n                return null;\n            }\n        },\n    };\n    var docWithoutDirectiveSubtrees = visit(doc, {\n        // These two AST node types share the same implementation, defined above.\n        Field: fieldOrInlineFragmentVisitor,\n        InlineFragment: fieldOrInlineFragmentVisitor,\n        VariableDefinition: {\n            enter: function () {\n                // VariableDefinition nodes do not count as variables in use, though\n                // they do contain Variable nodes that might be visited below. To avoid\n                // counting variable declarations as usages, we skip visiting the\n                // contents of this VariableDefinition node by returning false.\n                return false;\n            },\n        },\n        Variable: {\n            enter: function (node, _key, _parent, _path, ancestors) {\n                var inUse = getInUse(ancestors);\n                if (inUse) {\n                    inUse.variables.add(node.name.value);\n                }\n            },\n        },\n        FragmentSpread: {\n            enter: function (node, _key, _parent, _path, ancestors) {\n                if (shouldRemoveField(node.directives)) {\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n                var inUse = getInUse(ancestors);\n                if (inUse) {\n                    inUse.fragmentSpreads.add(node.name.value);\n                }\n                // We might like to remove this FragmentSpread by returning null here if\n                // the corresponding FragmentDefinition node is also going to be removed\n                // by the logic below, but we can't control the relative order of those\n                // events, so we have to postpone the removal of dangling FragmentSpread\n                // nodes until after the current visit of the document has finished.\n            },\n        },\n        FragmentDefinition: {\n            enter: function (node, _key, _parent, path) {\n                originalFragmentDefsByPath.set(JSON.stringify(path), node);\n            },\n            leave: function (node, _key, _parent, path) {\n                var originalNode = originalFragmentDefsByPath.get(JSON.stringify(path));\n                if (node === originalNode) {\n                    // If the FragmentNode received by this leave function is identical to\n                    // the one received by the corresponding enter function (above), then\n                    // the visitor must not have made any changes within this\n                    // FragmentDefinition node. This fragment definition may still be\n                    // removed if there are no ...spread references to it, but it won't be\n                    // removed just because it has only a __typename field.\n                    return node;\n                }\n                if (\n                // This logic applies only if the document contains one or more\n                // operations, since removing all fragments from a document containing\n                // only fragments makes the document useless.\n                operationCount > 0 &&\n                    node.selectionSet.selections.every(function (selection) {\n                        return selection.kind === Kind.FIELD &&\n                            selection.name.value === \"__typename\";\n                    })) {\n                    // This is a somewhat opinionated choice: if a FragmentDefinition ends\n                    // up having no fields other than __typename, we remove the whole\n                    // fragment definition, and later prune ...spread references to it.\n                    getInUseByFragmentName(node.name.value).removed = true;\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n            },\n        },\n        Directive: {\n            leave: function (node) {\n                // If a matching directive is found, remove the directive itself. Note\n                // that this does not remove the target (field, argument, etc) of the\n                // directive, but only the directive itself.\n                if (directiveMatcher(node)) {\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n            },\n        },\n    });\n    if (!firstVisitMadeChanges) {\n        // If our first pass did not change anything about the document, then there\n        // is no cleanup we need to do, and we can return the original doc.\n        return doc;\n    }\n    // Utility for making sure inUse.transitiveVars is recursively populated.\n    // Because this logic assumes inUse.fragmentSpreads has been completely\n    // populated and inUse.removed has been set if appropriate,\n    // populateTransitiveVars must be called after that information has been\n    // collected by the first traversal of the document.\n    var populateTransitiveVars = function (inUse) {\n        if (!inUse.transitiveVars) {\n            inUse.transitiveVars = new Set(inUse.variables);\n            if (!inUse.removed) {\n                inUse.fragmentSpreads.forEach(function (childFragmentName) {\n                    populateTransitiveVars(getInUseByFragmentName(childFragmentName)).transitiveVars.forEach(function (varName) {\n                        inUse.transitiveVars.add(varName);\n                    });\n                });\n            }\n        }\n        return inUse;\n    };\n    // Since we've been keeping track of fragment spreads used by particular\n    // operations and fragment definitions, we now need to compute the set of all\n    // spreads used (transitively) by any operations in the document.\n    var allFragmentNamesUsed = new Set();\n    docWithoutDirectiveSubtrees.definitions.forEach(function (def) {\n        if (def.kind === Kind.OPERATION_DEFINITION) {\n            populateTransitiveVars(getInUseByOperationName(def.name && def.name.value)).fragmentSpreads.forEach(function (childFragmentName) {\n                allFragmentNamesUsed.add(childFragmentName);\n            });\n        }\n        else if (def.kind === Kind.FRAGMENT_DEFINITION &&\n            // If there are no operations in the document, then all fragment\n            // definitions count as usages of their own fragment names. This heuristic\n            // prevents accidentally removing all fragment definitions from the\n            // document just because it contains no operations that use the fragments.\n            operationCount === 0 &&\n            !getInUseByFragmentName(def.name.value).removed) {\n            allFragmentNamesUsed.add(def.name.value);\n        }\n    });\n    // Now that we have added all fragment spreads used by operations to the\n    // allFragmentNamesUsed set, we can complete the set by transitively adding\n    // all fragment spreads used by those fragments, and so on.\n    allFragmentNamesUsed.forEach(function (fragmentName) {\n        // Once all the childFragmentName strings added here have been seen already,\n        // the top-level allFragmentNamesUsed.forEach loop will terminate.\n        populateTransitiveVars(getInUseByFragmentName(fragmentName)).fragmentSpreads.forEach(function (childFragmentName) {\n            allFragmentNamesUsed.add(childFragmentName);\n        });\n    });\n    var fragmentWillBeRemoved = function (fragmentName) {\n        return !!(\n        // A fragment definition will be removed if there are no spreads that refer\n        // to it, or the fragment was explicitly removed because it had no fields\n        // other than __typename.\n        (!allFragmentNamesUsed.has(fragmentName) ||\n            getInUseByFragmentName(fragmentName).removed));\n    };\n    var enterVisitor = {\n        enter: function (node) {\n            if (fragmentWillBeRemoved(node.name.value)) {\n                return null;\n            }\n        },\n    };\n    return nullIfDocIsEmpty(visit(docWithoutDirectiveSubtrees, {\n        // If the fragment is going to be removed, then leaving any dangling\n        // FragmentSpread nodes with the same name would be a mistake.\n        FragmentSpread: enterVisitor,\n        // This is where the fragment definition is actually removed.\n        FragmentDefinition: enterVisitor,\n        OperationDefinition: {\n            leave: function (node) {\n                // Upon leaving each operation in the depth-first AST traversal, prune\n                // any variables that are declared by the operation but unused within.\n                if (node.variableDefinitions) {\n                    var usedVariableNames_1 = populateTransitiveVars(\n                    // If an operation is anonymous, we use the empty string as its key.\n                    getInUseByOperationName(node.name && node.name.value)).transitiveVars;\n                    // According to the GraphQL spec, all variables declared by an\n                    // operation must either be used by that operation or used by some\n                    // fragment included transitively into that operation:\n                    // https://spec.graphql.org/draft/#sec-All-Variables-Used\n                    //\n                    // To stay on the right side of this validation rule, if/when we\n                    // remove the last $var references from an operation or its fragments,\n                    // we must also remove the corresponding $var declaration from the\n                    // enclosing operation. This pruning applies only to operations and\n                    // not fragment definitions, at the moment. Fragments may be able to\n                    // declare variables eventually, but today they can only consume them.\n                    if (usedVariableNames_1.size < node.variableDefinitions.length) {\n                        return __assign(__assign({}, node), { variableDefinitions: node.variableDefinitions.filter(function (varDef) {\n                                return usedVariableNames_1.has(varDef.variable.name.value);\n                            }) });\n                    }\n                }\n            },\n        },\n    }));\n}\nexport var addTypenameToDocument = Object.assign(function (doc) {\n    return visit(doc, {\n        SelectionSet: {\n            enter: function (node, _key, parent) {\n                // Don't add __typename to OperationDefinitions.\n                if (parent &&\n                    parent.kind ===\n                        Kind.OPERATION_DEFINITION) {\n                    return;\n                }\n                // No changes if no selections.\n                var selections = node.selections;\n                if (!selections) {\n                    return;\n                }\n                // If selections already have a __typename, or are part of an\n                // introspection query, do nothing.\n                var skip = selections.some(function (selection) {\n                    return (isField(selection) &&\n                        (selection.name.value === \"__typename\" ||\n                            selection.name.value.lastIndexOf(\"__\", 0) === 0));\n                });\n                if (skip) {\n                    return;\n                }\n                // If this SelectionSet is @export-ed as an input variable, it should\n                // not have a __typename field (see issue #4691).\n                var field = parent;\n                if (isField(field) &&\n                    field.directives &&\n                    field.directives.some(function (d) { return d.name.value === \"export\"; })) {\n                    return;\n                }\n                // Create and return a new SelectionSet with a __typename Field.\n                return __assign(__assign({}, node), { selections: __spreadArray(__spreadArray([], selections, true), [TYPENAME_FIELD], false) });\n            },\n        },\n    });\n}, {\n    added: function (field) {\n        return field === TYPENAME_FIELD;\n    },\n});\nvar connectionRemoveConfig = {\n    test: function (directive) {\n        var willRemove = directive.name.value === \"connection\";\n        if (willRemove) {\n            if (!directive.arguments ||\n                !directive.arguments.some(function (arg) { return arg.name.value === \"key\"; })) {\n                globalThis.__DEV__ !== false && invariant.warn(98);\n            }\n        }\n        return willRemove;\n    },\n};\nexport function removeConnectionDirectiveFromDocument(doc) {\n    return removeDirectivesFromDocument([connectionRemoveConfig], checkDocument(doc));\n}\nfunction hasDirectivesInSelectionSet(directives, selectionSet, nestedCheck) {\n    if (nestedCheck === void 0) { nestedCheck = true; }\n    return (!!selectionSet &&\n        selectionSet.selections &&\n        selectionSet.selections.some(function (selection) {\n            return hasDirectivesInSelection(directives, selection, nestedCheck);\n        }));\n}\nfunction hasDirectivesInSelection(directives, selection, nestedCheck) {\n    if (nestedCheck === void 0) { nestedCheck = true; }\n    if (!isField(selection)) {\n        return true;\n    }\n    if (!selection.directives) {\n        return false;\n    }\n    return (selection.directives.some(getDirectiveMatcher(directives)) ||\n        (nestedCheck &&\n            hasDirectivesInSelectionSet(directives, selection.selectionSet, nestedCheck)));\n}\nfunction getArgumentMatcher(config) {\n    return function argumentMatcher(argument) {\n        return config.some(function (aConfig) {\n            return argument.value &&\n                argument.value.kind === Kind.VARIABLE &&\n                argument.value.name &&\n                (aConfig.name === argument.value.name.value ||\n                    (aConfig.test && aConfig.test(argument)));\n        });\n    };\n}\nexport function removeArgumentsFromDocument(config, doc) {\n    var argMatcher = getArgumentMatcher(config);\n    return nullIfDocIsEmpty(visit(doc, {\n        OperationDefinition: {\n            enter: function (node) {\n                return __assign(__assign({}, node), { \n                    // Remove matching top level variables definitions.\n                    variableDefinitions: node.variableDefinitions ?\n                        node.variableDefinitions.filter(function (varDef) {\n                            return !config.some(function (arg) { return arg.name === varDef.variable.name.value; });\n                        })\n                        : [] });\n            },\n        },\n        Field: {\n            enter: function (node) {\n                // If `remove` is set to true for an argument, and an argument match\n                // is found for a field, remove the field as well.\n                var shouldRemoveField = config.some(function (argConfig) { return argConfig.remove; });\n                if (shouldRemoveField) {\n                    var argMatchCount_1 = 0;\n                    if (node.arguments) {\n                        node.arguments.forEach(function (arg) {\n                            if (argMatcher(arg)) {\n                                argMatchCount_1 += 1;\n                            }\n                        });\n                    }\n                    if (argMatchCount_1 === 1) {\n                        return null;\n                    }\n                }\n            },\n        },\n        Argument: {\n            enter: function (node) {\n                // Remove all matching arguments.\n                if (argMatcher(node)) {\n                    return null;\n                }\n            },\n        },\n    }));\n}\nexport function removeFragmentSpreadFromDocument(config, doc) {\n    function enter(node) {\n        if (config.some(function (def) { return def.name === node.name.value; })) {\n            return null;\n        }\n    }\n    return nullIfDocIsEmpty(visit(doc, {\n        FragmentSpread: { enter: enter },\n        FragmentDefinition: { enter: enter },\n    }));\n}\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(document) {\n    var definition = getMainDefinition(document);\n    var definitionOperation = definition.operation;\n    if (definitionOperation === \"query\") {\n        // Already a query, so return the existing document.\n        return document;\n    }\n    // Build a new query using the selection set of the main operation.\n    var modifiedDoc = visit(document, {\n        OperationDefinition: {\n            enter: function (node) {\n                return __assign(__assign({}, node), { operation: \"query\" });\n            },\n        },\n    });\n    return modifiedDoc;\n}\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(document) {\n    checkDocument(document);\n    var modifiedDoc = removeDirectivesFromDocument([\n        {\n            test: function (directive) { return directive.name.value === \"client\"; },\n            remove: true,\n        },\n    ], document);\n    return modifiedDoc;\n}\nexport function addNonReactiveToNamedFragments(document) {\n    checkDocument(document);\n    return visit(document, {\n        FragmentSpread: function (node) {\n            var _a;\n            // Do not add `@nonreactive` if the fragment is marked with `@unmask`\n            // since we want to react to changes in this fragment.\n            if ((_a = node.directives) === null || _a === void 0 ? void 0 : _a.some(function (directive) { return directive.name.value === \"unmask\"; })) {\n                return;\n            }\n            return __assign(__assign({}, node), { directives: __spreadArray(__spreadArray([], (node.directives || []), true), [\n                    {\n                        kind: Kind.DIRECTIVE,\n                        name: { kind: Kind.NAME, value: \"nonreactive\" },\n                    },\n                ], false) });\n        },\n    });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AAC/C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,KAAK,EAAEC,IAAI,QAAQ,SAAS;AACrC,SAASC,aAAa,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,iBAAiB,QAAS,iBAAiB;AAC1I,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,EAAEC,eAAe,QAAQ,qBAAqB;AAC9D,IAAIC,cAAc,GAAG;EACjBC,IAAI,EAAEX,IAAI,CAACY,KAAK;EAChBC,IAAI,EAAE;IACFF,IAAI,EAAEX,IAAI,CAACc,IAAI;IACfC,KAAK,EAAE;EACX;AACJ,CAAC;AACD,SAASC,OAAOA,CAACC,EAAE,EAAEC,WAAW,EAAE;EAC9B,OAAQ,CAACD,EAAE,IACPA,EAAE,CAACE,YAAY,CAACC,UAAU,CAACC,KAAK,CAAC,UAAUC,SAAS,EAAE;IAClD,OAAOA,SAAS,CAACX,IAAI,KAAKX,IAAI,CAACuB,eAAe,IAC1CP,OAAO,CAACE,WAAW,CAACI,SAAS,CAACT,IAAI,CAACE,KAAK,CAAC,EAAEG,WAAW,CAAC;EAC/D,CAAC,CAAC;AACV;AACA,SAASM,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAQT,OAAO,CAACd,sBAAsB,CAACuB,GAAG,CAAC,IAAItB,qBAAqB,CAACsB,GAAG,CAAC,EAAElB,iBAAiB,CAACH,sBAAsB,CAACqB,GAAG,CAAC,CAAC,CAAC,GACtH,IAAI,GACFA,GAAG;AACb;AACA,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,IAAIC,KAAK,GAAG,IAAID,GAAG,CAAC,CAAC;EACrBF,OAAO,CAACI,OAAO,CAAC,UAAUC,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,IAAIA,SAAS,CAACnB,IAAI,EAAE;QAChBe,KAAK,CAACK,GAAG,CAACD,SAAS,CAACnB,IAAI,EAAEmB,SAAS,CAAC;MACxC,CAAC,MACI,IAAIA,SAAS,CAACE,IAAI,EAAE;QACrBJ,KAAK,CAACG,GAAG,CAACD,SAAS,CAACE,IAAI,EAAEF,SAAS,CAAC;MACxC;IACJ;EACJ,CAAC,CAAC;EACF,OAAO,UAAUA,SAAS,EAAE;IACxB,IAAIG,MAAM,GAAGP,KAAK,CAACQ,GAAG,CAACJ,SAAS,CAACnB,IAAI,CAACE,KAAK,CAAC;IAC5C,IAAI,CAACoB,MAAM,IAAIL,KAAK,CAACO,IAAI,EAAE;MACvBP,KAAK,CAACC,OAAO,CAAC,UAAUO,UAAU,EAAEJ,IAAI,EAAE;QACtC,IAAIA,IAAI,CAACF,SAAS,CAAC,EAAE;UACjBG,MAAM,GAAGG,UAAU;QACvB;MACJ,CAAC,CAAC;IACN;IACA,OAAOH,MAAM;EACjB,CAAC;AACL;AACA,SAASI,uBAAuBA,CAACC,UAAU,EAAE;EACzC,IAAIC,GAAG,GAAG,IAAIZ,GAAG,CAAC,CAAC;EACnB,OAAO,SAASa,mBAAmBA,CAACC,GAAG,EAAE;IACrC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAGH,UAAU;IAAE;IACxC,IAAII,KAAK,GAAGH,GAAG,CAACL,GAAG,CAACO,GAAG,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;MACRH,GAAG,CAACR,GAAG,CAACU,GAAG,EAAGC,KAAK,GAAG;QAClB;QACA;QACA;QACA;QACAC,SAAS,EAAE,IAAIC,GAAG,CAAC,CAAC;QACpBC,eAAe,EAAE,IAAID,GAAG,CAAC;MAC7B,CAAE,CAAC;IACP;IACA,OAAOF,KAAK;EAChB,CAAC;AACL;AACA,OAAO,SAASI,4BAA4BA,CAACC,UAAU,EAAExB,GAAG,EAAE;EAC1DxB,aAAa,CAACwB,GAAG,CAAC;EAClB;EACA;EACA;EACA;EACA,IAAIyB,uBAAuB,GAAGX,uBAAuB,CAAC,EAAE,CAAC;EACzD,IAAIY,sBAAsB,GAAGZ,uBAAuB,CAAC,EAAE,CAAC;EACxD,IAAIa,QAAQ,GAAG,SAAAA,CAAUC,SAAS,EAAE;IAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAG,KAAK,CAAC,EAAED,CAAC,GAAGD,SAAS,CAACG,MAAM,KAAKD,QAAQ,GAAGF,SAAS,CAACC,CAAC,CAAC,CAAC,EAAE,EAAEA,CAAC,EAAE;MACvF,IAAI9C,OAAO,CAAC+C,QAAQ,CAAC,EACjB;MACJ,IAAIA,QAAQ,CAAC5C,IAAI,KAAKX,IAAI,CAACyD,oBAAoB,EAAE;QAC7C;QACA,OAAOP,uBAAuB,CAACK,QAAQ,CAAC1C,IAAI,IAAI0C,QAAQ,CAAC1C,IAAI,CAACE,KAAK,CAAC;MACxE;MACA,IAAIwC,QAAQ,CAAC5C,IAAI,KAAKX,IAAI,CAAC0D,mBAAmB,EAAE;QAC5C,OAAOP,sBAAsB,CAACI,QAAQ,CAAC1C,IAAI,CAACE,KAAK,CAAC;MACtD;IACJ;IACA4C,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI9D,SAAS,CAAC+D,KAAK,CAAC,EAAE,CAAC;IACnD,OAAO,IAAI;EACf,CAAC;EACD,IAAIC,cAAc,GAAG,CAAC;EACtB,KAAK,IAAIC,CAAC,GAAGtC,GAAG,CAACuC,WAAW,CAACR,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAClD,IAAItC,GAAG,CAACuC,WAAW,CAACD,CAAC,CAAC,CAACpD,IAAI,KAAKX,IAAI,CAACyD,oBAAoB,EAAE;MACvD,EAAEK,cAAc;IACpB;EACJ;EACA,IAAIG,gBAAgB,GAAGvC,mBAAmB,CAACuB,UAAU,CAAC;EACtD,IAAIiB,iBAAiB,GAAG,SAAAA,CAAUC,cAAc,EAAE;IAC9C,OAAO1D,eAAe,CAAC0D,cAAc,CAAC,IAClCA,cAAc,CACT1B,GAAG,CAACwB,gBAAgB,CAAC,CACrBG,IAAI,CAAC,UAAUjC,MAAM,EAAE;MAAE,OAAOA,MAAM,IAAIA,MAAM,CAACkC,MAAM;IAAE,CAAC,CAAC;EACxE,CAAC;EACD,IAAIC,0BAA0B,GAAG,IAAIzC,GAAG,CAAC,CAAC;EAC1C;EACA;EACA;EACA;EACA;EACA,IAAI0C,qBAAqB,GAAG,KAAK;EACjC,IAAIC,4BAA4B,GAAG;IAC/BC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACnB,IAAIR,iBAAiB,CAACQ,IAAI,CAACzB,UAAU,CAAC,EAAE;QACpCsB,qBAAqB,GAAG,IAAI;QAC5B,OAAO,IAAI;MACf;IACJ;EACJ,CAAC;EACD,IAAII,2BAA2B,GAAG5E,KAAK,CAAC0B,GAAG,EAAE;IACzC;IACAmD,KAAK,EAAEJ,4BAA4B;IACnCK,cAAc,EAAEL,4BAA4B;IAC5CM,kBAAkB,EAAE;MAChBL,KAAK,EAAE,SAAAA,CAAA,EAAY;QACf;QACA;QACA;QACA;QACA,OAAO,KAAK;MAChB;IACJ,CAAC;IACDM,QAAQ,EAAE;MACNN,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAEM,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE7B,SAAS,EAAE;QACpD,IAAIT,KAAK,GAAGQ,QAAQ,CAACC,SAAS,CAAC;QAC/B,IAAIT,KAAK,EAAE;UACPA,KAAK,CAACC,SAAS,CAACsC,GAAG,CAACT,IAAI,CAAC7D,IAAI,CAACE,KAAK,CAAC;QACxC;MACJ;IACJ,CAAC;IACDqE,cAAc,EAAE;MACZX,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAEM,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE7B,SAAS,EAAE;QACpD,IAAIa,iBAAiB,CAACQ,IAAI,CAACzB,UAAU,CAAC,EAAE;UACpCsB,qBAAqB,GAAG,IAAI;UAC5B,OAAO,IAAI;QACf;QACA,IAAI3B,KAAK,GAAGQ,QAAQ,CAACC,SAAS,CAAC;QAC/B,IAAIT,KAAK,EAAE;UACPA,KAAK,CAACG,eAAe,CAACoC,GAAG,CAACT,IAAI,CAAC7D,IAAI,CAACE,KAAK,CAAC;QAC9C;QACA;QACA;QACA;QACA;QACA;MACJ;IACJ,CAAC;;IACDsE,kBAAkB,EAAE;MAChBZ,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAEM,IAAI,EAAEC,OAAO,EAAEK,IAAI,EAAE;QACxChB,0BAA0B,CAACrC,GAAG,CAACsD,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAEZ,IAAI,CAAC;MAC9D,CAAC;MACDe,KAAK,EAAE,SAAAA,CAAUf,IAAI,EAAEM,IAAI,EAAEC,OAAO,EAAEK,IAAI,EAAE;QACxC,IAAII,YAAY,GAAGpB,0BAA0B,CAAClC,GAAG,CAACmD,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;QACvE,IAAIZ,IAAI,KAAKgB,YAAY,EAAE;UACvB;UACA;UACA;UACA;UACA;UACA;UACA,OAAOhB,IAAI;QACf;QACA;QACA;QACA;QACA;QACAZ,cAAc,GAAG,CAAC,IACdY,IAAI,CAACvD,YAAY,CAACC,UAAU,CAACC,KAAK,CAAC,UAAUC,SAAS,EAAE;UACpD,OAAOA,SAAS,CAACX,IAAI,KAAKX,IAAI,CAACY,KAAK,IAChCU,SAAS,CAACT,IAAI,CAACE,KAAK,KAAK,YAAY;QAC7C,CAAC,CAAC,EAAE;UACJ;UACA;UACA;UACAoC,sBAAsB,CAACuB,IAAI,CAAC7D,IAAI,CAACE,KAAK,CAAC,CAAC4E,OAAO,GAAG,IAAI;UACtDpB,qBAAqB,GAAG,IAAI;UAC5B,OAAO,IAAI;QACf;MACJ;IACJ,CAAC;IACDqB,SAAS,EAAE;MACPH,KAAK,EAAE,SAAAA,CAAUf,IAAI,EAAE;QACnB;QACA;QACA;QACA,IAAIT,gBAAgB,CAACS,IAAI,CAAC,EAAE;UACxBH,qBAAqB,GAAG,IAAI;UAC5B,OAAO,IAAI;QACf;MACJ;IACJ;EACJ,CAAC,CAAC;EACF,IAAI,CAACA,qBAAqB,EAAE;IACxB;IACA;IACA,OAAO9C,GAAG;EACd;EACA;EACA;EACA;EACA;EACA;EACA,IAAIoE,sBAAsB,GAAG,SAAAA,CAAUjD,KAAK,EAAE;IAC1C,IAAI,CAACA,KAAK,CAACkD,cAAc,EAAE;MACvBlD,KAAK,CAACkD,cAAc,GAAG,IAAIhD,GAAG,CAACF,KAAK,CAACC,SAAS,CAAC;MAC/C,IAAI,CAACD,KAAK,CAAC+C,OAAO,EAAE;QAChB/C,KAAK,CAACG,eAAe,CAAChB,OAAO,CAAC,UAAUgE,iBAAiB,EAAE;UACvDF,sBAAsB,CAAC1C,sBAAsB,CAAC4C,iBAAiB,CAAC,CAAC,CAACD,cAAc,CAAC/D,OAAO,CAAC,UAAUiE,OAAO,EAAE;YACxGpD,KAAK,CAACkD,cAAc,CAACX,GAAG,CAACa,OAAO,CAAC;UACrC,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ;IACA,OAAOpD,KAAK;EAChB,CAAC;EACD;EACA;EACA;EACA,IAAIqD,oBAAoB,GAAG,IAAInD,GAAG,CAAC,CAAC;EACpC6B,2BAA2B,CAACX,WAAW,CAACjC,OAAO,CAAC,UAAUmE,GAAG,EAAE;IAC3D,IAAIA,GAAG,CAACvF,IAAI,KAAKX,IAAI,CAACyD,oBAAoB,EAAE;MACxCoC,sBAAsB,CAAC3C,uBAAuB,CAACgD,GAAG,CAACrF,IAAI,IAAIqF,GAAG,CAACrF,IAAI,CAACE,KAAK,CAAC,CAAC,CAACgC,eAAe,CAAChB,OAAO,CAAC,UAAUgE,iBAAiB,EAAE;QAC7HE,oBAAoB,CAACd,GAAG,CAACY,iBAAiB,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC,MACI,IAAIG,GAAG,CAACvF,IAAI,KAAKX,IAAI,CAAC0D,mBAAmB;IAC1C;IACA;IACA;IACA;IACAI,cAAc,KAAK,CAAC,IACpB,CAACX,sBAAsB,CAAC+C,GAAG,CAACrF,IAAI,CAACE,KAAK,CAAC,CAAC4E,OAAO,EAAE;MACjDM,oBAAoB,CAACd,GAAG,CAACe,GAAG,CAACrF,IAAI,CAACE,KAAK,CAAC;IAC5C;EACJ,CAAC,CAAC;EACF;EACA;EACA;EACAkF,oBAAoB,CAAClE,OAAO,CAAC,UAAUoE,YAAY,EAAE;IACjD;IACA;IACAN,sBAAsB,CAAC1C,sBAAsB,CAACgD,YAAY,CAAC,CAAC,CAACpD,eAAe,CAAChB,OAAO,CAAC,UAAUgE,iBAAiB,EAAE;MAC9GE,oBAAoB,CAACd,GAAG,CAACY,iBAAiB,CAAC;IAC/C,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAIK,qBAAqB,GAAG,SAAAA,CAAUD,YAAY,EAAE;IAChD,OAAO,CAAC;IACR;IACA;IACA;IACC,CAACF,oBAAoB,CAACI,GAAG,CAACF,YAAY,CAAC,IACpChD,sBAAsB,CAACgD,YAAY,CAAC,CAACR,OAAO,CAAE;EACtD,CAAC;EACD,IAAIW,YAAY,GAAG;IACf7B,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACnB,IAAI0B,qBAAqB,CAAC1B,IAAI,CAAC7D,IAAI,CAACE,KAAK,CAAC,EAAE;QACxC,OAAO,IAAI;MACf;IACJ;EACJ,CAAC;EACD,OAAOS,gBAAgB,CAACzB,KAAK,CAAC4E,2BAA2B,EAAE;IACvD;IACA;IACAS,cAAc,EAAEkB,YAAY;IAC5B;IACAjB,kBAAkB,EAAEiB,YAAY;IAChCC,mBAAmB,EAAE;MACjBd,KAAK,EAAE,SAAAA,CAAUf,IAAI,EAAE;QACnB;QACA;QACA,IAAIA,IAAI,CAAC8B,mBAAmB,EAAE;UAC1B,IAAIC,mBAAmB,GAAGZ,sBAAsB;UAChD;UACA3C,uBAAuB,CAACwB,IAAI,CAAC7D,IAAI,IAAI6D,IAAI,CAAC7D,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC+E,cAAc;UACrE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIW,mBAAmB,CAACpE,IAAI,GAAGqC,IAAI,CAAC8B,mBAAmB,CAAChD,MAAM,EAAE;YAC5D,OAAO5D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;cAAE8B,mBAAmB,EAAE9B,IAAI,CAAC8B,mBAAmB,CAACE,MAAM,CAAC,UAAUC,MAAM,EAAE;gBACrG,OAAOF,mBAAmB,CAACJ,GAAG,CAACM,MAAM,CAACC,QAAQ,CAAC/F,IAAI,CAACE,KAAK,CAAC;cAC9D,CAAC;YAAE,CAAC,CAAC;UACb;QACJ;MACJ;IACJ;EACJ,CAAC,CAAC,CAAC;AACP;AACA,OAAO,IAAI8F,qBAAqB,GAAGC,MAAM,CAACC,MAAM,CAAC,UAAUtF,GAAG,EAAE;EAC5D,OAAO1B,KAAK,CAAC0B,GAAG,EAAE;IACduF,YAAY,EAAE;MACVvC,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAEM,IAAI,EAAEiC,MAAM,EAAE;QACjC;QACA,IAAIA,MAAM,IACNA,MAAM,CAACtG,IAAI,KACPX,IAAI,CAACyD,oBAAoB,EAAE;UAC/B;QACJ;QACA;QACA,IAAIrC,UAAU,GAAGsD,IAAI,CAACtD,UAAU;QAChC,IAAI,CAACA,UAAU,EAAE;UACb;QACJ;QACA;QACA;QACA,IAAI8F,IAAI,GAAG9F,UAAU,CAACgD,IAAI,CAAC,UAAU9C,SAAS,EAAE;UAC5C,OAAQhB,OAAO,CAACgB,SAAS,CAAC,KACrBA,SAAS,CAACT,IAAI,CAACE,KAAK,KAAK,YAAY,IAClCO,SAAS,CAACT,IAAI,CAACE,KAAK,CAACoG,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC;QACF,IAAID,IAAI,EAAE;UACN;QACJ;QACA;QACA;QACA,IAAIE,KAAK,GAAGH,MAAM;QAClB,IAAI3G,OAAO,CAAC8G,KAAK,CAAC,IACdA,KAAK,CAACnE,UAAU,IAChBmE,KAAK,CAACnE,UAAU,CAACmB,IAAI,CAAC,UAAUiD,CAAC,EAAE;UAAE,OAAOA,CAAC,CAACxG,IAAI,CAACE,KAAK,KAAK,QAAQ;QAAE,CAAC,CAAC,EAAE;UAC3E;QACJ;QACA;QACA,OAAOnB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;UAAEtD,UAAU,EAAEvB,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEuB,UAAU,EAAE,IAAI,CAAC,EAAE,CAACV,cAAc,CAAC,EAAE,KAAK;QAAE,CAAC,CAAC;MACpI;IACJ;EACJ,CAAC,CAAC;AACN,CAAC,EAAE;EACC4G,KAAK,EAAE,SAAAA,CAAUF,KAAK,EAAE;IACpB,OAAOA,KAAK,KAAK1G,cAAc;EACnC;AACJ,CAAC,CAAC;AACF,IAAI6G,sBAAsB,GAAG;EACzBrF,IAAI,EAAE,SAAAA,CAAUF,SAAS,EAAE;IACvB,IAAIwF,UAAU,GAAGxF,SAAS,CAACnB,IAAI,CAACE,KAAK,KAAK,YAAY;IACtD,IAAIyG,UAAU,EAAE;MACZ,IAAI,CAACxF,SAAS,CAACyF,SAAS,IACpB,CAACzF,SAAS,CAACyF,SAAS,CAACrD,IAAI,CAAC,UAAUsD,GAAG,EAAE;QAAE,OAAOA,GAAG,CAAC7G,IAAI,CAACE,KAAK,KAAK,KAAK;MAAE,CAAC,CAAC,EAAE;QAChF4C,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI9D,SAAS,CAAC6H,IAAI,CAAC,EAAE,CAAC;MACtD;IACJ;IACA,OAAOH,UAAU;EACrB;AACJ,CAAC;AACD,OAAO,SAASI,qCAAqCA,CAACnG,GAAG,EAAE;EACvD,OAAOuB,4BAA4B,CAAC,CAACuE,sBAAsB,CAAC,EAAEtH,aAAa,CAACwB,GAAG,CAAC,CAAC;AACrF;AACA,SAASoG,2BAA2BA,CAAC5E,UAAU,EAAE9B,YAAY,EAAE2G,WAAW,EAAE;EACxE,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,IAAI;EAAE;EAClD,OAAQ,CAAC,CAAC3G,YAAY,IAClBA,YAAY,CAACC,UAAU,IACvBD,YAAY,CAACC,UAAU,CAACgD,IAAI,CAAC,UAAU9C,SAAS,EAAE;IAC9C,OAAOyG,wBAAwB,CAAC9E,UAAU,EAAE3B,SAAS,EAAEwG,WAAW,CAAC;EACvE,CAAC,CAAC;AACV;AACA,SAASC,wBAAwBA,CAAC9E,UAAU,EAAE3B,SAAS,EAAEwG,WAAW,EAAE;EAClE,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,IAAI;EAAE;EAClD,IAAI,CAACxH,OAAO,CAACgB,SAAS,CAAC,EAAE;IACrB,OAAO,IAAI;EACf;EACA,IAAI,CAACA,SAAS,CAAC2B,UAAU,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,OAAQ3B,SAAS,CAAC2B,UAAU,CAACmB,IAAI,CAAC1C,mBAAmB,CAACuB,UAAU,CAAC,CAAC,IAC7D6E,WAAW,IACRD,2BAA2B,CAAC5E,UAAU,EAAE3B,SAAS,CAACH,YAAY,EAAE2G,WAAW,CAAE;AACzF;AACA,SAASE,kBAAkBA,CAAC7F,MAAM,EAAE;EAChC,OAAO,SAAS8F,eAAeA,CAACC,QAAQ,EAAE;IACtC,OAAO/F,MAAM,CAACiC,IAAI,CAAC,UAAU+D,OAAO,EAAE;MAClC,OAAOD,QAAQ,CAACnH,KAAK,IACjBmH,QAAQ,CAACnH,KAAK,CAACJ,IAAI,KAAKX,IAAI,CAACoI,QAAQ,IACrCF,QAAQ,CAACnH,KAAK,CAACF,IAAI,KAClBsH,OAAO,CAACtH,IAAI,KAAKqH,QAAQ,CAACnH,KAAK,CAACF,IAAI,CAACE,KAAK,IACtCoH,OAAO,CAACjG,IAAI,IAAIiG,OAAO,CAACjG,IAAI,CAACgG,QAAQ,CAAE,CAAC;IACrD,CAAC,CAAC;EACN,CAAC;AACL;AACA,OAAO,SAASG,2BAA2BA,CAAClG,MAAM,EAAEV,GAAG,EAAE;EACrD,IAAI6G,UAAU,GAAGN,kBAAkB,CAAC7F,MAAM,CAAC;EAC3C,OAAOX,gBAAgB,CAACzB,KAAK,CAAC0B,GAAG,EAAE;IAC/B8E,mBAAmB,EAAE;MACjB9B,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACnB,OAAO9E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;UAChC;UACA8B,mBAAmB,EAAE9B,IAAI,CAAC8B,mBAAmB,GACzC9B,IAAI,CAAC8B,mBAAmB,CAACE,MAAM,CAAC,UAAUC,MAAM,EAAE;YAC9C,OAAO,CAACxE,MAAM,CAACiC,IAAI,CAAC,UAAUsD,GAAG,EAAE;cAAE,OAAOA,GAAG,CAAC7G,IAAI,KAAK8F,MAAM,CAACC,QAAQ,CAAC/F,IAAI,CAACE,KAAK;YAAE,CAAC,CAAC;UAC3F,CAAC,CAAC,GACA;QAAG,CAAC,CAAC;MACnB;IACJ,CAAC;IACD6D,KAAK,EAAE;MACHH,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACnB;QACA;QACA,IAAIR,iBAAiB,GAAG/B,MAAM,CAACiC,IAAI,CAAC,UAAUmE,SAAS,EAAE;UAAE,OAAOA,SAAS,CAAClE,MAAM;QAAE,CAAC,CAAC;QACtF,IAAIH,iBAAiB,EAAE;UACnB,IAAIsE,eAAe,GAAG,CAAC;UACvB,IAAI9D,IAAI,CAAC+C,SAAS,EAAE;YAChB/C,IAAI,CAAC+C,SAAS,CAAC1F,OAAO,CAAC,UAAU2F,GAAG,EAAE;cAClC,IAAIY,UAAU,CAACZ,GAAG,CAAC,EAAE;gBACjBc,eAAe,IAAI,CAAC;cACxB;YACJ,CAAC,CAAC;UACN;UACA,IAAIA,eAAe,KAAK,CAAC,EAAE;YACvB,OAAO,IAAI;UACf;QACJ;MACJ;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNhE,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACnB;QACA,IAAI4D,UAAU,CAAC5D,IAAI,CAAC,EAAE;UAClB,OAAO,IAAI;QACf;MACJ;IACJ;EACJ,CAAC,CAAC,CAAC;AACP;AACA,OAAO,SAASgE,gCAAgCA,CAACvG,MAAM,EAAEV,GAAG,EAAE;EAC1D,SAASgD,KAAKA,CAACC,IAAI,EAAE;IACjB,IAAIvC,MAAM,CAACiC,IAAI,CAAC,UAAU8B,GAAG,EAAE;MAAE,OAAOA,GAAG,CAACrF,IAAI,KAAK6D,IAAI,CAAC7D,IAAI,CAACE,KAAK;IAAE,CAAC,CAAC,EAAE;MACtE,OAAO,IAAI;IACf;EACJ;EACA,OAAOS,gBAAgB,CAACzB,KAAK,CAAC0B,GAAG,EAAE;IAC/B2D,cAAc,EAAE;MAAEX,KAAK,EAAEA;IAAM,CAAC;IAChCY,kBAAkB,EAAE;MAAEZ,KAAK,EAAEA;IAAM;EACvC,CAAC,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA,OAAO,SAASkE,0BAA0BA,CAACC,QAAQ,EAAE;EACjD,IAAIC,UAAU,GAAGxI,iBAAiB,CAACuI,QAAQ,CAAC;EAC5C,IAAIE,mBAAmB,GAAGD,UAAU,CAACE,SAAS;EAC9C,IAAID,mBAAmB,KAAK,OAAO,EAAE;IACjC;IACA,OAAOF,QAAQ;EACnB;EACA;EACA,IAAII,WAAW,GAAGjJ,KAAK,CAAC6I,QAAQ,EAAE;IAC9BrC,mBAAmB,EAAE;MACjB9B,KAAK,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACnB,OAAO9E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;UAAEqE,SAAS,EAAE;QAAQ,CAAC,CAAC;MAC/D;IACJ;EACJ,CAAC,CAAC;EACF,OAAOC,WAAW;AACtB;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACL,QAAQ,EAAE;EACnD3I,aAAa,CAAC2I,QAAQ,CAAC;EACvB,IAAII,WAAW,GAAGhG,4BAA4B,CAAC,CAC3C;IACId,IAAI,EAAE,SAAAA,CAAUF,SAAS,EAAE;MAAE,OAAOA,SAAS,CAACnB,IAAI,CAACE,KAAK,KAAK,QAAQ;IAAE,CAAC;IACxEsD,MAAM,EAAE;EACZ,CAAC,CACJ,EAAEuE,QAAQ,CAAC;EACZ,OAAOI,WAAW;AACtB;AACA,OAAO,SAASE,8BAA8BA,CAACN,QAAQ,EAAE;EACrD3I,aAAa,CAAC2I,QAAQ,CAAC;EACvB,OAAO7I,KAAK,CAAC6I,QAAQ,EAAE;IACnBxD,cAAc,EAAE,SAAAA,CAAUV,IAAI,EAAE;MAC5B,IAAIyE,EAAE;MACN;MACA;MACA,IAAI,CAACA,EAAE,GAAGzE,IAAI,CAACzB,UAAU,MAAM,IAAI,IAAIkG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/E,IAAI,CAAC,UAAUpC,SAAS,EAAE;QAAE,OAAOA,SAAS,CAACnB,IAAI,CAACE,KAAK,KAAK,QAAQ;MAAE,CAAC,CAAC,EAAE;QACzI;MACJ;MACA,OAAOnB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8E,IAAI,CAAC,EAAE;QAAEzB,UAAU,EAAEpD,aAAa,CAACA,aAAa,CAAC,EAAE,EAAG6E,IAAI,CAACzB,UAAU,IAAI,EAAE,EAAG,IAAI,CAAC,EAAE,CAC1G;UACItC,IAAI,EAAEX,IAAI,CAACoJ,SAAS;UACpBvI,IAAI,EAAE;YAAEF,IAAI,EAAEX,IAAI,CAACc,IAAI;YAAEC,KAAK,EAAE;UAAc;QAClD,CAAC,CACJ,EAAE,KAAK;MAAE,CAAC,CAAC;IACpB;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}