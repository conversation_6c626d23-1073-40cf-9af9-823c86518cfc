{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { OperationBatcher } from \"./batching.js\";\nexport { OperationBatcher } from \"./batching.js\";\nvar BatchLink = /** @class */function (_super) {\n  __extends(BatchLink, _super);\n  function BatchLink(fetchParams) {\n    var _this = _super.call(this) || this;\n    var _a = fetchParams || {},\n      batchDebounce = _a.batchDebounce,\n      _b = _a.batchInterval,\n      batchInterval = _b === void 0 ? 10 : _b,\n      _c = _a.batchMax,\n      batchMax = _c === void 0 ? 0 : _c,\n      _d = _a.batchHandler,\n      batchHandler = _d === void 0 ? function () {\n        return null;\n      } : _d,\n      _e = _a.batchKey,\n      batchKey = _e === void 0 ? function () {\n        return \"\";\n      } : _e;\n    _this.batcher = new OperationBatcher({\n      batchDebounce: batchDebounce,\n      batchInterval: batchInterval,\n      batchMax: batchMax,\n      batchHandler: batch<PERSON><PERSON><PERSON>,\n      batchKey: batchKey\n    });\n    //make this link terminating\n    if (fetchParams.batchHandler.length <= 1) {\n      _this.request = function (operation) {\n        return _this.batcher.enqueueRequest({\n          operation: operation\n        });\n      };\n    }\n    return _this;\n  }\n  BatchLink.prototype.request = function (operation, forward) {\n    return this.batcher.enqueueRequest({\n      operation: operation,\n      forward: forward\n    });\n  };\n  return BatchLink;\n}(ApolloLink);\nexport { BatchLink };", "map": {"version": 3, "names": ["__extends", "ApolloLink", "OperationBatcher", "BatchLink", "_super", "fetchParams", "_this", "call", "_a", "batchDebounce", "_b", "batchInterval", "_c", "batchMax", "_d", "<PERSON><PERSON><PERSON><PERSON>", "_e", "<PERSON><PERSON><PERSON>", "batcher", "length", "request", "operation", "enqueueRequest", "prototype", "forward"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/batch/batchLink.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { OperationBatcher } from \"./batching.js\";\nexport { OperationBatcher } from \"./batching.js\";\nvar BatchLink = /** @class */ (function (_super) {\n    __extends(BatchLink, _super);\n    function BatchLink(fetchParams) {\n        var _this = _super.call(this) || this;\n        var _a = fetchParams || {}, batchDebounce = _a.batchDebounce, _b = _a.batchInterval, batchInterval = _b === void 0 ? 10 : _b, _c = _a.batchMax, batchMax = _c === void 0 ? 0 : _c, _d = _a.batchHandler, batchHandler = _d === void 0 ? function () { return null; } : _d, _e = _a.batchKey, batchKey = _e === void 0 ? function () { return \"\"; } : _e;\n        _this.batcher = new OperationBatcher({\n            batchDebounce: batchDebounce,\n            batchInterval: batchInterval,\n            batchMax: batchMax,\n            batchHandler: batch<PERSON><PERSON><PERSON>,\n            batchKey: batchKey,\n        });\n        //make this link terminating\n        if (fetchParams.batchHandler.length <= 1) {\n            _this.request = function (operation) { return _this.batcher.enqueueRequest({ operation: operation }); };\n        }\n        return _this;\n    }\n    BatchLink.prototype.request = function (operation, forward) {\n        return this.batcher.enqueueRequest({\n            operation: operation,\n            forward: forward,\n        });\n    };\n    return BatchLink;\n}(ApolloLink));\nexport { BatchLink };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASA,gBAAgB,QAAQ,eAAe;AAChD,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7CJ,SAAS,CAACG,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,WAAW,EAAE;IAC5B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC,IAAIC,EAAE,GAAGH,WAAW,IAAI,CAAC,CAAC;MAAEI,aAAa,GAAGD,EAAE,CAACC,aAAa;MAAEC,EAAE,GAAGF,EAAE,CAACG,aAAa;MAAEA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;MAAEE,EAAE,GAAGJ,EAAE,CAACK,QAAQ;MAAEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;MAAEE,EAAE,GAAGN,EAAE,CAACO,YAAY;MAAEA,YAAY,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY;QAAE,OAAO,IAAI;MAAE,CAAC,GAAGA,EAAE;MAAEE,EAAE,GAAGR,EAAE,CAACS,QAAQ;MAAEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY;QAAE,OAAO,EAAE;MAAE,CAAC,GAAGA,EAAE;IACvVV,KAAK,CAACY,OAAO,GAAG,IAAIhB,gBAAgB,CAAC;MACjCO,aAAa,EAAEA,aAAa;MAC5BE,aAAa,EAAEA,aAAa;MAC5BE,QAAQ,EAAEA,QAAQ;MAClBE,YAAY,EAAEA,YAAY;MAC1BE,QAAQ,EAAEA;IACd,CAAC,CAAC;IACF;IACA,IAAIZ,WAAW,CAACU,YAAY,CAACI,MAAM,IAAI,CAAC,EAAE;MACtCb,KAAK,CAACc,OAAO,GAAG,UAAUC,SAAS,EAAE;QAAE,OAAOf,KAAK,CAACY,OAAO,CAACI,cAAc,CAAC;UAAED,SAAS,EAAEA;QAAU,CAAC,CAAC;MAAE,CAAC;IAC3G;IACA,OAAOf,KAAK;EAChB;EACAH,SAAS,CAACoB,SAAS,CAACH,OAAO,GAAG,UAAUC,SAAS,EAAEG,OAAO,EAAE;IACxD,OAAO,IAAI,CAACN,OAAO,CAACI,cAAc,CAAC;MAC/BD,SAAS,EAAEA,SAAS;MACpBG,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;EACD,OAAOrB,SAAS;AACpB,CAAC,CAACF,UAAU,CAAE;AACd,SAASE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}