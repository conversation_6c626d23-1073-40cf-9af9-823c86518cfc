{"ast": null, "code": "import { invariant } from '../jsutils/invariant.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { executeSync } from '../execution/execute.mjs';\nimport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n/**\n * Build an IntrospectionQuery from a GraphQLSchema\n *\n * IntrospectionQuery is useful for utilities that care about type and field\n * relationships, but do not need to traverse through those relationships.\n *\n * This is the inverse of buildClientSchema. The primary use case is outside\n * of the server context, for instance when doing schema comparisons.\n */\n\nexport function introspectionFromSchema(schema, options) {\n  const optionsWithDefaults = {\n    specifiedByUrl: true,\n    directiveIsRepeatable: true,\n    schemaDescription: true,\n    inputValueDeprecation: true,\n    ...options\n  };\n  const document = parse(getIntrospectionQuery(optionsWithDefaults));\n  const result = executeSync({\n    schema,\n    document\n  });\n  !result.errors && result.data || invariant(false);\n  return result.data;\n}", "map": {"version": 3, "names": ["invariant", "parse", "executeSync", "getIntrospectionQuery", "introspectionFromSchema", "schema", "options", "optionsWithDefaults", "specifiedByUrl", "directiveIsRepeatable", "schemaDescription", "inputValueDeprecation", "document", "result", "errors", "data"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/utilities/introspectionFromSchema.mjs"], "sourcesContent": ["import { invariant } from '../jsutils/invariant.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { executeSync } from '../execution/execute.mjs';\nimport { getIntrospectionQuery } from './getIntrospectionQuery.mjs';\n/**\n * Build an IntrospectionQuery from a GraphQLSchema\n *\n * IntrospectionQuery is useful for utilities that care about type and field\n * relationships, but do not need to traverse through those relationships.\n *\n * This is the inverse of buildClientSchema. The primary use case is outside\n * of the server context, for instance when doing schema comparisons.\n */\n\nexport function introspectionFromSchema(schema, options) {\n  const optionsWithDefaults = {\n    specifiedByUrl: true,\n    directiveIsRepeatable: true,\n    schemaDescription: true,\n    inputValueDeprecation: true,\n    ...options,\n  };\n  const document = parse(getIntrospectionQuery(optionsWithDefaults));\n  const result = executeSync({\n    schema,\n    document,\n  });\n  (!result.errors && result.data) || invariant(false);\n  return result.data;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,uBAAuBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACvD,MAAMC,mBAAmB,GAAG;IAC1BC,cAAc,EAAE,IAAI;IACpBC,qBAAqB,EAAE,IAAI;IAC3BC,iBAAiB,EAAE,IAAI;IACvBC,qBAAqB,EAAE,IAAI;IAC3B,GAAGL;EACL,CAAC;EACD,MAAMM,QAAQ,GAAGX,KAAK,CAACE,qBAAqB,CAACI,mBAAmB,CAAC,CAAC;EAClE,MAAMM,MAAM,GAAGX,WAAW,CAAC;IACzBG,MAAM;IACNO;EACF,CAAC,CAAC;EACD,CAACC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACE,IAAI,IAAKf,SAAS,CAAC,KAAK,CAAC;EACnD,OAAOa,MAAM,CAACE,IAAI;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}