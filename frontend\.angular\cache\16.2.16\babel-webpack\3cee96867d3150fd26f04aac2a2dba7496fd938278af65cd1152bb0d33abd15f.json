{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RendusService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.urlBackend}rendus`;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Soumettre un nouveau rendu\n  submitRendu(renduData) {\n    return this.http.post(`${this.apiUrl}/submit`, renduData);\n  }\n  // Vérifier si un étudiant a déjà soumis un rendu pour un projet\n  checkRenduExists(projetId, etudiantId) {\n    return this.http.get(`${this.apiUrl}/check/${projetId}/${etudiantId}`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Récupérer tous les rendus\n  getAllRendus() {\n    return this.http.get(this.apiUrl, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error('Erreur lors de la récupération des rendus:', error);\n      return of([]);\n    }));\n  }\n  // Récupérer un rendu par son ID\n  getRenduById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Récupérer les rendus par projet\n  getRendusByProjet(projetId) {\n    return this.http.get(`${this.apiUrl}/projet/${projetId}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error('Erreur lors de la récupération des rendus par projet:', error);\n      return of([]);\n    }));\n  }\n  // Évaluer un rendu (manuellement ou via IA)\n  evaluateRendu(renduId, evaluationData) {\n    return this.http.post(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {\n      headers: this.getHeaders()\n    });\n  }\n  // Mettre à jour une évaluation existante\n  updateEvaluation(renduId, evaluationData) {\n    return this.http.put(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {\n      headers: this.getHeaders()\n    });\n  }\n  static {\n    this.ɵfac = function RendusService_Factory(t) {\n      return new (t || RendusService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RendusService,\n      factory: RendusService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "of", "catchError", "environment", "RendusService", "constructor", "http", "apiUrl", "urlBackend", "getHeaders", "token", "localStorage", "getItem", "submitRendu", "renduData", "post", "checkRenduExists", "projetId", "etudiantId", "get", "headers", "getAllRendus", "pipe", "error", "console", "getRenduById", "id", "getRendusByProjet", "evaluateRendu", "renduId", "evaluationData", "updateEvaluation", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\rendus.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable, of } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RendusService {\r\n  private apiUrl = `${environment.urlBackend}rendus`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  // Soumettre un nouveau rendu\r\n  submitRendu(renduData: FormData): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/submit`, renduData);\r\n  }\r\n\r\n  // Vérifier si un étudiant a déjà soumis un rendu pour un projet\r\n  checkRenduExists(projetId: string, etudiantId: string): Observable<any> {\r\n    return this.http.get(`${this.apiUrl}/check/${projetId}/${etudiantId}`, { headers: this.getHeaders() });\r\n  }\r\n\r\n  // Récupérer tous les rendus\r\n  getAllRendus(): Observable<any[]> {\r\n    return this.http.get<any[]>(this.apiUrl, { headers: this.getHeaders() })\r\n      .pipe(\r\n        catchError(error => {\r\n          console.error('Erreur lors de la récupération des rendus:', error);\r\n          return of([]);\r\n        })\r\n      );\r\n  }\r\n\r\n  // Récupérer un rendu par son ID\r\n  getRenduById(id: string): Observable<any> {\r\n    return this.http.get<any>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() });\r\n  }\r\n\r\n  // Récupérer les rendus par projet\r\n  getRendusByProjet(projetId: string): Observable<any[]> {\r\n    return this.http.get<any[]>(`${this.apiUrl}/projet/${projetId}`, { headers: this.getHeaders() })\r\n      .pipe(\r\n        catchError(error => {\r\n          console.error('Erreur lors de la récupération des rendus par projet:', error);\r\n          return of([]);\r\n        })\r\n      );\r\n  }\r\n\r\n  // Évaluer un rendu (manuellement ou via IA)\r\n  evaluateRendu(renduId: string, evaluationData: any): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, { headers: this.getHeaders() });\r\n  }\r\n\r\n  // Mettre à jour une évaluation existante\r\n  updateEvaluation(renduId: string, evaluationData: any): Observable<any> {\r\n    return this.http.put(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, { headers: this.getHeaders() });\r\n  }\r\n}\r\n\r\n\r\n\r\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,UAAU,QAAQ;EAEV;EAEhCC,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,OAAO,IAAIZ,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUU,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAG,WAAWA,CAACC,SAAmB;IAC7B,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACR,MAAM,SAAS,EAAEO,SAAS,CAAC;EAC3D;EAEA;EACAE,gBAAgBA,CAACC,QAAgB,EAAEC,UAAkB;IACnD,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,GAAG,IAAI,CAACZ,MAAM,UAAUU,QAAQ,IAAIC,UAAU,EAAE,EAAE;MAAEE,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC;EACxG;EAEA;EACAY,YAAYA,CAAA;IACV,OAAO,IAAI,CAACf,IAAI,CAACa,GAAG,CAAQ,IAAI,CAACZ,MAAM,EAAE;MAAEa,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CACrEa,IAAI,CACHpB,UAAU,CAACqB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,OAAOtB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;EACAwB,YAAYA,CAACC,EAAU;IACrB,OAAO,IAAI,CAACpB,IAAI,CAACa,GAAG,CAAM,GAAG,IAAI,CAACZ,MAAM,IAAImB,EAAE,EAAE,EAAE;MAAEN,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC;EACnF;EAEA;EACAkB,iBAAiBA,CAACV,QAAgB;IAChC,OAAO,IAAI,CAACX,IAAI,CAACa,GAAG,CAAQ,GAAG,IAAI,CAACZ,MAAM,WAAWU,QAAQ,EAAE,EAAE;MAAEG,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CAC7Fa,IAAI,CACHpB,UAAU,CAACqB,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,uDAAuD,EAAEA,KAAK,CAAC;MAC7E,OAAOtB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEA;EACA2B,aAAaA,CAACC,OAAe,EAAEC,cAAmB;IAChD,OAAO,IAAI,CAACxB,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI,CAACR,MAAM,gBAAgBsB,OAAO,EAAE,EAAEC,cAAc,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC;EAChH;EAEA;EACAsB,gBAAgBA,CAACF,OAAe,EAAEC,cAAmB;IACnD,OAAO,IAAI,CAACxB,IAAI,CAAC0B,GAAG,CAAC,GAAG,IAAI,CAACzB,MAAM,gBAAgBsB,OAAO,EAAE,EAAEC,cAAc,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC;EAC/G;;;uBA1DWL,aAAa,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbhC,aAAa;MAAAiC,OAAA,EAAbjC,aAAa,CAAAkC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}