{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"div\", 19)(3, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"i\", 24)(4, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 26);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProfileComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 22)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\", 30)(4, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 31);\n    i0.ɵɵtext(7, \" Succ\\u00E8s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_26_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleEditMode());\n    });\n    i0.ɵɵtext(2, \" Complete Profile \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"div\", 33);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"h3\", 35);\n    i0.ɵɵtext(5, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 36);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37);\n    i0.ɵɵelement(9, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 27);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ProfileComponent_div_26_div_12_Template, 3, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.progressPercentage, \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.progressPercentage, \"%\")(\"background-color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getMotivationalMessage(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 103);\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r9.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_27_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 104);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r10.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_27_button_32_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction ProfileComponent_div_27_button_32__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 112);\n    i0.ɵɵelement(1, \"circle\", 113)(2, \"path\", 114);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onUpload());\n    });\n    i0.ɵɵelement(1, \"div\", 106)(2, \"div\", 107);\n    i0.ɵɵelementStart(3, \"span\", 108);\n    i0.ɵɵtemplate(4, ProfileComponent_div_27_button_32_i_4_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(5, ProfileComponent_div_27_button_32__svg_svg_5_Template, 3, 0, \"svg\", 110);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.uploadLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.uploadLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.uploadLoading ? \"T\\u00E9l\\u00E9chargement...\" : \"T\\u00E9l\\u00E9charger\");\n  }\n}\nfunction ProfileComponent_div_27_button_33_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 118);\n  }\n}\nfunction ProfileComponent_div_27_button_33__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 112);\n    i0.ɵɵelement(1, \"circle\", 113)(2, \"path\", 114);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.removeProfileImage());\n    });\n    i0.ɵɵelement(1, \"div\", 115)(2, \"div\", 116);\n    i0.ɵɵelementStart(3, \"span\", 108);\n    i0.ɵɵtemplate(4, ProfileComponent_div_27_button_33_i_4_Template, 1, 0, \"i\", 117);\n    i0.ɵɵtemplate(5, ProfileComponent_div_27_button_33__svg_svg_5_Template, 3, 0, \"svg\", 110);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r12.removeLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.removeLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r12.removeLoading ? \"Suppression...\" : \"Supprimer\");\n  }\n}\nfunction ProfileComponent_div_27_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Pr\\u00E9nom \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.user.firstName, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Nom de famille \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.user.lastName, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Date de naissance \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(7, 1, ctx_r15.user.dateOfBirth, \"mediumDate\"), \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" T\\u00E9l\\u00E9phone \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.user.phoneNumber, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" D\\u00E9partement \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.user.department, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Position \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.user.position, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Bio \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.user.bio, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Adresse \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.user.address, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_83_span_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_83_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_div_83_span_6_span_2_Template, 2, 0, \"span\", 120);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r31 = ctx.$implicit;\n    const last_r32 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r31, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r32);\n  }\n}\nfunction ProfileComponent_div_27_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"div\", 76);\n    i0.ɵɵelementStart(3, \"div\", 77);\n    i0.ɵɵtext(4, \" Comp\\u00E9tences \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtemplate(6, ProfileComponent_div_27_div_83_span_6_Template, 3, 2, \"span\", 119);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r21.user.skills);\n  }\n}\nfunction ProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"div\", 33)(3, \"div\", 44);\n    i0.ɵɵelementStart(4, \"div\", 45)(5, \"div\", 46);\n    i0.ɵɵelement(6, \"div\", 47);\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtemplate(8, ProfileComponent_div_27_img_8_Template, 1, 1, \"img\", 49);\n    i0.ɵɵtemplate(9, ProfileComponent_div_27_img_9_Template, 1, 1, \"img\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"h2\", 51);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 52);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 53);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 40)(18, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.toggleEditMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 55);\n    i0.ɵɵelement(20, \"path\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"div\", 57)(23, \"label\", 58);\n    i0.ɵɵelement(24, \"i\", 59);\n    i0.ɵɵtext(25, \" Photo de profil \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 60)(27, \"div\", 61)(28, \"input\", 62);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_27_Template_input_change_28_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 63);\n    i0.ɵɵelement(30, \"div\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 65);\n    i0.ɵɵtemplate(32, ProfileComponent_div_27_button_32_Template, 8, 4, \"button\", 66);\n    i0.ɵɵtemplate(33, ProfileComponent_div_27_button_33_Template, 8, 4, \"button\", 66);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 67)(35, \"div\", 43);\n    i0.ɵɵelement(36, \"div\", 33)(37, \"div\", 44);\n    i0.ɵɵelementStart(38, \"h3\", 68)(39, \"div\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 70);\n    i0.ɵɵelement(41, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(42, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43, \" Informations du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 73)(45, \"div\", 74)(46, \"div\", 75);\n    i0.ɵɵelement(47, \"div\", 76);\n    i0.ɵɵelementStart(48, \"div\", 77);\n    i0.ɵɵtext(49, \" Nom complet \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 78);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 74)(53, \"div\", 75);\n    i0.ɵɵelement(54, \"div\", 76);\n    i0.ɵɵelementStart(55, \"div\", 77);\n    i0.ɵɵtext(56, \" Adresse email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 78);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 74)(60, \"div\", 75);\n    i0.ɵɵelement(61, \"div\", 76);\n    i0.ɵɵelementStart(62, \"div\", 77);\n    i0.ɵɵtext(63, \" Type de compte \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 78);\n    i0.ɵɵtext(65);\n    i0.ɵɵpipe(66, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 74)(68, \"div\", 75);\n    i0.ɵɵelement(69, \"div\", 76);\n    i0.ɵɵelementStart(70, \"div\", 77);\n    i0.ɵɵtext(71, \" Membre depuis \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 78);\n    i0.ɵɵtext(73);\n    i0.ɵɵpipe(74, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(75, ProfileComponent_div_27_div_75_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(76, ProfileComponent_div_27_div_76_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(77, ProfileComponent_div_27_div_77_Template, 8, 4, \"div\", 79);\n    i0.ɵɵtemplate(78, ProfileComponent_div_27_div_78_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(79, ProfileComponent_div_27_div_79_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(80, ProfileComponent_div_27_div_80_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(81, ProfileComponent_div_27_div_81_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(82, ProfileComponent_div_27_div_82_Template, 7, 1, \"div\", 79);\n    i0.ɵɵtemplate(83, ProfileComponent_div_27_div_83_Template, 7, 1, \"div\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"div\", 43);\n    i0.ɵɵelement(85, \"div\", 33)(86, \"div\", 44);\n    i0.ɵɵelementStart(87, \"h3\", 68)(88, \"div\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(89, \"svg\", 70);\n    i0.ɵɵelement(90, \"path\", 80)(91, \"path\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(92, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(93, \" Actions du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"div\", 82)(95, \"a\", 83);\n    i0.ɵɵelement(96, \"div\", 84)(97, \"div\", 85);\n    i0.ɵɵelementStart(98, \"span\", 86)(99, \"div\", 87);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(100, \"svg\", 88);\n    i0.ɵɵelement(101, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(102, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(103, \" Changer le mot de passe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(104, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_104_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.logout());\n    });\n    i0.ɵɵelement(105, \"div\", 92)(106, \"div\", 93);\n    i0.ɵɵelementStart(107, \"span\", 94)(108, \"div\", 87);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(109, \"svg\", 88);\n    i0.ɵɵelement(110, \"path\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(111, \"div\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(112, \" D\\u00E9connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(113, \"a\", 97);\n    i0.ɵɵelement(114, \"div\", 98)(115, \"div\", 99);\n    i0.ɵɵelementStart(116, \"span\", 100)(117, \"div\", 87);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(118, \"svg\", 88);\n    i0.ɵɵelement(119, \"path\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(120, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Tableau de bord \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.previewUrl || ctx_r4.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.previewUrl && !ctx_r4.uploadLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.email, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 22, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.isEditMode ? \"Cancel Edit\" : \"Edit Profile\", \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.profileImage || ctx_r4.user.image || ctx_r4.user.profileImageURL);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.email, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(66, 24, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(74, 26, ctx_r4.user.createdAt, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.firstName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.lastName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.dateOfBirth);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.phoneNumber);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.department);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.bio);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.address);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.skills && ctx_r4.user.skills.length > 0);\n    i0.ɵɵadvance(30);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n  }\n}\nfunction ProfileComponent_div_28_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r38.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 157);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.getFieldError(\"bio\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 158);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 159);\n    i0.ɵɵelement(2, \"circle\", 113)(3, \"path\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Saving... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"p\", 161);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r46.message);\n  }\n}\nfunction ProfileComponent_div_28_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"p\", 163);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r47.error);\n  }\n}\nfunction ProfileComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"div\", 122)(2, \"div\", 123)(3, \"div\", 124)(4, \"h2\", 125);\n    i0.ɵɵtext(5, \"Edit Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.cancelEdit());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 127);\n    i0.ɵɵelement(8, \"path\", 128);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"form\", 129);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_28_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onEditSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 130)(11, \"div\")(12, \"label\", 131);\n    i0.ɵɵtext(13, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 132);\n    i0.ɵɵtemplate(15, ProfileComponent_div_28_p_15_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"label\", 131);\n    i0.ɵɵtext(18, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 134);\n    i0.ɵɵtemplate(20, ProfileComponent_div_28_p_20_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"label\", 131);\n    i0.ɵɵtext(23, \" Full Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 135);\n    i0.ɵɵtemplate(25, ProfileComponent_div_28_p_25_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"label\", 131);\n    i0.ɵɵtext(28, \" Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 136);\n    i0.ɵɵtemplate(30, ProfileComponent_div_28_p_30_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"label\", 131);\n    i0.ɵɵtext(33, \" Date of Birth \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\")(36, \"label\", 131);\n    i0.ɵɵtext(37, \" Phone Number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 138);\n    i0.ɵɵtemplate(39, ProfileComponent_div_28_p_39_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"label\", 131);\n    i0.ɵɵtext(42, \" Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"label\", 131);\n    i0.ɵɵtext(46, \" Position \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 140);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 141)(49, \"label\", 131);\n    i0.ɵɵtext(50, \" Bio \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 142);\n    i0.ɵɵtemplate(52, ProfileComponent_div_28_p_52_Template, 2, 1, \"p\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 141)(54, \"label\", 131);\n    i0.ɵɵtext(55, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 141)(58, \"label\", 131);\n    i0.ɵɵtext(59, \" Skills \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"input\", 144);\n    i0.ɵɵelementStart(61, \"p\", 145);\n    i0.ɵɵtext(62, \" Separate skills with commas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 141)(64, \"label\", 131);\n    i0.ɵɵtext(65, \" Profile Picture \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 146)(67, \"div\", 147);\n    i0.ɵɵelement(68, \"img\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 149)(70, \"input\", 150);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_28_Template_input_change_70_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"p\", 145);\n    i0.ɵɵtext(72, \" Upload a new profile picture (optional) \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"div\", 151)(74, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.cancelEdit());\n    });\n    i0.ɵɵtext(75, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 153);\n    i0.ɵɵtemplate(77, ProfileComponent_div_28_span_77_Template, 2, 0, \"span\", 120);\n    i0.ɵɵtemplate(78, ProfileComponent_div_28_span_78_Template, 5, 0, \"span\", 154);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, ProfileComponent_div_28_div_79_Template, 3, 1, \"div\", 155);\n    i0.ɵɵtemplate(80, ProfileComponent_div_28_div_80_Template, 3, 1, \"div\", 156);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.editForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"fullName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"email\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"phoneNumber\"));\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"bio\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"bio\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl || ctx_r5.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.error);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, authuserService, dataService, router, fb) {\n    this.authService = authService;\n    this.authuserService = authuserService;\n    this.dataService = dataService;\n    this.router = router;\n    this.fb = fb;\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.message = '';\n    this.error = '';\n    this.uploadLoading = false;\n    this.removeLoading = false;\n    // Edit profile functionality\n    this.isEditMode = false;\n    this.editLoading = false;\n    this.progressPercentage = 0;\n    this.editForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      dateOfBirth: [''],\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: [''],\n      position: [''],\n      bio: ['', [Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    // Load user profile using DataService\n    this.dataService.getProfile().subscribe({\n      next: res => {\n        this.user = res;\n        // Ensure image properties are consistent\n        if (!this.user.profileImage && this.user.image) {\n          this.user.profileImage = this.user.image;\n        } else if (!this.user.image && this.user.profileImage) {\n          this.user.image = this.user.profileImage;\n        }\n        // If no image is available, use default\n        if (!this.user.profileImage || this.user.profileImage === 'null' || this.user.profileImage.trim() === '') {\n          this.user.profileImage = 'assets/images/default-profile.png';\n          this.user.image = 'assets/images/default-profile.png';\n        }\n        // Ensure profileImageURL is also set for backward compatibility\n        if (!this.user.profileImageURL) {\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\n        }\n        // Calculate profile completion percentage\n        this.calculateProfileCompletion();\n        // Populate edit form with current user data\n        this.populateEditForm();\n      },\n      error: () => {\n        this.error = 'Failed to load profile.';\n      }\n    });\n  }\n  calculateProfileCompletion() {\n    if (!this.user) return;\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      const value = this.user[field];\n      if (value && value.toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  populateEditForm() {\n    if (!this.user) return;\n    this.editForm.patchValue({\n      firstName: this.user.firstName || '',\n      lastName: this.user.lastName || '',\n      fullName: this.user.fullName || '',\n      email: this.user.email || '',\n      dateOfBirth: this.user.dateOfBirth || '',\n      phoneNumber: this.user.phoneNumber || '',\n      department: this.user.department || '',\n      position: this.user.position || '',\n      bio: this.user.bio || '',\n      address: this.user.address || '',\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : this.user.skills || ''\n    });\n  }\n  /**\n   * Returns the appropriate profile image URL based on available properties\n   * Uses the same logic as in front-layout component for consistency\n   */\n  getProfileImageUrl() {\n    if (!this.user) return 'assets/images/default-profile.png';\n    // Check profileImage first\n    if (this.user.profileImage && this.user.profileImage !== 'null' && this.user.profileImage.trim() !== '') {\n      return this.user.profileImage;\n    }\n    // Then check image\n    if (this.user.image && this.user.image !== 'null' && this.user.image.trim() !== '') {\n      return this.user.image;\n    }\n    // Then check profileImageURL (for backward compatibility)\n    if (this.user.profileImageURL && this.user.profileImageURL !== 'null' && this.user.profileImageURL.trim() !== '') {\n      return this.user.profileImageURL;\n    }\n    // Default fallback\n    return 'assets/images/default-profile.png';\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      const file = input.files[0];\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n        this.resetFileInput();\n        return;\n      }\n      if (file.size > 2 * 1024 * 1024) {\n        this.error = \"L'image ne doit pas dépasser 2MB\";\n        this.resetFileInput();\n        return;\n      }\n      this.selectedImage = file;\n      this.error = '';\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewUrl = e.target?.result || null;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onUpload() {\n    if (!this.selectedImage) return;\n    this.uploadLoading = true; // Activer l'état de chargement\n    this.message = '';\n    this.error = '';\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\n    this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n      this.uploadLoading = false;\n      console.log('Upload finished, uploadLoading:', this.uploadLoading);\n    })).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile updated successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = response.imageUrl;\n        this.user.profileImage = response.imageUrl;\n        this.user.image = response.imageUrl;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Upload failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  removeProfileImage() {\n    if (!confirm('Are you sure you want to remove your profile picture?')) return;\n    this.removeLoading = true;\n    this.message = '';\n    this.error = '';\n    this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile picture removed successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = null;\n        this.user.profileImage = null;\n        this.user.image = null;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Removal failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  resetFileInput() {\n    this.selectedImage = null;\n    this.previewUrl = null;\n    const fileInput = document.getElementById('profile-upload');\n    if (fileInput) fileInput.value = '';\n  }\n  navigateTo(path) {\n    this.router.navigate([path]);\n  }\n  // Edit profile methods\n  toggleEditMode() {\n    this.isEditMode = !this.isEditMode;\n    if (this.isEditMode) {\n      this.populateEditForm();\n    }\n    this.message = '';\n    this.error = '';\n  }\n  onEditSubmit() {\n    if (this.editForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.editLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.editForm.value).forEach(key => {\n      const value = this.editForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedImage) {\n      formData.append('image', this.selectedImage);\n    }\n    this.dataService.completeProfile(formData).subscribe({\n      next: response => {\n        this.editLoading = false;\n        this.message = 'Profile updated successfully!';\n        // Update current user data\n        this.user = {\n          ...this.user,\n          ...response.user\n        };\n        this.authuserService.setCurrentUser(this.user);\n        // Recalculate progress\n        this.calculateProfileCompletion();\n        // Exit edit mode\n        this.isEditMode = false;\n        // Clear selected image\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.editLoading = false;\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\n        // Auto-hide error after 5 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 5000);\n      }\n    });\n  }\n  cancelEdit() {\n    this.isEditMode = false;\n    this.populateEditForm(); // Reset form to original values\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.resetFileInput();\n    this.message = '';\n    this.error = '';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.editForm.controls).forEach(key => {\n      this.editForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.editForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['email']) return `Invalid email format`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.editForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Let's complete your profile to unlock all features! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making great progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Excellent! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost perfect! Just a few more details! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete! ✨\";\n    }\n  }\n  logout() {\n    this.authuserService.logout().subscribe({\n      next: () => {\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {});\n        }, 100);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 29,\n      vars: 6,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-3 gap-6\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-1\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-2xl\", \"font-bold\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-3\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"text-sm\", \"font-medium\", 3, \"click\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"relative\", \"mb-5\", \"group/avatar\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"opacity-0\", \"group-hover/avatar:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"-z-10\"], [1, \"w-28\", \"h-28\", \"rounded-full\", \"border-4\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"group-hover/avatar:border-[#4f5fad]\", \"dark:group-hover/avatar:border-[#6d78c9]\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", \"duration-300\", \"relative\", \"z-10\", 2, \"min-height\", \"112px\", \"min-width\", \"112px\"], [\"alt\", \"Profile\", \"class\", \"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Preview\", \"class\", \"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\", 3, \"src\", 4, \"ngIf\"], [1, \"text-lg\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-xs\", \"rounded-full\", \"backdrop-blur-sm\"], [1, \"w-full\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"text-sm\", \"font-medium\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"mt-6\", \"w-full\"], [\"for\", \"profile-upload\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-camera\", \"mr-1.5\"], [1, \"flex\", \"flex-wrap\", \"items-center\", \"gap-2\"], [1, \"relative\", \"w-full\", \"group/upload\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"file:mr-3\", \"file:py-1.5\", \"file:px-3\", \"file:rounded-lg\", \"file:border-0\", \"file:text-xs\", \"file:bg-[#4f5fad]\", \"dark:file:bg-[#6d78c9]\", \"file:text-white\", \"hover:file:bg-[#3d4a85]\", \"dark:hover:file:bg-[#4f5fad]\", \"file:transition-colors\", 3, \"change\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within/upload:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"gap-2\", \"w-full\", \"mt-3\"], [\"class\", \"relative overflow-hidden group/btn flex-1\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"md:col-span-2\", \"space-y-6\"], [1, \"text-base\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-4\", \"flex\", \"items-center\"], [1, \"relative\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"space-y-4\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"p-4\", \"rounded-lg\", \"backdrop-blur-sm\", \"group/item\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"mb-1\"], [1, \"w-1\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"mr-2\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"font-medium\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-3\", \"group-hover/item:translate-x-1\", \"transition-transform\"], [\"class\", \"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/change-password\", 1, \"relative\", \"overflow-hidden\", \"group/btn\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"relative\", \"mr-1.5\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"relative\", \"z-10\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff8785]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", \"transition-transform\", \"duration-300\", \"group-hover/avatar:scale-105\", 3, \"src\"], [\"alt\", \"Preview\", 1, \"h-full\", \"w-full\", \"object-cover\", \"transition-transform\", \"duration-300\", \"group-hover/avatar:scale-105\", 3, \"src\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", \"flex-1\", 3, \"disabled\", \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"fas fa-upload mr-1.5\", 4, \"ngIf\"], [\"class\", \"animate-spin mr-1.5 h-3.5 w-3.5 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"fas\", \"fa-upload\", \"mr-1.5\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-1.5\", \"h-3.5\", \"w-3.5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [\"class\", \"fas fa-trash-alt mr-1.5\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1.5\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-2xl\", \"max-w-4xl\", \"w-full\", \"max-h-[90vh]\", \"overflow-y-auto\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white\", \"hover:text-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"department\", \"placeholder\", \"e.g., Computer Science, Marketing\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Student, Professor, Developer\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"mt-6\"], [\"formControlName\", \"bio\", \"rows\", \"4\", \"placeholder\", \"Tell us about yourself, your interests, and goals...\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"resize-none\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"skills\", \"placeholder\", \"e.g., JavaScript, Python, Project Management (comma separated)\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex-shrink-0\"], [\"alt\", \"Profile preview\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", 3, \"src\"], [1, \"flex-1\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [1, \"flex\", \"justify-end\", \"space-x-4\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]\", \"hover:text-white\", \"dark:hover:bg-[#a0a0a0]\", \"dark:hover:text-black\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"h1\", 9);\n          i0.ɵɵtext(20, \" Mon Profil \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 10);\n          i0.ɵɵtext(22, \" G\\u00E9rez vos informations personnelles et vos pr\\u00E9f\\u00E9rences \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ProfileComponent_div_23_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(24, ProfileComponent_div_24_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(25, ProfileComponent_div_25_Template, 10, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, ProfileComponent_div_26_Template, 13, 9, \"div\", 14);\n          i0.ɵɵtemplate(27, ProfileComponent_div_27_Template, 122, 29, \"div\", 15);\n          i0.ɵɵtemplate(28, ProfileComponent_div_28_Template, 81, 25, \"div\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", !ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\"\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 4px solid rgba(0, 0, 0, 0.1);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  border-left-color: #09f;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.form-loading[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2ZpbGUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw4QkFBOEI7QUFDOUI7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULG9DQUFvQztFQUNwQyxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtBQUNmOztBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHVCQUF1QjtFQUN2QixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7QUFDQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtBQUN6QiIsImZpbGUiOiJwcm9maWxlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvZmlsZS9wcm9maWxlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsOEJBQThCO0FBQzlCO0VBQ0UsZUFBZTtFQUNmLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxvQ0FBb0M7RUFDcEMsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix1QkFBdUI7RUFDdkIsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGO0FBQ0E7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUEsb3FEQUFvcUQiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "ctx_r2", "message", "ɵɵlistener", "ProfileComponent_div_26_div_12_Template_button_click_1_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "toggleEditMode", "ɵɵtemplate", "ProfileComponent_div_26_div_12_Template", "ɵɵstyleProp", "ctx_r3", "getProgressColor", "progressPercentage", "getMotivationalMessage", "ɵɵproperty", "ctx_r9", "getProfileImageUrl", "ɵɵsanitizeUrl", "ctx_r10", "previewUrl", "ɵɵnamespaceSVG", "ProfileComponent_div_27_button_32_Template_button_click_0_listener", "_r25", "ctx_r24", "onUpload", "ProfileComponent_div_27_button_32_i_4_Template", "ProfileComponent_div_27_button_32__svg_svg_5_Template", "ctx_r11", "uploadLoading", "ProfileComponent_div_27_button_33_Template_button_click_0_listener", "_r29", "ctx_r28", "removeProfileImage", "ProfileComponent_div_27_button_33_i_4_Template", "ProfileComponent_div_27_button_33__svg_svg_5_Template", "ctx_r12", "removeLoading", "ctx_r13", "user", "firstName", "ctx_r14", "lastName", "ɵɵpipeBind2", "ctx_r15", "dateOfBirth", "ctx_r16", "phoneNumber", "ctx_r17", "department", "ctx_r18", "position", "ctx_r19", "bio", "ctx_r20", "address", "ProfileComponent_div_27_div_83_span_6_span_2_Template", "skill_r31", "last_r32", "ProfileComponent_div_27_div_83_span_6_Template", "ctx_r21", "skills", "ProfileComponent_div_27_img_8_Template", "ProfileComponent_div_27_img_9_Template", "ProfileComponent_div_27_Template_button_click_18_listener", "_r35", "ctx_r34", "ɵɵnamespaceHTML", "ProfileComponent_div_27_Template_input_change_28_listener", "$event", "ctx_r36", "onFileSelected", "ProfileComponent_div_27_button_32_Template", "ProfileComponent_div_27_button_33_Template", "ProfileComponent_div_27_div_75_Template", "ProfileComponent_div_27_div_76_Template", "ProfileComponent_div_27_div_77_Template", "ProfileComponent_div_27_div_78_Template", "ProfileComponent_div_27_div_79_Template", "ProfileComponent_div_27_div_80_Template", "ProfileComponent_div_27_div_81_Template", "ProfileComponent_div_27_div_82_Template", "ProfileComponent_div_27_div_83_Template", "ProfileComponent_div_27_Template_button_click_104_listener", "ctx_r37", "logout", "ctx_r4", "fullName", "email", "ɵɵpipeBind1", "role", "isEditMode", "selectedImage", "profileImage", "image", "profileImageURL", "createdAt", "length", "ctx_r38", "getFieldError", "ctx_r39", "ctx_r40", "ctx_r41", "ctx_r42", "ctx_r43", "ctx_r46", "ctx_r47", "ProfileComponent_div_28_Template_button_click_6_listener", "_r49", "ctx_r48", "cancelEdit", "ProfileComponent_div_28_Template_form_ngSubmit_9_listener", "ctx_r50", "onEditSubmit", "ProfileComponent_div_28_p_15_Template", "ProfileComponent_div_28_p_20_Template", "ProfileComponent_div_28_p_25_Template", "ProfileComponent_div_28_p_30_Template", "ProfileComponent_div_28_p_39_Template", "ProfileComponent_div_28_p_52_Template", "ProfileComponent_div_28_Template_input_change_70_listener", "ctx_r51", "ProfileComponent_div_28_Template_button_click_74_listener", "ctx_r52", "ProfileComponent_div_28_span_77_Template", "ProfileComponent_div_28_span_78_Template", "ProfileComponent_div_28_div_79_Template", "ProfileComponent_div_28_div_80_Template", "ctx_r5", "editForm", "ɵɵclassProp", "isFieldInvalid", "editLoading", "ProfileComponent", "constructor", "authService", "authuserService", "dataService", "router", "fb", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "ngOnInit", "getProfile", "subscribe", "next", "res", "trim", "calculateProfileCompletion", "populateEditForm", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "value", "toString", "hasProfileImage", "requiredPercentage", "optionalPercentage", "imagePercentage", "Math", "round", "patchValue", "Array", "isArray", "join", "event", "input", "target", "files", "file", "validTypes", "includes", "type", "resetFileInput", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "console", "log", "uploadProfileImage", "pipe", "response", "imageUrl", "updateCurrentUser", "setCurrentUser", "token", "localStorage", "setItem", "setTimeout", "err", "confirm", "fileInput", "document", "getElementById", "navigateTo", "path", "navigate", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "completeProfile", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "clearAuthData", "queryParams", "replaceUrl", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "i5", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_23_Template", "ProfileComponent_div_24_Template", "ProfileComponent_div_25_Template", "ProfileComponent_div_26_Template", "ProfileComponent_div_27_Template", "ProfileComponent_div_28_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Router } from '@angular/router';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.css'],\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  user: any;\r\n  selectedImage: File | null = null;\r\n  previewUrl: string | null = null;\r\n  message = '';\r\n  error = '';\r\n  uploadLoading = false;\r\n  removeLoading = false;\r\n\r\n  // Edit profile functionality\r\n  isEditMode = false;\r\n  editForm: FormGroup;\r\n  editLoading = false;\r\n  progressPercentage = 0;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private authuserService: AuthuserService,\r\n    private dataService: DataService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.editForm = this.fb.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      dateOfBirth: [''],\r\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\r\n      department: [''],\r\n      position: [''],\r\n      bio: ['', [Validators.minLength(10)]],\r\n      address: [''],\r\n      skills: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Load user profile using DataService\r\n    this.dataService.getProfile().subscribe({\r\n      next: (res) => {\r\n        this.user = res;\r\n\r\n        // Ensure image properties are consistent\r\n        if (!this.user.profileImage && this.user.image) {\r\n          this.user.profileImage = this.user.image;\r\n        } else if (!this.user.image && this.user.profileImage) {\r\n          this.user.image = this.user.profileImage;\r\n        }\r\n\r\n        // If no image is available, use default\r\n        if (\r\n          !this.user.profileImage ||\r\n          this.user.profileImage === 'null' ||\r\n          this.user.profileImage.trim() === ''\r\n        ) {\r\n          this.user.profileImage = 'assets/images/default-profile.png';\r\n          this.user.image = 'assets/images/default-profile.png';\r\n        }\r\n\r\n        // Ensure profileImageURL is also set for backward compatibility\r\n        if (!this.user.profileImageURL) {\r\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\r\n        }\r\n\r\n        // Calculate profile completion percentage\r\n        this.calculateProfileCompletion();\r\n\r\n        // Populate edit form with current user data\r\n        this.populateEditForm();\r\n      },\r\n      error: () => {\r\n        this.error = 'Failed to load profile.';\r\n      },\r\n    });\r\n  }\r\n\r\n  calculateProfileCompletion(): void {\r\n    if (!this.user) return;\r\n\r\n    const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\r\n    const optionalFields = ['position', 'address', 'skills'];\r\n\r\n    let completedRequired = 0;\r\n    let completedOptional = 0;\r\n\r\n    // Check required fields\r\n    requiredFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\r\n        completedRequired++;\r\n      }\r\n    });\r\n\r\n    // Check optional fields\r\n    optionalFields.forEach(field => {\r\n      const value = this.user[field];\r\n      if (value && value.toString().trim() !== '') {\r\n        completedOptional++;\r\n      }\r\n    });\r\n\r\n    // Check profile image\r\n    let hasProfileImage = 0;\r\n    if (this.user.profileImage &&\r\n        this.user.profileImage !== 'uploads/default.png' &&\r\n        this.user.profileImage !== 'assets/images/default-profile.png' &&\r\n        this.user.profileImage.trim() !== '') {\r\n      hasProfileImage = 1;\r\n    }\r\n\r\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\r\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\r\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\r\n    const imagePercentage = hasProfileImage * 10;\r\n\r\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\r\n  }\r\n\r\n  populateEditForm(): void {\r\n    if (!this.user) return;\r\n\r\n    this.editForm.patchValue({\r\n      firstName: this.user.firstName || '',\r\n      lastName: this.user.lastName || '',\r\n      fullName: this.user.fullName || '',\r\n      email: this.user.email || '',\r\n      dateOfBirth: this.user.dateOfBirth || '',\r\n      phoneNumber: this.user.phoneNumber || '',\r\n      department: this.user.department || '',\r\n      position: this.user.position || '',\r\n      bio: this.user.bio || '',\r\n      address: this.user.address || '',\r\n      skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : (this.user.skills || '')\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns the appropriate profile image URL based on available properties\r\n   * Uses the same logic as in front-layout component for consistency\r\n   */\r\n  getProfileImageUrl(): string {\r\n    if (!this.user) return 'assets/images/default-profile.png';\r\n\r\n    // Check profileImage first\r\n    if (\r\n      this.user.profileImage &&\r\n      this.user.profileImage !== 'null' &&\r\n      this.user.profileImage.trim() !== ''\r\n    ) {\r\n      return this.user.profileImage;\r\n    }\r\n\r\n    // Then check image\r\n    if (\r\n      this.user.image &&\r\n      this.user.image !== 'null' &&\r\n      this.user.image.trim() !== ''\r\n    ) {\r\n      return this.user.image;\r\n    }\r\n\r\n    // Then check profileImageURL (for backward compatibility)\r\n    if (\r\n      this.user.profileImageURL &&\r\n      this.user.profileImageURL !== 'null' &&\r\n      this.user.profileImageURL.trim() !== ''\r\n    ) {\r\n      return this.user.profileImageURL;\r\n    }\r\n\r\n    // Default fallback\r\n    return 'assets/images/default-profile.png';\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      const file = input.files[0];\r\n\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.error = \"L'image ne doit pas dépasser 2MB\";\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      this.selectedImage = file;\r\n      this.error = '';\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        this.previewUrl = (e.target?.result as string) || null;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedImage) return;\r\n\r\n    this.uploadLoading = true; // Activer l'état de chargement\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\r\n\r\n    this.dataService\r\n      .uploadProfileImage(this.selectedImage)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.uploadLoading = false;\r\n          console.log('Upload finished, uploadLoading:', this.uploadLoading);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message = response.message || 'Profile updated successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = response.imageUrl;\r\n          this.user.profileImage = response.imageUrl;\r\n          this.user.image = response.imageUrl;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          this.selectedImage = null;\r\n          this.previewUrl = null;\r\n          this.resetFileInput();\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Upload failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  removeProfileImage(): void {\r\n    if (!confirm('Are you sure you want to remove your profile picture?'))\r\n      return;\r\n\r\n    this.removeLoading = true;\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    this.dataService\r\n      .removeProfileImage()\r\n      .pipe(finalize(() => (this.removeLoading = false)))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message =\r\n            response.message || 'Profile picture removed successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = null;\r\n          this.user.profileImage = null;\r\n          this.user.image = null;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Removal failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  private resetFileInput(): void {\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    const fileInput = document.getElementById(\r\n      'profile-upload'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n  }\r\n\r\n  navigateTo(path: string): void {\r\n    this.router.navigate([path]);\r\n  }\r\n\r\n  // Edit profile methods\r\n  toggleEditMode(): void {\r\n    this.isEditMode = !this.isEditMode;\r\n    if (this.isEditMode) {\r\n      this.populateEditForm();\r\n    }\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  onEditSubmit(): void {\r\n    if (this.editForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    this.editLoading = true;\r\n    this.error = '';\r\n    this.message = '';\r\n\r\n    const formData = new FormData();\r\n\r\n    // Add form fields\r\n    Object.keys(this.editForm.value).forEach(key => {\r\n      const value = this.editForm.value[key];\r\n      if (key === 'skills' && value) {\r\n        // Convert skills string to array\r\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\r\n        formData.append(key, JSON.stringify(skillsArray));\r\n      } else if (value) {\r\n        formData.append(key, value);\r\n      }\r\n    });\r\n\r\n    // Add profile image if selected\r\n    if (this.selectedImage) {\r\n      formData.append('image', this.selectedImage);\r\n    }\r\n\r\n    this.dataService.completeProfile(formData).subscribe({\r\n      next: (response: any) => {\r\n        this.editLoading = false;\r\n        this.message = 'Profile updated successfully!';\r\n\r\n        // Update current user data\r\n        this.user = { ...this.user, ...response.user };\r\n        this.authuserService.setCurrentUser(this.user);\r\n\r\n        // Recalculate progress\r\n        this.calculateProfileCompletion();\r\n\r\n        // Exit edit mode\r\n        this.isEditMode = false;\r\n\r\n        // Clear selected image\r\n        this.selectedImage = null;\r\n        this.previewUrl = null;\r\n        this.resetFileInput();\r\n\r\n        // Auto-hide message after 3 seconds\r\n        setTimeout(() => {\r\n          this.message = '';\r\n        }, 3000);\r\n      },\r\n      error: (err) => {\r\n        this.editLoading = false;\r\n        this.error = err.error?.message || 'An error occurred while updating your profile.';\r\n\r\n        // Auto-hide error after 5 seconds\r\n        setTimeout(() => {\r\n          this.error = '';\r\n        }, 5000);\r\n      }\r\n    });\r\n  }\r\n\r\n  cancelEdit(): void {\r\n    this.isEditMode = false;\r\n    this.populateEditForm(); // Reset form to original values\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    this.resetFileInput();\r\n    this.message = '';\r\n    this.error = '';\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.editForm.controls).forEach(key => {\r\n      this.editForm.get(key)?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  // Helper methods for template\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.editForm.get(fieldName);\r\n    if (field?.errors && field.touched) {\r\n      if (field.errors['required']) return `${fieldName} is required`;\r\n      if (field.errors['minlength']) return `${fieldName} is too short`;\r\n      if (field.errors['email']) return `Invalid email format`;\r\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.editForm.get(fieldName);\r\n    return !!(field?.invalid && field.touched);\r\n  }\r\n\r\n  getProgressColor(): string {\r\n    if (this.progressPercentage < 25) return '#ef4444'; // red\r\n    if (this.progressPercentage < 50) return '#f97316'; // orange\r\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\r\n    if (this.progressPercentage < 100) return '#22c55e'; // green\r\n    return '#10b981'; // emerald\r\n  }\r\n\r\n  getMotivationalMessage(): string {\r\n    if (this.progressPercentage < 25) {\r\n      return \"Let's complete your profile to unlock all features! 🚀\";\r\n    } else if (this.progressPercentage < 50) {\r\n      return \"You're making great progress! Keep going! 💪\";\r\n    } else if (this.progressPercentage < 75) {\r\n      return \"Excellent! You're more than halfway there! 🌟\";\r\n    } else if (this.progressPercentage < 100) {\r\n      return \"Almost perfect! Just a few more details! 🎯\";\r\n    } else {\r\n      return \"Perfect! Your profile is complete! ✨\";\r\n    }\r\n  }\r\n\r\n  logout(): void {\r\n    this.authuserService.logout().subscribe({\r\n      next: () => {\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Logout error:', err);\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {});\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-6 relative z-10\">\r\n    <!-- Page Title -->\r\n    <div class=\"mb-8\">\r\n      <h1\r\n        class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n      >\r\n        Mon Profil\r\n      </h1>\r\n      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n        Gérez vos informations personnelles et vos préférences\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"!user\" class=\"flex justify-center items-center py-20\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\r\n            Erreur\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div\r\n      *ngIf=\"message\"\r\n      class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-1\">\r\n            Succès\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            {{ message }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Profile Completion Progress -->\r\n    <div *ngIf=\"user\" class=\"mb-8\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden\">\r\n        <!-- Decorative gradient top border -->\r\n        <div class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"></div>\r\n\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h3 class=\"text-lg font-semibold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n            Profile Completion\r\n          </h3>\r\n          <span class=\"text-2xl font-bold\" [style.color]=\"getProgressColor()\">\r\n            {{ progressPercentage }}%\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Progress Bar -->\r\n        <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-4 overflow-hidden mb-3\">\r\n          <div\r\n            class=\"h-full rounded-full transition-all duration-500 ease-out\"\r\n            [style.width.%]=\"progressPercentage\"\r\n            [style.background-color]=\"getProgressColor()\">\r\n          </div>\r\n        </div>\r\n\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n          {{ getMotivationalMessage() }}\r\n        </p>\r\n\r\n        <!-- Complete Profile Button (if not 100%) -->\r\n        <div *ngIf=\"progressPercentage < 100\" class=\"mt-4\">\r\n          <button\r\n            (click)=\"toggleEditMode()\"\r\n            class=\"px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all text-sm font-medium\">\r\n            Complete Profile\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Profile -->\r\n    <div *ngIf=\"user\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n      <!-- Profile Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n        ></div>\r\n\r\n        <!-- Glow effect on hover -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n        ></div>\r\n\r\n        <div class=\"flex flex-col items-center\">\r\n          <!-- Profile Image with Glow Effect -->\r\n          <div class=\"relative mb-5 group/avatar\">\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full opacity-0 group-hover/avatar:opacity-100 blur-xl transition-opacity duration-300 -z-10\"\r\n            ></div>\r\n\r\n            <div\r\n              class=\"w-28 h-28 rounded-full border-4 border-[#edf1f4] dark:border-[#2a2a2a] group-hover/avatar:border-[#4f5fad] dark:group-hover/avatar:border-[#6d78c9] overflow-hidden flex items-center justify-center transition-colors duration-300 relative z-10\"\r\n              style=\"min-height: 112px; min-width: 112px\"\r\n            >\r\n              <img\r\n                *ngIf=\"!previewUrl || uploadLoading\"\r\n                [src]=\"getProfileImageUrl()\"\r\n                alt=\"Profile\"\r\n                class=\"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\"\r\n              />\r\n              <img\r\n                *ngIf=\"previewUrl && !uploadLoading\"\r\n                [src]=\"previewUrl\"\r\n                alt=\"Preview\"\r\n                class=\"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <h2\r\n            class=\"text-lg font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-1\"\r\n          >\r\n            {{ user.fullName }}\r\n          </h2>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-2\">\r\n            {{ user.email }}\r\n          </p>\r\n          <div\r\n            class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-xs rounded-full backdrop-blur-sm\"\r\n          >\r\n            {{ user.role | titlecase }}\r\n          </div>\r\n\r\n          <!-- Edit Profile Button -->\r\n          <div class=\"mt-4\">\r\n            <button\r\n              (click)=\"toggleEditMode()\"\r\n              class=\"w-full px-4 py-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all text-sm font-medium flex items-center justify-center\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n              </svg>\r\n              {{ isEditMode ? 'Cancel Edit' : 'Edit Profile' }}\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Upload Profile Image -->\r\n          <div class=\"mt-6 w-full\">\r\n            <label\r\n              for=\"profile-upload\"\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-camera mr-1.5\"></i>\r\n              Photo de profil\r\n            </label>\r\n            <div class=\"flex flex-wrap items-center gap-2\">\r\n              <div class=\"relative w-full group/upload\">\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"profile-upload\"\r\n                  accept=\"image/*\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all file:mr-3 file:py-1.5 file:px-3 file:rounded-lg file:border-0 file:text-xs file:bg-[#4f5fad] dark:file:bg-[#6d78c9] file:text-white hover:file:bg-[#3d4a85] dark:hover:file:bg-[#4f5fad] file:transition-colors\"\r\n                />\r\n                <div\r\n                  class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within/upload:opacity-100 transition-opacity\"\r\n                >\r\n                  <div\r\n                    class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"flex items-center gap-2 w-full mt-3\">\r\n                <!-- Upload Button -->\r\n                <button\r\n                  *ngIf=\"selectedImage\"\r\n                  (click)=\"onUpload()\"\r\n                  class=\"relative overflow-hidden group/btn flex-1\"\r\n                  [disabled]=\"uploadLoading\"\r\n                >\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/btn:scale-105 disabled:opacity-50\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300 disabled:opacity-0\"\r\n                  ></div>\r\n                  <span\r\n                    class=\"relative flex items-center justify-center text-white font-medium py-2 px-3 rounded-lg transition-all z-10\"\r\n                  >\r\n                    <i *ngIf=\"!uploadLoading\" class=\"fas fa-upload mr-1.5\"></i>\r\n                    <svg\r\n                      *ngIf=\"uploadLoading\"\r\n                      class=\"animate-spin mr-1.5 h-3.5 w-3.5 text-white\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <circle\r\n                        class=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        stroke-width=\"4\"\r\n                      ></circle>\r\n                      <path\r\n                        class=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                      ></path>\r\n                    </svg>\r\n                    <span>{{\r\n                      uploadLoading ? \"Téléchargement...\" : \"Télécharger\"\r\n                    }}</span>\r\n                  </span>\r\n                </button>\r\n\r\n                <!-- Remove Button -->\r\n                <button\r\n                  *ngIf=\"\r\n                    user.profileImage || user.image || user.profileImageURL\r\n                  \"\r\n                  (click)=\"removeProfileImage()\"\r\n                  class=\"relative overflow-hidden group/btn flex-1\"\r\n                  [disabled]=\"removeLoading\"\r\n                >\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] rounded-lg transition-transform duration-300 group-hover/btn:scale-105 disabled:opacity-50\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300 disabled:opacity-0\"\r\n                  ></div>\r\n                  <span\r\n                    class=\"relative flex items-center justify-center text-white font-medium py-2 px-3 rounded-lg transition-all z-10\"\r\n                  >\r\n                    <i\r\n                      *ngIf=\"!removeLoading\"\r\n                      class=\"fas fa-trash-alt mr-1.5\"\r\n                    ></i>\r\n                    <svg\r\n                      *ngIf=\"removeLoading\"\r\n                      class=\"animate-spin mr-1.5 h-3.5 w-3.5 text-white\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <circle\r\n                        class=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        stroke-width=\"4\"\r\n                      ></circle>\r\n                      <path\r\n                        class=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                      ></path>\r\n                    </svg>\r\n                    <span>{{\r\n                      removeLoading ? \"Suppression...\" : \"Supprimer\"\r\n                    }}</span>\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Account Information -->\r\n      <div class=\"md:col-span-2 space-y-6\">\r\n        <!-- Account Details -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Informations du compte\r\n          </h3>\r\n\r\n          <div class=\"space-y-4\">\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Nom complet\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.fullName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Adresse email\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.email }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Type de compte\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.role | titlecase }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Membre depuis\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.createdAt | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Additional Profile Fields -->\r\n            <div *ngIf=\"user.firstName\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Prénom\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.firstName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.lastName\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Nom de famille\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.lastName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.dateOfBirth\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Date de naissance\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.dateOfBirth | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.phoneNumber\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Téléphone\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.phoneNumber }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.department\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Département\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.department }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.position\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Position\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.position }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.bio\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Bio\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.bio }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.address\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Adresse\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.address }}\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"user.skills && user.skills.length > 0\"\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Compétences\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                <span *ngFor=\"let skill of user.skills; let last = last\">\r\n                  {{ skill }}<span *ngIf=\"!last\">, </span>\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Account Actions -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\r\n                />\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Actions du compte\r\n          </h3>\r\n\r\n          <div class=\"flex flex-wrap gap-3\">\r\n            <!-- Change Password Button -->\r\n            <a\r\n              routerLink=\"/change-password\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#4f5fad] dark:text-[#6d78c9] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#4f5fad] dark:border-[#6d78c9]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Changer le mot de passe\r\n              </span>\r\n            </a>\r\n\r\n            <!-- Logout Button -->\r\n            <button\r\n              (click)=\"logout()\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#ff6b69] dark:text-[#ff8785] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#ff6b69] dark:border-[#ff8785]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Déconnexion\r\n              </span>\r\n            </button>\r\n\r\n            <!-- Dashboard Button -->\r\n            <a\r\n              [routerLink]=\"\r\n                user.role === 'admin' ? '/admin/dashboard' : '/home'\r\n              \"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#6d6870] dark:text-[#a0a0a0] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#6d6870] dark:border-[#a0a0a0]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Tableau de bord\r\n              </span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Edit Profile Modal/Form -->\r\n    <div *ngIf=\"isEditMode\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div class=\"bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n        <!-- Header -->\r\n        <div class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] p-6 rounded-t-2xl\">\r\n          <div class=\"flex justify-between items-center\">\r\n            <h2 class=\"text-2xl font-bold text-white\">Edit Profile</h2>\r\n            <button\r\n              (click)=\"cancelEdit()\"\r\n              class=\"text-white hover:text-gray-200 transition-colors\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form Content -->\r\n        <form [formGroup]=\"editForm\" (ngSubmit)=\"onEditSubmit()\" class=\"p-6\">\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <!-- First Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                First Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"firstName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('firstName')\"\r\n                placeholder=\"Enter your first name\">\r\n              <p *ngIf=\"getFieldError('firstName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('firstName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Last Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Last Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"lastName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('lastName')\"\r\n                placeholder=\"Enter your last name\">\r\n              <p *ngIf=\"getFieldError('lastName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('lastName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Full Name -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Full Name *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"fullName\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('fullName')\"\r\n                placeholder=\"Enter your full name\">\r\n              <p *ngIf=\"getFieldError('fullName')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('fullName') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Email -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Email *\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('email')\"\r\n                placeholder=\"Enter your email\">\r\n              <p *ngIf=\"getFieldError('email')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('email') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Date of Birth -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Date of Birth\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                formControlName=\"dateOfBirth\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n            </div>\r\n\r\n            <!-- Phone Number -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Phone Number\r\n              </label>\r\n              <input\r\n                type=\"tel\"\r\n                formControlName=\"phoneNumber\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                [class.border-red-500]=\"isFieldInvalid('phoneNumber')\"\r\n                placeholder=\"Enter your phone number\">\r\n              <p *ngIf=\"getFieldError('phoneNumber')\" class=\"text-red-500 text-sm mt-1\">\r\n                {{ getFieldError('phoneNumber') }}\r\n              </p>\r\n            </div>\r\n\r\n            <!-- Department -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Department\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"department\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Computer Science, Marketing\">\r\n            </div>\r\n\r\n            <!-- Position -->\r\n            <div>\r\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n                Position\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"position\"\r\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                placeholder=\"e.g., Student, Professor, Developer\">\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Bio -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Bio\r\n            </label>\r\n            <textarea\r\n              formControlName=\"bio\"\r\n              rows=\"4\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all resize-none\"\r\n              [class.border-red-500]=\"isFieldInvalid('bio')\"\r\n              placeholder=\"Tell us about yourself, your interests, and goals...\"></textarea>\r\n            <p *ngIf=\"getFieldError('bio')\" class=\"text-red-500 text-sm mt-1\">\r\n              {{ getFieldError('bio') }}\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Address -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Address\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"address\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"Enter your address\">\r\n          </div>\r\n\r\n          <!-- Skills -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Skills\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              formControlName=\"skills\"\r\n              class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              placeholder=\"e.g., JavaScript, Python, Project Management (comma separated)\">\r\n            <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n              Separate skills with commas\r\n            </p>\r\n          </div>\r\n\r\n          <!-- Profile Picture Upload -->\r\n          <div class=\"mt-6\">\r\n            <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\r\n              Profile Picture\r\n            </label>\r\n            <div class=\"flex items-center space-x-4\">\r\n              <div class=\"flex-shrink-0\">\r\n                <img\r\n                  [src]=\"previewUrl || getProfileImageUrl()\"\r\n                  alt=\"Profile preview\"\r\n                  class=\"w-20 h-20 rounded-full object-cover border-2 border-[#4f5fad] dark:border-[#6d78c9]\">\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <input\r\n                  type=\"file\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  accept=\"image/*\"\r\n                  class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\">\r\n                <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n                  Upload a new profile picture (optional)\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Action Buttons -->\r\n          <div class=\"flex justify-end space-x-4 mt-8 pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\r\n            <button\r\n              type=\"button\"\r\n              (click)=\"cancelEdit()\"\r\n              class=\"px-6 py-3 text-[#6d6870] dark:text-[#a0a0a0] border border-[#6d6870] dark:border-[#a0a0a0] rounded-lg hover:bg-[#6d6870] hover:text-white dark:hover:bg-[#a0a0a0] dark:hover:text-black transition-all\">\r\n              Cancel\r\n            </button>\r\n\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"editLoading\"\r\n              class=\"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\">\r\n              <span *ngIf=\"!editLoading\">Save Changes</span>\r\n              <span *ngIf=\"editLoading\" class=\"flex items-center\">\r\n                <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n                Saving...\r\n              </span>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Messages -->\r\n          <div *ngIf=\"message\" class=\"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n            <p class=\"text-green-800 dark:text-green-200\">{{ message }}</p>\r\n          </div>\r\n\r\n          <div *ngIf=\"error\" class=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\r\n            <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;IC0CrCC,EAAA,CAAAC,cAAA,cAAkE;IAE9DD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAMvER,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;;IAkCFX,EAAA,CAAAC,cAAA,cAAmD;IAE/CD,EAAA,CAAAY,UAAA,mBAAAC,gEAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAjCfH,EAAA,CAAAC,cAAA,aAA+B;IAG3BD,EAAA,CAAAE,SAAA,cAAwI;IAExIF,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,SAAA,cAIM;IACRF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAoB,UAAA,KAAAC,uCAAA,kBAMM;IACRrB,EAAA,CAAAG,YAAA,EAAM;;;;IA1B+BH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAC,gBAAA,GAAkC;IACjExB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAE,kBAAA,OACF;IAOEzB,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAsB,WAAA,UAAAC,MAAA,CAAAE,kBAAA,MAAoC,qBAAAF,MAAA,CAAAC,gBAAA;IAMtCxB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAc,MAAA,CAAAG,sBAAA,QACF;IAGM1B,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2B,UAAA,SAAAJ,MAAA,CAAAE,kBAAA,OAA8B;;;;;IAqC9BzB,EAAA,CAAAE,SAAA,eAKE;;;;IAHAF,EAAA,CAAA2B,UAAA,QAAAC,MAAA,CAAAC,kBAAA,IAAA7B,EAAA,CAAA8B,aAAA,CAA4B;;;;;IAI9B9B,EAAA,CAAAE,SAAA,eAKE;;;;IAHAF,EAAA,CAAA2B,UAAA,QAAAI,OAAA,CAAAC,UAAA,EAAAhC,EAAA,CAAA8B,aAAA,CAAkB;;;;;IA6Ed9B,EAAA,CAAAE,SAAA,aAA2D;;;;;IAC3DF,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,kBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApCVH,EAAA,CAAAC,cAAA,kBAKC;IAHCD,EAAA,CAAAY,UAAA,mBAAAsB,mEAAA;MAAAlC,EAAA,CAAAc,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAkB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAIpBrC,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAoB,UAAA,IAAAkB,8CAAA,iBAA2D;IAC3DtC,EAAA,CAAAoB,UAAA,IAAAmB,qDAAA,mBAoBM;IACNvC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAEJ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAnCXH,EAAA,CAAA2B,UAAA,aAAAa,OAAA,CAAAC,aAAA,CAA0B;IAWpBzC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAa,OAAA,CAAAC,aAAA,CAAoB;IAErBzC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAa,OAAA,CAAAC,aAAA,CAAmB;IAoBhBzC,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,iBAAA,CAAAkC,OAAA,CAAAC,aAAA,2DAEJ;;;;;IAsBFzC,EAAA,CAAAE,SAAA,aAGK;;;;;IACLF,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,kBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAzCVH,EAAA,CAAAC,cAAA,kBAOC;IAHCD,EAAA,CAAAY,UAAA,mBAAA8B,mEAAA;MAAA1C,EAAA,CAAAc,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA0B,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9B7C,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAoB,UAAA,IAAA0B,8CAAA,iBAGK;IACL9C,EAAA,CAAAoB,UAAA,IAAA2B,qDAAA,mBAoBM;IACN/C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAEJ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAtCXH,EAAA,CAAA2B,UAAA,aAAAqB,OAAA,CAAAC,aAAA,CAA0B;IAYrBjD,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,UAAAqB,OAAA,CAAAC,aAAA,CAAoB;IAIpBjD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAqB,OAAA,CAAAC,aAAA,CAAmB;IAoBhBjD,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,iBAAA,CAAA0C,OAAA,CAAAC,aAAA,kCAEJ;;;;;IAqIVjD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyC,OAAA,CAAAC,IAAA,CAAAC,SAAA,MACF;;;;;IAGFpD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,OAAA,CAAAF,IAAA,CAAAG,QAAA,MACF;;;;;IAGFtD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAuD,WAAA,OAAAC,OAAA,CAAAL,IAAA,CAAAM,WAAA,qBACF;;;;;IAGFzD,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAiD,OAAA,CAAAP,IAAA,CAAAQ,WAAA,MACF;;;;;IAGF3D,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAmD,OAAA,CAAAT,IAAA,CAAAU,UAAA,MACF;;;;;IAGF7D,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAqD,OAAA,CAAAX,IAAA,CAAAY,QAAA,MACF;;;;;IAGF/D,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuD,OAAA,CAAAb,IAAA,CAAAc,GAAA,MACF;;;;;IAGFjE,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyD,OAAA,CAAAf,IAAA,CAAAgB,OAAA,MACF;;;;;IAoBenE,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAI,MAAA,SAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAD1CH,EAAA,CAAAC,cAAA,WAAyD;IACvDD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAoB,UAAA,IAAAgD,qDAAA,oBAA6B;IAC1CpE,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAS,kBAAA,MAAA4D,SAAA,KAAW;IAAOrE,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,UAAA,UAAA2C,QAAA,CAAW;;;;;IAjBnCtE,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAoB,UAAA,IAAAmD,8CAAA,oBAEO;IACTvE,EAAA,CAAAG,YAAA,EAAM;;;;IAHoBH,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA2B,UAAA,YAAA6C,OAAA,CAAArB,IAAA,CAAAsB,MAAA,CAAgB;;;;;;IA7epDzE,EAAA,CAAAC,cAAA,cAAgE;IAM5DD,EAAA,CAAAE,SAAA,cAEO;IAOPF,EAAA,CAAAC,cAAA,cAAwC;IAGpCD,EAAA,CAAAE,SAAA,cAEO;IAEPF,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAoB,UAAA,IAAAsD,sCAAA,kBAKE;IACF1E,EAAA,CAAAoB,UAAA,IAAAuD,sCAAA,kBAKE;IACJ3E,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IAEdD,EAAA,CAAAY,UAAA,mBAAAgE,0DAAA;MAAA5E,EAAA,CAAAc,aAAA,CAAA+D,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA4D,OAAA,CAAA3D,cAAA,EAAgB;IAAA,EAAC;IAE1BnB,EAAA,CAAAiC,cAAA,EAAmH;IAAnHjC,EAAA,CAAAC,cAAA,eAAmH;IACjHD,EAAA,CAAAE,SAAA,gBAAmM;IACrMF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAA+E,eAAA,EAAyB;IAAzB/E,EAAA,CAAAC,cAAA,eAAyB;IAKrBD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAA+C;IAMzCD,EAAA,CAAAY,UAAA,oBAAAoE,0DAAAC,MAAA;MAAAjF,EAAA,CAAAc,aAAA,CAAA+D,IAAA;MAAA,MAAAK,OAAA,GAAAlF,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAgE,OAAA,CAAAC,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAJnCjF,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAiD;IAE/CD,EAAA,CAAAoB,UAAA,KAAAgE,0CAAA,qBAyCS;IAGTpF,EAAA,CAAAoB,UAAA,KAAAiE,0CAAA,qBA8CS;IACXrF,EAAA,CAAAG,YAAA,EAAM;IAOdH,EAAA,CAAAC,cAAA,eAAqC;IAMjCD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA+E,eAAA,EAEC;IAFD/E,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAuB;IAKjBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAoB,UAAA,KAAAkE,uCAAA,kBAkBM;IAENtF,EAAA,CAAAoB,UAAA,KAAAmE,uCAAA,kBAkBM;IAENvF,EAAA,CAAAoB,UAAA,KAAAoE,uCAAA,kBAkBM;IAENxF,EAAA,CAAAoB,UAAA,KAAAqE,uCAAA,kBAkBM;IAENzF,EAAA,CAAAoB,UAAA,KAAAsE,uCAAA,kBAkBM;IAEN1F,EAAA,CAAAoB,UAAA,KAAAuE,uCAAA,kBAkBM;IAEN3F,EAAA,CAAAoB,UAAA,KAAAwE,uCAAA,kBAkBM;IAEN5F,EAAA,CAAAoB,UAAA,KAAAyE,uCAAA,kBAkBM;IAEN7F,EAAA,CAAAoB,UAAA,KAAA0E,uCAAA,kBAoBM;IACR9F,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IAOJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA+E,eAAA,EAEC;IAFD/E,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAkC;IAM9BD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IAEGD,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA+E,eAAA,EAEC;IAFD/E,EAAA,CAAAE,SAAA,gBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,mBAGC;IAFCD,EAAA,CAAAY,UAAA,mBAAAmF,2DAAA;MAAA/F,EAAA,CAAAc,aAAA,CAAA+D,IAAA;MAAA,MAAAmB,OAAA,GAAAhG,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAA8E,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBjG,EAAA,CAAAE,SAAA,gBAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IAEGD,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA+E,eAAA,EAEC;IAFD/E,EAAA,CAAAE,SAAA,gBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,gBAEO;IAIPF,EAAA,CAAAC,cAAA,kBAEC;IAEGD,EAAA,CAAAiC,cAAA,EAMC;IANDjC,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,kBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA+E,eAAA,EAEC;IAFD/E,EAAA,CAAAE,SAAA,iBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IA1nBJH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,UAAAuE,MAAA,CAAAlE,UAAA,IAAAkE,MAAA,CAAAzD,aAAA,CAAkC;IAMlCzC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAAlE,UAAA,KAAAkE,MAAA,CAAAzD,aAAA,CAAkC;IAWvCzC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,MAAA,CAAA/C,IAAA,CAAAgD,QAAA,MACF;IAEEnG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,MAAA,CAAA/C,IAAA,CAAAiD,KAAA,MACF;IAIEpG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAqG,WAAA,SAAAH,MAAA,CAAA/C,IAAA,CAAAmD,IAAA,OACF;IAUItG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,MAAA,CAAAK,UAAA,uCACF;IAiCOvG,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAAM,aAAA,CAAmB;IA4CnBxG,EAAA,CAAAK,SAAA,GAEA;IAFAL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAsD,YAAA,IAAAP,MAAA,CAAA/C,IAAA,CAAAuD,KAAA,IAAAR,MAAA,CAAA/C,IAAA,CAAAwD,eAAA,CAEA;IA6GH3G,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,MAAA,CAAA/C,IAAA,CAAAgD,QAAA,MACF;IAmBEnG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyF,MAAA,CAAA/C,IAAA,CAAAiD,KAAA,MACF;IAmBEpG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAqG,WAAA,SAAAH,MAAA,CAAA/C,IAAA,CAAAmD,IAAA,OACF;IAmBEtG,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAuD,WAAA,SAAA2C,MAAA,CAAA/C,IAAA,CAAAyD,SAAA,qBACF;IAII5G,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAC,SAAA,CAAoB;IAoBpBpD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAG,QAAA,CAAmB;IAoBnBtD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAM,WAAA,CAAsB;IAoBtBzD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAQ,WAAA,CAAsB;IAoBtB3D,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAU,UAAA,CAAqB;IAoBrB7D,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAY,QAAA,CAAmB;IAoBnB/D,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAc,GAAA,CAAc;IAoBdjE,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAgB,OAAA,CAAkB;IAoBlBnE,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAA2B,UAAA,SAAAuE,MAAA,CAAA/C,IAAA,CAAAsB,MAAA,IAAAyB,MAAA,CAAA/C,IAAA,CAAAsB,MAAA,CAAAoC,MAAA,KAA2C;IAqJ/C7G,EAAA,CAAAK,SAAA,IAEC;IAFDL,EAAA,CAAA2B,UAAA,eAAAuE,MAAA,CAAA/C,IAAA,CAAAmD,IAAA,4CAEC;;;;;IAuEDtG,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAqG,OAAA,CAAAC,aAAA,mBACF;;;;;IAcA/G,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAuG,OAAA,CAAAD,aAAA,kBACF;;;;;IAcA/G,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAwG,OAAA,CAAAF,aAAA,kBACF;;;;;IAcA/G,EAAA,CAAAC,cAAA,aAAoE;IAClED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAyG,OAAA,CAAAH,aAAA,eACF;;;;;IAyBA/G,EAAA,CAAAC,cAAA,aAA0E;IACxED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA0G,OAAA,CAAAJ,aAAA,qBACF;;;;;IAuCF/G,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA2G,OAAA,CAAAL,aAAA,aACF;;;;;IAoEE/G,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAC9CH,EAAA,CAAAC,cAAA,gBAAoD;IAClDD,EAAA,CAAAiC,cAAA,EAA2H;IAA3HjC,EAAA,CAAAC,cAAA,eAA2H;IACzHD,EAAA,CAAAE,SAAA,kBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAKXH,EAAA,CAAAC,cAAA,eAAgI;IAChFD,EAAA,CAAAI,MAAA,GAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAjBH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,iBAAA,CAAA+G,OAAA,CAAA1G,OAAA,CAAa;;;;;IAG7DX,EAAA,CAAAC,cAAA,eAAsH;IAC1ED,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAgH,OAAA,CAAA9G,KAAA,CAAW;;;;;;IAzO7DR,EAAA,CAAAC,cAAA,eAA+G;IAK7DD,EAAA,CAAAI,MAAA,mBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,kBAE2D;IADzDD,EAAA,CAAAY,UAAA,mBAAA2G,yDAAA;MAAAvH,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAuG,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtB1H,EAAA,CAAAiC,cAAA,EAA8G;IAA9GjC,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAiG;IACnGF,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAA+E,eAAA,EAAqE;IAArE/E,EAAA,CAAAC,cAAA,gBAAqE;IAAxCD,EAAA,CAAAY,UAAA,sBAAA+G,0DAAA;MAAA3H,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAI,OAAA,GAAA5H,EAAA,CAAAiB,aAAA;MAAA,OAAYjB,EAAA,CAAAkB,WAAA,CAAA0G,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACtD7H,EAAA,CAAAC,cAAA,gBAAmD;IAI7CD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKsC;IACtCF,EAAA,CAAAoB,UAAA,KAAA0G,qCAAA,iBAEI;IACN9H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAA2G,qCAAA,iBAEI;IACN/H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKqC;IACrCF,EAAA,CAAAoB,UAAA,KAAA4G,qCAAA,iBAEI;IACNhI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKiC;IACjCF,EAAA,CAAAoB,UAAA,KAAA6G,qCAAA,iBAEI;IACNjI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAGyS;IAC3SF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAKwC;IACxCF,EAAA,CAAAoB,UAAA,KAAA8G,qCAAA,iBAEI;IACNlI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAEDD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAIoD;IACtDF,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,qBAKgF;IAChFF,EAAA,CAAAoB,UAAA,KAAA+G,qCAAA,iBAEI;IACNnI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAImC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAE,SAAA,kBAI+E;IAC/EF,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAyC;IAErCD,EAAA,CAAAE,SAAA,gBAG8F;IAChGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAoB;IAGhBD,EAAA,CAAAY,UAAA,oBAAAwH,0DAAAnD,MAAA;MAAAjF,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAa,OAAA,GAAArI,EAAA,CAAAiB,aAAA;MAAA,OAAUjB,EAAA,CAAAkB,WAAA,CAAAmH,OAAA,CAAAlD,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAFnCjF,EAAA,CAAAG,YAAA,EAIyS;IACzSH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAI,MAAA,iDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAMVH,EAAA,CAAAC,cAAA,gBAAkG;IAG9FD,EAAA,CAAAY,UAAA,mBAAA0H,0DAAA;MAAAtI,EAAA,CAAAc,aAAA,CAAA0G,IAAA;MAAA,MAAAe,OAAA,GAAAvI,EAAA,CAAAiB,aAAA;MAAA,OAASjB,EAAA,CAAAkB,WAAA,CAAAqH,OAAA,CAAAb,UAAA,EAAY;IAAA,EAAC;IAEtB1H,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,mBAG8L;IAC5LD,EAAA,CAAAoB,UAAA,KAAAoH,wCAAA,oBAA8C;IAC9CxI,EAAA,CAAAoB,UAAA,KAAAqH,wCAAA,oBAMO;IACTzI,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAoB,UAAA,KAAAsH,uCAAA,mBAEM;IAEN1I,EAAA,CAAAoB,UAAA,KAAAuH,uCAAA,mBAEM;IACR3I,EAAA,CAAAG,YAAA,EAAO;;;;IA1NDH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA2B,UAAA,cAAAiH,MAAA,CAAAC,QAAA,CAAsB;IAWpB7I,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,cAAoD;IAElD/I,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,cAAgC;IAclC/G,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,aAAmD;IAEjD/I,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,aAA+B;IAcjC/G,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,aAAmD;IAEjD/I,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,aAA+B;IAcjC/G,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,UAAgD;IAE9C/I,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,UAA4B;IAyB9B/G,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,gBAAsD;IAEpD/I,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,gBAAkC;IAuCtC/G,EAAA,CAAAK,SAAA,IAA8C;IAA9CL,EAAA,CAAA8I,WAAA,mBAAAF,MAAA,CAAAG,cAAA,QAA8C;IAE5C/I,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAA7B,aAAA,QAA0B;IAwCxB/G,EAAA,CAAAK,SAAA,IAA0C;IAA1CL,EAAA,CAAA2B,UAAA,QAAAiH,MAAA,CAAA5G,UAAA,IAAA4G,MAAA,CAAA/G,kBAAA,IAAA7B,EAAA,CAAA8B,aAAA,CAA0C;IA4B9C9B,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA2B,UAAA,aAAAiH,MAAA,CAAAI,WAAA,CAAwB;IAEjBhJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA2B,UAAA,UAAAiH,MAAA,CAAAI,WAAA,CAAkB;IAClBhJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAAI,WAAA,CAAiB;IAWtBhJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAAjI,OAAA,CAAa;IAIbX,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,UAAA,SAAAiH,MAAA,CAAApI,KAAA,CAAW;;;ADtgC3B,OAAM,MAAOyI,gBAAgB;EAe3BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAJf,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAlBZ,KAAA/C,aAAa,GAAgB,IAAI;IACjC,KAAAxE,UAAU,GAAkB,IAAI;IAChC,KAAArB,OAAO,GAAG,EAAE;IACZ,KAAAH,KAAK,GAAG,EAAE;IACV,KAAAiC,aAAa,GAAG,KAAK;IACrB,KAAAQ,aAAa,GAAG,KAAK;IAErB;IACA,KAAAsD,UAAU,GAAG,KAAK;IAElB,KAAAyC,WAAW,GAAG,KAAK;IACnB,KAAAvH,kBAAkB,GAAG,CAAC;IASpB,IAAI,CAACoH,QAAQ,GAAG,IAAI,CAACU,EAAE,CAACC,KAAK,CAAC;MAC5BpG,SAAS,EAAE,CAAC,EAAE,EAAE,CAACtD,UAAU,CAAC2J,QAAQ,EAAE3J,UAAU,CAAC4J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DpG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACxD,UAAU,CAAC2J,QAAQ,EAAE3J,UAAU,CAAC4J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DvD,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrG,UAAU,CAAC2J,QAAQ,EAAE3J,UAAU,CAAC4J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DtD,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAAC2J,QAAQ,EAAE3J,UAAU,CAACsG,KAAK,CAAC,CAAC;MACpD3C,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC7D,UAAU,CAAC6J,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC1D9F,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdE,GAAG,EAAE,CAAC,EAAE,EAAE,CAACnE,UAAU,CAAC4J,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCvF,OAAO,EAAE,CAAC,EAAE,CAAC;MACbM,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAmF,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,WAAW,CAACQ,UAAU,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAAC7G,IAAI,GAAG6G,GAAG;QAEf;QACA,IAAI,CAAC,IAAI,CAAC7G,IAAI,CAACsD,YAAY,IAAI,IAAI,CAACtD,IAAI,CAACuD,KAAK,EAAE;UAC9C,IAAI,CAACvD,IAAI,CAACsD,YAAY,GAAG,IAAI,CAACtD,IAAI,CAACuD,KAAK;SACzC,MAAM,IAAI,CAAC,IAAI,CAACvD,IAAI,CAACuD,KAAK,IAAI,IAAI,CAACvD,IAAI,CAACsD,YAAY,EAAE;UACrD,IAAI,CAACtD,IAAI,CAACuD,KAAK,GAAG,IAAI,CAACvD,IAAI,CAACsD,YAAY;;QAG1C;QACA,IACE,CAAC,IAAI,CAACtD,IAAI,CAACsD,YAAY,IACvB,IAAI,CAACtD,IAAI,CAACsD,YAAY,KAAK,MAAM,IACjC,IAAI,CAACtD,IAAI,CAACsD,YAAY,CAACwD,IAAI,EAAE,KAAK,EAAE,EACpC;UACA,IAAI,CAAC9G,IAAI,CAACsD,YAAY,GAAG,mCAAmC;UAC5D,IAAI,CAACtD,IAAI,CAACuD,KAAK,GAAG,mCAAmC;;QAGvD;QACA,IAAI,CAAC,IAAI,CAACvD,IAAI,CAACwD,eAAe,EAAE;UAC9B,IAAI,CAACxD,IAAI,CAACwD,eAAe,GAAG,IAAI,CAACxD,IAAI,CAACsD,YAAY,IAAI,IAAI,CAACtD,IAAI,CAACuD,KAAK;;QAGvE;QACA,IAAI,CAACwD,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACD3J,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,yBAAyB;MACxC;KACD,CAAC;EACJ;EAEA0J,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC/G,IAAI,EAAE;IAEhB,MAAMiH,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACxH,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACvH,IAAI,CAACsH,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACV,IAAI,EAAE,KAAK,EAAE,IAAIS,KAAK,KAAK,qBAAqB,EAAE;QAC9EJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACvH,IAAI,CAACsH,KAAK,CAAC;MAC9B,IAAIC,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE,CAACV,IAAI,EAAE,KAAK,EAAE,EAAE;QAC3CM,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAACzH,IAAI,CAACsD,YAAY,IACtB,IAAI,CAACtD,IAAI,CAACsD,YAAY,KAAK,qBAAqB,IAChD,IAAI,CAACtD,IAAI,CAACsD,YAAY,KAAK,mCAAmC,IAC9D,IAAI,CAACtD,IAAI,CAACsD,YAAY,CAACwD,IAAI,EAAE,KAAK,EAAE,EAAE;MACxCW,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAMC,kBAAkB,GAAIP,iBAAiB,GAAGF,cAAc,CAACvD,MAAM,GAAI,EAAE;IAC3E,MAAMiE,kBAAkB,GAAIP,iBAAiB,GAAGF,cAAc,CAACxD,MAAM,GAAI,EAAE;IAC3E,MAAMkE,eAAe,GAAGH,eAAe,GAAG,EAAE;IAE5C,IAAI,CAACnJ,kBAAkB,GAAGuJ,IAAI,CAACC,KAAK,CAACJ,kBAAkB,GAAGC,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEAZ,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAChH,IAAI,EAAE;IAEhB,IAAI,CAAC0F,QAAQ,CAACqC,UAAU,CAAC;MACvB9H,SAAS,EAAE,IAAI,CAACD,IAAI,CAACC,SAAS,IAAI,EAAE;MACpCE,QAAQ,EAAE,IAAI,CAACH,IAAI,CAACG,QAAQ,IAAI,EAAE;MAClC6C,QAAQ,EAAE,IAAI,CAAChD,IAAI,CAACgD,QAAQ,IAAI,EAAE;MAClCC,KAAK,EAAE,IAAI,CAACjD,IAAI,CAACiD,KAAK,IAAI,EAAE;MAC5B3C,WAAW,EAAE,IAAI,CAACN,IAAI,CAACM,WAAW,IAAI,EAAE;MACxCE,WAAW,EAAE,IAAI,CAACR,IAAI,CAACQ,WAAW,IAAI,EAAE;MACxCE,UAAU,EAAE,IAAI,CAACV,IAAI,CAACU,UAAU,IAAI,EAAE;MACtCE,QAAQ,EAAE,IAAI,CAACZ,IAAI,CAACY,QAAQ,IAAI,EAAE;MAClCE,GAAG,EAAE,IAAI,CAACd,IAAI,CAACc,GAAG,IAAI,EAAE;MACxBE,OAAO,EAAE,IAAI,CAAChB,IAAI,CAACgB,OAAO,IAAI,EAAE;MAChCM,MAAM,EAAE0G,KAAK,CAACC,OAAO,CAAC,IAAI,CAACjI,IAAI,CAACsB,MAAM,CAAC,GAAG,IAAI,CAACtB,IAAI,CAACsB,MAAM,CAAC4G,IAAI,CAAC,IAAI,CAAC,GAAI,IAAI,CAAClI,IAAI,CAACsB,MAAM,IAAI;KAC9F,CAAC;EACJ;EAEA;;;;EAIA5C,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACsB,IAAI,EAAE,OAAO,mCAAmC;IAE1D;IACA,IACE,IAAI,CAACA,IAAI,CAACsD,YAAY,IACtB,IAAI,CAACtD,IAAI,CAACsD,YAAY,KAAK,MAAM,IACjC,IAAI,CAACtD,IAAI,CAACsD,YAAY,CAACwD,IAAI,EAAE,KAAK,EAAE,EACpC;MACA,OAAO,IAAI,CAAC9G,IAAI,CAACsD,YAAY;;IAG/B;IACA,IACE,IAAI,CAACtD,IAAI,CAACuD,KAAK,IACf,IAAI,CAACvD,IAAI,CAACuD,KAAK,KAAK,MAAM,IAC1B,IAAI,CAACvD,IAAI,CAACuD,KAAK,CAACuD,IAAI,EAAE,KAAK,EAAE,EAC7B;MACA,OAAO,IAAI,CAAC9G,IAAI,CAACuD,KAAK;;IAGxB;IACA,IACE,IAAI,CAACvD,IAAI,CAACwD,eAAe,IACzB,IAAI,CAACxD,IAAI,CAACwD,eAAe,KAAK,MAAM,IACpC,IAAI,CAACxD,IAAI,CAACwD,eAAe,CAACsD,IAAI,EAAE,KAAK,EAAE,EACvC;MACA,OAAO,IAAI,CAAC9G,IAAI,CAACwD,eAAe;;IAGlC;IACA,OAAO,mCAAmC;EAC5C;EAEAxB,cAAcA,CAACmG,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE5E,MAAM,EAAE;MACvB,MAAM6E,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACnC,IAAI,CAACrL,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAACsL,cAAc,EAAE;QACrB;;MAGF,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACvL,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAACsL,cAAc,EAAE;QACrB;;MAGF,IAAI,CAACtF,aAAa,GAAGkF,IAAI;MACzB,IAAI,CAAClL,KAAK,GAAG,EAAE;MAEf,MAAMwL,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAACnK,UAAU,GAAImK,CAAC,CAACX,MAAM,EAAEY,MAAiB,IAAI,IAAI;MACxD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;EAE9B;EAEArJ,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACmE,aAAa,EAAE;IAEzB,IAAI,CAAC/D,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAAC9B,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf8L,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC9J,aAAa,CAAC;IAEjE,IAAI,CAAC4G,WAAW,CACbmD,kBAAkB,CAAC,IAAI,CAAChG,aAAa,CAAC,CACtCiG,IAAI,CACH1M,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC0C,aAAa,GAAG,KAAK;MAC1B6J,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC9J,aAAa,CAAC;IACpE,CAAC,CAAC,CACH,CACAqH,SAAS,CAAC;MACTC,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAI,CAAC/L,OAAO,GAAG+L,QAAQ,CAAC/L,OAAO,IAAI,8BAA8B;QAEjE;QACA,IAAI,CAACwC,IAAI,CAACwD,eAAe,GAAG+F,QAAQ,CAACC,QAAQ;QAC7C,IAAI,CAACxJ,IAAI,CAACsD,YAAY,GAAGiG,QAAQ,CAACC,QAAQ;QAC1C,IAAI,CAACxJ,IAAI,CAACuD,KAAK,GAAGgG,QAAQ,CAACC,QAAQ;QAEnC;QACA,IAAI,CAACtD,WAAW,CAACuD,iBAAiB,CAAC;UACjCnG,YAAY,EAAEiG,QAAQ,CAACC,QAAQ;UAC/BjG,KAAK,EAAEgG,QAAQ,CAACC;SACjB,CAAC;QAEF;QACA,IAAI,CAACvD,eAAe,CAACyD,cAAc,CAAC;UAClC,GAAG,IAAI,CAAC1J,IAAI;UACZsD,YAAY,EAAEiG,QAAQ,CAACC,QAAQ;UAC/BjG,KAAK,EAAEgG,QAAQ,CAACC;SACjB,CAAC;QAEF,IAAI,CAACnG,aAAa,GAAG,IAAI;QACzB,IAAI,CAACxE,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC8J,cAAc,EAAE;QAErB,IAAIY,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAACtM,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAG0M,GAAmC,IAAI;QAC7C,IAAI,CAAC1M,KAAK,GAAG0M,GAAG,CAAC1M,KAAK,EAAEG,OAAO,IAAI,eAAe;QAClD;QACAsM,UAAU,CAAC,MAAK;UACd,IAAI,CAACzM,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEAqC,kBAAkBA,CAAA;IAChB,IAAI,CAACsK,OAAO,CAAC,uDAAuD,CAAC,EACnE;IAEF,IAAI,CAAClK,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC6I,WAAW,CACbxG,kBAAkB,EAAE,CACpB4J,IAAI,CAAC1M,QAAQ,CAAC,MAAO,IAAI,CAACkD,aAAa,GAAG,KAAM,CAAC,CAAC,CAClD6G,SAAS,CAAC;MACTC,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAI,CAAC/L,OAAO,GACV+L,QAAQ,CAAC/L,OAAO,IAAI,sCAAsC;QAE5D;QACA,IAAI,CAACwC,IAAI,CAACwD,eAAe,GAAG,IAAI;QAChC,IAAI,CAACxD,IAAI,CAACsD,YAAY,GAAG,IAAI;QAC7B,IAAI,CAACtD,IAAI,CAACuD,KAAK,GAAG,IAAI;QAEtB;QACA,IAAI,CAAC2C,WAAW,CAACuD,iBAAiB,CAAC;UACjCnG,YAAY,EAAE,mCAAmC;UACjDC,KAAK,EAAE;SACR,CAAC;QAEF;QACA,IAAI,CAAC0C,eAAe,CAACyD,cAAc,CAAC;UAClC,GAAG,IAAI,CAAC1J,IAAI;UACZsD,YAAY,EAAE,mCAAmC;UACjDC,KAAK,EAAE;SACR,CAAC;QAEF,IAAIgG,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAACtM,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAG0M,GAAmC,IAAI;QAC7C,IAAI,CAAC1M,KAAK,GAAG0M,GAAG,CAAC1M,KAAK,EAAEG,OAAO,IAAI,gBAAgB;QACnD;QACAsM,UAAU,CAAC,MAAK;UACd,IAAI,CAACzM,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEQsL,cAAcA,CAAA;IACpB,IAAI,CAACtF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACxE,UAAU,GAAG,IAAI;IACtB,MAAMoL,SAAS,GAAGC,QAAQ,CAACC,cAAc,CACvC,gBAAgB,CACG;IACrB,IAAIF,SAAS,EAAEA,SAAS,CAAC1C,KAAK,GAAG,EAAE;EACrC;EAEA6C,UAAUA,CAACC,IAAY;IACrB,IAAI,CAAClE,MAAM,CAACmE,QAAQ,CAAC,CAACD,IAAI,CAAC,CAAC;EAC9B;EAEA;EACArM,cAAcA,CAAA;IACZ,IAAI,CAACoF,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnB,IAAI,CAAC4D,gBAAgB,EAAE;;IAEzB,IAAI,CAACxJ,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEAqH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,QAAQ,CAAC6E,OAAO,EAAE;MACzB,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC3E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACxI,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,OAAO,GAAG,EAAE;IAEjB,MAAMiN,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClF,QAAQ,CAAC6B,KAAK,CAAC,CAACF,OAAO,CAACwD,GAAG,IAAG;MAC7C,MAAMtD,KAAK,GAAG,IAAI,CAAC7B,QAAQ,CAAC6B,KAAK,CAACsD,GAAG,CAAC;MACtC,IAAIA,GAAG,KAAK,QAAQ,IAAItD,KAAK,EAAE;QAC7B;QACA,MAAMuD,WAAW,GAAGvD,KAAK,CAACwD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAACnE,IAAI,EAAE,CAAC,CAACoE,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAIvD,KAAK,EAAE;QAChBkD,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEtD,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAAClE,aAAa,EAAE;MACtBoH,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC9H,aAAa,CAAC;;IAG9C,IAAI,CAAC6C,WAAW,CAACoF,eAAe,CAACb,QAAQ,CAAC,CAAC9D,SAAS,CAAC;MACnDC,IAAI,EAAG2C,QAAa,IAAI;QACtB,IAAI,CAAC1D,WAAW,GAAG,KAAK;QACxB,IAAI,CAACrI,OAAO,GAAG,+BAA+B;QAE9C;QACA,IAAI,CAACwC,IAAI,GAAG;UAAE,GAAG,IAAI,CAACA,IAAI;UAAE,GAAGuJ,QAAQ,CAACvJ;QAAI,CAAE;QAC9C,IAAI,CAACiG,eAAe,CAACyD,cAAc,CAAC,IAAI,CAAC1J,IAAI,CAAC;QAE9C;QACA,IAAI,CAAC+G,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAAC3D,UAAU,GAAG,KAAK;QAEvB;QACA,IAAI,CAACC,aAAa,GAAG,IAAI;QACzB,IAAI,CAACxE,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC8J,cAAc,EAAE;QAErB;QACAmB,UAAU,CAAC,MAAK;UACd,IAAI,CAACtM,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAG0M,GAAG,IAAI;QACb,IAAI,CAAClE,WAAW,GAAG,KAAK;QACxB,IAAI,CAACxI,KAAK,GAAG0M,GAAG,CAAC1M,KAAK,EAAEG,OAAO,IAAI,gDAAgD;QAEnF;QACAsM,UAAU,CAAC,MAAK;UACd,IAAI,CAACzM,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACJ;EAEAkH,UAAUA,CAAA;IACR,IAAI,CAACnB,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC4D,gBAAgB,EAAE,CAAC,CAAC;IACzB,IAAI,CAAC3D,aAAa,GAAG,IAAI;IACzB,IAAI,CAACxE,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC8J,cAAc,EAAE;IACrB,IAAI,CAACnL,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;EACjB;EAEQmN,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClF,QAAQ,CAAC6F,QAAQ,CAAC,CAAClE,OAAO,CAACwD,GAAG,IAAG;MAChD,IAAI,CAACnF,QAAQ,CAAC8F,GAAG,CAACX,GAAG,CAAC,EAAEY,aAAa,EAAE;IACzC,CAAC,CAAC;EACJ;EAEA;EACA7H,aAAaA,CAAC8H,SAAiB;IAC7B,MAAMpE,KAAK,GAAG,IAAI,CAAC5B,QAAQ,CAAC8F,GAAG,CAACE,SAAS,CAAC;IAC1C,IAAIpE,KAAK,EAAEqE,MAAM,IAAIrE,KAAK,CAACsE,OAAO,EAAE;MAClC,IAAItE,KAAK,CAACqE,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAIpE,KAAK,CAACqE,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAIpE,KAAK,CAACqE,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,sBAAsB;MACxD,IAAIrE,KAAK,CAACqE,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEA9F,cAAcA,CAAC8F,SAAiB;IAC9B,MAAMpE,KAAK,GAAG,IAAI,CAAC5B,QAAQ,CAAC8F,GAAG,CAACE,SAAS,CAAC;IAC1C,OAAO,CAAC,EAAEpE,KAAK,EAAEiD,OAAO,IAAIjD,KAAK,CAACsE,OAAO,CAAC;EAC5C;EAEAvN,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACC,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACD,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,wDAAwD;KAChE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,8CAA8C;KACtD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,6CAA6C;KACrD,MAAM;MACL,OAAO,sCAAsC;;EAEjD;EAEAwE,MAAMA,CAAA;IACJ,IAAI,CAACmD,eAAe,CAACnD,MAAM,EAAE,CAAC6D,SAAS,CAAC;MACtCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACX,eAAe,CAAC4F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC3D,MAAM,CAACmE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC/BwB,WAAW,EAAE;cAAEtO,OAAO,EAAE;YAAqB,CAAE;YAC/CuO,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD1O,KAAK,EAAG0M,GAAQ,IAAI;QAClBZ,OAAO,CAAC9L,KAAK,CAAC,eAAe,EAAE0M,GAAG,CAAC;QACnC,IAAI,CAAC9D,eAAe,CAAC4F,aAAa,EAAE;QACpC/B,UAAU,CAAC,MAAK;UACd,IAAI,CAAC3D,MAAM,CAACmE,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACtC,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;;;uBAleWxE,gBAAgB,EAAAjJ,EAAA,CAAAmP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArP,EAAA,CAAAmP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvP,EAAA,CAAAmP,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAzP,EAAA,CAAAmP,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAA3P,EAAA,CAAAmP,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhB5G,gBAAgB;MAAA6G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7BpQ,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAuD;UAMjDD,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,+EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAoB,UAAA,KAAAkP,gCAAA,kBAUM;UAGNtQ,EAAA,CAAAoB,UAAA,KAAAmP,gCAAA,mBAmBM;UAGNvQ,EAAA,CAAAoB,UAAA,KAAAoP,gCAAA,mBAqBM;UAGNxQ,EAAA,CAAAoB,UAAA,KAAAqP,gCAAA,mBAoCM;UAGNzQ,EAAA,CAAAoB,UAAA,KAAAsP,gCAAA,qBA0pBM;UAGN1Q,EAAA,CAAAoB,UAAA,KAAAuP,gCAAA,oBA6OM;UACR3Q,EAAA,CAAAG,YAAA,EAAM;;;UA7+BEH,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAA2B,UAAA,UAAA0O,GAAA,CAAAlN,IAAA,CAAW;UAcdnD,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA2B,UAAA,SAAA0O,GAAA,CAAA7P,KAAA,CAAW;UAsBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2B,UAAA,SAAA0O,GAAA,CAAA1P,OAAA,CAAa;UAuBVX,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAA0O,GAAA,CAAAlN,IAAA,CAAU;UAuCVnD,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAA2B,UAAA,SAAA0O,GAAA,CAAAlN,IAAA,CAAU;UA6pBVnD,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAA2B,UAAA,SAAA0O,GAAA,CAAA9J,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}