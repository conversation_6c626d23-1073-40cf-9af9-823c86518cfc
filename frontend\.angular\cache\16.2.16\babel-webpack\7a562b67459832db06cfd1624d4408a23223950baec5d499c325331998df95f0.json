{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NotificationsRoutingModule } from './notifications-routing.module';\nimport { RouterModule } from '@angular/router';\nimport { NotificationListComponent } from './notification-list/notification-list.component';\nimport { MessageService } from 'src/app/services/message.service';\nimport * as i0 from \"@angular/core\";\nexport class NotificationsModule {\n  static {\n    this.ɵfac = function NotificationsModule_Factory(t) {\n      return new (t || NotificationsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NotificationsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService],\n      imports: [CommonModule, NotificationsRoutingModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotificationsModule, {\n    declarations: [NotificationListComponent],\n    imports: [CommonModule, NotificationsRoutingModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NotificationsRoutingModule", "RouterModule", "NotificationListComponent", "MessageService", "NotificationsModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\notifications\\notifications.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NotificationsRoutingModule } from './notifications-routing.module';\r\nimport { RouterModule } from '@angular/router';\r\nimport { NotificationListComponent } from './notification-list/notification-list.component';\r\nimport { MessageService } from 'src/app/services/message.service';\r\n\r\n@NgModule({\r\n  declarations: [NotificationListComponent],\r\n  imports: [CommonModule, NotificationsRoutingModule, RouterModule],\r\n  providers: [MessageService],\r\n})\r\nexport class NotificationsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,cAAc,QAAQ,kCAAkC;;AAOjE,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAFnB,CAACD,cAAc,CAAC;MAAAE,OAAA,GADjBN,YAAY,EAAEC,0BAA0B,EAAEC,YAAY;IAAA;EAAA;;;2EAGrDG,mBAAmB;IAAAE,YAAA,GAJfJ,yBAAyB;IAAAG,OAAA,GAC9BN,YAAY,EAAEC,0BAA0B,EAAEC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}