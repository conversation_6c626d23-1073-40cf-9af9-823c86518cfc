{"ast": null, "code": "\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.isExtractableFile`.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an extractable file.\n * @see [`extract-files` `isExtractableFile` docs](https://github.com/jaydenseric/extract-files#function-isextractablefile).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import isExtractableFile from \"apollo-upload-client/public/isExtractableFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const isExtractableFile = require(\"apollo-upload-client/public/isExtractableFile.js\");\n * ```\n */\nmodule.exports = require(\"extract-files/public/isExtractableFile.js\");", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-upload-client/public/isExtractableFile.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.isExtractableFile`.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an extractable file.\n * @see [`extract-files` `isExtractableFile` docs](https://github.com/jaydenseric/extract-files#function-isextractablefile).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import isExtractableFile from \"apollo-upload-client/public/isExtractableFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const isExtractableFile = require(\"apollo-upload-client/public/isExtractableFile.js\");\n * ```\n */\nmodule.exports = require(\"extract-files/public/isExtractableFile.js\");\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,2CAA2C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}