{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ProjectsRoutingModule } from './projects-routing.module';\nimport { ProjectListComponent } from './project-list/project-list.component';\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class ProjectsModule {\n  static {\n    this.ɵfac = function ProjectsModule_Factory(t) {\n      return new (t || ProjectsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProjectsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ProjectsRoutingModule, FormsModule, ReactiveFormsModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProjectsModule, {\n    declarations: [ProjectListComponent, ProjectDetailComponent, ProjectSubmissionComponent],\n    imports: [CommonModule, ProjectsRoutingModule, FormsModule, ReactiveFormsModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ProjectsRoutingModule", "ProjectListComponent", "ProjectDetailComponent", "ProjectSubmissionComponent", "FormsModule", "ReactiveFormsModule", "RouterModule", "ProjectsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\projects\\projects.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ProjectsRoutingModule } from './projects-routing.module';\r\nimport { ProjectListComponent } from './project-list/project-list.component';\r\nimport { ProjectDetailComponent } from './project-detail/project-detail.component';\r\nimport { ProjectSubmissionComponent } from './project-submission/project-submission.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProjectListComponent,\r\n    ProjectDetailComponent,\r\n    ProjectSubmissionComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ProjectsRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    RouterModule\r\n  ]\r\n})\r\nexport class ProjectsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;;AAgB9C,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAPvBR,YAAY,EACZC,qBAAqB,EACrBI,WAAW,EACXC,mBAAmB,EACnBC,YAAY;IAAA;EAAA;;;2EAGHC,cAAc;IAAAC,YAAA,GAZvBP,oBAAoB,EACpBC,sBAAsB,EACtBC,0BAA0B;IAAAM,OAAA,GAG1BV,YAAY,EACZC,qBAAqB,EACrBI,WAAW,EACXC,mBAAmB,EACnBC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}