{"ast": null, "code": "import { invariant } from \"../globals/index.js\";\nimport { visit, BREAK, Kind } from \"graphql\";\nexport function shouldInclude(_a, variables) {\n  var directives = _a.directives;\n  if (!directives || !directives.length) {\n    return true;\n  }\n  return getInclusionDirectives(directives).every(function (_a) {\n    var directive = _a.directive,\n      ifArgument = _a.ifArgument;\n    var evaledValue = false;\n    if (ifArgument.value.kind === \"Variable\") {\n      evaledValue = variables && variables[ifArgument.value.name.value];\n      invariant(evaledValue !== void 0, 78, directive.name.value);\n    } else {\n      evaledValue = ifArgument.value.value;\n    }\n    return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n  });\n}\nexport function getDirectiveNames(root) {\n  var names = [];\n  visit(root, {\n    Directive: function (node) {\n      names.push(node.name.value);\n    }\n  });\n  return names;\n}\nexport var hasAnyDirectives = function (names, root) {\n  return hasDirectives(names, root, false);\n};\nexport var hasAllDirectives = function (names, root) {\n  return hasDirectives(names, root, true);\n};\nexport function hasDirectives(names, root, all) {\n  var nameSet = new Set(names);\n  var uniqueCount = nameSet.size;\n  visit(root, {\n    Directive: function (node) {\n      if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n        return BREAK;\n      }\n    }\n  });\n  // If we found all the names, nameSet will be empty. If we only care about\n  // finding some of them, the < condition is sufficient.\n  return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\nexport function hasClientExports(document) {\n  return document && hasDirectives([\"client\", \"export\"], document, true);\n}\nfunction isInclusionDirective(_a) {\n  var value = _a.name.value;\n  return value === \"skip\" || value === \"include\";\n}\nexport function getInclusionDirectives(directives) {\n  var result = [];\n  if (directives && directives.length) {\n    directives.forEach(function (directive) {\n      if (!isInclusionDirective(directive)) return;\n      var directiveArguments = directive.arguments;\n      var directiveName = directive.name.value;\n      invariant(directiveArguments && directiveArguments.length === 1, 79, directiveName);\n      var ifArgument = directiveArguments[0];\n      invariant(ifArgument.name && ifArgument.name.value === \"if\", 80, directiveName);\n      var ifValue = ifArgument.value;\n      // means it has to be a variable value if this is a valid @skip or @include directive\n      invariant(ifValue && (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"), 81, directiveName);\n      result.push({\n        directive: directive,\n        ifArgument: ifArgument\n      });\n    });\n  }\n  return result;\n}\n/** @internal */\nexport function getFragmentMaskMode(fragment) {\n  var _a, _b;\n  var directive = (_a = fragment.directives) === null || _a === void 0 ? void 0 : _a.find(function (_a) {\n    var name = _a.name;\n    return name.value === \"unmask\";\n  });\n  if (!directive) {\n    return \"mask\";\n  }\n  var modeArg = (_b = directive.arguments) === null || _b === void 0 ? void 0 : _b.find(function (_a) {\n    var name = _a.name;\n    return name.value === \"mode\";\n  });\n  if (globalThis.__DEV__ !== false) {\n    if (modeArg) {\n      if (modeArg.value.kind === Kind.VARIABLE) {\n        globalThis.__DEV__ !== false && invariant.warn(82);\n      } else if (modeArg.value.kind !== Kind.STRING) {\n        globalThis.__DEV__ !== false && invariant.warn(83);\n      } else if (modeArg.value.value !== \"migrate\") {\n        globalThis.__DEV__ !== false && invariant.warn(84, modeArg.value.value);\n      }\n    }\n  }\n  if (modeArg && \"value\" in modeArg.value && modeArg.value.value === \"migrate\") {\n    return \"migrate\";\n  }\n  return \"unmask\";\n}", "map": {"version": 3, "names": ["invariant", "visit", "BREAK", "Kind", "shouldInclude", "_a", "variables", "directives", "length", "getInclusionDirectives", "every", "directive", "ifArgument", "evale<PERSON><PERSON><PERSON><PERSON>", "value", "kind", "name", "getDirectiveNames", "root", "names", "Directive", "node", "push", "hasAnyDirectives", "hasDirectives", "hasAllDirectives", "all", "nameSet", "Set", "uniqueCount", "size", "delete", "hasClientExports", "document", "isInclusionDirective", "result", "for<PERSON>ach", "directiveArguments", "arguments", "directiveName", "ifValue", "getFragmentMaskMode", "fragment", "_b", "find", "modeArg", "globalThis", "__DEV__", "VARIABLE", "warn", "STRING"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/directives.js"], "sourcesContent": ["import { invariant } from \"../globals/index.js\";\nimport { visit, BREAK, Kind } from \"graphql\";\nexport function shouldInclude(_a, variables) {\n    var directives = _a.directives;\n    if (!directives || !directives.length) {\n        return true;\n    }\n    return getInclusionDirectives(directives).every(function (_a) {\n        var directive = _a.directive, ifArgument = _a.ifArgument;\n        var evaledValue = false;\n        if (ifArgument.value.kind === \"Variable\") {\n            evaledValue =\n                variables && variables[ifArgument.value.name.value];\n            invariant(evaledValue !== void 0, 78, directive.name.value);\n        }\n        else {\n            evaledValue = ifArgument.value.value;\n        }\n        return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n    });\n}\nexport function getDirectiveNames(root) {\n    var names = [];\n    visit(root, {\n        Directive: function (node) {\n            names.push(node.name.value);\n        },\n    });\n    return names;\n}\nexport var hasAnyDirectives = function (names, root) {\n    return hasDirectives(names, root, false);\n};\nexport var hasAllDirectives = function (names, root) {\n    return hasDirectives(names, root, true);\n};\nexport function hasDirectives(names, root, all) {\n    var nameSet = new Set(names);\n    var uniqueCount = nameSet.size;\n    visit(root, {\n        Directive: function (node) {\n            if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n                return BREAK;\n            }\n        },\n    });\n    // If we found all the names, nameSet will be empty. If we only care about\n    // finding some of them, the < condition is sufficient.\n    return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\nexport function hasClientExports(document) {\n    return document && hasDirectives([\"client\", \"export\"], document, true);\n}\nfunction isInclusionDirective(_a) {\n    var value = _a.name.value;\n    return value === \"skip\" || value === \"include\";\n}\nexport function getInclusionDirectives(directives) {\n    var result = [];\n    if (directives && directives.length) {\n        directives.forEach(function (directive) {\n            if (!isInclusionDirective(directive))\n                return;\n            var directiveArguments = directive.arguments;\n            var directiveName = directive.name.value;\n            invariant(directiveArguments && directiveArguments.length === 1, 79, directiveName);\n            var ifArgument = directiveArguments[0];\n            invariant(ifArgument.name && ifArgument.name.value === \"if\", 80, directiveName);\n            var ifValue = ifArgument.value;\n            // means it has to be a variable value if this is a valid @skip or @include directive\n            invariant(ifValue &&\n                (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"), 81, directiveName);\n            result.push({ directive: directive, ifArgument: ifArgument });\n        });\n    }\n    return result;\n}\n/** @internal */\nexport function getFragmentMaskMode(fragment) {\n    var _a, _b;\n    var directive = (_a = fragment.directives) === null || _a === void 0 ? void 0 : _a.find(function (_a) {\n        var name = _a.name;\n        return name.value === \"unmask\";\n    });\n    if (!directive) {\n        return \"mask\";\n    }\n    var modeArg = (_b = directive.arguments) === null || _b === void 0 ? void 0 : _b.find(function (_a) {\n        var name = _a.name;\n        return name.value === \"mode\";\n    });\n    if (globalThis.__DEV__ !== false) {\n        if (modeArg) {\n            if (modeArg.value.kind === Kind.VARIABLE) {\n                globalThis.__DEV__ !== false && invariant.warn(82);\n            }\n            else if (modeArg.value.kind !== Kind.STRING) {\n                globalThis.__DEV__ !== false && invariant.warn(83);\n            }\n            else if (modeArg.value.value !== \"migrate\") {\n                globalThis.__DEV__ !== false && invariant.warn(84, modeArg.value.value);\n            }\n        }\n    }\n    if (modeArg &&\n        \"value\" in modeArg.value &&\n        modeArg.value.value === \"migrate\") {\n        return \"migrate\";\n    }\n    return \"unmask\";\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,SAAS;AAC5C,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,SAAS,EAAE;EACzC,IAAIC,UAAU,GAAGF,EAAE,CAACE,UAAU;EAC9B,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACC,MAAM,EAAE;IACnC,OAAO,IAAI;EACf;EACA,OAAOC,sBAAsB,CAACF,UAAU,CAAC,CAACG,KAAK,CAAC,UAAUL,EAAE,EAAE;IAC1D,IAAIM,SAAS,GAAGN,EAAE,CAACM,SAAS;MAAEC,UAAU,GAAGP,EAAE,CAACO,UAAU;IACxD,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAID,UAAU,CAACE,KAAK,CAACC,IAAI,KAAK,UAAU,EAAE;MACtCF,WAAW,GACPP,SAAS,IAAIA,SAAS,CAACM,UAAU,CAACE,KAAK,CAACE,IAAI,CAACF,KAAK,CAAC;MACvDd,SAAS,CAACa,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,EAAEF,SAAS,CAACK,IAAI,CAACF,KAAK,CAAC;IAC/D,CAAC,MACI;MACDD,WAAW,GAAGD,UAAU,CAACE,KAAK,CAACA,KAAK;IACxC;IACA,OAAOH,SAAS,CAACK,IAAI,CAACF,KAAK,KAAK,MAAM,GAAG,CAACD,WAAW,GAAGA,WAAW;EACvE,CAAC,CAAC;AACN;AACA,OAAO,SAASI,iBAAiBA,CAACC,IAAI,EAAE;EACpC,IAAIC,KAAK,GAAG,EAAE;EACdlB,KAAK,CAACiB,IAAI,EAAE;IACRE,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACvBF,KAAK,CAACG,IAAI,CAACD,IAAI,CAACL,IAAI,CAACF,KAAK,CAAC;IAC/B;EACJ,CAAC,CAAC;EACF,OAAOK,KAAK;AAChB;AACA,OAAO,IAAII,gBAAgB,GAAG,SAAAA,CAAUJ,KAAK,EAAED,IAAI,EAAE;EACjD,OAAOM,aAAa,CAACL,KAAK,EAAED,IAAI,EAAE,KAAK,CAAC;AAC5C,CAAC;AACD,OAAO,IAAIO,gBAAgB,GAAG,SAAAA,CAAUN,KAAK,EAAED,IAAI,EAAE;EACjD,OAAOM,aAAa,CAACL,KAAK,EAAED,IAAI,EAAE,IAAI,CAAC;AAC3C,CAAC;AACD,OAAO,SAASM,aAAaA,CAACL,KAAK,EAAED,IAAI,EAAEQ,GAAG,EAAE;EAC5C,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAACT,KAAK,CAAC;EAC5B,IAAIU,WAAW,GAAGF,OAAO,CAACG,IAAI;EAC9B7B,KAAK,CAACiB,IAAI,EAAE;IACRE,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACvB,IAAIM,OAAO,CAACI,MAAM,CAACV,IAAI,CAACL,IAAI,CAACF,KAAK,CAAC,KAAK,CAACY,GAAG,IAAI,CAACC,OAAO,CAACG,IAAI,CAAC,EAAE;QAC5D,OAAO5B,KAAK;MAChB;IACJ;EACJ,CAAC,CAAC;EACF;EACA;EACA,OAAOwB,GAAG,GAAG,CAACC,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACG,IAAI,GAAGD,WAAW;AAC3D;AACA,OAAO,SAASG,gBAAgBA,CAACC,QAAQ,EAAE;EACvC,OAAOA,QAAQ,IAAIT,aAAa,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAES,QAAQ,EAAE,IAAI,CAAC;AAC1E;AACA,SAASC,oBAAoBA,CAAC7B,EAAE,EAAE;EAC9B,IAAIS,KAAK,GAAGT,EAAE,CAACW,IAAI,CAACF,KAAK;EACzB,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS;AAClD;AACA,OAAO,SAASL,sBAAsBA,CAACF,UAAU,EAAE;EAC/C,IAAI4B,MAAM,GAAG,EAAE;EACf,IAAI5B,UAAU,IAAIA,UAAU,CAACC,MAAM,EAAE;IACjCD,UAAU,CAAC6B,OAAO,CAAC,UAAUzB,SAAS,EAAE;MACpC,IAAI,CAACuB,oBAAoB,CAACvB,SAAS,CAAC,EAChC;MACJ,IAAI0B,kBAAkB,GAAG1B,SAAS,CAAC2B,SAAS;MAC5C,IAAIC,aAAa,GAAG5B,SAAS,CAACK,IAAI,CAACF,KAAK;MACxCd,SAAS,CAACqC,kBAAkB,IAAIA,kBAAkB,CAAC7B,MAAM,KAAK,CAAC,EAAE,EAAE,EAAE+B,aAAa,CAAC;MACnF,IAAI3B,UAAU,GAAGyB,kBAAkB,CAAC,CAAC,CAAC;MACtCrC,SAAS,CAACY,UAAU,CAACI,IAAI,IAAIJ,UAAU,CAACI,IAAI,CAACF,KAAK,KAAK,IAAI,EAAE,EAAE,EAAEyB,aAAa,CAAC;MAC/E,IAAIC,OAAO,GAAG5B,UAAU,CAACE,KAAK;MAC9B;MACAd,SAAS,CAACwC,OAAO,KACZA,OAAO,CAACzB,IAAI,KAAK,UAAU,IAAIyB,OAAO,CAACzB,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,EAAEwB,aAAa,CAAC;MACxFJ,MAAM,CAACb,IAAI,CAAC;QAAEX,SAAS,EAAEA,SAAS;QAAEC,UAAU,EAAEA;MAAW,CAAC,CAAC;IACjE,CAAC,CAAC;EACN;EACA,OAAOuB,MAAM;AACjB;AACA;AACA,OAAO,SAASM,mBAAmBA,CAACC,QAAQ,EAAE;EAC1C,IAAIrC,EAAE,EAAEsC,EAAE;EACV,IAAIhC,SAAS,GAAG,CAACN,EAAE,GAAGqC,QAAQ,CAACnC,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuC,IAAI,CAAC,UAAUvC,EAAE,EAAE;IAClG,IAAIW,IAAI,GAAGX,EAAE,CAACW,IAAI;IAClB,OAAOA,IAAI,CAACF,KAAK,KAAK,QAAQ;EAClC,CAAC,CAAC;EACF,IAAI,CAACH,SAAS,EAAE;IACZ,OAAO,MAAM;EACjB;EACA,IAAIkC,OAAO,GAAG,CAACF,EAAE,GAAGhC,SAAS,CAAC2B,SAAS,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAC,UAAUvC,EAAE,EAAE;IAChG,IAAIW,IAAI,GAAGX,EAAE,CAACW,IAAI;IAClB,OAAOA,IAAI,CAACF,KAAK,KAAK,MAAM;EAChC,CAAC,CAAC;EACF,IAAIgC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;IAC9B,IAAIF,OAAO,EAAE;MACT,IAAIA,OAAO,CAAC/B,KAAK,CAACC,IAAI,KAAKZ,IAAI,CAAC6C,QAAQ,EAAE;QACtCF,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI/C,SAAS,CAACiD,IAAI,CAAC,EAAE,CAAC;MACtD,CAAC,MACI,IAAIJ,OAAO,CAAC/B,KAAK,CAACC,IAAI,KAAKZ,IAAI,CAAC+C,MAAM,EAAE;QACzCJ,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI/C,SAAS,CAACiD,IAAI,CAAC,EAAE,CAAC;MACtD,CAAC,MACI,IAAIJ,OAAO,CAAC/B,KAAK,CAACA,KAAK,KAAK,SAAS,EAAE;QACxCgC,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI/C,SAAS,CAACiD,IAAI,CAAC,EAAE,EAAEJ,OAAO,CAAC/B,KAAK,CAACA,KAAK,CAAC;MAC3E;IACJ;EACJ;EACA,IAAI+B,OAAO,IACP,OAAO,IAAIA,OAAO,CAAC/B,KAAK,IACxB+B,OAAO,CAAC/B,KAAK,CAACA,KAAK,KAAK,SAAS,EAAE;IACnC,OAAO,SAAS;EACpB;EACA,OAAO,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}