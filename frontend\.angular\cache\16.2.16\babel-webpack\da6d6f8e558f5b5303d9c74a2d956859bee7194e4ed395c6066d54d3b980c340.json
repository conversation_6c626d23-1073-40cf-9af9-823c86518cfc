{"ast": null, "code": "import { getOperationDefinition } from \"./getFromAST.js\";\nfunction isOperation(document, operation) {\n  var _a;\n  return ((_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation) === operation;\n}\nexport function isMutationOperation(document) {\n  return isOperation(document, \"mutation\");\n}\nexport function isQueryOperation(document) {\n  return isOperation(document, \"query\");\n}\nexport function isSubscriptionOperation(document) {\n  return isOperation(document, \"subscription\");\n}", "map": {"version": 3, "names": ["getOperationDefinition", "isOperation", "document", "operation", "_a", "isMutationOperation", "isQueryOperation", "isSubscriptionOperation"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/operations.js"], "sourcesContent": ["import { getOperationDefinition } from \"./getFromAST.js\";\nfunction isOperation(document, operation) {\n    var _a;\n    return ((_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation) === operation;\n}\nexport function isMutationOperation(document) {\n    return isOperation(document, \"mutation\");\n}\nexport function isQueryOperation(document) {\n    return isOperation(document, \"query\");\n}\nexport function isSubscriptionOperation(document) {\n    return isOperation(document, \"subscription\");\n}\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,iBAAiB;AACxD,SAASC,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACtC,IAAIC,EAAE;EACN,OAAO,CAAC,CAACA,EAAE,GAAGJ,sBAAsB,CAACE,QAAQ,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,SAAS,MAAMA,SAAS;AACpH;AACA,OAAO,SAASE,mBAAmBA,CAACH,QAAQ,EAAE;EAC1C,OAAOD,WAAW,CAACC,QAAQ,EAAE,UAAU,CAAC;AAC5C;AACA,OAAO,SAASI,gBAAgBA,CAACJ,QAAQ,EAAE;EACvC,OAAOD,WAAW,CAACC,QAAQ,EAAE,OAAO,CAAC;AACzC;AACA,OAAO,SAASK,uBAAuBA,CAACL,QAAQ,EAAE;EAC9C,OAAOD,WAAW,CAACC,QAAQ,EAAE,cAAc,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}