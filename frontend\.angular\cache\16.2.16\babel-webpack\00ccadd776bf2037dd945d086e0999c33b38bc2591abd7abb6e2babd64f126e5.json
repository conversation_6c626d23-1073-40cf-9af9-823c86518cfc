{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { execute } from \"../link/core/index.js\";\nimport { addNonReactiveToNamedFragments, hasDirectives, isExecutionPatchIncrementalResult, isExecutionPatchResult, isFullyUnmaskedOperation, removeDirectivesFromDocument } from \"../utilities/index.js\";\nimport { canonicalStringify } from \"../cache/index.js\";\nimport { getDefaultValues, getOperationDefinition, getOperationName, hasClientExports, graphQLResultHasError, getGraphQLErrorsFromResult, Observable, asyncMap, isNonEmptyArray, Concast, makeUniqueId, isDocumentNode, isNonNullObject, DocumentTransform } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/common/incrementalResult.js\";\nimport { ApolloError, isApolloError, graphQLResultHasProtocolErrors } from \"../errors/index.js\";\nimport { ObservableQuery, logMissingFieldErrors } from \"./ObservableQuery.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { QueryInfo, shouldWriteResult } from \"./QueryInfo.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../errors/index.js\";\nimport { print } from \"../utilities/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar IGNORE = Object.create(null);\nimport { Trie } from \"@wry/trie\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../utilities/index.js\";\nimport { maskFragment, maskOperation } from \"../masking/index.js\";\nvar QueryManager = /** @class */function () {\n  function QueryManager(options) {\n    var _this = this;\n    this.clientAwareness = {};\n    // All the queries that the QueryManager is currently managing (not\n    // including mutations and subscriptions).\n    this.queries = new Map();\n    // Maps from queryId strings to Promise rejection functions for\n    // currently active queries and fetches.\n    // Use protected instead of private field so\n    // @apollo/experimental-nextjs-app-support can access type info.\n    this.fetchCancelFns = new Map();\n    this.transformCache = new AutoCleanedWeakCache(cacheSizes[\"queryManager.getDocumentInfo\"] || 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */);\n    this.queryIdCounter = 1;\n    this.requestIdCounter = 1;\n    this.mutationIdCounter = 1;\n    // Use protected instead of private field so\n    // @apollo/experimental-nextjs-app-support can access type info.\n    this.inFlightLinkObservables = new Trie(false);\n    this.noCacheWarningsByQueryId = new Set();\n    var defaultDocumentTransform = new DocumentTransform(function (document) {\n      return _this.cache.transformDocument(document);\n    },\n    // Allow the apollo cache to manage its own transform caches\n    {\n      cache: false\n    });\n    this.cache = options.cache;\n    this.link = options.link;\n    this.defaultOptions = options.defaultOptions;\n    this.queryDeduplication = options.queryDeduplication;\n    this.clientAwareness = options.clientAwareness;\n    this.localState = options.localState;\n    this.ssrMode = options.ssrMode;\n    this.assumeImmutableResults = options.assumeImmutableResults;\n    this.dataMasking = options.dataMasking;\n    var documentTransform = options.documentTransform;\n    this.documentTransform = documentTransform ? defaultDocumentTransform.concat(documentTransform)\n    // The custom document transform may add new fragment spreads or new\n    // field selections, so we want to give the cache a chance to run\n    // again. For example, the InMemoryCache adds __typename to field\n    // selections and fragments from the fragment registry.\n    .concat(defaultDocumentTransform) : defaultDocumentTransform;\n    this.defaultContext = options.defaultContext || Object.create(null);\n    if (this.onBroadcast = options.onBroadcast) {\n      this.mutationStore = Object.create(null);\n    }\n  }\n  /**\n   * Call this method to terminate any active query processes, making it safe\n   * to dispose of this QueryManager instance.\n   */\n  QueryManager.prototype.stop = function () {\n    var _this = this;\n    this.queries.forEach(function (_info, queryId) {\n      _this.stopQueryNoBroadcast(queryId);\n    });\n    this.cancelPendingFetches(newInvariantError(27));\n  };\n  QueryManager.prototype.cancelPendingFetches = function (error) {\n    this.fetchCancelFns.forEach(function (cancel) {\n      return cancel(error);\n    });\n    this.fetchCancelFns.clear();\n  };\n  QueryManager.prototype.mutate = function (_a) {\n    return __awaiter(this, arguments, void 0, function (_b) {\n      var mutationId, hasClientExports, mutationStoreValue, isOptimistic, self;\n      var _c, _d;\n      var mutation = _b.mutation,\n        variables = _b.variables,\n        optimisticResponse = _b.optimisticResponse,\n        updateQueries = _b.updateQueries,\n        _e = _b.refetchQueries,\n        refetchQueries = _e === void 0 ? [] : _e,\n        _f = _b.awaitRefetchQueries,\n        awaitRefetchQueries = _f === void 0 ? false : _f,\n        updateWithProxyFn = _b.update,\n        onQueryUpdated = _b.onQueryUpdated,\n        _g = _b.fetchPolicy,\n        fetchPolicy = _g === void 0 ? ((_c = this.defaultOptions.mutate) === null || _c === void 0 ? void 0 : _c.fetchPolicy) || \"network-only\" : _g,\n        _h = _b.errorPolicy,\n        errorPolicy = _h === void 0 ? ((_d = this.defaultOptions.mutate) === null || _d === void 0 ? void 0 : _d.errorPolicy) || \"none\" : _h,\n        keepRootFields = _b.keepRootFields,\n        context = _b.context;\n      return __generator(this, function (_j) {\n        switch (_j.label) {\n          case 0:\n            invariant(mutation, 28);\n            invariant(fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\", 29);\n            mutationId = this.generateMutationId();\n            mutation = this.cache.transformForLink(this.transform(mutation));\n            hasClientExports = this.getDocumentInfo(mutation).hasClientExports;\n            variables = this.getVariables(mutation, variables);\n            if (!hasClientExports) return [3 /*break*/, 2];\n            return [4 /*yield*/, this.localState.addExportedVariables(mutation, variables, context)];\n          case 1:\n            variables = _j.sent();\n            _j.label = 2;\n          case 2:\n            mutationStoreValue = this.mutationStore && (this.mutationStore[mutationId] = {\n              mutation: mutation,\n              variables: variables,\n              loading: true,\n              error: null\n            });\n            isOptimistic = optimisticResponse && this.markMutationOptimistic(optimisticResponse, {\n              mutationId: mutationId,\n              document: mutation,\n              variables: variables,\n              fetchPolicy: fetchPolicy,\n              errorPolicy: errorPolicy,\n              context: context,\n              updateQueries: updateQueries,\n              update: updateWithProxyFn,\n              keepRootFields: keepRootFields\n            });\n            this.broadcastQueries();\n            self = this;\n            return [2 /*return*/, new Promise(function (resolve, reject) {\n              return asyncMap(self.getObservableFromLink(mutation, __assign(__assign({}, context), {\n                optimisticResponse: isOptimistic ? optimisticResponse : void 0\n              }), variables, {}, false), function (result) {\n                if (graphQLResultHasError(result) && errorPolicy === \"none\") {\n                  throw new ApolloError({\n                    graphQLErrors: getGraphQLErrorsFromResult(result)\n                  });\n                }\n                if (mutationStoreValue) {\n                  mutationStoreValue.loading = false;\n                  mutationStoreValue.error = null;\n                }\n                var storeResult = __assign({}, result);\n                if (typeof refetchQueries === \"function\") {\n                  refetchQueries = refetchQueries(storeResult);\n                }\n                if (errorPolicy === \"ignore\" && graphQLResultHasError(storeResult)) {\n                  delete storeResult.errors;\n                }\n                return self.markMutationResult({\n                  mutationId: mutationId,\n                  result: storeResult,\n                  document: mutation,\n                  variables: variables,\n                  fetchPolicy: fetchPolicy,\n                  errorPolicy: errorPolicy,\n                  context: context,\n                  update: updateWithProxyFn,\n                  updateQueries: updateQueries,\n                  awaitRefetchQueries: awaitRefetchQueries,\n                  refetchQueries: refetchQueries,\n                  removeOptimistic: isOptimistic ? mutationId : void 0,\n                  onQueryUpdated: onQueryUpdated,\n                  keepRootFields: keepRootFields\n                });\n              }).subscribe({\n                next: function (storeResult) {\n                  self.broadcastQueries();\n                  // Since mutations might receive multiple payloads from the\n                  // ApolloLink chain (e.g. when used with @defer),\n                  // we resolve with a SingleExecutionResult or after the final\n                  // ExecutionPatchResult has arrived and we have assembled the\n                  // multipart response into a single result.\n                  if (!(\"hasNext\" in storeResult) || storeResult.hasNext === false) {\n                    resolve(__assign(__assign({}, storeResult), {\n                      data: self.maskOperation({\n                        document: mutation,\n                        data: storeResult.data,\n                        fetchPolicy: fetchPolicy,\n                        id: mutationId\n                      })\n                    }));\n                  }\n                },\n                error: function (err) {\n                  if (mutationStoreValue) {\n                    mutationStoreValue.loading = false;\n                    mutationStoreValue.error = err;\n                  }\n                  if (isOptimistic) {\n                    self.cache.removeOptimistic(mutationId);\n                  }\n                  self.broadcastQueries();\n                  reject(err instanceof ApolloError ? err : new ApolloError({\n                    networkError: err\n                  }));\n                }\n              });\n            })];\n        }\n      });\n    });\n  };\n  QueryManager.prototype.markMutationResult = function (mutation, cache) {\n    var _this = this;\n    if (cache === void 0) {\n      cache = this.cache;\n    }\n    var result = mutation.result;\n    var cacheWrites = [];\n    var skipCache = mutation.fetchPolicy === \"no-cache\";\n    if (!skipCache && shouldWriteResult(result, mutation.errorPolicy)) {\n      if (!isExecutionPatchIncrementalResult(result)) {\n        cacheWrites.push({\n          result: result.data,\n          dataId: \"ROOT_MUTATION\",\n          query: mutation.document,\n          variables: mutation.variables\n        });\n      }\n      if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n        var diff = cache.diff({\n          id: \"ROOT_MUTATION\",\n          // The cache complains if passed a mutation where it expects a\n          // query, so we transform mutations and subscriptions to queries\n          // (only once, thanks to this.transformCache).\n          query: this.getDocumentInfo(mutation.document).asQuery,\n          variables: mutation.variables,\n          optimistic: false,\n          returnPartialData: true\n        });\n        var mergedData = void 0;\n        if (diff.result) {\n          mergedData = mergeIncrementalData(diff.result, result);\n        }\n        if (typeof mergedData !== \"undefined\") {\n          // cast the ExecutionPatchResult to FetchResult here since\n          // ExecutionPatchResult never has `data` when returned from the server\n          result.data = mergedData;\n          cacheWrites.push({\n            result: mergedData,\n            dataId: \"ROOT_MUTATION\",\n            query: mutation.document,\n            variables: mutation.variables\n          });\n        }\n      }\n      var updateQueries_1 = mutation.updateQueries;\n      if (updateQueries_1) {\n        this.queries.forEach(function (_a, queryId) {\n          var observableQuery = _a.observableQuery;\n          var queryName = observableQuery && observableQuery.queryName;\n          if (!queryName || !hasOwnProperty.call(updateQueries_1, queryName)) {\n            return;\n          }\n          var updater = updateQueries_1[queryName];\n          var _b = _this.queries.get(queryId),\n            document = _b.document,\n            variables = _b.variables;\n          // Read the current query result from the store.\n          var _c = cache.diff({\n              query: document,\n              variables: variables,\n              returnPartialData: true,\n              optimistic: false\n            }),\n            currentQueryResult = _c.result,\n            complete = _c.complete;\n          if (complete && currentQueryResult) {\n            // Run our reducer using the current query result and the mutation result.\n            var nextQueryResult = updater(currentQueryResult, {\n              mutationResult: result,\n              queryName: document && getOperationName(document) || void 0,\n              queryVariables: variables\n            });\n            // Write the modified result back into the store if we got a new result.\n            if (nextQueryResult) {\n              cacheWrites.push({\n                result: nextQueryResult,\n                dataId: \"ROOT_QUERY\",\n                query: document,\n                variables: variables\n              });\n            }\n          }\n        });\n      }\n    }\n    if (cacheWrites.length > 0 || (mutation.refetchQueries || \"\").length > 0 || mutation.update || mutation.onQueryUpdated || mutation.removeOptimistic) {\n      var results_1 = [];\n      this.refetchQueries({\n        updateCache: function (cache) {\n          if (!skipCache) {\n            cacheWrites.forEach(function (write) {\n              return cache.write(write);\n            });\n          }\n          // If the mutation has some writes associated with it then we need to\n          // apply those writes to the store by running this reducer again with\n          // a write action.\n          var update = mutation.update;\n          // Determine whether result is a SingleExecutionResult,\n          // or the final ExecutionPatchResult.\n          var isFinalResult = !isExecutionPatchResult(result) || isExecutionPatchIncrementalResult(result) && !result.hasNext;\n          if (update) {\n            if (!skipCache) {\n              // Re-read the ROOT_MUTATION data we just wrote into the cache\n              // (the first cache.write call in the cacheWrites.forEach loop\n              // above), so field read functions have a chance to run for\n              // fields within mutation result objects.\n              var diff = cache.diff({\n                id: \"ROOT_MUTATION\",\n                // The cache complains if passed a mutation where it expects a\n                // query, so we transform mutations and subscriptions to queries\n                // (only once, thanks to this.transformCache).\n                query: _this.getDocumentInfo(mutation.document).asQuery,\n                variables: mutation.variables,\n                optimistic: false,\n                returnPartialData: true\n              });\n              if (diff.complete) {\n                result = __assign(__assign({}, result), {\n                  data: diff.result\n                });\n                if (\"incremental\" in result) {\n                  delete result.incremental;\n                }\n                if (\"hasNext\" in result) {\n                  delete result.hasNext;\n                }\n              }\n            }\n            // If we've received the whole response,\n            // either a SingleExecutionResult or the final ExecutionPatchResult,\n            // call the update function.\n            if (isFinalResult) {\n              update(cache, result, {\n                context: mutation.context,\n                variables: mutation.variables\n              });\n            }\n          }\n          // TODO Do this with cache.evict({ id: 'ROOT_MUTATION' }) but make it\n          // shallow to allow rolling back optimistic evictions.\n          if (!skipCache && !mutation.keepRootFields && isFinalResult) {\n            cache.modify({\n              id: \"ROOT_MUTATION\",\n              fields: function (value, _a) {\n                var fieldName = _a.fieldName,\n                  DELETE = _a.DELETE;\n                return fieldName === \"__typename\" ? value : DELETE;\n              }\n            });\n          }\n        },\n        include: mutation.refetchQueries,\n        // Write the final mutation.result to the root layer of the cache.\n        optimistic: false,\n        // Remove the corresponding optimistic layer at the same time as we\n        // write the final non-optimistic result.\n        removeOptimistic: mutation.removeOptimistic,\n        // Let the caller of client.mutate optionally determine the refetching\n        // behavior for watched queries after the mutation.update function runs.\n        // If no onQueryUpdated function was provided for this mutation, pass\n        // null instead of undefined to disable the default refetching behavior.\n        onQueryUpdated: mutation.onQueryUpdated || null\n      }).forEach(function (result) {\n        return results_1.push(result);\n      });\n      if (mutation.awaitRefetchQueries || mutation.onQueryUpdated) {\n        // Returning a promise here makes the mutation await that promise, so we\n        // include results in that promise's work if awaitRefetchQueries or an\n        // onQueryUpdated function was specified.\n        return Promise.all(results_1).then(function () {\n          return result;\n        });\n      }\n    }\n    return Promise.resolve(result);\n  };\n  QueryManager.prototype.markMutationOptimistic = function (optimisticResponse, mutation) {\n    var _this = this;\n    var data = typeof optimisticResponse === \"function\" ? optimisticResponse(mutation.variables, {\n      IGNORE: IGNORE\n    }) : optimisticResponse;\n    if (data === IGNORE) {\n      return false;\n    }\n    this.cache.recordOptimisticTransaction(function (cache) {\n      try {\n        _this.markMutationResult(__assign(__assign({}, mutation), {\n          result: {\n            data: data\n          }\n        }), cache);\n      } catch (error) {\n        globalThis.__DEV__ !== false && invariant.error(error);\n      }\n    }, mutation.mutationId);\n    return true;\n  };\n  QueryManager.prototype.fetchQuery = function (queryId, options, networkStatus) {\n    return this.fetchConcastWithInfo(this.getOrCreateQuery(queryId), options, networkStatus).concast.promise;\n  };\n  QueryManager.prototype.getQueryStore = function () {\n    var store = Object.create(null);\n    this.queries.forEach(function (info, queryId) {\n      store[queryId] = {\n        variables: info.variables,\n        networkStatus: info.networkStatus,\n        networkError: info.networkError,\n        graphQLErrors: info.graphQLErrors\n      };\n    });\n    return store;\n  };\n  QueryManager.prototype.resetErrors = function (queryId) {\n    var queryInfo = this.queries.get(queryId);\n    if (queryInfo) {\n      queryInfo.networkError = undefined;\n      queryInfo.graphQLErrors = [];\n    }\n  };\n  QueryManager.prototype.transform = function (document) {\n    return this.documentTransform.transformDocument(document);\n  };\n  QueryManager.prototype.getDocumentInfo = function (document) {\n    var transformCache = this.transformCache;\n    if (!transformCache.has(document)) {\n      var cacheEntry = {\n        // TODO These three calls (hasClientExports, shouldForceResolvers, and\n        // usesNonreactiveDirective) are performing independent full traversals\n        // of the transformed document. We should consider merging these\n        // traversals into a single pass in the future, though the work is\n        // cached after the first time.\n        hasClientExports: hasClientExports(document),\n        hasForcedResolvers: this.localState.shouldForceResolvers(document),\n        hasNonreactiveDirective: hasDirectives([\"nonreactive\"], document),\n        nonReactiveQuery: addNonReactiveToNamedFragments(document),\n        clientQuery: this.localState.clientQuery(document),\n        serverQuery: removeDirectivesFromDocument([{\n          name: \"client\",\n          remove: true\n        }, {\n          name: \"connection\"\n        }, {\n          name: \"nonreactive\"\n        }, {\n          name: \"unmask\"\n        }], document),\n        defaultVars: getDefaultValues(getOperationDefinition(document)),\n        // Transform any mutation or subscription operations to query operations\n        // so we can read/write them from/to the cache.\n        asQuery: __assign(__assign({}, document), {\n          definitions: document.definitions.map(function (def) {\n            if (def.kind === \"OperationDefinition\" && def.operation !== \"query\") {\n              return __assign(__assign({}, def), {\n                operation: \"query\"\n              });\n            }\n            return def;\n          })\n        })\n      };\n      transformCache.set(document, cacheEntry);\n    }\n    return transformCache.get(document);\n  };\n  QueryManager.prototype.getVariables = function (document, variables) {\n    return __assign(__assign({}, this.getDocumentInfo(document).defaultVars), variables);\n  };\n  QueryManager.prototype.watchQuery = function (options) {\n    var query = this.transform(options.query);\n    // assign variable default values if supplied\n    // NOTE: We don't modify options.query here with the transformed query to\n    // ensure observable.options.query is set to the raw untransformed query.\n    options = __assign(__assign({}, options), {\n      variables: this.getVariables(query, options.variables)\n    });\n    if (typeof options.notifyOnNetworkStatusChange === \"undefined\") {\n      options.notifyOnNetworkStatusChange = false;\n    }\n    var queryInfo = new QueryInfo(this);\n    var observable = new ObservableQuery({\n      queryManager: this,\n      queryInfo: queryInfo,\n      options: options\n    });\n    observable[\"lastQuery\"] = query;\n    if (!ObservableQuery[\"inactiveOnCreation\"].getValue()) {\n      this.queries.set(observable.queryId, queryInfo);\n    }\n    // We give queryInfo the transformed query to ensure the first cache diff\n    // uses the transformed query instead of the raw query\n    queryInfo.init({\n      document: query,\n      observableQuery: observable,\n      variables: observable.variables\n    });\n    return observable;\n  };\n  QueryManager.prototype.query = function (options, queryId) {\n    var _this = this;\n    if (queryId === void 0) {\n      queryId = this.generateQueryId();\n    }\n    invariant(options.query, 30);\n    invariant(options.query.kind === \"Document\", 31);\n    invariant(!options.returnPartialData, 32);\n    invariant(!options.pollInterval, 33);\n    var query = this.transform(options.query);\n    return this.fetchQuery(queryId, __assign(__assign({}, options), {\n      query: query\n    })).then(function (result) {\n      return result && __assign(__assign({}, result), {\n        data: _this.maskOperation({\n          document: query,\n          data: result.data,\n          fetchPolicy: options.fetchPolicy,\n          id: queryId\n        })\n      });\n    }).finally(function () {\n      return _this.stopQuery(queryId);\n    });\n  };\n  QueryManager.prototype.generateQueryId = function () {\n    return String(this.queryIdCounter++);\n  };\n  QueryManager.prototype.generateRequestId = function () {\n    return this.requestIdCounter++;\n  };\n  QueryManager.prototype.generateMutationId = function () {\n    return String(this.mutationIdCounter++);\n  };\n  QueryManager.prototype.stopQueryInStore = function (queryId) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.broadcastQueries();\n  };\n  QueryManager.prototype.stopQueryInStoreNoBroadcast = function (queryId) {\n    var queryInfo = this.queries.get(queryId);\n    if (queryInfo) queryInfo.stop();\n  };\n  QueryManager.prototype.clearStore = function (options) {\n    if (options === void 0) {\n      options = {\n        discardWatches: true\n      };\n    }\n    // Before we have sent the reset action to the store, we can no longer\n    // rely on the results returned by in-flight requests since these may\n    // depend on values that previously existed in the data portion of the\n    // store. So, we cancel the promises and observers that we have issued\n    // so far and not yet resolved (in the case of queries).\n    this.cancelPendingFetches(newInvariantError(34));\n    this.queries.forEach(function (queryInfo) {\n      if (queryInfo.observableQuery) {\n        // Set loading to true so listeners don't trigger unless they want\n        // results with partial data.\n        queryInfo.networkStatus = NetworkStatus.loading;\n      } else {\n        queryInfo.stop();\n      }\n    });\n    if (this.mutationStore) {\n      this.mutationStore = Object.create(null);\n    }\n    // begin removing data from the store\n    return this.cache.reset(options);\n  };\n  QueryManager.prototype.getObservableQueries = function (include) {\n    var _this = this;\n    if (include === void 0) {\n      include = \"active\";\n    }\n    var queries = new Map();\n    var queryNames = new Map();\n    var queryNamesAndQueryStrings = new Map();\n    var legacyQueryOptions = new Set();\n    if (Array.isArray(include)) {\n      include.forEach(function (desc) {\n        if (typeof desc === \"string\") {\n          queryNames.set(desc, desc);\n          queryNamesAndQueryStrings.set(desc, false);\n        } else if (isDocumentNode(desc)) {\n          var queryString = print(_this.transform(desc));\n          queryNames.set(queryString, getOperationName(desc));\n          queryNamesAndQueryStrings.set(queryString, false);\n        } else if (isNonNullObject(desc) && desc.query) {\n          legacyQueryOptions.add(desc);\n        }\n      });\n    }\n    this.queries.forEach(function (_a, queryId) {\n      var oq = _a.observableQuery,\n        document = _a.document;\n      if (oq) {\n        if (include === \"all\") {\n          queries.set(queryId, oq);\n          return;\n        }\n        var queryName = oq.queryName,\n          fetchPolicy = oq.options.fetchPolicy;\n        if (fetchPolicy === \"standby\" || include === \"active\" && !oq.hasObservers()) {\n          return;\n        }\n        if (include === \"active\" || queryName && queryNamesAndQueryStrings.has(queryName) || document && queryNamesAndQueryStrings.has(print(document))) {\n          queries.set(queryId, oq);\n          if (queryName) queryNamesAndQueryStrings.set(queryName, true);\n          if (document) queryNamesAndQueryStrings.set(print(document), true);\n        }\n      }\n    });\n    if (legacyQueryOptions.size) {\n      legacyQueryOptions.forEach(function (options) {\n        // We will be issuing a fresh network request for this query, so we\n        // pre-allocate a new query ID here, using a special prefix to enable\n        // cleaning up these temporary queries later, after fetching.\n        var queryId = makeUniqueId(\"legacyOneTimeQuery\");\n        var queryInfo = _this.getOrCreateQuery(queryId).init({\n          document: options.query,\n          variables: options.variables\n        });\n        var oq = new ObservableQuery({\n          queryManager: _this,\n          queryInfo: queryInfo,\n          options: __assign(__assign({}, options), {\n            fetchPolicy: \"network-only\"\n          })\n        });\n        invariant(oq.queryId === queryId);\n        queryInfo.setObservableQuery(oq);\n        queries.set(queryId, oq);\n      });\n    }\n    if (globalThis.__DEV__ !== false && queryNamesAndQueryStrings.size) {\n      queryNamesAndQueryStrings.forEach(function (included, nameOrQueryString) {\n        if (!included) {\n          var queryName = queryNames.get(nameOrQueryString);\n          if (queryName) {\n            globalThis.__DEV__ !== false && invariant.warn(35, queryName);\n          } else {\n            globalThis.__DEV__ !== false && invariant.warn(36);\n          }\n        }\n      });\n    }\n    return queries;\n  };\n  QueryManager.prototype.reFetchObservableQueries = function (includeStandby) {\n    var _this = this;\n    if (includeStandby === void 0) {\n      includeStandby = false;\n    }\n    var observableQueryPromises = [];\n    this.getObservableQueries(includeStandby ? \"all\" : \"active\").forEach(function (observableQuery, queryId) {\n      var fetchPolicy = observableQuery.options.fetchPolicy;\n      observableQuery.resetLastResults();\n      if (includeStandby || fetchPolicy !== \"standby\" && fetchPolicy !== \"cache-only\") {\n        observableQueryPromises.push(observableQuery.refetch());\n      }\n      (_this.queries.get(queryId) || observableQuery[\"queryInfo\"]).setDiff(null);\n    });\n    this.broadcastQueries();\n    return Promise.all(observableQueryPromises);\n  };\n  QueryManager.prototype.startGraphQLSubscription = function (options) {\n    var _this = this;\n    var query = options.query,\n      variables = options.variables;\n    var fetchPolicy = options.fetchPolicy,\n      _a = options.errorPolicy,\n      errorPolicy = _a === void 0 ? \"none\" : _a,\n      _b = options.context,\n      context = _b === void 0 ? {} : _b,\n      _c = options.extensions,\n      extensions = _c === void 0 ? {} : _c;\n    query = this.transform(query);\n    variables = this.getVariables(query, variables);\n    var makeObservable = function (variables) {\n      return _this.getObservableFromLink(query, context, variables, extensions).map(function (result) {\n        if (fetchPolicy !== \"no-cache\") {\n          // the subscription interface should handle not sending us results we no longer subscribe to.\n          // XXX I don't think we ever send in an object with errors, but we might in the future...\n          if (shouldWriteResult(result, errorPolicy)) {\n            _this.cache.write({\n              query: query,\n              result: result.data,\n              dataId: \"ROOT_SUBSCRIPTION\",\n              variables: variables\n            });\n          }\n          _this.broadcastQueries();\n        }\n        var hasErrors = graphQLResultHasError(result);\n        var hasProtocolErrors = graphQLResultHasProtocolErrors(result);\n        if (hasErrors || hasProtocolErrors) {\n          var errors = {};\n          if (hasErrors) {\n            errors.graphQLErrors = result.errors;\n          }\n          if (hasProtocolErrors) {\n            errors.protocolErrors = result.extensions[PROTOCOL_ERRORS_SYMBOL];\n          }\n          // `errorPolicy` is a mechanism for handling GraphQL errors, according\n          // to our documentation, so we throw protocol errors regardless of the\n          // set error policy.\n          if (errorPolicy === \"none\" || hasProtocolErrors) {\n            throw new ApolloError(errors);\n          }\n        }\n        if (errorPolicy === \"ignore\") {\n          delete result.errors;\n        }\n        return result;\n      });\n    };\n    if (this.getDocumentInfo(query).hasClientExports) {\n      var observablePromise_1 = this.localState.addExportedVariables(query, variables, context).then(makeObservable);\n      return new Observable(function (observer) {\n        var sub = null;\n        observablePromise_1.then(function (observable) {\n          return sub = observable.subscribe(observer);\n        }, observer.error);\n        return function () {\n          return sub && sub.unsubscribe();\n        };\n      });\n    }\n    return makeObservable(variables);\n  };\n  QueryManager.prototype.stopQuery = function (queryId) {\n    this.stopQueryNoBroadcast(queryId);\n    this.broadcastQueries();\n  };\n  QueryManager.prototype.stopQueryNoBroadcast = function (queryId) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.removeQuery(queryId);\n  };\n  QueryManager.prototype.removeQuery = function (queryId) {\n    var _a;\n    // teardown all links\n    // Both `QueryManager.fetchRequest` and `QueryManager.query` create separate promises\n    // that each add their reject functions to fetchCancelFns.\n    // A query created with `QueryManager.query()` could trigger a `QueryManager.fetchRequest`.\n    // The same queryId could have two rejection fns for two promises\n    this.fetchCancelFns.delete(queryId);\n    if (this.queries.has(queryId)) {\n      (_a = this.queries.get(queryId)) === null || _a === void 0 ? void 0 : _a.stop();\n      this.queries.delete(queryId);\n    }\n  };\n  QueryManager.prototype.broadcastQueries = function () {\n    if (this.onBroadcast) this.onBroadcast();\n    this.queries.forEach(function (info) {\n      var _a;\n      return (_a = info.observableQuery) === null || _a === void 0 ? void 0 : _a[\"notify\"]();\n    });\n  };\n  QueryManager.prototype.getLocalState = function () {\n    return this.localState;\n  };\n  QueryManager.prototype.getObservableFromLink = function (query, context, variables, extensions,\n  // Prefer context.queryDeduplication if specified.\n  deduplication) {\n    var _this = this;\n    var _a;\n    if (deduplication === void 0) {\n      deduplication = (_a = context === null || context === void 0 ? void 0 : context.queryDeduplication) !== null && _a !== void 0 ? _a : this.queryDeduplication;\n    }\n    var observable;\n    var _b = this.getDocumentInfo(query),\n      serverQuery = _b.serverQuery,\n      clientQuery = _b.clientQuery;\n    if (serverQuery) {\n      var _c = this,\n        inFlightLinkObservables_1 = _c.inFlightLinkObservables,\n        link = _c.link;\n      var operation = {\n        query: serverQuery,\n        variables: variables,\n        operationName: getOperationName(serverQuery) || void 0,\n        context: this.prepareContext(__assign(__assign({}, context), {\n          forceFetch: !deduplication\n        })),\n        extensions: extensions\n      };\n      context = operation.context;\n      if (deduplication) {\n        var printedServerQuery_1 = print(serverQuery);\n        var varJson_1 = canonicalStringify(variables);\n        var entry = inFlightLinkObservables_1.lookup(printedServerQuery_1, varJson_1);\n        observable = entry.observable;\n        if (!observable) {\n          var concast_1 = new Concast([execute(link, operation)]);\n          observable = entry.observable = concast_1;\n          concast_1.beforeNext(function cb(method, arg) {\n            if (method === \"next\" && \"hasNext\" in arg && arg.hasNext) {\n              concast_1.beforeNext(cb);\n            } else {\n              inFlightLinkObservables_1.remove(printedServerQuery_1, varJson_1);\n            }\n          });\n        }\n      } else {\n        observable = new Concast([execute(link, operation)]);\n      }\n    } else {\n      observable = new Concast([Observable.of({\n        data: {}\n      })]);\n      context = this.prepareContext(context);\n    }\n    if (clientQuery) {\n      observable = asyncMap(observable, function (result) {\n        return _this.localState.runResolvers({\n          document: clientQuery,\n          remoteResult: result,\n          context: context,\n          variables: variables\n        });\n      });\n    }\n    return observable;\n  };\n  QueryManager.prototype.getResultsFromLink = function (queryInfo, cacheWriteBehavior, options) {\n    var requestId = queryInfo.lastRequestId = this.generateRequestId();\n    // Performing transformForLink here gives this.cache a chance to fill in\n    // missing fragment definitions (for example) before sending this document\n    // through the link chain.\n    var linkDocument = this.cache.transformForLink(options.query);\n    return asyncMap(this.getObservableFromLink(linkDocument, options.context, options.variables), function (result) {\n      var graphQLErrors = getGraphQLErrorsFromResult(result);\n      var hasErrors = graphQLErrors.length > 0;\n      var errorPolicy = options.errorPolicy;\n      // If we interrupted this request by calling getResultsFromLink again\n      // with the same QueryInfo object, we ignore the old results.\n      if (requestId >= queryInfo.lastRequestId) {\n        if (hasErrors && errorPolicy === \"none\") {\n          // Throwing here effectively calls observer.error.\n          throw queryInfo.markError(new ApolloError({\n            graphQLErrors: graphQLErrors\n          }));\n        }\n        // Use linkDocument rather than queryInfo.document so the\n        // operation/fragments used to write the result are the same as the\n        // ones used to obtain it from the link.\n        queryInfo.markResult(result, linkDocument, options, cacheWriteBehavior);\n        queryInfo.markReady();\n      }\n      var aqr = {\n        data: result.data,\n        loading: false,\n        networkStatus: NetworkStatus.ready\n      };\n      // In the case we start multiple network requests simulatenously, we\n      // want to ensure we properly set `data` if we're reporting on an old\n      // result which will not be caught by the conditional above that ends up\n      // throwing the markError result.\n      if (hasErrors && errorPolicy === \"none\") {\n        aqr.data = void 0;\n      }\n      if (hasErrors && errorPolicy !== \"ignore\") {\n        aqr.errors = graphQLErrors;\n        aqr.networkStatus = NetworkStatus.error;\n      }\n      return aqr;\n    }, function (networkError) {\n      var error = isApolloError(networkError) ? networkError : new ApolloError({\n        networkError: networkError\n      });\n      // Avoid storing errors from older interrupted queries.\n      if (requestId >= queryInfo.lastRequestId) {\n        queryInfo.markError(error);\n      }\n      throw error;\n    });\n  };\n  QueryManager.prototype.fetchConcastWithInfo = function (queryInfo, options,\n  // The initial networkStatus for this fetch, most often\n  // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n  // or setVariables.\n  networkStatus, query) {\n    var _this = this;\n    if (networkStatus === void 0) {\n      networkStatus = NetworkStatus.loading;\n    }\n    if (query === void 0) {\n      query = options.query;\n    }\n    var variables = this.getVariables(query, options.variables);\n    var defaults = this.defaultOptions.watchQuery;\n    var _a = options.fetchPolicy,\n      fetchPolicy = _a === void 0 ? defaults && defaults.fetchPolicy || \"cache-first\" : _a,\n      _b = options.errorPolicy,\n      errorPolicy = _b === void 0 ? defaults && defaults.errorPolicy || \"none\" : _b,\n      _c = options.returnPartialData,\n      returnPartialData = _c === void 0 ? false : _c,\n      _d = options.notifyOnNetworkStatusChange,\n      notifyOnNetworkStatusChange = _d === void 0 ? false : _d,\n      _e = options.context,\n      context = _e === void 0 ? {} : _e;\n    var normalized = Object.assign({}, options, {\n      query: query,\n      variables: variables,\n      fetchPolicy: fetchPolicy,\n      errorPolicy: errorPolicy,\n      returnPartialData: returnPartialData,\n      notifyOnNetworkStatusChange: notifyOnNetworkStatusChange,\n      context: context\n    });\n    var fromVariables = function (variables) {\n      // Since normalized is always a fresh copy of options, it's safe to\n      // modify its properties here, rather than creating yet another new\n      // WatchQueryOptions object.\n      normalized.variables = variables;\n      var sourcesWithInfo = _this.fetchQueryByPolicy(queryInfo, normalized, networkStatus);\n      if (\n      // If we're in standby, postpone advancing options.fetchPolicy using\n      // applyNextFetchPolicy.\n      normalized.fetchPolicy !== \"standby\" &&\n      // The \"standby\" policy currently returns [] from fetchQueryByPolicy, so\n      // this is another way to detect when nothing was done/fetched.\n      sourcesWithInfo.sources.length > 0 && queryInfo.observableQuery) {\n        queryInfo.observableQuery[\"applyNextFetchPolicy\"](\"after-fetch\", options);\n      }\n      return sourcesWithInfo;\n    };\n    // This cancel function needs to be set before the concast is created,\n    // in case concast creation synchronously cancels the request.\n    var cleanupCancelFn = function () {\n      return _this.fetchCancelFns.delete(queryInfo.queryId);\n    };\n    this.fetchCancelFns.set(queryInfo.queryId, function (reason) {\n      cleanupCancelFn();\n      // This delay ensures the concast variable has been initialized.\n      setTimeout(function () {\n        return concast.cancel(reason);\n      });\n    });\n    var concast, containsDataFromLink;\n    // If the query has @export(as: ...) directives, then we need to\n    // process those directives asynchronously. When there are no\n    // @export directives (the common case), we deliberately avoid\n    // wrapping the result of this.fetchQueryByPolicy in a Promise,\n    // since the timing of result delivery is (unfortunately) important\n    // for backwards compatibility. TODO This code could be simpler if\n    // we deprecated and removed LocalState.\n    if (this.getDocumentInfo(normalized.query).hasClientExports) {\n      concast = new Concast(this.localState.addExportedVariables(normalized.query, normalized.variables, normalized.context).then(fromVariables).then(function (sourcesWithInfo) {\n        return sourcesWithInfo.sources;\n      }));\n      // there is just no way we can synchronously get the *right* value here,\n      // so we will assume `true`, which is the behaviour before the bug fix in\n      // #10597. This means that bug is not fixed in that case, and is probably\n      // un-fixable with reasonable effort for the edge case of @export as\n      // directives.\n      containsDataFromLink = true;\n    } else {\n      var sourcesWithInfo = fromVariables(normalized.variables);\n      containsDataFromLink = sourcesWithInfo.fromLink;\n      concast = new Concast(sourcesWithInfo.sources);\n    }\n    concast.promise.then(cleanupCancelFn, cleanupCancelFn);\n    return {\n      concast: concast,\n      fromLink: containsDataFromLink\n    };\n  };\n  QueryManager.prototype.refetchQueries = function (_a) {\n    var _this = this;\n    var updateCache = _a.updateCache,\n      include = _a.include,\n      _b = _a.optimistic,\n      optimistic = _b === void 0 ? false : _b,\n      _c = _a.removeOptimistic,\n      removeOptimistic = _c === void 0 ? optimistic ? makeUniqueId(\"refetchQueries\") : void 0 : _c,\n      onQueryUpdated = _a.onQueryUpdated;\n    var includedQueriesById = new Map();\n    if (include) {\n      this.getObservableQueries(include).forEach(function (oq, queryId) {\n        includedQueriesById.set(queryId, {\n          oq: oq,\n          lastDiff: (_this.queries.get(queryId) || oq[\"queryInfo\"]).getDiff()\n        });\n      });\n    }\n    var results = new Map();\n    if (updateCache) {\n      this.cache.batch({\n        update: updateCache,\n        // Since you can perform any combination of cache reads and/or writes in\n        // the cache.batch update function, its optimistic option can be either\n        // a boolean or a string, representing three distinct modes of\n        // operation:\n        //\n        // * false: read/write only the root layer\n        // * true: read/write the topmost layer\n        // * string: read/write a fresh optimistic layer with that ID string\n        //\n        // When typeof optimistic === \"string\", a new optimistic layer will be\n        // temporarily created within cache.batch with that string as its ID. If\n        // we then pass that same string as the removeOptimistic option, we can\n        // make cache.batch immediately remove the optimistic layer after\n        // running the updateCache function, triggering only one broadcast.\n        //\n        // However, the refetchQueries method accepts only true or false for its\n        // optimistic option (not string). We interpret true to mean a temporary\n        // optimistic layer should be created, to allow efficiently rolling back\n        // the effect of the updateCache function, which involves passing a\n        // string instead of true as the optimistic option to cache.batch, when\n        // refetchQueries receives optimistic: true.\n        //\n        // In other words, we are deliberately not supporting the use case of\n        // writing to an *existing* optimistic layer (using the refetchQueries\n        // updateCache function), since that would potentially interfere with\n        // other optimistic updates in progress. Instead, you can read/write\n        // only the root layer by passing optimistic: false to refetchQueries,\n        // or you can read/write a brand new optimistic layer that will be\n        // automatically removed by passing optimistic: true.\n        optimistic: optimistic && removeOptimistic || false,\n        // The removeOptimistic option can also be provided by itself, even if\n        // optimistic === false, to remove some previously-added optimistic\n        // layer safely and efficiently, like we do in markMutationResult.\n        //\n        // If an explicit removeOptimistic string is provided with optimistic:\n        // true, the removeOptimistic string will determine the ID of the\n        // temporary optimistic layer, in case that ever matters.\n        removeOptimistic: removeOptimistic,\n        onWatchUpdated: function (watch, diff, lastDiff) {\n          var oq = watch.watcher instanceof QueryInfo && watch.watcher.observableQuery;\n          if (oq) {\n            if (onQueryUpdated) {\n              // Since we're about to handle this query now, remove it from\n              // includedQueriesById, in case it was added earlier because of\n              // options.include.\n              includedQueriesById.delete(oq.queryId);\n              var result = onQueryUpdated(oq, diff, lastDiff);\n              if (result === true) {\n                // The onQueryUpdated function requested the default refetching\n                // behavior by returning true.\n                result = oq.refetch();\n              }\n              // Record the result in the results Map, as long as onQueryUpdated\n              // did not return false to skip/ignore this result.\n              if (result !== false) {\n                results.set(oq, result);\n              }\n              // Allow the default cache broadcast to happen, except when\n              // onQueryUpdated returns false.\n              return result;\n            }\n            if (onQueryUpdated !== null) {\n              // If we don't have an onQueryUpdated function, and onQueryUpdated\n              // was not disabled by passing null, make sure this query is\n              // \"included\" like any other options.include-specified query.\n              includedQueriesById.set(oq.queryId, {\n                oq: oq,\n                lastDiff: lastDiff,\n                diff: diff\n              });\n            }\n          }\n        }\n      });\n    }\n    if (includedQueriesById.size) {\n      includedQueriesById.forEach(function (_a, queryId) {\n        var oq = _a.oq,\n          lastDiff = _a.lastDiff,\n          diff = _a.diff;\n        var result;\n        // If onQueryUpdated is provided, we want to use it for all included\n        // queries, even the QueryOptions ones.\n        if (onQueryUpdated) {\n          if (!diff) {\n            diff = _this.cache.diff(oq[\"queryInfo\"][\"getDiffOptions\"]());\n          }\n          result = onQueryUpdated(oq, diff, lastDiff);\n        }\n        // Otherwise, we fall back to refetching.\n        if (!onQueryUpdated || result === true) {\n          result = oq.refetch();\n        }\n        if (result !== false) {\n          results.set(oq, result);\n        }\n        if (queryId.indexOf(\"legacyOneTimeQuery\") >= 0) {\n          _this.stopQueryNoBroadcast(queryId);\n        }\n      });\n    }\n    if (removeOptimistic) {\n      // In case no updateCache callback was provided (so cache.batch was not\n      // called above, and thus did not already remove the optimistic layer),\n      // remove it here. Since this is a no-op when the layer has already been\n      // removed, we do it even if we called cache.batch above, since it's\n      // possible this.cache is an instance of some ApolloCache subclass other\n      // than InMemoryCache, and does not fully support the removeOptimistic\n      // option for cache.batch.\n      this.cache.removeOptimistic(removeOptimistic);\n    }\n    return results;\n  };\n  QueryManager.prototype.maskOperation = function (options) {\n    var _a, _b, _c;\n    var document = options.document,\n      data = options.data;\n    if (globalThis.__DEV__ !== false) {\n      var fetchPolicy = options.fetchPolicy,\n        id = options.id;\n      var operationType = (_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation;\n      var operationId = ((_b = operationType === null || operationType === void 0 ? void 0 : operationType[0]) !== null && _b !== void 0 ? _b : \"o\") + id;\n      if (this.dataMasking && fetchPolicy === \"no-cache\" && !isFullyUnmaskedOperation(document) && !this.noCacheWarningsByQueryId.has(operationId)) {\n        this.noCacheWarningsByQueryId.add(operationId);\n        globalThis.__DEV__ !== false && invariant.warn(37, (_c = getOperationName(document)) !== null && _c !== void 0 ? _c : \"Unnamed \".concat(operationType !== null && operationType !== void 0 ? operationType : \"operation\"));\n      }\n    }\n    return this.dataMasking ? maskOperation(data, document, this.cache) : data;\n  };\n  QueryManager.prototype.maskFragment = function (options) {\n    var data = options.data,\n      fragment = options.fragment,\n      fragmentName = options.fragmentName;\n    return this.dataMasking ? maskFragment(data, fragment, this.cache, fragmentName) : data;\n  };\n  QueryManager.prototype.fetchQueryByPolicy = function (queryInfo, _a,\n  // The initial networkStatus for this fetch, most often\n  // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n  // or setVariables.\n  networkStatus) {\n    var _this = this;\n    var query = _a.query,\n      variables = _a.variables,\n      fetchPolicy = _a.fetchPolicy,\n      refetchWritePolicy = _a.refetchWritePolicy,\n      errorPolicy = _a.errorPolicy,\n      returnPartialData = _a.returnPartialData,\n      context = _a.context,\n      notifyOnNetworkStatusChange = _a.notifyOnNetworkStatusChange;\n    var oldNetworkStatus = queryInfo.networkStatus;\n    queryInfo.init({\n      document: query,\n      variables: variables,\n      networkStatus: networkStatus\n    });\n    var readCache = function () {\n      return queryInfo.getDiff();\n    };\n    var resultsFromCache = function (diff, networkStatus) {\n      if (networkStatus === void 0) {\n        networkStatus = queryInfo.networkStatus || NetworkStatus.loading;\n      }\n      var data = diff.result;\n      if (globalThis.__DEV__ !== false && !returnPartialData && !equal(data, {})) {\n        logMissingFieldErrors(diff.missing);\n      }\n      var fromData = function (data) {\n        return Observable.of(__assign({\n          data: data,\n          loading: isNetworkRequestInFlight(networkStatus),\n          networkStatus: networkStatus\n        }, diff.complete ? null : {\n          partial: true\n        }));\n      };\n      if (data && _this.getDocumentInfo(query).hasForcedResolvers) {\n        return _this.localState.runResolvers({\n          document: query,\n          remoteResult: {\n            data: data\n          },\n          context: context,\n          variables: variables,\n          onlyRunForcedResolvers: true\n        }).then(function (resolved) {\n          return fromData(resolved.data || void 0);\n        });\n      }\n      // Resolves https://github.com/apollographql/apollo-client/issues/10317.\n      // If errorPolicy is 'none' and notifyOnNetworkStatusChange is true,\n      // data was incorrectly returned from the cache on refetch:\n      // if diff.missing exists, we should not return cache data.\n      if (errorPolicy === \"none\" && networkStatus === NetworkStatus.refetch && Array.isArray(diff.missing)) {\n        return fromData(void 0);\n      }\n      return fromData(data);\n    };\n    var cacheWriteBehavior = fetchPolicy === \"no-cache\" ? 0 /* CacheWriteBehavior.FORBID */\n    // Watched queries must opt into overwriting existing data on refetch,\n    // by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n    : networkStatus === NetworkStatus.refetch && refetchWritePolicy !== \"merge\" ? 1 /* CacheWriteBehavior.OVERWRITE */ : 2 /* CacheWriteBehavior.MERGE */;\n    var resultsFromLink = function () {\n      return _this.getResultsFromLink(queryInfo, cacheWriteBehavior, {\n        query: query,\n        variables: variables,\n        context: context,\n        fetchPolicy: fetchPolicy,\n        errorPolicy: errorPolicy\n      });\n    };\n    var shouldNotify = notifyOnNetworkStatusChange && typeof oldNetworkStatus === \"number\" && oldNetworkStatus !== networkStatus && isNetworkRequestInFlight(networkStatus);\n    switch (fetchPolicy) {\n      default:\n      case \"cache-first\":\n        {\n          var diff = readCache();\n          if (diff.complete) {\n            return {\n              fromLink: false,\n              sources: [resultsFromCache(diff, queryInfo.markReady())]\n            };\n          }\n          if (returnPartialData || shouldNotify) {\n            return {\n              fromLink: true,\n              sources: [resultsFromCache(diff), resultsFromLink()]\n            };\n          }\n          return {\n            fromLink: true,\n            sources: [resultsFromLink()]\n          };\n        }\n      case \"cache-and-network\":\n        {\n          var diff = readCache();\n          if (diff.complete || returnPartialData || shouldNotify) {\n            return {\n              fromLink: true,\n              sources: [resultsFromCache(diff), resultsFromLink()]\n            };\n          }\n          return {\n            fromLink: true,\n            sources: [resultsFromLink()]\n          };\n        }\n      case \"cache-only\":\n        return {\n          fromLink: false,\n          sources: [resultsFromCache(readCache(), queryInfo.markReady())]\n        };\n      case \"network-only\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            sources: [resultsFromCache(readCache()), resultsFromLink()]\n          };\n        }\n        return {\n          fromLink: true,\n          sources: [resultsFromLink()]\n        };\n      case \"no-cache\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            // Note that queryInfo.getDiff() for no-cache queries does not call\n            // cache.diff, but instead returns a { complete: false } stub result\n            // when there is no queryInfo.diff already defined.\n            sources: [resultsFromCache(queryInfo.getDiff()), resultsFromLink()]\n          };\n        }\n        return {\n          fromLink: true,\n          sources: [resultsFromLink()]\n        };\n      case \"standby\":\n        return {\n          fromLink: false,\n          sources: []\n        };\n    }\n  };\n  QueryManager.prototype.getOrCreateQuery = function (queryId) {\n    if (queryId && !this.queries.has(queryId)) {\n      this.queries.set(queryId, new QueryInfo(this, queryId));\n    }\n    return this.queries.get(queryId);\n  };\n  QueryManager.prototype.prepareContext = function (context) {\n    if (context === void 0) {\n      context = {};\n    }\n    var newContext = this.localState.prepareContext(context);\n    return __assign(__assign(__assign({}, this.defaultContext), newContext), {\n      clientAwareness: this.clientAwareness\n    });\n  };\n  return QueryManager;\n}();\nexport { QueryManager };", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "invariant", "newInvariantError", "equal", "execute", "addNonReactiveToNamedFragments", "hasDirectives", "isExecutionPatchIncrementalResult", "isExecutionPatchResult", "isFullyUnmaskedOperation", "removeDirectivesFromDocument", "canonicalStringify", "getDefaultValues", "getOperationDefinition", "getOperationName", "hasClientExports", "graphQLResultHasError", "getGraphQLErrorsFromResult", "Observable", "asyncMap", "isNonEmptyArray", "Concast", "makeUniqueId", "isDocumentNode", "isNonNullObject", "DocumentTransform", "mergeIncrementalData", "ApolloError", "isApolloError", "graphQLResultHasProtocolErrors", "ObservableQuery", "logMissingFieldErrors", "NetworkStatus", "isNetworkRequestInFlight", "QueryInfo", "shouldWriteResult", "PROTOCOL_ERRORS_SYMBOL", "print", "hasOwnProperty", "Object", "prototype", "IGNORE", "create", "<PERSON><PERSON>", "AutoCleanedWeakCache", "cacheSizes", "maskFragment", "maskOperation", "QueryManager", "options", "_this", "clientAwareness", "queries", "Map", "fetchCancelFns", "transformCache", "queryIdCounter", "requestIdCounter", "mutationIdCounter", "inFlightLinkObservables", "noCacheWarningsByQueryId", "Set", "defaultDocumentTransform", "document", "cache", "transformDocument", "link", "defaultOptions", "queryDeduplication", "localState", "ssrMode", "assumeImmutableResults", "dataMasking", "documentTransform", "concat", "defaultContext", "onBroadcast", "mutationStore", "stop", "for<PERSON>ach", "_info", "queryId", "stopQueryNoBroadcast", "cancelPendingFetches", "error", "cancel", "clear", "mutate", "_a", "arguments", "_b", "mutationId", "mutationStoreValue", "isOptimistic", "self", "_c", "_d", "mutation", "variables", "optimisticResponse", "updateQueries", "_e", "refetchQueries", "_f", "awaitRefetchQueries", "updateWithProxyFn", "update", "onQueryUpdated", "_g", "fetchPolicy", "_h", "errorPolicy", "keepR<PERSON>Fields", "context", "_j", "label", "generateMutationId", "transformForLink", "transform", "getDocumentInfo", "getVariables", "addExportedVariables", "sent", "loading", "markMutationOptimistic", "broadcastQueries", "Promise", "resolve", "reject", "getObservableFromLink", "result", "graphQLErrors", "storeResult", "errors", "markMutationResult", "removeOptimistic", "subscribe", "next", "hasNext", "data", "id", "err", "networkError", "cacheWrites", "<PERSON><PERSON><PERSON>", "push", "dataId", "query", "incremental", "diff", "<PERSON><PERSON><PERSON><PERSON>", "optimistic", "returnPartialData", "mergedData", "updateQueries_1", "observableQuery", "queryName", "call", "updater", "get", "currentQueryResult", "complete", "next<PERSON><PERSON><PERSON><PERSON><PERSON>ult", "mutationResult", "queryVariables", "length", "results_1", "updateCache", "write", "isFinalResult", "modify", "fields", "value", "fieldName", "DELETE", "include", "all", "then", "recordOptimisticTransaction", "globalThis", "__DEV__", "<PERSON><PERSON><PERSON><PERSON>", "networkStatus", "fetchConcastWithInfo", "getOrCreateQuery", "concast", "promise", "getQueryStore", "store", "info", "resetErrors", "queryInfo", "undefined", "has", "cacheEntry", "hasForcedResolvers", "shouldForceResolvers", "hasNonreactiveDirective", "nonReactiveQuery", "clientQuery", "serverQuery", "name", "remove", "defaultVars", "definitions", "map", "def", "kind", "operation", "set", "watch<PERSON><PERSON>y", "notifyOnNetworkStatusChange", "observable", "query<PERSON>anager", "getValue", "init", "generateQueryId", "pollInterval", "finally", "stopQuery", "String", "generateRequestId", "stopQueryInStore", "stopQueryInStoreNoBroadcast", "clearStore", "discardWatches", "reset", "getObservableQueries", "queryNames", "queryNamesAndQueryStrings", "legacyQueryOptions", "Array", "isArray", "desc", "queryString", "add", "oq", "hasObservers", "size", "setObservableQuery", "included", "nameOrQueryString", "warn", "reFetchObservableQueries", "includeStandby", "observableQueryPromises", "resetLastResults", "refetch", "setDiff", "startGraphQLSubscription", "extensions", "makeObservable", "hasErrors", "hasProtocolErrors", "protocolErrors", "observablePromise_1", "observer", "sub", "unsubscribe", "<PERSON><PERSON><PERSON><PERSON>", "delete", "getLocalState", "deduplication", "inFlightLinkObservables_1", "operationName", "prepareContext", "forceFetch", "printedServerQuery_1", "varJson_1", "entry", "lookup", "concast_1", "beforeNext", "cb", "method", "arg", "of", "runResolvers", "remoteResult", "getResultsFromLink", "cacheWriteBehavior", "requestId", "lastRequestId", "linkDocument", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "aqr", "ready", "defaults", "normalized", "assign", "fromVariables", "sourcesWithInfo", "fetchQueryByPolicy", "sources", "cleanupCancelFn", "reason", "setTimeout", "containsDataFromLink", "fromLink", "includedQueriesById", "lastDiff", "getDiff", "results", "batch", "onWatchUpdated", "watch", "watcher", "indexOf", "operationType", "operationId", "fragment", "fragmentName", "refetchWritePolicy", "oldNetworkStatus", "readCache", "resultsFromCache", "missing", "fromData", "partial", "onlyRunForcedResolvers", "resolved", "resultsFromLink", "shouldNotify", "newContext"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/QueryManager.js"], "sourcesContent": ["import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { execute } from \"../link/core/index.js\";\nimport { addNonReactiveToNamedFragments, hasDirectives, isExecutionPatchIncrementalResult, isExecutionPatchResult, isFullyUnmaskedOperation, removeDirectivesFromDocument, } from \"../utilities/index.js\";\nimport { canonicalStringify } from \"../cache/index.js\";\nimport { getDefaultValues, getOperationDefinition, getOperationName, hasClientExports, graphQLResultHasError, getGraphQLErrorsFromResult, Observable, asyncMap, isNonEmptyArray, Concast, makeUniqueId, isDocumentNode, isNonNullObject, DocumentTransform, } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/common/incrementalResult.js\";\nimport { ApolloError, isApolloError, graphQLResultHasProtocolErrors, } from \"../errors/index.js\";\nimport { ObservableQuery, logMissingFieldErrors } from \"./ObservableQuery.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { QueryInfo, shouldWriteResult, } from \"./QueryInfo.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../errors/index.js\";\nimport { print } from \"../utilities/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar IGNORE = Object.create(null);\nimport { Trie } from \"@wry/trie\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../utilities/index.js\";\nimport { maskFragment, maskOperation } from \"../masking/index.js\";\nvar QueryManager = /** @class */ (function () {\n    function QueryManager(options) {\n        var _this = this;\n        this.clientAwareness = {};\n        // All the queries that the QueryManager is currently managing (not\n        // including mutations and subscriptions).\n        this.queries = new Map();\n        // Maps from queryId strings to Promise rejection functions for\n        // currently active queries and fetches.\n        // Use protected instead of private field so\n        // @apollo/experimental-nextjs-app-support can access type info.\n        this.fetchCancelFns = new Map();\n        this.transformCache = new AutoCleanedWeakCache(cacheSizes[\"queryManager.getDocumentInfo\"] ||\n            2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */);\n        this.queryIdCounter = 1;\n        this.requestIdCounter = 1;\n        this.mutationIdCounter = 1;\n        // Use protected instead of private field so\n        // @apollo/experimental-nextjs-app-support can access type info.\n        this.inFlightLinkObservables = new Trie(false);\n        this.noCacheWarningsByQueryId = new Set();\n        var defaultDocumentTransform = new DocumentTransform(function (document) { return _this.cache.transformDocument(document); }, \n        // Allow the apollo cache to manage its own transform caches\n        { cache: false });\n        this.cache = options.cache;\n        this.link = options.link;\n        this.defaultOptions = options.defaultOptions;\n        this.queryDeduplication = options.queryDeduplication;\n        this.clientAwareness = options.clientAwareness;\n        this.localState = options.localState;\n        this.ssrMode = options.ssrMode;\n        this.assumeImmutableResults = options.assumeImmutableResults;\n        this.dataMasking = options.dataMasking;\n        var documentTransform = options.documentTransform;\n        this.documentTransform =\n            documentTransform ?\n                defaultDocumentTransform\n                    .concat(documentTransform)\n                    // The custom document transform may add new fragment spreads or new\n                    // field selections, so we want to give the cache a chance to run\n                    // again. For example, the InMemoryCache adds __typename to field\n                    // selections and fragments from the fragment registry.\n                    .concat(defaultDocumentTransform)\n                : defaultDocumentTransform;\n        this.defaultContext = options.defaultContext || Object.create(null);\n        if ((this.onBroadcast = options.onBroadcast)) {\n            this.mutationStore = Object.create(null);\n        }\n    }\n    /**\n     * Call this method to terminate any active query processes, making it safe\n     * to dispose of this QueryManager instance.\n     */\n    QueryManager.prototype.stop = function () {\n        var _this = this;\n        this.queries.forEach(function (_info, queryId) {\n            _this.stopQueryNoBroadcast(queryId);\n        });\n        this.cancelPendingFetches(newInvariantError(27));\n    };\n    QueryManager.prototype.cancelPendingFetches = function (error) {\n        this.fetchCancelFns.forEach(function (cancel) { return cancel(error); });\n        this.fetchCancelFns.clear();\n    };\n    QueryManager.prototype.mutate = function (_a) {\n        return __awaiter(this, arguments, void 0, function (_b) {\n            var mutationId, hasClientExports, mutationStoreValue, isOptimistic, self;\n            var _c, _d;\n            var mutation = _b.mutation, variables = _b.variables, optimisticResponse = _b.optimisticResponse, updateQueries = _b.updateQueries, _e = _b.refetchQueries, refetchQueries = _e === void 0 ? [] : _e, _f = _b.awaitRefetchQueries, awaitRefetchQueries = _f === void 0 ? false : _f, updateWithProxyFn = _b.update, onQueryUpdated = _b.onQueryUpdated, _g = _b.fetchPolicy, fetchPolicy = _g === void 0 ? ((_c = this.defaultOptions.mutate) === null || _c === void 0 ? void 0 : _c.fetchPolicy) || \"network-only\" : _g, _h = _b.errorPolicy, errorPolicy = _h === void 0 ? ((_d = this.defaultOptions.mutate) === null || _d === void 0 ? void 0 : _d.errorPolicy) || \"none\" : _h, keepRootFields = _b.keepRootFields, context = _b.context;\n            return __generator(this, function (_j) {\n                switch (_j.label) {\n                    case 0:\n                        invariant(mutation, 28);\n                        invariant(fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\", 29);\n                        mutationId = this.generateMutationId();\n                        mutation = this.cache.transformForLink(this.transform(mutation));\n                        hasClientExports = this.getDocumentInfo(mutation).hasClientExports;\n                        variables = this.getVariables(mutation, variables);\n                        if (!hasClientExports) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this.localState.addExportedVariables(mutation, variables, context)];\n                    case 1:\n                        variables = (_j.sent());\n                        _j.label = 2;\n                    case 2:\n                        mutationStoreValue = this.mutationStore &&\n                            (this.mutationStore[mutationId] = {\n                                mutation: mutation,\n                                variables: variables,\n                                loading: true,\n                                error: null,\n                            });\n                        isOptimistic = optimisticResponse &&\n                            this.markMutationOptimistic(optimisticResponse, {\n                                mutationId: mutationId,\n                                document: mutation,\n                                variables: variables,\n                                fetchPolicy: fetchPolicy,\n                                errorPolicy: errorPolicy,\n                                context: context,\n                                updateQueries: updateQueries,\n                                update: updateWithProxyFn,\n                                keepRootFields: keepRootFields,\n                            });\n                        this.broadcastQueries();\n                        self = this;\n                        return [2 /*return*/, new Promise(function (resolve, reject) {\n                                return asyncMap(self.getObservableFromLink(mutation, __assign(__assign({}, context), { optimisticResponse: isOptimistic ? optimisticResponse : void 0 }), variables, {}, false), function (result) {\n                                    if (graphQLResultHasError(result) && errorPolicy === \"none\") {\n                                        throw new ApolloError({\n                                            graphQLErrors: getGraphQLErrorsFromResult(result),\n                                        });\n                                    }\n                                    if (mutationStoreValue) {\n                                        mutationStoreValue.loading = false;\n                                        mutationStoreValue.error = null;\n                                    }\n                                    var storeResult = __assign({}, result);\n                                    if (typeof refetchQueries === \"function\") {\n                                        refetchQueries = refetchQueries(storeResult);\n                                    }\n                                    if (errorPolicy === \"ignore\" && graphQLResultHasError(storeResult)) {\n                                        delete storeResult.errors;\n                                    }\n                                    return self.markMutationResult({\n                                        mutationId: mutationId,\n                                        result: storeResult,\n                                        document: mutation,\n                                        variables: variables,\n                                        fetchPolicy: fetchPolicy,\n                                        errorPolicy: errorPolicy,\n                                        context: context,\n                                        update: updateWithProxyFn,\n                                        updateQueries: updateQueries,\n                                        awaitRefetchQueries: awaitRefetchQueries,\n                                        refetchQueries: refetchQueries,\n                                        removeOptimistic: isOptimistic ? mutationId : void 0,\n                                        onQueryUpdated: onQueryUpdated,\n                                        keepRootFields: keepRootFields,\n                                    });\n                                }).subscribe({\n                                    next: function (storeResult) {\n                                        self.broadcastQueries();\n                                        // Since mutations might receive multiple payloads from the\n                                        // ApolloLink chain (e.g. when used with @defer),\n                                        // we resolve with a SingleExecutionResult or after the final\n                                        // ExecutionPatchResult has arrived and we have assembled the\n                                        // multipart response into a single result.\n                                        if (!(\"hasNext\" in storeResult) || storeResult.hasNext === false) {\n                                            resolve(__assign(__assign({}, storeResult), { data: self.maskOperation({\n                                                    document: mutation,\n                                                    data: storeResult.data,\n                                                    fetchPolicy: fetchPolicy,\n                                                    id: mutationId,\n                                                }) }));\n                                        }\n                                    },\n                                    error: function (err) {\n                                        if (mutationStoreValue) {\n                                            mutationStoreValue.loading = false;\n                                            mutationStoreValue.error = err;\n                                        }\n                                        if (isOptimistic) {\n                                            self.cache.removeOptimistic(mutationId);\n                                        }\n                                        self.broadcastQueries();\n                                        reject(err instanceof ApolloError ? err : (new ApolloError({\n                                            networkError: err,\n                                        })));\n                                    },\n                                });\n                            })];\n                }\n            });\n        });\n    };\n    QueryManager.prototype.markMutationResult = function (mutation, cache) {\n        var _this = this;\n        if (cache === void 0) { cache = this.cache; }\n        var result = mutation.result;\n        var cacheWrites = [];\n        var skipCache = mutation.fetchPolicy === \"no-cache\";\n        if (!skipCache && shouldWriteResult(result, mutation.errorPolicy)) {\n            if (!isExecutionPatchIncrementalResult(result)) {\n                cacheWrites.push({\n                    result: result.data,\n                    dataId: \"ROOT_MUTATION\",\n                    query: mutation.document,\n                    variables: mutation.variables,\n                });\n            }\n            if (isExecutionPatchIncrementalResult(result) &&\n                isNonEmptyArray(result.incremental)) {\n                var diff = cache.diff({\n                    id: \"ROOT_MUTATION\",\n                    // The cache complains if passed a mutation where it expects a\n                    // query, so we transform mutations and subscriptions to queries\n                    // (only once, thanks to this.transformCache).\n                    query: this.getDocumentInfo(mutation.document).asQuery,\n                    variables: mutation.variables,\n                    optimistic: false,\n                    returnPartialData: true,\n                });\n                var mergedData = void 0;\n                if (diff.result) {\n                    mergedData = mergeIncrementalData(diff.result, result);\n                }\n                if (typeof mergedData !== \"undefined\") {\n                    // cast the ExecutionPatchResult to FetchResult here since\n                    // ExecutionPatchResult never has `data` when returned from the server\n                    result.data = mergedData;\n                    cacheWrites.push({\n                        result: mergedData,\n                        dataId: \"ROOT_MUTATION\",\n                        query: mutation.document,\n                        variables: mutation.variables,\n                    });\n                }\n            }\n            var updateQueries_1 = mutation.updateQueries;\n            if (updateQueries_1) {\n                this.queries.forEach(function (_a, queryId) {\n                    var observableQuery = _a.observableQuery;\n                    var queryName = observableQuery && observableQuery.queryName;\n                    if (!queryName || !hasOwnProperty.call(updateQueries_1, queryName)) {\n                        return;\n                    }\n                    var updater = updateQueries_1[queryName];\n                    var _b = _this.queries.get(queryId), document = _b.document, variables = _b.variables;\n                    // Read the current query result from the store.\n                    var _c = cache.diff({\n                        query: document,\n                        variables: variables,\n                        returnPartialData: true,\n                        optimistic: false,\n                    }), currentQueryResult = _c.result, complete = _c.complete;\n                    if (complete && currentQueryResult) {\n                        // Run our reducer using the current query result and the mutation result.\n                        var nextQueryResult = updater(currentQueryResult, {\n                            mutationResult: result,\n                            queryName: (document && getOperationName(document)) || void 0,\n                            queryVariables: variables,\n                        });\n                        // Write the modified result back into the store if we got a new result.\n                        if (nextQueryResult) {\n                            cacheWrites.push({\n                                result: nextQueryResult,\n                                dataId: \"ROOT_QUERY\",\n                                query: document,\n                                variables: variables,\n                            });\n                        }\n                    }\n                });\n            }\n        }\n        if (cacheWrites.length > 0 ||\n            (mutation.refetchQueries || \"\").length > 0 ||\n            mutation.update ||\n            mutation.onQueryUpdated ||\n            mutation.removeOptimistic) {\n            var results_1 = [];\n            this.refetchQueries({\n                updateCache: function (cache) {\n                    if (!skipCache) {\n                        cacheWrites.forEach(function (write) { return cache.write(write); });\n                    }\n                    // If the mutation has some writes associated with it then we need to\n                    // apply those writes to the store by running this reducer again with\n                    // a write action.\n                    var update = mutation.update;\n                    // Determine whether result is a SingleExecutionResult,\n                    // or the final ExecutionPatchResult.\n                    var isFinalResult = !isExecutionPatchResult(result) ||\n                        (isExecutionPatchIncrementalResult(result) && !result.hasNext);\n                    if (update) {\n                        if (!skipCache) {\n                            // Re-read the ROOT_MUTATION data we just wrote into the cache\n                            // (the first cache.write call in the cacheWrites.forEach loop\n                            // above), so field read functions have a chance to run for\n                            // fields within mutation result objects.\n                            var diff = cache.diff({\n                                id: \"ROOT_MUTATION\",\n                                // The cache complains if passed a mutation where it expects a\n                                // query, so we transform mutations and subscriptions to queries\n                                // (only once, thanks to this.transformCache).\n                                query: _this.getDocumentInfo(mutation.document).asQuery,\n                                variables: mutation.variables,\n                                optimistic: false,\n                                returnPartialData: true,\n                            });\n                            if (diff.complete) {\n                                result = __assign(__assign({}, result), { data: diff.result });\n                                if (\"incremental\" in result) {\n                                    delete result.incremental;\n                                }\n                                if (\"hasNext\" in result) {\n                                    delete result.hasNext;\n                                }\n                            }\n                        }\n                        // If we've received the whole response,\n                        // either a SingleExecutionResult or the final ExecutionPatchResult,\n                        // call the update function.\n                        if (isFinalResult) {\n                            update(cache, result, {\n                                context: mutation.context,\n                                variables: mutation.variables,\n                            });\n                        }\n                    }\n                    // TODO Do this with cache.evict({ id: 'ROOT_MUTATION' }) but make it\n                    // shallow to allow rolling back optimistic evictions.\n                    if (!skipCache && !mutation.keepRootFields && isFinalResult) {\n                        cache.modify({\n                            id: \"ROOT_MUTATION\",\n                            fields: function (value, _a) {\n                                var fieldName = _a.fieldName, DELETE = _a.DELETE;\n                                return fieldName === \"__typename\" ? value : DELETE;\n                            },\n                        });\n                    }\n                },\n                include: mutation.refetchQueries,\n                // Write the final mutation.result to the root layer of the cache.\n                optimistic: false,\n                // Remove the corresponding optimistic layer at the same time as we\n                // write the final non-optimistic result.\n                removeOptimistic: mutation.removeOptimistic,\n                // Let the caller of client.mutate optionally determine the refetching\n                // behavior for watched queries after the mutation.update function runs.\n                // If no onQueryUpdated function was provided for this mutation, pass\n                // null instead of undefined to disable the default refetching behavior.\n                onQueryUpdated: mutation.onQueryUpdated || null,\n            }).forEach(function (result) { return results_1.push(result); });\n            if (mutation.awaitRefetchQueries || mutation.onQueryUpdated) {\n                // Returning a promise here makes the mutation await that promise, so we\n                // include results in that promise's work if awaitRefetchQueries or an\n                // onQueryUpdated function was specified.\n                return Promise.all(results_1).then(function () { return result; });\n            }\n        }\n        return Promise.resolve(result);\n    };\n    QueryManager.prototype.markMutationOptimistic = function (optimisticResponse, mutation) {\n        var _this = this;\n        var data = typeof optimisticResponse === \"function\" ?\n            optimisticResponse(mutation.variables, { IGNORE: IGNORE })\n            : optimisticResponse;\n        if (data === IGNORE) {\n            return false;\n        }\n        this.cache.recordOptimisticTransaction(function (cache) {\n            try {\n                _this.markMutationResult(__assign(__assign({}, mutation), { result: { data: data } }), cache);\n            }\n            catch (error) {\n                globalThis.__DEV__ !== false && invariant.error(error);\n            }\n        }, mutation.mutationId);\n        return true;\n    };\n    QueryManager.prototype.fetchQuery = function (queryId, options, networkStatus) {\n        return this.fetchConcastWithInfo(this.getOrCreateQuery(queryId), options, networkStatus).concast.promise;\n    };\n    QueryManager.prototype.getQueryStore = function () {\n        var store = Object.create(null);\n        this.queries.forEach(function (info, queryId) {\n            store[queryId] = {\n                variables: info.variables,\n                networkStatus: info.networkStatus,\n                networkError: info.networkError,\n                graphQLErrors: info.graphQLErrors,\n            };\n        });\n        return store;\n    };\n    QueryManager.prototype.resetErrors = function (queryId) {\n        var queryInfo = this.queries.get(queryId);\n        if (queryInfo) {\n            queryInfo.networkError = undefined;\n            queryInfo.graphQLErrors = [];\n        }\n    };\n    QueryManager.prototype.transform = function (document) {\n        return this.documentTransform.transformDocument(document);\n    };\n    QueryManager.prototype.getDocumentInfo = function (document) {\n        var transformCache = this.transformCache;\n        if (!transformCache.has(document)) {\n            var cacheEntry = {\n                // TODO These three calls (hasClientExports, shouldForceResolvers, and\n                // usesNonreactiveDirective) are performing independent full traversals\n                // of the transformed document. We should consider merging these\n                // traversals into a single pass in the future, though the work is\n                // cached after the first time.\n                hasClientExports: hasClientExports(document),\n                hasForcedResolvers: this.localState.shouldForceResolvers(document),\n                hasNonreactiveDirective: hasDirectives([\"nonreactive\"], document),\n                nonReactiveQuery: addNonReactiveToNamedFragments(document),\n                clientQuery: this.localState.clientQuery(document),\n                serverQuery: removeDirectivesFromDocument([\n                    { name: \"client\", remove: true },\n                    { name: \"connection\" },\n                    { name: \"nonreactive\" },\n                    { name: \"unmask\" },\n                ], document),\n                defaultVars: getDefaultValues(getOperationDefinition(document)),\n                // Transform any mutation or subscription operations to query operations\n                // so we can read/write them from/to the cache.\n                asQuery: __assign(__assign({}, document), { definitions: document.definitions.map(function (def) {\n                        if (def.kind === \"OperationDefinition\" &&\n                            def.operation !== \"query\") {\n                            return __assign(__assign({}, def), { operation: \"query\" });\n                        }\n                        return def;\n                    }) }),\n            };\n            transformCache.set(document, cacheEntry);\n        }\n        return transformCache.get(document);\n    };\n    QueryManager.prototype.getVariables = function (document, variables) {\n        return __assign(__assign({}, this.getDocumentInfo(document).defaultVars), variables);\n    };\n    QueryManager.prototype.watchQuery = function (options) {\n        var query = this.transform(options.query);\n        // assign variable default values if supplied\n        // NOTE: We don't modify options.query here with the transformed query to\n        // ensure observable.options.query is set to the raw untransformed query.\n        options = __assign(__assign({}, options), { variables: this.getVariables(query, options.variables) });\n        if (typeof options.notifyOnNetworkStatusChange === \"undefined\") {\n            options.notifyOnNetworkStatusChange = false;\n        }\n        var queryInfo = new QueryInfo(this);\n        var observable = new ObservableQuery({\n            queryManager: this,\n            queryInfo: queryInfo,\n            options: options,\n        });\n        observable[\"lastQuery\"] = query;\n        if (!ObservableQuery[\"inactiveOnCreation\"].getValue()) {\n            this.queries.set(observable.queryId, queryInfo);\n        }\n        // We give queryInfo the transformed query to ensure the first cache diff\n        // uses the transformed query instead of the raw query\n        queryInfo.init({\n            document: query,\n            observableQuery: observable,\n            variables: observable.variables,\n        });\n        return observable;\n    };\n    QueryManager.prototype.query = function (options, queryId) {\n        var _this = this;\n        if (queryId === void 0) { queryId = this.generateQueryId(); }\n        invariant(options.query, 30);\n        invariant(options.query.kind === \"Document\", 31);\n        invariant(!options.returnPartialData, 32);\n        invariant(!options.pollInterval, 33);\n        var query = this.transform(options.query);\n        return this.fetchQuery(queryId, __assign(__assign({}, options), { query: query }))\n            .then(function (result) {\n            return result && __assign(__assign({}, result), { data: _this.maskOperation({\n                    document: query,\n                    data: result.data,\n                    fetchPolicy: options.fetchPolicy,\n                    id: queryId,\n                }) });\n        })\n            .finally(function () { return _this.stopQuery(queryId); });\n    };\n    QueryManager.prototype.generateQueryId = function () {\n        return String(this.queryIdCounter++);\n    };\n    QueryManager.prototype.generateRequestId = function () {\n        return this.requestIdCounter++;\n    };\n    QueryManager.prototype.generateMutationId = function () {\n        return String(this.mutationIdCounter++);\n    };\n    QueryManager.prototype.stopQueryInStore = function (queryId) {\n        this.stopQueryInStoreNoBroadcast(queryId);\n        this.broadcastQueries();\n    };\n    QueryManager.prototype.stopQueryInStoreNoBroadcast = function (queryId) {\n        var queryInfo = this.queries.get(queryId);\n        if (queryInfo)\n            queryInfo.stop();\n    };\n    QueryManager.prototype.clearStore = function (options) {\n        if (options === void 0) { options = {\n            discardWatches: true,\n        }; }\n        // Before we have sent the reset action to the store, we can no longer\n        // rely on the results returned by in-flight requests since these may\n        // depend on values that previously existed in the data portion of the\n        // store. So, we cancel the promises and observers that we have issued\n        // so far and not yet resolved (in the case of queries).\n        this.cancelPendingFetches(newInvariantError(34));\n        this.queries.forEach(function (queryInfo) {\n            if (queryInfo.observableQuery) {\n                // Set loading to true so listeners don't trigger unless they want\n                // results with partial data.\n                queryInfo.networkStatus = NetworkStatus.loading;\n            }\n            else {\n                queryInfo.stop();\n            }\n        });\n        if (this.mutationStore) {\n            this.mutationStore = Object.create(null);\n        }\n        // begin removing data from the store\n        return this.cache.reset(options);\n    };\n    QueryManager.prototype.getObservableQueries = function (include) {\n        var _this = this;\n        if (include === void 0) { include = \"active\"; }\n        var queries = new Map();\n        var queryNames = new Map();\n        var queryNamesAndQueryStrings = new Map();\n        var legacyQueryOptions = new Set();\n        if (Array.isArray(include)) {\n            include.forEach(function (desc) {\n                if (typeof desc === \"string\") {\n                    queryNames.set(desc, desc);\n                    queryNamesAndQueryStrings.set(desc, false);\n                }\n                else if (isDocumentNode(desc)) {\n                    var queryString = print(_this.transform(desc));\n                    queryNames.set(queryString, getOperationName(desc));\n                    queryNamesAndQueryStrings.set(queryString, false);\n                }\n                else if (isNonNullObject(desc) && desc.query) {\n                    legacyQueryOptions.add(desc);\n                }\n            });\n        }\n        this.queries.forEach(function (_a, queryId) {\n            var oq = _a.observableQuery, document = _a.document;\n            if (oq) {\n                if (include === \"all\") {\n                    queries.set(queryId, oq);\n                    return;\n                }\n                var queryName = oq.queryName, fetchPolicy = oq.options.fetchPolicy;\n                if (fetchPolicy === \"standby\" ||\n                    (include === \"active\" && !oq.hasObservers())) {\n                    return;\n                }\n                if (include === \"active\" ||\n                    (queryName && queryNamesAndQueryStrings.has(queryName)) ||\n                    (document && queryNamesAndQueryStrings.has(print(document)))) {\n                    queries.set(queryId, oq);\n                    if (queryName)\n                        queryNamesAndQueryStrings.set(queryName, true);\n                    if (document)\n                        queryNamesAndQueryStrings.set(print(document), true);\n                }\n            }\n        });\n        if (legacyQueryOptions.size) {\n            legacyQueryOptions.forEach(function (options) {\n                // We will be issuing a fresh network request for this query, so we\n                // pre-allocate a new query ID here, using a special prefix to enable\n                // cleaning up these temporary queries later, after fetching.\n                var queryId = makeUniqueId(\"legacyOneTimeQuery\");\n                var queryInfo = _this.getOrCreateQuery(queryId).init({\n                    document: options.query,\n                    variables: options.variables,\n                });\n                var oq = new ObservableQuery({\n                    queryManager: _this,\n                    queryInfo: queryInfo,\n                    options: __assign(__assign({}, options), { fetchPolicy: \"network-only\" }),\n                });\n                invariant(oq.queryId === queryId);\n                queryInfo.setObservableQuery(oq);\n                queries.set(queryId, oq);\n            });\n        }\n        if (globalThis.__DEV__ !== false && queryNamesAndQueryStrings.size) {\n            queryNamesAndQueryStrings.forEach(function (included, nameOrQueryString) {\n                if (!included) {\n                    var queryName = queryNames.get(nameOrQueryString);\n                    if (queryName) {\n                        globalThis.__DEV__ !== false && invariant.warn(35, queryName);\n                    }\n                    else {\n                        globalThis.__DEV__ !== false && invariant.warn(36);\n                    }\n                }\n            });\n        }\n        return queries;\n    };\n    QueryManager.prototype.reFetchObservableQueries = function (includeStandby) {\n        var _this = this;\n        if (includeStandby === void 0) { includeStandby = false; }\n        var observableQueryPromises = [];\n        this.getObservableQueries(includeStandby ? \"all\" : \"active\").forEach(function (observableQuery, queryId) {\n            var fetchPolicy = observableQuery.options.fetchPolicy;\n            observableQuery.resetLastResults();\n            if (includeStandby ||\n                (fetchPolicy !== \"standby\" && fetchPolicy !== \"cache-only\")) {\n                observableQueryPromises.push(observableQuery.refetch());\n            }\n            (_this.queries.get(queryId) || observableQuery[\"queryInfo\"]).setDiff(null);\n        });\n        this.broadcastQueries();\n        return Promise.all(observableQueryPromises);\n    };\n    QueryManager.prototype.startGraphQLSubscription = function (options) {\n        var _this = this;\n        var query = options.query, variables = options.variables;\n        var fetchPolicy = options.fetchPolicy, _a = options.errorPolicy, errorPolicy = _a === void 0 ? \"none\" : _a, _b = options.context, context = _b === void 0 ? {} : _b, _c = options.extensions, extensions = _c === void 0 ? {} : _c;\n        query = this.transform(query);\n        variables = this.getVariables(query, variables);\n        var makeObservable = function (variables) {\n            return _this.getObservableFromLink(query, context, variables, extensions).map(function (result) {\n                if (fetchPolicy !== \"no-cache\") {\n                    // the subscription interface should handle not sending us results we no longer subscribe to.\n                    // XXX I don't think we ever send in an object with errors, but we might in the future...\n                    if (shouldWriteResult(result, errorPolicy)) {\n                        _this.cache.write({\n                            query: query,\n                            result: result.data,\n                            dataId: \"ROOT_SUBSCRIPTION\",\n                            variables: variables,\n                        });\n                    }\n                    _this.broadcastQueries();\n                }\n                var hasErrors = graphQLResultHasError(result);\n                var hasProtocolErrors = graphQLResultHasProtocolErrors(result);\n                if (hasErrors || hasProtocolErrors) {\n                    var errors = {};\n                    if (hasErrors) {\n                        errors.graphQLErrors = result.errors;\n                    }\n                    if (hasProtocolErrors) {\n                        errors.protocolErrors = result.extensions[PROTOCOL_ERRORS_SYMBOL];\n                    }\n                    // `errorPolicy` is a mechanism for handling GraphQL errors, according\n                    // to our documentation, so we throw protocol errors regardless of the\n                    // set error policy.\n                    if (errorPolicy === \"none\" || hasProtocolErrors) {\n                        throw new ApolloError(errors);\n                    }\n                }\n                if (errorPolicy === \"ignore\") {\n                    delete result.errors;\n                }\n                return result;\n            });\n        };\n        if (this.getDocumentInfo(query).hasClientExports) {\n            var observablePromise_1 = this.localState\n                .addExportedVariables(query, variables, context)\n                .then(makeObservable);\n            return new Observable(function (observer) {\n                var sub = null;\n                observablePromise_1.then(function (observable) { return (sub = observable.subscribe(observer)); }, observer.error);\n                return function () { return sub && sub.unsubscribe(); };\n            });\n        }\n        return makeObservable(variables);\n    };\n    QueryManager.prototype.stopQuery = function (queryId) {\n        this.stopQueryNoBroadcast(queryId);\n        this.broadcastQueries();\n    };\n    QueryManager.prototype.stopQueryNoBroadcast = function (queryId) {\n        this.stopQueryInStoreNoBroadcast(queryId);\n        this.removeQuery(queryId);\n    };\n    QueryManager.prototype.removeQuery = function (queryId) {\n        var _a;\n        // teardown all links\n        // Both `QueryManager.fetchRequest` and `QueryManager.query` create separate promises\n        // that each add their reject functions to fetchCancelFns.\n        // A query created with `QueryManager.query()` could trigger a `QueryManager.fetchRequest`.\n        // The same queryId could have two rejection fns for two promises\n        this.fetchCancelFns.delete(queryId);\n        if (this.queries.has(queryId)) {\n            (_a = this.queries.get(queryId)) === null || _a === void 0 ? void 0 : _a.stop();\n            this.queries.delete(queryId);\n        }\n    };\n    QueryManager.prototype.broadcastQueries = function () {\n        if (this.onBroadcast)\n            this.onBroadcast();\n        this.queries.forEach(function (info) { var _a; return (_a = info.observableQuery) === null || _a === void 0 ? void 0 : _a[\"notify\"](); });\n    };\n    QueryManager.prototype.getLocalState = function () {\n        return this.localState;\n    };\n    QueryManager.prototype.getObservableFromLink = function (query, context, variables, extensions, \n    // Prefer context.queryDeduplication if specified.\n    deduplication) {\n        var _this = this;\n        var _a;\n        if (deduplication === void 0) { deduplication = (_a = context === null || context === void 0 ? void 0 : context.queryDeduplication) !== null && _a !== void 0 ? _a : this.queryDeduplication; }\n        var observable;\n        var _b = this.getDocumentInfo(query), serverQuery = _b.serverQuery, clientQuery = _b.clientQuery;\n        if (serverQuery) {\n            var _c = this, inFlightLinkObservables_1 = _c.inFlightLinkObservables, link = _c.link;\n            var operation = {\n                query: serverQuery,\n                variables: variables,\n                operationName: getOperationName(serverQuery) || void 0,\n                context: this.prepareContext(__assign(__assign({}, context), { forceFetch: !deduplication })),\n                extensions: extensions,\n            };\n            context = operation.context;\n            if (deduplication) {\n                var printedServerQuery_1 = print(serverQuery);\n                var varJson_1 = canonicalStringify(variables);\n                var entry = inFlightLinkObservables_1.lookup(printedServerQuery_1, varJson_1);\n                observable = entry.observable;\n                if (!observable) {\n                    var concast_1 = new Concast([\n                        execute(link, operation),\n                    ]);\n                    observable = entry.observable = concast_1;\n                    concast_1.beforeNext(function cb(method, arg) {\n                        if (method === \"next\" && \"hasNext\" in arg && arg.hasNext) {\n                            concast_1.beforeNext(cb);\n                        }\n                        else {\n                            inFlightLinkObservables_1.remove(printedServerQuery_1, varJson_1);\n                        }\n                    });\n                }\n            }\n            else {\n                observable = new Concast([\n                    execute(link, operation),\n                ]);\n            }\n        }\n        else {\n            observable = new Concast([Observable.of({ data: {} })]);\n            context = this.prepareContext(context);\n        }\n        if (clientQuery) {\n            observable = asyncMap(observable, function (result) {\n                return _this.localState.runResolvers({\n                    document: clientQuery,\n                    remoteResult: result,\n                    context: context,\n                    variables: variables,\n                });\n            });\n        }\n        return observable;\n    };\n    QueryManager.prototype.getResultsFromLink = function (queryInfo, cacheWriteBehavior, options) {\n        var requestId = (queryInfo.lastRequestId = this.generateRequestId());\n        // Performing transformForLink here gives this.cache a chance to fill in\n        // missing fragment definitions (for example) before sending this document\n        // through the link chain.\n        var linkDocument = this.cache.transformForLink(options.query);\n        return asyncMap(this.getObservableFromLink(linkDocument, options.context, options.variables), function (result) {\n            var graphQLErrors = getGraphQLErrorsFromResult(result);\n            var hasErrors = graphQLErrors.length > 0;\n            var errorPolicy = options.errorPolicy;\n            // If we interrupted this request by calling getResultsFromLink again\n            // with the same QueryInfo object, we ignore the old results.\n            if (requestId >= queryInfo.lastRequestId) {\n                if (hasErrors && errorPolicy === \"none\") {\n                    // Throwing here effectively calls observer.error.\n                    throw queryInfo.markError(new ApolloError({\n                        graphQLErrors: graphQLErrors,\n                    }));\n                }\n                // Use linkDocument rather than queryInfo.document so the\n                // operation/fragments used to write the result are the same as the\n                // ones used to obtain it from the link.\n                queryInfo.markResult(result, linkDocument, options, cacheWriteBehavior);\n                queryInfo.markReady();\n            }\n            var aqr = {\n                data: result.data,\n                loading: false,\n                networkStatus: NetworkStatus.ready,\n            };\n            // In the case we start multiple network requests simulatenously, we\n            // want to ensure we properly set `data` if we're reporting on an old\n            // result which will not be caught by the conditional above that ends up\n            // throwing the markError result.\n            if (hasErrors && errorPolicy === \"none\") {\n                aqr.data = void 0;\n            }\n            if (hasErrors && errorPolicy !== \"ignore\") {\n                aqr.errors = graphQLErrors;\n                aqr.networkStatus = NetworkStatus.error;\n            }\n            return aqr;\n        }, function (networkError) {\n            var error = isApolloError(networkError) ? networkError : (new ApolloError({ networkError: networkError }));\n            // Avoid storing errors from older interrupted queries.\n            if (requestId >= queryInfo.lastRequestId) {\n                queryInfo.markError(error);\n            }\n            throw error;\n        });\n    };\n    QueryManager.prototype.fetchConcastWithInfo = function (queryInfo, options, \n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus, query) {\n        var _this = this;\n        if (networkStatus === void 0) { networkStatus = NetworkStatus.loading; }\n        if (query === void 0) { query = options.query; }\n        var variables = this.getVariables(query, options.variables);\n        var defaults = this.defaultOptions.watchQuery;\n        var _a = options.fetchPolicy, fetchPolicy = _a === void 0 ? (defaults && defaults.fetchPolicy) || \"cache-first\" : _a, _b = options.errorPolicy, errorPolicy = _b === void 0 ? (defaults && defaults.errorPolicy) || \"none\" : _b, _c = options.returnPartialData, returnPartialData = _c === void 0 ? false : _c, _d = options.notifyOnNetworkStatusChange, notifyOnNetworkStatusChange = _d === void 0 ? false : _d, _e = options.context, context = _e === void 0 ? {} : _e;\n        var normalized = Object.assign({}, options, {\n            query: query,\n            variables: variables,\n            fetchPolicy: fetchPolicy,\n            errorPolicy: errorPolicy,\n            returnPartialData: returnPartialData,\n            notifyOnNetworkStatusChange: notifyOnNetworkStatusChange,\n            context: context,\n        });\n        var fromVariables = function (variables) {\n            // Since normalized is always a fresh copy of options, it's safe to\n            // modify its properties here, rather than creating yet another new\n            // WatchQueryOptions object.\n            normalized.variables = variables;\n            var sourcesWithInfo = _this.fetchQueryByPolicy(queryInfo, normalized, networkStatus);\n            if (\n            // If we're in standby, postpone advancing options.fetchPolicy using\n            // applyNextFetchPolicy.\n            normalized.fetchPolicy !== \"standby\" &&\n                // The \"standby\" policy currently returns [] from fetchQueryByPolicy, so\n                // this is another way to detect when nothing was done/fetched.\n                sourcesWithInfo.sources.length > 0 &&\n                queryInfo.observableQuery) {\n                queryInfo.observableQuery[\"applyNextFetchPolicy\"](\"after-fetch\", options);\n            }\n            return sourcesWithInfo;\n        };\n        // This cancel function needs to be set before the concast is created,\n        // in case concast creation synchronously cancels the request.\n        var cleanupCancelFn = function () { return _this.fetchCancelFns.delete(queryInfo.queryId); };\n        this.fetchCancelFns.set(queryInfo.queryId, function (reason) {\n            cleanupCancelFn();\n            // This delay ensures the concast variable has been initialized.\n            setTimeout(function () { return concast.cancel(reason); });\n        });\n        var concast, containsDataFromLink;\n        // If the query has @export(as: ...) directives, then we need to\n        // process those directives asynchronously. When there are no\n        // @export directives (the common case), we deliberately avoid\n        // wrapping the result of this.fetchQueryByPolicy in a Promise,\n        // since the timing of result delivery is (unfortunately) important\n        // for backwards compatibility. TODO This code could be simpler if\n        // we deprecated and removed LocalState.\n        if (this.getDocumentInfo(normalized.query).hasClientExports) {\n            concast = new Concast(this.localState\n                .addExportedVariables(normalized.query, normalized.variables, normalized.context)\n                .then(fromVariables)\n                .then(function (sourcesWithInfo) { return sourcesWithInfo.sources; }));\n            // there is just no way we can synchronously get the *right* value here,\n            // so we will assume `true`, which is the behaviour before the bug fix in\n            // #10597. This means that bug is not fixed in that case, and is probably\n            // un-fixable with reasonable effort for the edge case of @export as\n            // directives.\n            containsDataFromLink = true;\n        }\n        else {\n            var sourcesWithInfo = fromVariables(normalized.variables);\n            containsDataFromLink = sourcesWithInfo.fromLink;\n            concast = new Concast(sourcesWithInfo.sources);\n        }\n        concast.promise.then(cleanupCancelFn, cleanupCancelFn);\n        return {\n            concast: concast,\n            fromLink: containsDataFromLink,\n        };\n    };\n    QueryManager.prototype.refetchQueries = function (_a) {\n        var _this = this;\n        var updateCache = _a.updateCache, include = _a.include, _b = _a.optimistic, optimistic = _b === void 0 ? false : _b, _c = _a.removeOptimistic, removeOptimistic = _c === void 0 ? optimistic ? makeUniqueId(\"refetchQueries\") : void 0 : _c, onQueryUpdated = _a.onQueryUpdated;\n        var includedQueriesById = new Map();\n        if (include) {\n            this.getObservableQueries(include).forEach(function (oq, queryId) {\n                includedQueriesById.set(queryId, {\n                    oq: oq,\n                    lastDiff: (_this.queries.get(queryId) || oq[\"queryInfo\"]).getDiff(),\n                });\n            });\n        }\n        var results = new Map();\n        if (updateCache) {\n            this.cache.batch({\n                update: updateCache,\n                // Since you can perform any combination of cache reads and/or writes in\n                // the cache.batch update function, its optimistic option can be either\n                // a boolean or a string, representing three distinct modes of\n                // operation:\n                //\n                // * false: read/write only the root layer\n                // * true: read/write the topmost layer\n                // * string: read/write a fresh optimistic layer with that ID string\n                //\n                // When typeof optimistic === \"string\", a new optimistic layer will be\n                // temporarily created within cache.batch with that string as its ID. If\n                // we then pass that same string as the removeOptimistic option, we can\n                // make cache.batch immediately remove the optimistic layer after\n                // running the updateCache function, triggering only one broadcast.\n                //\n                // However, the refetchQueries method accepts only true or false for its\n                // optimistic option (not string). We interpret true to mean a temporary\n                // optimistic layer should be created, to allow efficiently rolling back\n                // the effect of the updateCache function, which involves passing a\n                // string instead of true as the optimistic option to cache.batch, when\n                // refetchQueries receives optimistic: true.\n                //\n                // In other words, we are deliberately not supporting the use case of\n                // writing to an *existing* optimistic layer (using the refetchQueries\n                // updateCache function), since that would potentially interfere with\n                // other optimistic updates in progress. Instead, you can read/write\n                // only the root layer by passing optimistic: false to refetchQueries,\n                // or you can read/write a brand new optimistic layer that will be\n                // automatically removed by passing optimistic: true.\n                optimistic: (optimistic && removeOptimistic) || false,\n                // The removeOptimistic option can also be provided by itself, even if\n                // optimistic === false, to remove some previously-added optimistic\n                // layer safely and efficiently, like we do in markMutationResult.\n                //\n                // If an explicit removeOptimistic string is provided with optimistic:\n                // true, the removeOptimistic string will determine the ID of the\n                // temporary optimistic layer, in case that ever matters.\n                removeOptimistic: removeOptimistic,\n                onWatchUpdated: function (watch, diff, lastDiff) {\n                    var oq = watch.watcher instanceof QueryInfo && watch.watcher.observableQuery;\n                    if (oq) {\n                        if (onQueryUpdated) {\n                            // Since we're about to handle this query now, remove it from\n                            // includedQueriesById, in case it was added earlier because of\n                            // options.include.\n                            includedQueriesById.delete(oq.queryId);\n                            var result = onQueryUpdated(oq, diff, lastDiff);\n                            if (result === true) {\n                                // The onQueryUpdated function requested the default refetching\n                                // behavior by returning true.\n                                result = oq.refetch();\n                            }\n                            // Record the result in the results Map, as long as onQueryUpdated\n                            // did not return false to skip/ignore this result.\n                            if (result !== false) {\n                                results.set(oq, result);\n                            }\n                            // Allow the default cache broadcast to happen, except when\n                            // onQueryUpdated returns false.\n                            return result;\n                        }\n                        if (onQueryUpdated !== null) {\n                            // If we don't have an onQueryUpdated function, and onQueryUpdated\n                            // was not disabled by passing null, make sure this query is\n                            // \"included\" like any other options.include-specified query.\n                            includedQueriesById.set(oq.queryId, { oq: oq, lastDiff: lastDiff, diff: diff });\n                        }\n                    }\n                },\n            });\n        }\n        if (includedQueriesById.size) {\n            includedQueriesById.forEach(function (_a, queryId) {\n                var oq = _a.oq, lastDiff = _a.lastDiff, diff = _a.diff;\n                var result;\n                // If onQueryUpdated is provided, we want to use it for all included\n                // queries, even the QueryOptions ones.\n                if (onQueryUpdated) {\n                    if (!diff) {\n                        diff = _this.cache.diff(oq[\"queryInfo\"][\"getDiffOptions\"]());\n                    }\n                    result = onQueryUpdated(oq, diff, lastDiff);\n                }\n                // Otherwise, we fall back to refetching.\n                if (!onQueryUpdated || result === true) {\n                    result = oq.refetch();\n                }\n                if (result !== false) {\n                    results.set(oq, result);\n                }\n                if (queryId.indexOf(\"legacyOneTimeQuery\") >= 0) {\n                    _this.stopQueryNoBroadcast(queryId);\n                }\n            });\n        }\n        if (removeOptimistic) {\n            // In case no updateCache callback was provided (so cache.batch was not\n            // called above, and thus did not already remove the optimistic layer),\n            // remove it here. Since this is a no-op when the layer has already been\n            // removed, we do it even if we called cache.batch above, since it's\n            // possible this.cache is an instance of some ApolloCache subclass other\n            // than InMemoryCache, and does not fully support the removeOptimistic\n            // option for cache.batch.\n            this.cache.removeOptimistic(removeOptimistic);\n        }\n        return results;\n    };\n    QueryManager.prototype.maskOperation = function (options) {\n        var _a, _b, _c;\n        var document = options.document, data = options.data;\n        if (globalThis.__DEV__ !== false) {\n            var fetchPolicy = options.fetchPolicy, id = options.id;\n            var operationType = (_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation;\n            var operationId = ((_b = operationType === null || operationType === void 0 ? void 0 : operationType[0]) !== null && _b !== void 0 ? _b : \"o\") + id;\n            if (this.dataMasking &&\n                fetchPolicy === \"no-cache\" &&\n                !isFullyUnmaskedOperation(document) &&\n                !this.noCacheWarningsByQueryId.has(operationId)) {\n                this.noCacheWarningsByQueryId.add(operationId);\n                globalThis.__DEV__ !== false && invariant.warn(\n                    37,\n                    (_c = getOperationName(document)) !== null && _c !== void 0 ? _c : \"Unnamed \".concat(operationType !== null && operationType !== void 0 ? operationType : \"operation\")\n                );\n            }\n        }\n        return (this.dataMasking ?\n            maskOperation(data, document, this.cache)\n            : data);\n    };\n    QueryManager.prototype.maskFragment = function (options) {\n        var data = options.data, fragment = options.fragment, fragmentName = options.fragmentName;\n        return this.dataMasking ?\n            maskFragment(data, fragment, this.cache, fragmentName)\n            : data;\n    };\n    QueryManager.prototype.fetchQueryByPolicy = function (queryInfo, _a, \n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus) {\n        var _this = this;\n        var query = _a.query, variables = _a.variables, fetchPolicy = _a.fetchPolicy, refetchWritePolicy = _a.refetchWritePolicy, errorPolicy = _a.errorPolicy, returnPartialData = _a.returnPartialData, context = _a.context, notifyOnNetworkStatusChange = _a.notifyOnNetworkStatusChange;\n        var oldNetworkStatus = queryInfo.networkStatus;\n        queryInfo.init({\n            document: query,\n            variables: variables,\n            networkStatus: networkStatus,\n        });\n        var readCache = function () { return queryInfo.getDiff(); };\n        var resultsFromCache = function (diff, networkStatus) {\n            if (networkStatus === void 0) { networkStatus = queryInfo.networkStatus || NetworkStatus.loading; }\n            var data = diff.result;\n            if (globalThis.__DEV__ !== false && !returnPartialData && !equal(data, {})) {\n                logMissingFieldErrors(diff.missing);\n            }\n            var fromData = function (data) {\n                return Observable.of(__assign({ data: data, loading: isNetworkRequestInFlight(networkStatus), networkStatus: networkStatus }, (diff.complete ? null : { partial: true })));\n            };\n            if (data && _this.getDocumentInfo(query).hasForcedResolvers) {\n                return _this.localState\n                    .runResolvers({\n                    document: query,\n                    remoteResult: { data: data },\n                    context: context,\n                    variables: variables,\n                    onlyRunForcedResolvers: true,\n                })\n                    .then(function (resolved) { return fromData(resolved.data || void 0); });\n            }\n            // Resolves https://github.com/apollographql/apollo-client/issues/10317.\n            // If errorPolicy is 'none' and notifyOnNetworkStatusChange is true,\n            // data was incorrectly returned from the cache on refetch:\n            // if diff.missing exists, we should not return cache data.\n            if (errorPolicy === \"none\" &&\n                networkStatus === NetworkStatus.refetch &&\n                Array.isArray(diff.missing)) {\n                return fromData(void 0);\n            }\n            return fromData(data);\n        };\n        var cacheWriteBehavior = fetchPolicy === \"no-cache\" ? 0 /* CacheWriteBehavior.FORBID */\n            // Watched queries must opt into overwriting existing data on refetch,\n            // by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n            : (networkStatus === NetworkStatus.refetch &&\n                refetchWritePolicy !== \"merge\") ?\n                1 /* CacheWriteBehavior.OVERWRITE */\n                : 2 /* CacheWriteBehavior.MERGE */;\n        var resultsFromLink = function () {\n            return _this.getResultsFromLink(queryInfo, cacheWriteBehavior, {\n                query: query,\n                variables: variables,\n                context: context,\n                fetchPolicy: fetchPolicy,\n                errorPolicy: errorPolicy,\n            });\n        };\n        var shouldNotify = notifyOnNetworkStatusChange &&\n            typeof oldNetworkStatus === \"number\" &&\n            oldNetworkStatus !== networkStatus &&\n            isNetworkRequestInFlight(networkStatus);\n        switch (fetchPolicy) {\n            default:\n            case \"cache-first\": {\n                var diff = readCache();\n                if (diff.complete) {\n                    return {\n                        fromLink: false,\n                        sources: [resultsFromCache(diff, queryInfo.markReady())],\n                    };\n                }\n                if (returnPartialData || shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(diff), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            }\n            case \"cache-and-network\": {\n                var diff = readCache();\n                if (diff.complete || returnPartialData || shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(diff), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            }\n            case \"cache-only\":\n                return {\n                    fromLink: false,\n                    sources: [resultsFromCache(readCache(), queryInfo.markReady())],\n                };\n            case \"network-only\":\n                if (shouldNotify) {\n                    return {\n                        fromLink: true,\n                        sources: [resultsFromCache(readCache()), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            case \"no-cache\":\n                if (shouldNotify) {\n                    return {\n                        fromLink: true,\n                        // Note that queryInfo.getDiff() for no-cache queries does not call\n                        // cache.diff, but instead returns a { complete: false } stub result\n                        // when there is no queryInfo.diff already defined.\n                        sources: [resultsFromCache(queryInfo.getDiff()), resultsFromLink()],\n                    };\n                }\n                return { fromLink: true, sources: [resultsFromLink()] };\n            case \"standby\":\n                return { fromLink: false, sources: [] };\n        }\n    };\n    QueryManager.prototype.getOrCreateQuery = function (queryId) {\n        if (queryId && !this.queries.has(queryId)) {\n            this.queries.set(queryId, new QueryInfo(this, queryId));\n        }\n        return this.queries.get(queryId);\n    };\n    QueryManager.prototype.prepareContext = function (context) {\n        if (context === void 0) { context = {}; }\n        var newContext = this.localState.prepareContext(context);\n        return __assign(__assign(__assign({}, this.defaultContext), newContext), { clientAwareness: this.clientAwareness });\n    };\n    return QueryManager;\n}());\nexport { QueryManager };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,+BAA+B;AAC5E,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,8BAA8B,EAAEC,aAAa,EAAEC,iCAAiC,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,4BAA4B,QAAS,uBAAuB;AACzM,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,OAAO,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,iBAAiB,QAAS,uBAAuB;AAC1R,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,WAAW,EAAEC,aAAa,EAAEC,8BAA8B,QAAS,oBAAoB;AAChG,SAASC,eAAe,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC7E,SAASC,aAAa,EAAEC,wBAAwB,QAAQ,oBAAoB;AAC5E,SAASC,SAAS,EAAEC,iBAAiB,QAAS,gBAAgB;AAC9D,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,IAAIC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACpD,IAAIG,MAAM,GAAGF,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,oBAAoB,EAAEC,UAAU,QAAQ,uBAAuB;AACxE,SAASC,YAAY,EAAEC,aAAa,QAAQ,qBAAqB;AACjE,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACC,OAAO,EAAE;IAC3B,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB;IACA;IACA,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACE,cAAc,GAAG,IAAIX,oBAAoB,CAACC,UAAU,CAAC,8BAA8B,CAAC,IACrF,IAAI,CAAC,uDAAuD,CAAC;IACjE,IAAI,CAACW,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B;IACA;IACA,IAAI,CAACC,uBAAuB,GAAG,IAAIhB,IAAI,CAAC,KAAK,CAAC;IAC9C,IAAI,CAACiB,wBAAwB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzC,IAAIC,wBAAwB,GAAG,IAAIrC,iBAAiB,CAAC,UAAUsC,QAAQ,EAAE;MAAE,OAAOb,KAAK,CAACc,KAAK,CAACC,iBAAiB,CAACF,QAAQ,CAAC;IAAE,CAAC;IAC5H;IACA;MAAEC,KAAK,EAAE;IAAM,CAAC,CAAC;IACjB,IAAI,CAACA,KAAK,GAAGf,OAAO,CAACe,KAAK;IAC1B,IAAI,CAACE,IAAI,GAAGjB,OAAO,CAACiB,IAAI;IACxB,IAAI,CAACC,cAAc,GAAGlB,OAAO,CAACkB,cAAc;IAC5C,IAAI,CAACC,kBAAkB,GAAGnB,OAAO,CAACmB,kBAAkB;IACpD,IAAI,CAACjB,eAAe,GAAGF,OAAO,CAACE,eAAe;IAC9C,IAAI,CAACkB,UAAU,GAAGpB,OAAO,CAACoB,UAAU;IACpC,IAAI,CAACC,OAAO,GAAGrB,OAAO,CAACqB,OAAO;IAC9B,IAAI,CAACC,sBAAsB,GAAGtB,OAAO,CAACsB,sBAAsB;IAC5D,IAAI,CAACC,WAAW,GAAGvB,OAAO,CAACuB,WAAW;IACtC,IAAIC,iBAAiB,GAAGxB,OAAO,CAACwB,iBAAiB;IACjD,IAAI,CAACA,iBAAiB,GAClBA,iBAAiB,GACbX,wBAAwB,CACnBY,MAAM,CAACD,iBAAiB;IACzB;IACA;IACA;IACA;IAAA,CACCC,MAAM,CAACZ,wBAAwB,CAAC,GACnCA,wBAAwB;IAClC,IAAI,CAACa,cAAc,GAAG1B,OAAO,CAAC0B,cAAc,IAAIpC,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC;IACnE,IAAK,IAAI,CAACkC,WAAW,GAAG3B,OAAO,CAAC2B,WAAW,EAAG;MAC1C,IAAI,CAACC,aAAa,GAAGtC,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;EACIM,YAAY,CAACR,SAAS,CAACsC,IAAI,GAAG,YAAY;IACtC,IAAI5B,KAAK,GAAG,IAAI;IAChB,IAAI,CAACE,OAAO,CAAC2B,OAAO,CAAC,UAAUC,KAAK,EAAEC,OAAO,EAAE;MAC3C/B,KAAK,CAACgC,oBAAoB,CAACD,OAAO,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACE,oBAAoB,CAACjF,iBAAiB,CAAC,EAAE,CAAC,CAAC;EACpD,CAAC;EACD8C,YAAY,CAACR,SAAS,CAAC2C,oBAAoB,GAAG,UAAUC,KAAK,EAAE;IAC3D,IAAI,CAAC9B,cAAc,CAACyB,OAAO,CAAC,UAAUM,MAAM,EAAE;MAAE,OAAOA,MAAM,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;IACxE,IAAI,CAAC9B,cAAc,CAACgC,KAAK,CAAC,CAAC;EAC/B,CAAC;EACDtC,YAAY,CAACR,SAAS,CAAC+C,MAAM,GAAG,UAAUC,EAAE,EAAE;IAC1C,OAAOzF,SAAS,CAAC,IAAI,EAAE0F,SAAS,EAAE,KAAK,CAAC,EAAE,UAAUC,EAAE,EAAE;MACpD,IAAIC,UAAU,EAAE5E,gBAAgB,EAAE6E,kBAAkB,EAAEC,YAAY,EAAEC,IAAI;MACxE,IAAIC,EAAE,EAAEC,EAAE;MACV,IAAIC,QAAQ,GAAGP,EAAE,CAACO,QAAQ;QAAEC,SAAS,GAAGR,EAAE,CAACQ,SAAS;QAAEC,kBAAkB,GAAGT,EAAE,CAACS,kBAAkB;QAAEC,aAAa,GAAGV,EAAE,CAACU,aAAa;QAAEC,EAAE,GAAGX,EAAE,CAACY,cAAc;QAAEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;QAAEE,EAAE,GAAGb,EAAE,CAACc,mBAAmB;QAAEA,mBAAmB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;QAAEE,iBAAiB,GAAGf,EAAE,CAACgB,MAAM;QAAEC,cAAc,GAAGjB,EAAE,CAACiB,cAAc;QAAEC,EAAE,GAAGlB,EAAE,CAACmB,WAAW;QAAEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAACb,EAAE,GAAG,IAAI,CAAC5B,cAAc,CAACoB,MAAM,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,WAAW,KAAK,cAAc,GAAGD,EAAE;QAAEE,EAAE,GAAGpB,EAAE,CAACqB,WAAW;QAAEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAACd,EAAE,GAAG,IAAI,CAAC7B,cAAc,CAACoB,MAAM,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,WAAW,KAAK,MAAM,GAAGD,EAAE;QAAEE,cAAc,GAAGtB,EAAE,CAACsB,cAAc;QAAEC,OAAO,GAAGvB,EAAE,CAACuB,OAAO;MAC9sB,OAAOjH,WAAW,CAAC,IAAI,EAAE,UAAUkH,EAAE,EAAE;QACnC,QAAQA,EAAE,CAACC,KAAK;UACZ,KAAK,CAAC;YACFlH,SAAS,CAACgG,QAAQ,EAAE,EAAE,CAAC;YACvBhG,SAAS,CAAC4G,WAAW,KAAK,cAAc,IAAIA,WAAW,KAAK,UAAU,EAAE,EAAE,CAAC;YAC3ElB,UAAU,GAAG,IAAI,CAACyB,kBAAkB,CAAC,CAAC;YACtCnB,QAAQ,GAAG,IAAI,CAACjC,KAAK,CAACqD,gBAAgB,CAAC,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAAC,CAAC;YAChElF,gBAAgB,GAAG,IAAI,CAACwG,eAAe,CAACtB,QAAQ,CAAC,CAAClF,gBAAgB;YAClEmF,SAAS,GAAG,IAAI,CAACsB,YAAY,CAACvB,QAAQ,EAAEC,SAAS,CAAC;YAClD,IAAI,CAACnF,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YAC9C,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACsD,UAAU,CAACoD,oBAAoB,CAACxB,QAAQ,EAAEC,SAAS,EAAEe,OAAO,CAAC,CAAC;UAC5F,KAAK,CAAC;YACFf,SAAS,GAAIgB,EAAE,CAACQ,IAAI,CAAC,CAAE;YACvBR,EAAE,CAACC,KAAK,GAAG,CAAC;UAChB,KAAK,CAAC;YACFvB,kBAAkB,GAAG,IAAI,CAACf,aAAa,KAClC,IAAI,CAACA,aAAa,CAACc,UAAU,CAAC,GAAG;cAC9BM,QAAQ,EAAEA,QAAQ;cAClBC,SAAS,EAAEA,SAAS;cACpByB,OAAO,EAAE,IAAI;cACbvC,KAAK,EAAE;YACX,CAAC,CAAC;YACNS,YAAY,GAAGM,kBAAkB,IAC7B,IAAI,CAACyB,sBAAsB,CAACzB,kBAAkB,EAAE;cAC5CR,UAAU,EAAEA,UAAU;cACtB5B,QAAQ,EAAEkC,QAAQ;cAClBC,SAAS,EAAEA,SAAS;cACpBW,WAAW,EAAEA,WAAW;cACxBE,WAAW,EAAEA,WAAW;cACxBE,OAAO,EAAEA,OAAO;cAChBb,aAAa,EAAEA,aAAa;cAC5BM,MAAM,EAAED,iBAAiB;cACzBO,cAAc,EAAEA;YACpB,CAAC,CAAC;YACN,IAAI,CAACa,gBAAgB,CAAC,CAAC;YACvB/B,IAAI,GAAG,IAAI;YACX,OAAO,CAAC,CAAC,CAAC,YAAY,IAAIgC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;cACrD,OAAO7G,QAAQ,CAAC2E,IAAI,CAACmC,qBAAqB,CAAChC,QAAQ,EAAEnG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmH,OAAO,CAAC,EAAE;gBAAEd,kBAAkB,EAAEN,YAAY,GAAGM,kBAAkB,GAAG,KAAK;cAAE,CAAC,CAAC,EAAED,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,UAAUgC,MAAM,EAAE;gBAC/L,IAAIlH,qBAAqB,CAACkH,MAAM,CAAC,IAAInB,WAAW,KAAK,MAAM,EAAE;kBACzD,MAAM,IAAIpF,WAAW,CAAC;oBAClBwG,aAAa,EAAElH,0BAA0B,CAACiH,MAAM;kBACpD,CAAC,CAAC;gBACN;gBACA,IAAItC,kBAAkB,EAAE;kBACpBA,kBAAkB,CAAC+B,OAAO,GAAG,KAAK;kBAClC/B,kBAAkB,CAACR,KAAK,GAAG,IAAI;gBACnC;gBACA,IAAIgD,WAAW,GAAGtI,QAAQ,CAAC,CAAC,CAAC,EAAEoI,MAAM,CAAC;gBACtC,IAAI,OAAO5B,cAAc,KAAK,UAAU,EAAE;kBACtCA,cAAc,GAAGA,cAAc,CAAC8B,WAAW,CAAC;gBAChD;gBACA,IAAIrB,WAAW,KAAK,QAAQ,IAAI/F,qBAAqB,CAACoH,WAAW,CAAC,EAAE;kBAChE,OAAOA,WAAW,CAACC,MAAM;gBAC7B;gBACA,OAAOvC,IAAI,CAACwC,kBAAkB,CAAC;kBAC3B3C,UAAU,EAAEA,UAAU;kBACtBuC,MAAM,EAAEE,WAAW;kBACnBrE,QAAQ,EAAEkC,QAAQ;kBAClBC,SAAS,EAAEA,SAAS;kBACpBW,WAAW,EAAEA,WAAW;kBACxBE,WAAW,EAAEA,WAAW;kBACxBE,OAAO,EAAEA,OAAO;kBAChBP,MAAM,EAAED,iBAAiB;kBACzBL,aAAa,EAAEA,aAAa;kBAC5BI,mBAAmB,EAAEA,mBAAmB;kBACxCF,cAAc,EAAEA,cAAc;kBAC9BiC,gBAAgB,EAAE1C,YAAY,GAAGF,UAAU,GAAG,KAAK,CAAC;kBACpDgB,cAAc,EAAEA,cAAc;kBAC9BK,cAAc,EAAEA;gBACpB,CAAC,CAAC;cACN,CAAC,CAAC,CAACwB,SAAS,CAAC;gBACTC,IAAI,EAAE,SAAAA,CAAUL,WAAW,EAAE;kBACzBtC,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;kBACvB;kBACA;kBACA;kBACA;kBACA;kBACA,IAAI,EAAE,SAAS,IAAIO,WAAW,CAAC,IAAIA,WAAW,CAACM,OAAO,KAAK,KAAK,EAAE;oBAC9DX,OAAO,CAACjI,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsI,WAAW,CAAC,EAAE;sBAAEO,IAAI,EAAE7C,IAAI,CAAC/C,aAAa,CAAC;wBAC/DgB,QAAQ,EAAEkC,QAAQ;wBAClB0C,IAAI,EAAEP,WAAW,CAACO,IAAI;wBACtB9B,WAAW,EAAEA,WAAW;wBACxB+B,EAAE,EAAEjD;sBACR,CAAC;oBAAE,CAAC,CAAC,CAAC;kBACd;gBACJ,CAAC;gBACDP,KAAK,EAAE,SAAAA,CAAUyD,GAAG,EAAE;kBAClB,IAAIjD,kBAAkB,EAAE;oBACpBA,kBAAkB,CAAC+B,OAAO,GAAG,KAAK;oBAClC/B,kBAAkB,CAACR,KAAK,GAAGyD,GAAG;kBAClC;kBACA,IAAIhD,YAAY,EAAE;oBACdC,IAAI,CAAC9B,KAAK,CAACuE,gBAAgB,CAAC5C,UAAU,CAAC;kBAC3C;kBACAG,IAAI,CAAC+B,gBAAgB,CAAC,CAAC;kBACvBG,MAAM,CAACa,GAAG,YAAYlH,WAAW,GAAGkH,GAAG,GAAI,IAAIlH,WAAW,CAAC;oBACvDmH,YAAY,EAAED;kBAClB,CAAC,CAAE,CAAC;gBACR;cACJ,CAAC,CAAC;YACN,CAAC,CAAC,CAAC;QACf;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD7F,YAAY,CAACR,SAAS,CAAC8F,kBAAkB,GAAG,UAAUrC,QAAQ,EAAEjC,KAAK,EAAE;IACnE,IAAId,KAAK,GAAG,IAAI;IAChB,IAAIc,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI,CAACA,KAAK;IAAE;IAC5C,IAAIkE,MAAM,GAAGjC,QAAQ,CAACiC,MAAM;IAC5B,IAAIa,WAAW,GAAG,EAAE;IACpB,IAAIC,SAAS,GAAG/C,QAAQ,CAACY,WAAW,KAAK,UAAU;IACnD,IAAI,CAACmC,SAAS,IAAI7G,iBAAiB,CAAC+F,MAAM,EAAEjC,QAAQ,CAACc,WAAW,CAAC,EAAE;MAC/D,IAAI,CAACxG,iCAAiC,CAAC2H,MAAM,CAAC,EAAE;QAC5Ca,WAAW,CAACE,IAAI,CAAC;UACbf,MAAM,EAAEA,MAAM,CAACS,IAAI;UACnBO,MAAM,EAAE,eAAe;UACvBC,KAAK,EAAElD,QAAQ,CAAClC,QAAQ;UACxBmC,SAAS,EAAED,QAAQ,CAACC;QACxB,CAAC,CAAC;MACN;MACA,IAAI3F,iCAAiC,CAAC2H,MAAM,CAAC,IACzC9G,eAAe,CAAC8G,MAAM,CAACkB,WAAW,CAAC,EAAE;QACrC,IAAIC,IAAI,GAAGrF,KAAK,CAACqF,IAAI,CAAC;UAClBT,EAAE,EAAE,eAAe;UACnB;UACA;UACA;UACAO,KAAK,EAAE,IAAI,CAAC5B,eAAe,CAACtB,QAAQ,CAAClC,QAAQ,CAAC,CAACuF,OAAO;UACtDpD,SAAS,EAAED,QAAQ,CAACC,SAAS;UAC7BqD,UAAU,EAAE,KAAK;UACjBC,iBAAiB,EAAE;QACvB,CAAC,CAAC;QACF,IAAIC,UAAU,GAAG,KAAK,CAAC;QACvB,IAAIJ,IAAI,CAACnB,MAAM,EAAE;UACbuB,UAAU,GAAG/H,oBAAoB,CAAC2H,IAAI,CAACnB,MAAM,EAAEA,MAAM,CAAC;QAC1D;QACA,IAAI,OAAOuB,UAAU,KAAK,WAAW,EAAE;UACnC;UACA;UACAvB,MAAM,CAACS,IAAI,GAAGc,UAAU;UACxBV,WAAW,CAACE,IAAI,CAAC;YACbf,MAAM,EAAEuB,UAAU;YAClBP,MAAM,EAAE,eAAe;YACvBC,KAAK,EAAElD,QAAQ,CAAClC,QAAQ;YACxBmC,SAAS,EAAED,QAAQ,CAACC;UACxB,CAAC,CAAC;QACN;MACJ;MACA,IAAIwD,eAAe,GAAGzD,QAAQ,CAACG,aAAa;MAC5C,IAAIsD,eAAe,EAAE;QACjB,IAAI,CAACtG,OAAO,CAAC2B,OAAO,CAAC,UAAUS,EAAE,EAAEP,OAAO,EAAE;UACxC,IAAI0E,eAAe,GAAGnE,EAAE,CAACmE,eAAe;UACxC,IAAIC,SAAS,GAAGD,eAAe,IAAIA,eAAe,CAACC,SAAS;UAC5D,IAAI,CAACA,SAAS,IAAI,CAACtH,cAAc,CAACuH,IAAI,CAACH,eAAe,EAAEE,SAAS,CAAC,EAAE;YAChE;UACJ;UACA,IAAIE,OAAO,GAAGJ,eAAe,CAACE,SAAS,CAAC;UACxC,IAAIlE,EAAE,GAAGxC,KAAK,CAACE,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC;YAAElB,QAAQ,GAAG2B,EAAE,CAAC3B,QAAQ;YAAEmC,SAAS,GAAGR,EAAE,CAACQ,SAAS;UACrF;UACA,IAAIH,EAAE,GAAG/B,KAAK,CAACqF,IAAI,CAAC;cAChBF,KAAK,EAAEpF,QAAQ;cACfmC,SAAS,EAAEA,SAAS;cACpBsD,iBAAiB,EAAE,IAAI;cACvBD,UAAU,EAAE;YAChB,CAAC,CAAC;YAAES,kBAAkB,GAAGjE,EAAE,CAACmC,MAAM;YAAE+B,QAAQ,GAAGlE,EAAE,CAACkE,QAAQ;UAC1D,IAAIA,QAAQ,IAAID,kBAAkB,EAAE;YAChC;YACA,IAAIE,eAAe,GAAGJ,OAAO,CAACE,kBAAkB,EAAE;cAC9CG,cAAc,EAAEjC,MAAM;cACtB0B,SAAS,EAAG7F,QAAQ,IAAIjD,gBAAgB,CAACiD,QAAQ,CAAC,IAAK,KAAK,CAAC;cAC7DqG,cAAc,EAAElE;YACpB,CAAC,CAAC;YACF;YACA,IAAIgE,eAAe,EAAE;cACjBnB,WAAW,CAACE,IAAI,CAAC;gBACbf,MAAM,EAAEgC,eAAe;gBACvBhB,MAAM,EAAE,YAAY;gBACpBC,KAAK,EAAEpF,QAAQ;gBACfmC,SAAS,EAAEA;cACf,CAAC,CAAC;YACN;UACJ;QACJ,CAAC,CAAC;MACN;IACJ;IACA,IAAI6C,WAAW,CAACsB,MAAM,GAAG,CAAC,IACtB,CAACpE,QAAQ,CAACK,cAAc,IAAI,EAAE,EAAE+D,MAAM,GAAG,CAAC,IAC1CpE,QAAQ,CAACS,MAAM,IACfT,QAAQ,CAACU,cAAc,IACvBV,QAAQ,CAACsC,gBAAgB,EAAE;MAC3B,IAAI+B,SAAS,GAAG,EAAE;MAClB,IAAI,CAAChE,cAAc,CAAC;QAChBiE,WAAW,EAAE,SAAAA,CAAUvG,KAAK,EAAE;UAC1B,IAAI,CAACgF,SAAS,EAAE;YACZD,WAAW,CAAChE,OAAO,CAAC,UAAUyF,KAAK,EAAE;cAAE,OAAOxG,KAAK,CAACwG,KAAK,CAACA,KAAK,CAAC;YAAE,CAAC,CAAC;UACxE;UACA;UACA;UACA;UACA,IAAI9D,MAAM,GAAGT,QAAQ,CAACS,MAAM;UAC5B;UACA;UACA,IAAI+D,aAAa,GAAG,CAACjK,sBAAsB,CAAC0H,MAAM,CAAC,IAC9C3H,iCAAiC,CAAC2H,MAAM,CAAC,IAAI,CAACA,MAAM,CAACQ,OAAQ;UAClE,IAAIhC,MAAM,EAAE;YACR,IAAI,CAACsC,SAAS,EAAE;cACZ;cACA;cACA;cACA;cACA,IAAIK,IAAI,GAAGrF,KAAK,CAACqF,IAAI,CAAC;gBAClBT,EAAE,EAAE,eAAe;gBACnB;gBACA;gBACA;gBACAO,KAAK,EAAEjG,KAAK,CAACqE,eAAe,CAACtB,QAAQ,CAAClC,QAAQ,CAAC,CAACuF,OAAO;gBACvDpD,SAAS,EAAED,QAAQ,CAACC,SAAS;gBAC7BqD,UAAU,EAAE,KAAK;gBACjBC,iBAAiB,EAAE;cACvB,CAAC,CAAC;cACF,IAAIH,IAAI,CAACY,QAAQ,EAAE;gBACf/B,MAAM,GAAGpI,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoI,MAAM,CAAC,EAAE;kBAAES,IAAI,EAAEU,IAAI,CAACnB;gBAAO,CAAC,CAAC;gBAC9D,IAAI,aAAa,IAAIA,MAAM,EAAE;kBACzB,OAAOA,MAAM,CAACkB,WAAW;gBAC7B;gBACA,IAAI,SAAS,IAAIlB,MAAM,EAAE;kBACrB,OAAOA,MAAM,CAACQ,OAAO;gBACzB;cACJ;YACJ;YACA;YACA;YACA;YACA,IAAI+B,aAAa,EAAE;cACf/D,MAAM,CAAC1C,KAAK,EAAEkE,MAAM,EAAE;gBAClBjB,OAAO,EAAEhB,QAAQ,CAACgB,OAAO;gBACzBf,SAAS,EAAED,QAAQ,CAACC;cACxB,CAAC,CAAC;YACN;UACJ;UACA;UACA;UACA,IAAI,CAAC8C,SAAS,IAAI,CAAC/C,QAAQ,CAACe,cAAc,IAAIyD,aAAa,EAAE;YACzDzG,KAAK,CAAC0G,MAAM,CAAC;cACT9B,EAAE,EAAE,eAAe;cACnB+B,MAAM,EAAE,SAAAA,CAAUC,KAAK,EAAEpF,EAAE,EAAE;gBACzB,IAAIqF,SAAS,GAAGrF,EAAE,CAACqF,SAAS;kBAAEC,MAAM,GAAGtF,EAAE,CAACsF,MAAM;gBAChD,OAAOD,SAAS,KAAK,YAAY,GAAGD,KAAK,GAAGE,MAAM;cACtD;YACJ,CAAC,CAAC;UACN;QACJ,CAAC;QACDC,OAAO,EAAE9E,QAAQ,CAACK,cAAc;QAChC;QACAiD,UAAU,EAAE,KAAK;QACjB;QACA;QACAhB,gBAAgB,EAAEtC,QAAQ,CAACsC,gBAAgB;QAC3C;QACA;QACA;QACA;QACA5B,cAAc,EAAEV,QAAQ,CAACU,cAAc,IAAI;MAC/C,CAAC,CAAC,CAAC5B,OAAO,CAAC,UAAUmD,MAAM,EAAE;QAAE,OAAOoC,SAAS,CAACrB,IAAI,CAACf,MAAM,CAAC;MAAE,CAAC,CAAC;MAChE,IAAIjC,QAAQ,CAACO,mBAAmB,IAAIP,QAAQ,CAACU,cAAc,EAAE;QACzD;QACA;QACA;QACA,OAAOmB,OAAO,CAACkD,GAAG,CAACV,SAAS,CAAC,CAACW,IAAI,CAAC,YAAY;UAAE,OAAO/C,MAAM;QAAE,CAAC,CAAC;MACtE;IACJ;IACA,OAAOJ,OAAO,CAACC,OAAO,CAACG,MAAM,CAAC;EAClC,CAAC;EACDlF,YAAY,CAACR,SAAS,CAACoF,sBAAsB,GAAG,UAAUzB,kBAAkB,EAAEF,QAAQ,EAAE;IACpF,IAAI/C,KAAK,GAAG,IAAI;IAChB,IAAIyF,IAAI,GAAG,OAAOxC,kBAAkB,KAAK,UAAU,GAC/CA,kBAAkB,CAACF,QAAQ,CAACC,SAAS,EAAE;MAAEzD,MAAM,EAAEA;IAAO,CAAC,CAAC,GACxD0D,kBAAkB;IACxB,IAAIwC,IAAI,KAAKlG,MAAM,EAAE;MACjB,OAAO,KAAK;IAChB;IACA,IAAI,CAACuB,KAAK,CAACkH,2BAA2B,CAAC,UAAUlH,KAAK,EAAE;MACpD,IAAI;QACAd,KAAK,CAACoF,kBAAkB,CAACxI,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmG,QAAQ,CAAC,EAAE;UAAEiC,MAAM,EAAE;YAAES,IAAI,EAAEA;UAAK;QAAE,CAAC,CAAC,EAAE3E,KAAK,CAAC;MACjG,CAAC,CACD,OAAOoB,KAAK,EAAE;QACV+F,UAAU,CAACC,OAAO,KAAK,KAAK,IAAInL,SAAS,CAACmF,KAAK,CAACA,KAAK,CAAC;MAC1D;IACJ,CAAC,EAAEa,QAAQ,CAACN,UAAU,CAAC;IACvB,OAAO,IAAI;EACf,CAAC;EACD3C,YAAY,CAACR,SAAS,CAAC6I,UAAU,GAAG,UAAUpG,OAAO,EAAEhC,OAAO,EAAEqI,aAAa,EAAE;IAC3E,OAAO,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,gBAAgB,CAACvG,OAAO,CAAC,EAAEhC,OAAO,EAAEqI,aAAa,CAAC,CAACG,OAAO,CAACC,OAAO;EAC5G,CAAC;EACD1I,YAAY,CAACR,SAAS,CAACmJ,aAAa,GAAG,YAAY;IAC/C,IAAIC,KAAK,GAAGrJ,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACU,OAAO,CAAC2B,OAAO,CAAC,UAAU8G,IAAI,EAAE5G,OAAO,EAAE;MAC1C2G,KAAK,CAAC3G,OAAO,CAAC,GAAG;QACbiB,SAAS,EAAE2F,IAAI,CAAC3F,SAAS;QACzBoF,aAAa,EAAEO,IAAI,CAACP,aAAa;QACjCxC,YAAY,EAAE+C,IAAI,CAAC/C,YAAY;QAC/BX,aAAa,EAAE0D,IAAI,CAAC1D;MACxB,CAAC;IACL,CAAC,CAAC;IACF,OAAOyD,KAAK;EAChB,CAAC;EACD5I,YAAY,CAACR,SAAS,CAACsJ,WAAW,GAAG,UAAU7G,OAAO,EAAE;IACpD,IAAI8G,SAAS,GAAG,IAAI,CAAC3I,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC;IACzC,IAAI8G,SAAS,EAAE;MACXA,SAAS,CAACjD,YAAY,GAAGkD,SAAS;MAClCD,SAAS,CAAC5D,aAAa,GAAG,EAAE;IAChC;EACJ,CAAC;EACDnF,YAAY,CAACR,SAAS,CAAC8E,SAAS,GAAG,UAAUvD,QAAQ,EAAE;IACnD,OAAO,IAAI,CAACU,iBAAiB,CAACR,iBAAiB,CAACF,QAAQ,CAAC;EAC7D,CAAC;EACDf,YAAY,CAACR,SAAS,CAAC+E,eAAe,GAAG,UAAUxD,QAAQ,EAAE;IACzD,IAAIR,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI,CAACA,cAAc,CAAC0I,GAAG,CAAClI,QAAQ,CAAC,EAAE;MAC/B,IAAImI,UAAU,GAAG;QACb;QACA;QACA;QACA;QACA;QACAnL,gBAAgB,EAAEA,gBAAgB,CAACgD,QAAQ,CAAC;QAC5CoI,kBAAkB,EAAE,IAAI,CAAC9H,UAAU,CAAC+H,oBAAoB,CAACrI,QAAQ,CAAC;QAClEsI,uBAAuB,EAAE/L,aAAa,CAAC,CAAC,aAAa,CAAC,EAAEyD,QAAQ,CAAC;QACjEuI,gBAAgB,EAAEjM,8BAA8B,CAAC0D,QAAQ,CAAC;QAC1DwI,WAAW,EAAE,IAAI,CAAClI,UAAU,CAACkI,WAAW,CAACxI,QAAQ,CAAC;QAClDyI,WAAW,EAAE9L,4BAA4B,CAAC,CACtC;UAAE+L,IAAI,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAK,CAAC,EAChC;UAAED,IAAI,EAAE;QAAa,CAAC,EACtB;UAAEA,IAAI,EAAE;QAAc,CAAC,EACvB;UAAEA,IAAI,EAAE;QAAS,CAAC,CACrB,EAAE1I,QAAQ,CAAC;QACZ4I,WAAW,EAAE/L,gBAAgB,CAACC,sBAAsB,CAACkD,QAAQ,CAAC,CAAC;QAC/D;QACA;QACAuF,OAAO,EAAExJ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiE,QAAQ,CAAC,EAAE;UAAE6I,WAAW,EAAE7I,QAAQ,CAAC6I,WAAW,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;YACzF,IAAIA,GAAG,CAACC,IAAI,KAAK,qBAAqB,IAClCD,GAAG,CAACE,SAAS,KAAK,OAAO,EAAE;cAC3B,OAAOlN,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgN,GAAG,CAAC,EAAE;gBAAEE,SAAS,EAAE;cAAQ,CAAC,CAAC;YAC9D;YACA,OAAOF,GAAG;UACd,CAAC;QAAE,CAAC;MACZ,CAAC;MACDvJ,cAAc,CAAC0J,GAAG,CAAClJ,QAAQ,EAAEmI,UAAU,CAAC;IAC5C;IACA,OAAO3I,cAAc,CAACwG,GAAG,CAAChG,QAAQ,CAAC;EACvC,CAAC;EACDf,YAAY,CAACR,SAAS,CAACgF,YAAY,GAAG,UAAUzD,QAAQ,EAAEmC,SAAS,EAAE;IACjE,OAAOpG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyH,eAAe,CAACxD,QAAQ,CAAC,CAAC4I,WAAW,CAAC,EAAEzG,SAAS,CAAC;EACxF,CAAC;EACDlD,YAAY,CAACR,SAAS,CAAC0K,UAAU,GAAG,UAAUjK,OAAO,EAAE;IACnD,IAAIkG,KAAK,GAAG,IAAI,CAAC7B,SAAS,CAACrE,OAAO,CAACkG,KAAK,CAAC;IACzC;IACA;IACA;IACAlG,OAAO,GAAGnD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmD,OAAO,CAAC,EAAE;MAAEiD,SAAS,EAAE,IAAI,CAACsB,YAAY,CAAC2B,KAAK,EAAElG,OAAO,CAACiD,SAAS;IAAE,CAAC,CAAC;IACrG,IAAI,OAAOjD,OAAO,CAACkK,2BAA2B,KAAK,WAAW,EAAE;MAC5DlK,OAAO,CAACkK,2BAA2B,GAAG,KAAK;IAC/C;IACA,IAAIpB,SAAS,GAAG,IAAI7J,SAAS,CAAC,IAAI,CAAC;IACnC,IAAIkL,UAAU,GAAG,IAAItL,eAAe,CAAC;MACjCuL,YAAY,EAAE,IAAI;MAClBtB,SAAS,EAAEA,SAAS;MACpB9I,OAAO,EAAEA;IACb,CAAC,CAAC;IACFmK,UAAU,CAAC,WAAW,CAAC,GAAGjE,KAAK;IAC/B,IAAI,CAACrH,eAAe,CAAC,oBAAoB,CAAC,CAACwL,QAAQ,CAAC,CAAC,EAAE;MACnD,IAAI,CAAClK,OAAO,CAAC6J,GAAG,CAACG,UAAU,CAACnI,OAAO,EAAE8G,SAAS,CAAC;IACnD;IACA;IACA;IACAA,SAAS,CAACwB,IAAI,CAAC;MACXxJ,QAAQ,EAAEoF,KAAK;MACfQ,eAAe,EAAEyD,UAAU;MAC3BlH,SAAS,EAAEkH,UAAU,CAAClH;IAC1B,CAAC,CAAC;IACF,OAAOkH,UAAU;EACrB,CAAC;EACDpK,YAAY,CAACR,SAAS,CAAC2G,KAAK,GAAG,UAAUlG,OAAO,EAAEgC,OAAO,EAAE;IACvD,IAAI/B,KAAK,GAAG,IAAI;IAChB,IAAI+B,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,IAAI,CAACuI,eAAe,CAAC,CAAC;IAAE;IAC5DvN,SAAS,CAACgD,OAAO,CAACkG,KAAK,EAAE,EAAE,CAAC;IAC5BlJ,SAAS,CAACgD,OAAO,CAACkG,KAAK,CAAC4D,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC;IAChD9M,SAAS,CAAC,CAACgD,OAAO,CAACuG,iBAAiB,EAAE,EAAE,CAAC;IACzCvJ,SAAS,CAAC,CAACgD,OAAO,CAACwK,YAAY,EAAE,EAAE,CAAC;IACpC,IAAItE,KAAK,GAAG,IAAI,CAAC7B,SAAS,CAACrE,OAAO,CAACkG,KAAK,CAAC;IACzC,OAAO,IAAI,CAACkC,UAAU,CAACpG,OAAO,EAAEnF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmD,OAAO,CAAC,EAAE;MAAEkG,KAAK,EAAEA;IAAM,CAAC,CAAC,CAAC,CAC7E8B,IAAI,CAAC,UAAU/C,MAAM,EAAE;MACxB,OAAOA,MAAM,IAAIpI,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoI,MAAM,CAAC,EAAE;QAAES,IAAI,EAAEzF,KAAK,CAACH,aAAa,CAAC;UACpEgB,QAAQ,EAAEoF,KAAK;UACfR,IAAI,EAAET,MAAM,CAACS,IAAI;UACjB9B,WAAW,EAAE5D,OAAO,CAAC4D,WAAW;UAChC+B,EAAE,EAAE3D;QACR,CAAC;MAAE,CAAC,CAAC;IACb,CAAC,CAAC,CACGyI,OAAO,CAAC,YAAY;MAAE,OAAOxK,KAAK,CAACyK,SAAS,CAAC1I,OAAO,CAAC;IAAE,CAAC,CAAC;EAClE,CAAC;EACDjC,YAAY,CAACR,SAAS,CAACgL,eAAe,GAAG,YAAY;IACjD,OAAOI,MAAM,CAAC,IAAI,CAACpK,cAAc,EAAE,CAAC;EACxC,CAAC;EACDR,YAAY,CAACR,SAAS,CAACqL,iBAAiB,GAAG,YAAY;IACnD,OAAO,IAAI,CAACpK,gBAAgB,EAAE;EAClC,CAAC;EACDT,YAAY,CAACR,SAAS,CAAC4E,kBAAkB,GAAG,YAAY;IACpD,OAAOwG,MAAM,CAAC,IAAI,CAAClK,iBAAiB,EAAE,CAAC;EAC3C,CAAC;EACDV,YAAY,CAACR,SAAS,CAACsL,gBAAgB,GAAG,UAAU7I,OAAO,EAAE;IACzD,IAAI,CAAC8I,2BAA2B,CAAC9I,OAAO,CAAC;IACzC,IAAI,CAAC4C,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACD7E,YAAY,CAACR,SAAS,CAACuL,2BAA2B,GAAG,UAAU9I,OAAO,EAAE;IACpE,IAAI8G,SAAS,GAAG,IAAI,CAAC3I,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC;IACzC,IAAI8G,SAAS,EACTA,SAAS,CAACjH,IAAI,CAAC,CAAC;EACxB,CAAC;EACD9B,YAAY,CAACR,SAAS,CAACwL,UAAU,GAAG,UAAU/K,OAAO,EAAE;IACnD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG;QAChCgL,cAAc,EAAE;MACpB,CAAC;IAAE;IACH;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC9I,oBAAoB,CAACjF,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkD,OAAO,CAAC2B,OAAO,CAAC,UAAUgH,SAAS,EAAE;MACtC,IAAIA,SAAS,CAACpC,eAAe,EAAE;QAC3B;QACA;QACAoC,SAAS,CAACT,aAAa,GAAGtJ,aAAa,CAAC2F,OAAO;MACnD,CAAC,MACI;QACDoE,SAAS,CAACjH,IAAI,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACD,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,GAAGtC,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC;IAC5C;IACA;IACA,OAAO,IAAI,CAACsB,KAAK,CAACkK,KAAK,CAACjL,OAAO,CAAC;EACpC,CAAC;EACDD,YAAY,CAACR,SAAS,CAAC2L,oBAAoB,GAAG,UAAUpD,OAAO,EAAE;IAC7D,IAAI7H,KAAK,GAAG,IAAI;IAChB,IAAI6H,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,QAAQ;IAAE;IAC9C,IAAI3H,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAI+K,UAAU,GAAG,IAAI/K,GAAG,CAAC,CAAC;IAC1B,IAAIgL,yBAAyB,GAAG,IAAIhL,GAAG,CAAC,CAAC;IACzC,IAAIiL,kBAAkB,GAAG,IAAIzK,GAAG,CAAC,CAAC;IAClC,IAAI0K,KAAK,CAACC,OAAO,CAACzD,OAAO,CAAC,EAAE;MACxBA,OAAO,CAAChG,OAAO,CAAC,UAAU0J,IAAI,EAAE;QAC5B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1BL,UAAU,CAACnB,GAAG,CAACwB,IAAI,EAAEA,IAAI,CAAC;UAC1BJ,yBAAyB,CAACpB,GAAG,CAACwB,IAAI,EAAE,KAAK,CAAC;QAC9C,CAAC,MACI,IAAIlN,cAAc,CAACkN,IAAI,CAAC,EAAE;UAC3B,IAAIC,WAAW,GAAGrM,KAAK,CAACa,KAAK,CAACoE,SAAS,CAACmH,IAAI,CAAC,CAAC;UAC9CL,UAAU,CAACnB,GAAG,CAACyB,WAAW,EAAE5N,gBAAgB,CAAC2N,IAAI,CAAC,CAAC;UACnDJ,yBAAyB,CAACpB,GAAG,CAACyB,WAAW,EAAE,KAAK,CAAC;QACrD,CAAC,MACI,IAAIlN,eAAe,CAACiN,IAAI,CAAC,IAAIA,IAAI,CAACtF,KAAK,EAAE;UAC1CmF,kBAAkB,CAACK,GAAG,CAACF,IAAI,CAAC;QAChC;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACrL,OAAO,CAAC2B,OAAO,CAAC,UAAUS,EAAE,EAAEP,OAAO,EAAE;MACxC,IAAI2J,EAAE,GAAGpJ,EAAE,CAACmE,eAAe;QAAE5F,QAAQ,GAAGyB,EAAE,CAACzB,QAAQ;MACnD,IAAI6K,EAAE,EAAE;QACJ,IAAI7D,OAAO,KAAK,KAAK,EAAE;UACnB3H,OAAO,CAAC6J,GAAG,CAAChI,OAAO,EAAE2J,EAAE,CAAC;UACxB;QACJ;QACA,IAAIhF,SAAS,GAAGgF,EAAE,CAAChF,SAAS;UAAE/C,WAAW,GAAG+H,EAAE,CAAC3L,OAAO,CAAC4D,WAAW;QAClE,IAAIA,WAAW,KAAK,SAAS,IACxBkE,OAAO,KAAK,QAAQ,IAAI,CAAC6D,EAAE,CAACC,YAAY,CAAC,CAAE,EAAE;UAC9C;QACJ;QACA,IAAI9D,OAAO,KAAK,QAAQ,IACnBnB,SAAS,IAAIyE,yBAAyB,CAACpC,GAAG,CAACrC,SAAS,CAAE,IACtD7F,QAAQ,IAAIsK,yBAAyB,CAACpC,GAAG,CAAC5J,KAAK,CAAC0B,QAAQ,CAAC,CAAE,EAAE;UAC9DX,OAAO,CAAC6J,GAAG,CAAChI,OAAO,EAAE2J,EAAE,CAAC;UACxB,IAAIhF,SAAS,EACTyE,yBAAyB,CAACpB,GAAG,CAACrD,SAAS,EAAE,IAAI,CAAC;UAClD,IAAI7F,QAAQ,EACRsK,yBAAyB,CAACpB,GAAG,CAAC5K,KAAK,CAAC0B,QAAQ,CAAC,EAAE,IAAI,CAAC;QAC5D;MACJ;IACJ,CAAC,CAAC;IACF,IAAIuK,kBAAkB,CAACQ,IAAI,EAAE;MACzBR,kBAAkB,CAACvJ,OAAO,CAAC,UAAU9B,OAAO,EAAE;QAC1C;QACA;QACA;QACA,IAAIgC,OAAO,GAAG3D,YAAY,CAAC,oBAAoB,CAAC;QAChD,IAAIyK,SAAS,GAAG7I,KAAK,CAACsI,gBAAgB,CAACvG,OAAO,CAAC,CAACsI,IAAI,CAAC;UACjDxJ,QAAQ,EAAEd,OAAO,CAACkG,KAAK;UACvBjD,SAAS,EAAEjD,OAAO,CAACiD;QACvB,CAAC,CAAC;QACF,IAAI0I,EAAE,GAAG,IAAI9M,eAAe,CAAC;UACzBuL,YAAY,EAAEnK,KAAK;UACnB6I,SAAS,EAAEA,SAAS;UACpB9I,OAAO,EAAEnD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmD,OAAO,CAAC,EAAE;YAAE4D,WAAW,EAAE;UAAe,CAAC;QAC5E,CAAC,CAAC;QACF5G,SAAS,CAAC2O,EAAE,CAAC3J,OAAO,KAAKA,OAAO,CAAC;QACjC8G,SAAS,CAACgD,kBAAkB,CAACH,EAAE,CAAC;QAChCxL,OAAO,CAAC6J,GAAG,CAAChI,OAAO,EAAE2J,EAAE,CAAC;MAC5B,CAAC,CAAC;IACN;IACA,IAAIzD,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIiD,yBAAyB,CAACS,IAAI,EAAE;MAChET,yBAAyB,CAACtJ,OAAO,CAAC,UAAUiK,QAAQ,EAAEC,iBAAiB,EAAE;QACrE,IAAI,CAACD,QAAQ,EAAE;UACX,IAAIpF,SAAS,GAAGwE,UAAU,CAACrE,GAAG,CAACkF,iBAAiB,CAAC;UACjD,IAAIrF,SAAS,EAAE;YACXuB,UAAU,CAACC,OAAO,KAAK,KAAK,IAAInL,SAAS,CAACiP,IAAI,CAAC,EAAE,EAAEtF,SAAS,CAAC;UACjE,CAAC,MACI;YACDuB,UAAU,CAACC,OAAO,KAAK,KAAK,IAAInL,SAAS,CAACiP,IAAI,CAAC,EAAE,CAAC;UACtD;QACJ;MACJ,CAAC,CAAC;IACN;IACA,OAAO9L,OAAO;EAClB,CAAC;EACDJ,YAAY,CAACR,SAAS,CAAC2M,wBAAwB,GAAG,UAAUC,cAAc,EAAE;IACxE,IAAIlM,KAAK,GAAG,IAAI;IAChB,IAAIkM,cAAc,KAAK,KAAK,CAAC,EAAE;MAAEA,cAAc,GAAG,KAAK;IAAE;IACzD,IAAIC,uBAAuB,GAAG,EAAE;IAChC,IAAI,CAAClB,oBAAoB,CAACiB,cAAc,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACrK,OAAO,CAAC,UAAU4E,eAAe,EAAE1E,OAAO,EAAE;MACrG,IAAI4B,WAAW,GAAG8C,eAAe,CAAC1G,OAAO,CAAC4D,WAAW;MACrD8C,eAAe,CAAC2F,gBAAgB,CAAC,CAAC;MAClC,IAAIF,cAAc,IACbvI,WAAW,KAAK,SAAS,IAAIA,WAAW,KAAK,YAAa,EAAE;QAC7DwI,uBAAuB,CAACpG,IAAI,CAACU,eAAe,CAAC4F,OAAO,CAAC,CAAC,CAAC;MAC3D;MACA,CAACrM,KAAK,CAACE,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC,IAAI0E,eAAe,CAAC,WAAW,CAAC,EAAE6F,OAAO,CAAC,IAAI,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAAC3H,gBAAgB,CAAC,CAAC;IACvB,OAAOC,OAAO,CAACkD,GAAG,CAACqE,uBAAuB,CAAC;EAC/C,CAAC;EACDrM,YAAY,CAACR,SAAS,CAACiN,wBAAwB,GAAG,UAAUxM,OAAO,EAAE;IACjE,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIiG,KAAK,GAAGlG,OAAO,CAACkG,KAAK;MAAEjD,SAAS,GAAGjD,OAAO,CAACiD,SAAS;IACxD,IAAIW,WAAW,GAAG5D,OAAO,CAAC4D,WAAW;MAAErB,EAAE,GAAGvC,OAAO,CAAC8D,WAAW;MAAEA,WAAW,GAAGvB,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,EAAE;MAAEE,EAAE,GAAGzC,OAAO,CAACgE,OAAO;MAAEA,OAAO,GAAGvB,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;MAAEK,EAAE,GAAG9C,OAAO,CAACyM,UAAU;MAAEA,UAAU,GAAG3J,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;IAClOoD,KAAK,GAAG,IAAI,CAAC7B,SAAS,CAAC6B,KAAK,CAAC;IAC7BjD,SAAS,GAAG,IAAI,CAACsB,YAAY,CAAC2B,KAAK,EAAEjD,SAAS,CAAC;IAC/C,IAAIyJ,cAAc,GAAG,SAAAA,CAAUzJ,SAAS,EAAE;MACtC,OAAOhD,KAAK,CAAC+E,qBAAqB,CAACkB,KAAK,EAAElC,OAAO,EAAEf,SAAS,EAAEwJ,UAAU,CAAC,CAAC7C,GAAG,CAAC,UAAU3E,MAAM,EAAE;QAC5F,IAAIrB,WAAW,KAAK,UAAU,EAAE;UAC5B;UACA;UACA,IAAI1E,iBAAiB,CAAC+F,MAAM,EAAEnB,WAAW,CAAC,EAAE;YACxC7D,KAAK,CAACc,KAAK,CAACwG,KAAK,CAAC;cACdrB,KAAK,EAAEA,KAAK;cACZjB,MAAM,EAAEA,MAAM,CAACS,IAAI;cACnBO,MAAM,EAAE,mBAAmB;cAC3BhD,SAAS,EAAEA;YACf,CAAC,CAAC;UACN;UACAhD,KAAK,CAAC2E,gBAAgB,CAAC,CAAC;QAC5B;QACA,IAAI+H,SAAS,GAAG5O,qBAAqB,CAACkH,MAAM,CAAC;QAC7C,IAAI2H,iBAAiB,GAAGhO,8BAA8B,CAACqG,MAAM,CAAC;QAC9D,IAAI0H,SAAS,IAAIC,iBAAiB,EAAE;UAChC,IAAIxH,MAAM,GAAG,CAAC,CAAC;UACf,IAAIuH,SAAS,EAAE;YACXvH,MAAM,CAACF,aAAa,GAAGD,MAAM,CAACG,MAAM;UACxC;UACA,IAAIwH,iBAAiB,EAAE;YACnBxH,MAAM,CAACyH,cAAc,GAAG5H,MAAM,CAACwH,UAAU,CAACtN,sBAAsB,CAAC;UACrE;UACA;UACA;UACA;UACA,IAAI2E,WAAW,KAAK,MAAM,IAAI8I,iBAAiB,EAAE;YAC7C,MAAM,IAAIlO,WAAW,CAAC0G,MAAM,CAAC;UACjC;QACJ;QACA,IAAItB,WAAW,KAAK,QAAQ,EAAE;UAC1B,OAAOmB,MAAM,CAACG,MAAM;QACxB;QACA,OAAOH,MAAM;MACjB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,IAAI,CAACX,eAAe,CAAC4B,KAAK,CAAC,CAACpI,gBAAgB,EAAE;MAC9C,IAAIgP,mBAAmB,GAAG,IAAI,CAAC1L,UAAU,CACpCoD,oBAAoB,CAAC0B,KAAK,EAAEjD,SAAS,EAAEe,OAAO,CAAC,CAC/CgE,IAAI,CAAC0E,cAAc,CAAC;MACzB,OAAO,IAAIzO,UAAU,CAAC,UAAU8O,QAAQ,EAAE;QACtC,IAAIC,GAAG,GAAG,IAAI;QACdF,mBAAmB,CAAC9E,IAAI,CAAC,UAAUmC,UAAU,EAAE;UAAE,OAAQ6C,GAAG,GAAG7C,UAAU,CAAC5E,SAAS,CAACwH,QAAQ,CAAC;QAAG,CAAC,EAAEA,QAAQ,CAAC5K,KAAK,CAAC;QAClH,OAAO,YAAY;UAAE,OAAO6K,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC;QAAE,CAAC;MAC3D,CAAC,CAAC;IACN;IACA,OAAOP,cAAc,CAACzJ,SAAS,CAAC;EACpC,CAAC;EACDlD,YAAY,CAACR,SAAS,CAACmL,SAAS,GAAG,UAAU1I,OAAO,EAAE;IAClD,IAAI,CAACC,oBAAoB,CAACD,OAAO,CAAC;IAClC,IAAI,CAAC4C,gBAAgB,CAAC,CAAC;EAC3B,CAAC;EACD7E,YAAY,CAACR,SAAS,CAAC0C,oBAAoB,GAAG,UAAUD,OAAO,EAAE;IAC7D,IAAI,CAAC8I,2BAA2B,CAAC9I,OAAO,CAAC;IACzC,IAAI,CAACkL,WAAW,CAAClL,OAAO,CAAC;EAC7B,CAAC;EACDjC,YAAY,CAACR,SAAS,CAAC2N,WAAW,GAAG,UAAUlL,OAAO,EAAE;IACpD,IAAIO,EAAE;IACN;IACA;IACA;IACA;IACA;IACA,IAAI,CAAClC,cAAc,CAAC8M,MAAM,CAACnL,OAAO,CAAC;IACnC,IAAI,IAAI,CAAC7B,OAAO,CAAC6I,GAAG,CAAChH,OAAO,CAAC,EAAE;MAC3B,CAACO,EAAE,GAAG,IAAI,CAACpC,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACV,IAAI,CAAC,CAAC;MAC/E,IAAI,CAAC1B,OAAO,CAACgN,MAAM,CAACnL,OAAO,CAAC;IAChC;EACJ,CAAC;EACDjC,YAAY,CAACR,SAAS,CAACqF,gBAAgB,GAAG,YAAY;IAClD,IAAI,IAAI,CAACjD,WAAW,EAChB,IAAI,CAACA,WAAW,CAAC,CAAC;IACtB,IAAI,CAACxB,OAAO,CAAC2B,OAAO,CAAC,UAAU8G,IAAI,EAAE;MAAE,IAAIrG,EAAE;MAAE,OAAO,CAACA,EAAE,GAAGqG,IAAI,CAAClC,eAAe,MAAM,IAAI,IAAInE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAC7I,CAAC;EACDxC,YAAY,CAACR,SAAS,CAAC6N,aAAa,GAAG,YAAY;IAC/C,OAAO,IAAI,CAAChM,UAAU;EAC1B,CAAC;EACDrB,YAAY,CAACR,SAAS,CAACyF,qBAAqB,GAAG,UAAUkB,KAAK,EAAElC,OAAO,EAAEf,SAAS,EAAEwJ,UAAU;EAC9F;EACAY,aAAa,EAAE;IACX,IAAIpN,KAAK,GAAG,IAAI;IAChB,IAAIsC,EAAE;IACN,IAAI8K,aAAa,KAAK,KAAK,CAAC,EAAE;MAAEA,aAAa,GAAG,CAAC9K,EAAE,GAAGyB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC7C,kBAAkB,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACpB,kBAAkB;IAAE;IAC9L,IAAIgJ,UAAU;IACd,IAAI1H,EAAE,GAAG,IAAI,CAAC6B,eAAe,CAAC4B,KAAK,CAAC;MAAEqD,WAAW,GAAG9G,EAAE,CAAC8G,WAAW;MAAED,WAAW,GAAG7G,EAAE,CAAC6G,WAAW;IAChG,IAAIC,WAAW,EAAE;MACb,IAAIzG,EAAE,GAAG,IAAI;QAAEwK,yBAAyB,GAAGxK,EAAE,CAACpC,uBAAuB;QAAEO,IAAI,GAAG6B,EAAE,CAAC7B,IAAI;MACrF,IAAI8I,SAAS,GAAG;QACZ7D,KAAK,EAAEqD,WAAW;QAClBtG,SAAS,EAAEA,SAAS;QACpBsK,aAAa,EAAE1P,gBAAgB,CAAC0L,WAAW,CAAC,IAAI,KAAK,CAAC;QACtDvF,OAAO,EAAE,IAAI,CAACwJ,cAAc,CAAC3Q,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmH,OAAO,CAAC,EAAE;UAAEyJ,UAAU,EAAE,CAACJ;QAAc,CAAC,CAAC,CAAC;QAC7FZ,UAAU,EAAEA;MAChB,CAAC;MACDzI,OAAO,GAAG+F,SAAS,CAAC/F,OAAO;MAC3B,IAAIqJ,aAAa,EAAE;QACf,IAAIK,oBAAoB,GAAGtO,KAAK,CAACmK,WAAW,CAAC;QAC7C,IAAIoE,SAAS,GAAGjQ,kBAAkB,CAACuF,SAAS,CAAC;QAC7C,IAAI2K,KAAK,GAAGN,yBAAyB,CAACO,MAAM,CAACH,oBAAoB,EAAEC,SAAS,CAAC;QAC7ExD,UAAU,GAAGyD,KAAK,CAACzD,UAAU;QAC7B,IAAI,CAACA,UAAU,EAAE;UACb,IAAI2D,SAAS,GAAG,IAAI1P,OAAO,CAAC,CACxBjB,OAAO,CAAC8D,IAAI,EAAE8I,SAAS,CAAC,CAC3B,CAAC;UACFI,UAAU,GAAGyD,KAAK,CAACzD,UAAU,GAAG2D,SAAS;UACzCA,SAAS,CAACC,UAAU,CAAC,SAASC,EAAEA,CAACC,MAAM,EAAEC,GAAG,EAAE;YAC1C,IAAID,MAAM,KAAK,MAAM,IAAI,SAAS,IAAIC,GAAG,IAAIA,GAAG,CAACzI,OAAO,EAAE;cACtDqI,SAAS,CAACC,UAAU,CAACC,EAAE,CAAC;YAC5B,CAAC,MACI;cACDV,yBAAyB,CAAC7D,MAAM,CAACiE,oBAAoB,EAAEC,SAAS,CAAC;YACrE;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACDxD,UAAU,GAAG,IAAI/L,OAAO,CAAC,CACrBjB,OAAO,CAAC8D,IAAI,EAAE8I,SAAS,CAAC,CAC3B,CAAC;MACN;IACJ,CAAC,MACI;MACDI,UAAU,GAAG,IAAI/L,OAAO,CAAC,CAACH,UAAU,CAACkQ,EAAE,CAAC;QAAEzI,IAAI,EAAE,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;MACvD1B,OAAO,GAAG,IAAI,CAACwJ,cAAc,CAACxJ,OAAO,CAAC;IAC1C;IACA,IAAIsF,WAAW,EAAE;MACba,UAAU,GAAGjM,QAAQ,CAACiM,UAAU,EAAE,UAAUlF,MAAM,EAAE;QAChD,OAAOhF,KAAK,CAACmB,UAAU,CAACgN,YAAY,CAAC;UACjCtN,QAAQ,EAAEwI,WAAW;UACrB+E,YAAY,EAAEpJ,MAAM;UACpBjB,OAAO,EAAEA,OAAO;UAChBf,SAAS,EAAEA;QACf,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,OAAOkH,UAAU;EACrB,CAAC;EACDpK,YAAY,CAACR,SAAS,CAAC+O,kBAAkB,GAAG,UAAUxF,SAAS,EAAEyF,kBAAkB,EAAEvO,OAAO,EAAE;IAC1F,IAAIwO,SAAS,GAAI1F,SAAS,CAAC2F,aAAa,GAAG,IAAI,CAAC7D,iBAAiB,CAAC,CAAE;IACpE;IACA;IACA;IACA,IAAI8D,YAAY,GAAG,IAAI,CAAC3N,KAAK,CAACqD,gBAAgB,CAACpE,OAAO,CAACkG,KAAK,CAAC;IAC7D,OAAOhI,QAAQ,CAAC,IAAI,CAAC8G,qBAAqB,CAAC0J,YAAY,EAAE1O,OAAO,CAACgE,OAAO,EAAEhE,OAAO,CAACiD,SAAS,CAAC,EAAE,UAAUgC,MAAM,EAAE;MAC5G,IAAIC,aAAa,GAAGlH,0BAA0B,CAACiH,MAAM,CAAC;MACtD,IAAI0H,SAAS,GAAGzH,aAAa,CAACkC,MAAM,GAAG,CAAC;MACxC,IAAItD,WAAW,GAAG9D,OAAO,CAAC8D,WAAW;MACrC;MACA;MACA,IAAI0K,SAAS,IAAI1F,SAAS,CAAC2F,aAAa,EAAE;QACtC,IAAI9B,SAAS,IAAI7I,WAAW,KAAK,MAAM,EAAE;UACrC;UACA,MAAMgF,SAAS,CAAC6F,SAAS,CAAC,IAAIjQ,WAAW,CAAC;YACtCwG,aAAa,EAAEA;UACnB,CAAC,CAAC,CAAC;QACP;QACA;QACA;QACA;QACA4D,SAAS,CAAC8F,UAAU,CAAC3J,MAAM,EAAEyJ,YAAY,EAAE1O,OAAO,EAAEuO,kBAAkB,CAAC;QACvEzF,SAAS,CAAC+F,SAAS,CAAC,CAAC;MACzB;MACA,IAAIC,GAAG,GAAG;QACNpJ,IAAI,EAAET,MAAM,CAACS,IAAI;QACjBhB,OAAO,EAAE,KAAK;QACd2D,aAAa,EAAEtJ,aAAa,CAACgQ;MACjC,CAAC;MACD;MACA;MACA;MACA;MACA,IAAIpC,SAAS,IAAI7I,WAAW,KAAK,MAAM,EAAE;QACrCgL,GAAG,CAACpJ,IAAI,GAAG,KAAK,CAAC;MACrB;MACA,IAAIiH,SAAS,IAAI7I,WAAW,KAAK,QAAQ,EAAE;QACvCgL,GAAG,CAAC1J,MAAM,GAAGF,aAAa;QAC1B4J,GAAG,CAACzG,aAAa,GAAGtJ,aAAa,CAACoD,KAAK;MAC3C;MACA,OAAO2M,GAAG;IACd,CAAC,EAAE,UAAUjJ,YAAY,EAAE;MACvB,IAAI1D,KAAK,GAAGxD,aAAa,CAACkH,YAAY,CAAC,GAAGA,YAAY,GAAI,IAAInH,WAAW,CAAC;QAAEmH,YAAY,EAAEA;MAAa,CAAC,CAAE;MAC1G;MACA,IAAI2I,SAAS,IAAI1F,SAAS,CAAC2F,aAAa,EAAE;QACtC3F,SAAS,CAAC6F,SAAS,CAACxM,KAAK,CAAC;MAC9B;MACA,MAAMA,KAAK;IACf,CAAC,CAAC;EACN,CAAC;EACDpC,YAAY,CAACR,SAAS,CAAC+I,oBAAoB,GAAG,UAAUQ,SAAS,EAAE9I,OAAO;EAC1E;EACA;EACA;EACAqI,aAAa,EAAEnC,KAAK,EAAE;IAClB,IAAIjG,KAAK,GAAG,IAAI;IAChB,IAAIoI,aAAa,KAAK,KAAK,CAAC,EAAE;MAAEA,aAAa,GAAGtJ,aAAa,CAAC2F,OAAO;IAAE;IACvE,IAAIwB,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAGlG,OAAO,CAACkG,KAAK;IAAE;IAC/C,IAAIjD,SAAS,GAAG,IAAI,CAACsB,YAAY,CAAC2B,KAAK,EAAElG,OAAO,CAACiD,SAAS,CAAC;IAC3D,IAAI+L,QAAQ,GAAG,IAAI,CAAC9N,cAAc,CAAC+I,UAAU;IAC7C,IAAI1H,EAAE,GAAGvC,OAAO,CAAC4D,WAAW;MAAEA,WAAW,GAAGrB,EAAE,KAAK,KAAK,CAAC,GAAIyM,QAAQ,IAAIA,QAAQ,CAACpL,WAAW,IAAK,aAAa,GAAGrB,EAAE;MAAEE,EAAE,GAAGzC,OAAO,CAAC8D,WAAW;MAAEA,WAAW,GAAGrB,EAAE,KAAK,KAAK,CAAC,GAAIuM,QAAQ,IAAIA,QAAQ,CAAClL,WAAW,IAAK,MAAM,GAAGrB,EAAE;MAAEK,EAAE,GAAG9C,OAAO,CAACuG,iBAAiB;MAAEA,iBAAiB,GAAGzD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEC,EAAE,GAAG/C,OAAO,CAACkK,2BAA2B;MAAEA,2BAA2B,GAAGnH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEK,EAAE,GAAGpD,OAAO,CAACgE,OAAO;MAAEA,OAAO,GAAGZ,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;IAC5c,IAAI6L,UAAU,GAAG3P,MAAM,CAAC4P,MAAM,CAAC,CAAC,CAAC,EAAElP,OAAO,EAAE;MACxCkG,KAAK,EAAEA,KAAK;MACZjD,SAAS,EAAEA,SAAS;MACpBW,WAAW,EAAEA,WAAW;MACxBE,WAAW,EAAEA,WAAW;MACxByC,iBAAiB,EAAEA,iBAAiB;MACpC2D,2BAA2B,EAAEA,2BAA2B;MACxDlG,OAAO,EAAEA;IACb,CAAC,CAAC;IACF,IAAImL,aAAa,GAAG,SAAAA,CAAUlM,SAAS,EAAE;MACrC;MACA;MACA;MACAgM,UAAU,CAAChM,SAAS,GAAGA,SAAS;MAChC,IAAImM,eAAe,GAAGnP,KAAK,CAACoP,kBAAkB,CAACvG,SAAS,EAAEmG,UAAU,EAAE5G,aAAa,CAAC;MACpF;MACA;MACA;MACA4G,UAAU,CAACrL,WAAW,KAAK,SAAS;MAChC;MACA;MACAwL,eAAe,CAACE,OAAO,CAAClI,MAAM,GAAG,CAAC,IAClC0B,SAAS,CAACpC,eAAe,EAAE;QAC3BoC,SAAS,CAACpC,eAAe,CAAC,sBAAsB,CAAC,CAAC,aAAa,EAAE1G,OAAO,CAAC;MAC7E;MACA,OAAOoP,eAAe;IAC1B,CAAC;IACD;IACA;IACA,IAAIG,eAAe,GAAG,SAAAA,CAAA,EAAY;MAAE,OAAOtP,KAAK,CAACI,cAAc,CAAC8M,MAAM,CAACrE,SAAS,CAAC9G,OAAO,CAAC;IAAE,CAAC;IAC5F,IAAI,CAAC3B,cAAc,CAAC2J,GAAG,CAAClB,SAAS,CAAC9G,OAAO,EAAE,UAAUwN,MAAM,EAAE;MACzDD,eAAe,CAAC,CAAC;MACjB;MACAE,UAAU,CAAC,YAAY;QAAE,OAAOjH,OAAO,CAACpG,MAAM,CAACoN,MAAM,CAAC;MAAE,CAAC,CAAC;IAC9D,CAAC,CAAC;IACF,IAAIhH,OAAO,EAAEkH,oBAAoB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACpL,eAAe,CAAC2K,UAAU,CAAC/I,KAAK,CAAC,CAACpI,gBAAgB,EAAE;MACzD0K,OAAO,GAAG,IAAIpK,OAAO,CAAC,IAAI,CAACgD,UAAU,CAChCoD,oBAAoB,CAACyK,UAAU,CAAC/I,KAAK,EAAE+I,UAAU,CAAChM,SAAS,EAAEgM,UAAU,CAACjL,OAAO,CAAC,CAChFgE,IAAI,CAACmH,aAAa,CAAC,CACnBnH,IAAI,CAAC,UAAUoH,eAAe,EAAE;QAAE,OAAOA,eAAe,CAACE,OAAO;MAAE,CAAC,CAAC,CAAC;MAC1E;MACA;MACA;MACA;MACA;MACAI,oBAAoB,GAAG,IAAI;IAC/B,CAAC,MACI;MACD,IAAIN,eAAe,GAAGD,aAAa,CAACF,UAAU,CAAChM,SAAS,CAAC;MACzDyM,oBAAoB,GAAGN,eAAe,CAACO,QAAQ;MAC/CnH,OAAO,GAAG,IAAIpK,OAAO,CAACgR,eAAe,CAACE,OAAO,CAAC;IAClD;IACA9G,OAAO,CAACC,OAAO,CAACT,IAAI,CAACuH,eAAe,EAAEA,eAAe,CAAC;IACtD,OAAO;MACH/G,OAAO,EAAEA,OAAO;MAChBmH,QAAQ,EAAED;IACd,CAAC;EACL,CAAC;EACD3P,YAAY,CAACR,SAAS,CAAC8D,cAAc,GAAG,UAAUd,EAAE,EAAE;IAClD,IAAItC,KAAK,GAAG,IAAI;IAChB,IAAIqH,WAAW,GAAG/E,EAAE,CAAC+E,WAAW;MAAEQ,OAAO,GAAGvF,EAAE,CAACuF,OAAO;MAAErF,EAAE,GAAGF,EAAE,CAAC+D,UAAU;MAAEA,UAAU,GAAG7D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEK,EAAE,GAAGP,EAAE,CAAC+C,gBAAgB;MAAEA,gBAAgB,GAAGxC,EAAE,KAAK,KAAK,CAAC,GAAGwD,UAAU,GAAGjI,YAAY,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,GAAGyE,EAAE;MAAEY,cAAc,GAAGnB,EAAE,CAACmB,cAAc;IAC/Q,IAAIkM,mBAAmB,GAAG,IAAIxP,GAAG,CAAC,CAAC;IACnC,IAAI0H,OAAO,EAAE;MACT,IAAI,CAACoD,oBAAoB,CAACpD,OAAO,CAAC,CAAChG,OAAO,CAAC,UAAU6J,EAAE,EAAE3J,OAAO,EAAE;QAC9D4N,mBAAmB,CAAC5F,GAAG,CAAChI,OAAO,EAAE;UAC7B2J,EAAE,EAAEA,EAAE;UACNkE,QAAQ,EAAE,CAAC5P,KAAK,CAACE,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC,IAAI2J,EAAE,CAAC,WAAW,CAAC,EAAEmE,OAAO,CAAC;QACtE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAIC,OAAO,GAAG,IAAI3P,GAAG,CAAC,CAAC;IACvB,IAAIkH,WAAW,EAAE;MACb,IAAI,CAACvG,KAAK,CAACiP,KAAK,CAAC;QACbvM,MAAM,EAAE6D,WAAW;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAhB,UAAU,EAAGA,UAAU,IAAIhB,gBAAgB,IAAK,KAAK;QACrD;QACA;QACA;QACA;QACA;QACA;QACA;QACAA,gBAAgB,EAAEA,gBAAgB;QAClC2K,cAAc,EAAE,SAAAA,CAAUC,KAAK,EAAE9J,IAAI,EAAEyJ,QAAQ,EAAE;UAC7C,IAAIlE,EAAE,GAAGuE,KAAK,CAACC,OAAO,YAAYlR,SAAS,IAAIiR,KAAK,CAACC,OAAO,CAACzJ,eAAe;UAC5E,IAAIiF,EAAE,EAAE;YACJ,IAAIjI,cAAc,EAAE;cAChB;cACA;cACA;cACAkM,mBAAmB,CAACzC,MAAM,CAACxB,EAAE,CAAC3J,OAAO,CAAC;cACtC,IAAIiD,MAAM,GAAGvB,cAAc,CAACiI,EAAE,EAAEvF,IAAI,EAAEyJ,QAAQ,CAAC;cAC/C,IAAI5K,MAAM,KAAK,IAAI,EAAE;gBACjB;gBACA;gBACAA,MAAM,GAAG0G,EAAE,CAACW,OAAO,CAAC,CAAC;cACzB;cACA;cACA;cACA,IAAIrH,MAAM,KAAK,KAAK,EAAE;gBAClB8K,OAAO,CAAC/F,GAAG,CAAC2B,EAAE,EAAE1G,MAAM,CAAC;cAC3B;cACA;cACA;cACA,OAAOA,MAAM;YACjB;YACA,IAAIvB,cAAc,KAAK,IAAI,EAAE;cACzB;cACA;cACA;cACAkM,mBAAmB,CAAC5F,GAAG,CAAC2B,EAAE,CAAC3J,OAAO,EAAE;gBAAE2J,EAAE,EAAEA,EAAE;gBAAEkE,QAAQ,EAAEA,QAAQ;gBAAEzJ,IAAI,EAAEA;cAAK,CAAC,CAAC;YACnF;UACJ;QACJ;MACJ,CAAC,CAAC;IACN;IACA,IAAIwJ,mBAAmB,CAAC/D,IAAI,EAAE;MAC1B+D,mBAAmB,CAAC9N,OAAO,CAAC,UAAUS,EAAE,EAAEP,OAAO,EAAE;QAC/C,IAAI2J,EAAE,GAAGpJ,EAAE,CAACoJ,EAAE;UAAEkE,QAAQ,GAAGtN,EAAE,CAACsN,QAAQ;UAAEzJ,IAAI,GAAG7D,EAAE,CAAC6D,IAAI;QACtD,IAAInB,MAAM;QACV;QACA;QACA,IAAIvB,cAAc,EAAE;UAChB,IAAI,CAAC0C,IAAI,EAAE;YACPA,IAAI,GAAGnG,KAAK,CAACc,KAAK,CAACqF,IAAI,CAACuF,EAAE,CAAC,WAAW,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAChE;UACA1G,MAAM,GAAGvB,cAAc,CAACiI,EAAE,EAAEvF,IAAI,EAAEyJ,QAAQ,CAAC;QAC/C;QACA;QACA,IAAI,CAACnM,cAAc,IAAIuB,MAAM,KAAK,IAAI,EAAE;UACpCA,MAAM,GAAG0G,EAAE,CAACW,OAAO,CAAC,CAAC;QACzB;QACA,IAAIrH,MAAM,KAAK,KAAK,EAAE;UAClB8K,OAAO,CAAC/F,GAAG,CAAC2B,EAAE,EAAE1G,MAAM,CAAC;QAC3B;QACA,IAAIjD,OAAO,CAACoO,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;UAC5CnQ,KAAK,CAACgC,oBAAoB,CAACD,OAAO,CAAC;QACvC;MACJ,CAAC,CAAC;IACN;IACA,IAAIsD,gBAAgB,EAAE;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACvE,KAAK,CAACuE,gBAAgB,CAACA,gBAAgB,CAAC;IACjD;IACA,OAAOyK,OAAO;EAClB,CAAC;EACDhQ,YAAY,CAACR,SAAS,CAACO,aAAa,GAAG,UAAUE,OAAO,EAAE;IACtD,IAAIuC,EAAE,EAAEE,EAAE,EAAEK,EAAE;IACd,IAAIhC,QAAQ,GAAGd,OAAO,CAACc,QAAQ;MAAE4E,IAAI,GAAG1F,OAAO,CAAC0F,IAAI;IACpD,IAAIwC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;MAC9B,IAAIvE,WAAW,GAAG5D,OAAO,CAAC4D,WAAW;QAAE+B,EAAE,GAAG3F,OAAO,CAAC2F,EAAE;MACtD,IAAI0K,aAAa,GAAG,CAAC9N,EAAE,GAAG3E,sBAAsB,CAACkD,QAAQ,CAAC,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwH,SAAS;MAC7G,IAAIuG,WAAW,GAAG,CAAC,CAAC7N,EAAE,GAAG4N,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,IAAIkD,EAAE;MACnJ,IAAI,IAAI,CAACpE,WAAW,IAChBqC,WAAW,KAAK,UAAU,IAC1B,CAACpG,wBAAwB,CAACsD,QAAQ,CAAC,IACnC,CAAC,IAAI,CAACH,wBAAwB,CAACqI,GAAG,CAACsH,WAAW,CAAC,EAAE;QACjD,IAAI,CAAC3P,wBAAwB,CAAC+K,GAAG,CAAC4E,WAAW,CAAC;QAC9CpI,UAAU,CAACC,OAAO,KAAK,KAAK,IAAInL,SAAS,CAACiP,IAAI,CAC1C,EAAE,EACF,CAACnJ,EAAE,GAAGjF,gBAAgB,CAACiD,QAAQ,CAAC,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,UAAU,CAACrB,MAAM,CAAC4O,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,WAAW,CACzK,CAAC;MACL;IACJ;IACA,OAAQ,IAAI,CAAC9O,WAAW,GACpBzB,aAAa,CAAC4F,IAAI,EAAE5E,QAAQ,EAAE,IAAI,CAACC,KAAK,CAAC,GACvC2E,IAAI;EACd,CAAC;EACD3F,YAAY,CAACR,SAAS,CAACM,YAAY,GAAG,UAAUG,OAAO,EAAE;IACrD,IAAI0F,IAAI,GAAG1F,OAAO,CAAC0F,IAAI;MAAE6K,QAAQ,GAAGvQ,OAAO,CAACuQ,QAAQ;MAAEC,YAAY,GAAGxQ,OAAO,CAACwQ,YAAY;IACzF,OAAO,IAAI,CAACjP,WAAW,GACnB1B,YAAY,CAAC6F,IAAI,EAAE6K,QAAQ,EAAE,IAAI,CAACxP,KAAK,EAAEyP,YAAY,CAAC,GACpD9K,IAAI;EACd,CAAC;EACD3F,YAAY,CAACR,SAAS,CAAC8P,kBAAkB,GAAG,UAAUvG,SAAS,EAAEvG,EAAE;EACnE;EACA;EACA;EACA8F,aAAa,EAAE;IACX,IAAIpI,KAAK,GAAG,IAAI;IAChB,IAAIiG,KAAK,GAAG3D,EAAE,CAAC2D,KAAK;MAAEjD,SAAS,GAAGV,EAAE,CAACU,SAAS;MAAEW,WAAW,GAAGrB,EAAE,CAACqB,WAAW;MAAE6M,kBAAkB,GAAGlO,EAAE,CAACkO,kBAAkB;MAAE3M,WAAW,GAAGvB,EAAE,CAACuB,WAAW;MAAEyC,iBAAiB,GAAGhE,EAAE,CAACgE,iBAAiB;MAAEvC,OAAO,GAAGzB,EAAE,CAACyB,OAAO;MAAEkG,2BAA2B,GAAG3H,EAAE,CAAC2H,2BAA2B;IACpR,IAAIwG,gBAAgB,GAAG5H,SAAS,CAACT,aAAa;IAC9CS,SAAS,CAACwB,IAAI,CAAC;MACXxJ,QAAQ,EAAEoF,KAAK;MACfjD,SAAS,EAAEA,SAAS;MACpBoF,aAAa,EAAEA;IACnB,CAAC,CAAC;IACF,IAAIsI,SAAS,GAAG,SAAAA,CAAA,EAAY;MAAE,OAAO7H,SAAS,CAACgH,OAAO,CAAC,CAAC;IAAE,CAAC;IAC3D,IAAIc,gBAAgB,GAAG,SAAAA,CAAUxK,IAAI,EAAEiC,aAAa,EAAE;MAClD,IAAIA,aAAa,KAAK,KAAK,CAAC,EAAE;QAAEA,aAAa,GAAGS,SAAS,CAACT,aAAa,IAAItJ,aAAa,CAAC2F,OAAO;MAAE;MAClG,IAAIgB,IAAI,GAAGU,IAAI,CAACnB,MAAM;MACtB,IAAIiD,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI,CAAC5B,iBAAiB,IAAI,CAACrJ,KAAK,CAACwI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QACxE5G,qBAAqB,CAACsH,IAAI,CAACyK,OAAO,CAAC;MACvC;MACA,IAAIC,QAAQ,GAAG,SAAAA,CAAUpL,IAAI,EAAE;QAC3B,OAAOzH,UAAU,CAACkQ,EAAE,CAACtR,QAAQ,CAAC;UAAE6I,IAAI,EAAEA,IAAI;UAAEhB,OAAO,EAAE1F,wBAAwB,CAACqJ,aAAa,CAAC;UAAEA,aAAa,EAAEA;QAAc,CAAC,EAAGjC,IAAI,CAACY,QAAQ,GAAG,IAAI,GAAG;UAAE+J,OAAO,EAAE;QAAK,CAAE,CAAC,CAAC;MAC9K,CAAC;MACD,IAAIrL,IAAI,IAAIzF,KAAK,CAACqE,eAAe,CAAC4B,KAAK,CAAC,CAACgD,kBAAkB,EAAE;QACzD,OAAOjJ,KAAK,CAACmB,UAAU,CAClBgN,YAAY,CAAC;UACdtN,QAAQ,EAAEoF,KAAK;UACfmI,YAAY,EAAE;YAAE3I,IAAI,EAAEA;UAAK,CAAC;UAC5B1B,OAAO,EAAEA,OAAO;UAChBf,SAAS,EAAEA,SAAS;UACpB+N,sBAAsB,EAAE;QAC5B,CAAC,CAAC,CACGhJ,IAAI,CAAC,UAAUiJ,QAAQ,EAAE;UAAE,OAAOH,QAAQ,CAACG,QAAQ,CAACvL,IAAI,IAAI,KAAK,CAAC,CAAC;QAAE,CAAC,CAAC;MAChF;MACA;MACA;MACA;MACA;MACA,IAAI5B,WAAW,KAAK,MAAM,IACtBuE,aAAa,KAAKtJ,aAAa,CAACuN,OAAO,IACvChB,KAAK,CAACC,OAAO,CAACnF,IAAI,CAACyK,OAAO,CAAC,EAAE;QAC7B,OAAOC,QAAQ,CAAC,KAAK,CAAC,CAAC;MAC3B;MACA,OAAOA,QAAQ,CAACpL,IAAI,CAAC;IACzB,CAAC;IACD,IAAI6I,kBAAkB,GAAG3K,WAAW,KAAK,UAAU,GAAG,CAAC,CAAC;IACpD;IACA;IAAA,EACGyE,aAAa,KAAKtJ,aAAa,CAACuN,OAAO,IACtCmE,kBAAkB,KAAK,OAAO,GAC9B,CAAC,CAAC,qCACA,CAAC,CAAC;IACZ,IAAIS,eAAe,GAAG,SAAAA,CAAA,EAAY;MAC9B,OAAOjR,KAAK,CAACqO,kBAAkB,CAACxF,SAAS,EAAEyF,kBAAkB,EAAE;QAC3DrI,KAAK,EAAEA,KAAK;QACZjD,SAAS,EAAEA,SAAS;QACpBe,OAAO,EAAEA,OAAO;QAChBJ,WAAW,EAAEA,WAAW;QACxBE,WAAW,EAAEA;MACjB,CAAC,CAAC;IACN,CAAC;IACD,IAAIqN,YAAY,GAAGjH,2BAA2B,IAC1C,OAAOwG,gBAAgB,KAAK,QAAQ,IACpCA,gBAAgB,KAAKrI,aAAa,IAClCrJ,wBAAwB,CAACqJ,aAAa,CAAC;IAC3C,QAAQzE,WAAW;MACf;MACA,KAAK,aAAa;QAAE;UAChB,IAAIwC,IAAI,GAAGuK,SAAS,CAAC,CAAC;UACtB,IAAIvK,IAAI,CAACY,QAAQ,EAAE;YACf,OAAO;cACH2I,QAAQ,EAAE,KAAK;cACfL,OAAO,EAAE,CAACsB,gBAAgB,CAACxK,IAAI,EAAE0C,SAAS,CAAC+F,SAAS,CAAC,CAAC,CAAC;YAC3D,CAAC;UACL;UACA,IAAItI,iBAAiB,IAAI4K,YAAY,EAAE;YACnC,OAAO;cACHxB,QAAQ,EAAE,IAAI;cACdL,OAAO,EAAE,CAACsB,gBAAgB,CAACxK,IAAI,CAAC,EAAE8K,eAAe,CAAC,CAAC;YACvD,CAAC;UACL;UACA,OAAO;YAAEvB,QAAQ,EAAE,IAAI;YAAEL,OAAO,EAAE,CAAC4B,eAAe,CAAC,CAAC;UAAE,CAAC;QAC3D;MACA,KAAK,mBAAmB;QAAE;UACtB,IAAI9K,IAAI,GAAGuK,SAAS,CAAC,CAAC;UACtB,IAAIvK,IAAI,CAACY,QAAQ,IAAIT,iBAAiB,IAAI4K,YAAY,EAAE;YACpD,OAAO;cACHxB,QAAQ,EAAE,IAAI;cACdL,OAAO,EAAE,CAACsB,gBAAgB,CAACxK,IAAI,CAAC,EAAE8K,eAAe,CAAC,CAAC;YACvD,CAAC;UACL;UACA,OAAO;YAAEvB,QAAQ,EAAE,IAAI;YAAEL,OAAO,EAAE,CAAC4B,eAAe,CAAC,CAAC;UAAE,CAAC;QAC3D;MACA,KAAK,YAAY;QACb,OAAO;UACHvB,QAAQ,EAAE,KAAK;UACfL,OAAO,EAAE,CAACsB,gBAAgB,CAACD,SAAS,CAAC,CAAC,EAAE7H,SAAS,CAAC+F,SAAS,CAAC,CAAC,CAAC;QAClE,CAAC;MACL,KAAK,cAAc;QACf,IAAIsC,YAAY,EAAE;UACd,OAAO;YACHxB,QAAQ,EAAE,IAAI;YACdL,OAAO,EAAE,CAACsB,gBAAgB,CAACD,SAAS,CAAC,CAAC,CAAC,EAAEO,eAAe,CAAC,CAAC;UAC9D,CAAC;QACL;QACA,OAAO;UAAEvB,QAAQ,EAAE,IAAI;UAAEL,OAAO,EAAE,CAAC4B,eAAe,CAAC,CAAC;QAAE,CAAC;MAC3D,KAAK,UAAU;QACX,IAAIC,YAAY,EAAE;UACd,OAAO;YACHxB,QAAQ,EAAE,IAAI;YACd;YACA;YACA;YACAL,OAAO,EAAE,CAACsB,gBAAgB,CAAC9H,SAAS,CAACgH,OAAO,CAAC,CAAC,CAAC,EAAEoB,eAAe,CAAC,CAAC;UACtE,CAAC;QACL;QACA,OAAO;UAAEvB,QAAQ,EAAE,IAAI;UAAEL,OAAO,EAAE,CAAC4B,eAAe,CAAC,CAAC;QAAE,CAAC;MAC3D,KAAK,SAAS;QACV,OAAO;UAAEvB,QAAQ,EAAE,KAAK;UAAEL,OAAO,EAAE;QAAG,CAAC;IAC/C;EACJ,CAAC;EACDvP,YAAY,CAACR,SAAS,CAACgJ,gBAAgB,GAAG,UAAUvG,OAAO,EAAE;IACzD,IAAIA,OAAO,IAAI,CAAC,IAAI,CAAC7B,OAAO,CAAC6I,GAAG,CAAChH,OAAO,CAAC,EAAE;MACvC,IAAI,CAAC7B,OAAO,CAAC6J,GAAG,CAAChI,OAAO,EAAE,IAAI/C,SAAS,CAAC,IAAI,EAAE+C,OAAO,CAAC,CAAC;IAC3D;IACA,OAAO,IAAI,CAAC7B,OAAO,CAAC2G,GAAG,CAAC9E,OAAO,CAAC;EACpC,CAAC;EACDjC,YAAY,CAACR,SAAS,CAACiO,cAAc,GAAG,UAAUxJ,OAAO,EAAE;IACvD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,CAAC,CAAC;IAAE;IACxC,IAAIoN,UAAU,GAAG,IAAI,CAAChQ,UAAU,CAACoM,cAAc,CAACxJ,OAAO,CAAC;IACxD,OAAOnH,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6E,cAAc,CAAC,EAAE0P,UAAU,CAAC,EAAE;MAAElR,eAAe,EAAE,IAAI,CAACA;IAAgB,CAAC,CAAC;EACvH,CAAC;EACD,OAAOH,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}