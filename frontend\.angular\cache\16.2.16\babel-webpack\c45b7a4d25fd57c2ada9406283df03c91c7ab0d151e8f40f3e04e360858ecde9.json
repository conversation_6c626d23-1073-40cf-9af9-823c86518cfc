{"ast": null, "code": "import { devAssert } from './jsutils/devAssert.mjs';\nimport { isPromise } from './jsutils/isPromise.mjs';\nimport { parse } from './language/parser.mjs';\nimport { validateSchema } from './type/validate.mjs';\nimport { validate } from './validation/validate.mjs';\nimport { execute } from './execution/execute.mjs';\n/**\n * This is the primary entry point function for fulfilling GraphQL operations\n * by parsing, validating, and executing a GraphQL document along side a\n * GraphQL schema.\n *\n * More sophisticated GraphQL servers, such as those which persist queries,\n * may wish to separate the validation and execution phases to a static time\n * tooling step, and a server runtime step.\n *\n * Accepts either an object with named arguments, or individual arguments:\n *\n * schema:\n *    The GraphQL type system to use when validating and executing a query.\n * source:\n *    A GraphQL language formatted string representing the requested operation.\n * rootValue:\n *    The value provided as the first argument to resolver functions on the top\n *    level type (e.g. the query object type).\n * contextValue:\n *    The context value is provided as an argument to resolver functions after\n *    field arguments. It is used to pass shared information useful at any point\n *    during executing this query, for example the currently logged in user and\n *    connections to databases or other services.\n * variableValues:\n *    A mapping of variable name to runtime value to use for all variables\n *    defined in the requestString.\n * operationName:\n *    The name of the operation to use if requestString contains multiple\n *    possible operations. Can be omitted if requestString contains only\n *    one operation.\n * fieldResolver:\n *    A resolver function to use when one is not provided by the schema.\n *    If not provided, the default field resolver is used (which looks for a\n *    value or method on the source value with the field's name).\n * typeResolver:\n *    A type resolver function to use when none is provided by the schema.\n *    If not provided, the default type resolver is used (which looks for a\n *    `__typename` field or alternatively calls the `isTypeOf` method).\n */\n\nexport function graphql(args) {\n  // Always return a Promise for a consistent API.\n  return new Promise(resolve => resolve(graphqlImpl(args)));\n}\n/**\n * The graphqlSync function also fulfills GraphQL operations by parsing,\n * validating, and executing a GraphQL document along side a GraphQL schema.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\n\nexport function graphqlSync(args) {\n  const result = graphqlImpl(args); // Assert that the execution was synchronous.\n\n  if (isPromise(result)) {\n    throw new Error('GraphQL execution failed to complete synchronously.');\n  }\n  return result;\n}\nfunction graphqlImpl(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n  const {\n    schema,\n    source,\n    rootValue,\n    contextValue,\n    variableValues,\n    operationName,\n    fieldResolver,\n    typeResolver\n  } = args; // Validate Schema\n\n  const schemaValidationErrors = validateSchema(schema);\n  if (schemaValidationErrors.length > 0) {\n    return {\n      errors: schemaValidationErrors\n    };\n  } // Parse\n\n  let document;\n  try {\n    document = parse(source);\n  } catch (syntaxError) {\n    return {\n      errors: [syntaxError]\n    };\n  } // Validate\n\n  const validationErrors = validate(schema, document);\n  if (validationErrors.length > 0) {\n    return {\n      errors: validationErrors\n    };\n  } // Execute\n\n  return execute({\n    schema,\n    document,\n    rootValue,\n    contextValue,\n    variableValues,\n    operationName,\n    fieldResolver,\n    typeResolver\n  });\n}", "map": {"version": 3, "names": ["devAssert", "isPromise", "parse", "validateSchema", "validate", "execute", "graphql", "args", "Promise", "resolve", "graphqlImpl", "graphqlSync", "result", "Error", "arguments", "length", "schema", "source", "rootValue", "contextValue", "variableValues", "operationName", "fieldResolver", "typeResolver", "schemaValidationErrors", "errors", "document", "syntaxError", "validationErrors"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/graphql.mjs"], "sourcesContent": ["import { devAssert } from './jsutils/devAssert.mjs';\nimport { isPromise } from './jsutils/isPromise.mjs';\nimport { parse } from './language/parser.mjs';\nimport { validateSchema } from './type/validate.mjs';\nimport { validate } from './validation/validate.mjs';\nimport { execute } from './execution/execute.mjs';\n/**\n * This is the primary entry point function for fulfilling GraphQL operations\n * by parsing, validating, and executing a GraphQL document along side a\n * GraphQL schema.\n *\n * More sophisticated GraphQL servers, such as those which persist queries,\n * may wish to separate the validation and execution phases to a static time\n * tooling step, and a server runtime step.\n *\n * Accepts either an object with named arguments, or individual arguments:\n *\n * schema:\n *    The GraphQL type system to use when validating and executing a query.\n * source:\n *    A GraphQL language formatted string representing the requested operation.\n * rootValue:\n *    The value provided as the first argument to resolver functions on the top\n *    level type (e.g. the query object type).\n * contextValue:\n *    The context value is provided as an argument to resolver functions after\n *    field arguments. It is used to pass shared information useful at any point\n *    during executing this query, for example the currently logged in user and\n *    connections to databases or other services.\n * variableValues:\n *    A mapping of variable name to runtime value to use for all variables\n *    defined in the requestString.\n * operationName:\n *    The name of the operation to use if requestString contains multiple\n *    possible operations. Can be omitted if requestString contains only\n *    one operation.\n * fieldResolver:\n *    A resolver function to use when one is not provided by the schema.\n *    If not provided, the default field resolver is used (which looks for a\n *    value or method on the source value with the field's name).\n * typeResolver:\n *    A type resolver function to use when none is provided by the schema.\n *    If not provided, the default type resolver is used (which looks for a\n *    `__typename` field or alternatively calls the `isTypeOf` method).\n */\n\nexport function graphql(args) {\n  // Always return a Promise for a consistent API.\n  return new Promise((resolve) => resolve(graphqlImpl(args)));\n}\n/**\n * The graphqlSync function also fulfills GraphQL operations by parsing,\n * validating, and executing a GraphQL document along side a GraphQL schema.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\n\nexport function graphqlSync(args) {\n  const result = graphqlImpl(args); // Assert that the execution was synchronous.\n\n  if (isPromise(result)) {\n    throw new Error('GraphQL execution failed to complete synchronously.');\n  }\n\n  return result;\n}\n\nfunction graphqlImpl(args) {\n  // Temporary for v15 to v16 migration. Remove in v17\n  arguments.length < 2 ||\n    devAssert(\n      false,\n      'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.',\n    );\n  const {\n    schema,\n    source,\n    rootValue,\n    contextValue,\n    variableValues,\n    operationName,\n    fieldResolver,\n    typeResolver,\n  } = args; // Validate Schema\n\n  const schemaValidationErrors = validateSchema(schema);\n\n  if (schemaValidationErrors.length > 0) {\n    return {\n      errors: schemaValidationErrors,\n    };\n  } // Parse\n\n  let document;\n\n  try {\n    document = parse(source);\n  } catch (syntaxError) {\n    return {\n      errors: [syntaxError],\n    };\n  } // Validate\n\n  const validationErrors = validate(schema, document);\n\n  if (validationErrors.length > 0) {\n    return {\n      errors: validationErrors,\n    };\n  } // Execute\n\n  return execute({\n    schema,\n    document,\n    rootValue,\n    contextValue,\n    variableValues,\n    operationName,\n    fieldResolver,\n    typeResolver,\n  });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAE;EAC5B;EACA,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACC,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASI,WAAWA,CAACJ,IAAI,EAAE;EAChC,MAAMK,MAAM,GAAGF,WAAW,CAACH,IAAI,CAAC,CAAC,CAAC;;EAElC,IAAIN,SAAS,CAACW,MAAM,CAAC,EAAE;IACrB,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,OAAOD,MAAM;AACf;AAEA,SAASF,WAAWA,CAACH,IAAI,EAAE;EACzB;EACAO,SAAS,CAACC,MAAM,GAAG,CAAC,IAClBf,SAAS,CACP,KAAK,EACL,qGACF,CAAC;EACH,MAAM;IACJgB,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,GAAGhB,IAAI,CAAC,CAAC;;EAEV,MAAMiB,sBAAsB,GAAGrB,cAAc,CAACa,MAAM,CAAC;EAErD,IAAIQ,sBAAsB,CAACT,MAAM,GAAG,CAAC,EAAE;IACrC,OAAO;MACLU,MAAM,EAAED;IACV,CAAC;EACH,CAAC,CAAC;;EAEF,IAAIE,QAAQ;EAEZ,IAAI;IACFA,QAAQ,GAAGxB,KAAK,CAACe,MAAM,CAAC;EAC1B,CAAC,CAAC,OAAOU,WAAW,EAAE;IACpB,OAAO;MACLF,MAAM,EAAE,CAACE,WAAW;IACtB,CAAC;EACH,CAAC,CAAC;;EAEF,MAAMC,gBAAgB,GAAGxB,QAAQ,CAACY,MAAM,EAAEU,QAAQ,CAAC;EAEnD,IAAIE,gBAAgB,CAACb,MAAM,GAAG,CAAC,EAAE;IAC/B,OAAO;MACLU,MAAM,EAAEG;IACV,CAAC;EACH,CAAC,CAAC;;EAEF,OAAOvB,OAAO,CAAC;IACbW,MAAM;IACNU,QAAQ;IACRR,SAAS;IACTC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbC,aAAa;IACbC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}