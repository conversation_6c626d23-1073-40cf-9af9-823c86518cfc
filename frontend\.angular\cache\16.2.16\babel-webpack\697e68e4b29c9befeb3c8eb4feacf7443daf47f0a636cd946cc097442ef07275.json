{"ast": null, "code": "export function maybe(thunk) {\n  try {\n    return thunk();\n  } catch (_a) {}\n}", "map": {"version": 3, "names": ["maybe", "thunk", "_a"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/globals/maybe.js"], "sourcesContent": ["export function maybe(thunk) {\n    try {\n        return thunk();\n    }\n    catch (_a) { }\n}\n"], "mappings": "AAAA,OAAO,SAASA,KAAKA,CAACC,KAAK,EAAE;EACzB,IAAI;IACA,OAAOA,KAAK,CAAC,CAAC;EAClB,CAAC,CACD,OAAOC,EAAE,EAAE,CAAE;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}