{"ast": null, "code": "export function preventUnhandledRejection(promise) {\n  promise.catch(function () {});\n  return promise;\n}", "map": {"version": 3, "names": ["preventUnhandledRejection", "promise", "catch"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/promises/preventUnhandledRejection.js"], "sourcesContent": ["export function preventUnhandledRejection(promise) {\n    promise.catch(function () { });\n    return promise;\n}\n"], "mappings": "AAAA,OAAO,SAASA,yBAAyBA,CAACC,OAAO,EAAE;EAC/CA,OAAO,CAACC,KAAK,CAAC,YAAY,CAAE,CAAC,CAAC;EAC9B,OAAOD,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}