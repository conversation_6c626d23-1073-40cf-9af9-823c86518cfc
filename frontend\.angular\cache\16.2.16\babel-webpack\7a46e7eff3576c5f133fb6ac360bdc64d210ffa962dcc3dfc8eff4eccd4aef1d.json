{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { Observable } from \"../../utilities/index.js\";\n// QueryBatcher doesn't fire requests immediately. Requests that were enqueued within\n// a certain amount of time (configurable through `batchInterval`) will be batched together\n// into one query.\nvar OperationBatcher = /** @class */function () {\n  function OperationBatcher(_a) {\n    var batchDebounce = _a.batchDebounce,\n      batchInterval = _a.batchInterval,\n      batchMax = _a.batchMax,\n      batchHandler = _a.batchHandler,\n      batchKey = _a.batchKey;\n    // Queue on which the QueryBatcher will operate on a per-tick basis.\n    this.batchesByKey = new Map();\n    this.scheduledBatchTimerByKey = new Map();\n    this.batchDebounce = batchDebounce;\n    this.batchInterval = batchInterval;\n    this.batchMax = batchMax || 0;\n    this.batchHandler = batchHandler;\n    this.batchKey = batchKey || function () {\n      return \"\";\n    };\n  }\n  OperationBatcher.prototype.enqueueRequest = function (request) {\n    var _this = this;\n    var requestCopy = __assign(__assign({}, request), {\n      next: [],\n      error: [],\n      complete: [],\n      subscribers: new Set()\n    });\n    var key = this.batchKey(request.operation);\n    if (!requestCopy.observable) {\n      requestCopy.observable = new Observable(function (observer) {\n        var batch = _this.batchesByKey.get(key);\n        if (!batch) _this.batchesByKey.set(key, batch = new Set());\n        // These booleans seem to me (@benjamn) like they might always be the\n        // same (and thus we could do with only one of them), but I'm not 100%\n        // sure about that.\n        var isFirstEnqueuedRequest = batch.size === 0;\n        var isFirstSubscriber = requestCopy.subscribers.size === 0;\n        requestCopy.subscribers.add(observer);\n        if (isFirstSubscriber) {\n          batch.add(requestCopy);\n        }\n        // called for each subscriber, so need to save all listeners (next, error, complete)\n        if (observer.next) {\n          requestCopy.next.push(observer.next.bind(observer));\n        }\n        if (observer.error) {\n          requestCopy.error.push(observer.error.bind(observer));\n        }\n        if (observer.complete) {\n          requestCopy.complete.push(observer.complete.bind(observer));\n        }\n        // The first enqueued request triggers the queue consumption after `batchInterval` milliseconds.\n        if (isFirstEnqueuedRequest || _this.batchDebounce) {\n          _this.scheduleQueueConsumption(key);\n        }\n        // When amount of requests reaches `batchMax`, trigger the queue consumption without waiting on the `batchInterval`.\n        if (batch.size === _this.batchMax) {\n          _this.consumeQueue(key);\n        }\n        return function () {\n          var _a;\n          // If this is last subscriber for this request, remove request from queue\n          if (requestCopy.subscribers.delete(observer) && requestCopy.subscribers.size < 1) {\n            // If this is last request from queue, remove queue entirely\n            if (batch.delete(requestCopy) && batch.size < 1) {\n              _this.consumeQueue(key);\n              // If queue was in flight, cancel it\n              (_a = batch.subscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n            }\n          }\n        };\n      });\n    }\n    return requestCopy.observable;\n  };\n  // Consumes the queue.\n  // Returns a list of promises (one for each query).\n  OperationBatcher.prototype.consumeQueue = function (key) {\n    if (key === void 0) {\n      key = \"\";\n    }\n    var batch = this.batchesByKey.get(key);\n    // Delete this batch and process it below.\n    this.batchesByKey.delete(key);\n    if (!batch || !batch.size) {\n      // No requests to be processed.\n      return;\n    }\n    var operations = [];\n    var forwards = [];\n    var observables = [];\n    var nexts = [];\n    var errors = [];\n    var completes = [];\n    // Even though batch is a Set, it preserves the order of first insertion\n    // when iterating (per ECMAScript specification), so these requests will be\n    // handled in the order they were enqueued (minus any deleted ones).\n    batch.forEach(function (request) {\n      operations.push(request.operation);\n      forwards.push(request.forward);\n      observables.push(request.observable);\n      nexts.push(request.next);\n      errors.push(request.error);\n      completes.push(request.complete);\n    });\n    var batchedObservable = this.batchHandler(operations, forwards) || Observable.of();\n    var onError = function (error) {\n      //each callback list in batch\n      errors.forEach(function (rejecters) {\n        if (rejecters) {\n          //each subscriber to request\n          rejecters.forEach(function (e) {\n            return e(error);\n          });\n        }\n      });\n    };\n    batch.subscription = batchedObservable.subscribe({\n      next: function (results) {\n        if (!Array.isArray(results)) {\n          results = [results];\n        }\n        if (nexts.length !== results.length) {\n          var error = new Error(\"server returned results with length \".concat(results.length, \", expected length of \").concat(nexts.length));\n          error.result = results;\n          return onError(error);\n        }\n        results.forEach(function (result, index) {\n          if (nexts[index]) {\n            nexts[index].forEach(function (next) {\n              return next(result);\n            });\n          }\n        });\n      },\n      error: onError,\n      complete: function () {\n        completes.forEach(function (complete) {\n          if (complete) {\n            //each subscriber to request\n            complete.forEach(function (c) {\n              return c();\n            });\n          }\n        });\n      }\n    });\n    return observables;\n  };\n  OperationBatcher.prototype.scheduleQueueConsumption = function (key) {\n    var _this = this;\n    clearTimeout(this.scheduledBatchTimerByKey.get(key));\n    this.scheduledBatchTimerByKey.set(key, setTimeout(function () {\n      _this.consumeQueue(key);\n      _this.scheduledBatchTimerByKey.delete(key);\n    }, this.batchInterval));\n  };\n  return OperationBatcher;\n}();\nexport { OperationBatcher };", "map": {"version": 3, "names": ["__assign", "Observable", "OperationBatcher", "_a", "batchDebounce", "batchInterval", "batchMax", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "batchesByKey", "Map", "scheduledBatchTimerByKey", "prototype", "enqueueRequest", "request", "_this", "requestCopy", "next", "error", "complete", "subscribers", "Set", "key", "operation", "observable", "observer", "batch", "get", "set", "isFirstEnqueuedRequest", "size", "isFirstSubscriber", "add", "push", "bind", "scheduleQueueConsumption", "consumeQueue", "delete", "subscription", "unsubscribe", "operations", "forwards", "observables", "nexts", "errors", "completes", "for<PERSON>ach", "forward", "batchedObservable", "of", "onError", "rejecters", "e", "subscribe", "results", "Array", "isArray", "length", "Error", "concat", "result", "index", "c", "clearTimeout", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/batch/batching.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { Observable } from \"../../utilities/index.js\";\n// QueryBatcher doesn't fire requests immediately. Requests that were enqueued within\n// a certain amount of time (configurable through `batchInterval`) will be batched together\n// into one query.\nvar OperationBatcher = /** @class */ (function () {\n    function OperationBatcher(_a) {\n        var batchDebounce = _a.batchDebounce, batchInterval = _a.batchInterval, batchMax = _a.batchMax, batchHandler = _a.batchHandler, batchKey = _a.batchKey;\n        // Queue on which the QueryBatcher will operate on a per-tick basis.\n        this.batchesByKey = new Map();\n        this.scheduledBatchTimerByKey = new Map();\n        this.batchDebounce = batchDebounce;\n        this.batchInterval = batchInterval;\n        this.batchMax = batchMax || 0;\n        this.batchHandler = batchHandler;\n        this.batchKey = batchKey || (function () { return \"\"; });\n    }\n    OperationBatcher.prototype.enqueueRequest = function (request) {\n        var _this = this;\n        var requestCopy = __assign(__assign({}, request), { next: [], error: [], complete: [], subscribers: new Set() });\n        var key = this.batchKey(request.operation);\n        if (!requestCopy.observable) {\n            requestCopy.observable = new Observable(function (observer) {\n                var batch = _this.batchesByKey.get(key);\n                if (!batch)\n                    _this.batchesByKey.set(key, (batch = new Set()));\n                // These booleans seem to me (@benjamn) like they might always be the\n                // same (and thus we could do with only one of them), but I'm not 100%\n                // sure about that.\n                var isFirstEnqueuedRequest = batch.size === 0;\n                var isFirstSubscriber = requestCopy.subscribers.size === 0;\n                requestCopy.subscribers.add(observer);\n                if (isFirstSubscriber) {\n                    batch.add(requestCopy);\n                }\n                // called for each subscriber, so need to save all listeners (next, error, complete)\n                if (observer.next) {\n                    requestCopy.next.push(observer.next.bind(observer));\n                }\n                if (observer.error) {\n                    requestCopy.error.push(observer.error.bind(observer));\n                }\n                if (observer.complete) {\n                    requestCopy.complete.push(observer.complete.bind(observer));\n                }\n                // The first enqueued request triggers the queue consumption after `batchInterval` milliseconds.\n                if (isFirstEnqueuedRequest || _this.batchDebounce) {\n                    _this.scheduleQueueConsumption(key);\n                }\n                // When amount of requests reaches `batchMax`, trigger the queue consumption without waiting on the `batchInterval`.\n                if (batch.size === _this.batchMax) {\n                    _this.consumeQueue(key);\n                }\n                return function () {\n                    var _a;\n                    // If this is last subscriber for this request, remove request from queue\n                    if (requestCopy.subscribers.delete(observer) &&\n                        requestCopy.subscribers.size < 1) {\n                        // If this is last request from queue, remove queue entirely\n                        if (batch.delete(requestCopy) && batch.size < 1) {\n                            _this.consumeQueue(key);\n                            // If queue was in flight, cancel it\n                            (_a = batch.subscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n                        }\n                    }\n                };\n            });\n        }\n        return requestCopy.observable;\n    };\n    // Consumes the queue.\n    // Returns a list of promises (one for each query).\n    OperationBatcher.prototype.consumeQueue = function (key) {\n        if (key === void 0) { key = \"\"; }\n        var batch = this.batchesByKey.get(key);\n        // Delete this batch and process it below.\n        this.batchesByKey.delete(key);\n        if (!batch || !batch.size) {\n            // No requests to be processed.\n            return;\n        }\n        var operations = [];\n        var forwards = [];\n        var observables = [];\n        var nexts = [];\n        var errors = [];\n        var completes = [];\n        // Even though batch is a Set, it preserves the order of first insertion\n        // when iterating (per ECMAScript specification), so these requests will be\n        // handled in the order they were enqueued (minus any deleted ones).\n        batch.forEach(function (request) {\n            operations.push(request.operation);\n            forwards.push(request.forward);\n            observables.push(request.observable);\n            nexts.push(request.next);\n            errors.push(request.error);\n            completes.push(request.complete);\n        });\n        var batchedObservable = this.batchHandler(operations, forwards) || Observable.of();\n        var onError = function (error) {\n            //each callback list in batch\n            errors.forEach(function (rejecters) {\n                if (rejecters) {\n                    //each subscriber to request\n                    rejecters.forEach(function (e) { return e(error); });\n                }\n            });\n        };\n        batch.subscription = batchedObservable.subscribe({\n            next: function (results) {\n                if (!Array.isArray(results)) {\n                    results = [results];\n                }\n                if (nexts.length !== results.length) {\n                    var error = new Error(\"server returned results with length \".concat(results.length, \", expected length of \").concat(nexts.length));\n                    error.result = results;\n                    return onError(error);\n                }\n                results.forEach(function (result, index) {\n                    if (nexts[index]) {\n                        nexts[index].forEach(function (next) { return next(result); });\n                    }\n                });\n            },\n            error: onError,\n            complete: function () {\n                completes.forEach(function (complete) {\n                    if (complete) {\n                        //each subscriber to request\n                        complete.forEach(function (c) { return c(); });\n                    }\n                });\n            },\n        });\n        return observables;\n    };\n    OperationBatcher.prototype.scheduleQueueConsumption = function (key) {\n        var _this = this;\n        clearTimeout(this.scheduledBatchTimerByKey.get(key));\n        this.scheduledBatchTimerByKey.set(key, setTimeout(function () {\n            _this.consumeQueue(key);\n            _this.scheduledBatchTimerByKey.delete(key);\n        }, this.batchInterval));\n    };\n    return OperationBatcher;\n}());\nexport { OperationBatcher };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,UAAU,QAAQ,0BAA0B;AACrD;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAACC,EAAE,EAAE;IAC1B,IAAIC,aAAa,GAAGD,EAAE,CAACC,aAAa;MAAEC,aAAa,GAAGF,EAAE,CAACE,aAAa;MAAEC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;MAAEC,YAAY,GAAGJ,EAAE,CAACI,YAAY;MAAEC,QAAQ,GAAGL,EAAE,CAACK,QAAQ;IACtJ;IACA,IAAI,CAACC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,wBAAwB,GAAG,IAAID,GAAG,CAAC,CAAC;IACzC,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAI,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,QAAQ,GAAGA,QAAQ,IAAK,YAAY;MAAE,OAAO,EAAE;IAAE,CAAE;EAC5D;EACAN,gBAAgB,CAACU,SAAS,CAACC,cAAc,GAAG,UAAUC,OAAO,EAAE;IAC3D,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,WAAW,GAAGhB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEc,OAAO,CAAC,EAAE;MAAEG,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,WAAW,EAAE,IAAIC,GAAG,CAAC;IAAE,CAAC,CAAC;IAChH,IAAIC,GAAG,GAAG,IAAI,CAACd,QAAQ,CAACM,OAAO,CAACS,SAAS,CAAC;IAC1C,IAAI,CAACP,WAAW,CAACQ,UAAU,EAAE;MACzBR,WAAW,CAACQ,UAAU,GAAG,IAAIvB,UAAU,CAAC,UAAUwB,QAAQ,EAAE;QACxD,IAAIC,KAAK,GAAGX,KAAK,CAACN,YAAY,CAACkB,GAAG,CAACL,GAAG,CAAC;QACvC,IAAI,CAACI,KAAK,EACNX,KAAK,CAACN,YAAY,CAACmB,GAAG,CAACN,GAAG,EAAGI,KAAK,GAAG,IAAIL,GAAG,CAAC,CAAE,CAAC;QACpD;QACA;QACA;QACA,IAAIQ,sBAAsB,GAAGH,KAAK,CAACI,IAAI,KAAK,CAAC;QAC7C,IAAIC,iBAAiB,GAAGf,WAAW,CAACI,WAAW,CAACU,IAAI,KAAK,CAAC;QAC1Dd,WAAW,CAACI,WAAW,CAACY,GAAG,CAACP,QAAQ,CAAC;QACrC,IAAIM,iBAAiB,EAAE;UACnBL,KAAK,CAACM,GAAG,CAAChB,WAAW,CAAC;QAC1B;QACA;QACA,IAAIS,QAAQ,CAACR,IAAI,EAAE;UACfD,WAAW,CAACC,IAAI,CAACgB,IAAI,CAACR,QAAQ,CAACR,IAAI,CAACiB,IAAI,CAACT,QAAQ,CAAC,CAAC;QACvD;QACA,IAAIA,QAAQ,CAACP,KAAK,EAAE;UAChBF,WAAW,CAACE,KAAK,CAACe,IAAI,CAACR,QAAQ,CAACP,KAAK,CAACgB,IAAI,CAACT,QAAQ,CAAC,CAAC;QACzD;QACA,IAAIA,QAAQ,CAACN,QAAQ,EAAE;UACnBH,WAAW,CAACG,QAAQ,CAACc,IAAI,CAACR,QAAQ,CAACN,QAAQ,CAACe,IAAI,CAACT,QAAQ,CAAC,CAAC;QAC/D;QACA;QACA,IAAII,sBAAsB,IAAId,KAAK,CAACX,aAAa,EAAE;UAC/CW,KAAK,CAACoB,wBAAwB,CAACb,GAAG,CAAC;QACvC;QACA;QACA,IAAII,KAAK,CAACI,IAAI,KAAKf,KAAK,CAACT,QAAQ,EAAE;UAC/BS,KAAK,CAACqB,YAAY,CAACd,GAAG,CAAC;QAC3B;QACA,OAAO,YAAY;UACf,IAAInB,EAAE;UACN;UACA,IAAIa,WAAW,CAACI,WAAW,CAACiB,MAAM,CAACZ,QAAQ,CAAC,IACxCT,WAAW,CAACI,WAAW,CAACU,IAAI,GAAG,CAAC,EAAE;YAClC;YACA,IAAIJ,KAAK,CAACW,MAAM,CAACrB,WAAW,CAAC,IAAIU,KAAK,CAACI,IAAI,GAAG,CAAC,EAAE;cAC7Cf,KAAK,CAACqB,YAAY,CAACd,GAAG,CAAC;cACvB;cACA,CAACnB,EAAE,GAAGuB,KAAK,CAACY,YAAY,MAAM,IAAI,IAAInC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoC,WAAW,CAAC,CAAC;YACnF;UACJ;QACJ,CAAC;MACL,CAAC,CAAC;IACN;IACA,OAAOvB,WAAW,CAACQ,UAAU;EACjC,CAAC;EACD;EACA;EACAtB,gBAAgB,CAACU,SAAS,CAACwB,YAAY,GAAG,UAAUd,GAAG,EAAE;IACrD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAG,EAAE;IAAE;IAChC,IAAII,KAAK,GAAG,IAAI,CAACjB,YAAY,CAACkB,GAAG,CAACL,GAAG,CAAC;IACtC;IACA,IAAI,CAACb,YAAY,CAAC4B,MAAM,CAACf,GAAG,CAAC;IAC7B,IAAI,CAACI,KAAK,IAAI,CAACA,KAAK,CAACI,IAAI,EAAE;MACvB;MACA;IACJ;IACA,IAAIU,UAAU,GAAG,EAAE;IACnB,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,SAAS,GAAG,EAAE;IAClB;IACA;IACA;IACAnB,KAAK,CAACoB,OAAO,CAAC,UAAUhC,OAAO,EAAE;MAC7B0B,UAAU,CAACP,IAAI,CAACnB,OAAO,CAACS,SAAS,CAAC;MAClCkB,QAAQ,CAACR,IAAI,CAACnB,OAAO,CAACiC,OAAO,CAAC;MAC9BL,WAAW,CAACT,IAAI,CAACnB,OAAO,CAACU,UAAU,CAAC;MACpCmB,KAAK,CAACV,IAAI,CAACnB,OAAO,CAACG,IAAI,CAAC;MACxB2B,MAAM,CAACX,IAAI,CAACnB,OAAO,CAACI,KAAK,CAAC;MAC1B2B,SAAS,CAACZ,IAAI,CAACnB,OAAO,CAACK,QAAQ,CAAC;IACpC,CAAC,CAAC;IACF,IAAI6B,iBAAiB,GAAG,IAAI,CAACzC,YAAY,CAACiC,UAAU,EAAEC,QAAQ,CAAC,IAAIxC,UAAU,CAACgD,EAAE,CAAC,CAAC;IAClF,IAAIC,OAAO,GAAG,SAAAA,CAAUhC,KAAK,EAAE;MAC3B;MACA0B,MAAM,CAACE,OAAO,CAAC,UAAUK,SAAS,EAAE;QAChC,IAAIA,SAAS,EAAE;UACX;UACAA,SAAS,CAACL,OAAO,CAAC,UAAUM,CAAC,EAAE;YAAE,OAAOA,CAAC,CAAClC,KAAK,CAAC;UAAE,CAAC,CAAC;QACxD;MACJ,CAAC,CAAC;IACN,CAAC;IACDQ,KAAK,CAACY,YAAY,GAAGU,iBAAiB,CAACK,SAAS,CAAC;MAC7CpC,IAAI,EAAE,SAAAA,CAAUqC,OAAO,EAAE;QACrB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;UACzBA,OAAO,GAAG,CAACA,OAAO,CAAC;QACvB;QACA,IAAIX,KAAK,CAACc,MAAM,KAAKH,OAAO,CAACG,MAAM,EAAE;UACjC,IAAIvC,KAAK,GAAG,IAAIwC,KAAK,CAAC,sCAAsC,CAACC,MAAM,CAACL,OAAO,CAACG,MAAM,EAAE,uBAAuB,CAAC,CAACE,MAAM,CAAChB,KAAK,CAACc,MAAM,CAAC,CAAC;UAClIvC,KAAK,CAAC0C,MAAM,GAAGN,OAAO;UACtB,OAAOJ,OAAO,CAAChC,KAAK,CAAC;QACzB;QACAoC,OAAO,CAACR,OAAO,CAAC,UAAUc,MAAM,EAAEC,KAAK,EAAE;UACrC,IAAIlB,KAAK,CAACkB,KAAK,CAAC,EAAE;YACdlB,KAAK,CAACkB,KAAK,CAAC,CAACf,OAAO,CAAC,UAAU7B,IAAI,EAAE;cAAE,OAAOA,IAAI,CAAC2C,MAAM,CAAC;YAAE,CAAC,CAAC;UAClE;QACJ,CAAC,CAAC;MACN,CAAC;MACD1C,KAAK,EAAEgC,OAAO;MACd/B,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB0B,SAAS,CAACC,OAAO,CAAC,UAAU3B,QAAQ,EAAE;UAClC,IAAIA,QAAQ,EAAE;YACV;YACAA,QAAQ,CAAC2B,OAAO,CAAC,UAAUgB,CAAC,EAAE;cAAE,OAAOA,CAAC,CAAC,CAAC;YAAE,CAAC,CAAC;UAClD;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,OAAOpB,WAAW;EACtB,CAAC;EACDxC,gBAAgB,CAACU,SAAS,CAACuB,wBAAwB,GAAG,UAAUb,GAAG,EAAE;IACjE,IAAIP,KAAK,GAAG,IAAI;IAChBgD,YAAY,CAAC,IAAI,CAACpD,wBAAwB,CAACgB,GAAG,CAACL,GAAG,CAAC,CAAC;IACpD,IAAI,CAACX,wBAAwB,CAACiB,GAAG,CAACN,GAAG,EAAE0C,UAAU,CAAC,YAAY;MAC1DjD,KAAK,CAACqB,YAAY,CAACd,GAAG,CAAC;MACvBP,KAAK,CAACJ,wBAAwB,CAAC0B,MAAM,CAACf,GAAG,CAAC;IAC9C,CAAC,EAAE,IAAI,CAACjB,aAAa,CAAC,CAAC;EAC3B,CAAC;EACD,OAAOH,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}