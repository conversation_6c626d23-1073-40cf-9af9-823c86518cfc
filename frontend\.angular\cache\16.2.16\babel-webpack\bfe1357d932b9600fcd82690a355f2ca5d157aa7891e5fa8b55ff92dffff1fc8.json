{"ast": null, "code": "import { timer } from 'rxjs';\nimport { switchMap, catchError, map } from 'rxjs/operators';\nimport gql from 'graphql-tag';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"../../services/logger.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"connected\": a0,\n    \"disconnected\": a1\n  };\n};\nfunction GraphqlStatusComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"i\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, ctx_r0.isConnected, !ctx_r0.isConnected));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isConnected ? \"fa-check-circle\" : \"fa-exclamation-circle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isConnected ? \"Connexion GraphQL \\u00E9tablie\" : \"Connexion GraphQL perdue\", \" \");\n  }\n}\n// Requête simple pour vérifier la connexion GraphQL\nconst PING_QUERY = gql`\n  query Ping {\n    __typename\n  }\n`;\nexport class GraphqlStatusComponent {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.isConnected = true;\n    this.showStatus = false;\n    this.subscriptions = [];\n    this.checkInterval = 30000; // 30 secondes\n  }\n\n  ngOnInit() {\n    // Vérifier la connexion GraphQL périodiquement\n    const pingSubscription = timer(0, this.checkInterval).pipe(switchMap(() => this.checkGraphQLConnection()), catchError(error => {\n      this.logger.error('Error checking GraphQL connection', error);\n      this.updateConnectionStatus(false);\n      return [];\n    })).subscribe();\n    this.subscriptions.push(pingSubscription);\n  }\n  // Vérifier la connexion GraphQL\n  checkGraphQLConnection() {\n    return this.apollo.query({\n      query: PING_QUERY,\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      this.updateConnectionStatus(true);\n      return result;\n    }), catchError(error => {\n      this.updateConnectionStatus(false);\n      throw error;\n    }));\n  }\n  // Mettre à jour l'état de la connexion\n  updateConnectionStatus(isConnected) {\n    if (this.isConnected !== isConnected) {\n      this.isConnected = isConnected;\n      this.showStatus = true;\n      this.logger.debug(`GraphQL connection status changed: ${isConnected ? 'connected' : 'disconnected'}`);\n      // Masquer le statut après 3 secondes\n      setTimeout(() => {\n        this.showStatus = false;\n      }, 3000);\n    }\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function GraphqlStatusComponent_Factory(t) {\n      return new (t || GraphqlStatusComponent)(i0.ɵɵdirectiveInject(i1.Apollo), i0.ɵɵdirectiveInject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GraphqlStatusComponent,\n      selectors: [[\"app-graphql-status\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"graphql-status\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"graphql-status\", 3, \"ngClass\"], [1, \"status-icon\"], [1, \"fas\", 3, \"ngClass\"], [1, \"status-text\"]],\n      template: function GraphqlStatusComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, GraphqlStatusComponent_div_0_Template, 5, 6, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showStatus);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgIf],\n      styles: [\".graphql-status[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 20px;\\n  border-radius: 30px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out, _ngcontent-%COMP%_fadeOut 0.3s ease-out 2.7s;\\n}\\n\\n.connected[_ngcontent-%COMP%] {\\n  background-color: #4f5fad;\\n  color: white;\\n}\\n\\n.disconnected[_ngcontent-%COMP%] {\\n  background-color: #ff9800;\\n  color: white;\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  font-size: 18px;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    transform: translate(-50%, -100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translate(-50%, 0);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeOut {\\n  from {\\n    opacity: 1;\\n  }\\n  to {\\n    opacity: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImdyYXBocWwtc3RhdHVzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFlO0VBQ2YsU0FBUztFQUNULFNBQVM7RUFDVCwyQkFBMkI7RUFDM0IsYUFBYTtFQUNiLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsa0JBQWtCO0VBQ2xCLG1CQUFtQjtFQUNuQiwwQ0FBMEM7RUFDMUMsOERBQThEO0FBQ2hFOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLFlBQVk7QUFDZDs7QUFFQTtFQUNFLHlCQUF5QjtFQUN6QixZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0U7SUFDRSxpQ0FBaUM7SUFDakMsVUFBVTtFQUNaO0VBQ0E7SUFDRSw2QkFBNkI7SUFDN0IsVUFBVTtFQUNaO0FBQ0Y7QUFDQTtFQUNFO0lBQ0UsVUFBVTtFQUNaO0VBQ0E7SUFDRSxVQUFVO0VBQ1o7QUFDRiIsImZpbGUiOiJncmFwaHFsLXN0YXR1cy5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmdyYXBocWwtc3RhdHVzIHtcclxuICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgdG9wOiAyMHB4O1xyXG4gIGxlZnQ6IDUwJTtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XHJcbiAgei1pbmRleDogMTAwMDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgcGFkZGluZzogMTBweCAyMHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gIGFuaW1hdGlvbjogc2xpZGVEb3duIDAuM3MgZWFzZS1vdXQsIGZhZGVPdXQgMC4zcyBlYXNlLW91dCAyLjdzO1xyXG59XHJcblxyXG4uY29ubmVjdGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGY1ZmFkO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLmRpc2Nvbm5lY3RlZCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmOTgwMDtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5zdGF0dXMtaWNvbiB7XHJcbiAgbWFyZ2luLXJpZ2h0OiAxMHB4O1xyXG4gIGZvbnQtc2l6ZTogMThweDtcclxufVxyXG5cclxuLnN0YXR1cy10ZXh0IHtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxufVxyXG5cclxuQGtleWZyYW1lcyBzbGlkZURvd24ge1xyXG4gIGZyb20ge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTEwMCUpO1xyXG4gICAgb3BhY2l0eTogMDtcclxuICB9XHJcbiAgdG8ge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgMCk7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gIH1cclxufVxyXG5Aa2V5ZnJhbWVzIGZhZGVPdXQge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICB9XHJcbiAgdG8ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICB9XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["timer", "switchMap", "catchError", "map", "gql", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r0", "isConnected", "ɵɵadvance", "ɵɵtextInterpolate1", "PING_QUERY", "GraphqlStatusComponent", "constructor", "apollo", "logger", "showStatus", "subscriptions", "checkInterval", "ngOnInit", "pingSubscription", "pipe", "checkGraphQLConnection", "error", "updateConnectionStatus", "subscribe", "push", "query", "fetchPolicy", "result", "debug", "setTimeout", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "Apollo", "i2", "LoggerService", "selectors", "decls", "vars", "consts", "template", "GraphqlStatusComponent_Template", "rf", "ctx", "ɵɵtemplate", "GraphqlStatusComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\graphql-status\\graphql-status.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\graphql-status\\graphql-status.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { Apollo } from 'apollo-angular';\r\nimport { Subscription, timer } from 'rxjs';\r\nimport { switchMap, catchError, map } from 'rxjs/operators';\r\nimport { LoggerService } from '../../services/logger.service';\r\nimport gql from 'graphql-tag';\r\n\r\n// Requête simple pour vérifier la connexion GraphQL\r\nconst PING_QUERY = gql`\r\n  query Ping {\r\n    __typename\r\n  }\r\n`;\r\n\r\n@Component({\r\n  selector: 'app-graphql-status',\r\n  templateUrl: './graphql-status.component.html',\r\n  styleUrls: ['./graphql-status.component.css']\r\n})\r\nexport class GraphqlStatusComponent implements OnInit, OnDestroy {\r\n  isConnected: boolean = true;\r\n  showStatus: boolean = false;\r\n  private subscriptions: Subscription[] = [];\r\n  private checkInterval: number = 30000; // 30 secondes\r\n\r\n  constructor(\r\n    private apollo: Apollo,\r\n    private logger: LoggerService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // Vérifier la connexion GraphQL périodiquement\r\n    const pingSubscription = timer(0, this.checkInterval).pipe(\r\n      switchMap(() => this.checkGraphQLConnection()),\r\n      catchError(error => {\r\n        this.logger.error('Error checking GraphQL connection', error);\r\n        this.updateConnectionStatus(false);\r\n        return [];\r\n      })\r\n    ).subscribe();\r\n    \r\n    this.subscriptions.push(pingSubscription);\r\n  }\r\n\r\n  // Vérifier la connexion GraphQL\r\n  private checkGraphQLConnection() {\r\n    return this.apollo.query({\r\n      query: PING_QUERY,\r\n      fetchPolicy: 'network-only'\r\n    }).pipe(\r\n      map(result => {\r\n        this.updateConnectionStatus(true);\r\n        return result;\r\n      }),\r\n      catchError(error => {\r\n        this.updateConnectionStatus(false);\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Mettre à jour l'état de la connexion\r\n  private updateConnectionStatus(isConnected: boolean) {\r\n    if (this.isConnected !== isConnected) {\r\n      this.isConnected = isConnected;\r\n      this.showStatus = true;\r\n      \r\n      this.logger.debug(`GraphQL connection status changed: ${isConnected ? 'connected' : 'disconnected'}`);\r\n      \r\n      // Masquer le statut après 3 secondes\r\n      setTimeout(() => {\r\n        this.showStatus = false;\r\n      }, 3000);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Nettoyer les abonnements\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n  }\r\n}\r\n\r\n", "<div *ngIf=\"showStatus\" class=\"graphql-status\" [ngClass]=\"{'connected': isConnected, 'disconnected': !isConnected}\">\r\n  <div class=\"status-icon\">\r\n    <i class=\"fas\" [ngClass]=\"isConnected ? 'fa-check-circle' : 'fa-exclamation-circle'\"></i>\r\n  </div>\r\n  <div class=\"status-text\">\r\n    {{ isConnected ? 'Connexion GraphQL établie' : 'Connexion GraphQL perdue' }}\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAAuBA,KAAK,QAAQ,MAAM;AAC1C,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAE3D,OAAOC,GAAG,MAAM,aAAa;;;;;;;;;;;;;ICL7BC,EAAA,CAAAC,cAAA,aAAoH;IAEhHD,EAAA,CAAAE,SAAA,WAAyF;IAC3FF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IANuCH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,WAAA,GAAAD,MAAA,CAAAC,WAAA,EAAoE;IAEhGT,EAAA,CAAAU,SAAA,GAAqE;IAArEV,EAAA,CAAAK,UAAA,YAAAG,MAAA,CAAAC,WAAA,+CAAqE;IAGpFT,EAAA,CAAAU,SAAA,GACF;IADEV,EAAA,CAAAW,kBAAA,MAAAH,MAAA,CAAAC,WAAA,sEACF;;;ADCF;AACA,MAAMG,UAAU,GAAGb,GAAG;;;;CAIrB;AAOD,OAAM,MAAOc,sBAAsB;EAMjCC,YACUC,MAAc,EACdC,MAAqB;IADrB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAP,WAAW,GAAY,IAAI;IAC3B,KAAAQ,UAAU,GAAY,KAAK;IACnB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,aAAa,GAAW,KAAK,CAAC,CAAC;EAKnC;;EAEJC,QAAQA,CAAA;IACN;IACA,MAAMC,gBAAgB,GAAG1B,KAAK,CAAC,CAAC,EAAE,IAAI,CAACwB,aAAa,CAAC,CAACG,IAAI,CACxD1B,SAAS,CAAC,MAAM,IAAI,CAAC2B,sBAAsB,EAAE,CAAC,EAC9C1B,UAAU,CAAC2B,KAAK,IAAG;MACjB,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACC,sBAAsB,CAAC,KAAK,CAAC;MAClC,OAAO,EAAE;IACX,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IAEb,IAAI,CAACR,aAAa,CAACS,IAAI,CAACN,gBAAgB,CAAC;EAC3C;EAEA;EACQE,sBAAsBA,CAAA;IAC5B,OAAO,IAAI,CAACR,MAAM,CAACa,KAAK,CAAC;MACvBA,KAAK,EAAEhB,UAAU;MACjBiB,WAAW,EAAE;KACd,CAAC,CAACP,IAAI,CACLxB,GAAG,CAACgC,MAAM,IAAG;MACX,IAAI,CAACL,sBAAsB,CAAC,IAAI,CAAC;MACjC,OAAOK,MAAM;IACf,CAAC,CAAC,EACFjC,UAAU,CAAC2B,KAAK,IAAG;MACjB,IAAI,CAACC,sBAAsB,CAAC,KAAK,CAAC;MAClC,MAAMD,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACQC,sBAAsBA,CAAChB,WAAoB;IACjD,IAAI,IAAI,CAACA,WAAW,KAAKA,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACQ,UAAU,GAAG,IAAI;MAEtB,IAAI,CAACD,MAAM,CAACe,KAAK,CAAC,sCAAsCtB,WAAW,GAAG,WAAW,GAAG,cAAc,EAAE,CAAC;MAErG;MACAuB,UAAU,CAAC,MAAK;QACd,IAAI,CAACf,UAAU,GAAG,KAAK;MACzB,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEAgB,WAAWA,CAAA;IACT;IACA,IAAI,CAACf,aAAa,CAACgB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;;;uBA5DWvB,sBAAsB,EAAAb,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtB5B,sBAAsB;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBnChD,EAAA,CAAAkD,UAAA,IAAAC,qCAAA,iBAOM;;;UAPAnD,EAAA,CAAAK,UAAA,SAAA4C,GAAA,CAAAhC,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}