{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ToastService {\n  constructor() {\n    this.toastsSubject = new BehaviorSubject([]);\n    this.toasts$ = this.toastsSubject.asObservable();\n    this.currentId = 0;\n  }\n  show(message, type = 'info', duration = 5000) {\n    const id = this.currentId++;\n    const toast = {\n      id,\n      type,\n      message,\n      duration\n    };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n  showSuccess(message, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n  showError(message, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n  showWarning(message, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n  showInfo(message, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n  dismiss(id) {\n    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n  static {\n    this.ɵfac = function ToastService_Factory(t) {\n      return new (t || ToastService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ToastService,\n      factory: ToastService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ToastService", "constructor", "toastsSubject", "toasts$", "asObservable", "currentId", "show", "message", "type", "duration", "id", "toast", "currentToasts", "value", "next", "setTimeout", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "filter", "t", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\toast.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Toast  } from 'src/app/models/message.model';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ToastService {\r\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\r\n  toasts$ = this.toastsSubject.asObservable();\r\n  private currentId = 0;\r\n\r\n  constructor() { }\r\n\r\n  show(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration = 5000) {\r\n    const id = this.currentId++;\r\n    const toast: Toast = { id, type, message, duration };\r\n    const currentToasts = this.toastsSubject.value;\r\n    this.toastsSubject.next([...currentToasts, toast]);\r\n\r\n    if (duration > 0) {\r\n      setTimeout(() => this.dismiss(id), duration);\r\n    }\r\n  }\r\n\r\n  showSuccess(message: string, duration = 3000) {\r\n    this.show(message, 'success', duration);\r\n  }\r\n\r\n  showError(message: string, duration = 5000) {\r\n    this.show(message, 'error', duration);\r\n  }\r\n\r\n  showWarning(message: string, duration = 4000) {\r\n    this.show(message, 'warning', duration);\r\n  }\r\n\r\n  showInfo(message: string, duration = 3000) {\r\n    this.show(message, 'info', duration);\r\n  }\r\n\r\n  dismiss(id: number) {\r\n    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);\r\n    this.toastsSubject.next(currentToasts);\r\n  }\r\n\r\n  clear() {\r\n    this.toastsSubject.next([]);\r\n  }\r\n}"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAKtC,OAAM,MAAOC,YAAY;EAKvBC,YAAA;IAJQ,KAAAC,aAAa,GAAG,IAAIH,eAAe,CAAU,EAAE,CAAC;IACxD,KAAAI,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEL;EAEhBC,IAAIA,CAACC,OAAe,EAAEC,IAAA,GAAiD,MAAM,EAAEC,QAAQ,GAAG,IAAI;IAC5F,MAAMC,EAAE,GAAG,IAAI,CAACL,SAAS,EAAE;IAC3B,MAAMM,KAAK,GAAU;MAAED,EAAE;MAAEF,IAAI;MAAED,OAAO;MAAEE;IAAQ,CAAE;IACpD,MAAMG,aAAa,GAAG,IAAI,CAACV,aAAa,CAACW,KAAK;IAC9C,IAAI,CAACX,aAAa,CAACY,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAElD,IAAIF,QAAQ,GAAG,CAAC,EAAE;MAChBM,UAAU,CAAC,MAAM,IAAI,CAACC,OAAO,CAACN,EAAE,CAAC,EAAED,QAAQ,CAAC;;EAEhD;EAEAQ,WAAWA,CAACV,OAAe,EAAEE,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAS,SAASA,CAACX,OAAe,EAAEE,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEE,QAAQ,CAAC;EACvC;EAEAU,WAAWA,CAACZ,OAAe,EAAEE,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EACzC;EAEAW,QAAQA,CAACb,OAAe,EAAEE,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACH,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEE,QAAQ,CAAC;EACtC;EAEAO,OAAOA,CAACN,EAAU;IAChB,MAAME,aAAa,GAAG,IAAI,CAACV,aAAa,CAACW,KAAK,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKA,EAAE,CAAC;IACvE,IAAI,CAACR,aAAa,CAACY,IAAI,CAACF,aAAa,CAAC;EACxC;EAEAW,KAAKA,CAAA;IACH,IAAI,CAACrB,aAAa,CAACY,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBAzCWd,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAwB,OAAA,EAAZxB,YAAY,CAAAyB,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}