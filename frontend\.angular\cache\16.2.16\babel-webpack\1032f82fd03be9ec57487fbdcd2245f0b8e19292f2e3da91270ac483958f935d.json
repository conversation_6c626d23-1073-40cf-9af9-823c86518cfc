{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * GraphQL Value literals.\n *\n * Returns `undefined` when the value could not be validly coerced according to\n * the provided type.\n *\n * | GraphQL Value        | JSON Value    |\n * | -------------------- | ------------- |\n * | Input Object         | Object        |\n * | List                 | Array         |\n * | Boolean              | Boolean       |\n * | String               | String        |\n * | Int / Float          | Number        |\n * | Enum Value           | Unknown       |\n * | NullValue            | null          |\n *\n */\n\nexport function valueFromAST(valueNode, type, variables) {\n  if (!valueNode) {\n    // When there is no node, then there is also no value.\n    // Importantly, this is different from returning the value null.\n    return;\n  }\n  if (valueNode.kind === Kind.VARIABLE) {\n    const variableName = valueNode.name.value;\n    if (variables == null || variables[variableName] === undefined) {\n      // No valid return value.\n      return;\n    }\n    const variableValue = variables[variableName];\n    if (variableValue === null && isNonNullType(type)) {\n      return; // Invalid: intentionally return no value.\n    } // Note: This does no further checking that this variable is correct.\n    // This assumes that this query has been validated and the variable\n    // usage here is of the correct type.\n\n    return variableValue;\n  }\n  if (isNonNullType(type)) {\n    if (valueNode.kind === Kind.NULL) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return valueFromAST(valueNode, type.ofType, variables);\n  }\n  if (valueNode.kind === Kind.NULL) {\n    // This is explicitly returning the value null.\n    return null;\n  }\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (valueNode.kind === Kind.LIST) {\n      const coercedValues = [];\n      for (const itemNode of valueNode.values) {\n        if (isMissingVariable(itemNode, variables)) {\n          // If an array contains a missing variable, it is either coerced to\n          // null or if the item type is non-null, it considered invalid.\n          if (isNonNullType(itemType)) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(null);\n        } else {\n          const itemValue = valueFromAST(itemNode, itemType, variables);\n          if (itemValue === undefined) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(itemValue);\n        }\n      }\n      return coercedValues;\n    }\n    const coercedValue = valueFromAST(valueNode, itemType, variables);\n    if (coercedValue === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return [coercedValue];\n  }\n  if (isInputObjectType(type)) {\n    if (valueNode.kind !== Kind.OBJECT) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    const coercedObj = Object.create(null);\n    const fieldNodes = keyMap(valueNode.fields, field => field.name.value);\n    for (const field of Object.values(type.getFields())) {\n      const fieldNode = fieldNodes[field.name];\n      if (!fieldNode || isMissingVariable(fieldNode.value, variables)) {\n        if (field.defaultValue !== undefined) {\n          coercedObj[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          return; // Invalid: intentionally return no value.\n        }\n\n        continue;\n      }\n      const fieldValue = valueFromAST(fieldNode.value, field.type, variables);\n      if (fieldValue === undefined) {\n        return; // Invalid: intentionally return no value.\n      }\n\n      coercedObj[field.name] = fieldValue;\n    }\n    return coercedObj;\n  }\n  if (isLeafType(type)) {\n    // Scalars and Enums fulfill parsing a literal value via parseLiteral().\n    // Invalid values represent a failure to parse correctly, in which case\n    // no value is returned.\n    let result;\n    try {\n      result = type.parseLiteral(valueNode, variables);\n    } catch (_error) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    if (result === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return result;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible input types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n} // Returns true if the provided valueNode is a variable which is not defined\n// in the set of variables.\n\nfunction isMissingVariable(valueNode, variables) {\n  return valueNode.kind === Kind.VARIABLE && (variables == null || variables[valueNode.name.value] === undefined);\n}", "map": {"version": 3, "names": ["inspect", "invariant", "keyMap", "Kind", "isInputObjectType", "isLeafType", "isListType", "isNonNullType", "valueFromAST", "valueNode", "type", "variables", "kind", "VARIABLE", "variableName", "name", "value", "undefined", "variableValue", "NULL", "ofType", "itemType", "LIST", "coer<PERSON><PERSON><PERSON><PERSON>", "itemNode", "values", "isMissingVariable", "push", "itemValue", "coerced<PERSON><PERSON><PERSON>", "OBJECT", "coerced<PERSON><PERSON>j", "Object", "create", "fieldNodes", "fields", "field", "getFields", "fieldNode", "defaultValue", "fieldValue", "result", "parseLiteral", "_error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/utilities/valueFromAST.mjs"], "sourcesContent": ["import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport {\n  isInputObjectType,\n  isLeafType,\n  isListType,\n  isNonNullType,\n} from '../type/definition.mjs';\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * GraphQL Value literals.\n *\n * Returns `undefined` when the value could not be validly coerced according to\n * the provided type.\n *\n * | GraphQL Value        | JSON Value    |\n * | -------------------- | ------------- |\n * | Input Object         | Object        |\n * | List                 | Array         |\n * | Boolean              | Boolean       |\n * | String               | String        |\n * | Int / Float          | Number        |\n * | Enum Value           | Unknown       |\n * | NullValue            | null          |\n *\n */\n\nexport function valueFromAST(valueNode, type, variables) {\n  if (!valueNode) {\n    // When there is no node, then there is also no value.\n    // Importantly, this is different from returning the value null.\n    return;\n  }\n\n  if (valueNode.kind === Kind.VARIABLE) {\n    const variableName = valueNode.name.value;\n\n    if (variables == null || variables[variableName] === undefined) {\n      // No valid return value.\n      return;\n    }\n\n    const variableValue = variables[variableName];\n\n    if (variableValue === null && isNonNullType(type)) {\n      return; // Invalid: intentionally return no value.\n    } // Note: This does no further checking that this variable is correct.\n    // This assumes that this query has been validated and the variable\n    // usage here is of the correct type.\n\n    return variableValue;\n  }\n\n  if (isNonNullType(type)) {\n    if (valueNode.kind === Kind.NULL) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return valueFromAST(valueNode, type.ofType, variables);\n  }\n\n  if (valueNode.kind === Kind.NULL) {\n    // This is explicitly returning the value null.\n    return null;\n  }\n\n  if (isListType(type)) {\n    const itemType = type.ofType;\n\n    if (valueNode.kind === Kind.LIST) {\n      const coercedValues = [];\n\n      for (const itemNode of valueNode.values) {\n        if (isMissingVariable(itemNode, variables)) {\n          // If an array contains a missing variable, it is either coerced to\n          // null or if the item type is non-null, it considered invalid.\n          if (isNonNullType(itemType)) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(null);\n        } else {\n          const itemValue = valueFromAST(itemNode, itemType, variables);\n\n          if (itemValue === undefined) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(itemValue);\n        }\n      }\n\n      return coercedValues;\n    }\n\n    const coercedValue = valueFromAST(valueNode, itemType, variables);\n\n    if (coercedValue === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return [coercedValue];\n  }\n\n  if (isInputObjectType(type)) {\n    if (valueNode.kind !== Kind.OBJECT) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    const coercedObj = Object.create(null);\n    const fieldNodes = keyMap(valueNode.fields, (field) => field.name.value);\n\n    for (const field of Object.values(type.getFields())) {\n      const fieldNode = fieldNodes[field.name];\n\n      if (!fieldNode || isMissingVariable(fieldNode.value, variables)) {\n        if (field.defaultValue !== undefined) {\n          coercedObj[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          return; // Invalid: intentionally return no value.\n        }\n\n        continue;\n      }\n\n      const fieldValue = valueFromAST(fieldNode.value, field.type, variables);\n\n      if (fieldValue === undefined) {\n        return; // Invalid: intentionally return no value.\n      }\n\n      coercedObj[field.name] = fieldValue;\n    }\n\n    return coercedObj;\n  }\n\n  if (isLeafType(type)) {\n    // Scalars and Enums fulfill parsing a literal value via parseLiteral().\n    // Invalid values represent a failure to parse correctly, in which case\n    // no value is returned.\n    let result;\n\n    try {\n      result = type.parseLiteral(valueNode, variables);\n    } catch (_error) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    if (result === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return result;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible input types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n} // Returns true if the provided valueNode is a variable which is not defined\n// in the set of variables.\n\nfunction isMissingVariable(valueNode, variables) {\n  return (\n    valueNode.kind === Kind.VARIABLE &&\n    (variables == null || variables[valueNode.name.value] === undefined)\n  );\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SACEC,iBAAiB,EACjBC,UAAU,EACVC,UAAU,EACVC,aAAa,QACR,wBAAwB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACvD,IAAI,CAACF,SAAS,EAAE;IACd;IACA;IACA;EACF;EAEA,IAAIA,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACU,QAAQ,EAAE;IACpC,MAAMC,YAAY,GAAGL,SAAS,CAACM,IAAI,CAACC,KAAK;IAEzC,IAAIL,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACG,YAAY,CAAC,KAAKG,SAAS,EAAE;MAC9D;MACA;IACF;IAEA,MAAMC,aAAa,GAAGP,SAAS,CAACG,YAAY,CAAC;IAE7C,IAAII,aAAa,KAAK,IAAI,IAAIX,aAAa,CAACG,IAAI,CAAC,EAAE;MACjD,OAAO,CAAC;IACV,CAAC,CAAC;IACF;IACA;;IAEA,OAAOQ,aAAa;EACtB;EAEA,IAAIX,aAAa,CAACG,IAAI,CAAC,EAAE;IACvB,IAAID,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACgB,IAAI,EAAE;MAChC,OAAO,CAAC;IACV;;IAEA,OAAOX,YAAY,CAACC,SAAS,EAAEC,IAAI,CAACU,MAAM,EAAET,SAAS,CAAC;EACxD;EAEA,IAAIF,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACgB,IAAI,EAAE;IAChC;IACA,OAAO,IAAI;EACb;EAEA,IAAIb,UAAU,CAACI,IAAI,CAAC,EAAE;IACpB,MAAMW,QAAQ,GAAGX,IAAI,CAACU,MAAM;IAE5B,IAAIX,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACmB,IAAI,EAAE;MAChC,MAAMC,aAAa,GAAG,EAAE;MAExB,KAAK,MAAMC,QAAQ,IAAIf,SAAS,CAACgB,MAAM,EAAE;QACvC,IAAIC,iBAAiB,CAACF,QAAQ,EAAEb,SAAS,CAAC,EAAE;UAC1C;UACA;UACA,IAAIJ,aAAa,CAACc,QAAQ,CAAC,EAAE;YAC3B,OAAO,CAAC;UACV;;UAEAE,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC;QAC1B,CAAC,MAAM;UACL,MAAMC,SAAS,GAAGpB,YAAY,CAACgB,QAAQ,EAAEH,QAAQ,EAAEV,SAAS,CAAC;UAE7D,IAAIiB,SAAS,KAAKX,SAAS,EAAE;YAC3B,OAAO,CAAC;UACV;;UAEAM,aAAa,CAACI,IAAI,CAACC,SAAS,CAAC;QAC/B;MACF;MAEA,OAAOL,aAAa;IACtB;IAEA,MAAMM,YAAY,GAAGrB,YAAY,CAACC,SAAS,EAAEY,QAAQ,EAAEV,SAAS,CAAC;IAEjE,IAAIkB,YAAY,KAAKZ,SAAS,EAAE;MAC9B,OAAO,CAAC;IACV;;IAEA,OAAO,CAACY,YAAY,CAAC;EACvB;EAEA,IAAIzB,iBAAiB,CAACM,IAAI,CAAC,EAAE;IAC3B,IAAID,SAAS,CAACG,IAAI,KAAKT,IAAI,CAAC2B,MAAM,EAAE;MAClC,OAAO,CAAC;IACV;;IAEA,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACtC,MAAMC,UAAU,GAAGhC,MAAM,CAACO,SAAS,CAAC0B,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACrB,IAAI,CAACC,KAAK,CAAC;IAExE,KAAK,MAAMoB,KAAK,IAAIJ,MAAM,CAACP,MAAM,CAACf,IAAI,CAAC2B,SAAS,CAAC,CAAC,CAAC,EAAE;MACnD,MAAMC,SAAS,GAAGJ,UAAU,CAACE,KAAK,CAACrB,IAAI,CAAC;MAExC,IAAI,CAACuB,SAAS,IAAIZ,iBAAiB,CAACY,SAAS,CAACtB,KAAK,EAAEL,SAAS,CAAC,EAAE;QAC/D,IAAIyB,KAAK,CAACG,YAAY,KAAKtB,SAAS,EAAE;UACpCc,UAAU,CAACK,KAAK,CAACrB,IAAI,CAAC,GAAGqB,KAAK,CAACG,YAAY;QAC7C,CAAC,MAAM,IAAIhC,aAAa,CAAC6B,KAAK,CAAC1B,IAAI,CAAC,EAAE;UACpC,OAAO,CAAC;QACV;;QAEA;MACF;MAEA,MAAM8B,UAAU,GAAGhC,YAAY,CAAC8B,SAAS,CAACtB,KAAK,EAAEoB,KAAK,CAAC1B,IAAI,EAAEC,SAAS,CAAC;MAEvE,IAAI6B,UAAU,KAAKvB,SAAS,EAAE;QAC5B,OAAO,CAAC;MACV;;MAEAc,UAAU,CAACK,KAAK,CAACrB,IAAI,CAAC,GAAGyB,UAAU;IACrC;IAEA,OAAOT,UAAU;EACnB;EAEA,IAAI1B,UAAU,CAACK,IAAI,CAAC,EAAE;IACpB;IACA;IACA;IACA,IAAI+B,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG/B,IAAI,CAACgC,YAAY,CAACjC,SAAS,EAAEE,SAAS,CAAC;IAClD,CAAC,CAAC,OAAOgC,MAAM,EAAE;MACf,OAAO,CAAC;IACV;;IAEA,IAAIF,MAAM,KAAKxB,SAAS,EAAE;MACxB,OAAO,CAAC;IACV;;IAEA,OAAOwB,MAAM;EACf;EACA;EACA;;EAEA,KAAK,IAAIxC,SAAS,CAAC,KAAK,EAAE,yBAAyB,GAAGD,OAAO,CAACU,IAAI,CAAC,CAAC;AACtE,CAAC,CAAC;AACF;;AAEA,SAASgB,iBAAiBA,CAACjB,SAAS,EAAEE,SAAS,EAAE;EAC/C,OACEF,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACU,QAAQ,KAC/BF,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACF,SAAS,CAACM,IAAI,CAACC,KAAK,CAAC,KAAKC,SAAS,CAAC;AAExE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}