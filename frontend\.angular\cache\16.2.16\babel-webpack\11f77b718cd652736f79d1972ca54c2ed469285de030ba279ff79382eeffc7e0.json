{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Observable } from \"./Observable.js\";\nimport { iterateObserversSafely } from \"./iteration.js\";\nimport { fixObservableSubclass } from \"./subclassing.js\";\nfunction isPromiseLike(value) {\n  return value && typeof value.then === \"function\";\n}\n// A Concast<T> observable concatenates the given sources into a single\n// non-overlapping sequence of Ts, automatically unwrapping any promises,\n// and broadcasts the T elements of that sequence to any number of\n// subscribers, all without creating a bunch of intermediary Observable\n// wrapper objects.\n//\n// Even though any number of observers can subscribe to the Concast, each\n// source observable is guaranteed to receive at most one subscribe call,\n// and the results are multicast to all observers.\n//\n// In addition to broadcasting every next/error message to this.observers,\n// the Concast stores the most recent message using this.latest, so any\n// new observers can immediately receive the latest message, even if it\n// was originally delivered in the past. This behavior means we can assume\n// every active observer in this.observers has received the same most\n// recent message.\n//\n// With the exception of this.latest replay, a Concast is a \"hot\"\n// observable in the sense that it does not replay past results from the\n// beginning of time for each new observer.\n//\n// Could we have used some existing RxJS class instead? Concast<T> is\n// similar to a BehaviorSubject<T>, because it is multicast and redelivers\n// the latest next/error message to new subscribers. Unlike Subject<T>,\n// Concast<T> does not expose an Observer<T> interface (this.handlers is\n// intentionally private), since Concast<T> gets its inputs from the\n// concatenated sources. If we ever switch to RxJS, there may be some\n// value in reusing their code, but for now we use zen-observable, which\n// does not contain any Subject implementations.\nvar Concast = /** @class */function (_super) {\n  __extends(Concast, _super);\n  // Not only can the individual elements of the iterable be promises, but\n  // also the iterable itself can be wrapped in a promise.\n  function Concast(sources) {\n    var _this = _super.call(this, function (observer) {\n      _this.addObserver(observer);\n      return function () {\n        return _this.removeObserver(observer);\n      };\n    }) || this;\n    // Active observers receiving broadcast messages. Thanks to this.latest,\n    // we can assume all observers in this Set have received the same most\n    // recent message, though possibly at different times in the past.\n    _this.observers = new Set();\n    _this.promise = new Promise(function (resolve, reject) {\n      _this.resolve = resolve;\n      _this.reject = reject;\n    });\n    // Bound handler functions that can be reused for every internal\n    // subscription.\n    _this.handlers = {\n      next: function (result) {\n        if (_this.sub !== null) {\n          _this.latest = [\"next\", result];\n          _this.notify(\"next\", result);\n          iterateObserversSafely(_this.observers, \"next\", result);\n        }\n      },\n      error: function (error) {\n        var sub = _this.sub;\n        if (sub !== null) {\n          // Delay unsubscribing from the underlying subscription slightly,\n          // so that immediately subscribing another observer can keep the\n          // subscription active.\n          if (sub) setTimeout(function () {\n            return sub.unsubscribe();\n          });\n          _this.sub = null;\n          _this.latest = [\"error\", error];\n          _this.reject(error);\n          _this.notify(\"error\", error);\n          iterateObserversSafely(_this.observers, \"error\", error);\n        }\n      },\n      complete: function () {\n        var _a = _this,\n          sub = _a.sub,\n          _b = _a.sources,\n          sources = _b === void 0 ? [] : _b;\n        if (sub !== null) {\n          // If complete is called before concast.start, this.sources may be\n          // undefined, so we use a default value of [] for sources. That works\n          // here because it falls into the if (!value) {...} block, which\n          // appropriately terminates the Concast, even if this.sources might\n          // eventually have been initialized to a non-empty array.\n          var value = sources.shift();\n          if (!value) {\n            if (sub) setTimeout(function () {\n              return sub.unsubscribe();\n            });\n            _this.sub = null;\n            if (_this.latest && _this.latest[0] === \"next\") {\n              _this.resolve(_this.latest[1]);\n            } else {\n              _this.resolve();\n            }\n            _this.notify(\"complete\");\n            // We do not store this.latest = [\"complete\"], because doing so\n            // discards useful information about the previous next (or\n            // error) message. Instead, if new observers subscribe after\n            // this Concast has completed, they will receive the final\n            // 'next' message (unless there was an error) immediately\n            // followed by a 'complete' message (see addObserver).\n            iterateObserversSafely(_this.observers, \"complete\");\n          } else if (isPromiseLike(value)) {\n            value.then(function (obs) {\n              return _this.sub = obs.subscribe(_this.handlers);\n            }, _this.handlers.error);\n          } else {\n            _this.sub = value.subscribe(_this.handlers);\n          }\n        }\n      }\n    };\n    _this.nextResultListeners = new Set();\n    // A public way to abort observation and broadcast.\n    _this.cancel = function (reason) {\n      _this.reject(reason);\n      _this.sources = [];\n      _this.handlers.error(reason);\n    };\n    // Suppress rejection warnings for this.promise, since it's perfectly\n    // acceptable to pay no attention to this.promise if you're consuming\n    // the results through the normal observable API.\n    _this.promise.catch(function (_) {});\n    // If someone accidentally tries to create a Concast using a subscriber\n    // function, recover by creating an Observable from that subscriber and\n    // using it as the source.\n    if (typeof sources === \"function\") {\n      sources = [new Observable(sources)];\n    }\n    if (isPromiseLike(sources)) {\n      sources.then(function (iterable) {\n        return _this.start(iterable);\n      }, _this.handlers.error);\n    } else {\n      _this.start(sources);\n    }\n    return _this;\n  }\n  Concast.prototype.start = function (sources) {\n    if (this.sub !== void 0) return;\n    // In practice, sources is most often simply an Array of observables.\n    // TODO Consider using sources[Symbol.iterator]() to take advantage\n    // of the laziness of non-Array iterables.\n    this.sources = Array.from(sources);\n    // Calling this.handlers.complete() kicks off consumption of the first\n    // source observable. It's tempting to do this step lazily in\n    // addObserver, but this.promise can be accessed without calling\n    // addObserver, so consumption needs to begin eagerly.\n    this.handlers.complete();\n  };\n  Concast.prototype.deliverLastMessage = function (observer) {\n    if (this.latest) {\n      var nextOrError = this.latest[0];\n      var method = observer[nextOrError];\n      if (method) {\n        method.call(observer, this.latest[1]);\n      }\n      // If the subscription is already closed, and the last message was\n      // a 'next' message, simulate delivery of the final 'complete'\n      // message again.\n      if (this.sub === null && nextOrError === \"next\" && observer.complete) {\n        observer.complete();\n      }\n    }\n  };\n  Concast.prototype.addObserver = function (observer) {\n    if (!this.observers.has(observer)) {\n      // Immediately deliver the most recent message, so we can always\n      // be sure all observers have the latest information.\n      this.deliverLastMessage(observer);\n      this.observers.add(observer);\n    }\n  };\n  Concast.prototype.removeObserver = function (observer) {\n    if (this.observers.delete(observer) && this.observers.size < 1) {\n      // In case there are still any listeners in this.nextResultListeners, and\n      // no error or completion has been broadcast yet, make sure those\n      // observers have a chance to run and then remove themselves from\n      // this.observers.\n      this.handlers.complete();\n    }\n  };\n  Concast.prototype.notify = function (method, arg) {\n    var nextResultListeners = this.nextResultListeners;\n    if (nextResultListeners.size) {\n      // Replacing this.nextResultListeners first ensures it does not grow while\n      // we are iterating over it, potentially leading to infinite loops.\n      this.nextResultListeners = new Set();\n      nextResultListeners.forEach(function (listener) {\n        return listener(method, arg);\n      });\n    }\n  };\n  // We need a way to run callbacks just *before* the next result (or error or\n  // completion) is delivered by this Concast, so we can be sure any code that\n  // runs as a result of delivering that result/error observes the effects of\n  // running the callback(s). It was tempting to reuse the Observer type instead\n  // of introducing NextResultListener, but that messes with the sizing and\n  // maintenance of this.observers, and ends up being more code overall.\n  Concast.prototype.beforeNext = function (callback) {\n    var called = false;\n    this.nextResultListeners.add(function (method, arg) {\n      if (!called) {\n        called = true;\n        callback(method, arg);\n      }\n    });\n  };\n  return Concast;\n}(Observable);\nexport { Concast };\n// Necessary because the Concast constructor has a different signature\n// than the Observable constructor.\nfixObservableSubclass(Concast);", "map": {"version": 3, "names": ["__extends", "Observable", "iterateObserversSafely", "fixObservableSubclass", "isPromiseLike", "value", "then", "Concast", "_super", "sources", "_this", "call", "observer", "addObserver", "removeObserver", "observers", "Set", "promise", "Promise", "resolve", "reject", "handlers", "next", "result", "sub", "latest", "notify", "error", "setTimeout", "unsubscribe", "complete", "_a", "_b", "shift", "obs", "subscribe", "nextResultListeners", "cancel", "reason", "catch", "_", "iterable", "start", "prototype", "Array", "from", "deliverLastMessage", "nextOrError", "method", "has", "add", "delete", "size", "arg", "for<PERSON>ach", "listener", "beforeNext", "callback", "called"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/observables/Concast.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { Observable } from \"./Observable.js\";\nimport { iterateObserversSafely } from \"./iteration.js\";\nimport { fixObservableSubclass } from \"./subclassing.js\";\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// A Concast<T> observable concatenates the given sources into a single\n// non-overlapping sequence of Ts, automatically unwrapping any promises,\n// and broadcasts the T elements of that sequence to any number of\n// subscribers, all without creating a bunch of intermediary Observable\n// wrapper objects.\n//\n// Even though any number of observers can subscribe to the Concast, each\n// source observable is guaranteed to receive at most one subscribe call,\n// and the results are multicast to all observers.\n//\n// In addition to broadcasting every next/error message to this.observers,\n// the Concast stores the most recent message using this.latest, so any\n// new observers can immediately receive the latest message, even if it\n// was originally delivered in the past. This behavior means we can assume\n// every active observer in this.observers has received the same most\n// recent message.\n//\n// With the exception of this.latest replay, a Concast is a \"hot\"\n// observable in the sense that it does not replay past results from the\n// beginning of time for each new observer.\n//\n// Could we have used some existing RxJS class instead? Concast<T> is\n// similar to a BehaviorSubject<T>, because it is multicast and redelivers\n// the latest next/error message to new subscribers. Unlike Subject<T>,\n// Concast<T> does not expose an Observer<T> interface (this.handlers is\n// intentionally private), since Concast<T> gets its inputs from the\n// concatenated sources. If we ever switch to RxJS, there may be some\n// value in reusing their code, but for now we use zen-observable, which\n// does not contain any Subject implementations.\nvar Concast = /** @class */ (function (_super) {\n    __extends(Concast, _super);\n    // Not only can the individual elements of the iterable be promises, but\n    // also the iterable itself can be wrapped in a promise.\n    function Concast(sources) {\n        var _this = _super.call(this, function (observer) {\n            _this.addObserver(observer);\n            return function () { return _this.removeObserver(observer); };\n        }) || this;\n        // Active observers receiving broadcast messages. Thanks to this.latest,\n        // we can assume all observers in this Set have received the same most\n        // recent message, though possibly at different times in the past.\n        _this.observers = new Set();\n        _this.promise = new Promise(function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        });\n        // Bound handler functions that can be reused for every internal\n        // subscription.\n        _this.handlers = {\n            next: function (result) {\n                if (_this.sub !== null) {\n                    _this.latest = [\"next\", result];\n                    _this.notify(\"next\", result);\n                    iterateObserversSafely(_this.observers, \"next\", result);\n                }\n            },\n            error: function (error) {\n                var sub = _this.sub;\n                if (sub !== null) {\n                    // Delay unsubscribing from the underlying subscription slightly,\n                    // so that immediately subscribing another observer can keep the\n                    // subscription active.\n                    if (sub)\n                        setTimeout(function () { return sub.unsubscribe(); });\n                    _this.sub = null;\n                    _this.latest = [\"error\", error];\n                    _this.reject(error);\n                    _this.notify(\"error\", error);\n                    iterateObserversSafely(_this.observers, \"error\", error);\n                }\n            },\n            complete: function () {\n                var _a = _this, sub = _a.sub, _b = _a.sources, sources = _b === void 0 ? [] : _b;\n                if (sub !== null) {\n                    // If complete is called before concast.start, this.sources may be\n                    // undefined, so we use a default value of [] for sources. That works\n                    // here because it falls into the if (!value) {...} block, which\n                    // appropriately terminates the Concast, even if this.sources might\n                    // eventually have been initialized to a non-empty array.\n                    var value = sources.shift();\n                    if (!value) {\n                        if (sub)\n                            setTimeout(function () { return sub.unsubscribe(); });\n                        _this.sub = null;\n                        if (_this.latest && _this.latest[0] === \"next\") {\n                            _this.resolve(_this.latest[1]);\n                        }\n                        else {\n                            _this.resolve();\n                        }\n                        _this.notify(\"complete\");\n                        // We do not store this.latest = [\"complete\"], because doing so\n                        // discards useful information about the previous next (or\n                        // error) message. Instead, if new observers subscribe after\n                        // this Concast has completed, they will receive the final\n                        // 'next' message (unless there was an error) immediately\n                        // followed by a 'complete' message (see addObserver).\n                        iterateObserversSafely(_this.observers, \"complete\");\n                    }\n                    else if (isPromiseLike(value)) {\n                        value.then(function (obs) { return (_this.sub = obs.subscribe(_this.handlers)); }, _this.handlers.error);\n                    }\n                    else {\n                        _this.sub = value.subscribe(_this.handlers);\n                    }\n                }\n            },\n        };\n        _this.nextResultListeners = new Set();\n        // A public way to abort observation and broadcast.\n        _this.cancel = function (reason) {\n            _this.reject(reason);\n            _this.sources = [];\n            _this.handlers.error(reason);\n        };\n        // Suppress rejection warnings for this.promise, since it's perfectly\n        // acceptable to pay no attention to this.promise if you're consuming\n        // the results through the normal observable API.\n        _this.promise.catch(function (_) { });\n        // If someone accidentally tries to create a Concast using a subscriber\n        // function, recover by creating an Observable from that subscriber and\n        // using it as the source.\n        if (typeof sources === \"function\") {\n            sources = [new Observable(sources)];\n        }\n        if (isPromiseLike(sources)) {\n            sources.then(function (iterable) { return _this.start(iterable); }, _this.handlers.error);\n        }\n        else {\n            _this.start(sources);\n        }\n        return _this;\n    }\n    Concast.prototype.start = function (sources) {\n        if (this.sub !== void 0)\n            return;\n        // In practice, sources is most often simply an Array of observables.\n        // TODO Consider using sources[Symbol.iterator]() to take advantage\n        // of the laziness of non-Array iterables.\n        this.sources = Array.from(sources);\n        // Calling this.handlers.complete() kicks off consumption of the first\n        // source observable. It's tempting to do this step lazily in\n        // addObserver, but this.promise can be accessed without calling\n        // addObserver, so consumption needs to begin eagerly.\n        this.handlers.complete();\n    };\n    Concast.prototype.deliverLastMessage = function (observer) {\n        if (this.latest) {\n            var nextOrError = this.latest[0];\n            var method = observer[nextOrError];\n            if (method) {\n                method.call(observer, this.latest[1]);\n            }\n            // If the subscription is already closed, and the last message was\n            // a 'next' message, simulate delivery of the final 'complete'\n            // message again.\n            if (this.sub === null && nextOrError === \"next\" && observer.complete) {\n                observer.complete();\n            }\n        }\n    };\n    Concast.prototype.addObserver = function (observer) {\n        if (!this.observers.has(observer)) {\n            // Immediately deliver the most recent message, so we can always\n            // be sure all observers have the latest information.\n            this.deliverLastMessage(observer);\n            this.observers.add(observer);\n        }\n    };\n    Concast.prototype.removeObserver = function (observer) {\n        if (this.observers.delete(observer) && this.observers.size < 1) {\n            // In case there are still any listeners in this.nextResultListeners, and\n            // no error or completion has been broadcast yet, make sure those\n            // observers have a chance to run and then remove themselves from\n            // this.observers.\n            this.handlers.complete();\n        }\n    };\n    Concast.prototype.notify = function (method, arg) {\n        var nextResultListeners = this.nextResultListeners;\n        if (nextResultListeners.size) {\n            // Replacing this.nextResultListeners first ensures it does not grow while\n            // we are iterating over it, potentially leading to infinite loops.\n            this.nextResultListeners = new Set();\n            nextResultListeners.forEach(function (listener) { return listener(method, arg); });\n        }\n    };\n    // We need a way to run callbacks just *before* the next result (or error or\n    // completion) is delivered by this Concast, so we can be sure any code that\n    // runs as a result of delivering that result/error observes the effects of\n    // running the callback(s). It was tempting to reuse the Observer type instead\n    // of introducing NextResultListener, but that messes with the sizing and\n    // maintenance of this.observers, and ends up being more code overall.\n    Concast.prototype.beforeNext = function (callback) {\n        var called = false;\n        this.nextResultListeners.add(function (method, arg) {\n            if (!called) {\n                called = true;\n                callback(method, arg);\n            }\n        });\n    };\n    return Concast;\n}(Observable));\nexport { Concast };\n// Necessary because the Concast constructor has a different signature\n// than the Observable constructor.\nfixObservableSubclass(Concast);\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,sBAAsB,QAAQ,gBAAgB;AACvD,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC3CR,SAAS,CAACO,OAAO,EAAEC,MAAM,CAAC;EAC1B;EACA;EACA,SAASD,OAAOA,CAACE,OAAO,EAAE;IACtB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAE,UAAUC,QAAQ,EAAE;MAC9CF,KAAK,CAACG,WAAW,CAACD,QAAQ,CAAC;MAC3B,OAAO,YAAY;QAAE,OAAOF,KAAK,CAACI,cAAc,CAACF,QAAQ,CAAC;MAAE,CAAC;IACjE,CAAC,CAAC,IAAI,IAAI;IACV;IACA;IACA;IACAF,KAAK,CAACK,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3BN,KAAK,CAACO,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MACnDV,KAAK,CAACS,OAAO,GAAGA,OAAO;MACvBT,KAAK,CAACU,MAAM,GAAGA,MAAM;IACzB,CAAC,CAAC;IACF;IACA;IACAV,KAAK,CAACW,QAAQ,GAAG;MACbC,IAAI,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACpB,IAAIb,KAAK,CAACc,GAAG,KAAK,IAAI,EAAE;UACpBd,KAAK,CAACe,MAAM,GAAG,CAAC,MAAM,EAAEF,MAAM,CAAC;UAC/Bb,KAAK,CAACgB,MAAM,CAAC,MAAM,EAAEH,MAAM,CAAC;UAC5BrB,sBAAsB,CAACQ,KAAK,CAACK,SAAS,EAAE,MAAM,EAAEQ,MAAM,CAAC;QAC3D;MACJ,CAAC;MACDI,KAAK,EAAE,SAAAA,CAAUA,KAAK,EAAE;QACpB,IAAIH,GAAG,GAAGd,KAAK,CAACc,GAAG;QACnB,IAAIA,GAAG,KAAK,IAAI,EAAE;UACd;UACA;UACA;UACA,IAAIA,GAAG,EACHI,UAAU,CAAC,YAAY;YAAE,OAAOJ,GAAG,CAACK,WAAW,CAAC,CAAC;UAAE,CAAC,CAAC;UACzDnB,KAAK,CAACc,GAAG,GAAG,IAAI;UAChBd,KAAK,CAACe,MAAM,GAAG,CAAC,OAAO,EAAEE,KAAK,CAAC;UAC/BjB,KAAK,CAACU,MAAM,CAACO,KAAK,CAAC;UACnBjB,KAAK,CAACgB,MAAM,CAAC,OAAO,EAAEC,KAAK,CAAC;UAC5BzB,sBAAsB,CAACQ,KAAK,CAACK,SAAS,EAAE,OAAO,EAAEY,KAAK,CAAC;QAC3D;MACJ,CAAC;MACDG,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,IAAIC,EAAE,GAAGrB,KAAK;UAAEc,GAAG,GAAGO,EAAE,CAACP,GAAG;UAAEQ,EAAE,GAAGD,EAAE,CAACtB,OAAO;UAAEA,OAAO,GAAGuB,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;QAChF,IAAIR,GAAG,KAAK,IAAI,EAAE;UACd;UACA;UACA;UACA;UACA;UACA,IAAInB,KAAK,GAAGI,OAAO,CAACwB,KAAK,CAAC,CAAC;UAC3B,IAAI,CAAC5B,KAAK,EAAE;YACR,IAAImB,GAAG,EACHI,UAAU,CAAC,YAAY;cAAE,OAAOJ,GAAG,CAACK,WAAW,CAAC,CAAC;YAAE,CAAC,CAAC;YACzDnB,KAAK,CAACc,GAAG,GAAG,IAAI;YAChB,IAAId,KAAK,CAACe,MAAM,IAAIf,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;cAC5Cf,KAAK,CAACS,OAAO,CAACT,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,MACI;cACDf,KAAK,CAACS,OAAO,CAAC,CAAC;YACnB;YACAT,KAAK,CAACgB,MAAM,CAAC,UAAU,CAAC;YACxB;YACA;YACA;YACA;YACA;YACA;YACAxB,sBAAsB,CAACQ,KAAK,CAACK,SAAS,EAAE,UAAU,CAAC;UACvD,CAAC,MACI,IAAIX,aAAa,CAACC,KAAK,CAAC,EAAE;YAC3BA,KAAK,CAACC,IAAI,CAAC,UAAU4B,GAAG,EAAE;cAAE,OAAQxB,KAAK,CAACc,GAAG,GAAGU,GAAG,CAACC,SAAS,CAACzB,KAAK,CAACW,QAAQ,CAAC;YAAG,CAAC,EAAEX,KAAK,CAACW,QAAQ,CAACM,KAAK,CAAC;UAC5G,CAAC,MACI;YACDjB,KAAK,CAACc,GAAG,GAAGnB,KAAK,CAAC8B,SAAS,CAACzB,KAAK,CAACW,QAAQ,CAAC;UAC/C;QACJ;MACJ;IACJ,CAAC;IACDX,KAAK,CAAC0B,mBAAmB,GAAG,IAAIpB,GAAG,CAAC,CAAC;IACrC;IACAN,KAAK,CAAC2B,MAAM,GAAG,UAAUC,MAAM,EAAE;MAC7B5B,KAAK,CAACU,MAAM,CAACkB,MAAM,CAAC;MACpB5B,KAAK,CAACD,OAAO,GAAG,EAAE;MAClBC,KAAK,CAACW,QAAQ,CAACM,KAAK,CAACW,MAAM,CAAC;IAChC,CAAC;IACD;IACA;IACA;IACA5B,KAAK,CAACO,OAAO,CAACsB,KAAK,CAAC,UAAUC,CAAC,EAAE,CAAE,CAAC,CAAC;IACrC;IACA;IACA;IACA,IAAI,OAAO/B,OAAO,KAAK,UAAU,EAAE;MAC/BA,OAAO,GAAG,CAAC,IAAIR,UAAU,CAACQ,OAAO,CAAC,CAAC;IACvC;IACA,IAAIL,aAAa,CAACK,OAAO,CAAC,EAAE;MACxBA,OAAO,CAACH,IAAI,CAAC,UAAUmC,QAAQ,EAAE;QAAE,OAAO/B,KAAK,CAACgC,KAAK,CAACD,QAAQ,CAAC;MAAE,CAAC,EAAE/B,KAAK,CAACW,QAAQ,CAACM,KAAK,CAAC;IAC7F,CAAC,MACI;MACDjB,KAAK,CAACgC,KAAK,CAACjC,OAAO,CAAC;IACxB;IACA,OAAOC,KAAK;EAChB;EACAH,OAAO,CAACoC,SAAS,CAACD,KAAK,GAAG,UAAUjC,OAAO,EAAE;IACzC,IAAI,IAAI,CAACe,GAAG,KAAK,KAAK,CAAC,EACnB;IACJ;IACA;IACA;IACA,IAAI,CAACf,OAAO,GAAGmC,KAAK,CAACC,IAAI,CAACpC,OAAO,CAAC;IAClC;IACA;IACA;IACA;IACA,IAAI,CAACY,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACDvB,OAAO,CAACoC,SAAS,CAACG,kBAAkB,GAAG,UAAUlC,QAAQ,EAAE;IACvD,IAAI,IAAI,CAACa,MAAM,EAAE;MACb,IAAIsB,WAAW,GAAG,IAAI,CAACtB,MAAM,CAAC,CAAC,CAAC;MAChC,IAAIuB,MAAM,GAAGpC,QAAQ,CAACmC,WAAW,CAAC;MAClC,IAAIC,MAAM,EAAE;QACRA,MAAM,CAACrC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACa,MAAM,CAAC,CAAC,CAAC,CAAC;MACzC;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACD,GAAG,KAAK,IAAI,IAAIuB,WAAW,KAAK,MAAM,IAAInC,QAAQ,CAACkB,QAAQ,EAAE;QAClElB,QAAQ,CAACkB,QAAQ,CAAC,CAAC;MACvB;IACJ;EACJ,CAAC;EACDvB,OAAO,CAACoC,SAAS,CAAC9B,WAAW,GAAG,UAAUD,QAAQ,EAAE;IAChD,IAAI,CAAC,IAAI,CAACG,SAAS,CAACkC,GAAG,CAACrC,QAAQ,CAAC,EAAE;MAC/B;MACA;MACA,IAAI,CAACkC,kBAAkB,CAAClC,QAAQ,CAAC;MACjC,IAAI,CAACG,SAAS,CAACmC,GAAG,CAACtC,QAAQ,CAAC;IAChC;EACJ,CAAC;EACDL,OAAO,CAACoC,SAAS,CAAC7B,cAAc,GAAG,UAAUF,QAAQ,EAAE;IACnD,IAAI,IAAI,CAACG,SAAS,CAACoC,MAAM,CAACvC,QAAQ,CAAC,IAAI,IAAI,CAACG,SAAS,CAACqC,IAAI,GAAG,CAAC,EAAE;MAC5D;MACA;MACA;MACA;MACA,IAAI,CAAC/B,QAAQ,CAACS,QAAQ,CAAC,CAAC;IAC5B;EACJ,CAAC;EACDvB,OAAO,CAACoC,SAAS,CAACjB,MAAM,GAAG,UAAUsB,MAAM,EAAEK,GAAG,EAAE;IAC9C,IAAIjB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB;IAClD,IAAIA,mBAAmB,CAACgB,IAAI,EAAE;MAC1B;MACA;MACA,IAAI,CAAChB,mBAAmB,GAAG,IAAIpB,GAAG,CAAC,CAAC;MACpCoB,mBAAmB,CAACkB,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAAE,OAAOA,QAAQ,CAACP,MAAM,EAAEK,GAAG,CAAC;MAAE,CAAC,CAAC;IACtF;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA9C,OAAO,CAACoC,SAAS,CAACa,UAAU,GAAG,UAAUC,QAAQ,EAAE;IAC/C,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAI,CAACtB,mBAAmB,CAACc,GAAG,CAAC,UAAUF,MAAM,EAAEK,GAAG,EAAE;MAChD,IAAI,CAACK,MAAM,EAAE;QACTA,MAAM,GAAG,IAAI;QACbD,QAAQ,CAACT,MAAM,EAAEK,GAAG,CAAC;MACzB;IACJ,CAAC,CAAC;EACN,CAAC;EACD,OAAO9C,OAAO;AAClB,CAAC,CAACN,UAAU,CAAE;AACd,SAASM,OAAO;AAChB;AACA;AACAJ,qBAAqB,CAACI,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}