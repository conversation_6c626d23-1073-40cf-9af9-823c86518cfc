{"ast": null, "code": "export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport { cacheSizes } from \"./sizes.js\";", "map": {"version": 3, "names": ["AutoCleanedStrongCache", "AutoCleanedWeakCache", "cacheSizes"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/caching/index.js"], "sourcesContent": ["export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport { cacheSizes } from \"./sizes.js\";\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,oBAAoB,QAAQ,aAAa;AAC1E,SAASC,UAAU,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}