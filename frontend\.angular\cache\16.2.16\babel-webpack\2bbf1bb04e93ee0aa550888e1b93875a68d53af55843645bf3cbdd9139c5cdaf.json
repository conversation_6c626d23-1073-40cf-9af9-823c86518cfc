{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/reunion.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@app/services/planning.service\";\nimport * as i6 from \"@angular/common\";\nfunction ReunionEditComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r8.titre);\n  }\n}\nfunction ReunionEditComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_ng_container_50_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r10._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r10.username);\n  }\n}\nfunction ReunionEditComponent_ng_container_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionEditComponent_ng_container_50_option_1_Template, 2, 2, \"option\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.users);\n  }\n}\nexport class ReunionEditComponent {\n  constructor(fb, route, router, reunionService, userService, planningService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.reunionService = reunionService;\n    this.userService = userService;\n    this.planningService = planningService;\n    this.error = null;\n    this.isSubmitting = false;\n    this.users = [];\n    this.plannings = [];\n  }\n  ngOnInit() {\n    this.reunionId = this.route.snapshot.paramMap.get('id');\n    this.initForm();\n    this.fetchUsers();\n    this.fetchPlannings();\n    this.loadReunion();\n  }\n  initForm() {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n  }\n  fetchUsers() {\n    this.userService.getAllUsers().subscribe(users => {\n      this.users = users;\n    });\n  }\n  fetchPlannings() {\n    this.planningService.getAllPlannings().subscribe(plannings => {\n      this.plannings = plannings.plannings;\n    });\n  }\n  loadReunion() {\n    this.reunionService.getReunionById(this.reunionId).subscribe({\n      next: reunion => {\n        this.reunionForm.patchValue({\n          titre: reunion.reunion.titre,\n          description: reunion.reunion.description,\n          date: reunion.reunion.date?.split('T')[0],\n          heureDebut: reunion.reunion.heureDebut,\n          heureFin: reunion.reunion.heureFin,\n          lieu: reunion.reunion.lieu,\n          lienVisio: reunion.reunion.lienVisio,\n          planning: reunion.reunion.planning?.id,\n          participants: reunion.reunion.participants?.map(p => p._id)\n        });\n      },\n      error: err => {\n        this.error = err;\n      }\n    });\n  }\n  onSubmit() {\n    if (this.reunionForm.invalid) return;\n    this.isSubmitting = true;\n    const reunion = this.reunionForm.value;\n    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.router.navigate(['/reunions']);\n      },\n      error: err => {\n        this.error = err;\n        this.isSubmitting = false;\n      }\n    });\n  }\n  goReunion() {\n    this.router.navigate(['/reunions']);\n  }\n  static {\n    this.ɵfac = function ReunionEditComponent_Factory(t) {\n      return new (t || ReunionEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ReunionService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.PlanningService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionEditComponent,\n      selectors: [[\"app-reunion-edit\"]],\n      decls: 56,\n      vars: 11,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [3, \"value\"]],\n      template: function ReunionEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ReunionEditComponent_Template_form_ngSubmit_1_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(2, ReunionEditComponent_div_2_Template, 2, 1, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\")(5, \"label\", 4);\n          i0.ɵɵtext(6, \"Titre *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"input\", 5);\n          i0.ɵɵtemplate(8, ReunionEditComponent_div_8_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\")(10, \"label\", 7);\n          i0.ɵɵtext(11, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"textarea\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\")(15, \"label\", 10);\n          i0.ɵɵtext(16, \"Date *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 11);\n          i0.ɵɵtemplate(18, ReunionEditComponent_div_18_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\")(20, \"label\", 12);\n          i0.ɵɵtext(21, \"Heure de d\\u00E9but *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 13);\n          i0.ɵɵtemplate(23, ReunionEditComponent_div_23_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\")(25, \"label\", 14);\n          i0.ɵɵtext(26, \"Heure de fin *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"input\", 15);\n          i0.ɵɵtemplate(28, ReunionEditComponent_div_28_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 16)(30, \"div\")(31, \"label\", 17);\n          i0.ɵɵtext(32, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\")(35, \"label\", 19);\n          i0.ɵɵtext(36, \"Lien Visio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\")(39, \"label\", 21);\n          i0.ɵɵtext(40, \"Planning *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"select\", 22)(42, \"option\", 23);\n          i0.ɵɵtext(43, \"S\\u00E9lectionnez un planning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, ReunionEditComponent_option_44_Template, 2, 2, \"option\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, ReunionEditComponent_div_45_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\")(47, \"label\", 25);\n          i0.ɵɵtext(48, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"select\", 26);\n          i0.ɵɵtemplate(50, ReunionEditComponent_ng_container_50_Template, 2, 1, \"ng-container\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 28)(52, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function ReunionEditComponent_Template_button_click_52_listener() {\n            return ctx.goReunion();\n          });\n          i0.ɵɵtext(53, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 30);\n          i0.ɵɵtext(55);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.users);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWVkaXQuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1lZGl0L3JldW5pb24tZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "message", "ɵɵproperty", "planning_r8", "id", "ɵɵtextInterpolate", "titre", "user_r10", "_id", "username", "ɵɵelementContainerStart", "ɵɵtemplate", "ReunionEditComponent_ng_container_50_option_1_Template", "ɵɵelementContainerEnd", "ctx_r7", "users", "ReunionEditComponent", "constructor", "fb", "route", "router", "reunionService", "userService", "planningService", "isSubmitting", "plannings", "ngOnInit", "reunionId", "snapshot", "paramMap", "get", "initForm", "fetchUsers", "fetchPlannings", "loadReunion", "reunionForm", "group", "required", "description", "date", "heureDebut", "heure<PERSON>in", "lieu", "lienVisio", "planning", "participants", "getAllUsers", "subscribe", "getAllPlannings", "getReunionById", "next", "reunion", "patchValue", "split", "map", "p", "err", "onSubmit", "invalid", "value", "updateReunion", "navigate", "goReunion", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "ReunionService", "i4", "DataService", "i5", "PlanningService", "selectors", "decls", "vars", "consts", "template", "ReunionEditComponent_Template", "rf", "ctx", "ɵɵlistener", "ReunionEditComponent_Template_form_ngSubmit_1_listener", "ReunionEditComponent_div_2_Template", "ɵɵelement", "ReunionEditComponent_div_8_Template", "ReunionEditComponent_div_18_Template", "ReunionEditComponent_div_23_Template", "ReunionEditComponent_div_28_Template", "ReunionEditComponent_option_44_Template", "ReunionEditComponent_div_45_Template", "ReunionEditComponent_ng_container_50_Template", "ReunionEditComponent_Template_button_click_52_listener", "tmp_2_0", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_7_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-edit\\reunion-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-edit\\reunion-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport {ReunionService} from \"@app/services/reunion.service\";\r\nimport {DataService} from \"@app/services/data.service\";\r\nimport {PlanningService} from \"@app/services/planning.service\";\r\nimport {Planning} from \"@app/models/planning.model\";\r\nimport {User} from \"@app/models/user.model\";\r\n\r\n@Component({\r\n  selector: 'app-reunion-edit',\r\n  templateUrl: './reunion-edit.component.html',\r\n  styleUrls: ['./reunion-edit.component.css']\r\n})\r\nexport class ReunionEditComponent implements OnInit {\r\n  reunionForm!: FormGroup;\r\n  reunionId!: string;\r\n  error: any = null;\r\n  isSubmitting = false;\r\n  users: User[] = [];\r\n  plannings: Planning[] = [];\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private reunionService: ReunionService,\r\n    private userService: DataService,\r\n    private planningService: PlanningService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.reunionId = this.route.snapshot.paramMap.get('id')!;\r\n    this.initForm();\r\n    this.fetchUsers();\r\n    this.fetchPlannings();\r\n    this.loadReunion();\r\n  }\r\n\r\n  initForm(): void {\r\n    this.reunionForm = this.fb.group({\r\n      titre: ['', Validators.required],\r\n      description: [''],\r\n      date: ['', Validators.required],\r\n      heureDebut: ['', Validators.required],\r\n      heureFin: ['', Validators.required],\r\n      lieu: [''],\r\n      lienVisio: [''],\r\n      planning: ['', Validators.required],\r\n      participants: [[]]\r\n    });\r\n  }\r\n\r\n  fetchUsers(): void {\r\n    this.userService.getAllUsers().subscribe((users:any) => {\r\n      this.users = users;\r\n    });\r\n  }\r\n\r\n  fetchPlannings(): void {\r\n    this.planningService.getAllPlannings().subscribe((plannings:any) => {\r\n      this.plannings = plannings.plannings;\r\n    });\r\n  }\r\n\r\n  loadReunion(): void {\r\n    this.reunionService.getReunionById(this.reunionId).subscribe({\r\n      next: (reunion: any) => {\r\n        this.reunionForm.patchValue({\r\n          titre: reunion.reunion.titre,\r\n          description: reunion.reunion.description,\r\n          date: reunion.reunion.date?.split('T')[0],\r\n          heureDebut: reunion.reunion.heureDebut,\r\n          heureFin: reunion.reunion.heureFin,\r\n          lieu: reunion.reunion.lieu,\r\n          lienVisio: reunion.reunion.lienVisio,\r\n          planning: reunion.reunion.planning?.id,\r\n          participants: reunion.reunion.participants?.map((p:any) => p._id)\r\n        });\r\n      },\r\n      error: (err) => {\r\n        this.error = err;\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.reunionForm.invalid) return;\r\n\r\n    this.isSubmitting = true;\r\n    const reunion: any = this.reunionForm.value;\r\n\r\n    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\r\n      next: () => {\r\n        this.isSubmitting = false;\r\n        this.router.navigate(['/reunions']);\r\n      },\r\n      error: (err) => {\r\n        this.error = err;\r\n        this.isSubmitting = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  goReunion(): void {\r\n    this.router.navigate(['/reunions']);\r\n  }\r\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\r\n\r\n\r\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-lg shadow-md p-6\">\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error.message || 'Une erreur est survenue' }}\r\n    </div>\r\n\r\n    <div class=\"grid grid-cols-1 gap-6\">\r\n      <!-- Titre -->\r\n      <div>\r\n        <label for=\"titre\" class=\"block text-sm font-medium text-gray-700\">Titre *</label>\r\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\r\n               class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\r\n             class=\"text-red-500 text-sm mt-1\">\r\n          Le titre est obligatoire\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Description -->\r\n      <div>\r\n        <label for=\"description\" class=\"block text-sm font-medium text-gray-700\">Description</label>\r\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\r\n                  class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\"></textarea>\r\n      </div>\r\n\r\n      <!-- Date and Time -->\r\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n        <div>\r\n          <label for=\"date\" class=\"block text-sm font-medium text-gray-700\">Date *</label>\r\n          <input id=\"date\" type=\"date\" formControlName=\"date\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            La date est obligatoire\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"heureDebut\" class=\"block text-sm font-medium text-gray-700\">Heure de début *</label>\r\n          <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            L'heure de début est obligatoire\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"heureFin\" class=\"block text-sm font-medium text-gray-700\">Heure de fin *</label>\r\n          <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\r\n               class=\"text-red-500 text-sm mt-1\">\r\n            L'heure de fin est obligatoire\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Lieu / Lien visio -->\r\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        <div>\r\n          <label for=\"lieu\" class=\"block text-sm font-medium text-gray-700\">Lieu</label>\r\n          <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"lienVisio\" class=\"block text-sm font-medium text-gray-700\">Lien Visio</label>\r\n          <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\r\n                 class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Planning -->\r\n      <div>\r\n        <label for=\"planning\" class=\"block text-sm font-medium text-gray-700\">Planning *</label>\r\n        <select id=\"planning\" formControlName=\"planning\"\r\n                class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500\">\r\n          <option value=\"\">Sélectionnez un planning</option>\r\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning.id\">{{ planning.titre }}</option>\r\n        </select>\r\n        <div *ngIf=\"reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched\"\r\n             class=\"text-red-500 text-sm mt-1\">\r\n          Le planning est obligatoire\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Participants -->\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\r\n        <select formControlName=\"participants\" multiple\r\n                class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\">\r\n          <ng-container *ngIf=\"users\">\r\n            <option *ngFor=\"let user of users\" [value]=\"user._id\">{{ user.username }}</option>\r\n          </ng-container>\r\n        </select>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mt-6 flex justify-end space-x-3\">\r\n      <button type=\"button\" (click)=\"goReunion()\"\r\n              class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\">\r\n        Annuler\r\n      </button>\r\n      <button type=\"submit\" [disabled]=\"reunionForm.invalid || isSubmitting\"\r\n              class=\"px-4 py-2 rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-50\">\r\n        {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,mCACF;;;;;IAQIR,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBJH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAONH,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBNH,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnDH,EAAA,CAAAS,UAAA,UAAAC,WAAA,CAAAC,EAAA,CAAqB;IAACX,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,iBAAA,CAAAF,WAAA,CAAAG,KAAA,CAAoB;;;;;IAEvFb,EAAA,CAAAC,cAAA,cACuC;IACrCD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IASFH,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAS,UAAA,UAAAK,QAAA,CAAAC,GAAA,CAAkB;IAACf,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAY,iBAAA,CAAAE,QAAA,CAAAE,QAAA,CAAmB;;;;;IAD3EhB,EAAA,CAAAiB,uBAAA,GAA4B;IAC1BjB,EAAA,CAAAkB,UAAA,IAAAC,sDAAA,qBAAkF;IACpFnB,EAAA,CAAAoB,qBAAA,EAAe;;;;IADYpB,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAS,UAAA,YAAAY,MAAA,CAAAC,KAAA,CAAQ;;;ADjF7C,OAAM,MAAOC,oBAAoB;EAQ/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,eAAgC;IALhC,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAXzB,KAAAvB,KAAK,GAAQ,IAAI;IACjB,KAAAwB,YAAY,GAAG,KAAK;IACpB,KAAAT,KAAK,GAAW,EAAE;IAClB,KAAAU,SAAS,GAAe,EAAE;EASvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAE;IACxD,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACI,WAAW,GAAG,IAAI,CAACjB,EAAE,CAACkB,KAAK,CAAC;MAC/B9B,KAAK,EAAE,CAAC,EAAE,EAAEd,UAAU,CAAC6C,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,EAAE/C,UAAU,CAAC6C,QAAQ,CAAC;MAC/BG,UAAU,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC6C,QAAQ,CAAC;MACrCI,QAAQ,EAAE,CAAC,EAAE,EAAEjD,UAAU,CAAC6C,QAAQ,CAAC;MACnCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAEpD,UAAU,CAAC6C,QAAQ,CAAC;MACnCQ,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;EACJ;EAEAb,UAAUA,CAAA;IACR,IAAI,CAACV,WAAW,CAACwB,WAAW,EAAE,CAACC,SAAS,CAAEhC,KAAS,IAAI;MACrD,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACV,eAAe,CAACyB,eAAe,EAAE,CAACD,SAAS,CAAEtB,SAAa,IAAI;MACjE,IAAI,CAACA,SAAS,GAAGA,SAAS,CAACA,SAAS;IACtC,CAAC,CAAC;EACJ;EAEAS,WAAWA,CAAA;IACT,IAAI,CAACb,cAAc,CAAC4B,cAAc,CAAC,IAAI,CAACtB,SAAS,CAAC,CAACoB,SAAS,CAAC;MAC3DG,IAAI,EAAGC,OAAY,IAAI;QACrB,IAAI,CAAChB,WAAW,CAACiB,UAAU,CAAC;UAC1B9C,KAAK,EAAE6C,OAAO,CAACA,OAAO,CAAC7C,KAAK;UAC5BgC,WAAW,EAAEa,OAAO,CAACA,OAAO,CAACb,WAAW;UACxCC,IAAI,EAAEY,OAAO,CAACA,OAAO,CAACZ,IAAI,EAAEc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzCb,UAAU,EAAEW,OAAO,CAACA,OAAO,CAACX,UAAU;UACtCC,QAAQ,EAAEU,OAAO,CAACA,OAAO,CAACV,QAAQ;UAClCC,IAAI,EAAES,OAAO,CAACA,OAAO,CAACT,IAAI;UAC1BC,SAAS,EAAEQ,OAAO,CAACA,OAAO,CAACR,SAAS;UACpCC,QAAQ,EAAEO,OAAO,CAACA,OAAO,CAACP,QAAQ,EAAExC,EAAE;UACtCyC,YAAY,EAAEM,OAAO,CAACA,OAAO,CAACN,YAAY,EAAES,GAAG,CAAEC,CAAK,IAAKA,CAAC,CAAC/C,GAAG;SACjE,CAAC;MACJ,CAAC;MACDR,KAAK,EAAGwD,GAAG,IAAI;QACb,IAAI,CAACxD,KAAK,GAAGwD,GAAG;MAClB;KACD,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtB,WAAW,CAACuB,OAAO,EAAE;IAE9B,IAAI,CAAClC,YAAY,GAAG,IAAI;IACxB,MAAM2B,OAAO,GAAQ,IAAI,CAAChB,WAAW,CAACwB,KAAK;IAE3C,IAAI,CAACtC,cAAc,CAACuC,aAAa,CAAC,IAAI,CAACjC,SAAS,EAAEwB,OAAO,CAAC,CAACJ,SAAS,CAAC;MACnEG,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC1B,YAAY,GAAG,KAAK;QACzB,IAAI,CAACJ,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACD7D,KAAK,EAAGwD,GAAG,IAAI;QACb,IAAI,CAACxD,KAAK,GAAGwD,GAAG;QAChB,IAAI,CAAChC,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEAsC,SAASA,CAAA;IACP,IAAI,CAAC1C,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;;;uBA5FW7C,oBAAoB,EAAAvB,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3E,EAAA,CAAAsE,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA7E,EAAA,CAAAsE,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAAsE,iBAAA,CAAAU,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAApB1D,oBAAoB;MAAA2D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdjCxF,EAAA,CAAAC,cAAA,aAAmD;UAGjBD,EAAA,CAAA0F,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAzB,QAAA,EAAU;UAAA,EAAC;UACrDhE,EAAA,CAAAkB,UAAA,IAAA0E,mCAAA,iBAEM;UAEN5F,EAAA,CAAAC,cAAA,aAAoC;UAGmCD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClFH,EAAA,CAAA6F,SAAA,eACoH;UACpH7F,EAAA,CAAAkB,UAAA,IAAA4E,mCAAA,iBAGM;UACR9F,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,UAAK;UACsED,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA6F,SAAA,mBACkI;UACpI7F,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAmD;UAEmBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChFH,EAAA,CAAA6F,SAAA,iBACoH;UACpH7F,EAAA,CAAAkB,UAAA,KAAA6E,oCAAA,iBAGM;UACR/F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACqED,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChGH,EAAA,CAAA6F,SAAA,iBACoH;UACpH7F,EAAA,CAAAkB,UAAA,KAAA8E,oCAAA,iBAGM;UACRhG,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA6F,SAAA,iBACoH;UACpH7F,EAAA,CAAAkB,UAAA,KAAA+E,oCAAA,iBAGM;UACRjG,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAmD;UAEmBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAA6F,SAAA,iBACoH;UACtH7F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UACoED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzFH,EAAA,CAAA6F,SAAA,iBACoH;UACtH7F,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxFH,EAAA,CAAAC,cAAA,kBACqH;UAClGD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAkB,UAAA,KAAAgF,uCAAA,qBAA8F;UAChGlG,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAkB,UAAA,KAAAiF,oCAAA,iBAGM;UACRnG,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UACoDD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBACiI;UAC/HD,EAAA,CAAAkB,UAAA,KAAAkF,6CAAA,2BAEe;UACjBpG,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA6C;UACrBD,EAAA,CAAA0F,UAAA,mBAAAW,uDAAA;YAAA,OAASZ,GAAA,CAAApB,SAAA,EAAW;UAAA,EAAC;UAEzCrE,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAC0H;UACxHD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;UA1GPH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAS,UAAA,cAAAgF,GAAA,CAAA/C,WAAA,CAAyB;UACvB1C,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAS,UAAA,SAAAgF,GAAA,CAAAlF,KAAA,CAAW;UAUPP,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAS,UAAA,WAAA6F,OAAA,GAAAb,GAAA,CAAA/C,WAAA,CAAAL,GAAA,4BAAAiE,OAAA,CAAArC,OAAA,OAAAqC,OAAA,GAAAb,GAAA,CAAA/C,WAAA,CAAAL,GAAA,4BAAAiE,OAAA,CAAAC,OAAA,EAA4E;UAmB1EvG,EAAA,CAAAI,SAAA,IAA0E;UAA1EJ,EAAA,CAAAS,UAAA,WAAA+F,OAAA,GAAAf,GAAA,CAAA/C,WAAA,CAAAL,GAAA,2BAAAmE,OAAA,CAAAvC,OAAA,OAAAuC,OAAA,GAAAf,GAAA,CAAA/C,WAAA,CAAAL,GAAA,2BAAAmE,OAAA,CAAAD,OAAA,EAA0E;UAU1EvG,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAS,UAAA,WAAAgG,OAAA,GAAAhB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,iCAAAoE,OAAA,CAAAxC,OAAA,OAAAwC,OAAA,GAAAhB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,iCAAAoE,OAAA,CAAAF,OAAA,EAAsF;UAUtFvG,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAS,UAAA,WAAAiG,OAAA,GAAAjB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,+BAAAqE,OAAA,CAAAzC,OAAA,OAAAyC,OAAA,GAAAjB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,+BAAAqE,OAAA,CAAAH,OAAA,EAAkF;UA4B3DvG,EAAA,CAAAI,SAAA,IAAY;UAAZJ,EAAA,CAAAS,UAAA,YAAAgF,GAAA,CAAAzD,SAAA,CAAY;UAErChC,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAS,UAAA,WAAAkG,OAAA,GAAAlB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,+BAAAsE,OAAA,CAAA1C,OAAA,OAAA0C,OAAA,GAAAlB,GAAA,CAAA/C,WAAA,CAAAL,GAAA,+BAAAsE,OAAA,CAAAJ,OAAA,EAAkF;UAWvEvG,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAS,UAAA,SAAAgF,GAAA,CAAAnE,KAAA,CAAW;UAYRtB,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAS,UAAA,aAAAgF,GAAA,CAAA/C,WAAA,CAAAuB,OAAA,IAAAwB,GAAA,CAAA1D,YAAA,CAAgD;UAEpE/B,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAoF,GAAA,CAAA1D,YAAA,4CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}