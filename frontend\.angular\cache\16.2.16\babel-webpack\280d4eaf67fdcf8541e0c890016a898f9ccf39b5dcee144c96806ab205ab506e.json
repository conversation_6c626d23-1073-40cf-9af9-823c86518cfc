{"ast": null, "code": "var _asyncToGenerator = require(\"C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/asyncToGenerator.js\").default;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) : typeof define === 'function' && define.amd ? define(['exports'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.graphqlWs = {}));\n})(this, function (exports) {\n  'use strict';\n\n  /******************************************************************************\r\n  Copyright (c) Microsoft Corporation.\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n  purpose with or without fee is hereby granted.\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n  PERFORMANCE OF THIS SOFTWARE.\r\n  ***************************************************************************** */\n  /* global Reflect, Promise */\n  function __await(v) {\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\n  }\n  function __asyncGenerator(thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []),\n      i,\n      q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () {\n      return this;\n    }, i;\n    function verb(n) {\n      if (g[n]) i[n] = function (v) {\n        return new Promise(function (a, b) {\n          q.push([n, v, a, b]) > 1 || resume(n, v);\n        });\n      };\n    }\n    function resume(n, v) {\n      try {\n        step(g[n](v));\n      } catch (e) {\n        settle(q[0][3], e);\n      }\n    }\n    function step(r) {\n      r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);\n    }\n    function fulfill(value) {\n      resume(\"next\", value);\n    }\n    function reject(value) {\n      resume(\"throw\", value);\n    }\n    function settle(f, v) {\n      if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);\n    }\n  }\n\n  /** @private */\n  function extendedTypeof(val) {\n    if (val === null) {\n      return 'null';\n    }\n    if (Array.isArray(val)) {\n      return 'array';\n    }\n    return typeof val;\n  }\n  /** @private */\n  function isObject(val) {\n    return extendedTypeof(val) === 'object';\n  }\n  /** @private */\n  function areGraphQLErrors(obj) {\n    return Array.isArray(obj) &&\n    // must be at least one error\n    obj.length > 0 &&\n    // error has at least a message\n    obj.every(ob => 'message' in ob);\n  }\n  /**\n   * Limits the WebSocket close event reason to not exceed a length of one frame.\n   * Reference: https://datatracker.ietf.org/doc/html/rfc6455#section-5.2.\n   *\n   * @private\n   */\n  function limitCloseReason(reason, whenTooLong) {\n    return reason.length < 124 ? reason : whenTooLong;\n  }\n\n  /**\n   *\n   * common\n   *\n   */\n  /**\n   * The WebSocket sub-protocol used for the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n   *\n   * @category Common\n   */\n  const GRAPHQL_TRANSPORT_WS_PROTOCOL = 'graphql-transport-ws';\n  /**\n   * The deprecated subprotocol used by [subscriptions-transport-ws](https://github.com/apollographql/subscriptions-transport-ws).\n   *\n   * @private\n   */\n  const DEPRECATED_GRAPHQL_WS_PROTOCOL = 'graphql-ws';\n  /**\n   * `graphql-ws` expected and standard close codes of the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n   *\n   * @category Common\n   */\n  exports.CloseCode = void 0;\n  (function (CloseCode) {\n    CloseCode[CloseCode[\"InternalServerError\"] = 4500] = \"InternalServerError\";\n    CloseCode[CloseCode[\"InternalClientError\"] = 4005] = \"InternalClientError\";\n    CloseCode[CloseCode[\"BadRequest\"] = 4400] = \"BadRequest\";\n    CloseCode[CloseCode[\"BadResponse\"] = 4004] = \"BadResponse\";\n    /** Tried subscribing before connect ack */\n    CloseCode[CloseCode[\"Unauthorized\"] = 4401] = \"Unauthorized\";\n    CloseCode[CloseCode[\"Forbidden\"] = 4403] = \"Forbidden\";\n    CloseCode[CloseCode[\"SubprotocolNotAcceptable\"] = 4406] = \"SubprotocolNotAcceptable\";\n    CloseCode[CloseCode[\"ConnectionInitialisationTimeout\"] = 4408] = \"ConnectionInitialisationTimeout\";\n    CloseCode[CloseCode[\"ConnectionAcknowledgementTimeout\"] = 4504] = \"ConnectionAcknowledgementTimeout\";\n    /** Subscriber distinction is very important */\n    CloseCode[CloseCode[\"SubscriberAlreadyExists\"] = 4409] = \"SubscriberAlreadyExists\";\n    CloseCode[CloseCode[\"TooManyInitialisationRequests\"] = 4429] = \"TooManyInitialisationRequests\";\n  })(exports.CloseCode || (exports.CloseCode = {}));\n  /**\n   * Types of messages allowed to be sent by the client/server over the WS protocol.\n   *\n   * @category Common\n   */\n  exports.MessageType = void 0;\n  (function (MessageType) {\n    MessageType[\"ConnectionInit\"] = \"connection_init\";\n    MessageType[\"ConnectionAck\"] = \"connection_ack\";\n    MessageType[\"Ping\"] = \"ping\";\n    MessageType[\"Pong\"] = \"pong\";\n    MessageType[\"Subscribe\"] = \"subscribe\";\n    MessageType[\"Next\"] = \"next\";\n    MessageType[\"Error\"] = \"error\";\n    MessageType[\"Complete\"] = \"complete\";\n  })(exports.MessageType || (exports.MessageType = {}));\n  /**\n   * Validates the message against the GraphQL over WebSocket Protocol.\n   *\n   * Invalid messages will throw descriptive errors.\n   *\n   * @category Common\n   */\n  function validateMessage(val) {\n    if (!isObject(val)) {\n      throw new Error(`Message is expected to be an object, but got ${extendedTypeof(val)}`);\n    }\n    if (!val.type) {\n      throw new Error(`Message is missing the 'type' property`);\n    }\n    if (typeof val.type !== 'string') {\n      throw new Error(`Message is expects the 'type' property to be a string, but got ${extendedTypeof(val.type)}`);\n    }\n    switch (val.type) {\n      case exports.MessageType.ConnectionInit:\n      case exports.MessageType.ConnectionAck:\n      case exports.MessageType.Ping:\n      case exports.MessageType.Pong:\n        {\n          if (val.payload != null && !isObject(val.payload)) {\n            throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object or nullish or missing, but got \"${val.payload}\"`);\n          }\n          break;\n        }\n      case exports.MessageType.Subscribe:\n        {\n          if (typeof val.id !== 'string') {\n            throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n          }\n          if (!val.id) {\n            throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n          }\n          if (!isObject(val.payload)) {\n            throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n          }\n          if (typeof val.payload.query !== 'string') {\n            throw new Error(`\"${val.type}\" message payload expects the 'query' property to be a string, but got ${extendedTypeof(val.payload.query)}`);\n          }\n          if (val.payload.variables != null && !isObject(val.payload.variables)) {\n            throw new Error(`\"${val.type}\" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.variables)}`);\n          }\n          if (val.payload.operationName != null && extendedTypeof(val.payload.operationName) !== 'string') {\n            throw new Error(`\"${val.type}\" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${extendedTypeof(val.payload.operationName)}`);\n          }\n          if (val.payload.extensions != null && !isObject(val.payload.extensions)) {\n            throw new Error(`\"${val.type}\" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.extensions)}`);\n          }\n          break;\n        }\n      case exports.MessageType.Next:\n        {\n          if (typeof val.id !== 'string') {\n            throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n          }\n          if (!val.id) {\n            throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n          }\n          if (!isObject(val.payload)) {\n            throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n          }\n          break;\n        }\n      case exports.MessageType.Error:\n        {\n          if (typeof val.id !== 'string') {\n            throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n          }\n          if (!val.id) {\n            throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n          }\n          if (!areGraphQLErrors(val.payload)) {\n            throw new Error(`\"${val.type}\" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(val.payload)}`);\n          }\n          break;\n        }\n      case exports.MessageType.Complete:\n        {\n          if (typeof val.id !== 'string') {\n            throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n          }\n          if (!val.id) {\n            throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n          }\n          break;\n        }\n      default:\n        throw new Error(`Invalid message 'type' property \"${val.type}\"`);\n    }\n    return val;\n  }\n  /**\n   * Checks if the provided value is a valid GraphQL over WebSocket message.\n   *\n   * @deprecated Use `validateMessage` instead.\n   *\n   * @category Common\n   */\n  function isMessage(val) {\n    try {\n      validateMessage(val);\n      return true;\n    } catch (_a) {\n      return false;\n    }\n  }\n  /**\n   * Parses the raw websocket message data to a valid message.\n   *\n   * @category Common\n   */\n  function parseMessage(data, reviver) {\n    return validateMessage(typeof data === 'string' ? JSON.parse(data, reviver) : data);\n  }\n  /**\n   * Stringifies a valid message ready to be sent through the socket.\n   *\n   * @category Common\n   */\n  function stringifyMessage(msg, replacer) {\n    validateMessage(msg);\n    return JSON.stringify(msg, replacer);\n  }\n\n  /**\n   *\n   * client\n   *\n   */\n  /**\n   * Creates a disposable GraphQL over WebSocket client.\n   *\n   * @category Client\n   */\n  function createClient(options) {\n    const {\n      url,\n      connectionParams,\n      lazy = true,\n      onNonLazyError = console.error,\n      lazyCloseTimeout: lazyCloseTimeoutMs = 0,\n      keepAlive = 0,\n      disablePong,\n      connectionAckWaitTimeout = 0,\n      retryAttempts = 5,\n      retryWait = /*#__PURE__*/function () {\n        var _randomisedExponentialBackoff = _asyncToGenerator(function* (retries) {\n          let retryDelay = 1000; // start with 1s delay\n          for (let i = 0; i < retries; i++) {\n            retryDelay *= 2;\n          }\n          yield new Promise(resolve => setTimeout(resolve, retryDelay +\n          // add random timeout from 300ms to 3s\n          Math.floor(Math.random() * (3000 - 300) + 300)));\n        });\n        function randomisedExponentialBackoff(_x) {\n          return _randomisedExponentialBackoff.apply(this, arguments);\n        }\n        return randomisedExponentialBackoff;\n      }(),\n      shouldRetry = isLikeCloseEvent,\n      isFatalConnectionProblem,\n      on,\n      webSocketImpl,\n      /**\n       * Generates a v4 UUID to be used as the ID using `Math`\n       * as the random number generator. Supply your own generator\n       * in case you need more uniqueness.\n       *\n       * Reference: https://gist.github.com/jed/982883\n       */\n      generateID = function generateUUID() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n          const r = Math.random() * 16 | 0,\n            v = c == 'x' ? r : r & 0x3 | 0x8;\n          return v.toString(16);\n        });\n      },\n      jsonMessageReplacer: replacer,\n      jsonMessageReviver: reviver\n    } = options;\n    let ws;\n    if (webSocketImpl) {\n      if (!isWebSocket(webSocketImpl)) {\n        throw new Error('Invalid WebSocket implementation provided');\n      }\n      ws = webSocketImpl;\n    } else if (typeof WebSocket !== 'undefined') {\n      ws = WebSocket;\n    } else if (typeof global !== 'undefined') {\n      ws = global.WebSocket ||\n      // @ts-expect-error: Support more browsers\n      global.MozWebSocket;\n    } else if (typeof window !== 'undefined') {\n      ws = window.WebSocket ||\n      // @ts-expect-error: Support more browsers\n      window.MozWebSocket;\n    }\n    if (!ws) throw new Error(\"WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`\");\n    const WebSocketImpl = ws;\n    // websocket status emitter, subscriptions are handled differently\n    const emitter = (() => {\n      const message = (() => {\n        const listeners = {};\n        return {\n          on(id, listener) {\n            listeners[id] = listener;\n            return () => {\n              delete listeners[id];\n            };\n          },\n          emit(message) {\n            var _a;\n            if ('id' in message) (_a = listeners[message.id]) === null || _a === void 0 ? void 0 : _a.call(listeners, message);\n          }\n        };\n      })();\n      const listeners = {\n        connecting: (on === null || on === void 0 ? void 0 : on.connecting) ? [on.connecting] : [],\n        opened: (on === null || on === void 0 ? void 0 : on.opened) ? [on.opened] : [],\n        connected: (on === null || on === void 0 ? void 0 : on.connected) ? [on.connected] : [],\n        ping: (on === null || on === void 0 ? void 0 : on.ping) ? [on.ping] : [],\n        pong: (on === null || on === void 0 ? void 0 : on.pong) ? [on.pong] : [],\n        message: (on === null || on === void 0 ? void 0 : on.message) ? [message.emit, on.message] : [message.emit],\n        closed: (on === null || on === void 0 ? void 0 : on.closed) ? [on.closed] : [],\n        error: (on === null || on === void 0 ? void 0 : on.error) ? [on.error] : []\n      };\n      return {\n        onMessage: message.on,\n        on(event, listener) {\n          const l = listeners[event];\n          l.push(listener);\n          return () => {\n            l.splice(l.indexOf(listener), 1);\n          };\n        },\n        emit(event, ...args) {\n          // we copy the listeners so that unlistens dont \"pull the rug under our feet\"\n          for (const listener of [...listeners[event]]) {\n            // @ts-expect-error: The args should fit\n            listener(...args);\n          }\n        }\n      };\n    })();\n    // invokes the callback either when an error or closed event is emitted,\n    // first one that gets called prevails, other emissions are ignored\n    function errorOrClosed(cb) {\n      const listening = [\n      // errors are fatal and more critical than close events, throw them first\n      emitter.on('error', err => {\n        listening.forEach(unlisten => unlisten());\n        cb(err);\n      }),\n      // closes can be graceful and not fatal, throw them second (if error didnt throw)\n      emitter.on('closed', event => {\n        listening.forEach(unlisten => unlisten());\n        cb(event);\n      })];\n    }\n    let connecting,\n      locks = 0,\n      lazyCloseTimeout,\n      retrying = false,\n      retries = 0,\n      disposed = false;\n    function connect() {\n      return _connect.apply(this, arguments);\n    }\n    /**\n     * Checks the `connect` problem and evaluates if the client should retry.\n     */\n    function _connect() {\n      _connect = _asyncToGenerator(function* () {\n        // clear the lazy close timeout immediatelly so that close gets debounced\n        // see: https://github.com/enisdenjo/graphql-ws/issues/388\n        clearTimeout(lazyCloseTimeout);\n        const [socket, throwOnClose] = yield connecting !== null && connecting !== void 0 ? connecting : connecting = new Promise((connected, denied) => _asyncToGenerator(function* () {\n          if (retrying) {\n            yield retryWait(retries);\n            // subscriptions might complete while waiting for retry\n            if (!locks) {\n              connecting = undefined;\n              return denied({\n                code: 1000,\n                reason: 'All Subscriptions Gone'\n              });\n            }\n            retries++;\n          }\n          emitter.emit('connecting');\n          const socket = new WebSocketImpl(typeof url === 'function' ? yield url() : url, GRAPHQL_TRANSPORT_WS_PROTOCOL);\n          let connectionAckTimeout, queuedPing;\n          function enqueuePing() {\n            if (isFinite(keepAlive) && keepAlive > 0) {\n              clearTimeout(queuedPing); // in case where a pong was received before a ping (this is valid behaviour)\n              queuedPing = setTimeout(() => {\n                if (socket.readyState === WebSocketImpl.OPEN) {\n                  socket.send(stringifyMessage({\n                    type: exports.MessageType.Ping\n                  }));\n                  emitter.emit('ping', false, undefined);\n                }\n              }, keepAlive);\n            }\n          }\n          errorOrClosed(errOrEvent => {\n            connecting = undefined;\n            clearTimeout(connectionAckTimeout);\n            clearTimeout(queuedPing);\n            denied(errOrEvent);\n            if (isLikeCloseEvent(errOrEvent) && errOrEvent.code === 4499) {\n              socket.close(4499, 'Terminated'); // close event is artificial and emitted manually, see `Client.terminate()` below\n              socket.onerror = null;\n              socket.onclose = null;\n            }\n          });\n          socket.onerror = err => emitter.emit('error', err);\n          socket.onclose = event => emitter.emit('closed', event);\n          socket.onopen = /*#__PURE__*/_asyncToGenerator(function* () {\n            try {\n              emitter.emit('opened', socket);\n              const payload = typeof connectionParams === 'function' ? yield connectionParams() : connectionParams;\n              // connectionParams might take too long causing the server to kick off the client\n              // the necessary error/close event is already reported - simply stop execution\n              if (socket.readyState !== WebSocketImpl.OPEN) return;\n              socket.send(stringifyMessage(payload ? {\n                type: exports.MessageType.ConnectionInit,\n                payload\n              } : {\n                type: exports.MessageType.ConnectionInit\n                // payload is completely absent if not provided\n              }, replacer));\n              if (isFinite(connectionAckWaitTimeout) && connectionAckWaitTimeout > 0) {\n                connectionAckTimeout = setTimeout(() => {\n                  socket.close(exports.CloseCode.ConnectionAcknowledgementTimeout, 'Connection acknowledgement timeout');\n                }, connectionAckWaitTimeout);\n              }\n              enqueuePing(); // enqueue ping (noop if disabled)\n            } catch (err) {\n              emitter.emit('error', err);\n              socket.close(exports.CloseCode.InternalClientError, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Internal client error'));\n            }\n          });\n          let acknowledged = false;\n          socket.onmessage = ({\n            data\n          }) => {\n            try {\n              const message = parseMessage(data, reviver);\n              emitter.emit('message', message);\n              if (message.type === 'ping' || message.type === 'pong') {\n                emitter.emit(message.type, true, message.payload); // received\n                if (message.type === 'pong') {\n                  enqueuePing(); // enqueue next ping (noop if disabled)\n                } else if (!disablePong) {\n                  // respond with pong on ping\n                  socket.send(stringifyMessage(message.payload ? {\n                    type: exports.MessageType.Pong,\n                    payload: message.payload\n                  } : {\n                    type: exports.MessageType.Pong\n                    // payload is completely absent if not provided\n                  }));\n\n                  emitter.emit('pong', false, message.payload);\n                }\n                return; // ping and pongs can be received whenever\n              }\n\n              if (acknowledged) return; // already connected and acknowledged\n              if (message.type !== exports.MessageType.ConnectionAck) throw new Error(`First message cannot be of type ${message.type}`);\n              clearTimeout(connectionAckTimeout);\n              acknowledged = true;\n              emitter.emit('connected', socket, message.payload); // connected = socket opened + acknowledged\n              retrying = false; // future lazy connects are not retries\n              retries = 0; // reset the retries on connect\n              connected([socket, new Promise((_, reject) => errorOrClosed(reject))]);\n            } catch (err) {\n              socket.onmessage = null; // stop reading messages as soon as reading breaks once\n              emitter.emit('error', err);\n              socket.close(exports.CloseCode.BadResponse, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Bad response'));\n            }\n          };\n        })());\n        // if the provided socket is in a closing state, wait for the throw on close\n        if (socket.readyState === WebSocketImpl.CLOSING) yield throwOnClose;\n        let release = () => {\n          // releases this connection\n        };\n        const released = new Promise(resolve => release = resolve);\n        return [socket, release, Promise.race([\n        // wait for\n        released.then(() => {\n          if (!locks) {\n            // and if no more locks are present, complete the connection\n            const complete = () => socket.close(1000, 'Normal Closure');\n            if (isFinite(lazyCloseTimeoutMs) && lazyCloseTimeoutMs > 0) {\n              // if the keepalive is set, allow for the specified calmdown time and\n              // then complete if the socket is still open.\n              lazyCloseTimeout = setTimeout(() => {\n                if (socket.readyState === WebSocketImpl.OPEN) complete();\n              }, lazyCloseTimeoutMs);\n            } else {\n              // otherwise complete immediately\n              complete();\n            }\n          }\n        }),\n        // or\n        throwOnClose])];\n      });\n      return _connect.apply(this, arguments);\n    }\n    function shouldRetryConnectOrThrow(errOrCloseEvent) {\n      // some close codes are worth reporting immediately\n      if (isLikeCloseEvent(errOrCloseEvent) && (isFatalInternalCloseCode(errOrCloseEvent.code) || [exports.CloseCode.InternalServerError, exports.CloseCode.InternalClientError, exports.CloseCode.BadRequest, exports.CloseCode.BadResponse, exports.CloseCode.Unauthorized,\n      // CloseCode.Forbidden, might grant access out after retry\n      exports.CloseCode.SubprotocolNotAcceptable,\n      // CloseCode.ConnectionInitialisationTimeout, might not time out after retry\n      // CloseCode.ConnectionAcknowledgementTimeout, might not time out after retry\n      exports.CloseCode.SubscriberAlreadyExists, exports.CloseCode.TooManyInitialisationRequests\n      // 4499, // Terminated, probably because the socket froze, we want to retry\n      ].includes(errOrCloseEvent.code))) throw errOrCloseEvent;\n      // client was disposed, no retries should proceed regardless\n      if (disposed) return false;\n      // normal closure (possibly all subscriptions have completed)\n      // if no locks were acquired in the meantime, shouldnt try again\n      if (isLikeCloseEvent(errOrCloseEvent) && errOrCloseEvent.code === 1000) return locks > 0;\n      // retries are not allowed or we tried to many times, report error\n      if (!retryAttempts || retries >= retryAttempts) throw errOrCloseEvent;\n      // throw non-retryable connection problems\n      if (!shouldRetry(errOrCloseEvent)) throw errOrCloseEvent;\n      // @deprecated throw fatal connection problems immediately\n      if (isFatalConnectionProblem === null || isFatalConnectionProblem === void 0 ? void 0 : isFatalConnectionProblem(errOrCloseEvent)) throw errOrCloseEvent;\n      // looks good, start retrying\n      return retrying = true;\n    }\n    // in non-lazy (hot?) mode always hold one connection lock to persist the socket\n    if (!lazy) {\n      _asyncToGenerator(function* () {\n        locks++;\n        for (;;) {\n          try {\n            const [,, throwOnClose] = yield connect();\n            yield throwOnClose; // will always throw because releaser is not used\n          } catch (errOrCloseEvent) {\n            try {\n              if (!shouldRetryConnectOrThrow(errOrCloseEvent)) return;\n            } catch (errOrCloseEvent) {\n              // report thrown error, no further retries\n              return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(errOrCloseEvent);\n            }\n          }\n        }\n      })();\n    }\n    return {\n      on: emitter.on,\n      subscribe(payload, sink) {\n        const id = generateID(payload);\n        let done = false,\n          errored = false,\n          releaser = () => {\n            // for handling completions before connect\n            locks--;\n            done = true;\n          };\n        _asyncToGenerator(function* () {\n          locks++;\n          for (;;) {\n            try {\n              const [socket, release, waitForReleaseOrThrowOnClose] = yield connect();\n              // if done while waiting for connect, release the connection lock right away\n              if (done) return release();\n              const unlisten = emitter.onMessage(id, message => {\n                switch (message.type) {\n                  case exports.MessageType.Next:\n                    {\n                      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- payload will fit type\n                      sink.next(message.payload);\n                      return;\n                    }\n                  case exports.MessageType.Error:\n                    {\n                      errored = true, done = true;\n                      sink.error(message.payload);\n                      releaser();\n                      return;\n                    }\n                  case exports.MessageType.Complete:\n                    {\n                      done = true;\n                      releaser(); // release completes the sink\n                      return;\n                    }\n                }\n              });\n              socket.send(stringifyMessage({\n                id,\n                type: exports.MessageType.Subscribe,\n                payload\n              }, replacer));\n              releaser = () => {\n                if (!done && socket.readyState === WebSocketImpl.OPEN)\n                  // if not completed already and socket is open, send complete message to server on release\n                  socket.send(stringifyMessage({\n                    id,\n                    type: exports.MessageType.Complete\n                  }, replacer));\n                locks--;\n                done = true;\n                release();\n              };\n              // either the releaser will be called, connection completed and\n              // the promise resolved or the socket closed and the promise rejected.\n              // whatever happens though, we want to stop listening for messages\n              yield waitForReleaseOrThrowOnClose.finally(unlisten);\n              return; // completed, shouldnt try again\n            } catch (errOrCloseEvent) {\n              if (!shouldRetryConnectOrThrow(errOrCloseEvent)) return;\n            }\n          }\n        })().then(() => {\n          // delivering either an error or a complete terminates the sequence\n          if (!errored) sink.complete();\n        }) // resolves on release or normal closure\n        .catch(err => {\n          sink.error(err);\n        }); // rejects on close events and errors\n        return () => {\n          // dispose only of active subscriptions\n          if (!done) releaser();\n        };\n      },\n      iterate(request) {\n        const pending = [];\n        const deferred = {\n          done: false,\n          error: null,\n          resolve: () => {\n            // noop\n          }\n        };\n        const dispose = this.subscribe(request, {\n          next(val) {\n            pending.push(val);\n            deferred.resolve();\n          },\n          error(err) {\n            deferred.done = true;\n            deferred.error = err;\n            deferred.resolve();\n          },\n          complete() {\n            deferred.done = true;\n            deferred.resolve();\n          }\n        });\n        const iterator = function iterator() {\n          return __asyncGenerator(this, arguments, function* iterator_1() {\n            for (;;) {\n              if (!pending.length) {\n                // only wait if there are no pending messages available\n                yield __await(new Promise(resolve => deferred.resolve = resolve));\n              }\n              // first flush\n              while (pending.length) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                yield yield __await(pending.shift());\n              }\n              // then error\n              if (deferred.error) {\n                throw deferred.error;\n              }\n              // or complete\n              if (deferred.done) {\n                return yield __await(void 0);\n              }\n            }\n          });\n        }();\n        iterator.throw = /*#__PURE__*/function () {\n          var _ref3 = _asyncToGenerator(function* (err) {\n            if (!deferred.done) {\n              deferred.done = true;\n              deferred.error = err;\n              deferred.resolve();\n            }\n            return {\n              done: true,\n              value: undefined\n            };\n          });\n          return function (_x2) {\n            return _ref3.apply(this, arguments);\n          };\n        }();\n        iterator.return = /*#__PURE__*/_asyncToGenerator(function* () {\n          dispose();\n          return {\n            done: true,\n            value: undefined\n          };\n        });\n        return iterator;\n      },\n      dispose() {\n        return _asyncToGenerator(function* () {\n          disposed = true;\n          if (connecting) {\n            // if there is a connection, close it\n            const [socket] = yield connecting;\n            socket.close(1000, 'Normal Closure');\n          }\n        })();\n      },\n      terminate() {\n        if (connecting) {\n          // only if there is a connection\n          emitter.emit('closed', {\n            code: 4499,\n            reason: 'Terminated',\n            wasClean: false\n          });\n        }\n      }\n    };\n  }\n  function isLikeCloseEvent(val) {\n    return isObject(val) && 'code' in val && 'reason' in val;\n  }\n  function isFatalInternalCloseCode(code) {\n    if ([1000, 1001, 1006, 1005, 1012, 1013, 1013 // Bad Gateway\n    ].includes(code)) return false;\n    // all other internal errors are fatal\n    return code >= 1000 && code <= 1999;\n  }\n  function isWebSocket(val) {\n    return typeof val === 'function' && 'constructor' in val && 'CLOSED' in val && 'CLOSING' in val && 'CONNECTING' in val && 'OPEN' in val;\n  }\n  exports.DEPRECATED_GRAPHQL_WS_PROTOCOL = DEPRECATED_GRAPHQL_WS_PROTOCOL;\n  exports.GRAPHQL_TRANSPORT_WS_PROTOCOL = GRAPHQL_TRANSPORT_WS_PROTOCOL;\n  exports.createClient = createClient;\n  exports.isMessage = isMessage;\n  exports.parseMessage = parseMessage;\n  exports.stringifyMessage = stringifyMessage;\n  exports.validateMessage = validateMessage;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "define", "amd", "globalThis", "self", "graphqlWs", "__await", "v", "__asyncGenerator", "thisArg", "_arguments", "generator", "Symbol", "asyncIterator", "TypeError", "g", "apply", "i", "q", "verb", "n", "Promise", "a", "b", "push", "resume", "step", "e", "settle", "r", "value", "resolve", "then", "fulfill", "reject", "f", "shift", "length", "extendedTypeof", "val", "Array", "isArray", "isObject", "areGraphQLErrors", "obj", "every", "ob", "limitCloseReason", "reason", "whenTooLong", "GRAPHQL_TRANSPORT_WS_PROTOCOL", "DEPRECATED_GRAPHQL_WS_PROTOCOL", "CloseCode", "MessageType", "validateMessage", "Error", "type", "ConnectionInit", "ConnectionAck", "<PERSON>", "Pong", "payload", "Subscribe", "id", "query", "variables", "operationName", "extensions", "Next", "JSON", "stringify", "Complete", "isMessage", "_a", "parseMessage", "data", "reviver", "parse", "stringifyMessage", "msg", "replacer", "createClient", "options", "url", "connectionParams", "lazy", "onNonLazyError", "console", "error", "lazyCloseTimeout", "lazyCloseTimeoutMs", "keepAlive", "disable<PERSON><PERSON>", "connectionAckWaitTimeout", "retryAttempts", "retryWait", "_randomisedExponentialBackoff", "_asyncToGenerator", "retries", "retry<PERSON><PERSON><PERSON>", "setTimeout", "Math", "floor", "random", "randomisedExponentialBackoff", "_x", "arguments", "shouldRetry", "isLikeCloseEvent", "isFatalConnectionProblem", "on", "webSocketImpl", "generateID", "generateUUID", "replace", "c", "toString", "jsonMessageReplacer", "jsonMessageReviver", "ws", "isWebSocket", "WebSocket", "MozWebSocket", "window", "WebSocketImpl", "emitter", "message", "listeners", "listener", "emit", "call", "connecting", "opened", "connected", "ping", "pong", "closed", "onMessage", "event", "l", "splice", "indexOf", "args", "errorOrClosed", "cb", "listening", "err", "for<PERSON>ach", "unlisten", "locks", "retrying", "disposed", "connect", "_connect", "clearTimeout", "socket", "throwOnClose", "denied", "undefined", "code", "connectionAckTimeout", "queuedPing", "enqueuePing", "isFinite", "readyState", "OPEN", "send", "errOrEvent", "close", "onerror", "onclose", "onopen", "ConnectionAcknowledgementTimeout", "InternalClientError", "acknowledged", "onmessage", "_", "BadResponse", "CLOSING", "release", "released", "race", "complete", "shouldRetryConnectOrThrow", "errOrCloseEvent", "isFatalInternalCloseCode", "InternalServerError", "BadRequest", "Unauthorized", "SubprotocolNotAcceptable", "SubscriberAlreadyExists", "TooManyInitialisationRequests", "includes", "subscribe", "sink", "done", "errored", "releaser", "waitForReleaseOrThrowOnClose", "next", "finally", "catch", "iterate", "request", "pending", "deferred", "dispose", "iterator", "iterator_1", "throw", "_ref3", "_x2", "return", "terminate", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql-ws/umd/graphql-ws.js"], "sourcesContent": ["(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define(['exports'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.graphqlWs = {}));\n})(this, (function (exports) { 'use strict';\n\n    /******************************************************************************\r\n    Copyright (c) Microsoft Corporation.\r\n\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n    purpose with or without fee is hereby granted.\r\n\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n    PERFORMANCE OF THIS SOFTWARE.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise */\r\n\r\n\r\n    function __await(v) {\r\n        return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n    }\r\n\r\n    function __asyncGenerator(thisArg, _arguments, generator) {\r\n        if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n        var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n        return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n        function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n        function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n        function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n        function fulfill(value) { resume(\"next\", value); }\r\n        function reject(value) { resume(\"throw\", value); }\r\n        function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n    }\n\n    /** @private */\n    function extendedTypeof(val) {\n        if (val === null) {\n            return 'null';\n        }\n        if (Array.isArray(val)) {\n            return 'array';\n        }\n        return typeof val;\n    }\n    /** @private */\n    function isObject(val) {\n        return extendedTypeof(val) === 'object';\n    }\n    /** @private */\n    function areGraphQLErrors(obj) {\n        return (Array.isArray(obj) &&\n            // must be at least one error\n            obj.length > 0 &&\n            // error has at least a message\n            obj.every((ob) => 'message' in ob));\n    }\n    /**\n     * Limits the WebSocket close event reason to not exceed a length of one frame.\n     * Reference: https://datatracker.ietf.org/doc/html/rfc6455#section-5.2.\n     *\n     * @private\n     */\n    function limitCloseReason(reason, whenTooLong) {\n        return reason.length < 124 ? reason : whenTooLong;\n    }\n\n    /**\n     *\n     * common\n     *\n     */\n    /**\n     * The WebSocket sub-protocol used for the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n     *\n     * @category Common\n     */\n    const GRAPHQL_TRANSPORT_WS_PROTOCOL = 'graphql-transport-ws';\n    /**\n     * The deprecated subprotocol used by [subscriptions-transport-ws](https://github.com/apollographql/subscriptions-transport-ws).\n     *\n     * @private\n     */\n    const DEPRECATED_GRAPHQL_WS_PROTOCOL = 'graphql-ws';\n    /**\n     * `graphql-ws` expected and standard close codes of the [GraphQL over WebSocket Protocol](https://github.com/graphql/graphql-over-http/blob/main/rfcs/GraphQLOverWebSocket.md).\n     *\n     * @category Common\n     */\n    exports.CloseCode = void 0;\n    (function (CloseCode) {\n        CloseCode[CloseCode[\"InternalServerError\"] = 4500] = \"InternalServerError\";\n        CloseCode[CloseCode[\"InternalClientError\"] = 4005] = \"InternalClientError\";\n        CloseCode[CloseCode[\"BadRequest\"] = 4400] = \"BadRequest\";\n        CloseCode[CloseCode[\"BadResponse\"] = 4004] = \"BadResponse\";\n        /** Tried subscribing before connect ack */\n        CloseCode[CloseCode[\"Unauthorized\"] = 4401] = \"Unauthorized\";\n        CloseCode[CloseCode[\"Forbidden\"] = 4403] = \"Forbidden\";\n        CloseCode[CloseCode[\"SubprotocolNotAcceptable\"] = 4406] = \"SubprotocolNotAcceptable\";\n        CloseCode[CloseCode[\"ConnectionInitialisationTimeout\"] = 4408] = \"ConnectionInitialisationTimeout\";\n        CloseCode[CloseCode[\"ConnectionAcknowledgementTimeout\"] = 4504] = \"ConnectionAcknowledgementTimeout\";\n        /** Subscriber distinction is very important */\n        CloseCode[CloseCode[\"SubscriberAlreadyExists\"] = 4409] = \"SubscriberAlreadyExists\";\n        CloseCode[CloseCode[\"TooManyInitialisationRequests\"] = 4429] = \"TooManyInitialisationRequests\";\n    })(exports.CloseCode || (exports.CloseCode = {}));\n    /**\n     * Types of messages allowed to be sent by the client/server over the WS protocol.\n     *\n     * @category Common\n     */\n    exports.MessageType = void 0;\n    (function (MessageType) {\n        MessageType[\"ConnectionInit\"] = \"connection_init\";\n        MessageType[\"ConnectionAck\"] = \"connection_ack\";\n        MessageType[\"Ping\"] = \"ping\";\n        MessageType[\"Pong\"] = \"pong\";\n        MessageType[\"Subscribe\"] = \"subscribe\";\n        MessageType[\"Next\"] = \"next\";\n        MessageType[\"Error\"] = \"error\";\n        MessageType[\"Complete\"] = \"complete\";\n    })(exports.MessageType || (exports.MessageType = {}));\n    /**\n     * Validates the message against the GraphQL over WebSocket Protocol.\n     *\n     * Invalid messages will throw descriptive errors.\n     *\n     * @category Common\n     */\n    function validateMessage(val) {\n        if (!isObject(val)) {\n            throw new Error(`Message is expected to be an object, but got ${extendedTypeof(val)}`);\n        }\n        if (!val.type) {\n            throw new Error(`Message is missing the 'type' property`);\n        }\n        if (typeof val.type !== 'string') {\n            throw new Error(`Message is expects the 'type' property to be a string, but got ${extendedTypeof(val.type)}`);\n        }\n        switch (val.type) {\n            case exports.MessageType.ConnectionInit:\n            case exports.MessageType.ConnectionAck:\n            case exports.MessageType.Ping:\n            case exports.MessageType.Pong: {\n                if (val.payload != null && !isObject(val.payload)) {\n                    throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object or nullish or missing, but got \"${val.payload}\"`);\n                }\n                break;\n            }\n            case exports.MessageType.Subscribe: {\n                if (typeof val.id !== 'string') {\n                    throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n                }\n                if (!val.id) {\n                    throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n                }\n                if (!isObject(val.payload)) {\n                    throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n                }\n                if (typeof val.payload.query !== 'string') {\n                    throw new Error(`\"${val.type}\" message payload expects the 'query' property to be a string, but got ${extendedTypeof(val.payload.query)}`);\n                }\n                if (val.payload.variables != null && !isObject(val.payload.variables)) {\n                    throw new Error(`\"${val.type}\" message payload expects the 'variables' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.variables)}`);\n                }\n                if (val.payload.operationName != null &&\n                    extendedTypeof(val.payload.operationName) !== 'string') {\n                    throw new Error(`\"${val.type}\" message payload expects the 'operationName' property to be a string or nullish or missing, but got ${extendedTypeof(val.payload.operationName)}`);\n                }\n                if (val.payload.extensions != null && !isObject(val.payload.extensions)) {\n                    throw new Error(`\"${val.type}\" message payload expects the 'extensions' property to be a an object or nullish or missing, but got ${extendedTypeof(val.payload.extensions)}`);\n                }\n                break;\n            }\n            case exports.MessageType.Next: {\n                if (typeof val.id !== 'string') {\n                    throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n                }\n                if (!val.id) {\n                    throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n                }\n                if (!isObject(val.payload)) {\n                    throw new Error(`\"${val.type}\" message expects the 'payload' property to be an object, but got ${extendedTypeof(val.payload)}`);\n                }\n                break;\n            }\n            case exports.MessageType.Error: {\n                if (typeof val.id !== 'string') {\n                    throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n                }\n                if (!val.id) {\n                    throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n                }\n                if (!areGraphQLErrors(val.payload)) {\n                    throw new Error(`\"${val.type}\" message expects the 'payload' property to be an array of GraphQL errors, but got ${JSON.stringify(val.payload)}`);\n                }\n                break;\n            }\n            case exports.MessageType.Complete: {\n                if (typeof val.id !== 'string') {\n                    throw new Error(`\"${val.type}\" message expects the 'id' property to be a string, but got ${extendedTypeof(val.id)}`);\n                }\n                if (!val.id) {\n                    throw new Error(`\"${val.type}\" message requires a non-empty 'id' property`);\n                }\n                break;\n            }\n            default:\n                throw new Error(`Invalid message 'type' property \"${val.type}\"`);\n        }\n        return val;\n    }\n    /**\n     * Checks if the provided value is a valid GraphQL over WebSocket message.\n     *\n     * @deprecated Use `validateMessage` instead.\n     *\n     * @category Common\n     */\n    function isMessage(val) {\n        try {\n            validateMessage(val);\n            return true;\n        }\n        catch (_a) {\n            return false;\n        }\n    }\n    /**\n     * Parses the raw websocket message data to a valid message.\n     *\n     * @category Common\n     */\n    function parseMessage(data, reviver) {\n        return validateMessage(typeof data === 'string' ? JSON.parse(data, reviver) : data);\n    }\n    /**\n     * Stringifies a valid message ready to be sent through the socket.\n     *\n     * @category Common\n     */\n    function stringifyMessage(msg, replacer) {\n        validateMessage(msg);\n        return JSON.stringify(msg, replacer);\n    }\n\n    /**\n     *\n     * client\n     *\n     */\n    /**\n     * Creates a disposable GraphQL over WebSocket client.\n     *\n     * @category Client\n     */\n    function createClient(options) {\n        const { url, connectionParams, lazy = true, onNonLazyError = console.error, lazyCloseTimeout: lazyCloseTimeoutMs = 0, keepAlive = 0, disablePong, connectionAckWaitTimeout = 0, retryAttempts = 5, retryWait = async function randomisedExponentialBackoff(retries) {\n            let retryDelay = 1000; // start with 1s delay\n            for (let i = 0; i < retries; i++) {\n                retryDelay *= 2;\n            }\n            await new Promise((resolve) => setTimeout(resolve, retryDelay +\n                // add random timeout from 300ms to 3s\n                Math.floor(Math.random() * (3000 - 300) + 300)));\n        }, shouldRetry = isLikeCloseEvent, isFatalConnectionProblem, on, webSocketImpl, \n        /**\n         * Generates a v4 UUID to be used as the ID using `Math`\n         * as the random number generator. Supply your own generator\n         * in case you need more uniqueness.\n         *\n         * Reference: https://gist.github.com/jed/982883\n         */\n        generateID = function generateUUID() {\n            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n                const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n                return v.toString(16);\n            });\n        }, jsonMessageReplacer: replacer, jsonMessageReviver: reviver, } = options;\n        let ws;\n        if (webSocketImpl) {\n            if (!isWebSocket(webSocketImpl)) {\n                throw new Error('Invalid WebSocket implementation provided');\n            }\n            ws = webSocketImpl;\n        }\n        else if (typeof WebSocket !== 'undefined') {\n            ws = WebSocket;\n        }\n        else if (typeof global !== 'undefined') {\n            ws =\n                global.WebSocket ||\n                    // @ts-expect-error: Support more browsers\n                    global.MozWebSocket;\n        }\n        else if (typeof window !== 'undefined') {\n            ws =\n                window.WebSocket ||\n                    // @ts-expect-error: Support more browsers\n                    window.MozWebSocket;\n        }\n        if (!ws)\n            throw new Error(\"WebSocket implementation missing; on Node you can `import WebSocket from 'ws';` and pass `webSocketImpl: WebSocket` to `createClient`\");\n        const WebSocketImpl = ws;\n        // websocket status emitter, subscriptions are handled differently\n        const emitter = (() => {\n            const message = (() => {\n                const listeners = {};\n                return {\n                    on(id, listener) {\n                        listeners[id] = listener;\n                        return () => {\n                            delete listeners[id];\n                        };\n                    },\n                    emit(message) {\n                        var _a;\n                        if ('id' in message)\n                            (_a = listeners[message.id]) === null || _a === void 0 ? void 0 : _a.call(listeners, message);\n                    },\n                };\n            })();\n            const listeners = {\n                connecting: (on === null || on === void 0 ? void 0 : on.connecting) ? [on.connecting] : [],\n                opened: (on === null || on === void 0 ? void 0 : on.opened) ? [on.opened] : [],\n                connected: (on === null || on === void 0 ? void 0 : on.connected) ? [on.connected] : [],\n                ping: (on === null || on === void 0 ? void 0 : on.ping) ? [on.ping] : [],\n                pong: (on === null || on === void 0 ? void 0 : on.pong) ? [on.pong] : [],\n                message: (on === null || on === void 0 ? void 0 : on.message) ? [message.emit, on.message] : [message.emit],\n                closed: (on === null || on === void 0 ? void 0 : on.closed) ? [on.closed] : [],\n                error: (on === null || on === void 0 ? void 0 : on.error) ? [on.error] : [],\n            };\n            return {\n                onMessage: message.on,\n                on(event, listener) {\n                    const l = listeners[event];\n                    l.push(listener);\n                    return () => {\n                        l.splice(l.indexOf(listener), 1);\n                    };\n                },\n                emit(event, ...args) {\n                    // we copy the listeners so that unlistens dont \"pull the rug under our feet\"\n                    for (const listener of [...listeners[event]]) {\n                        // @ts-expect-error: The args should fit\n                        listener(...args);\n                    }\n                },\n            };\n        })();\n        // invokes the callback either when an error or closed event is emitted,\n        // first one that gets called prevails, other emissions are ignored\n        function errorOrClosed(cb) {\n            const listening = [\n                // errors are fatal and more critical than close events, throw them first\n                emitter.on('error', (err) => {\n                    listening.forEach((unlisten) => unlisten());\n                    cb(err);\n                }),\n                // closes can be graceful and not fatal, throw them second (if error didnt throw)\n                emitter.on('closed', (event) => {\n                    listening.forEach((unlisten) => unlisten());\n                    cb(event);\n                }),\n            ];\n        }\n        let connecting, locks = 0, lazyCloseTimeout, retrying = false, retries = 0, disposed = false;\n        async function connect() {\n            // clear the lazy close timeout immediatelly so that close gets debounced\n            // see: https://github.com/enisdenjo/graphql-ws/issues/388\n            clearTimeout(lazyCloseTimeout);\n            const [socket, throwOnClose] = await (connecting !== null && connecting !== void 0 ? connecting : (connecting = new Promise((connected, denied) => (async () => {\n                if (retrying) {\n                    await retryWait(retries);\n                    // subscriptions might complete while waiting for retry\n                    if (!locks) {\n                        connecting = undefined;\n                        return denied({ code: 1000, reason: 'All Subscriptions Gone' });\n                    }\n                    retries++;\n                }\n                emitter.emit('connecting');\n                const socket = new WebSocketImpl(typeof url === 'function' ? await url() : url, GRAPHQL_TRANSPORT_WS_PROTOCOL);\n                let connectionAckTimeout, queuedPing;\n                function enqueuePing() {\n                    if (isFinite(keepAlive) && keepAlive > 0) {\n                        clearTimeout(queuedPing); // in case where a pong was received before a ping (this is valid behaviour)\n                        queuedPing = setTimeout(() => {\n                            if (socket.readyState === WebSocketImpl.OPEN) {\n                                socket.send(stringifyMessage({ type: exports.MessageType.Ping }));\n                                emitter.emit('ping', false, undefined);\n                            }\n                        }, keepAlive);\n                    }\n                }\n                errorOrClosed((errOrEvent) => {\n                    connecting = undefined;\n                    clearTimeout(connectionAckTimeout);\n                    clearTimeout(queuedPing);\n                    denied(errOrEvent);\n                    if (isLikeCloseEvent(errOrEvent) && errOrEvent.code === 4499) {\n                        socket.close(4499, 'Terminated'); // close event is artificial and emitted manually, see `Client.terminate()` below\n                        socket.onerror = null;\n                        socket.onclose = null;\n                    }\n                });\n                socket.onerror = (err) => emitter.emit('error', err);\n                socket.onclose = (event) => emitter.emit('closed', event);\n                socket.onopen = async () => {\n                    try {\n                        emitter.emit('opened', socket);\n                        const payload = typeof connectionParams === 'function'\n                            ? await connectionParams()\n                            : connectionParams;\n                        // connectionParams might take too long causing the server to kick off the client\n                        // the necessary error/close event is already reported - simply stop execution\n                        if (socket.readyState !== WebSocketImpl.OPEN)\n                            return;\n                        socket.send(stringifyMessage(payload\n                            ? {\n                                type: exports.MessageType.ConnectionInit,\n                                payload,\n                            }\n                            : {\n                                type: exports.MessageType.ConnectionInit,\n                                // payload is completely absent if not provided\n                            }, replacer));\n                        if (isFinite(connectionAckWaitTimeout) &&\n                            connectionAckWaitTimeout > 0) {\n                            connectionAckTimeout = setTimeout(() => {\n                                socket.close(exports.CloseCode.ConnectionAcknowledgementTimeout, 'Connection acknowledgement timeout');\n                            }, connectionAckWaitTimeout);\n                        }\n                        enqueuePing(); // enqueue ping (noop if disabled)\n                    }\n                    catch (err) {\n                        emitter.emit('error', err);\n                        socket.close(exports.CloseCode.InternalClientError, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Internal client error'));\n                    }\n                };\n                let acknowledged = false;\n                socket.onmessage = ({ data }) => {\n                    try {\n                        const message = parseMessage(data, reviver);\n                        emitter.emit('message', message);\n                        if (message.type === 'ping' || message.type === 'pong') {\n                            emitter.emit(message.type, true, message.payload); // received\n                            if (message.type === 'pong') {\n                                enqueuePing(); // enqueue next ping (noop if disabled)\n                            }\n                            else if (!disablePong) {\n                                // respond with pong on ping\n                                socket.send(stringifyMessage(message.payload\n                                    ? {\n                                        type: exports.MessageType.Pong,\n                                        payload: message.payload,\n                                    }\n                                    : {\n                                        type: exports.MessageType.Pong,\n                                        // payload is completely absent if not provided\n                                    }));\n                                emitter.emit('pong', false, message.payload);\n                            }\n                            return; // ping and pongs can be received whenever\n                        }\n                        if (acknowledged)\n                            return; // already connected and acknowledged\n                        if (message.type !== exports.MessageType.ConnectionAck)\n                            throw new Error(`First message cannot be of type ${message.type}`);\n                        clearTimeout(connectionAckTimeout);\n                        acknowledged = true;\n                        emitter.emit('connected', socket, message.payload); // connected = socket opened + acknowledged\n                        retrying = false; // future lazy connects are not retries\n                        retries = 0; // reset the retries on connect\n                        connected([\n                            socket,\n                            new Promise((_, reject) => errorOrClosed(reject)),\n                        ]);\n                    }\n                    catch (err) {\n                        socket.onmessage = null; // stop reading messages as soon as reading breaks once\n                        emitter.emit('error', err);\n                        socket.close(exports.CloseCode.BadResponse, limitCloseReason(err instanceof Error ? err.message : new Error(err).message, 'Bad response'));\n                    }\n                };\n            })())));\n            // if the provided socket is in a closing state, wait for the throw on close\n            if (socket.readyState === WebSocketImpl.CLOSING)\n                await throwOnClose;\n            let release = () => {\n                // releases this connection\n            };\n            const released = new Promise((resolve) => (release = resolve));\n            return [\n                socket,\n                release,\n                Promise.race([\n                    // wait for\n                    released.then(() => {\n                        if (!locks) {\n                            // and if no more locks are present, complete the connection\n                            const complete = () => socket.close(1000, 'Normal Closure');\n                            if (isFinite(lazyCloseTimeoutMs) && lazyCloseTimeoutMs > 0) {\n                                // if the keepalive is set, allow for the specified calmdown time and\n                                // then complete if the socket is still open.\n                                lazyCloseTimeout = setTimeout(() => {\n                                    if (socket.readyState === WebSocketImpl.OPEN)\n                                        complete();\n                                }, lazyCloseTimeoutMs);\n                            }\n                            else {\n                                // otherwise complete immediately\n                                complete();\n                            }\n                        }\n                    }),\n                    // or\n                    throwOnClose,\n                ]),\n            ];\n        }\n        /**\n         * Checks the `connect` problem and evaluates if the client should retry.\n         */\n        function shouldRetryConnectOrThrow(errOrCloseEvent) {\n            // some close codes are worth reporting immediately\n            if (isLikeCloseEvent(errOrCloseEvent) &&\n                (isFatalInternalCloseCode(errOrCloseEvent.code) ||\n                    [\n                        exports.CloseCode.InternalServerError,\n                        exports.CloseCode.InternalClientError,\n                        exports.CloseCode.BadRequest,\n                        exports.CloseCode.BadResponse,\n                        exports.CloseCode.Unauthorized,\n                        // CloseCode.Forbidden, might grant access out after retry\n                        exports.CloseCode.SubprotocolNotAcceptable,\n                        // CloseCode.ConnectionInitialisationTimeout, might not time out after retry\n                        // CloseCode.ConnectionAcknowledgementTimeout, might not time out after retry\n                        exports.CloseCode.SubscriberAlreadyExists,\n                        exports.CloseCode.TooManyInitialisationRequests,\n                        // 4499, // Terminated, probably because the socket froze, we want to retry\n                    ].includes(errOrCloseEvent.code)))\n                throw errOrCloseEvent;\n            // client was disposed, no retries should proceed regardless\n            if (disposed)\n                return false;\n            // normal closure (possibly all subscriptions have completed)\n            // if no locks were acquired in the meantime, shouldnt try again\n            if (isLikeCloseEvent(errOrCloseEvent) && errOrCloseEvent.code === 1000)\n                return locks > 0;\n            // retries are not allowed or we tried to many times, report error\n            if (!retryAttempts || retries >= retryAttempts)\n                throw errOrCloseEvent;\n            // throw non-retryable connection problems\n            if (!shouldRetry(errOrCloseEvent))\n                throw errOrCloseEvent;\n            // @deprecated throw fatal connection problems immediately\n            if (isFatalConnectionProblem === null || isFatalConnectionProblem === void 0 ? void 0 : isFatalConnectionProblem(errOrCloseEvent))\n                throw errOrCloseEvent;\n            // looks good, start retrying\n            return (retrying = true);\n        }\n        // in non-lazy (hot?) mode always hold one connection lock to persist the socket\n        if (!lazy) {\n            (async () => {\n                locks++;\n                for (;;) {\n                    try {\n                        const [, , throwOnClose] = await connect();\n                        await throwOnClose; // will always throw because releaser is not used\n                    }\n                    catch (errOrCloseEvent) {\n                        try {\n                            if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                                return;\n                        }\n                        catch (errOrCloseEvent) {\n                            // report thrown error, no further retries\n                            return onNonLazyError === null || onNonLazyError === void 0 ? void 0 : onNonLazyError(errOrCloseEvent);\n                        }\n                    }\n                }\n            })();\n        }\n        return {\n            on: emitter.on,\n            subscribe(payload, sink) {\n                const id = generateID(payload);\n                let done = false, errored = false, releaser = () => {\n                    // for handling completions before connect\n                    locks--;\n                    done = true;\n                };\n                (async () => {\n                    locks++;\n                    for (;;) {\n                        try {\n                            const [socket, release, waitForReleaseOrThrowOnClose] = await connect();\n                            // if done while waiting for connect, release the connection lock right away\n                            if (done)\n                                return release();\n                            const unlisten = emitter.onMessage(id, (message) => {\n                                switch (message.type) {\n                                    case exports.MessageType.Next: {\n                                        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- payload will fit type\n                                        sink.next(message.payload);\n                                        return;\n                                    }\n                                    case exports.MessageType.Error: {\n                                        (errored = true), (done = true);\n                                        sink.error(message.payload);\n                                        releaser();\n                                        return;\n                                    }\n                                    case exports.MessageType.Complete: {\n                                        done = true;\n                                        releaser(); // release completes the sink\n                                        return;\n                                    }\n                                }\n                            });\n                            socket.send(stringifyMessage({\n                                id,\n                                type: exports.MessageType.Subscribe,\n                                payload,\n                            }, replacer));\n                            releaser = () => {\n                                if (!done && socket.readyState === WebSocketImpl.OPEN)\n                                    // if not completed already and socket is open, send complete message to server on release\n                                    socket.send(stringifyMessage({\n                                        id,\n                                        type: exports.MessageType.Complete,\n                                    }, replacer));\n                                locks--;\n                                done = true;\n                                release();\n                            };\n                            // either the releaser will be called, connection completed and\n                            // the promise resolved or the socket closed and the promise rejected.\n                            // whatever happens though, we want to stop listening for messages\n                            await waitForReleaseOrThrowOnClose.finally(unlisten);\n                            return; // completed, shouldnt try again\n                        }\n                        catch (errOrCloseEvent) {\n                            if (!shouldRetryConnectOrThrow(errOrCloseEvent))\n                                return;\n                        }\n                    }\n                })()\n                    .then(() => {\n                    // delivering either an error or a complete terminates the sequence\n                    if (!errored)\n                        sink.complete();\n                }) // resolves on release or normal closure\n                    .catch((err) => {\n                    sink.error(err);\n                }); // rejects on close events and errors\n                return () => {\n                    // dispose only of active subscriptions\n                    if (!done)\n                        releaser();\n                };\n            },\n            iterate(request) {\n                const pending = [];\n                const deferred = {\n                    done: false,\n                    error: null,\n                    resolve: () => {\n                        // noop\n                    },\n                };\n                const dispose = this.subscribe(request, {\n                    next(val) {\n                        pending.push(val);\n                        deferred.resolve();\n                    },\n                    error(err) {\n                        deferred.done = true;\n                        deferred.error = err;\n                        deferred.resolve();\n                    },\n                    complete() {\n                        deferred.done = true;\n                        deferred.resolve();\n                    },\n                });\n                const iterator = (function iterator() {\n                    return __asyncGenerator(this, arguments, function* iterator_1() {\n                        for (;;) {\n                            if (!pending.length) {\n                                // only wait if there are no pending messages available\n                                yield __await(new Promise((resolve) => (deferred.resolve = resolve)));\n                            }\n                            // first flush\n                            while (pending.length) {\n                                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                                yield yield __await(pending.shift());\n                            }\n                            // then error\n                            if (deferred.error) {\n                                throw deferred.error;\n                            }\n                            // or complete\n                            if (deferred.done) {\n                                return yield __await(void 0);\n                            }\n                        }\n                    });\n                })();\n                iterator.throw = async (err) => {\n                    if (!deferred.done) {\n                        deferred.done = true;\n                        deferred.error = err;\n                        deferred.resolve();\n                    }\n                    return { done: true, value: undefined };\n                };\n                iterator.return = async () => {\n                    dispose();\n                    return { done: true, value: undefined };\n                };\n                return iterator;\n            },\n            async dispose() {\n                disposed = true;\n                if (connecting) {\n                    // if there is a connection, close it\n                    const [socket] = await connecting;\n                    socket.close(1000, 'Normal Closure');\n                }\n            },\n            terminate() {\n                if (connecting) {\n                    // only if there is a connection\n                    emitter.emit('closed', {\n                        code: 4499,\n                        reason: 'Terminated',\n                        wasClean: false,\n                    });\n                }\n            },\n        };\n    }\n    function isLikeCloseEvent(val) {\n        return isObject(val) && 'code' in val && 'reason' in val;\n    }\n    function isFatalInternalCloseCode(code) {\n        if ([\n            1000,\n            1001,\n            1006,\n            1005,\n            1012,\n            1013,\n            1013, // Bad Gateway\n        ].includes(code))\n            return false;\n        // all other internal errors are fatal\n        return code >= 1000 && code <= 1999;\n    }\n    function isWebSocket(val) {\n        return (typeof val === 'function' &&\n            'constructor' in val &&\n            'CLOSED' in val &&\n            'CLOSING' in val &&\n            'CONNECTING' in val &&\n            'OPEN' in val);\n    }\n\n    exports.DEPRECATED_GRAPHQL_WS_PROTOCOL = DEPRECATED_GRAPHQL_WS_PROTOCOL;\n    exports.GRAPHQL_TRANSPORT_WS_PROTOCOL = GRAPHQL_TRANSPORT_WS_PROTOCOL;\n    exports.createClient = createClient;\n    exports.isMessage = isMessage;\n    exports.parseMessage = parseMessage;\n    exports.stringifyMessage = stringifyMessage;\n    exports.validateMessage = validateMessage;\n\n}));\n"], "mappings": ";AAAA,CAAC,UAAUA,MAAM,EAAEC,OAAO,EAAE;EACxB,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGF,OAAO,CAACC,OAAO,CAAC,GAC/E,OAAOE,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,SAAS,CAAC,EAAEH,OAAO,CAAC,IACxED,MAAM,GAAG,OAAOM,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGN,MAAM,IAAIO,IAAI,EAAEN,OAAO,CAACD,MAAM,CAACQ,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9G,CAAC,EAAE,IAAI,EAAG,UAAUN,OAAO,EAAE;EAAE,YAAY;;EAEvC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGI;EAGA,SAASO,OAAOA,CAACC,CAAC,EAAE;IAChB,OAAO,IAAI,YAAYD,OAAO,IAAI,IAAI,CAACC,CAAC,GAAGA,CAAC,EAAE,IAAI,IAAI,IAAID,OAAO,CAACC,CAAC,CAAC;EACxE;EAEA,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE;IACtD,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;IACtF,IAAIC,CAAC,GAAGJ,SAAS,CAACK,KAAK,CAACP,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC;MAAEO,CAAC;MAAEC,CAAC,GAAG,EAAE;IAC7D,OAAOD,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,CAAC,MAAM,CAAC,EAAEA,IAAI,CAAC,OAAO,CAAC,EAAEA,IAAI,CAAC,QAAQ,CAAC,EAAEF,CAAC,CAACL,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;MAAE,OAAO,IAAI;IAAE,CAAC,EAAEI,CAAC;IACrH,SAASE,IAAIA,CAACC,CAAC,EAAE;MAAE,IAAIL,CAAC,CAACK,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,GAAG,UAAUb,CAAC,EAAE;QAAE,OAAO,IAAIc,OAAO,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;UAAEL,CAAC,CAACM,IAAI,CAAC,CAACJ,CAAC,EAAEb,CAAC,EAAEe,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIE,MAAM,CAACL,CAAC,EAAEb,CAAC,CAAC;QAAE,CAAC,CAAC;MAAE,CAAC;IAAE;IACzI,SAASkB,MAAMA,CAACL,CAAC,EAAEb,CAAC,EAAE;MAAE,IAAI;QAAEmB,IAAI,CAACX,CAAC,CAACK,CAAC,CAAC,CAACb,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOoB,CAAC,EAAE;QAAEC,MAAM,CAACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAES,CAAC,CAAC;MAAE;IAAE;IACjF,SAASD,IAAIA,CAACG,CAAC,EAAE;MAAEA,CAAC,CAACC,KAAK,YAAYxB,OAAO,GAAGe,OAAO,CAACU,OAAO,CAACF,CAAC,CAACC,KAAK,CAACvB,CAAC,CAAC,CAACyB,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAGN,MAAM,CAACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEW,CAAC,CAAC;IAAE;IACvH,SAASI,OAAOA,CAACH,KAAK,EAAE;MAAEL,MAAM,CAAC,MAAM,EAAEK,KAAK,CAAC;IAAE;IACjD,SAASI,MAAMA,CAACJ,KAAK,EAAE;MAAEL,MAAM,CAAC,OAAO,EAAEK,KAAK,CAAC;IAAE;IACjD,SAASF,MAAMA,CAACO,CAAC,EAAE5B,CAAC,EAAE;MAAE,IAAI4B,CAAC,CAAC5B,CAAC,CAAC,EAAEW,CAAC,CAACkB,KAAK,CAAC,CAAC,EAAElB,CAAC,CAACmB,MAAM,EAAEZ,MAAM,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAE;EACrF;;EAEA;EACA,SAASoB,cAAcA,CAACC,GAAG,EAAE;IACzB,IAAIA,GAAG,KAAK,IAAI,EAAE;MACd,OAAO,MAAM;IACjB;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;MACpB,OAAO,OAAO;IAClB;IACA,OAAO,OAAOA,GAAG;EACrB;EACA;EACA,SAASG,QAAQA,CAACH,GAAG,EAAE;IACnB,OAAOD,cAAc,CAACC,GAAG,CAAC,KAAK,QAAQ;EAC3C;EACA;EACA,SAASI,gBAAgBA,CAACC,GAAG,EAAE;IAC3B,OAAQJ,KAAK,CAACC,OAAO,CAACG,GAAG,CAAC;IACtB;IACAA,GAAG,CAACP,MAAM,GAAG,CAAC;IACd;IACAO,GAAG,CAACC,KAAK,CAAEC,EAAE,IAAK,SAAS,IAAIA,EAAE,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,WAAW,EAAE;IAC3C,OAAOD,MAAM,CAACX,MAAM,GAAG,GAAG,GAAGW,MAAM,GAAGC,WAAW;EACrD;;EAEA;AACJ;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;AACA;EACI,MAAMC,6BAA6B,GAAG,sBAAsB;EAC5D;AACJ;AACA;AACA;AACA;EACI,MAAMC,8BAA8B,GAAG,YAAY;EACnD;AACJ;AACA;AACA;AACA;EACIpD,OAAO,CAACqD,SAAS,GAAG,KAAK,CAAC;EAC1B,CAAC,UAAUA,SAAS,EAAE;IAClBA,SAAS,CAACA,SAAS,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAqB;IAC1EA,SAAS,CAACA,SAAS,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,GAAG,qBAAqB;IAC1EA,SAAS,CAACA,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,YAAY;IACxDA,SAAS,CAACA,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,aAAa;IAC1D;IACAA,SAAS,CAACA,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,cAAc;IAC5DA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,WAAW;IACtDA,SAAS,CAACA,SAAS,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC,GAAG,0BAA0B;IACpFA,SAAS,CAACA,SAAS,CAAC,iCAAiC,CAAC,GAAG,IAAI,CAAC,GAAG,iCAAiC;IAClGA,SAAS,CAACA,SAAS,CAAC,kCAAkC,CAAC,GAAG,IAAI,CAAC,GAAG,kCAAkC;IACpG;IACAA,SAAS,CAACA,SAAS,CAAC,yBAAyB,CAAC,GAAG,IAAI,CAAC,GAAG,yBAAyB;IAClFA,SAAS,CAACA,SAAS,CAAC,+BAA+B,CAAC,GAAG,IAAI,CAAC,GAAG,+BAA+B;EAClG,CAAC,EAAErD,OAAO,CAACqD,SAAS,KAAKrD,OAAO,CAACqD,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EACjD;AACJ;AACA;AACA;AACA;EACIrD,OAAO,CAACsD,WAAW,GAAG,KAAK,CAAC;EAC5B,CAAC,UAAUA,WAAW,EAAE;IACpBA,WAAW,CAAC,gBAAgB,CAAC,GAAG,iBAAiB;IACjDA,WAAW,CAAC,eAAe,CAAC,GAAG,gBAAgB;IAC/CA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;IAC5BA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;IAC5BA,WAAW,CAAC,WAAW,CAAC,GAAG,WAAW;IACtCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;IAC5BA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;IAC9BA,WAAW,CAAC,UAAU,CAAC,GAAG,UAAU;EACxC,CAAC,EAAEtD,OAAO,CAACsD,WAAW,KAAKtD,OAAO,CAACsD,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;EACrD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,eAAeA,CAACf,GAAG,EAAE;IAC1B,IAAI,CAACG,QAAQ,CAACH,GAAG,CAAC,EAAE;MAChB,MAAM,IAAIgB,KAAK,CAAE,gDAA+CjB,cAAc,CAACC,GAAG,CAAE,EAAC,CAAC;IAC1F;IACA,IAAI,CAACA,GAAG,CAACiB,IAAI,EAAE;MACX,MAAM,IAAID,KAAK,CAAE,wCAAuC,CAAC;IAC7D;IACA,IAAI,OAAOhB,GAAG,CAACiB,IAAI,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAID,KAAK,CAAE,kEAAiEjB,cAAc,CAACC,GAAG,CAACiB,IAAI,CAAE,EAAC,CAAC;IACjH;IACA,QAAQjB,GAAG,CAACiB,IAAI;MACZ,KAAKzD,OAAO,CAACsD,WAAW,CAACI,cAAc;MACvC,KAAK1D,OAAO,CAACsD,WAAW,CAACK,aAAa;MACtC,KAAK3D,OAAO,CAACsD,WAAW,CAACM,IAAI;MAC7B,KAAK5D,OAAO,CAACsD,WAAW,CAACO,IAAI;QAAE;UAC3B,IAAIrB,GAAG,CAACsB,OAAO,IAAI,IAAI,IAAI,CAACnB,QAAQ,CAACH,GAAG,CAACsB,OAAO,CAAC,EAAE;YAC/C,MAAM,IAAIN,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,4FAA2FjB,GAAG,CAACsB,OAAQ,GAAE,CAAC;UAC3I;UACA;QACJ;MACA,KAAK9D,OAAO,CAACsD,WAAW,CAACS,SAAS;QAAE;UAChC,IAAI,OAAOvB,GAAG,CAACwB,EAAE,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,+DAA8DlB,cAAc,CAACC,GAAG,CAACwB,EAAE,CAAE,EAAC,CAAC;UACxH;UACA,IAAI,CAACxB,GAAG,CAACwB,EAAE,EAAE;YACT,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,8CAA6C,CAAC;UAC/E;UACA,IAAI,CAACd,QAAQ,CAACH,GAAG,CAACsB,OAAO,CAAC,EAAE;YACxB,MAAM,IAAIN,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,qEAAoElB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAAE,EAAC,CAAC;UACnI;UACA,IAAI,OAAOtB,GAAG,CAACsB,OAAO,CAACG,KAAK,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAIT,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,0EAAyElB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAACG,KAAK,CAAE,EAAC,CAAC;UAC9I;UACA,IAAIzB,GAAG,CAACsB,OAAO,CAACI,SAAS,IAAI,IAAI,IAAI,CAACvB,QAAQ,CAACH,GAAG,CAACsB,OAAO,CAACI,SAAS,CAAC,EAAE;YACnE,MAAM,IAAIV,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,uGAAsGlB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAACI,SAAS,CAAE,EAAC,CAAC;UAC/K;UACA,IAAI1B,GAAG,CAACsB,OAAO,CAACK,aAAa,IAAI,IAAI,IACjC5B,cAAc,CAACC,GAAG,CAACsB,OAAO,CAACK,aAAa,CAAC,KAAK,QAAQ,EAAE;YACxD,MAAM,IAAIX,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,wGAAuGlB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAACK,aAAa,CAAE,EAAC,CAAC;UACpL;UACA,IAAI3B,GAAG,CAACsB,OAAO,CAACM,UAAU,IAAI,IAAI,IAAI,CAACzB,QAAQ,CAACH,GAAG,CAACsB,OAAO,CAACM,UAAU,CAAC,EAAE;YACrE,MAAM,IAAIZ,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,wGAAuGlB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAACM,UAAU,CAAE,EAAC,CAAC;UACjL;UACA;QACJ;MACA,KAAKpE,OAAO,CAACsD,WAAW,CAACe,IAAI;QAAE;UAC3B,IAAI,OAAO7B,GAAG,CAACwB,EAAE,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,+DAA8DlB,cAAc,CAACC,GAAG,CAACwB,EAAE,CAAE,EAAC,CAAC;UACxH;UACA,IAAI,CAACxB,GAAG,CAACwB,EAAE,EAAE;YACT,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,8CAA6C,CAAC;UAC/E;UACA,IAAI,CAACd,QAAQ,CAACH,GAAG,CAACsB,OAAO,CAAC,EAAE;YACxB,MAAM,IAAIN,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,qEAAoElB,cAAc,CAACC,GAAG,CAACsB,OAAO,CAAE,EAAC,CAAC;UACnI;UACA;QACJ;MACA,KAAK9D,OAAO,CAACsD,WAAW,CAACE,KAAK;QAAE;UAC5B,IAAI,OAAOhB,GAAG,CAACwB,EAAE,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,+DAA8DlB,cAAc,CAACC,GAAG,CAACwB,EAAE,CAAE,EAAC,CAAC;UACxH;UACA,IAAI,CAACxB,GAAG,CAACwB,EAAE,EAAE;YACT,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,8CAA6C,CAAC;UAC/E;UACA,IAAI,CAACb,gBAAgB,CAACJ,GAAG,CAACsB,OAAO,CAAC,EAAE;YAChC,MAAM,IAAIN,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,sFAAqFa,IAAI,CAACC,SAAS,CAAC/B,GAAG,CAACsB,OAAO,CAAE,EAAC,CAAC;UACpJ;UACA;QACJ;MACA,KAAK9D,OAAO,CAACsD,WAAW,CAACkB,QAAQ;QAAE;UAC/B,IAAI,OAAOhC,GAAG,CAACwB,EAAE,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,+DAA8DlB,cAAc,CAACC,GAAG,CAACwB,EAAE,CAAE,EAAC,CAAC;UACxH;UACA,IAAI,CAACxB,GAAG,CAACwB,EAAE,EAAE;YACT,MAAM,IAAIR,KAAK,CAAE,IAAGhB,GAAG,CAACiB,IAAK,8CAA6C,CAAC;UAC/E;UACA;QACJ;MACA;QACI,MAAM,IAAID,KAAK,CAAE,oCAAmChB,GAAG,CAACiB,IAAK,GAAE,CAAC;IACxE;IACA,OAAOjB,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASiC,SAASA,CAACjC,GAAG,EAAE;IACpB,IAAI;MACAe,eAAe,CAACf,GAAG,CAAC;MACpB,OAAO,IAAI;IACf,CAAC,CACD,OAAOkC,EAAE,EAAE;MACP,OAAO,KAAK;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACjC,OAAOtB,eAAe,CAAC,OAAOqB,IAAI,KAAK,QAAQ,GAAGN,IAAI,CAACQ,KAAK,CAACF,IAAI,EAAEC,OAAO,CAAC,GAAGD,IAAI,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACI,SAASG,gBAAgBA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IACrC1B,eAAe,CAACyB,GAAG,CAAC;IACpB,OAAOV,IAAI,CAACC,SAAS,CAACS,GAAG,EAAEC,QAAQ,CAAC;EACxC;;EAEA;AACJ;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;AACA;EACI,SAASC,YAAYA,CAACC,OAAO,EAAE;IAC3B,MAAM;MAAEC,GAAG;MAAEC,gBAAgB;MAAEC,IAAI,GAAG,IAAI;MAAEC,cAAc,GAAGC,OAAO,CAACC,KAAK;MAAEC,gBAAgB,EAAEC,kBAAkB,GAAG,CAAC;MAAEC,SAAS,GAAG,CAAC;MAAEC,WAAW;MAAEC,wBAAwB,GAAG,CAAC;MAAEC,aAAa,GAAG,CAAC;MAAEC,SAAS;QAAA,IAAAC,6BAAA,GAAAC,iBAAA,CAAG,WAA4CC,OAAO,EAAE;UAChQ,IAAIC,UAAU,GAAG,IAAI,CAAC,CAAC;UACvB,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,OAAO,EAAEjF,CAAC,EAAE,EAAE;YAC9BkF,UAAU,IAAI,CAAC;UACnB;UACA,MAAM,IAAI9E,OAAO,CAAEU,OAAO,IAAKqE,UAAU,CAACrE,OAAO,EAAEoE,UAAU;UACzD;UACAE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACxD,CAAC;QAAA,SAR6NC,4BAA4BA,CAAAC,EAAA;UAAA,OAAAT,6BAAA,CAAAhF,KAAA,OAAA0F,SAAA;QAAA;QAAA,OAA5BF,4BAA4B;MAAA,GAQzP;MAAEG,WAAW,GAAGC,gBAAgB;MAAEC,wBAAwB;MAAEC,EAAE;MAAEC,aAAa;MAC9E;AACR;AACA;AACA;AACA;AACA;AACA;MACQC,UAAU,GAAG,SAASC,YAAYA,CAAA,EAAG;QACjC,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAGC,CAAC,IAAK;UAClE,MAAMtF,CAAC,GAAIwE,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;YAAEhG,CAAC,GAAG4G,CAAC,IAAI,GAAG,GAAGtF,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;UACtE,OAAOtB,CAAC,CAAC6G,QAAQ,CAAC,EAAE,CAAC;QACzB,CAAC,CAAC;MACN,CAAC;MAAEC,mBAAmB,EAAErC,QAAQ;MAAEsC,kBAAkB,EAAE1C;IAAS,CAAC,GAAGM,OAAO;IAC1E,IAAIqC,EAAE;IACN,IAAIR,aAAa,EAAE;MACf,IAAI,CAACS,WAAW,CAACT,aAAa,CAAC,EAAE;QAC7B,MAAM,IAAIxD,KAAK,CAAC,2CAA2C,CAAC;MAChE;MACAgE,EAAE,GAAGR,aAAa;IACtB,CAAC,MACI,IAAI,OAAOU,SAAS,KAAK,WAAW,EAAE;MACvCF,EAAE,GAAGE,SAAS;IAClB,CAAC,MACI,IAAI,OAAO5H,MAAM,KAAK,WAAW,EAAE;MACpC0H,EAAE,GACE1H,MAAM,CAAC4H,SAAS;MACZ;MACA5H,MAAM,CAAC6H,YAAY;IAC/B,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MACpCJ,EAAE,GACEI,MAAM,CAACF,SAAS;MACZ;MACAE,MAAM,CAACD,YAAY;IAC/B;IACA,IAAI,CAACH,EAAE,EACH,MAAM,IAAIhE,KAAK,CAAC,uIAAuI,CAAC;IAC5J,MAAMqE,aAAa,GAAGL,EAAE;IACxB;IACA,MAAMM,OAAO,GAAG,CAAC,MAAM;MACnB,MAAMC,OAAO,GAAG,CAAC,MAAM;QACnB,MAAMC,SAAS,GAAG,CAAC,CAAC;QACpB,OAAO;UACHjB,EAAEA,CAAC/C,EAAE,EAAEiE,QAAQ,EAAE;YACbD,SAAS,CAAChE,EAAE,CAAC,GAAGiE,QAAQ;YACxB,OAAO,MAAM;cACT,OAAOD,SAAS,CAAChE,EAAE,CAAC;YACxB,CAAC;UACL,CAAC;UACDkE,IAAIA,CAACH,OAAO,EAAE;YACV,IAAIrD,EAAE;YACN,IAAI,IAAI,IAAIqD,OAAO,EACf,CAACrD,EAAE,GAAGsD,SAAS,CAACD,OAAO,CAAC/D,EAAE,CAAC,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyD,IAAI,CAACH,SAAS,EAAED,OAAO,CAAC;UACrG;QACJ,CAAC;MACL,CAAC,EAAE,CAAC;MACJ,MAAMC,SAAS,GAAG;QACdI,UAAU,EAAE,CAACrB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqB,UAAU,IAAI,CAACrB,EAAE,CAACqB,UAAU,CAAC,GAAG,EAAE;QAC1FC,MAAM,EAAE,CAACtB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,MAAM,IAAI,CAACtB,EAAE,CAACsB,MAAM,CAAC,GAAG,EAAE;QAC9EC,SAAS,EAAE,CAACvB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,SAAS,IAAI,CAACvB,EAAE,CAACuB,SAAS,CAAC,GAAG,EAAE;QACvFC,IAAI,EAAE,CAACxB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,IAAI,IAAI,CAACxB,EAAE,CAACwB,IAAI,CAAC,GAAG,EAAE;QACxEC,IAAI,EAAE,CAACzB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,IAAI,IAAI,CAACzB,EAAE,CAACyB,IAAI,CAAC,GAAG,EAAE;QACxET,OAAO,EAAE,CAAChB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,OAAO,IAAI,CAACA,OAAO,CAACG,IAAI,EAAEnB,EAAE,CAACgB,OAAO,CAAC,GAAG,CAACA,OAAO,CAACG,IAAI,CAAC;QAC3GO,MAAM,EAAE,CAAC1B,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,MAAM,IAAI,CAAC1B,EAAE,CAAC0B,MAAM,CAAC,GAAG,EAAE;QAC9EhD,KAAK,EAAE,CAACsB,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACtB,KAAK,IAAI,CAACsB,EAAE,CAACtB,KAAK,CAAC,GAAG;MAC7E,CAAC;MACD,OAAO;QACHiD,SAAS,EAAEX,OAAO,CAAChB,EAAE;QACrBA,EAAEA,CAAC4B,KAAK,EAAEV,QAAQ,EAAE;UAChB,MAAMW,CAAC,GAAGZ,SAAS,CAACW,KAAK,CAAC;UAC1BC,CAAC,CAACnH,IAAI,CAACwG,QAAQ,CAAC;UAChB,OAAO,MAAM;YACTW,CAAC,CAACC,MAAM,CAACD,CAAC,CAACE,OAAO,CAACb,QAAQ,CAAC,EAAE,CAAC,CAAC;UACpC,CAAC;QACL,CAAC;QACDC,IAAIA,CAACS,KAAK,EAAE,GAAGI,IAAI,EAAE;UACjB;UACA,KAAK,MAAMd,QAAQ,IAAI,CAAC,GAAGD,SAAS,CAACW,KAAK,CAAC,CAAC,EAAE;YAC1C;YACAV,QAAQ,CAAC,GAAGc,IAAI,CAAC;UACrB;QACJ;MACJ,CAAC;IACL,CAAC,EAAE,CAAC;IACJ;IACA;IACA,SAASC,aAAaA,CAACC,EAAE,EAAE;MACvB,MAAMC,SAAS,GAAG;MACd;MACApB,OAAO,CAACf,EAAE,CAAC,OAAO,EAAGoC,GAAG,IAAK;QACzBD,SAAS,CAACE,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;QAC3CJ,EAAE,CAACE,GAAG,CAAC;MACX,CAAC,CAAC;MACF;MACArB,OAAO,CAACf,EAAE,CAAC,QAAQ,EAAG4B,KAAK,IAAK;QAC5BO,SAAS,CAACE,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;QAC3CJ,EAAE,CAACN,KAAK,CAAC;MACb,CAAC,CAAC,CACL;IACL;IACA,IAAIP,UAAU;MAAEkB,KAAK,GAAG,CAAC;MAAE5D,gBAAgB;MAAE6D,QAAQ,GAAG,KAAK;MAAEpD,OAAO,GAAG,CAAC;MAAEqD,QAAQ,GAAG,KAAK;IAAC,SAC9EC,OAAOA,CAAA;MAAA,OAAAC,QAAA,CAAAzI,KAAA,OAAA0F,SAAA;IAAA;IA0JtB;AACR;AACA;IAFQ,SAAA+C,SAAA;MAAAA,QAAA,GAAAxD,iBAAA,CA1JA,aAAyB;QACrB;QACA;QACAyD,YAAY,CAACjE,gBAAgB,CAAC;QAC9B,MAAM,CAACkE,MAAM,EAAEC,YAAY,CAAC,SAAUzB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAIA,UAAU,GAAG,IAAI9G,OAAO,CAAC,CAACgH,SAAS,EAAEwB,MAAM,KAAK5D,iBAAA,CAAC,aAAY;UAC5J,IAAIqD,QAAQ,EAAE;YACV,MAAMvD,SAAS,CAACG,OAAO,CAAC;YACxB;YACA,IAAI,CAACmD,KAAK,EAAE;cACRlB,UAAU,GAAG2B,SAAS;cACtB,OAAOD,MAAM,CAAC;gBAAEE,IAAI,EAAE,IAAI;gBAAE/G,MAAM,EAAE;cAAyB,CAAC,CAAC;YACnE;YACAkD,OAAO,EAAE;UACb;UACA2B,OAAO,CAACI,IAAI,CAAC,YAAY,CAAC;UAC1B,MAAM0B,MAAM,GAAG,IAAI/B,aAAa,CAAC,OAAOzC,GAAG,KAAK,UAAU,SAASA,GAAG,CAAC,CAAC,GAAGA,GAAG,EAAEjC,6BAA6B,CAAC;UAC9G,IAAI8G,oBAAoB,EAAEC,UAAU;UACpC,SAASC,WAAWA,CAAA,EAAG;YACnB,IAAIC,QAAQ,CAACxE,SAAS,CAAC,IAAIA,SAAS,GAAG,CAAC,EAAE;cACtC+D,YAAY,CAACO,UAAU,CAAC,CAAC,CAAC;cAC1BA,UAAU,GAAG7D,UAAU,CAAC,MAAM;gBAC1B,IAAIuD,MAAM,CAACS,UAAU,KAAKxC,aAAa,CAACyC,IAAI,EAAE;kBAC1CV,MAAM,CAACW,IAAI,CAACxF,gBAAgB,CAAC;oBAAEtB,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACM;kBAAK,CAAC,CAAC,CAAC;kBACjEkE,OAAO,CAACI,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE6B,SAAS,CAAC;gBAC1C;cACJ,CAAC,EAAEnE,SAAS,CAAC;YACjB;UACJ;UACAoD,aAAa,CAAEwB,UAAU,IAAK;YAC1BpC,UAAU,GAAG2B,SAAS;YACtBJ,YAAY,CAACM,oBAAoB,CAAC;YAClCN,YAAY,CAACO,UAAU,CAAC;YACxBJ,MAAM,CAACU,UAAU,CAAC;YAClB,IAAI3D,gBAAgB,CAAC2D,UAAU,CAAC,IAAIA,UAAU,CAACR,IAAI,KAAK,IAAI,EAAE;cAC1DJ,MAAM,CAACa,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;cAClCb,MAAM,CAACc,OAAO,GAAG,IAAI;cACrBd,MAAM,CAACe,OAAO,GAAG,IAAI;YACzB;UACJ,CAAC,CAAC;UACFf,MAAM,CAACc,OAAO,GAAIvB,GAAG,IAAKrB,OAAO,CAACI,IAAI,CAAC,OAAO,EAAEiB,GAAG,CAAC;UACpDS,MAAM,CAACe,OAAO,GAAIhC,KAAK,IAAKb,OAAO,CAACI,IAAI,CAAC,QAAQ,EAAES,KAAK,CAAC;UACzDiB,MAAM,CAACgB,MAAM,gBAAA1E,iBAAA,CAAG,aAAY;YACxB,IAAI;cACA4B,OAAO,CAACI,IAAI,CAAC,QAAQ,EAAE0B,MAAM,CAAC;cAC9B,MAAM9F,OAAO,GAAG,OAAOuB,gBAAgB,KAAK,UAAU,SAC1CA,gBAAgB,CAAC,CAAC,GACxBA,gBAAgB;cACtB;cACA;cACA,IAAIuE,MAAM,CAACS,UAAU,KAAKxC,aAAa,CAACyC,IAAI,EACxC;cACJV,MAAM,CAACW,IAAI,CAACxF,gBAAgB,CAACjB,OAAO,GAC9B;gBACEL,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACI,cAAc;gBACxCI;cACJ,CAAC,GACC;gBACEL,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACI;gBAC1B;cACJ,CAAC,EAAEuB,QAAQ,CAAC,CAAC;cACjB,IAAImF,QAAQ,CAACtE,wBAAwB,CAAC,IAClCA,wBAAwB,GAAG,CAAC,EAAE;gBAC9BmE,oBAAoB,GAAG5D,UAAU,CAAC,MAAM;kBACpCuD,MAAM,CAACa,KAAK,CAACzK,OAAO,CAACqD,SAAS,CAACwH,gCAAgC,EAAE,oCAAoC,CAAC;gBAC1G,CAAC,EAAE/E,wBAAwB,CAAC;cAChC;cACAqE,WAAW,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CACD,OAAOhB,GAAG,EAAE;cACRrB,OAAO,CAACI,IAAI,CAAC,OAAO,EAAEiB,GAAG,CAAC;cAC1BS,MAAM,CAACa,KAAK,CAACzK,OAAO,CAACqD,SAAS,CAACyH,mBAAmB,EAAE9H,gBAAgB,CAACmG,GAAG,YAAY3F,KAAK,GAAG2F,GAAG,CAACpB,OAAO,GAAG,IAAIvE,KAAK,CAAC2F,GAAG,CAAC,CAACpB,OAAO,EAAE,uBAAuB,CAAC,CAAC;YAC/J;UACJ,CAAC;UACD,IAAIgD,YAAY,GAAG,KAAK;UACxBnB,MAAM,CAACoB,SAAS,GAAG,CAAC;YAAEpG;UAAK,CAAC,KAAK;YAC7B,IAAI;cACA,MAAMmD,OAAO,GAAGpD,YAAY,CAACC,IAAI,EAAEC,OAAO,CAAC;cAC3CiD,OAAO,CAACI,IAAI,CAAC,SAAS,EAAEH,OAAO,CAAC;cAChC,IAAIA,OAAO,CAACtE,IAAI,KAAK,MAAM,IAAIsE,OAAO,CAACtE,IAAI,KAAK,MAAM,EAAE;gBACpDqE,OAAO,CAACI,IAAI,CAACH,OAAO,CAACtE,IAAI,EAAE,IAAI,EAAEsE,OAAO,CAACjE,OAAO,CAAC,CAAC,CAAC;gBACnD,IAAIiE,OAAO,CAACtE,IAAI,KAAK,MAAM,EAAE;kBACzB0G,WAAW,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,MACI,IAAI,CAACtE,WAAW,EAAE;kBACnB;kBACA+D,MAAM,CAACW,IAAI,CAACxF,gBAAgB,CAACgD,OAAO,CAACjE,OAAO,GACtC;oBACEL,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACO,IAAI;oBAC9BC,OAAO,EAAEiE,OAAO,CAACjE;kBACrB,CAAC,GACC;oBACEL,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACO;oBAC1B;kBACJ,CAAC,CAAC,CAAC;;kBACPiE,OAAO,CAACI,IAAI,CAAC,MAAM,EAAE,KAAK,EAAEH,OAAO,CAACjE,OAAO,CAAC;gBAChD;gBACA,OAAO,CAAC;cACZ;;cACA,IAAIiH,YAAY,EACZ,OAAO,CAAC;cACZ,IAAIhD,OAAO,CAACtE,IAAI,KAAKzD,OAAO,CAACsD,WAAW,CAACK,aAAa,EAClD,MAAM,IAAIH,KAAK,CAAE,mCAAkCuE,OAAO,CAACtE,IAAK,EAAC,CAAC;cACtEkG,YAAY,CAACM,oBAAoB,CAAC;cAClCc,YAAY,GAAG,IAAI;cACnBjD,OAAO,CAACI,IAAI,CAAC,WAAW,EAAE0B,MAAM,EAAE7B,OAAO,CAACjE,OAAO,CAAC,CAAC,CAAC;cACpDyF,QAAQ,GAAG,KAAK,CAAC,CAAC;cAClBpD,OAAO,GAAG,CAAC,CAAC,CAAC;cACbmC,SAAS,CAAC,CACNsB,MAAM,EACN,IAAItI,OAAO,CAAC,CAAC2J,CAAC,EAAE9I,MAAM,KAAK6G,aAAa,CAAC7G,MAAM,CAAC,CAAC,CACpD,CAAC;YACN,CAAC,CACD,OAAOgH,GAAG,EAAE;cACRS,MAAM,CAACoB,SAAS,GAAG,IAAI,CAAC,CAAC;cACzBlD,OAAO,CAACI,IAAI,CAAC,OAAO,EAAEiB,GAAG,CAAC;cAC1BS,MAAM,CAACa,KAAK,CAACzK,OAAO,CAACqD,SAAS,CAAC6H,WAAW,EAAElI,gBAAgB,CAACmG,GAAG,YAAY3F,KAAK,GAAG2F,GAAG,CAACpB,OAAO,GAAG,IAAIvE,KAAK,CAAC2F,GAAG,CAAC,CAACpB,OAAO,EAAE,cAAc,CAAC,CAAC;YAC9I;UACJ,CAAC;QACL,CAAC,EAAE,CAAC,CAAG;QACP;QACA,IAAI6B,MAAM,CAACS,UAAU,KAAKxC,aAAa,CAACsD,OAAO,EAC3C,MAAMtB,YAAY;QACtB,IAAIuB,OAAO,GAAGA,CAAA,KAAM;UAChB;QAAA,CACH;QACD,MAAMC,QAAQ,GAAG,IAAI/J,OAAO,CAAEU,OAAO,IAAMoJ,OAAO,GAAGpJ,OAAQ,CAAC;QAC9D,OAAO,CACH4H,MAAM,EACNwB,OAAO,EACP9J,OAAO,CAACgK,IAAI,CAAC;QACT;QACAD,QAAQ,CAACpJ,IAAI,CAAC,MAAM;UAChB,IAAI,CAACqH,KAAK,EAAE;YACR;YACA,MAAMiC,QAAQ,GAAGA,CAAA,KAAM3B,MAAM,CAACa,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC3D,IAAIL,QAAQ,CAACzE,kBAAkB,CAAC,IAAIA,kBAAkB,GAAG,CAAC,EAAE;cACxD;cACA;cACAD,gBAAgB,GAAGW,UAAU,CAAC,MAAM;gBAChC,IAAIuD,MAAM,CAACS,UAAU,KAAKxC,aAAa,CAACyC,IAAI,EACxCiB,QAAQ,CAAC,CAAC;cAClB,CAAC,EAAE5F,kBAAkB,CAAC;YAC1B,CAAC,MACI;cACD;cACA4F,QAAQ,CAAC,CAAC;YACd;UACJ;QACJ,CAAC,CAAC;QACF;QACA1B,YAAY,CACf,CAAC,CACL;MACL,CAAC;MAAA,OAAAH,QAAA,CAAAzI,KAAA,OAAA0F,SAAA;IAAA;IAID,SAAS6E,yBAAyBA,CAACC,eAAe,EAAE;MAChD;MACA,IAAI5E,gBAAgB,CAAC4E,eAAe,CAAC,KAChCC,wBAAwB,CAACD,eAAe,CAACzB,IAAI,CAAC,IAC3C,CACIhK,OAAO,CAACqD,SAAS,CAACsI,mBAAmB,EACrC3L,OAAO,CAACqD,SAAS,CAACyH,mBAAmB,EACrC9K,OAAO,CAACqD,SAAS,CAACuI,UAAU,EAC5B5L,OAAO,CAACqD,SAAS,CAAC6H,WAAW,EAC7BlL,OAAO,CAACqD,SAAS,CAACwI,YAAY;MAC9B;MACA7L,OAAO,CAACqD,SAAS,CAACyI,wBAAwB;MAC1C;MACA;MACA9L,OAAO,CAACqD,SAAS,CAAC0I,uBAAuB,EACzC/L,OAAO,CAACqD,SAAS,CAAC2I;MAClB;MAAA,CACH,CAACC,QAAQ,CAACR,eAAe,CAACzB,IAAI,CAAC,CAAC,EACrC,MAAMyB,eAAe;MACzB;MACA,IAAIjC,QAAQ,EACR,OAAO,KAAK;MAChB;MACA;MACA,IAAI3C,gBAAgB,CAAC4E,eAAe,CAAC,IAAIA,eAAe,CAACzB,IAAI,KAAK,IAAI,EAClE,OAAOV,KAAK,GAAG,CAAC;MACpB;MACA,IAAI,CAACvD,aAAa,IAAII,OAAO,IAAIJ,aAAa,EAC1C,MAAM0F,eAAe;MACzB;MACA,IAAI,CAAC7E,WAAW,CAAC6E,eAAe,CAAC,EAC7B,MAAMA,eAAe;MACzB;MACA,IAAI3E,wBAAwB,KAAK,IAAI,IAAIA,wBAAwB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,wBAAwB,CAAC2E,eAAe,CAAC,EAC7H,MAAMA,eAAe;MACzB;MACA,OAAQlC,QAAQ,GAAG,IAAI;IAC3B;IACA;IACA,IAAI,CAACjE,IAAI,EAAE;MACPY,iBAAA,CAAC,aAAY;QACToD,KAAK,EAAE;QACP,SAAS;UACL,IAAI;YACA,MAAM,IAAKO,YAAY,CAAC,SAASJ,OAAO,CAAC,CAAC;YAC1C,MAAMI,YAAY,CAAC,CAAC;UACxB,CAAC,CACD,OAAO4B,eAAe,EAAE;YACpB,IAAI;cACA,IAAI,CAACD,yBAAyB,CAACC,eAAe,CAAC,EAC3C;YACR,CAAC,CACD,OAAOA,eAAe,EAAE;cACpB;cACA,OAAOlG,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACkG,eAAe,CAAC;YAC1G;UACJ;QACJ;MACJ,CAAC,EAAE,CAAC;IACR;IACA,OAAO;MACH1E,EAAE,EAAEe,OAAO,CAACf,EAAE;MACdmF,SAASA,CAACpI,OAAO,EAAEqI,IAAI,EAAE;QACrB,MAAMnI,EAAE,GAAGiD,UAAU,CAACnD,OAAO,CAAC;QAC9B,IAAIsI,IAAI,GAAG,KAAK;UAAEC,OAAO,GAAG,KAAK;UAAEC,QAAQ,GAAGA,CAAA,KAAM;YAChD;YACAhD,KAAK,EAAE;YACP8C,IAAI,GAAG,IAAI;UACf,CAAC;QACDlG,iBAAA,CAAC,aAAY;UACToD,KAAK,EAAE;UACP,SAAS;YACL,IAAI;cACA,MAAM,CAACM,MAAM,EAAEwB,OAAO,EAAEmB,4BAA4B,CAAC,SAAS9C,OAAO,CAAC,CAAC;cACvE;cACA,IAAI2C,IAAI,EACJ,OAAOhB,OAAO,CAAC,CAAC;cACpB,MAAM/B,QAAQ,GAAGvB,OAAO,CAACY,SAAS,CAAC1E,EAAE,EAAG+D,OAAO,IAAK;gBAChD,QAAQA,OAAO,CAACtE,IAAI;kBAChB,KAAKzD,OAAO,CAACsD,WAAW,CAACe,IAAI;oBAAE;sBAC3B;sBACA8H,IAAI,CAACK,IAAI,CAACzE,OAAO,CAACjE,OAAO,CAAC;sBAC1B;oBACJ;kBACA,KAAK9D,OAAO,CAACsD,WAAW,CAACE,KAAK;oBAAE;sBAC3B6I,OAAO,GAAG,IAAI,EAAID,IAAI,GAAG,IAAK;sBAC/BD,IAAI,CAAC1G,KAAK,CAACsC,OAAO,CAACjE,OAAO,CAAC;sBAC3BwI,QAAQ,CAAC,CAAC;sBACV;oBACJ;kBACA,KAAKtM,OAAO,CAACsD,WAAW,CAACkB,QAAQ;oBAAE;sBAC/B4H,IAAI,GAAG,IAAI;sBACXE,QAAQ,CAAC,CAAC,CAAC,CAAC;sBACZ;oBACJ;gBACJ;cACJ,CAAC,CAAC;cACF1C,MAAM,CAACW,IAAI,CAACxF,gBAAgB,CAAC;gBACzBf,EAAE;gBACFP,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACS,SAAS;gBACnCD;cACJ,CAAC,EAAEmB,QAAQ,CAAC,CAAC;cACbqH,QAAQ,GAAGA,CAAA,KAAM;gBACb,IAAI,CAACF,IAAI,IAAIxC,MAAM,CAACS,UAAU,KAAKxC,aAAa,CAACyC,IAAI;kBACjD;kBACAV,MAAM,CAACW,IAAI,CAACxF,gBAAgB,CAAC;oBACzBf,EAAE;oBACFP,IAAI,EAAEzD,OAAO,CAACsD,WAAW,CAACkB;kBAC9B,CAAC,EAAES,QAAQ,CAAC,CAAC;gBACjBqE,KAAK,EAAE;gBACP8C,IAAI,GAAG,IAAI;gBACXhB,OAAO,CAAC,CAAC;cACb,CAAC;cACD;cACA;cACA;cACA,MAAMmB,4BAA4B,CAACE,OAAO,CAACpD,QAAQ,CAAC;cACpD,OAAO,CAAC;YACZ,CAAC,CACD,OAAOoC,eAAe,EAAE;cACpB,IAAI,CAACD,yBAAyB,CAACC,eAAe,CAAC,EAC3C;YACR;UACJ;QACJ,CAAC,EAAE,CAAC,CACCxJ,IAAI,CAAC,MAAM;UACZ;UACA,IAAI,CAACoK,OAAO,EACRF,IAAI,CAACZ,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAAA,CACEmB,KAAK,CAAEvD,GAAG,IAAK;UAChBgD,IAAI,CAAC1G,KAAK,CAAC0D,GAAG,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC;QACJ,OAAO,MAAM;UACT;UACA,IAAI,CAACiD,IAAI,EACLE,QAAQ,CAAC,CAAC;QAClB,CAAC;MACL,CAAC;MACDK,OAAOA,CAACC,OAAO,EAAE;QACb,MAAMC,OAAO,GAAG,EAAE;QAClB,MAAMC,QAAQ,GAAG;UACbV,IAAI,EAAE,KAAK;UACX3G,KAAK,EAAE,IAAI;UACXzD,OAAO,EAAEA,CAAA,KAAM;YACX;UAAA;QAER,CAAC;QACD,MAAM+K,OAAO,GAAG,IAAI,CAACb,SAAS,CAACU,OAAO,EAAE;UACpCJ,IAAIA,CAAChK,GAAG,EAAE;YACNqK,OAAO,CAACpL,IAAI,CAACe,GAAG,CAAC;YACjBsK,QAAQ,CAAC9K,OAAO,CAAC,CAAC;UACtB,CAAC;UACDyD,KAAKA,CAAC0D,GAAG,EAAE;YACP2D,QAAQ,CAACV,IAAI,GAAG,IAAI;YACpBU,QAAQ,CAACrH,KAAK,GAAG0D,GAAG;YACpB2D,QAAQ,CAAC9K,OAAO,CAAC,CAAC;UACtB,CAAC;UACDuJ,QAAQA,CAAA,EAAG;YACPuB,QAAQ,CAACV,IAAI,GAAG,IAAI;YACpBU,QAAQ,CAAC9K,OAAO,CAAC,CAAC;UACtB;QACJ,CAAC,CAAC;QACF,MAAMgL,QAAQ,GAAI,SAASA,QAAQA,CAAA,EAAG;UAClC,OAAOvM,gBAAgB,CAAC,IAAI,EAAEkG,SAAS,EAAE,UAAUsG,UAAUA,CAAA,EAAG;YAC5D,SAAS;cACL,IAAI,CAACJ,OAAO,CAACvK,MAAM,EAAE;gBACjB;gBACA,MAAM/B,OAAO,CAAC,IAAIe,OAAO,CAAEU,OAAO,IAAM8K,QAAQ,CAAC9K,OAAO,GAAGA,OAAQ,CAAC,CAAC;cACzE;cACA;cACA,OAAO6K,OAAO,CAACvK,MAAM,EAAE;gBACnB;gBACA,MAAM,MAAM/B,OAAO,CAACsM,OAAO,CAACxK,KAAK,CAAC,CAAC,CAAC;cACxC;cACA;cACA,IAAIyK,QAAQ,CAACrH,KAAK,EAAE;gBAChB,MAAMqH,QAAQ,CAACrH,KAAK;cACxB;cACA;cACA,IAAIqH,QAAQ,CAACV,IAAI,EAAE;gBACf,OAAO,MAAM7L,OAAO,CAAC,KAAK,CAAC,CAAC;cAChC;YACJ;UACJ,CAAC,CAAC;QACN,CAAC,CAAE,CAAC;QACJyM,QAAQ,CAACE,KAAK;UAAA,IAAAC,KAAA,GAAAjH,iBAAA,CAAG,WAAOiD,GAAG,EAAK;YAC5B,IAAI,CAAC2D,QAAQ,CAACV,IAAI,EAAE;cAChBU,QAAQ,CAACV,IAAI,GAAG,IAAI;cACpBU,QAAQ,CAACrH,KAAK,GAAG0D,GAAG;cACpB2D,QAAQ,CAAC9K,OAAO,CAAC,CAAC;YACtB;YACA,OAAO;cAAEoK,IAAI,EAAE,IAAI;cAAErK,KAAK,EAAEgI;YAAU,CAAC;UAC3C,CAAC;UAAA,iBAAAqD,GAAA;YAAA,OAAAD,KAAA,CAAAlM,KAAA,OAAA0F,SAAA;UAAA;QAAA;QACDqG,QAAQ,CAACK,MAAM,gBAAAnH,iBAAA,CAAG,aAAY;UAC1B6G,OAAO,CAAC,CAAC;UACT,OAAO;YAAEX,IAAI,EAAE,IAAI;YAAErK,KAAK,EAAEgI;UAAU,CAAC;QAC3C,CAAC;QACD,OAAOiD,QAAQ;MACnB,CAAC;MACKD,OAAOA,CAAA,EAAG;QAAA,OAAA7G,iBAAA;UACZsD,QAAQ,GAAG,IAAI;UACf,IAAIpB,UAAU,EAAE;YACZ;YACA,MAAM,CAACwB,MAAM,CAAC,SAASxB,UAAU;YACjCwB,MAAM,CAACa,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC;UACxC;QAAC;MACL,CAAC;MACD6C,SAASA,CAAA,EAAG;QACR,IAAIlF,UAAU,EAAE;UACZ;UACAN,OAAO,CAACI,IAAI,CAAC,QAAQ,EAAE;YACnB8B,IAAI,EAAE,IAAI;YACV/G,MAAM,EAAE,YAAY;YACpBsK,QAAQ,EAAE;UACd,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;EACL;EACA,SAAS1G,gBAAgBA,CAACrE,GAAG,EAAE;IAC3B,OAAOG,QAAQ,CAACH,GAAG,CAAC,IAAI,MAAM,IAAIA,GAAG,IAAI,QAAQ,IAAIA,GAAG;EAC5D;EACA,SAASkJ,wBAAwBA,CAAC1B,IAAI,EAAE;IACpC,IAAI,CACA,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CAAE;IAAA,CACT,CAACiC,QAAQ,CAACjC,IAAI,CAAC,EACZ,OAAO,KAAK;IAChB;IACA,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,IAAI;EACvC;EACA,SAASvC,WAAWA,CAACjF,GAAG,EAAE;IACtB,OAAQ,OAAOA,GAAG,KAAK,UAAU,IAC7B,aAAa,IAAIA,GAAG,IACpB,QAAQ,IAAIA,GAAG,IACf,SAAS,IAAIA,GAAG,IAChB,YAAY,IAAIA,GAAG,IACnB,MAAM,IAAIA,GAAG;EACrB;EAEAxC,OAAO,CAACoD,8BAA8B,GAAGA,8BAA8B;EACvEpD,OAAO,CAACmD,6BAA6B,GAAGA,6BAA6B;EACrEnD,OAAO,CAACkF,YAAY,GAAGA,YAAY;EACnClF,OAAO,CAACyE,SAAS,GAAGA,SAAS;EAC7BzE,OAAO,CAAC2E,YAAY,GAAGA,YAAY;EACnC3E,OAAO,CAAC+E,gBAAgB,GAAGA,gBAAgB;EAC3C/E,OAAO,CAACuD,eAAe,GAAGA,eAAe;AAE7C,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}