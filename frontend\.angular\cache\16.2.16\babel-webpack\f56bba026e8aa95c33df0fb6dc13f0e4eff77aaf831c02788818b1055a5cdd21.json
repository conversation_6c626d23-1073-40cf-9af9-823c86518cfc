{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵelement(3, \"i\", 22)(4, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 24);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProfileComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 20)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28)(4, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 29);\n    i0.ɵɵtext(7, \" Succ\\u00E8s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_26_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 88);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_26_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 89);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_26_button_27_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction ProfileComponent_div_26_button_27__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 97);\n    i0.ɵɵelement(1, \"circle\", 98)(2, \"path\", 99);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_26_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onUpload());\n    });\n    i0.ɵɵelement(1, \"div\", 91)(2, \"div\", 92);\n    i0.ɵɵelementStart(3, \"span\", 93);\n    i0.ɵɵtemplate(4, ProfileComponent_div_26_button_27_i_4_Template, 1, 0, \"i\", 94);\n    i0.ɵɵtemplate(5, ProfileComponent_div_26_button_27__svg_svg_5_Template, 3, 0, \"svg\", 95);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.uploadLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.uploadLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.uploadLoading ? \"T\\u00E9l\\u00E9chargement...\" : \"T\\u00E9l\\u00E9charger\");\n  }\n}\nfunction ProfileComponent_div_26_button_28_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 103);\n  }\n}\nfunction ProfileComponent_div_26_button_28__svg_svg_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 97);\n    i0.ɵɵelement(1, \"circle\", 98)(2, \"path\", 99);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_26_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.removeProfileImage());\n    });\n    i0.ɵɵelement(1, \"div\", 100)(2, \"div\", 101);\n    i0.ɵɵelementStart(3, \"span\", 93);\n    i0.ɵɵtemplate(4, ProfileComponent_div_26_button_28_i_4_Template, 1, 0, \"i\", 102);\n    i0.ɵɵtemplate(5, ProfileComponent_div_26_button_28__svg_svg_5_Template, 3, 0, \"svg\", 95);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.removeLoading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.removeLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.removeLoading ? \"Suppression...\" : \"Supprimer\");\n  }\n}\nfunction ProfileComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵelementStart(4, \"div\", 34)(5, \"div\", 35);\n    i0.ɵɵelement(6, \"div\", 36);\n    i0.ɵɵelementStart(7, \"div\", 37);\n    i0.ɵɵtemplate(8, ProfileComponent_div_26_img_8_Template, 1, 1, \"img\", 38);\n    i0.ɵɵtemplate(9, ProfileComponent_div_26_img_9_Template, 1, 1, \"img\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"h2\", 40);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 41);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 42);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 43)(18, \"label\", 44);\n    i0.ɵɵelement(19, \"i\", 45);\n    i0.ɵɵtext(20, \" Photo de profil \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 46)(22, \"div\", 47)(23, \"input\", 48);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_26_Template_input_change_23_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 49);\n    i0.ɵɵelement(25, \"div\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51);\n    i0.ɵɵtemplate(27, ProfileComponent_div_26_button_27_Template, 8, 4, \"button\", 52);\n    i0.ɵɵtemplate(28, ProfileComponent_div_26_button_28_Template, 8, 4, \"button\", 52);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(29, \"div\", 53)(30, \"div\", 31);\n    i0.ɵɵelement(31, \"div\", 32)(32, \"div\", 33);\n    i0.ɵɵelementStart(33, \"h3\", 54)(34, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(35, \"svg\", 56);\n    i0.ɵɵelement(36, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(37, \"div\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Informations du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 59)(40, \"div\", 60)(41, \"div\", 61);\n    i0.ɵɵelement(42, \"div\", 62);\n    i0.ɵɵelementStart(43, \"div\", 63);\n    i0.ɵɵtext(44, \" Nom complet \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 64);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 60)(48, \"div\", 61);\n    i0.ɵɵelement(49, \"div\", 62);\n    i0.ɵɵelementStart(50, \"div\", 63);\n    i0.ɵɵtext(51, \" Adresse email \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 64);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 60)(55, \"div\", 61);\n    i0.ɵɵelement(56, \"div\", 62);\n    i0.ɵɵelementStart(57, \"div\", 63);\n    i0.ɵɵtext(58, \" Type de compte \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 64);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 60)(63, \"div\", 61);\n    i0.ɵɵelement(64, \"div\", 62);\n    i0.ɵɵelementStart(65, \"div\", 63);\n    i0.ɵɵtext(66, \" Membre depuis \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 64);\n    i0.ɵɵtext(68);\n    i0.ɵɵpipe(69, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(70, \"div\", 31);\n    i0.ɵɵelement(71, \"div\", 32)(72, \"div\", 33);\n    i0.ɵɵelementStart(73, \"h3\", 54)(74, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(75, \"svg\", 56);\n    i0.ɵɵelement(76, \"path\", 65)(77, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(78, \"div\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(79, \" Actions du compte \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 67)(81, \"a\", 68);\n    i0.ɵɵelement(82, \"div\", 69)(83, \"div\", 70);\n    i0.ɵɵelementStart(84, \"span\", 71)(85, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(86, \"svg\", 73);\n    i0.ɵɵelement(87, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(88, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Changer le mot de passe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_Template_button_click_90_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.logout());\n    });\n    i0.ɵɵelement(91, \"div\", 77)(92, \"div\", 78);\n    i0.ɵɵelementStart(93, \"span\", 79)(94, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(95, \"svg\", 73);\n    i0.ɵɵelement(96, \"path\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(97, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(98, \" D\\u00E9connexion \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(99, \"a\", 82);\n    i0.ɵɵelement(100, \"div\", 83)(101, \"div\", 84);\n    i0.ɵɵelementStart(102, \"span\", 85)(103, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(104, \"svg\", 73);\n    i0.ɵɵelement(105, \"path\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(106, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(107, \" Tableau de bord \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewUrl || ctx_r3.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewUrl && !ctx_r3.uploadLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.fullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.email, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 12, ctx_r3.user.role), \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.user.profileImage || ctx_r3.user.image || ctx_r3.user.profileImageURL);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.fullName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.email, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(61, 14, ctx_r3.user.role), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(69, 16, ctx_r3.user.createdAt, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(31);\n    i0.ɵɵproperty(\"routerLink\", ctx_r3.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, authuserService, dataService, router, fb) {\n    this.authService = authService;\n    this.authuserService = authuserService;\n    this.dataService = dataService;\n    this.router = router;\n    this.fb = fb;\n    this.selectedImage = null;\n    this.previewUrl = null;\n    this.message = '';\n    this.error = '';\n    this.uploadLoading = false;\n    this.removeLoading = false;\n    // Edit profile functionality\n    this.isEditMode = false;\n    this.editLoading = false;\n    this.progressPercentage = 0;\n    this.editForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      dateOfBirth: [''],\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: [''],\n      position: [''],\n      bio: ['', [Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    // Load user profile using DataService\n    this.dataService.getProfile().subscribe({\n      next: res => {\n        this.user = res;\n        // Ensure image properties are consistent\n        if (!this.user.profileImage && this.user.image) {\n          this.user.profileImage = this.user.image;\n        } else if (!this.user.image && this.user.profileImage) {\n          this.user.image = this.user.profileImage;\n        }\n        // If no image is available, use default\n        if (!this.user.profileImage || this.user.profileImage === 'null' || this.user.profileImage.trim() === '') {\n          this.user.profileImage = 'assets/images/default-profile.png';\n          this.user.image = 'assets/images/default-profile.png';\n        }\n        // Ensure profileImageURL is also set for backward compatibility\n        if (!this.user.profileImageURL) {\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\n        }\n      },\n      error: () => {\n        this.error = 'Failed to load profile.';\n      }\n    });\n  }\n  /**\n   * Returns the appropriate profile image URL based on available properties\n   * Uses the same logic as in front-layout component for consistency\n   */\n  getProfileImageUrl() {\n    if (!this.user) return 'assets/images/default-profile.png';\n    // Check profileImage first\n    if (this.user.profileImage && this.user.profileImage !== 'null' && this.user.profileImage.trim() !== '') {\n      return this.user.profileImage;\n    }\n    // Then check image\n    if (this.user.image && this.user.image !== 'null' && this.user.image.trim() !== '') {\n      return this.user.image;\n    }\n    // Then check profileImageURL (for backward compatibility)\n    if (this.user.profileImageURL && this.user.profileImageURL !== 'null' && this.user.profileImageURL.trim() !== '') {\n      return this.user.profileImageURL;\n    }\n    // Default fallback\n    return 'assets/images/default-profile.png';\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      const file = input.files[0];\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n        this.resetFileInput();\n        return;\n      }\n      if (file.size > 2 * 1024 * 1024) {\n        this.error = \"L'image ne doit pas dépasser 2MB\";\n        this.resetFileInput();\n        return;\n      }\n      this.selectedImage = file;\n      this.error = '';\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewUrl = e.target?.result || null;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onUpload() {\n    if (!this.selectedImage) return;\n    this.uploadLoading = true; // Activer l'état de chargement\n    this.message = '';\n    this.error = '';\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\n    this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n      this.uploadLoading = false;\n      console.log('Upload finished, uploadLoading:', this.uploadLoading);\n    })).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile updated successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = response.imageUrl;\n        this.user.profileImage = response.imageUrl;\n        this.user.image = response.imageUrl;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Upload failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  removeProfileImage() {\n    if (!confirm('Are you sure you want to remove your profile picture?')) return;\n    this.removeLoading = true;\n    this.message = '';\n    this.error = '';\n    this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile picture removed successfully';\n        // Update all image properties to ensure consistency across the application\n        this.user.profileImageURL = null;\n        this.user.profileImage = null;\n        this.user.image = null;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        // Also update in AuthUserService to ensure all components are updated\n        this.authuserService.setCurrentUser({\n          ...this.user,\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Removal failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  resetFileInput() {\n    this.selectedImage = null;\n    this.previewUrl = null;\n    const fileInput = document.getElementById('profile-upload');\n    if (fileInput) fileInput.value = '';\n  }\n  navigateTo(path) {\n    this.router.navigate([path]);\n  }\n  logout() {\n    this.authuserService.logout().subscribe({\n      next: () => {\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authuserService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/login'], {});\n        }, 100);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 27,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"relative\", \"mb-5\", \"group/avatar\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"opacity-0\", \"group-hover/avatar:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"-z-10\"], [1, \"w-28\", \"h-28\", \"rounded-full\", \"border-4\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"group-hover/avatar:border-[#4f5fad]\", \"dark:group-hover/avatar:border-[#6d78c9]\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", \"duration-300\", \"relative\", \"z-10\", 2, \"min-height\", \"112px\", \"min-width\", \"112px\"], [\"alt\", \"Profile\", \"class\", \"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Preview\", \"class\", \"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\", 3, \"src\", 4, \"ngIf\"], [1, \"text-lg\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-xs\", \"rounded-full\", \"backdrop-blur-sm\"], [1, \"mt-6\", \"w-full\"], [\"for\", \"profile-upload\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-camera\", \"mr-1.5\"], [1, \"flex\", \"flex-wrap\", \"items-center\", \"gap-2\"], [1, \"relative\", \"w-full\", \"group/upload\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"file:mr-3\", \"file:py-1.5\", \"file:px-3\", \"file:rounded-lg\", \"file:border-0\", \"file:text-xs\", \"file:bg-[#4f5fad]\", \"dark:file:bg-[#6d78c9]\", \"file:text-white\", \"hover:file:bg-[#3d4a85]\", \"dark:hover:file:bg-[#4f5fad]\", \"file:transition-colors\", 3, \"change\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within/upload:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"gap-2\", \"w-full\", \"mt-3\"], [\"class\", \"relative overflow-hidden group/btn flex-1\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"md:col-span-2\", \"space-y-6\"], [1, \"text-base\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-4\", \"flex\", \"items-center\"], [1, \"relative\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"space-y-4\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"p-4\", \"rounded-lg\", \"backdrop-blur-sm\", \"group/item\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"mb-1\"], [1, \"w-1\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\", \"mr-2\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"font-medium\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-3\", \"group-hover/item:translate-x-1\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/change-password\", 1, \"relative\", \"overflow-hidden\", \"group/btn\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]/10\", \"to-[#4f5fad]/10\", \"dark:from-[#6d78c9]/10\", \"dark:to-[#4f5fad]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"relative\", \"mr-1.5\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"relative\", \"z-10\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]/10\", \"to-[#ff8785]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff8785]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"], [1, \"absolute\", \"inset-0\", \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", \"transition-transform\", \"duration-300\", \"group-hover/avatar:scale-105\", 3, \"src\"], [\"alt\", \"Preview\", 1, \"h-full\", \"w-full\", \"object-cover\", \"transition-transform\", \"duration-300\", \"group-hover/avatar:scale-105\", 3, \"src\"], [1, \"relative\", \"overflow-hidden\", \"group/btn\", \"flex-1\", 3, \"disabled\", \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"fas fa-upload mr-1.5\", 4, \"ngIf\"], [\"class\", \"animate-spin mr-1.5 h-3.5 w-3.5 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"fas\", \"fa-upload\", \"mr-1.5\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-1.5\", \"h-3.5\", \"w-3.5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/btn:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"rounded-lg\", \"opacity-0\", \"group-hover/btn:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [\"class\", \"fas fa-trash-alt mr-1.5\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1.5\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"h1\", 9);\n          i0.ɵɵtext(20, \" Mon Profil \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 10);\n          i0.ɵɵtext(22, \" G\\u00E9rez vos informations personnelles et vos pr\\u00E9f\\u00E9rences \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, ProfileComponent_div_23_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(24, ProfileComponent_div_24_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(25, ProfileComponent_div_25_Template, 10, 1, \"div\", 13);\n          i0.ɵɵtemplate(26, ProfileComponent_div_26_Template, 108, 19, \"div\", 14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngIf\", !ctx.user);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i6.NgIf, i4.RouterLink, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\"\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  border: 4px solid rgba(0, 0, 0, 0.1);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  border-left-color: #09f;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.form-loading[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2ZpbGUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw4QkFBOEI7QUFDOUI7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNULG9DQUFvQztFQUNwQyxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsYUFBYTtBQUNmOztBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLFdBQVc7RUFDWCxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLHVCQUF1QjtFQUN2QixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7QUFDQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtBQUN6QiIsImZpbGUiOiJwcm9maWxlLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvZmlsZS9wcm9maWxlLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsOEJBQThCO0FBQzlCO0VBQ0UsZUFBZTtFQUNmLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxvQ0FBb0M7RUFDcEMsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxXQUFXO0VBQ1gsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQix1QkFBdUI7RUFDdkIsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGO0FBQ0E7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUEsb3FEQUFvcUQiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBZGQgdG8geW91ciBjb21wb25lbnQgQ1NTICovXHJcbi5sb2FkaW5nLW92ZXJsYXkge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbn1cclxuXHJcbi5zcGlubmVyIHtcclxuICBib3JkZXI6IDRweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgd2lkdGg6IDM2cHg7XHJcbiAgaGVpZ2h0OiAzNnB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA5ZjtcclxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbi5mb3JtLWxvYWRpbmcge1xyXG4gIG1pbi1oZWlnaHQ6IDMwMHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵtextInterpolate1", "ctx_r2", "message", "ɵɵproperty", "ctx_r4", "getProfileImageUrl", "ɵɵsanitizeUrl", "ctx_r5", "previewUrl", "ɵɵnamespaceSVG", "ɵɵlistener", "ProfileComponent_div_26_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "onUpload", "ɵɵtemplate", "ProfileComponent_div_26_button_27_i_4_Template", "ProfileComponent_div_26_button_27__svg_svg_5_Template", "ctx_r6", "uploadLoading", "ProfileComponent_div_26_button_28_Template_button_click_0_listener", "_r15", "ctx_r14", "removeProfileImage", "ProfileComponent_div_26_button_28_i_4_Template", "ProfileComponent_div_26_button_28__svg_svg_5_Template", "ctx_r7", "removeLoading", "ProfileComponent_div_26_img_8_Template", "ProfileComponent_div_26_img_9_Template", "ProfileComponent_div_26_Template_input_change_23_listener", "$event", "_r17", "ctx_r16", "onFileSelected", "ProfileComponent_div_26_button_27_Template", "ProfileComponent_div_26_button_28_Template", "ɵɵnamespaceHTML", "ProfileComponent_div_26_Template_button_click_90_listener", "ctx_r18", "logout", "ctx_r3", "user", "fullName", "email", "ɵɵpipeBind1", "role", "selectedImage", "profileImage", "image", "profileImageURL", "ɵɵpipeBind2", "createdAt", "ProfileComponent", "constructor", "authService", "authuserService", "dataService", "router", "fb", "isEditMode", "editLoading", "progressPercentage", "editForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "dateOfBirth", "phoneNumber", "pattern", "department", "position", "bio", "address", "skills", "ngOnInit", "getProfile", "subscribe", "next", "res", "trim", "event", "input", "target", "files", "length", "file", "validTypes", "includes", "type", "resetFileInput", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "console", "log", "uploadProfileImage", "pipe", "response", "imageUrl", "updateCurrentUser", "setCurrentUser", "token", "localStorage", "setItem", "setTimeout", "err", "confirm", "fileInput", "document", "getElementById", "value", "navigateTo", "path", "navigate", "clearAuthData", "queryParams", "replaceUrl", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "i5", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_23_Template", "ProfileComponent_div_24_Template", "ProfileComponent_div_25_Template", "ProfileComponent_div_26_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Router } from '@angular/router';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { User } from 'src/app/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.css'],\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  user: any;\r\n  selectedImage: File | null = null;\r\n  previewUrl: string | null = null;\r\n  message = '';\r\n  error = '';\r\n  uploadLoading = false;\r\n  removeLoading = false;\r\n\r\n  // Edit profile functionality\r\n  isEditMode = false;\r\n  editForm: FormGroup;\r\n  editLoading = false;\r\n  progressPercentage = 0;\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private authuserService: AuthuserService,\r\n    private dataService: DataService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.editForm = this.fb.group({\r\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\r\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\r\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      dateOfBirth: [''],\r\n      phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\r\n      department: [''],\r\n      position: [''],\r\n      bio: ['', [Validators.minLength(10)]],\r\n      address: [''],\r\n      skills: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Load user profile using DataService\r\n    this.dataService.getProfile().subscribe({\r\n      next: (res) => {\r\n        this.user = res;\r\n\r\n        // Ensure image properties are consistent\r\n        if (!this.user.profileImage && this.user.image) {\r\n          this.user.profileImage = this.user.image;\r\n        } else if (!this.user.image && this.user.profileImage) {\r\n          this.user.image = this.user.profileImage;\r\n        }\r\n\r\n        // If no image is available, use default\r\n        if (\r\n          !this.user.profileImage ||\r\n          this.user.profileImage === 'null' ||\r\n          this.user.profileImage.trim() === ''\r\n        ) {\r\n          this.user.profileImage = 'assets/images/default-profile.png';\r\n          this.user.image = 'assets/images/default-profile.png';\r\n        }\r\n\r\n        // Ensure profileImageURL is also set for backward compatibility\r\n        if (!this.user.profileImageURL) {\r\n          this.user.profileImageURL = this.user.profileImage || this.user.image;\r\n        }\r\n      },\r\n      error: () => {\r\n        this.error = 'Failed to load profile.';\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns the appropriate profile image URL based on available properties\r\n   * Uses the same logic as in front-layout component for consistency\r\n   */\r\n  getProfileImageUrl(): string {\r\n    if (!this.user) return 'assets/images/default-profile.png';\r\n\r\n    // Check profileImage first\r\n    if (\r\n      this.user.profileImage &&\r\n      this.user.profileImage !== 'null' &&\r\n      this.user.profileImage.trim() !== ''\r\n    ) {\r\n      return this.user.profileImage;\r\n    }\r\n\r\n    // Then check image\r\n    if (\r\n      this.user.image &&\r\n      this.user.image !== 'null' &&\r\n      this.user.image.trim() !== ''\r\n    ) {\r\n      return this.user.image;\r\n    }\r\n\r\n    // Then check profileImageURL (for backward compatibility)\r\n    if (\r\n      this.user.profileImageURL &&\r\n      this.user.profileImageURL !== 'null' &&\r\n      this.user.profileImageURL.trim() !== ''\r\n    ) {\r\n      return this.user.profileImageURL;\r\n    }\r\n\r\n    // Default fallback\r\n    return 'assets/images/default-profile.png';\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      const file = input.files[0];\r\n\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.error = \"L'image ne doit pas dépasser 2MB\";\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      this.selectedImage = file;\r\n      this.error = '';\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        this.previewUrl = (e.target?.result as string) || null;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedImage) return;\r\n\r\n    this.uploadLoading = true; // Activer l'état de chargement\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\r\n\r\n    this.dataService\r\n      .uploadProfileImage(this.selectedImage)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.uploadLoading = false;\r\n          console.log('Upload finished, uploadLoading:', this.uploadLoading);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message = response.message || 'Profile updated successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = response.imageUrl;\r\n          this.user.profileImage = response.imageUrl;\r\n          this.user.image = response.imageUrl;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          this.selectedImage = null;\r\n          this.previewUrl = null;\r\n          this.resetFileInput();\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Upload failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  removeProfileImage(): void {\r\n    if (!confirm('Are you sure you want to remove your profile picture?'))\r\n      return;\r\n\r\n    this.removeLoading = true;\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    this.dataService\r\n      .removeProfileImage()\r\n      .pipe(finalize(() => (this.removeLoading = false)))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message =\r\n            response.message || 'Profile picture removed successfully';\r\n\r\n          // Update all image properties to ensure consistency across the application\r\n          this.user.profileImageURL = null;\r\n          this.user.profileImage = null;\r\n          this.user.image = null;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          // Also update in AuthUserService to ensure all components are updated\r\n          this.authuserService.setCurrentUser({\r\n            ...this.user,\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Removal failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  private resetFileInput(): void {\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    const fileInput = document.getElementById(\r\n      'profile-upload'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n  }\r\n\r\n  navigateTo(path: string): void {\r\n    this.router.navigate([path]);\r\n  }\r\n\r\n  logout(): void {\r\n    this.authuserService.logout().subscribe({\r\n      next: () => {\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Logout error:', err);\r\n        this.authuserService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/login'], {});\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-6 relative z-10\">\r\n    <!-- Page Title -->\r\n    <div class=\"mb-8\">\r\n      <h1\r\n        class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n      >\r\n        Mon Profil\r\n      </h1>\r\n      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n        Gérez vos informations personnelles et vos préférences\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"!user\" class=\"flex justify-center items-center py-20\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Error Message -->\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\r\n            Erreur\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div\r\n      *ngIf=\"message\"\r\n      class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <div class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xl relative\">\r\n          <i class=\"fas fa-check-circle\"></i>\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n          ></div>\r\n        </div>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-1\">\r\n            Succès\r\n          </h3>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            {{ message }}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- User Profile -->\r\n    <div *ngIf=\"user\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n      <!-- Profile Card -->\r\n      <div\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n      >\r\n        <!-- Decorative gradient top border -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n        ></div>\r\n\r\n        <!-- Glow effect on hover -->\r\n        <div\r\n          class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n        ></div>\r\n\r\n        <div class=\"flex flex-col items-center\">\r\n          <!-- Profile Image with Glow Effect -->\r\n          <div class=\"relative mb-5 group/avatar\">\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full opacity-0 group-hover/avatar:opacity-100 blur-xl transition-opacity duration-300 -z-10\"\r\n            ></div>\r\n\r\n            <div\r\n              class=\"w-28 h-28 rounded-full border-4 border-[#edf1f4] dark:border-[#2a2a2a] group-hover/avatar:border-[#4f5fad] dark:group-hover/avatar:border-[#6d78c9] overflow-hidden flex items-center justify-center transition-colors duration-300 relative z-10\"\r\n              style=\"min-height: 112px; min-width: 112px\"\r\n            >\r\n              <img\r\n                *ngIf=\"!previewUrl || uploadLoading\"\r\n                [src]=\"getProfileImageUrl()\"\r\n                alt=\"Profile\"\r\n                class=\"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\"\r\n              />\r\n              <img\r\n                *ngIf=\"previewUrl && !uploadLoading\"\r\n                [src]=\"previewUrl\"\r\n                alt=\"Preview\"\r\n                class=\"h-full w-full object-cover transition-transform duration-300 group-hover/avatar:scale-105\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <h2\r\n            class=\"text-lg font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-1\"\r\n          >\r\n            {{ user.fullName }}\r\n          </h2>\r\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-2\">\r\n            {{ user.email }}\r\n          </p>\r\n          <div\r\n            class=\"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-xs rounded-full backdrop-blur-sm\"\r\n          >\r\n            {{ user.role | titlecase }}\r\n          </div>\r\n\r\n          <!-- Upload Profile Image -->\r\n          <div class=\"mt-6 w-full\">\r\n            <label\r\n              for=\"profile-upload\"\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-camera mr-1.5\"></i>\r\n              Photo de profil\r\n            </label>\r\n            <div class=\"flex flex-wrap items-center gap-2\">\r\n              <div class=\"relative w-full group/upload\">\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"profile-upload\"\r\n                  accept=\"image/*\"\r\n                  (change)=\"onFileSelected($event)\"\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] w-full px-3 py-2 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all file:mr-3 file:py-1.5 file:px-3 file:rounded-lg file:border-0 file:text-xs file:bg-[#4f5fad] dark:file:bg-[#6d78c9] file:text-white hover:file:bg-[#3d4a85] dark:hover:file:bg-[#4f5fad] file:transition-colors\"\r\n                />\r\n                <div\r\n                  class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within/upload:opacity-100 transition-opacity\"\r\n                >\r\n                  <div\r\n                    class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                  ></div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"flex items-center gap-2 w-full mt-3\">\r\n                <!-- Upload Button -->\r\n                <button\r\n                  *ngIf=\"selectedImage\"\r\n                  (click)=\"onUpload()\"\r\n                  class=\"relative overflow-hidden group/btn flex-1\"\r\n                  [disabled]=\"uploadLoading\"\r\n                >\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/btn:scale-105 disabled:opacity-50\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300 disabled:opacity-0\"\r\n                  ></div>\r\n                  <span\r\n                    class=\"relative flex items-center justify-center text-white font-medium py-2 px-3 rounded-lg transition-all z-10\"\r\n                  >\r\n                    <i *ngIf=\"!uploadLoading\" class=\"fas fa-upload mr-1.5\"></i>\r\n                    <svg\r\n                      *ngIf=\"uploadLoading\"\r\n                      class=\"animate-spin mr-1.5 h-3.5 w-3.5 text-white\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <circle\r\n                        class=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        stroke-width=\"4\"\r\n                      ></circle>\r\n                      <path\r\n                        class=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                      ></path>\r\n                    </svg>\r\n                    <span>{{\r\n                      uploadLoading ? \"Téléchargement...\" : \"Télécharger\"\r\n                    }}</span>\r\n                  </span>\r\n                </button>\r\n\r\n                <!-- Remove Button -->\r\n                <button\r\n                  *ngIf=\"\r\n                    user.profileImage || user.image || user.profileImageURL\r\n                  \"\r\n                  (click)=\"removeProfileImage()\"\r\n                  class=\"relative overflow-hidden group/btn flex-1\"\r\n                  [disabled]=\"removeLoading\"\r\n                >\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] rounded-lg transition-transform duration-300 group-hover/btn:scale-105 disabled:opacity-50\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300 disabled:opacity-0\"\r\n                  ></div>\r\n                  <span\r\n                    class=\"relative flex items-center justify-center text-white font-medium py-2 px-3 rounded-lg transition-all z-10\"\r\n                  >\r\n                    <i\r\n                      *ngIf=\"!removeLoading\"\r\n                      class=\"fas fa-trash-alt mr-1.5\"\r\n                    ></i>\r\n                    <svg\r\n                      *ngIf=\"removeLoading\"\r\n                      class=\"animate-spin mr-1.5 h-3.5 w-3.5 text-white\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <circle\r\n                        class=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        stroke-width=\"4\"\r\n                      ></circle>\r\n                      <path\r\n                        class=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                      ></path>\r\n                    </svg>\r\n                    <span>{{\r\n                      removeLoading ? \"Suppression...\" : \"Supprimer\"\r\n                    }}</span>\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Account Information -->\r\n      <div class=\"md:col-span-2 space-y-6\">\r\n        <!-- Account Details -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Informations du compte\r\n          </h3>\r\n\r\n          <div class=\"space-y-4\">\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Nom complet\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.fullName }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Adresse email\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.email }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Type de compte\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.role | titlecase }}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              class=\"bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 p-4 rounded-lg backdrop-blur-sm group/item hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center mb-1\">\r\n                <div\r\n                  class=\"w-1 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full mr-2\"\r\n                ></div>\r\n                <div\r\n                  class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider font-medium\"\r\n                >\r\n                  Membre depuis\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-3 group-hover/item:translate-x-1 transition-transform\"\r\n              >\r\n                {{ user.createdAt | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Account Actions -->\r\n        <div\r\n          class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-6 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative overflow-hidden group\"\r\n        >\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <h3\r\n            class=\"text-base font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-4 flex items-center\"\r\n          >\r\n            <div class=\"relative mr-2\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-5 w-5 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\r\n                />\r\n                <path\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                  stroke-width=\"2\"\r\n                  d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                />\r\n              </svg>\r\n              <!-- Glow effect -->\r\n              <div\r\n                class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\r\n              ></div>\r\n            </div>\r\n            Actions du compte\r\n          </h3>\r\n\r\n          <div class=\"flex flex-wrap gap-3\">\r\n            <!-- Change Password Button -->\r\n            <a\r\n              routerLink=\"/change-password\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#4f5fad] dark:text-[#6d78c9] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#4f5fad] dark:border-[#6d78c9]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Changer le mot de passe\r\n              </span>\r\n            </a>\r\n\r\n            <!-- Logout Button -->\r\n            <button\r\n              (click)=\"logout()\"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69]/10 to-[#ff8785]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#ff6b69] dark:text-[#ff8785] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#ff6b69] dark:border-[#ff8785]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Déconnexion\r\n              </span>\r\n            </button>\r\n\r\n            <!-- Dashboard Button -->\r\n            <a\r\n              [routerLink]=\"\r\n                user.role === 'admin' ? '/admin/dashboard' : '/home'\r\n              \"\r\n              class=\"relative overflow-hidden group/btn\"\r\n            >\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg transition-transform duration-300 group-hover/btn:scale-105\"\r\n              ></div>\r\n              <div\r\n                class=\"absolute inset-0 bg-[#6d6870]/10 dark:bg-[#a0a0a0]/10 rounded-lg opacity-0 group-hover/btn:opacity-100 blur-md transition-opacity duration-300\"\r\n              ></div>\r\n              <span\r\n                class=\"relative flex items-center text-[#6d6870] dark:text-[#a0a0a0] font-medium py-2 px-3 rounded-lg transition-all z-10 border border-[#6d6870] dark:border-[#a0a0a0]\"\r\n              >\r\n                <div class=\"relative mr-1.5\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-4 w-4 relative z-10 group-hover/btn:scale-110 transition-transform\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\r\n                    />\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity blur-md rounded-full\"\r\n                  ></div>\r\n                </div>\r\n                Tableau de bord\r\n              </span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;IC0CrCC,EAAA,CAAAC,cAAA,cAAkE;IAE9DD,EAAA,CAAAE,SAAA,cAEO;IAKTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAAfH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAMvER,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAsD;IACpDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAgCIX,EAAA,CAAAE,SAAA,cAKE;;;;IAHAF,EAAA,CAAAY,UAAA,QAAAC,MAAA,CAAAC,kBAAA,IAAAd,EAAA,CAAAe,aAAA,CAA4B;;;;;IAI9Bf,EAAA,CAAAE,SAAA,cAKE;;;;IAHAF,EAAA,CAAAY,UAAA,QAAAI,MAAA,CAAAC,UAAA,EAAAjB,EAAA,CAAAe,aAAA,CAAkB;;;;;IAiEdf,EAAA,CAAAE,SAAA,YAA2D;;;;;IAC3DF,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,iBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApCVH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAmB,UAAA,mBAAAC,mEAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAIpB1B,EAAA,CAAAE,SAAA,cAEO;IAIPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAA2B,UAAA,IAAAC,8CAAA,gBAA2D;IAC3D5B,EAAA,CAAA2B,UAAA,IAAAE,qDAAA,kBAoBM;IACN7B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAEJ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAnCXH,EAAA,CAAAY,UAAA,aAAAkB,MAAA,CAAAC,aAAA,CAA0B;IAWpB/B,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAY,UAAA,UAAAkB,MAAA,CAAAC,aAAA,CAAoB;IAErB/B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,UAAA,SAAAkB,MAAA,CAAAC,aAAA,CAAmB;IAoBhB/B,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,iBAAA,CAAAwB,MAAA,CAAAC,aAAA,2DAEJ;;;;;IAsBF/B,EAAA,CAAAE,SAAA,aAGK;;;;;IACLF,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,iBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAzCVH,EAAA,CAAAC,cAAA,iBAOC;IAHCD,EAAA,CAAAmB,UAAA,mBAAAa,mEAAA;MAAAhC,EAAA,CAAAqB,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAS,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9BnC,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAA2B,UAAA,IAAAS,8CAAA,iBAGK;IACLpC,EAAA,CAAA2B,UAAA,IAAAU,qDAAA,kBAoBM;IACNrC,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAEJ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAtCXH,EAAA,CAAAY,UAAA,aAAA0B,MAAA,CAAAC,aAAA,CAA0B;IAYrBvC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAY,UAAA,UAAA0B,MAAA,CAAAC,aAAA,CAAoB;IAIpBvC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAY,UAAA,SAAA0B,MAAA,CAAAC,aAAA,CAAmB;IAoBhBvC,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAM,iBAAA,CAAAgC,MAAA,CAAAC,aAAA,kCAEJ;;;;;;IA5KlBvC,EAAA,CAAAC,cAAA,cAAgE;IAM5DD,EAAA,CAAAE,SAAA,cAEO;IAOPF,EAAA,CAAAC,cAAA,cAAwC;IAGpCD,EAAA,CAAAE,SAAA,cAEO;IAEPF,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAA2B,UAAA,IAAAa,sCAAA,kBAKE;IACFxC,EAAA,CAAA2B,UAAA,IAAAc,sCAAA,kBAKE;IACJzC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyB;IAKrBD,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAA+C;IAMzCD,EAAA,CAAAmB,UAAA,oBAAAuB,0DAAAC,MAAA;MAAA3C,EAAA,CAAAqB,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAAwB,aAAA;MAAA,OAAUxB,EAAA,CAAAyB,WAAA,CAAAoB,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAJnC3C,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAiD;IAE/CD,EAAA,CAAA2B,UAAA,KAAAoB,0CAAA,qBAyCS;IAGT/C,EAAA,CAAA2B,UAAA,KAAAqB,0CAAA,qBA8CS;IACXhD,EAAA,CAAAG,YAAA,EAAM;IAOdH,EAAA,CAAAC,cAAA,eAAqC;IAMjCD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAiD,eAAA,EAEC;IAFDjD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,gCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAuB;IAKjBD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAEC;IAEGD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAEC;IAECD,EAAA,CAAAE,SAAA,eAEO;IAOPF,EAAA,CAAAC,cAAA,cAEC;IAEGD,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IAOJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAiD,eAAA,EAEC;IAFDjD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAkC;IAM9BD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IAEGD,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAiD,eAAA,EAEC;IAFDjD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAmB,UAAA,mBAAA+B,0DAAA;MAAAlD,EAAA,CAAAqB,aAAA,CAAAuB,IAAA;MAAA,MAAAO,OAAA,GAAAnD,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA0B,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBpD,EAAA,CAAAE,SAAA,eAEO;IAIPF,EAAA,CAAAC,cAAA,gBAEC;IAEGD,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAE,SAAA,gBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAiD,eAAA,EAEC;IAFDjD,EAAA,CAAAE,SAAA,eAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,aAKC;IACCD,EAAA,CAAAE,SAAA,gBAEO;IAIPF,EAAA,CAAAC,cAAA,iBAEC;IAEGD,EAAA,CAAAkB,cAAA,EAMC;IANDlB,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAE,SAAA,iBAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAiD,eAAA,EAEC;IAFDjD,EAAA,CAAAE,SAAA,gBAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAvbJH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAY,UAAA,UAAAyC,MAAA,CAAApC,UAAA,IAAAoC,MAAA,CAAAtB,aAAA,CAAkC;IAMlC/B,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAY,UAAA,SAAAyC,MAAA,CAAApC,UAAA,KAAAoC,MAAA,CAAAtB,aAAA,CAAkC;IAWvC/B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,MAAA,CAAAC,IAAA,CAAAC,QAAA,MACF;IAEEvD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,MAAA,CAAAC,IAAA,CAAAE,KAAA,MACF;IAIExD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAyD,WAAA,SAAAJ,MAAA,CAAAC,IAAA,CAAAI,IAAA,OACF;IAgCS1D,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAY,UAAA,SAAAyC,MAAA,CAAAM,aAAA,CAAmB;IA4CnB3D,EAAA,CAAAK,SAAA,GAEA;IAFAL,EAAA,CAAAY,UAAA,SAAAyC,MAAA,CAAAC,IAAA,CAAAM,YAAA,IAAAP,MAAA,CAAAC,IAAA,CAAAO,KAAA,IAAAR,MAAA,CAAAC,IAAA,CAAAQ,eAAA,CAEA;IA6GH9D,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,MAAA,CAAAC,IAAA,CAAAC,QAAA,MACF;IAmBEvD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAA4C,MAAA,CAAAC,IAAA,CAAAE,KAAA,MACF;IAmBExD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAAyD,WAAA,SAAAJ,MAAA,CAAAC,IAAA,CAAAI,IAAA,OACF;IAmBE1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAS,kBAAA,MAAAT,EAAA,CAAA+D,WAAA,SAAAV,MAAA,CAAAC,IAAA,CAAAU,SAAA,qBACF;IAkIAhE,EAAA,CAAAK,SAAA,IAEC;IAFDL,EAAA,CAAAY,UAAA,eAAAyC,MAAA,CAAAC,IAAA,CAAAI,IAAA,4CAEC;;;AD3gBf,OAAM,MAAOO,gBAAgB;EAe3BC,YACUC,WAAwB,EACxBC,eAAgC,EAChCC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAJf,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAlBZ,KAAAZ,aAAa,GAAgB,IAAI;IACjC,KAAA1C,UAAU,GAAkB,IAAI;IAChC,KAAAN,OAAO,GAAG,EAAE;IACZ,KAAAH,KAAK,GAAG,EAAE;IACV,KAAAuB,aAAa,GAAG,KAAK;IACrB,KAAAQ,aAAa,GAAG,KAAK;IAErB;IACA,KAAAiC,UAAU,GAAG,KAAK;IAElB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,kBAAkB,GAAG,CAAC;IASpB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC5BC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAACiF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAClF,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAACiF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DxB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACzD,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAACiF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DvB,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1D,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAAC0D,KAAK,CAAC,CAAC;MACpDyB,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACpF,UAAU,CAACqF,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC1DC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACxF,UAAU,CAACiF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCQ,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACpB,WAAW,CAACqB,UAAU,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACvC,IAAI,GAAGuC,GAAG;QAEf;QACA,IAAI,CAAC,IAAI,CAACvC,IAAI,CAACM,YAAY,IAAI,IAAI,CAACN,IAAI,CAACO,KAAK,EAAE;UAC9C,IAAI,CAACP,IAAI,CAACM,YAAY,GAAG,IAAI,CAACN,IAAI,CAACO,KAAK;SACzC,MAAM,IAAI,CAAC,IAAI,CAACP,IAAI,CAACO,KAAK,IAAI,IAAI,CAACP,IAAI,CAACM,YAAY,EAAE;UACrD,IAAI,CAACN,IAAI,CAACO,KAAK,GAAG,IAAI,CAACP,IAAI,CAACM,YAAY;;QAG1C;QACA,IACE,CAAC,IAAI,CAACN,IAAI,CAACM,YAAY,IACvB,IAAI,CAACN,IAAI,CAACM,YAAY,KAAK,MAAM,IACjC,IAAI,CAACN,IAAI,CAACM,YAAY,CAACkC,IAAI,EAAE,KAAK,EAAE,EACpC;UACA,IAAI,CAACxC,IAAI,CAACM,YAAY,GAAG,mCAAmC;UAC5D,IAAI,CAACN,IAAI,CAACO,KAAK,GAAG,mCAAmC;;QAGvD;QACA,IAAI,CAAC,IAAI,CAACP,IAAI,CAACQ,eAAe,EAAE;UAC9B,IAAI,CAACR,IAAI,CAACQ,eAAe,GAAG,IAAI,CAACR,IAAI,CAACM,YAAY,IAAI,IAAI,CAACN,IAAI,CAACO,KAAK;;MAEzE,CAAC;MACDrD,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,yBAAyB;MACxC;KACD,CAAC;EACJ;EAEA;;;;EAIAM,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACwC,IAAI,EAAE,OAAO,mCAAmC;IAE1D;IACA,IACE,IAAI,CAACA,IAAI,CAACM,YAAY,IACtB,IAAI,CAACN,IAAI,CAACM,YAAY,KAAK,MAAM,IACjC,IAAI,CAACN,IAAI,CAACM,YAAY,CAACkC,IAAI,EAAE,KAAK,EAAE,EACpC;MACA,OAAO,IAAI,CAACxC,IAAI,CAACM,YAAY;;IAG/B;IACA,IACE,IAAI,CAACN,IAAI,CAACO,KAAK,IACf,IAAI,CAACP,IAAI,CAACO,KAAK,KAAK,MAAM,IAC1B,IAAI,CAACP,IAAI,CAACO,KAAK,CAACiC,IAAI,EAAE,KAAK,EAAE,EAC7B;MACA,OAAO,IAAI,CAACxC,IAAI,CAACO,KAAK;;IAGxB;IACA,IACE,IAAI,CAACP,IAAI,CAACQ,eAAe,IACzB,IAAI,CAACR,IAAI,CAACQ,eAAe,KAAK,MAAM,IACpC,IAAI,CAACR,IAAI,CAACQ,eAAe,CAACgC,IAAI,EAAE,KAAK,EAAE,EACvC;MACA,OAAO,IAAI,CAACxC,IAAI,CAACQ,eAAe;;IAGlC;IACA,OAAO,mCAAmC;EAC5C;EAEAhB,cAAcA,CAACiD,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAEC,MAAM,EAAE;MACvB,MAAMC,IAAI,GAAGJ,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,MAAMG,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACnC,IAAI,CAAC/F,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAACgG,cAAc,EAAE;QACrB;;MAGF,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACjG,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAACgG,cAAc,EAAE;QACrB;;MAGF,IAAI,CAAC7C,aAAa,GAAGyC,IAAI;MACzB,IAAI,CAAC5F,KAAK,GAAG,EAAE;MAEf,MAAMkG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAAC5F,UAAU,GAAI4F,CAAC,CAACZ,MAAM,EAAEa,MAAiB,IAAI,IAAI;MACxD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;EAE9B;EAEA1E,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACiC,aAAa,EAAE;IAEzB,IAAI,CAAC5B,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACpB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEfwG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAClF,aAAa,CAAC;IAEjE,IAAI,CAACsC,WAAW,CACb6C,kBAAkB,CAAC,IAAI,CAACvD,aAAa,CAAC,CACtCwD,IAAI,CACHpH,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACgC,aAAa,GAAG,KAAK;MAC1BiF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAClF,aAAa,CAAC;IACpE,CAAC,CAAC,CACH,CACA4D,SAAS,CAAC;MACTC,IAAI,EAAGwB,QAAa,IAAI;QACtB,IAAI,CAACzG,OAAO,GAAGyG,QAAQ,CAACzG,OAAO,IAAI,8BAA8B;QAEjE;QACA,IAAI,CAAC2C,IAAI,CAACQ,eAAe,GAAGsD,QAAQ,CAACC,QAAQ;QAC7C,IAAI,CAAC/D,IAAI,CAACM,YAAY,GAAGwD,QAAQ,CAACC,QAAQ;QAC1C,IAAI,CAAC/D,IAAI,CAACO,KAAK,GAAGuD,QAAQ,CAACC,QAAQ;QAEnC;QACA,IAAI,CAAChD,WAAW,CAACiD,iBAAiB,CAAC;UACjC1D,YAAY,EAAEwD,QAAQ,CAACC,QAAQ;UAC/BxD,KAAK,EAAEuD,QAAQ,CAACC;SACjB,CAAC;QAEF;QACA,IAAI,CAACjD,eAAe,CAACmD,cAAc,CAAC;UAClC,GAAG,IAAI,CAACjE,IAAI;UACZM,YAAY,EAAEwD,QAAQ,CAACC,QAAQ;UAC/BxD,KAAK,EAAEuD,QAAQ,CAACC;SACjB,CAAC;QAEF,IAAI,CAAC1D,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC1C,UAAU,GAAG,IAAI;QACtB,IAAI,CAACuF,cAAc,EAAE;QAErB,IAAIY,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAAChH,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGoH,GAAmC,IAAI;QAC7C,IAAI,CAACpH,KAAK,GAAGoH,GAAG,CAACpH,KAAK,EAAEG,OAAO,IAAI,eAAe;QAClD;QACAgH,UAAU,CAAC,MAAK;UACd,IAAI,CAACnH,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEA2B,kBAAkBA,CAAA;IAChB,IAAI,CAAC0F,OAAO,CAAC,uDAAuD,CAAC,EACnE;IAEF,IAAI,CAACtF,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC5B,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC6D,WAAW,CACblC,kBAAkB,EAAE,CACpBgF,IAAI,CAACpH,QAAQ,CAAC,MAAO,IAAI,CAACwC,aAAa,GAAG,KAAM,CAAC,CAAC,CAClDoD,SAAS,CAAC;MACTC,IAAI,EAAGwB,QAAa,IAAI;QACtB,IAAI,CAACzG,OAAO,GACVyG,QAAQ,CAACzG,OAAO,IAAI,sCAAsC;QAE5D;QACA,IAAI,CAAC2C,IAAI,CAACQ,eAAe,GAAG,IAAI;QAChC,IAAI,CAACR,IAAI,CAACM,YAAY,GAAG,IAAI;QAC7B,IAAI,CAACN,IAAI,CAACO,KAAK,GAAG,IAAI;QAEtB;QACA,IAAI,CAACQ,WAAW,CAACiD,iBAAiB,CAAC;UACjC1D,YAAY,EAAE,mCAAmC;UACjDC,KAAK,EAAE;SACR,CAAC;QAEF;QACA,IAAI,CAACO,eAAe,CAACmD,cAAc,CAAC;UAClC,GAAG,IAAI,CAACjE,IAAI;UACZM,YAAY,EAAE,mCAAmC;UACjDC,KAAK,EAAE;SACR,CAAC;QAEF,IAAIuD,QAAQ,CAACI,KAAK,EAAE;UAClBC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACI,KAAK,CAAC;;QAG/C;QACAG,UAAU,CAAC,MAAK;UACd,IAAI,CAAChH,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDH,KAAK,EAAGoH,GAAmC,IAAI;QAC7C,IAAI,CAACpH,KAAK,GAAGoH,GAAG,CAACpH,KAAK,EAAEG,OAAO,IAAI,gBAAgB;QACnD;QACAgH,UAAU,CAAC,MAAK;UACd,IAAI,CAACnH,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEQgG,cAAcA,CAAA;IACpB,IAAI,CAAC7C,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC1C,UAAU,GAAG,IAAI;IACtB,MAAM6G,SAAS,GAAGC,QAAQ,CAACC,cAAc,CACvC,gBAAgB,CACG;IACrB,IAAIF,SAAS,EAAEA,SAAS,CAACG,KAAK,GAAG,EAAE;EACrC;EAEAC,UAAUA,CAACC,IAAY;IACrB,IAAI,CAAC7D,MAAM,CAAC8D,QAAQ,CAAC,CAACD,IAAI,CAAC,CAAC;EAC9B;EAEA/E,MAAMA,CAAA;IACJ,IAAI,CAACgB,eAAe,CAAChB,MAAM,EAAE,CAACuC,SAAS,CAAC;MACtCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxB,eAAe,CAACiE,aAAa,EAAE;QACpCV,UAAU,CAAC,MAAK;UACd,IAAI,CAACrD,MAAM,CAAC8D,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;YAC/BE,WAAW,EAAE;cAAE3H,OAAO,EAAE;YAAqB,CAAE;YAC/C4H,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD/H,KAAK,EAAGoH,GAAQ,IAAI;QAClBZ,OAAO,CAACxG,KAAK,CAAC,eAAe,EAAEoH,GAAG,CAAC;QACnC,IAAI,CAACxD,eAAe,CAACiE,aAAa,EAAE;QACpCV,UAAU,CAAC,MAAK;UACd,IAAI,CAACrD,MAAM,CAAC8D,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACtC,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;;;uBA7RWnE,gBAAgB,EAAAjE,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5I,EAAA,CAAAwI,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAAwI,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAhJ,EAAA,CAAAwI,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBjF,gBAAgB;MAAAkF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7BzJ,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAAuD;UAMjDD,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,+EACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAA2B,UAAA,KAAAgI,gCAAA,kBAUM;UAGN3J,EAAA,CAAA2B,UAAA,KAAAiI,gCAAA,mBAmBM;UAGN5J,EAAA,CAAA2B,UAAA,KAAAkI,gCAAA,mBAqBM;UAGN7J,EAAA,CAAA2B,UAAA,KAAAmI,gCAAA,qBAudM;UACR9J,EAAA,CAAAG,YAAA,EAAM;;;UAnhBEH,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAAY,UAAA,UAAA8I,GAAA,CAAApG,IAAA,CAAW;UAcdtD,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAY,UAAA,SAAA8I,GAAA,CAAAlJ,KAAA,CAAW;UAsBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAY,UAAA,SAAA8I,GAAA,CAAA/I,OAAA,CAAa;UAuBVX,EAAA,CAAAK,SAAA,GAAU;UAAVL,EAAA,CAAAY,UAAA,SAAA8I,GAAA,CAAApG,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}