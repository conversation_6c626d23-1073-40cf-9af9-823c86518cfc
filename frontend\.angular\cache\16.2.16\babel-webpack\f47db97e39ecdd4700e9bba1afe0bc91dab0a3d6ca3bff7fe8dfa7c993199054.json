{"ast": null, "code": "import { __extends, __spreadArray } from \"tslib\";\nimport \"../utilities/globals/index.js\";\nimport { isNonNullObject } from \"../utilities/index.js\";\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport var PROTOCOL_ERRORS_SYMBOL = Symbol();\nexport function graphQLResultHasProtocolErrors(result) {\n  if (result.extensions) {\n    return Array.isArray(result.extensions[PROTOCOL_ERRORS_SYMBOL]);\n  }\n  return false;\n}\nexport function isApolloError(err) {\n  return err.hasOwnProperty(\"graphQLErrors\");\n}\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nvar generateErrorMessage = function (err) {\n  var errors = __spreadArray(__spreadArray(__spreadArray([], err.graphQLErrors, true), err.clientErrors, true), err.protocolErrors, true);\n  if (err.networkError) errors.push(err.networkError);\n  return errors\n  // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n  .map(function (err) {\n    return isNonNullObject(err) && err.message || \"Error message not found.\";\n  }).join(\"\\n\");\n};\nvar ApolloError = /** @class */function (_super) {\n  __extends(ApolloError, _super);\n  // Constructs an instance of ApolloError given serialized GraphQL errors,\n  // client errors, protocol errors or network errors.\n  // Note that one of these has to be a valid\n  // value or the constructed error will be meaningless.\n  function ApolloError(_a) {\n    var graphQLErrors = _a.graphQLErrors,\n      protocolErrors = _a.protocolErrors,\n      clientErrors = _a.clientErrors,\n      networkError = _a.networkError,\n      errorMessage = _a.errorMessage,\n      extraInfo = _a.extraInfo;\n    var _this = _super.call(this, errorMessage) || this;\n    _this.name = \"ApolloError\";\n    _this.graphQLErrors = graphQLErrors || [];\n    _this.protocolErrors = protocolErrors || [];\n    _this.clientErrors = clientErrors || [];\n    _this.networkError = networkError || null;\n    _this.message = errorMessage || generateErrorMessage(_this);\n    _this.extraInfo = extraInfo;\n    _this.cause = __spreadArray(__spreadArray(__spreadArray([networkError], graphQLErrors || [], true), protocolErrors || [], true), clientErrors || [], true).find(function (e) {\n      return !!e;\n    }) || null;\n    // We're not using `Object.setPrototypeOf` here as it isn't fully\n    // supported on Android (see issue #3236).\n    _this.__proto__ = ApolloError.prototype;\n    return _this;\n  }\n  return ApolloError;\n}(Error);\nexport { ApolloError };", "map": {"version": 3, "names": ["__extends", "__spread<PERSON><PERSON>y", "isNonNullObject", "PROTOCOL_ERRORS_SYMBOL", "Symbol", "graphQLResultHasProtocolErrors", "result", "extensions", "Array", "isArray", "isApolloError", "err", "hasOwnProperty", "generateErrorMessage", "errors", "graphQLErrors", "clientErrors", "protocolErrors", "networkError", "push", "map", "message", "join", "ApolloError", "_super", "_a", "errorMessage", "extraInfo", "_this", "call", "name", "cause", "find", "e", "__proto__", "prototype", "Error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/errors/index.js"], "sourcesContent": ["import { __extends, __spreadArray } from \"tslib\";\nimport \"../utilities/globals/index.js\";\nimport { isNonNullObject } from \"../utilities/index.js\";\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport var PROTOCOL_ERRORS_SYMBOL = Symbol();\nexport function graphQLResultHasProtocolErrors(result) {\n    if (result.extensions) {\n        return Array.isArray(result.extensions[PROTOCOL_ERRORS_SYMBOL]);\n    }\n    return false;\n}\nexport function isApolloError(err) {\n    return err.hasOwnProperty(\"graphQLErrors\");\n}\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nvar generateErrorMessage = function (err) {\n    var errors = __spreadArray(__spreadArray(__spreadArray([], err.graphQLErrors, true), err.clientErrors, true), err.protocolErrors, true);\n    if (err.networkError)\n        errors.push(err.networkError);\n    return (errors\n        // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n        .map(function (err) {\n        return (isNonNullObject(err) && err.message) || \"Error message not found.\";\n    })\n        .join(\"\\n\"));\n};\nvar ApolloError = /** @class */ (function (_super) {\n    __extends(ApolloError, _super);\n    // Constructs an instance of ApolloError given serialized GraphQL errors,\n    // client errors, protocol errors or network errors.\n    // Note that one of these has to be a valid\n    // value or the constructed error will be meaningless.\n    function ApolloError(_a) {\n        var graphQLErrors = _a.graphQLErrors, protocolErrors = _a.protocolErrors, clientErrors = _a.clientErrors, networkError = _a.networkError, errorMessage = _a.errorMessage, extraInfo = _a.extraInfo;\n        var _this = _super.call(this, errorMessage) || this;\n        _this.name = \"ApolloError\";\n        _this.graphQLErrors = graphQLErrors || [];\n        _this.protocolErrors = protocolErrors || [];\n        _this.clientErrors = clientErrors || [];\n        _this.networkError = networkError || null;\n        _this.message = errorMessage || generateErrorMessage(_this);\n        _this.extraInfo = extraInfo;\n        _this.cause =\n            __spreadArray(__spreadArray(__spreadArray([\n                networkError\n            ], (graphQLErrors || []), true), (protocolErrors || []), true), (clientErrors || []), true).find(function (e) { return !!e; }) || null;\n        // We're not using `Object.setPrototypeOf` here as it isn't fully\n        // supported on Android (see issue #3236).\n        _this.__proto__ = ApolloError.prototype;\n        return _this;\n    }\n    return ApolloError;\n}(Error));\nexport { ApolloError };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,aAAa,QAAQ,OAAO;AAChD,OAAO,+BAA+B;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAGC,MAAM,CAAC,CAAC;AAC5C,OAAO,SAASC,8BAA8BA,CAACC,MAAM,EAAE;EACnD,IAAIA,MAAM,CAACC,UAAU,EAAE;IACnB,OAAOC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACC,UAAU,CAACJ,sBAAsB,CAAC,CAAC;EACnE;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASO,aAAaA,CAACC,GAAG,EAAE;EAC/B,OAAOA,GAAG,CAACC,cAAc,CAAC,eAAe,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAAAA,CAAUF,GAAG,EAAE;EACtC,IAAIG,MAAM,GAAGb,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAE,EAAEU,GAAG,CAACI,aAAa,EAAE,IAAI,CAAC,EAAEJ,GAAG,CAACK,YAAY,EAAE,IAAI,CAAC,EAAEL,GAAG,CAACM,cAAc,EAAE,IAAI,CAAC;EACvI,IAAIN,GAAG,CAACO,YAAY,EAChBJ,MAAM,CAACK,IAAI,CAACR,GAAG,CAACO,YAAY,CAAC;EACjC,OAAQJ;EACJ;EAAA,CACCM,GAAG,CAAC,UAAUT,GAAG,EAAE;IACpB,OAAQT,eAAe,CAACS,GAAG,CAAC,IAAIA,GAAG,CAACU,OAAO,IAAK,0BAA0B;EAC9E,CAAC,CAAC,CACGC,IAAI,CAAC,IAAI,CAAC;AACnB,CAAC;AACD,IAAIC,WAAW,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/CxB,SAAS,CAACuB,WAAW,EAAEC,MAAM,CAAC;EAC9B;EACA;EACA;EACA;EACA,SAASD,WAAWA,CAACE,EAAE,EAAE;IACrB,IAAIV,aAAa,GAAGU,EAAE,CAACV,aAAa;MAAEE,cAAc,GAAGQ,EAAE,CAACR,cAAc;MAAED,YAAY,GAAGS,EAAE,CAACT,YAAY;MAAEE,YAAY,GAAGO,EAAE,CAACP,YAAY;MAAEQ,YAAY,GAAGD,EAAE,CAACC,YAAY;MAAEC,SAAS,GAAGF,EAAE,CAACE,SAAS;IAClM,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEH,YAAY,CAAC,IAAI,IAAI;IACnDE,KAAK,CAACE,IAAI,GAAG,aAAa;IAC1BF,KAAK,CAACb,aAAa,GAAGA,aAAa,IAAI,EAAE;IACzCa,KAAK,CAACX,cAAc,GAAGA,cAAc,IAAI,EAAE;IAC3CW,KAAK,CAACZ,YAAY,GAAGA,YAAY,IAAI,EAAE;IACvCY,KAAK,CAACV,YAAY,GAAGA,YAAY,IAAI,IAAI;IACzCU,KAAK,CAACP,OAAO,GAAGK,YAAY,IAAIb,oBAAoB,CAACe,KAAK,CAAC;IAC3DA,KAAK,CAACD,SAAS,GAAGA,SAAS;IAC3BC,KAAK,CAACG,KAAK,GACP9B,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CACtCiB,YAAY,CACf,EAAGH,aAAa,IAAI,EAAE,EAAG,IAAI,CAAC,EAAGE,cAAc,IAAI,EAAE,EAAG,IAAI,CAAC,EAAGD,YAAY,IAAI,EAAE,EAAG,IAAI,CAAC,CAACgB,IAAI,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAO,CAAC,CAACA,CAAC;IAAE,CAAC,CAAC,IAAI,IAAI;IAC1I;IACA;IACAL,KAAK,CAACM,SAAS,GAAGX,WAAW,CAACY,SAAS;IACvC,OAAOP,KAAK;EAChB;EACA,OAAOL,WAAW;AACtB,CAAC,CAACa,KAAK,CAAE;AACT,SAASb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}