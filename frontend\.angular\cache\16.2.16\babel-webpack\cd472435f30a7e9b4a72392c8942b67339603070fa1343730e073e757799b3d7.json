{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n  if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n    timestampProvider = selectorOrScheduler;\n  }\n  const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n  return source => multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n}", "map": {"version": 3, "names": ["ReplaySubject", "multicast", "isFunction", "publishReplay", "bufferSize", "windowTime", "selectorOrScheduler", "timestampProvider", "selector", "undefined", "source"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/rxjs/dist/esm/internal/operators/publishReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n    if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n        timestampProvider = selectorOrScheduler;\n    }\n    const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n    return (source) => multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAE;EAC1F,IAAID,mBAAmB,IAAI,CAACJ,UAAU,CAACI,mBAAmB,CAAC,EAAE;IACzDC,iBAAiB,GAAGD,mBAAmB;EAC3C;EACA,MAAME,QAAQ,GAAGN,UAAU,CAACI,mBAAmB,CAAC,GAAGA,mBAAmB,GAAGG,SAAS;EAClF,OAAQC,MAAM,IAAKT,SAAS,CAAC,IAAID,aAAa,CAACI,UAAU,EAAEC,UAAU,EAAEE,iBAAiB,CAAC,EAAEC,QAAQ,CAAC,CAACE,MAAM,CAAC;AAChH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}