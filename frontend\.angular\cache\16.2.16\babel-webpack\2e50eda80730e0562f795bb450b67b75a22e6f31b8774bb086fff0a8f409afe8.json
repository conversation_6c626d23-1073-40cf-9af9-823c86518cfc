{"ast": null, "code": "var toString = Object.prototype.toString;\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep(value) {\n  return cloneDeepHelper(value);\n}\nfunction cloneDeepHelper(val, seen) {\n  switch (toString.call(val)) {\n    case \"[object Array]\":\n      {\n        seen = seen || new Map();\n        if (seen.has(val)) return seen.get(val);\n        var copy_1 = val.slice(0);\n        seen.set(val, copy_1);\n        copy_1.forEach(function (child, i) {\n          copy_1[i] = cloneDeepHelper(child, seen);\n        });\n        return copy_1;\n      }\n    case \"[object Object]\":\n      {\n        seen = seen || new Map();\n        if (seen.has(val)) return seen.get(val);\n        // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n        // possible in all JS environments, so we will assume they exist/work.\n        var copy_2 = Object.create(Object.getPrototypeOf(val));\n        seen.set(val, copy_2);\n        Object.keys(val).forEach(function (key) {\n          copy_2[key] = cloneDeepHelper(val[key], seen);\n        });\n        return copy_2;\n      }\n    default:\n      return val;\n  }\n}", "map": {"version": 3, "names": ["toString", "Object", "prototype", "cloneDeep", "value", "cloneDeepHelper", "val", "seen", "call", "Map", "has", "get", "copy_1", "slice", "set", "for<PERSON>ach", "child", "i", "copy_2", "create", "getPrototypeOf", "keys", "key"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/cloneDeep.js"], "sourcesContent": ["var toString = Object.prototype.toString;\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep(value) {\n    return cloneDeepHelper(value);\n}\nfunction cloneDeepHelper(val, seen) {\n    switch (toString.call(val)) {\n        case \"[object Array]\": {\n            seen = seen || new Map();\n            if (seen.has(val))\n                return seen.get(val);\n            var copy_1 = val.slice(0);\n            seen.set(val, copy_1);\n            copy_1.forEach(function (child, i) {\n                copy_1[i] = cloneDeepHelper(child, seen);\n            });\n            return copy_1;\n        }\n        case \"[object Object]\": {\n            seen = seen || new Map();\n            if (seen.has(val))\n                return seen.get(val);\n            // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n            // possible in all JS environments, so we will assume they exist/work.\n            var copy_2 = Object.create(Object.getPrototypeOf(val));\n            seen.set(val, copy_2);\n            Object.keys(val).forEach(function (key) {\n                copy_2[key] = cloneDeepHelper(val[key], seen);\n            });\n            return copy_2;\n        }\n        default:\n            return val;\n    }\n}\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,SAAS,CAACF,QAAQ;AACxC;AACA;AACA;AACA,OAAO,SAASG,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAOC,eAAe,CAACD,KAAK,CAAC;AACjC;AACA,SAASC,eAAeA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAChC,QAAQP,QAAQ,CAACQ,IAAI,CAACF,GAAG,CAAC;IACtB,KAAK,gBAAgB;MAAE;QACnBC,IAAI,GAAGA,IAAI,IAAI,IAAIE,GAAG,CAAC,CAAC;QACxB,IAAIF,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC,EACb,OAAOC,IAAI,CAACI,GAAG,CAACL,GAAG,CAAC;QACxB,IAAIM,MAAM,GAAGN,GAAG,CAACO,KAAK,CAAC,CAAC,CAAC;QACzBN,IAAI,CAACO,GAAG,CAACR,GAAG,EAAEM,MAAM,CAAC;QACrBA,MAAM,CAACG,OAAO,CAAC,UAAUC,KAAK,EAAEC,CAAC,EAAE;UAC/BL,MAAM,CAACK,CAAC,CAAC,GAAGZ,eAAe,CAACW,KAAK,EAAET,IAAI,CAAC;QAC5C,CAAC,CAAC;QACF,OAAOK,MAAM;MACjB;IACA,KAAK,iBAAiB;MAAE;QACpBL,IAAI,GAAGA,IAAI,IAAI,IAAIE,GAAG,CAAC,CAAC;QACxB,IAAIF,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC,EACb,OAAOC,IAAI,CAACI,GAAG,CAACL,GAAG,CAAC;QACxB;QACA;QACA,IAAIY,MAAM,GAAGjB,MAAM,CAACkB,MAAM,CAAClB,MAAM,CAACmB,cAAc,CAACd,GAAG,CAAC,CAAC;QACtDC,IAAI,CAACO,GAAG,CAACR,GAAG,EAAEY,MAAM,CAAC;QACrBjB,MAAM,CAACoB,IAAI,CAACf,GAAG,CAAC,CAACS,OAAO,CAAC,UAAUO,GAAG,EAAE;UACpCJ,MAAM,CAACI,GAAG,CAAC,GAAGjB,eAAe,CAACC,GAAG,CAACgB,GAAG,CAAC,EAAEf,IAAI,CAAC;QACjD,CAAC,CAAC;QACF,OAAOW,MAAM;MACjB;IACA;MACI,OAAOZ,GAAG;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}