{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction PlanningEditComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Titre is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"At least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, PlanningEditComponent_div_12_span_1_Template, 2, 0, \"span\", 21);\n    i0.ɵɵtemplate(2, PlanningEditComponent_div_12_span_2_Template, 2, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningEditComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r6._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r6.username);\n  }\n}\nfunction PlanningEditComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Please select at least one participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent__svg_svg_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 23);\n    i0.ɵɵelement(1, \"circle\", 24)(2, \"path\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PlanningEditComponent {\n  constructor(fb, planningService, userService, route, router) {\n    this.fb = fb;\n    this.planningService = planningService;\n    this.userService = userService;\n    this.route = route;\n    this.router = router;\n    this.users$ = this.userService.getAllUsers();\n    this.error = '';\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    this.planningId = this.route.snapshot.paramMap.get('id');\n    this.initForm();\n    this.loadPlanning();\n  }\n  initForm() {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      lieu: [''],\n      participants: [[], Validators.required] // FormArray for multiple participants\n    });\n  }\n\n  loadPlanning() {\n    this.planningService.getPlanningById(this.planningId).subscribe({\n      next: response => {\n        const planning = response.planning;\n        this.planningForm.patchValue({\n          titre: planning.titre,\n          description: planning.description,\n          dateDebut: planning.dateDebut,\n          dateFin: planning.dateFin,\n          lieu: planning.lieu\n        });\n        const participantsArray = this.planningForm.get('participants');\n        participantsArray.clear();\n        planning.participants.forEach(p => {\n          participantsArray.push(this.fb.control(p._id));\n        });\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur lors du chargement du planning';\n      }\n    });\n  }\n  onSubmit() {\n    if (this.planningForm.invalid) return;\n    this.isLoading = true;\n    const formValue = this.planningForm.value;\n    const updatedPlanning = {\n      ...formValue,\n      participants: formValue.participants\n    };\n    this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({\n      next: () => {\n        this.isLoading = false;\n        this.router.navigate(['/plannings']);\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Erreur lors de la mise à jour du planning', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function PlanningEditComponent_Factory(t) {\n      return new (t || PlanningEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningEditComponent,\n      selectors: [[\"app-planning-edit\"]],\n      decls: 40,\n      vars: 11,\n      consts: [[1, \"bg-gray-50\", \"p-5\", \"sm:p-6\", \"rounded-xl\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"grid\", \"grid-cols-1\", \"gap-y-4\", \"gap-x-4\", \"sm:grid-cols-6\"], [1, \"sm:col-span-3\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"class\", \"text-sm text-red-600 mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"sm:col-span-6\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"ml-3\", \"inline-flex\", \"justify-center\", \"py-2\", \"px-4\", \"border\", \"border-transparent\", \"shadow-sm\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-purple-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\"], [\"class\", \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"text-sm\", \"text-red-600\", \"mt-1\"], [4, \"ngIf\"], [3, \"value\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n      template: function PlanningEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Planning Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Fill in the details for the event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function PlanningEditComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"Titre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 8);\n          i0.ɵɵtemplate(12, PlanningEditComponent_div_12_Template, 3, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"label\", 7);\n          i0.ɵɵtext(15, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 7);\n          i0.ɵɵtext(19, \"Date de d\\u00E9but\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 6)(22, \"label\", 7);\n          i0.ɵɵtext(23, \"Date de fin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 7);\n          i0.ɵɵtext(27, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"select\", 14);\n          i0.ɵɵtemplate(29, PlanningEditComponent_option_29_Template, 2, 2, \"option\", 15);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, PlanningEditComponent_div_31_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"label\", 7);\n          i0.ɵɵtext(34, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"textarea\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n          i0.ɵɵtemplate(38, PlanningEditComponent__svg_svg_38_Template, 3, 0, \"svg\", 19);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"border-red-300\", ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(30, 9, ctx.users$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.planningForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : \"Save Planning\", \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1lZGl0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWVkaXQvcGxhbm5pbmctZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "PlanningEditComponent_div_12_span_1_Template", "PlanningEditComponent_div_12_span_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "planningForm", "get", "errors", "tmp_1_0", "user_r6", "_id", "ɵɵtextInterpolate", "username", "ɵɵnamespaceSVG", "ɵɵelement", "PlanningEditComponent", "constructor", "fb", "planningService", "userService", "route", "router", "users$", "getAllUsers", "error", "isLoading", "ngOnInit", "planningId", "snapshot", "paramMap", "initForm", "loadPlanning", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "dateDebut", "dateFin", "lieu", "participants", "getPlanningById", "subscribe", "next", "response", "planning", "patchValue", "participantsArray", "clear", "for<PERSON>ach", "p", "push", "control", "err", "message", "onSubmit", "invalid", "formValue", "value", "updatedPlanning", "updatePlanning", "navigate", "console", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "PlanningService", "i3", "DataService", "i4", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "PlanningEditComponent_Template", "rf", "ctx", "ɵɵlistener", "PlanningEditComponent_Template_form_ngSubmit_6_listener", "PlanningEditComponent_div_12_Template", "PlanningEditComponent_option_29_Template", "PlanningEditComponent_div_31_Template", "PlanningEditComponent__svg_svg_38_Template", "ɵɵclassProp", "touched", "tmp_2_0", "ɵɵpipeBind1", "tmp_4_0", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-edit\\planning-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-edit\\planning-edit.component.html"], "sourcesContent": ["import {Component, OnInit} from '@angular/core';\r\nimport {<PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators} from \"@angular/forms\";\r\nimport {ActivatedRoute, Router} from \"@angular/router\";\r\nimport {PlanningService} from \"@app/services/planning.service\";\r\nimport {Planning} from \"@app/models/planning.model\";\r\nimport {DataService} from \"@app/services/data.service\";\r\nimport {User} from \"@app/models/user.model\";\r\n\r\n@Component({\r\n  selector: 'app-planning-edit',\r\n  templateUrl: './planning-edit.component.html',\r\n  styleUrls: ['./planning-edit.component.css']\r\n})\r\nexport class PlanningEditComponent implements OnInit {\r\n\r\n  planningForm!: FormGroup;\r\n  users$ = this.userService.getAllUsers();\r\n  planningId!: string;\r\n  error: string = '';\r\n  isLoading: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private planningService: PlanningService,\r\n    private userService: DataService,\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.planningId = this.route.snapshot.paramMap.get('id')!;\r\n    this.initForm();\r\n    this.loadPlanning();\r\n  }\r\n\r\n  initForm(): void {\r\n    this.planningForm = this.fb.group({\r\n      titre: ['', [Validators.required, Validators.minLength(3)]],\r\n      description: [''],\r\n      dateDebut: ['', Validators.required],\r\n      dateFin: ['', Validators.required],\r\n      lieu: [''],\r\n      participants: [[], Validators.required]  // FormArray for multiple participants\r\n    });\r\n  }\r\n\r\n  loadPlanning(): void {\r\n    this.planningService.getPlanningById(this.planningId).subscribe({\r\n      next: (response: any) => {\r\n        const planning = response.planning;\r\n\r\n        this.planningForm.patchValue({\r\n          titre: planning.titre,\r\n          description: planning.description,\r\n          dateDebut: planning.dateDebut,\r\n          dateFin: planning.dateFin,\r\n          lieu: planning.lieu\r\n        });\r\n\r\n        const participantsArray = this.planningForm.get('participants') as FormArray;\r\n        participantsArray.clear();\r\n\r\n        planning.participants.forEach((p: any) => {\r\n          participantsArray.push(this.fb.control(p._id));\r\n        });\r\n      },\r\n      error: err => {\r\n        this.error = err.error?.message || 'Erreur lors du chargement du planning';\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.planningForm.invalid) return;\r\n\r\n    this.isLoading = true;\r\n    const formValue = this.planningForm.value;\r\n\r\n    const updatedPlanning: Planning = {\r\n      ...formValue,\r\n      participants: formValue.participants\r\n    };\r\n\r\n    this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({\r\n      next: () => {\r\n        this.isLoading = false;\r\n        this.router.navigate(['/plannings']);\r\n      },\r\n      error: err => {\r\n        this.isLoading = false;\r\n        console.error('Erreur lors de la mise à jour du planning', err);\r\n      }\r\n    });\r\n  }\r\n}", "<section class=\"bg-gray-50 p-5 sm:p-6 rounded-xl\">\r\n  <div class=\"mb-4\">\r\n    <h2 class=\"text-lg font-medium text-gray-900\">Planning Form</h2>\r\n    <p class=\"text-sm text-gray-500 mt-1\">Fill in the details for the event</p>\r\n  </div>\r\n\r\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"onSubmit()\" novalidate>\r\n    <div class=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6\">\r\n      <!-- Titre -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Titre</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"titre\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n          [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\r\n        />\r\n        <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-sm text-red-600 mt-1\">\r\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Titre is required</span>\r\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">At least 3 characters</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Lieu -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Lieu</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"lieu\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Date début -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Date de début</label>\r\n        <input\r\n          type=\"date\"\r\n          formControlName=\"dateDebut\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Date fin -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Date de fin</label>\r\n        <input\r\n          type=\"date\"\r\n          formControlName=\"dateFin\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Participants -->\r\n      <div class=\"sm:col-span-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\r\n        <select\r\n          formControlName=\"participants\"\r\n          multiple\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        >\r\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\">{{ user.username }}</option>\r\n        </select>\r\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-sm text-red-600 mt-1\">\r\n          Please select at least one participant\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Description -->\r\n      <div class=\"sm:col-span-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Description</label>\r\n        <textarea\r\n          formControlName=\"description\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n          rows=\"3\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mt-6 flex justify-end\">\r\n      <button\r\n        type=\"submit\"\r\n        [disabled]=\"planningForm.invalid || isLoading\"\r\n        class=\"ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n      >\r\n        <svg\r\n          *ngIf=\"isLoading\"\r\n          class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n        >\r\n          <circle\r\n            class=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            stroke-width=\"4\"\r\n          ></circle>\r\n          <path\r\n            class=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          ></path>\r\n        </svg>\r\n        {{ isLoading ? 'Saving...' : 'Save Planning' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</section>\r\n"], "mappings": "AACA,SAA2CA,UAAU,QAAO,gBAAgB;;;;;;;;;ICiBlEC,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF7FH,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,mBAAsF;IACtFL,EAAA,CAAAI,UAAA,IAAAE,4CAAA,mBAA2F;IAC7FN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDb,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IA0C7Db,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAQ,UAAA,UAAAO,OAAA,CAAAC,GAAA,CAAkB;IAAChB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAiB,iBAAA,CAAAF,OAAA,CAAAG,QAAA,CAAmB;;;;;IAEpFlB,EAAA,CAAAC,cAAA,cAAsI;IACpID,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAmB,cAAA,EAMC;IANDnB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAoB,SAAA,iBAOU;IAMZpB,EAAA,CAAAG,YAAA,EAAM;;;AD5Fd,OAAM,MAAOkB,qBAAqB;EAQhCC,YACUC,EAAe,EACfC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc;IAJd,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,MAAM,GAAG,IAAI,CAACH,WAAW,CAACI,WAAW,EAAE;IAEvC,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAY,KAAK;EAQvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACvB,GAAG,CAAC,IAAI,CAAE;IACzD,IAAI,CAACwB,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,QAAQA,CAAA;IACN,IAAI,CAACzB,YAAY,GAAG,IAAI,CAACY,EAAE,CAACe,KAAK,CAAC;MAChCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACyC,QAAQ,EAAEzC,UAAU,CAAC0C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE5C,UAAU,CAACyC,QAAQ,CAAC;MACpCI,OAAO,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAACyC,QAAQ,CAAC;MAClCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC,EAAE,EAAE/C,UAAU,CAACyC,QAAQ,CAAC,CAAE;KAC1C,CAAC;EACJ;;EAEAH,YAAYA,CAAA;IACV,IAAI,CAACb,eAAe,CAACuB,eAAe,CAAC,IAAI,CAACd,UAAU,CAAC,CAACe,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAa,IAAI;QACtB,MAAMC,QAAQ,GAAGD,QAAQ,CAACC,QAAQ;QAElC,IAAI,CAACxC,YAAY,CAACyC,UAAU,CAAC;UAC3Bb,KAAK,EAAEY,QAAQ,CAACZ,KAAK;UACrBG,WAAW,EAAES,QAAQ,CAACT,WAAW;UACjCC,SAAS,EAAEQ,QAAQ,CAACR,SAAS;UAC7BC,OAAO,EAAEO,QAAQ,CAACP,OAAO;UACzBC,IAAI,EAAEM,QAAQ,CAACN;SAChB,CAAC;QAEF,MAAMQ,iBAAiB,GAAG,IAAI,CAAC1C,YAAY,CAACC,GAAG,CAAC,cAAc,CAAc;QAC5EyC,iBAAiB,CAACC,KAAK,EAAE;QAEzBH,QAAQ,CAACL,YAAY,CAACS,OAAO,CAAEC,CAAM,IAAI;UACvCH,iBAAiB,CAACI,IAAI,CAAC,IAAI,CAAClC,EAAE,CAACmC,OAAO,CAACF,CAAC,CAACxC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ,CAAC;MACDc,KAAK,EAAE6B,GAAG,IAAG;QACX,IAAI,CAAC7B,KAAK,GAAG6B,GAAG,CAAC7B,KAAK,EAAE8B,OAAO,IAAI,uCAAuC;MAC5E;KACD,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClD,YAAY,CAACmD,OAAO,EAAE;IAE/B,IAAI,CAAC/B,SAAS,GAAG,IAAI;IACrB,MAAMgC,SAAS,GAAG,IAAI,CAACpD,YAAY,CAACqD,KAAK;IAEzC,MAAMC,eAAe,GAAa;MAChC,GAAGF,SAAS;MACZjB,YAAY,EAAEiB,SAAS,CAACjB;KACzB;IAED,IAAI,CAACtB,eAAe,CAAC0C,cAAc,CAAC,IAAI,CAACjC,UAAU,EAAEgC,eAAe,CAAC,CAACjB,SAAS,CAAC;MAC9EC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAClB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACwC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACtC,CAAC;MACDrC,KAAK,EAAE6B,GAAG,IAAG;QACX,IAAI,CAAC5B,SAAS,GAAG,KAAK;QACtBqC,OAAO,CAACtC,KAAK,CAAC,2CAA2C,EAAE6B,GAAG,CAAC;MACjE;KACD,CAAC;EACJ;;;uBAhFWtC,qBAAqB,EAAArB,EAAA,CAAAqE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAqE,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzE,EAAA,CAAAqE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3E,EAAA,CAAAqE,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7E,EAAA,CAAAqE,iBAAA,CAAAO,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAArBzD,qBAAqB;MAAA0D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCblCrF,EAAA,CAAAC,cAAA,iBAAkD;UAEAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,WAAsC;UAAAD,EAAA,CAAAE,MAAA,wCAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7EH,EAAA,CAAAC,cAAA,cAAoE;UAAnCD,EAAA,CAAAuF,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAAzB,QAAA,EAAU;UAAA,EAAC;UACtD7D,EAAA,CAAAC,cAAA,aAA6D;UAGFD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAoB,SAAA,gBAKE;UACFpB,EAAA,CAAAI,UAAA,KAAAqF,qCAAA,iBAGM;UACRzF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAI,UAAA,KAAAsF,wCAAA,qBAA2F;;UAC7F1F,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAI,UAAA,KAAAuF,qCAAA,iBAEM;UACR3F,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAoB,SAAA,oBAIY;UACdpB,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAmC;UAM/BD,EAAA,CAAAI,UAAA,KAAAwF,0CAAA,kBAoBM;UACN5F,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;UArGPH,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,cAAA8E,GAAA,CAAA3E,YAAA,CAA0B;UASxBX,EAAA,CAAAO,SAAA,GAAiG;UAAjGP,EAAA,CAAA6F,WAAA,qBAAA/E,OAAA,GAAAwE,GAAA,CAAA3E,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAgD,OAAA,OAAAhD,OAAA,GAAAwE,GAAA,CAAA3E,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAgF,OAAA,EAAiG;UAE7F9F,EAAA,CAAAO,SAAA,GAA8E;UAA9EP,EAAA,CAAAQ,UAAA,WAAAuF,OAAA,GAAAT,GAAA,CAAA3E,YAAA,CAAAC,GAAA,4BAAAmF,OAAA,CAAAjC,OAAA,OAAAiC,OAAA,GAAAT,GAAA,CAAA3E,YAAA,CAAAC,GAAA,4BAAAmF,OAAA,CAAAD,OAAA,EAA8E;UA4CzD9F,EAAA,CAAAO,SAAA,IAAiB;UAAjBP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAgG,WAAA,QAAAV,GAAA,CAAA1D,MAAA,EAAiB;UAEtC5B,EAAA,CAAAO,SAAA,GAA4F;UAA5FP,EAAA,CAAAQ,UAAA,WAAAyF,OAAA,GAAAX,GAAA,CAAA3E,YAAA,CAAAC,GAAA,mCAAAqF,OAAA,CAAAnC,OAAA,OAAAmC,OAAA,GAAAX,GAAA,CAAA3E,YAAA,CAAAC,GAAA,mCAAAqF,OAAA,CAAAH,OAAA,EAA4F;UAmBlG9F,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,UAAA,aAAA8E,GAAA,CAAA3E,YAAA,CAAAmD,OAAA,IAAAwB,GAAA,CAAAvD,SAAA,CAA8C;UAI3C/B,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAA8E,GAAA,CAAAvD,SAAA,CAAe;UAoBlB/B,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAkG,kBAAA,MAAAZ,GAAA,CAAAvD,SAAA,sCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}