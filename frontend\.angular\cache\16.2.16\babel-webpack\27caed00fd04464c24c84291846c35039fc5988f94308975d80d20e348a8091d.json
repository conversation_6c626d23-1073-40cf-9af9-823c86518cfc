{"ast": null, "code": "import { ApolloLink } from \"./ApolloLink.js\";\nexport var empty = ApolloLink.empty;", "map": {"version": 3, "names": ["ApolloLink", "empty"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/empty.js"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\nexport var empty = ApolloLink.empty;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,KAAK,GAAGD,UAAU,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}