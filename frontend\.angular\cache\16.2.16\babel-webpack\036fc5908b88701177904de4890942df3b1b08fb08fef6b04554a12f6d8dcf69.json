{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMP<PERSON> } from 'rxjs';\nimport { map, catchError, tap, filter, switchMap } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.logger.debug('MessageService', `Chargement de ${notifications.length} notifications depuis le localStorage`);\n        // Vider le cache avant de charger les notifications pour éviter les doublons\n        this.notificationCache.clear();\n        // Mettre à jour le cache avec les notifications sauvegardées\n        notifications.forEach(notification => {\n          // Vérifier que la notification a un ID valide\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        // Mettre à jour le BehaviorSubject avec les notifications chargées\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n        this.logger.debug('MessageService', `${this.notificationCache.size} notifications chargées dans le cache`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors du chargement des notifications depuis le localStorage:', error);\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n    });\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.logger.debug('Incoming call received', call);\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      // Gérer la fin de la lecture\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n      this.logger.debug('MessageService', `Son chargé: ${name} (${path})`);\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors du chargement du son ${name}:`, error);\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      this.logger.debug('MessageService', `Son ${name} non joué (muet)`);\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n      // Configurer la lecture en boucle\n      sound.loop = loop;\n      // Jouer le son s'il n'est pas déjà en cours de lecture\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n        });\n        this.isPlaying[name] = true;\n        this.logger.debug('MessageService', `Lecture du son: ${name}, boucle: ${loop}`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors de la lecture du son ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\n        return;\n      }\n      // Arrêter le son s'il est en cours de lecture\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n        this.logger.debug('MessageService', `Son arrêté: ${name}`);\n      }\n    } catch (error) {\n      this.logger.error('MessageService', `Erreur lors de l'arrêt du son ${name}:`, error);\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n    this.logger.debug('MessageService', 'Tous les sons ont été arrêtés');\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    this.logger.info('MessageService', `Son ${muted ? 'désactivé' : 'activé'}`);\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Utiliser l'API Web Audio pour générer un son de notification simple\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Créer un oscillateur pour générer un son\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      // Configurer l'oscillateur\n      oscillator.type = 'sine';\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\n      // Configurer le volume\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.5, audioContext.currentTime + 0.01);\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\n      // Connecter les nœuds\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      // Démarrer et arrêter l'oscillateur\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + 0.3);\n      console.log('MessageService: Son de notification généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 1.0; // Volume maximum\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Envoie un message vocal à un utilisateur\n   * @param receiverId ID de l'utilisateur destinataire\n   * @param audioBlob Blob audio à envoyer\n   * @param conversationId ID de la conversation (optionnel)\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\n   * @returns Observable avec le message envoyé\n   */\n  sendVoiceMessage(receiverId, audioBlob, conversationId, duration) {\n    this.logger.debug(`[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`);\n    // Vérifier que le blob audio est valide\n    if (!audioBlob || audioBlob.size === 0) {\n      this.logger.error('[MessageService] Invalid audio blob');\n      return throwError(() => new Error('Invalid audio blob'));\n    }\n    // Créer un fichier à partir du blob audio avec un nom unique\n    const timestamp = Date.now();\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\n      type: 'audio/webm',\n      lastModified: timestamp\n    });\n    // Vérifier que le fichier a été créé correctement\n    if (!audioFile || audioFile.size === 0) {\n      this.logger.error('[MessageService] Failed to create audio file');\n      return throwError(() => new Error('Failed to create audio file'));\n    }\n    this.logger.debug(`[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`);\n    // Créer des métadonnées pour le message vocal\n    const metadata = {\n      duration: duration || 0,\n      isVoiceMessage: true,\n      timestamp: timestamp\n    };\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\n    return this.sendMessage(receiverId, ' ',\n    // Espace comme contenu minimal pour passer la validation\n    audioFile, MessageType.VOICE_MESSAGE, conversationId, undefined, metadata);\n  }\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      return messages.map(msg => this.normalizeMessage(msg));\n    }), catchError(error => {\n      this.logger.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  sendMessage(receiverId, content, file, messageType = MessageType.TEXT, conversationId, replyTo, metadata) {\n    this.logger.info(`[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`);\n    this.logger.debug(`[MessageService] Message content: \"${content?.substring(0, 50)}${content?.length > 50 ? '...' : ''}\"`);\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(`[MessageService] Authentication check before sending message: token=${!!token}`);\n    // Utiliser le type de message fourni ou le déterminer automatiquement\n    let finalMessageType = messageType;\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\n    if (file) {\n      // Si le type est déjà VOICE_MESSAGE, le conserver\n      if (messageType === MessageType.VOICE_MESSAGE) {\n        finalMessageType = MessageType.VOICE_MESSAGE;\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\n      }\n      // Sinon, déterminer le type en fonction du type de fichier\n      else if (messageType === MessageType.TEXT) {\n        if (file.type.startsWith('image/')) {\n          finalMessageType = MessageType.IMAGE;\n        } else if (file.type.startsWith('video/')) {\n          finalMessageType = MessageType.VIDEO;\n        } else if (file.type.startsWith('audio/')) {\n          // Vérifier si c'est un message vocal basé sur les métadonnées\n          if (metadata && metadata.isVoiceMessage) {\n            finalMessageType = MessageType.VOICE_MESSAGE;\n          } else {\n            finalMessageType = MessageType.AUDIO;\n          }\n        } else {\n          finalMessageType = MessageType.FILE;\n        }\n      }\n    }\n    this.logger.debug(`[MessageService] Message type determined: ${finalMessageType}`);\n    // Ajouter le type de message aux variables\n    // Utiliser directement la valeur de l'énumération sans conversion\n    const variables = {\n      receiverId,\n      content,\n      type: finalMessageType // Ajouter explicitement le type de message\n    };\n    // Forcer le type à être une valeur d'énumération GraphQL\n    // Cela empêche Apollo de convertir la valeur en minuscules\n    if (variables.type) {\n      Object.defineProperty(variables, 'type', {\n        value: finalMessageType,\n        enumerable: true,\n        writable: false\n      });\n    }\n    // Ajouter les métadonnées si elles sont fournies\n    if (metadata) {\n      variables.metadata = metadata;\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\n    }\n    if (file) {\n      variables.file = file;\n      this.logger.debug(`[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`);\n    }\n    if (conversationId) {\n      variables.conversationId = conversationId;\n      this.logger.debug(`[MessageService] Using existing conversation: ${conversationId}`);\n    }\n    if (replyTo) {\n      variables.replyTo = replyTo;\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\n    }\n    const context = file ? {\n      useMultipart: true,\n      file\n    } : undefined;\n    this.logger.debug(`[MessageService] Sending GraphQL mutation with variables:`, variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Message send response:`, result);\n      if (!result.data?.sendMessage) {\n        this.logger.error(`[MessageService] Failed to send message: No data returned`);\n        throw new Error('Failed to send message');\n      }\n      try {\n        this.logger.debug(`[MessageService] Normalizing sent message`, result.data.sendMessage);\n        const normalizedMessage = this.normalizeMessage(result.data.sendMessage);\n        this.logger.info(`[MessageService] Message sent successfully: ${normalizedMessage.id}`);\n        return normalizedMessage;\n      } catch (normalizationError) {\n        this.logger.error(`[MessageService] Error normalizing message:`, normalizationError);\n        // Retourner un message minimal mais valide plutôt que de lancer une erreur\n        const minimalMessage = {\n          id: result.data.sendMessage.id || 'temp-' + Date.now(),\n          content: result.data.sendMessage.content || '',\n          type: result.data.sendMessage.type || MessageType.TEXT,\n          timestamp: new Date(),\n          isRead: false,\n          sender: {\n            id: this.getCurrentUserId(),\n            username: 'You'\n          }\n        };\n        this.logger.info(`[MessageService] Returning minimal message: ${minimalMessage.id}`);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error sending message:`, error);\n      return throwError(() => new Error('Failed to send message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      console.log(`Total notifications in cache after update: ${cachedNotifications.length}`);\n      // Mettre à jour le BehaviorSubject avec toutes les notifications\n      this.notifications.next(cachedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    this.notificationCache.delete(notificationId);\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression de la notification:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: true,\n        message: 'Notification supprimée localement (erreur serveur)'\n      });\n    }));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    this.notificationCache.clear();\n    this.notifications.next([]);\n    this.notificationCount.next(0);\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression de toutes les notifications:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: true,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      });\n    }));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    let count = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        this.notificationCache.delete(id);\n        count++;\n      }\n    });\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Erreur lors de la suppression multiple de notifications:', error);\n      // En cas d'erreur, on garde la suppression locale\n      return of({\n        success: count > 0,\n        count,\n        message: `${count} notifications supprimées localement (erreur serveur)`\n      });\n    }));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels\n  // --------------------------------------------------------------------------\n  /**\n   * Initie un appel avec un autre utilisateur\n   * @param recipientId ID de l'utilisateur à appeler\n   * @param callType Type d'appel (audio, vidéo)\n   * @param conversationId ID de la conversation (optionnel)\n   * @param options Options d'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  initiateCall(recipientId, callType, conversationId, options) {\n    return this.setupMediaDevices(callType).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(this.generateCallId(), 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Créer l'offre SDP\n      return from(this.peerConnection.createOffer()).pipe(switchMap(offer => {\n        return from(this.peerConnection.setLocalDescription(offer)).pipe(map(() => offer));\n      }));\n    }), switchMap(offer => {\n      // Générer un ID d'appel unique\n      const callId = this.generateCallId();\n      // Envoyer l'offre au serveur\n      return this.apollo.mutate({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          offer: JSON.stringify(offer),\n          conversationId,\n          options\n        }\n      }).pipe(map(result => {\n        const call = result.data?.initiateCall;\n        if (!call) {\n          throw new Error('Failed to initiate call');\n        }\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next(call);\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(call.id).subscribe();\n        this.subscriptions.push(signalSub);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error initiating call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to initiate call'));\n    }));\n  }\n  /**\n   * Accepte un appel entrant\n   * @param incomingCall Appel entrant à accepter\n   * @returns Observable avec les informations de l'appel\n   */\n  acceptCall(incomingCall) {\n    this.stop('ringtone');\n    return this.setupMediaDevices(incomingCall.type).pipe(switchMap(stream => {\n      this.localStream = stream;\n      this.localStream$.next(stream);\n      // Créer une connexion peer\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      // Ajouter les pistes audio/vidéo\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n      });\n      // Écouter les candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          this.sendCallSignal(incomingCall.id, 'ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Écouter les pistes distantes\n      this.peerConnection.ontrack = event => {\n        if (!this.remoteStream) {\n          this.remoteStream = new MediaStream();\n          this.remoteStream$.next(this.remoteStream);\n        }\n        event.streams[0].getTracks().forEach(track => {\n          this.remoteStream.addTrack(track);\n        });\n      };\n      // Définir l'offre distante\n      const offer = JSON.parse(incomingCall.offer);\n      return from(this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer))).pipe(switchMap(() => from(this.peerConnection.createAnswer())), switchMap(answer => {\n        return from(this.peerConnection.setLocalDescription(answer)).pipe(map(() => answer));\n      }));\n    }), switchMap(answer => {\n      // Envoyer la réponse au serveur\n      return this.apollo.mutate({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: incomingCall.id,\n          answer: JSON.stringify(answer)\n        }\n      }).pipe(map(result => {\n        const call = result.data?.acceptCall;\n        if (!call) {\n          throw new Error('Failed to accept call');\n        }\n        // Jouer le son de connexion\n        this.play('call-connected');\n        // Mettre à jour l'état de l'appel actif\n        this.activeCall.next({\n          ...call,\n          caller: incomingCall.caller,\n          type: incomingCall.type,\n          conversationId: incomingCall.conversationId\n        });\n        // S'abonner aux signaux d'appel\n        const signalSub = this.subscribeToCallSignals(incomingCall.id).subscribe();\n        this.subscriptions.push(signalSub);\n        // Effacer l'appel entrant\n        this.incomingCall.next(null);\n        return call;\n      }));\n    }), catchError(error => {\n      this.logger.error('Error accepting call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to accept call'));\n    }));\n  }\n  /**\n   * Rejette un appel entrant\n   * @param callId ID de l'appel à rejeter\n   * @param reason Raison du rejet (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  rejectCall(callId, reason) {\n    this.stop('ringtone');\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason\n      }\n    }).pipe(map(result => {\n      const call = result.data?.rejectCall;\n      if (!call) {\n        throw new Error('Failed to reject call');\n      }\n      // Effacer l'appel entrant\n      this.incomingCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error rejecting call', error);\n      return throwError(() => new Error('Failed to reject call'));\n    }));\n  }\n  /**\n   * Termine un appel en cours\n   * @param callId ID de l'appel à terminer\n   * @param feedback Commentaires sur l'appel (optionnel)\n   * @returns Observable avec les informations de l'appel\n   */\n  endCall(callId, feedback) {\n    this.stop('ringtone');\n    this.play('call-end');\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId,\n        feedback\n      }\n    }).pipe(map(result => {\n      const call = result.data?.endCall;\n      if (!call) {\n        throw new Error('Failed to end call');\n      }\n      // Nettoyer les ressources\n      this.cleanupCall();\n      // Mettre à jour l'état de l'appel actif\n      this.activeCall.next(null);\n      return call;\n    }), catchError(error => {\n      this.logger.error('Error ending call', error);\n      this.cleanupCall();\n      return throwError(() => new Error('Failed to end call'));\n    }));\n  }\n  /**\n   * Active ou désactive la caméra ou le micro\n   * @param callId ID de l'appel\n   * @param video État de la caméra (optionnel)\n   * @param audio État du micro (optionnel)\n   * @returns Observable avec le résultat de l'opération\n   */\n  toggleMedia(callId, video, audio) {\n    if (this.localStream) {\n      // Mettre à jour les pistes locales\n      if (video !== undefined) {\n        this.localStream.getVideoTracks().forEach(track => {\n          track.enabled = video;\n        });\n      }\n      if (audio !== undefined) {\n        this.localStream.getAudioTracks().forEach(track => {\n          track.enabled = audio;\n        });\n      }\n    }\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video,\n        audio\n      }\n    }).pipe(map(result => {\n      const success = result.data?.toggleCallMedia;\n      if (!success) {\n        throw new Error('Failed to toggle media');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error toggling media', error);\n      return throwError(() => new Error('Failed to toggle media'));\n    }));\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // Group methods\n  getGroup(groupId) {\n    return this.apollo.watchQuery({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) throw new Error('Group not found');\n      return {\n        ...group,\n        participants: group.participants?.map(p => this.normalizeUser(p)) || [],\n        admins: group.admins?.map(a => this.normalizeUser(a)) || [],\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching group:', error);\n      return throwError(() => new Error('Failed to fetch group'));\n    }));\n  }\n  getUserGroups(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUserGroups?.map(group => ({\n      ...group,\n      participants: group.participants?.map(p => this.normalizeUser(p)) || [],\n      admins: group.admins?.map(a => this.normalizeUser(a)) || [],\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })) || []), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user groups:', error);\n      return throwError(() => new Error('Failed to fetch user groups'));\n    }));\n  }\n  createGroup(name, participantIds, photo, description) {\n    const variables = photo ? {\n      name,\n      participantIds,\n      photo,\n      description\n    } : {\n      name,\n      participantIds,\n      description\n    };\n    const context = photo ? {\n      useMultipart: true,\n      file: photo\n    } : undefined;\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables,\n      context,\n      refetchQueries: [{\n        query: GET_USER_GROUPS_QUERY,\n        variables: {\n          userId: this.getCurrentUserId()\n        }\n      }]\n    }).pipe(map(result => {\n      if (!result.data?.createGroup) throw new Error('Failed to create group');\n      return {\n        ...result.data.createGroup,\n        participants: result.data.createGroup.participants?.map(p => this.normalizeUser(p)) || [],\n        admins: result.data.createGroup.admins?.map(a => this.normalizeUser(a)) || []\n      };\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Failed to create group'));\n    }));\n  }\n  updateGroup(groupId, input) {\n    const context = input.photo ? {\n      useMultipart: true,\n      file: input.photo\n    } : undefined;\n    const {\n      photo,\n      ...inputWithoutPhoto\n    } = input;\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input: inputWithoutPhoto\n      },\n      context,\n      refetchQueries: [{\n        query: GET_GROUP_QUERY,\n        variables: {\n          id: groupId\n        }\n      }, {\n        query: GET_USER_GROUPS_QUERY,\n        variables: {\n          userId: this.getCurrentUserId()\n        }\n      }]\n    }).pipe(map(result => {\n      if (!result.data?.updateGroup) throw new Error('Failed to update group');\n      return {\n        ...result.data.updateGroup,\n        participants: result.data.updateGroup.participants?.map(p => this.normalizeUser(p)) || [],\n        admins: result.data.updateGroup.admins?.map(a => this.normalizeUser(a)) || []\n      };\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Failed to update group'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId) {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n      return of(null);\n    }\n    this.logger.debug(`Démarrage de l'abonnement aux nouveaux messages pour la conversation: ${conversationId}`);\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        this.logger.warn('No message payload received');\n        throw new Error('No message payload received');\n      }\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('Message without ID received:', msg);\n        // Générer un ID temporaire si nécessaire\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // Utiliser normalizeMessage pour une normalisation complète\n        const normalizedMessage = this.normalizeMessage(msg);\n        // Si c'est un message vocal, s'assurer qu'il est correctement traité\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'audio')) {\n          this.logger.debug('MessageService', 'Voice message received in real-time', normalizedMessage);\n          // Mettre à jour la conversation avec le nouveau message\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        }\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('Error normalizing message:', err);\n        // Créer un message minimal mais valide\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    const sub = sub$.subscribe({\n      next: message => {\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        this.logger.error('Error in message subscription:', err);\n      }\n    });\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    // Forcer une mise à jour de la conversation en récupérant les données à jour\n    this.getConversation(conversationId).subscribe({\n      next: conversation => {\n        this.logger.debug('MessageService', `Conversation ${conversationId} refreshed with new message ${message.id}, has ${conversation?.messages?.length || 0} messages`);\n        // Émettre un événement pour informer les composants que la conversation a été mise à jour\n        this.activeConversation.next(conversationId);\n      },\n      error: error => {\n        this.logger.error('MessageService', `Error refreshing conversation ${conversationId}:`, error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      // Créer un Observable vide plutôt que de retourner null\n      return EMPTY;\n    }\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      const normalized = this.normalizeNotification(notification);\n      // Vérifier si cette notification existe déjà dans le cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n        // Utiliser une technique différente pour ignorer cette notification\n        throw new Error('Notification already exists in cache');\n      }\n      // Jouer le son de notification\n      this.playNotificationSound();\n      // Mettre à jour le cache et émettre immédiatement la nouvelle notification\n      this.updateNotificationCache(normalized);\n      this.logger.debug('MessageService', 'New notification received and processed', normalized);\n      return normalized;\n    }),\n    // Utiliser catchError pour gérer les erreurs spécifiques\n    catchError(err => {\n      // Si c'est l'erreur spécifique pour les notifications déjà existantes, on ignore silencieusement\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('New notification subscription error:', err);\n      // Retourner un Observable vide au lieu de null\n      return EMPTY;\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('MessageService', 'Notification subscription next handler', notification);\n      },\n      error: error => {\n        this.logger.error('MessageService', 'Error in notification subscription', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n    }\n  }\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeNotMessage(message) {\n    return {\n      ...message,\n      ...(message.attachments && {\n        attachments: message.attachments.map(att => ({\n          url: att.url,\n          type: att.type,\n          ...(att.name && {\n            name: att.name\n          }),\n          ...(att.size && {\n            size: att.size\n          })\n        }))\n      })\n    };\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  updateCache(notifications) {\n    this.logger.debug('MessageService', `Updating notification cache with ${notifications.length} notifications`);\n    if (notifications.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    console.log(`Starting to update cache with ${notifications.length} notifications`);\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notifications.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notifications.length) {\n      console.warn(`Found ${notifications.length - validNotifications.length} notifications without valid IDs`);\n    }\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          console.error('Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (this.notificationCache.has(normalized.id)) {\n          console.log(`Notification ${normalized.id} already exists in cache, skipping`);\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        console.log(`Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        console.error(`Error processing notification ${index + 1}:`, error);\n        console.error('Problematic notification:', notif);\n      }\n    });\n    console.log(`Notification cache updated, now contains ${this.notificationCache.size} notifications`);\n    // Sauvegarder les notifications dans le localStorage après la mise à jour du cache\n    this.saveNotificationsToLocalStorage();\n  }\n  updateUnreadCount() {\n    const count = Array.from(this.notificationCache.values()).filter(n => !n.isRead).length;\n    this.notificationCount.next(count);\n  }\n  updateNotificationCache(notification) {\n    // Vérifier si la notification existe déjà dans le cache (pour éviter les doublons)\n    if (!this.notificationCache.has(notification.id)) {\n      this.notificationCache.set(notification.id, notification);\n      this.notifications.next(Array.from(this.notificationCache.values()));\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage après chaque mise à jour\n      this.saveNotificationsToLocalStorage();\n    } else {\n      this.logger.debug('MessageService', `Notification ${notification.id} already exists in cache, skipping`);\n    }\n  }\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead\n        });\n      }\n    });\n    this.notifications.next(Array.from(this.notificationCache.values()));\n    this.updateUnreadCount();\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "switchMap", "from", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "debug", "length", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "values", "updateUnreadCount", "size", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "warn", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "info", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "oscillator", "createOscillator", "gainNode", "createGain", "type", "frequency", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "volume", "err", "audioError", "sendVoiceMessage", "receiverId", "audioBlob", "conversationId", "duration", "Error", "timestamp", "Date", "now", "audioFile", "File", "lastModified", "metadata", "isVoiceMessage", "sendMessage", "VOICE_MESSAGE", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "getMessages", "senderId", "page", "variables", "messages", "msg", "normalizeMessage", "editMessage", "messageId", "newContent", "mutate", "mutation", "deleteMessage", "content", "file", "messageType", "TEXT", "replyTo", "substring", "token", "finalMessageType", "startsWith", "IMAGE", "VIDEO", "AUDIO", "FILE", "defineProperty", "value", "enumerable", "writable", "context", "useMultipart", "normalizedMessage", "normalizationError", "minimalMessage", "isRead", "sender", "getCurrentUserId", "username", "markMessageAsRead", "readAt", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "normalizeUser", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "offset", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "_id", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "updateCache", "cachedNotifications", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "delete", "response", "success", "setItem", "stringify", "deleteAllNotifications", "count", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "errorPolicy", "initiateCall", "recipientId", "callType", "options", "setupMediaDevices", "stream", "RTCPeerConnection", "getTracks", "track", "addTrack", "onicecandidate", "event", "candidate", "sendCallSignal", "generateCallId", "ontrack", "MediaStream", "streams", "createOffer", "offer", "setLocalDescription", "callId", "signalSub", "subscribeToCallSignals", "cleanupCall", "acceptCall", "setRemoteDescription", "RTCSessionDescription", "createAnswer", "answer", "caller", "rejectCall", "reason", "endCall", "feedback", "toggleMedia", "video", "getVideoTracks", "enabled", "getAudioTracks", "toggleCallMedia", "callSignal", "signal", "handleCallSignal", "signalType", "signalData", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "addIceCandidate", "RTCIceCandidate", "currentCall", "status", "ENDED", "endTime", "REJECTED", "close", "constraints", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "complete", "toString", "random", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "getGroup", "groupId", "group", "admins", "a", "createdAt", "updatedAt", "getUserGroups", "createGroup", "photo", "description", "refetchQueries", "updateGroup", "input", "inputWithoutPhoto", "subscribeToNewMessages", "isTokenValid", "sub$", "messageSent", "attachments", "some", "att", "updateConversationWithNewMessage", "sub", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "updateNotificationCache", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "String", "normalizeNotMessage", "image", "bio", "lastActive", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "validNotifications", "notifId", "ids", "startTyping", "stopTyping", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { <PERSON> } from 'apollo-angular';\r\nimport {\r\n  BehaviorSubject,\r\n  Observable,\r\n  of,\r\n  Subscription,\r\n  throwError,\r\n  retry,\r\n  EMPTY,\r\n} from 'rxjs';\r\nimport {\r\n  map,\r\n  catchError,\r\n  tap,\r\n  filter,\r\n  switchMap,\r\n  concatMap,\r\n  toArray,\r\n} from 'rxjs/operators';\r\nimport { from } from 'rxjs';\r\nimport {\r\n  MessageType,\r\n  Call,\r\n  CallType,\r\n  CallStatus,\r\n  IncomingCall,\r\n  CallSignal,\r\n  CallOptions,\r\n  CallFeedback,\r\n  CallSuccess,\r\n} from '../models/message.model';\r\nimport {\r\n  GET_CONVERSATIONS_QUERY,\r\n  GET_NOTIFICATIONS_QUERY,\r\n  NOTIFICATION_SUBSCRIPTION,\r\n  GET_CONVERSATION_QUERY,\r\n  SEND_MESSAGE_MUTATION,\r\n  MARK_AS_READ_MUTATION,\r\n  MESSAGE_SENT_SUBSCRIPTION,\r\n  USER_STATUS_SUBSCRIPTION,\r\n  GET_USER_QUERY,\r\n  GET_ALL_USER_QUERY,\r\n  CONVERSATION_UPDATED_SUBSCRIPTION,\r\n  SEARCH_MESSAGES_QUERY,\r\n  GET_UNREAD_MESSAGES_QUERY,\r\n  SET_USER_ONLINE_MUTATION,\r\n  SET_USER_OFFLINE_MUTATION,\r\n  GET_GROUP_QUERY,\r\n  GET_USER_GROUPS_QUERY,\r\n  CREATE_GROUP_MUTATION,\r\n  UPDATE_GROUP_MUTATION,\r\n  START_TYPING_MUTATION,\r\n  STOP_TYPING_MUTATION,\r\n  TYPING_INDICATOR_SUBSCRIPTION,\r\n  GET_CURRENT_USER_QUERY,\r\n  REACT_TO_MESSAGE_MUTATION,\r\n  FORWARD_MESSAGE_MUTATION,\r\n  PIN_MESSAGE_MUTATION,\r\n  EDIT_MESSAGE_MUTATION,\r\n  DELETE_MESSAGE_MUTATION,\r\n  GET_MESSAGES_QUERY,\r\n  GET_NOTIFICATIONS_ATTACHAMENTS,\r\n  MARK_NOTIFICATION_READ_MUTATION,\r\n  NOTIFICATIONS_READ_SUBSCRIPTION,\r\n  CREATE_CONVERSATION_MUTATION,\r\n  DELETE_NOTIFICATION_MUTATION,\r\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\r\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\r\n  // Requêtes et mutations pour les appels\r\n  CALL_HISTORY_QUERY,\r\n  CALL_DETAILS_QUERY,\r\n  CALL_STATS_QUERY,\r\n  INITIATE_CALL_MUTATION,\r\n  SEND_CALL_SIGNAL_MUTATION,\r\n  ACCEPT_CALL_MUTATION,\r\n  REJECT_CALL_MUTATION,\r\n  END_CALL_MUTATION,\r\n  TOGGLE_CALL_MEDIA_MUTATION,\r\n  CALL_SIGNAL_SUBSCRIPTION,\r\n  INCOMING_CALL_SUBSCRIPTION,\r\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\r\n  GET_VOICE_MESSAGES_QUERY,\r\n} from '../graphql/message.graphql';\r\nimport {\r\n  Conversation,\r\n  Message,\r\n  Notification,\r\n  User,\r\n  Attachment,\r\n  getNotificationAttachmentsEvent,\r\n  Group,\r\n  MessageFilter,\r\n  TypingIndicatorEvent,\r\n  GetConversationsResponse,\r\n  GetConversationResponse,\r\n  MarkAsReadResponse,\r\n  ReactToMessageResponse,\r\n  ForwardMessageResponse,\r\n  PinMessageResponse,\r\n  SearchMessagesResponse,\r\n  SendMessageResponse,\r\n  GetUnreadMessagesResponse,\r\n  GetAllUsersResponse,\r\n  GetOneUserResponse,\r\n  getCurrentUserResponse,\r\n  SetUserOnlineResponse,\r\n  SetUserOfflineResponse,\r\n  GetGroupResponse,\r\n  GetUserGroupsResponse,\r\n  CreateGroupResponse,\r\n  UpdateGroupResponse,\r\n  StartTupingResponse,\r\n  StopTypingResponse,\r\n  TypingIndicatorEvents,\r\n  getUserNotificationsResponse,\r\n  NotificationType,\r\n  MarkNotificationsAsReadResponse,\r\n  NotificationReceivedEvent,\r\n  NotificationsReadEvent,\r\n} from '../models/message.model';\r\nimport { LoggerService } from './logger.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class MessageService implements OnDestroy {\r\n  // État partagé\r\n  private activeConversation = new BehaviorSubject<string | null>(null);\r\n  private notifications = new BehaviorSubject<Notification[]>([]);\r\n  private notificationCache = new Map<string, Notification>();\r\n  private cleanupInterval: any;\r\n  private notificationCount = new BehaviorSubject<number>(0);\r\n  private onlineUsers = new Map<string, User>();\r\n  private subscriptions: Subscription[] = [];\r\n  private readonly CACHE_DURATION = 300000;\r\n  private lastFetchTime = 0;\r\n\r\n  // Propriétés pour les appels\r\n  private activeCall = new BehaviorSubject<Call | null>(null);\r\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\r\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\r\n  private localStream: MediaStream | null = null;\r\n  private remoteStream: MediaStream | null = null;\r\n  private peerConnection: RTCPeerConnection | null = null;\r\n\r\n  // Observables publics pour les appels\r\n  public activeCall$ = this.activeCall.asObservable();\r\n  public incomingCall$ = this.incomingCall.asObservable();\r\n  public callSignals$ = this.callSignals.asObservable();\r\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\r\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\r\n\r\n  // Configuration WebRTC\r\n  private readonly rtcConfig: RTCConfiguration = {\r\n    iceServers: [\r\n      { urls: 'stun:stun.l.google.com:19302' },\r\n      { urls: 'stun:stun1.l.google.com:19302' },\r\n    ],\r\n  };\r\n  private usersCache: User[] = [];\r\n\r\n  // Pagination metadata for user list\r\n  public currentUserPagination: {\r\n    totalCount: number;\r\n    totalPages: number;\r\n    currentPage: number;\r\n    hasNextPage: boolean;\r\n    hasPreviousPage: boolean;\r\n  } = {\r\n    totalCount: 0,\r\n    totalPages: 0,\r\n    currentPage: 1,\r\n    hasNextPage: false,\r\n    hasPreviousPage: false,\r\n  };\r\n\r\n  // Observables publics\r\n  public activeConversation$ = this.activeConversation.asObservable();\r\n  public notifications$ = this.notifications.asObservable();\r\n  public notificationCount$ = this.notificationCount.asObservable();\r\n\r\n  // Propriétés pour la gestion des sons\r\n  private sounds: { [key: string]: HTMLAudioElement } = {};\r\n  private isPlaying: { [key: string]: boolean } = {};\r\n  private muted = false;\r\n\r\n  constructor(\r\n    private apollo: Apollo,\r\n    private logger: LoggerService,\r\n    private zone: NgZone\r\n  ) {\r\n    this.loadNotificationsFromLocalStorage();\r\n    this.initSubscriptions();\r\n    this.startCleanupInterval();\r\n    this.preloadSounds();\r\n  }\r\n\r\n  /**\r\n   * Charge les notifications depuis le localStorage\r\n   * @private\r\n   */\r\n  private loadNotificationsFromLocalStorage(): void {\r\n    try {\r\n      const savedNotifications = localStorage.getItem('notifications');\r\n      if (savedNotifications) {\r\n        const notifications = JSON.parse(savedNotifications) as Notification[];\r\n        this.logger.debug(\r\n          'MessageService',\r\n          `Chargement de ${notifications.length} notifications depuis le localStorage`\r\n        );\r\n\r\n        // Vider le cache avant de charger les notifications pour éviter les doublons\r\n        this.notificationCache.clear();\r\n\r\n        // Mettre à jour le cache avec les notifications sauvegardées\r\n        notifications.forEach((notification) => {\r\n          // Vérifier que la notification a un ID valide\r\n          if (notification && notification.id) {\r\n            this.notificationCache.set(notification.id, notification);\r\n          }\r\n        });\r\n\r\n        // Mettre à jour le BehaviorSubject avec les notifications chargées\r\n        this.notifications.next(Array.from(this.notificationCache.values()));\r\n        this.updateUnreadCount();\r\n\r\n        this.logger.debug(\r\n          'MessageService',\r\n          `${this.notificationCache.size} notifications chargées dans le cache`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        'Erreur lors du chargement des notifications depuis le localStorage:',\r\n        error\r\n      );\r\n    }\r\n  }\r\n  private initSubscriptions(): void {\r\n    this.zone.runOutsideAngular(() => {\r\n      this.subscribeToNewNotifications().subscribe();\r\n      this.subscribeToNotificationsRead().subscribe();\r\n      this.subscribeToIncomingCalls().subscribe();\r\n    });\r\n    this.subscribeToUserStatus();\r\n  }\r\n\r\n  /**\r\n   * S'abonne aux appels entrants\r\n   */\r\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\r\n    return this.apollo\r\n      .subscribe<{ incomingCall: IncomingCall }>({\r\n        query: INCOMING_CALL_SUBSCRIPTION,\r\n      })\r\n      .pipe(\r\n        map(({ data }) => {\r\n          if (!data?.incomingCall) {\r\n            return null;\r\n          }\r\n\r\n          // Gérer l'appel entrant\r\n          this.handleIncomingCall(data.incomingCall);\r\n          return data.incomingCall;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error in incoming call subscription', error);\r\n          return of(null);\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Gère un appel entrant\r\n   */\r\n  private handleIncomingCall(call: IncomingCall): void {\r\n    this.logger.debug('Incoming call received', call);\r\n    this.incomingCall.next(call);\r\n    this.play('ringtone', true);\r\n  }\r\n\r\n  // --------------------------------------------------------------------------\r\n  // Section: Gestion des sons (intégré depuis SoundService)\r\n  // --------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Précharge les sons utilisés dans l'application\r\n   */\r\n  private preloadSounds(): void {\r\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\r\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\r\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\r\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\r\n  }\r\n\r\n  /**\r\n   * Charge un fichier audio\r\n   * @param name Nom du son\r\n   * @param path Chemin du fichier\r\n   */\r\n  private loadSound(name: string, path: string): void {\r\n    try {\r\n      const audio = new Audio(path);\r\n      audio.load();\r\n      this.sounds[name] = audio;\r\n      this.isPlaying[name] = false;\r\n\r\n      // Gérer la fin de la lecture\r\n      audio.addEventListener('ended', () => {\r\n        this.isPlaying[name] = false;\r\n      });\r\n\r\n      this.logger.debug('MessageService', `Son chargé: ${name} (${path})`);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        `Erreur lors du chargement du son ${name}:`,\r\n        error\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Joue un son\r\n   * @param name Nom du son\r\n   * @param loop Lecture en boucle\r\n   */\r\n  play(name: string, loop: boolean = false): void {\r\n    if (this.muted) {\r\n      this.logger.debug('MessageService', `Son ${name} non joué (muet)`);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const sound = this.sounds[name];\r\n      if (!sound) {\r\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\r\n        return;\r\n      }\r\n\r\n      // Configurer la lecture en boucle\r\n      sound.loop = loop;\r\n\r\n      // Jouer le son s'il n'est pas déjà en cours de lecture\r\n      if (!this.isPlaying[name]) {\r\n        sound.currentTime = 0;\r\n        sound.play().catch((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            `Erreur lors de la lecture du son ${name}:`,\r\n            error\r\n          );\r\n        });\r\n        this.isPlaying[name] = true;\r\n        this.logger.debug(\r\n          'MessageService',\r\n          `Lecture du son: ${name}, boucle: ${loop}`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        `Erreur lors de la lecture du son ${name}:`,\r\n        error\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Arrête un son\r\n   * @param name Nom du son\r\n   */\r\n  stop(name: string): void {\r\n    try {\r\n      const sound = this.sounds[name];\r\n      if (!sound) {\r\n        this.logger.warn('MessageService', `Son ${name} non trouvé`);\r\n        return;\r\n      }\r\n\r\n      // Arrêter le son s'il est en cours de lecture\r\n      if (this.isPlaying[name]) {\r\n        sound.pause();\r\n        sound.currentTime = 0;\r\n        this.isPlaying[name] = false;\r\n        this.logger.debug('MessageService', `Son arrêté: ${name}`);\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        `Erreur lors de l'arrêt du son ${name}:`,\r\n        error\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Arrête tous les sons\r\n   */\r\n  stopAllSounds(): void {\r\n    Object.keys(this.sounds).forEach((name) => {\r\n      this.stop(name);\r\n    });\r\n    this.logger.debug('MessageService', 'Tous les sons ont été arrêtés');\r\n  }\r\n\r\n  /**\r\n   * Active ou désactive le son\r\n   * @param muted True pour désactiver le son, false pour l'activer\r\n   */\r\n  setMuted(muted: boolean): void {\r\n    this.muted = muted;\r\n    this.logger.info('MessageService', `Son ${muted ? 'désactivé' : 'activé'}`);\r\n\r\n    if (muted) {\r\n      this.stopAllSounds();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifie si le son est désactivé\r\n   * @returns True si le son est désactivé, false sinon\r\n   */\r\n  isMuted(): boolean {\r\n    return this.muted;\r\n  }\r\n\r\n  /**\r\n   * Joue le son de notification\r\n   */\r\n  playNotificationSound(): void {\r\n    console.log('MessageService: Tentative de lecture du son de notification');\r\n\r\n    if (this.muted) {\r\n      console.log('MessageService: Son désactivé, notification ignorée');\r\n      return;\r\n    }\r\n\r\n    // Utiliser l'API Web Audio pour générer un son de notification simple\r\n    try {\r\n      // Créer un contexte audio\r\n      const audioContext = new (window.AudioContext ||\r\n        (window as any).webkitAudioContext)();\r\n\r\n      // Créer un oscillateur pour générer un son\r\n      const oscillator = audioContext.createOscillator();\r\n      const gainNode = audioContext.createGain();\r\n\r\n      // Configurer l'oscillateur\r\n      oscillator.type = 'sine';\r\n      oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // La note A5\r\n\r\n      // Configurer le volume\r\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\r\n      gainNode.gain.linearRampToValueAtTime(\r\n        0.5,\r\n        audioContext.currentTime + 0.01\r\n      );\r\n      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);\r\n\r\n      // Connecter les nœuds\r\n      oscillator.connect(gainNode);\r\n      gainNode.connect(audioContext.destination);\r\n\r\n      // Démarrer et arrêter l'oscillateur\r\n      oscillator.start(audioContext.currentTime);\r\n      oscillator.stop(audioContext.currentTime + 0.3);\r\n\r\n      console.log('MessageService: Son de notification généré avec succès');\r\n    } catch (error) {\r\n      console.error(\r\n        'MessageService: Erreur lors de la génération du son:',\r\n        error\r\n      );\r\n\r\n      // Fallback à la méthode originale en cas d'erreur\r\n      try {\r\n        const audio = new Audio('assets/sounds/notification.mp3');\r\n        audio.volume = 1.0; // Volume maximum\r\n        audio.play().catch((err) => {\r\n          console.error(\r\n            'MessageService: Erreur lors de la lecture du fichier son:',\r\n            err\r\n          );\r\n        });\r\n      } catch (audioError) {\r\n        console.error(\r\n          'MessageService: Exception lors de la lecture du fichier son:',\r\n          audioError\r\n        );\r\n      }\r\n    }\r\n  }\r\n  // --------------------------------------------------------------------------\r\n  // Section 1: Méthodes pour les Messages\r\n  // --------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Envoie un message vocal à un utilisateur\r\n   * @param receiverId ID de l'utilisateur destinataire\r\n   * @param audioBlob Blob audio à envoyer\r\n   * @param conversationId ID de la conversation (optionnel)\r\n   * @param duration Durée de l'enregistrement en secondes (optionnel)\r\n   * @returns Observable avec le message envoyé\r\n   */\r\n  sendVoiceMessage(\r\n    receiverId: string,\r\n    audioBlob: Blob,\r\n    conversationId?: string,\r\n    duration?: number\r\n  ): Observable<Message> {\r\n    this.logger.debug(\r\n      `[MessageService] Sending voice message to user ${receiverId}, duration: ${duration}s`\r\n    );\r\n\r\n    // Vérifier que le blob audio est valide\r\n    if (!audioBlob || audioBlob.size === 0) {\r\n      this.logger.error('[MessageService] Invalid audio blob');\r\n      return throwError(() => new Error('Invalid audio blob'));\r\n    }\r\n\r\n    // Créer un fichier à partir du blob audio avec un nom unique\r\n    const timestamp = Date.now();\r\n    const audioFile = new File([audioBlob], `voice-message-${timestamp}.webm`, {\r\n      type: 'audio/webm',\r\n      lastModified: timestamp,\r\n    });\r\n\r\n    // Vérifier que le fichier a été créé correctement\r\n    if (!audioFile || audioFile.size === 0) {\r\n      this.logger.error('[MessageService] Failed to create audio file');\r\n      return throwError(() => new Error('Failed to create audio file'));\r\n    }\r\n\r\n    this.logger.debug(\r\n      `[MessageService] Created audio file: ${audioFile.name}, size: ${audioFile.size} bytes`\r\n    );\r\n\r\n    // Créer des métadonnées pour le message vocal\r\n    const metadata = {\r\n      duration: duration || 0,\r\n      isVoiceMessage: true,\r\n      timestamp: timestamp,\r\n    };\r\n\r\n    // Utiliser la méthode sendMessage avec le type VOICE_MESSAGE\r\n    // Utiliser une chaîne vide comme contenu pour éviter les problèmes de validation\r\n    return this.sendMessage(\r\n      receiverId,\r\n      ' ', // Espace comme contenu minimal pour passer la validation\r\n      audioFile,\r\n      MessageType.VOICE_MESSAGE,\r\n      conversationId,\r\n      undefined,\r\n      metadata\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Joue un fichier audio\r\n   * @param audioUrl URL du fichier audio à jouer\r\n   * @returns Promise qui se résout lorsque la lecture est terminée\r\n   */\r\n  playAudio(audioUrl: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const audio = new Audio(audioUrl);\r\n\r\n      audio.onended = () => {\r\n        resolve();\r\n      };\r\n\r\n      audio.onerror = (error) => {\r\n        this.logger.error(`[MessageService] Error playing audio:`, error);\r\n        reject(error);\r\n      };\r\n\r\n      audio.play().catch((error) => {\r\n        this.logger.error(`[MessageService] Error playing audio:`, error);\r\n        reject(error);\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Récupère tous les messages vocaux de l'utilisateur\r\n   * @returns Observable avec la liste des messages vocaux\r\n   */\r\n  getVoiceMessages(): Observable<Call[]> {\r\n    this.logger.debug('[MessageService] Getting voice messages');\r\n\r\n    return this.apollo\r\n      .watchQuery<{ getVoiceMessages: Call[] }>({\r\n        query: GET_VOICE_MESSAGES_QUERY,\r\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          const voiceMessages = result.data?.getVoiceMessages || [];\r\n          this.logger.debug(\r\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\r\n          );\r\n          return voiceMessages;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            '[MessageService] Error fetching voice messages:',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to fetch voice messages'));\r\n        })\r\n      );\r\n  }\r\n  // Message methods\r\n  getMessages(\r\n    senderId: string,\r\n    receiverId: string,\r\n    conversationId: string,\r\n    page: number = 1,\r\n    limit: number = 10\r\n  ): Observable<Message[]> {\r\n    return this.apollo\r\n      .watchQuery<{ getMessages: Message[] }>({\r\n        query: GET_MESSAGES_QUERY,\r\n        variables: { senderId, receiverId, conversationId, limit, page },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          const messages = result.data?.getMessages || [];\r\n          return messages.map((msg) => this.normalizeMessage(msg));\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error fetching messages:', error);\r\n          return throwError(() => new Error('Failed to fetch messages'));\r\n        })\r\n      );\r\n  }\r\n  editMessage(messageId: string, newContent: string): Observable<Message> {\r\n    return this.apollo\r\n      .mutate<{ editMessage: Message }>({\r\n        mutation: EDIT_MESSAGE_MUTATION,\r\n        variables: { messageId, newContent },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.editMessage) {\r\n            throw new Error('Failed to edit message');\r\n          }\r\n          return this.normalizeMessage(result.data.editMessage);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error editing message:', error);\r\n          return throwError(() => new Error('Failed to edit message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  deleteMessage(messageId: string): Observable<Message> {\r\n    return this.apollo\r\n      .mutate<{ deleteMessage: Message }>({\r\n        mutation: DELETE_MESSAGE_MUTATION,\r\n        variables: { messageId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.deleteMessage) {\r\n            throw new Error('Failed to delete message');\r\n          }\r\n          return this.normalizeMessage(result.data.deleteMessage);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error deleting message:', error);\r\n          return throwError(() => new Error('Failed to delete message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  sendMessage(\r\n    receiverId: string,\r\n    content: string,\r\n    file?: File,\r\n    messageType: MessageType = MessageType.TEXT,\r\n    conversationId?: string,\r\n    replyTo?: string,\r\n    metadata?: any\r\n  ): Observable<Message> {\r\n    this.logger.info(\r\n      `[MessageService] Sending message to: ${receiverId}, hasFile: ${!!file}`\r\n    );\r\n    this.logger.debug(\r\n      `[MessageService] Message content: \"${content?.substring(0, 50)}${\r\n        content?.length > 50 ? '...' : ''\r\n      }\"`\r\n    );\r\n\r\n    // Vérifier l'authentification\r\n    const token = localStorage.getItem('token');\r\n    this.logger.debug(\r\n      `[MessageService] Authentication check before sending message: token=${!!token}`\r\n    );\r\n\r\n    // Utiliser le type de message fourni ou le déterminer automatiquement\r\n    let finalMessageType = messageType;\r\n\r\n    // Si le type n'est pas explicitement fourni et qu'il y a un fichier, déterminer le type\r\n    if (file) {\r\n      // Si le type est déjà VOICE_MESSAGE, le conserver\r\n      if (messageType === MessageType.VOICE_MESSAGE) {\r\n        finalMessageType = MessageType.VOICE_MESSAGE;\r\n        this.logger.debug(`[MessageService] Using explicit VOICE_MESSAGE type`);\r\n      }\r\n      // Sinon, déterminer le type en fonction du type de fichier\r\n      else if (messageType === MessageType.TEXT) {\r\n        if (file.type.startsWith('image/')) {\r\n          finalMessageType = MessageType.IMAGE;\r\n        } else if (file.type.startsWith('video/')) {\r\n          finalMessageType = MessageType.VIDEO;\r\n        } else if (file.type.startsWith('audio/')) {\r\n          // Vérifier si c'est un message vocal basé sur les métadonnées\r\n          if (metadata && metadata.isVoiceMessage) {\r\n            finalMessageType = MessageType.VOICE_MESSAGE;\r\n          } else {\r\n            finalMessageType = MessageType.AUDIO;\r\n          }\r\n        } else {\r\n          finalMessageType = MessageType.FILE;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.logger.debug(\r\n      `[MessageService] Message type determined: ${finalMessageType}`\r\n    );\r\n\r\n    // Ajouter le type de message aux variables\r\n    // Utiliser directement la valeur de l'énumération sans conversion\r\n    const variables: any = {\r\n      receiverId,\r\n      content,\r\n      type: finalMessageType, // Ajouter explicitement le type de message\r\n    };\r\n\r\n    // Forcer le type à être une valeur d'énumération GraphQL\r\n    // Cela empêche Apollo de convertir la valeur en minuscules\r\n    if (variables.type) {\r\n      Object.defineProperty(variables, 'type', {\r\n        value: finalMessageType,\r\n        enumerable: true,\r\n        writable: false,\r\n      });\r\n    }\r\n\r\n    // Ajouter les métadonnées si elles sont fournies\r\n    if (metadata) {\r\n      variables.metadata = metadata;\r\n      this.logger.debug(`[MessageService] Metadata attached:`, metadata);\r\n    }\r\n\r\n    if (file) {\r\n      variables.file = file;\r\n      this.logger.debug(\r\n        `[MessageService] File attached: ${file.name}, size: ${file.size}, type: ${file.type}, messageType: ${finalMessageType}`\r\n      );\r\n    }\r\n    if (conversationId) {\r\n      variables.conversationId = conversationId;\r\n      this.logger.debug(\r\n        `[MessageService] Using existing conversation: ${conversationId}`\r\n      );\r\n    }\r\n    if (replyTo) {\r\n      variables.replyTo = replyTo;\r\n      this.logger.debug(`[MessageService] Replying to message: ${replyTo}`);\r\n    }\r\n\r\n    const context = file ? { useMultipart: true, file } : undefined;\r\n\r\n    this.logger.debug(\r\n      `[MessageService] Sending GraphQL mutation with variables:`,\r\n      variables\r\n    );\r\n\r\n    return this.apollo\r\n      .mutate<SendMessageResponse>({\r\n        mutation: SEND_MESSAGE_MUTATION,\r\n        variables,\r\n        context,\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          this.logger.debug(`[MessageService] Message send response:`, result);\r\n\r\n          if (!result.data?.sendMessage) {\r\n            this.logger.error(\r\n              `[MessageService] Failed to send message: No data returned`\r\n            );\r\n            throw new Error('Failed to send message');\r\n          }\r\n\r\n          try {\r\n            this.logger.debug(\r\n              `[MessageService] Normalizing sent message`,\r\n              result.data.sendMessage\r\n            );\r\n            const normalizedMessage = this.normalizeMessage(\r\n              result.data.sendMessage\r\n            );\r\n\r\n            this.logger.info(\r\n              `[MessageService] Message sent successfully: ${normalizedMessage.id}`\r\n            );\r\n            return normalizedMessage;\r\n          } catch (normalizationError) {\r\n            this.logger.error(\r\n              `[MessageService] Error normalizing message:`,\r\n              normalizationError\r\n            );\r\n\r\n            // Retourner un message minimal mais valide plutôt que de lancer une erreur\r\n            const minimalMessage: Message = {\r\n              id: result.data.sendMessage.id || 'temp-' + Date.now(),\r\n              content: result.data.sendMessage.content || '',\r\n              type: result.data.sendMessage.type || MessageType.TEXT,\r\n              timestamp: new Date(),\r\n              isRead: false,\r\n              sender: {\r\n                id: this.getCurrentUserId(),\r\n                username: 'You',\r\n              },\r\n            };\r\n\r\n            this.logger.info(\r\n              `[MessageService] Returning minimal message: ${minimalMessage.id}`\r\n            );\r\n            return minimalMessage;\r\n          }\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(`[MessageService] Error sending message:`, error);\r\n          return throwError(() => new Error('Failed to send message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  markMessageAsRead(messageId: string): Observable<Message> {\r\n    return this.apollo\r\n      .mutate<MarkAsReadResponse>({\r\n        mutation: MARK_AS_READ_MUTATION,\r\n        variables: { messageId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.markMessageAsRead)\r\n            throw new Error('Failed to mark message as read');\r\n          return {\r\n            ...result.data.markMessageAsRead,\r\n            readAt: new Date(),\r\n          };\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error marking message as read:', error);\r\n          return throwError(() => new Error('Failed to mark message as read'));\r\n        })\r\n      );\r\n  }\r\n\r\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\r\n    return this.apollo\r\n      .mutate<ReactToMessageResponse>({\r\n        mutation: REACT_TO_MESSAGE_MUTATION,\r\n        variables: { messageId, emoji },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.reactToMessage)\r\n            throw new Error('Failed to react to message');\r\n          return result.data.reactToMessage;\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error reacting to message:', error);\r\n          return throwError(() => new Error('Failed to react to message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  forwardMessage(\r\n    messageId: string,\r\n    conversationIds: string[]\r\n  ): Observable<Message[]> {\r\n    return this.apollo\r\n      .mutate<ForwardMessageResponse>({\r\n        mutation: FORWARD_MESSAGE_MUTATION,\r\n        variables: { messageId, conversationIds },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.forwardMessage)\r\n            throw new Error('Failed to forward message');\r\n          return result.data.forwardMessage.map((msg) => ({\r\n            ...msg,\r\n            timestamp: msg.timestamp\r\n              ? this.normalizeDate(msg.timestamp)\r\n              : new Date(),\r\n          }));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error forwarding message:', error);\r\n          return throwError(() => new Error('Failed to forward message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\r\n    return this.apollo\r\n      .mutate<PinMessageResponse>({\r\n        mutation: PIN_MESSAGE_MUTATION,\r\n        variables: { messageId, conversationId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.pinMessage)\r\n            throw new Error('Failed to pin message');\r\n          return {\r\n            ...result.data.pinMessage,\r\n            pinnedAt: new Date(),\r\n          };\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error pinning message:', error);\r\n          return throwError(() => new Error('Failed to pin message'));\r\n        })\r\n      );\r\n  }\r\n\r\n  searchMessages(\r\n    query: string,\r\n    conversationId?: string,\r\n    filters: MessageFilter = {}\r\n  ): Observable<Message[]> {\r\n    return this.apollo\r\n      .watchQuery<SearchMessagesResponse>({\r\n        query: SEARCH_MESSAGES_QUERY,\r\n        variables: {\r\n          query,\r\n          conversationId,\r\n          ...filters,\r\n          dateFrom: this.toSafeISOString(filters.dateFrom),\r\n          dateTo: this.toSafeISOString(filters.dateTo),\r\n        },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map(\r\n          (result) =>\r\n            result.data?.searchMessages?.map((msg) => ({\r\n              ...msg,\r\n              timestamp: this.safeDate(msg.timestamp),\r\n              sender: this.normalizeUser(msg.sender),\r\n            })) || []\r\n        ),\r\n        catchError((error) => {\r\n          console.error('Error searching messages:', error);\r\n          return throwError(() => new Error('Failed to search messages'));\r\n        })\r\n      );\r\n  }\r\n\r\n  getUnreadMessages(userId: string): Observable<Message[]> {\r\n    return this.apollo\r\n      .watchQuery<GetUnreadMessagesResponse>({\r\n        query: GET_UNREAD_MESSAGES_QUERY,\r\n        variables: { userId },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map(\r\n          (result) =>\r\n            result.data?.getUnreadMessages?.map((msg) => ({\r\n              ...msg,\r\n              timestamp: this.safeDate(msg.timestamp),\r\n              sender: this.normalizeUser(msg.sender),\r\n            })) || []\r\n        ),\r\n        catchError((error) => {\r\n          console.error('Error fetching unread messages:', error);\r\n          return throwError(() => new Error('Failed to fetch unread messages'));\r\n        })\r\n      );\r\n  }\r\n\r\n  setActiveConversation(conversationId: string): void {\r\n    this.activeConversation.next(conversationId);\r\n  }\r\n\r\n  getConversations(): Observable<Conversation[]> {\r\n    return this.apollo\r\n      .watchQuery<GetConversationsResponse>({\r\n        query: GET_CONVERSATIONS_QUERY,\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          const conversations = result.data?.getConversations || [];\r\n          return conversations.map((conv) => this.normalizeConversation(conv));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error fetching conversations:', error);\r\n          return throwError(() => new Error('Failed to load conversations'));\r\n        })\r\n      );\r\n  }\r\n\r\n  getConversation(\r\n    conversationId: string,\r\n    limit?: number,\r\n    page?: number\r\n  ): Observable<Conversation> {\r\n    this.logger.info(\r\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\r\n    );\r\n\r\n    const variables: any = { conversationId };\r\n\r\n    // Ajouter les paramètres de pagination s'ils sont fournis\r\n    if (limit !== undefined) {\r\n      variables.limit = limit;\r\n    } else {\r\n      variables.limit = 10; // Valeur par défaut\r\n    }\r\n\r\n    // Calculer l'offset à partir de la page si elle est fournie\r\n    if (page !== undefined) {\r\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\r\n      const offset = (page - 1) * variables.limit;\r\n      variables.offset = offset;\r\n      this.logger.debug(\r\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\r\n      );\r\n    } else {\r\n      variables.offset = 0; // Valeur par défaut\r\n    }\r\n\r\n    this.logger.debug(\r\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\r\n    );\r\n\r\n    return this.apollo\r\n      .watchQuery<GetConversationResponse>({\r\n        query: GET_CONVERSATION_QUERY,\r\n        variables: variables,\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          this.logger.debug(\r\n            `[MessageService] Conversation response received:`,\r\n            result\r\n          );\r\n\r\n          const conv = result.data?.getConversation;\r\n          if (!conv) {\r\n            this.logger.error(\r\n              `[MessageService] Conversation not found: ${conversationId}`\r\n            );\r\n            throw new Error('Conversation not found');\r\n          }\r\n\r\n          this.logger.debug(\r\n            `[MessageService] Normalizing conversation: ${conversationId}`\r\n          );\r\n          const normalizedConversation = this.normalizeConversation(conv);\r\n\r\n          this.logger.info(\r\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\r\n              normalizedConversation.participants?.length || 0\r\n            }, messages: ${normalizedConversation.messages?.length || 0}`\r\n          );\r\n          return normalizedConversation;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            `[MessageService] Error fetching conversation:`,\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to load conversation'));\r\n        })\r\n      );\r\n  }\r\n\r\n  createConversation(userId: string): Observable<Conversation> {\r\n    this.logger.info(\r\n      `[MessageService] Creating conversation with user: ${userId}`\r\n    );\r\n\r\n    if (!userId) {\r\n      this.logger.error(\r\n        `[MessageService] Cannot create conversation: userId is undefined`\r\n      );\r\n      return throwError(\r\n        () => new Error('User ID is required to create a conversation')\r\n      );\r\n    }\r\n\r\n    return this.apollo\r\n      .mutate<{ createConversation: Conversation }>({\r\n        mutation: CREATE_CONVERSATION_MUTATION,\r\n        variables: { userId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          this.logger.debug(\r\n            `[MessageService] Conversation creation response:`,\r\n            result\r\n          );\r\n\r\n          const conversation = result.data?.createConversation;\r\n          if (!conversation) {\r\n            this.logger.error(\r\n              `[MessageService] Failed to create conversation with user: ${userId}`\r\n            );\r\n            throw new Error('Failed to create conversation');\r\n          }\r\n\r\n          try {\r\n            const normalizedConversation =\r\n              this.normalizeConversation(conversation);\r\n            this.logger.info(\r\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\r\n            );\r\n            return normalizedConversation;\r\n          } catch (error) {\r\n            this.logger.error(\r\n              `[MessageService] Error normalizing created conversation:`,\r\n              error\r\n            );\r\n            throw new Error('Error processing created conversation');\r\n          }\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            `[MessageService] Error creating conversation with user ${userId}:`,\r\n            error\r\n          );\r\n          return throwError(\r\n            () => new Error(`Failed to create conversation: ${error.message}`)\r\n          );\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\r\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\r\n   * @returns Observable avec la conversation\r\n   */\r\n  getOrCreateConversation(userId: string): Observable<Conversation> {\r\n    this.logger.info(\r\n      `[MessageService] Getting or creating conversation with user: ${userId}`\r\n    );\r\n\r\n    if (!userId) {\r\n      this.logger.error(\r\n        `[MessageService] Cannot get/create conversation: userId is undefined`\r\n      );\r\n      return throwError(\r\n        () => new Error('User ID is required to get/create a conversation')\r\n      );\r\n    }\r\n\r\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\r\n    return this.getConversations().pipe(\r\n      map((conversations) => {\r\n        // Récupérer l'ID de l'utilisateur actuel\r\n        const currentUserId = this.getCurrentUserId();\r\n\r\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\r\n        const existingConversation = conversations.find((conv) => {\r\n          if (conv.isGroup) return false;\r\n\r\n          // Vérifier si la conversation contient les deux utilisateurs\r\n          const participantIds =\r\n            conv.participants?.map((p) => p.id || p._id) || [];\r\n          return (\r\n            participantIds.includes(userId) &&\r\n            participantIds.includes(currentUserId)\r\n          );\r\n        });\r\n\r\n        if (existingConversation) {\r\n          this.logger.info(\r\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\r\n          );\r\n          return existingConversation;\r\n        }\r\n\r\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\r\n        throw new Error('No existing conversation found');\r\n      }),\r\n      catchError((error) => {\r\n        this.logger.info(\r\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\r\n        );\r\n        return this.createConversation(userId);\r\n      })\r\n    );\r\n  }\r\n\r\n  // --------------------------------------------------------------------------\r\n  // Section 2: Méthodes pour les Notifications\r\n  // --------------------------------------------------------------------------\r\n  // Propriétés pour la pagination des notifications\r\n  private notificationPagination = {\r\n    currentPage: 1,\r\n    limit: 10,\r\n    hasMoreNotifications: true,\r\n  };\r\n\r\n  getNotifications(\r\n    refresh = false,\r\n    page = 1,\r\n    limit = 10\r\n  ): Observable<Notification[]> {\r\n    this.logger.info(\r\n      'MessageService',\r\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\r\n    );\r\n    this.logger.debug('MessageService', 'Using query', {\r\n      query: GET_NOTIFICATIONS_QUERY,\r\n    });\r\n\r\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\r\n    // pour conserver les suppressions locales\r\n    if (refresh) {\r\n      this.logger.debug(\r\n        'MessageService',\r\n        'Resetting pagination due to refresh'\r\n      );\r\n      this.notificationPagination.currentPage = 1;\r\n      this.notificationPagination.hasMoreNotifications = true;\r\n    }\r\n\r\n    // Mettre à jour les paramètres de pagination\r\n    this.notificationPagination.currentPage = page;\r\n    this.notificationPagination.limit = limit;\r\n\r\n    // Récupérer les IDs des notifications supprimées du localStorage\r\n    const deletedNotificationIds = this.getDeletedNotificationIds();\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\r\n    );\r\n\r\n    return this.apollo\r\n      .watchQuery<getUserNotificationsResponse>({\r\n        query: GET_NOTIFICATIONS_QUERY,\r\n        variables: {\r\n          page: page,\r\n          limit: limit,\r\n        },\r\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          this.logger.debug(\r\n            'MessageService',\r\n            'Notifications response received'\r\n          );\r\n\r\n          if (result.errors) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'GraphQL errors:',\r\n              result.errors\r\n            );\r\n            throw new Error(result.errors.map((e) => e.message).join(', '));\r\n          }\r\n\r\n          const notifications = result.data?.getUserNotifications || [];\r\n          this.logger.debug(\r\n            'MessageService',\r\n            `Received ${notifications.length} notifications from server for page ${page}`\r\n          );\r\n\r\n          // Vérifier s'il y a plus de notifications à charger\r\n          this.notificationPagination.hasMoreNotifications =\r\n            notifications.length >= limit;\r\n\r\n          if (notifications.length === 0) {\r\n            this.logger.info(\r\n              'MessageService',\r\n              'No notifications received from server'\r\n            );\r\n            this.notificationPagination.hasMoreNotifications = false;\r\n          }\r\n\r\n          // Filtrer les notifications supprimées\r\n          const filteredNotifications = notifications.filter(\r\n            (notif) => !deletedNotificationIds.has(notif.id)\r\n          );\r\n\r\n          this.logger.debug(\r\n            'MessageService',\r\n            `Filtered out ${\r\n              notifications.length - filteredNotifications.length\r\n            } deleted notifications`\r\n          );\r\n\r\n          // Afficher les notifications reçues pour le débogage\r\n          filteredNotifications.forEach((notif, index) => {\r\n            console.log(`Notification ${index + 1} (page ${page}):`, {\r\n              id: notif.id || (notif as any)._id,\r\n              type: notif.type,\r\n              content: notif.content,\r\n              isRead: notif.isRead,\r\n            });\r\n          });\r\n\r\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\r\n          // Mettre à jour le cache avec les nouvelles notifications\r\n          this.updateCache(filteredNotifications);\r\n\r\n          // Récupérer toutes les notifications du cache\r\n          const cachedNotifications = Array.from(\r\n            this.notificationCache.values()\r\n          );\r\n\r\n          console.log(\r\n            `Total notifications in cache after update: ${cachedNotifications.length}`\r\n          );\r\n\r\n          // Mettre à jour le BehaviorSubject avec toutes les notifications\r\n          this.notifications.next(cachedNotifications);\r\n\r\n          // Mettre à jour le compteur de notifications non lues\r\n          this.updateUnreadCount();\r\n\r\n          // Sauvegarder les notifications dans le localStorage\r\n          this.saveNotificationsToLocalStorage();\r\n\r\n          return cachedNotifications;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error loading notifications:',\r\n            error\r\n          );\r\n\r\n          if (error.graphQLErrors) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'GraphQL errors:',\r\n              error.graphQLErrors\r\n            );\r\n          }\r\n\r\n          if (error.networkError) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'Network error:',\r\n              error.networkError\r\n            );\r\n          }\r\n\r\n          return throwError(() => new Error('Failed to load notifications'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Récupère les IDs des notifications supprimées du localStorage\r\n   * @private\r\n   * @returns Set contenant les IDs des notifications supprimées\r\n   */\r\n  private getDeletedNotificationIds(): Set<string> {\r\n    try {\r\n      const deletedIds = new Set<string>();\r\n      const savedNotifications = localStorage.getItem('notifications');\r\n\r\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\r\n      if (!savedNotifications) {\r\n        return deletedIds;\r\n      }\r\n\r\n      // Récupérer les IDs des notifications sauvegardées\r\n      const savedNotificationIds = new Set(\r\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\r\n      );\r\n\r\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\r\n      const serverNotifications =\r\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\r\n          query: GET_NOTIFICATIONS_QUERY,\r\n        })?.getUserNotifications || [];\r\n\r\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\r\n      serverNotifications.forEach((notification) => {\r\n        if (!savedNotificationIds.has(notification.id)) {\r\n          deletedIds.add(notification.id);\r\n        }\r\n      });\r\n\r\n      return deletedIds;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\r\n        error\r\n      );\r\n      return new Set<string>();\r\n    }\r\n  }\r\n\r\n  // Méthode pour vérifier s'il y a plus de notifications à charger\r\n  hasMoreNotifications(): boolean {\r\n    return this.notificationPagination.hasMoreNotifications;\r\n  }\r\n\r\n  // Méthode pour charger la page suivante de notifications\r\n  loadMoreNotifications(): Observable<Notification[]> {\r\n    const nextPage = this.notificationPagination.currentPage + 1;\r\n    return this.getNotifications(\r\n      false,\r\n      nextPage,\r\n      this.notificationPagination.limit\r\n    );\r\n  }\r\n  getNotificationById(id: string): Observable<Notification | undefined> {\r\n    return this.notifications$.pipe(\r\n      map((notifications) => notifications.find((n) => n.id === id)),\r\n      catchError((error) => {\r\n        this.logger.error('Error finding notification:', error);\r\n        return throwError(() => new Error('Failed to find notification'));\r\n      })\r\n    );\r\n  }\r\n  getNotificationCount(): number {\r\n    return this.notifications.value?.length || 0;\r\n  }\r\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\r\n    return this.apollo\r\n      .query<getNotificationAttachmentsEvent>({\r\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\r\n        variables: { id: notificationId },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .pipe(\r\n        map((result) => result.data?.getNotificationAttachments || []),\r\n        catchError((error) => {\r\n          this.logger.error('Error fetching notification attachments:', error);\r\n          return throwError(() => new Error('Failed to fetch attachments'));\r\n        })\r\n      );\r\n  }\r\n  getUnreadNotifications(): Observable<Notification[]> {\r\n    return this.notifications$.pipe(\r\n      map((notifications) => notifications.filter((n) => !n.isRead))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Supprime une notification\r\n   * @param notificationId ID de la notification à supprimer\r\n   * @returns Observable avec le résultat de l'opération\r\n   */\r\n  deleteNotification(\r\n    notificationId: string\r\n  ): Observable<{ success: boolean; message: string }> {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Suppression de la notification ${notificationId}`\r\n    );\r\n\r\n    if (!notificationId) {\r\n      this.logger.warn('MessageService', 'ID de notification invalide');\r\n      return throwError(() => new Error('ID de notification invalide'));\r\n    }\r\n\r\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\r\n    this.notificationCache.delete(notificationId);\r\n    this.notifications.next(Array.from(this.notificationCache.values()));\r\n    this.updateUnreadCount();\r\n    this.saveNotificationsToLocalStorage();\r\n\r\n    // Appeler le backend pour supprimer la notification\r\n    return this.apollo\r\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\r\n        mutation: DELETE_NOTIFICATION_MUTATION,\r\n        variables: { notificationId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const response = result.data?.deleteNotification;\r\n          if (!response) {\r\n            throw new Error('Réponse de suppression invalide');\r\n          }\r\n\r\n          this.logger.debug(\r\n            'MessageService',\r\n            'Résultat de la suppression:',\r\n            response\r\n          );\r\n\r\n          return response;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Erreur lors de la suppression de la notification:',\r\n            error\r\n          );\r\n\r\n          // En cas d'erreur, on garde la suppression locale\r\n          return of({\r\n            success: true,\r\n            message: 'Notification supprimée localement (erreur serveur)',\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Sauvegarde les notifications dans le localStorage\r\n   * @private\r\n   */\r\n  private saveNotificationsToLocalStorage(): void {\r\n    try {\r\n      const notifications = Array.from(this.notificationCache.values());\r\n      localStorage.setItem('notifications', JSON.stringify(notifications));\r\n      this.logger.debug(\r\n        'MessageService',\r\n        'Notifications sauvegardées localement'\r\n      );\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        'Erreur lors de la sauvegarde des notifications:',\r\n        error\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Supprime toutes les notifications de l'utilisateur\r\n   * @returns Observable avec le résultat de l'opération\r\n   */\r\n  deleteAllNotifications(): Observable<{\r\n    success: boolean;\r\n    count: number;\r\n    message: string;\r\n  }> {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      'Suppression de toutes les notifications'\r\n    );\r\n\r\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\r\n    const count = this.notificationCache.size;\r\n    this.notificationCache.clear();\r\n    this.notifications.next([]);\r\n    this.notificationCount.next(0);\r\n    this.saveNotificationsToLocalStorage();\r\n\r\n    // Appeler le backend pour supprimer toutes les notifications\r\n    return this.apollo\r\n      .mutate<{\r\n        deleteAllNotifications: {\r\n          success: boolean;\r\n          count: number;\r\n          message: string;\r\n        };\r\n      }>({\r\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const response = result.data?.deleteAllNotifications;\r\n          if (!response) {\r\n            throw new Error('Réponse de suppression invalide');\r\n          }\r\n\r\n          this.logger.debug(\r\n            'MessageService',\r\n            'Résultat de la suppression de toutes les notifications:',\r\n            response\r\n          );\r\n\r\n          return response;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Erreur lors de la suppression de toutes les notifications:',\r\n            error\r\n          );\r\n\r\n          // En cas d'erreur, on garde la suppression locale\r\n          return of({\r\n            success: true,\r\n            count,\r\n            message: `${count} notifications supprimées localement (erreur serveur)`,\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Supprime plusieurs notifications\r\n   * @param notificationIds IDs des notifications à supprimer\r\n   * @returns Observable avec le résultat de l'opération\r\n   */\r\n  deleteMultipleNotifications(\r\n    notificationIds: string[]\r\n  ): Observable<{ success: boolean; count: number; message: string }> {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Suppression de ${notificationIds.length} notifications`\r\n    );\r\n\r\n    if (!notificationIds || notificationIds.length === 0) {\r\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\r\n      return throwError(() => new Error('Aucun ID de notification fourni'));\r\n    }\r\n\r\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\r\n    let count = 0;\r\n    notificationIds.forEach((id) => {\r\n      if (this.notificationCache.has(id)) {\r\n        this.notificationCache.delete(id);\r\n        count++;\r\n      }\r\n    });\r\n\r\n    this.notifications.next(Array.from(this.notificationCache.values()));\r\n    this.updateUnreadCount();\r\n    this.saveNotificationsToLocalStorage();\r\n\r\n    // Appeler le backend pour supprimer les notifications\r\n    return this.apollo\r\n      .mutate<{\r\n        deleteMultipleNotifications: {\r\n          success: boolean;\r\n          count: number;\r\n          message: string;\r\n        };\r\n      }>({\r\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\r\n        variables: { notificationIds },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const response = result.data?.deleteMultipleNotifications;\r\n          if (!response) {\r\n            throw new Error('Réponse de suppression invalide');\r\n          }\r\n\r\n          this.logger.debug(\r\n            'MessageService',\r\n            'Résultat de la suppression multiple:',\r\n            response\r\n          );\r\n\r\n          return response;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Erreur lors de la suppression multiple de notifications:',\r\n            error\r\n          );\r\n\r\n          // En cas d'erreur, on garde la suppression locale\r\n          return of({\r\n            success: count > 0,\r\n            count,\r\n            message: `${count} notifications supprimées localement (erreur serveur)`,\r\n          });\r\n        })\r\n      );\r\n  }\r\n  groupNotificationsByType(): Observable<\r\n    Map<NotificationType, Notification[]>\r\n  > {\r\n    return this.notifications$.pipe(\r\n      map((notifications) => {\r\n        const groups = new Map<NotificationType, Notification[]>();\r\n        notifications.forEach((notif) => {\r\n          if (!groups.has(notif.type)) {\r\n            groups.set(notif.type, []);\r\n          }\r\n          groups.get(notif.type)?.push(notif);\r\n        });\r\n        return groups;\r\n      })\r\n    );\r\n  }\r\n  markAsRead(notificationIds: string[]): Observable<{\r\n    success: boolean;\r\n    readCount: number;\r\n    remainingCount: number;\r\n  }> {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\r\n    );\r\n\r\n    if (!notificationIds || notificationIds.length === 0) {\r\n      this.logger.warn('MessageService', 'No notification IDs provided');\r\n      return of({\r\n        success: false,\r\n        readCount: 0,\r\n        remainingCount: this.notificationCount.value,\r\n      });\r\n    }\r\n\r\n    // Vérifier que tous les IDs sont valides\r\n    const validIds = notificationIds.filter(\r\n      (id) => id && typeof id === 'string' && id.trim() !== ''\r\n    );\r\n\r\n    if (validIds.length !== notificationIds.length) {\r\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\r\n        provided: notificationIds,\r\n        valid: validIds,\r\n      });\r\n      return throwError(() => new Error('Some notification IDs are invalid'));\r\n    }\r\n\r\n    this.logger.debug(\r\n      'MessageService',\r\n      'Sending mutation to mark notifications as read',\r\n      validIds\r\n    );\r\n\r\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\r\n    this.updateNotificationStatus(validIds, true);\r\n\r\n    // Créer une réponse optimiste\r\n    const optimisticResponse = {\r\n      markNotificationsAsRead: {\r\n        success: true,\r\n        readCount: validIds.length,\r\n        remainingCount: Math.max(\r\n          0,\r\n          this.notificationCount.value - validIds.length\r\n        ),\r\n      },\r\n    };\r\n\r\n    // Afficher des informations de débogage supplémentaires\r\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\r\n      notificationIds: validIds,\r\n    });\r\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\r\n\r\n    return this.apollo\r\n      .mutate<MarkNotificationsAsReadResponse>({\r\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\r\n        variables: { notificationIds: validIds },\r\n        optimisticResponse: optimisticResponse,\r\n        errorPolicy: 'all', // Continuer même en cas d'erreur\r\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          this.logger.debug('MessageService', 'Mutation result', result);\r\n          console.log('Mutation result:', result);\r\n\r\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\r\n          if (result.errors) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'GraphQL errors:',\r\n              result.errors\r\n            );\r\n            console.error('GraphQL errors:', result.errors);\r\n          }\r\n\r\n          // Utiliser la réponse du serveur ou notre réponse optimiste\r\n          const response =\r\n            result.data?.markNotificationsAsRead ??\r\n            optimisticResponse.markNotificationsAsRead;\r\n\r\n          return response;\r\n        }),\r\n        catchError((error: Error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error marking notifications as read:',\r\n            error\r\n          );\r\n          console.error('Error in markAsRead:', error);\r\n\r\n          // En cas d'erreur, retourner quand même un succès simulé\r\n          // puisque nous avons déjà mis à jour l'interface utilisateur\r\n          return of({\r\n            success: true,\r\n            readCount: validIds.length,\r\n            remainingCount: Math.max(\r\n              0,\r\n              this.notificationCount.value - validIds.length\r\n            ),\r\n          });\r\n        })\r\n      );\r\n  }\r\n  // --------------------------------------------------------------------------\r\n  // Section 3: Méthodes pour les Appels\r\n  // --------------------------------------------------------------------------\r\n\r\n  /**\r\n   * Initie un appel avec un autre utilisateur\r\n   * @param recipientId ID de l'utilisateur à appeler\r\n   * @param callType Type d'appel (audio, vidéo)\r\n   * @param conversationId ID de la conversation (optionnel)\r\n   * @param options Options d'appel (optionnel)\r\n   * @returns Observable avec les informations de l'appel\r\n   */\r\n  initiateCall(\r\n    recipientId: string,\r\n    callType: CallType,\r\n    conversationId?: string,\r\n    options?: CallOptions\r\n  ): Observable<Call> {\r\n    return this.setupMediaDevices(callType).pipe(\r\n      switchMap((stream) => {\r\n        this.localStream = stream;\r\n        this.localStream$.next(stream);\r\n\r\n        // Créer une connexion peer\r\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\r\n\r\n        // Ajouter les pistes audio/vidéo\r\n        stream.getTracks().forEach((track) => {\r\n          this.peerConnection!.addTrack(track, stream);\r\n        });\r\n\r\n        // Écouter les candidats ICE\r\n        this.peerConnection.onicecandidate = (event) => {\r\n          if (event.candidate) {\r\n            this.sendCallSignal(\r\n              this.generateCallId(),\r\n              'ice-candidate',\r\n              JSON.stringify(event.candidate)\r\n            );\r\n          }\r\n        };\r\n\r\n        // Écouter les pistes distantes\r\n        this.peerConnection.ontrack = (event) => {\r\n          if (!this.remoteStream) {\r\n            this.remoteStream = new MediaStream();\r\n            this.remoteStream$.next(this.remoteStream);\r\n          }\r\n          event.streams[0].getTracks().forEach((track) => {\r\n            this.remoteStream!.addTrack(track);\r\n          });\r\n        };\r\n\r\n        // Créer l'offre SDP\r\n        return from(this.peerConnection.createOffer()).pipe(\r\n          switchMap((offer) => {\r\n            return from(this.peerConnection!.setLocalDescription(offer)).pipe(\r\n              map(() => offer)\r\n            );\r\n          })\r\n        );\r\n      }),\r\n      switchMap((offer) => {\r\n        // Générer un ID d'appel unique\r\n        const callId = this.generateCallId();\r\n\r\n        // Envoyer l'offre au serveur\r\n        return this.apollo\r\n          .mutate<{ initiateCall: Call }>({\r\n            mutation: INITIATE_CALL_MUTATION,\r\n            variables: {\r\n              recipientId,\r\n              callType,\r\n              callId,\r\n              offer: JSON.stringify(offer),\r\n              conversationId,\r\n              options,\r\n            },\r\n          })\r\n          .pipe(\r\n            map((result) => {\r\n              const call = result.data?.initiateCall;\r\n              if (!call) {\r\n                throw new Error('Failed to initiate call');\r\n              }\r\n\r\n              // Mettre à jour l'état de l'appel actif\r\n              this.activeCall.next(call);\r\n\r\n              // S'abonner aux signaux d'appel\r\n              const signalSub = this.subscribeToCallSignals(\r\n                call.id\r\n              ).subscribe();\r\n              this.subscriptions.push(signalSub);\r\n\r\n              return call;\r\n            })\r\n          );\r\n      }),\r\n      catchError((error) => {\r\n        this.logger.error('Error initiating call', error);\r\n        this.cleanupCall();\r\n        return throwError(() => new Error('Failed to initiate call'));\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Accepte un appel entrant\r\n   * @param incomingCall Appel entrant à accepter\r\n   * @returns Observable avec les informations de l'appel\r\n   */\r\n  acceptCall(incomingCall: IncomingCall): Observable<Call> {\r\n    this.stop('ringtone');\r\n\r\n    return this.setupMediaDevices(incomingCall.type).pipe(\r\n      switchMap((stream) => {\r\n        this.localStream = stream;\r\n        this.localStream$.next(stream);\r\n\r\n        // Créer une connexion peer\r\n        this.peerConnection = new RTCPeerConnection(this.rtcConfig);\r\n\r\n        // Ajouter les pistes audio/vidéo\r\n        stream.getTracks().forEach((track) => {\r\n          this.peerConnection!.addTrack(track, stream);\r\n        });\r\n\r\n        // Écouter les candidats ICE\r\n        this.peerConnection.onicecandidate = (event) => {\r\n          if (event.candidate) {\r\n            this.sendCallSignal(\r\n              incomingCall.id,\r\n              'ice-candidate',\r\n              JSON.stringify(event.candidate)\r\n            );\r\n          }\r\n        };\r\n\r\n        // Écouter les pistes distantes\r\n        this.peerConnection.ontrack = (event) => {\r\n          if (!this.remoteStream) {\r\n            this.remoteStream = new MediaStream();\r\n            this.remoteStream$.next(this.remoteStream);\r\n          }\r\n          event.streams[0].getTracks().forEach((track) => {\r\n            this.remoteStream!.addTrack(track);\r\n          });\r\n        };\r\n\r\n        // Définir l'offre distante\r\n        const offer = JSON.parse(incomingCall.offer);\r\n        return from(\r\n          this.peerConnection.setRemoteDescription(\r\n            new RTCSessionDescription(offer)\r\n          )\r\n        ).pipe(\r\n          switchMap(() => from(this.peerConnection!.createAnswer())),\r\n          switchMap((answer) => {\r\n            return from(this.peerConnection!.setLocalDescription(answer)).pipe(\r\n              map(() => answer)\r\n            );\r\n          })\r\n        );\r\n      }),\r\n      switchMap((answer) => {\r\n        // Envoyer la réponse au serveur\r\n        return this.apollo\r\n          .mutate<{ acceptCall: Call }>({\r\n            mutation: ACCEPT_CALL_MUTATION,\r\n            variables: {\r\n              callId: incomingCall.id,\r\n              answer: JSON.stringify(answer),\r\n            },\r\n          })\r\n          .pipe(\r\n            map((result) => {\r\n              const call = result.data?.acceptCall;\r\n              if (!call) {\r\n                throw new Error('Failed to accept call');\r\n              }\r\n\r\n              // Jouer le son de connexion\r\n              this.play('call-connected');\r\n\r\n              // Mettre à jour l'état de l'appel actif\r\n              this.activeCall.next({\r\n                ...call,\r\n                caller: incomingCall.caller,\r\n                type: incomingCall.type,\r\n                conversationId: incomingCall.conversationId,\r\n              });\r\n\r\n              // S'abonner aux signaux d'appel\r\n              const signalSub = this.subscribeToCallSignals(\r\n                incomingCall.id\r\n              ).subscribe();\r\n              this.subscriptions.push(signalSub);\r\n\r\n              // Effacer l'appel entrant\r\n              this.incomingCall.next(null);\r\n\r\n              return call;\r\n            })\r\n          );\r\n      }),\r\n      catchError((error) => {\r\n        this.logger.error('Error accepting call', error);\r\n        this.cleanupCall();\r\n        return throwError(() => new Error('Failed to accept call'));\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Rejette un appel entrant\r\n   * @param callId ID de l'appel à rejeter\r\n   * @param reason Raison du rejet (optionnel)\r\n   * @returns Observable avec les informations de l'appel\r\n   */\r\n  rejectCall(callId: string, reason?: string): Observable<Call> {\r\n    this.stop('ringtone');\r\n\r\n    return this.apollo\r\n      .mutate<{ rejectCall: Call }>({\r\n        mutation: REJECT_CALL_MUTATION,\r\n        variables: {\r\n          callId,\r\n          reason,\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const call = result.data?.rejectCall;\r\n          if (!call) {\r\n            throw new Error('Failed to reject call');\r\n          }\r\n\r\n          // Effacer l'appel entrant\r\n          this.incomingCall.next(null);\r\n\r\n          return call;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error rejecting call', error);\r\n          return throwError(() => new Error('Failed to reject call'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Termine un appel en cours\r\n   * @param callId ID de l'appel à terminer\r\n   * @param feedback Commentaires sur l'appel (optionnel)\r\n   * @returns Observable avec les informations de l'appel\r\n   */\r\n  endCall(callId: string, feedback?: CallFeedback): Observable<Call> {\r\n    this.stop('ringtone');\r\n    this.play('call-end');\r\n\r\n    return this.apollo\r\n      .mutate<{ endCall: Call }>({\r\n        mutation: END_CALL_MUTATION,\r\n        variables: {\r\n          callId,\r\n          feedback,\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const call = result.data?.endCall;\r\n          if (!call) {\r\n            throw new Error('Failed to end call');\r\n          }\r\n\r\n          // Nettoyer les ressources\r\n          this.cleanupCall();\r\n\r\n          // Mettre à jour l'état de l'appel actif\r\n          this.activeCall.next(null);\r\n\r\n          return call;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error ending call', error);\r\n          this.cleanupCall();\r\n          return throwError(() => new Error('Failed to end call'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Active ou désactive la caméra ou le micro\r\n   * @param callId ID de l'appel\r\n   * @param video État de la caméra (optionnel)\r\n   * @param audio État du micro (optionnel)\r\n   * @returns Observable avec le résultat de l'opération\r\n   */\r\n  toggleMedia(\r\n    callId: string,\r\n    video?: boolean,\r\n    audio?: boolean\r\n  ): Observable<CallSuccess> {\r\n    if (this.localStream) {\r\n      // Mettre à jour les pistes locales\r\n      if (video !== undefined) {\r\n        this.localStream.getVideoTracks().forEach((track) => {\r\n          track.enabled = video;\r\n        });\r\n      }\r\n\r\n      if (audio !== undefined) {\r\n        this.localStream.getAudioTracks().forEach((track) => {\r\n          track.enabled = audio;\r\n        });\r\n      }\r\n    }\r\n\r\n    return this.apollo\r\n      .mutate<{ toggleCallMedia: CallSuccess }>({\r\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\r\n        variables: {\r\n          callId,\r\n          video,\r\n          audio,\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const success = result.data?.toggleCallMedia;\r\n          if (!success) {\r\n            throw new Error('Failed to toggle media');\r\n          }\r\n          return success;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error toggling media', error);\r\n          return throwError(() => new Error('Failed to toggle media'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * S'abonne aux signaux d'appel\r\n   * @param callId ID de l'appel\r\n   * @returns Observable avec les signaux d'appel\r\n   */\r\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\r\n    return this.apollo\r\n      .subscribe<{ callSignal: CallSignal }>({\r\n        query: CALL_SIGNAL_SUBSCRIPTION,\r\n        variables: { callId },\r\n      })\r\n      .pipe(\r\n        map(({ data }) => {\r\n          if (!data?.callSignal) {\r\n            throw new Error('No call signal received');\r\n          }\r\n          return data.callSignal;\r\n        }),\r\n        tap((signal) => {\r\n          this.callSignals.next(signal);\r\n          this.handleCallSignal(signal);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error in call signal subscription', error);\r\n          return throwError(() => new Error('Call signal subscription failed'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Envoie un signal d'appel\r\n   * @param callId ID de l'appel\r\n   * @param signalType Type de signal\r\n   * @param signalData Données du signal\r\n   * @returns Observable avec le résultat de l'opération\r\n   */\r\n  sendCallSignal(\r\n    callId: string,\r\n    signalType: string,\r\n    signalData: string\r\n  ): Observable<CallSuccess> {\r\n    return this.apollo\r\n      .mutate<{ sendCallSignal: CallSuccess }>({\r\n        mutation: SEND_CALL_SIGNAL_MUTATION,\r\n        variables: {\r\n          callId,\r\n          signalType,\r\n          signalData,\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const success = result.data?.sendCallSignal;\r\n          if (!success) {\r\n            throw new Error('Failed to send call signal');\r\n          }\r\n          return success;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Error sending call signal', error);\r\n          return throwError(() => new Error('Failed to send call signal'));\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Gère un signal d'appel reçu\r\n   * @param signal Signal d'appel\r\n   */\r\n  private handleCallSignal(signal: CallSignal): void {\r\n    switch (signal.type) {\r\n      case 'ice-candidate':\r\n        this.handleIceCandidate(signal);\r\n        break;\r\n      case 'answer':\r\n        this.handleAnswer(signal);\r\n        break;\r\n      case 'end-call':\r\n        this.handleEndCall(signal);\r\n        break;\r\n      case 'reject':\r\n        this.handleRejectCall(signal);\r\n        break;\r\n      default:\r\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère un candidat ICE reçu\r\n   * @param signal Signal d'appel contenant un candidat ICE\r\n   */\r\n  private handleIceCandidate(signal: CallSignal): void {\r\n    if (!this.peerConnection) {\r\n      this.logger.error('No peer connection available for ICE candidate');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const candidate = JSON.parse(signal.data);\r\n      this.peerConnection\r\n        .addIceCandidate(new RTCIceCandidate(candidate))\r\n        .catch((error) => {\r\n          this.logger.error('Error adding ICE candidate', error as Error);\r\n        });\r\n    } catch (error) {\r\n      this.logger.error('Error parsing ICE candidate', error as Error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère une réponse SDP reçue\r\n   * @param signal Signal d'appel contenant une réponse SDP\r\n   */\r\n  private handleAnswer(signal: CallSignal): void {\r\n    if (!this.peerConnection) {\r\n      this.logger.error('No peer connection available for answer');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const answer = JSON.parse(signal.data);\r\n      this.peerConnection\r\n        .setRemoteDescription(new RTCSessionDescription(answer))\r\n        .catch((error) => {\r\n          this.logger.error('Error setting remote description', error as Error);\r\n        });\r\n    } catch (error) {\r\n      this.logger.error('Error parsing answer', error as Error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère la fin d'un appel\r\n   * @param signal Signal d'appel indiquant la fin de l'appel\r\n   */\r\n  private handleEndCall(signal: CallSignal): void {\r\n    this.stop('ringtone');\r\n    this.cleanupCall();\r\n\r\n    // Mettre à jour l'état de l'appel actif\r\n    const currentCall = this.activeCall.value;\r\n    if (currentCall && currentCall.id === signal.callId) {\r\n      this.activeCall.next({\r\n        ...currentCall,\r\n        status: CallStatus.ENDED,\r\n        endTime: new Date().toISOString(),\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère le rejet d'un appel\r\n   * @param signal Signal d'appel indiquant le rejet de l'appel\r\n   */\r\n  private handleRejectCall(signal: CallSignal): void {\r\n    this.stop('ringtone');\r\n    this.cleanupCall();\r\n\r\n    // Mettre à jour l'état de l'appel actif\r\n    const currentCall = this.activeCall.value;\r\n    if (currentCall && currentCall.id === signal.callId) {\r\n      this.activeCall.next({\r\n        ...currentCall,\r\n        status: CallStatus.REJECTED,\r\n        endTime: new Date().toISOString(),\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Nettoie les ressources d'appel\r\n   */\r\n  private cleanupCall(): void {\r\n    if (this.localStream) {\r\n      this.localStream.getTracks().forEach((track) => track.stop());\r\n      this.localStream = null;\r\n      this.localStream$.next(null);\r\n    }\r\n\r\n    if (this.peerConnection) {\r\n      this.peerConnection.close();\r\n      this.peerConnection = null;\r\n    }\r\n\r\n    this.remoteStream = null;\r\n    this.remoteStream$.next(null);\r\n  }\r\n\r\n  /**\r\n   * Configure les périphériques média pour un appel\r\n   * @param callType Type d'appel (audio, vidéo)\r\n   * @returns Observable avec le flux média\r\n   */\r\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\r\n    const constraints: MediaStreamConstraints = {\r\n      audio: true,\r\n      video:\r\n        callType !== CallType.AUDIO\r\n          ? {\r\n              width: { ideal: 1280 },\r\n              height: { ideal: 720 },\r\n            }\r\n          : false,\r\n    };\r\n\r\n    return new Observable<MediaStream>((observer) => {\r\n      navigator.mediaDevices\r\n        .getUserMedia(constraints)\r\n        .then((stream) => {\r\n          observer.next(stream);\r\n          observer.complete();\r\n        })\r\n        .catch((error) => {\r\n          this.logger.error('Error accessing media devices', error);\r\n          observer.error(new Error('Failed to access media devices'));\r\n        });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Génère un ID d'appel unique\r\n   * @returns ID d'appel unique\r\n   */\r\n  private generateCallId(): string {\r\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\r\n  }\r\n\r\n  // --------------------------------------------------------------------------\r\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\r\n  // --------------------------------------------------------------------------\r\n  // User methods\r\n  getAllUsers(\r\n    forceRefresh = false,\r\n    search?: string,\r\n    page: number = 1,\r\n    limit: number = 10,\r\n    sortBy: string = 'username',\r\n    sortOrder: string = 'asc',\r\n    isOnline?: boolean\r\n  ): Observable<User[]> {\r\n    this.logger.info(\r\n      'MessageService',\r\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\r\n        search || '(empty)'\r\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\r\n    );\r\n\r\n    const now = Date.now();\r\n    const cacheValid =\r\n      !forceRefresh &&\r\n      this.usersCache.length > 0 &&\r\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\r\n      !search &&\r\n      page === 1 &&\r\n      limit >= this.usersCache.length;\r\n\r\n    // Use cache only for first page with no filters\r\n    if (cacheValid) {\r\n      this.logger.debug(\r\n        'MessageService',\r\n        `Using cached users (${this.usersCache.length} users)`\r\n      );\r\n      return of([...this.usersCache]);\r\n    }\r\n\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Fetching users from server with pagination, fetchPolicy=${\r\n        forceRefresh ? 'network-only' : 'cache-first'\r\n      }`\r\n    );\r\n\r\n    return this.apollo\r\n      .watchQuery<any>({\r\n        query: GET_ALL_USER_QUERY,\r\n        variables: {\r\n          search,\r\n          page,\r\n          limit,\r\n          sortBy,\r\n          sortOrder,\r\n          isOnline: isOnline !== undefined ? isOnline : null,\r\n        },\r\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          this.logger.debug(\r\n            'MessageService',\r\n            'Users response received',\r\n            result\r\n          );\r\n\r\n          if (result.errors) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'GraphQL errors in getAllUsers:',\r\n              result.errors\r\n            );\r\n            throw new Error(result.errors.map((e) => e.message).join(', '));\r\n          }\r\n\r\n          if (!result.data?.getAllUsers) {\r\n            this.logger.warn(\r\n              'MessageService',\r\n              'No users data received from server'\r\n            );\r\n            return [];\r\n          }\r\n\r\n          const paginatedResponse = result.data.getAllUsers;\r\n\r\n          // Log pagination metadata\r\n          this.logger.debug('MessageService', 'Pagination metadata:', {\r\n            totalCount: paginatedResponse.totalCount,\r\n            totalPages: paginatedResponse.totalPages,\r\n            currentPage: paginatedResponse.currentPage,\r\n            hasNextPage: paginatedResponse.hasNextPage,\r\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\r\n          });\r\n\r\n          // Normalize users with error handling\r\n          const users: User[] = [];\r\n          for (const user of paginatedResponse.users) {\r\n            try {\r\n              if (user) {\r\n                users.push(this.normalizeUser(user));\r\n              }\r\n            } catch (error) {\r\n              this.logger.warn(\r\n                'MessageService',\r\n                `Error normalizing user, skipping:`,\r\n                error\r\n              );\r\n            }\r\n          }\r\n\r\n          this.logger.info(\r\n            'MessageService',\r\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\r\n          );\r\n\r\n          // Update cache only for first page with no filters\r\n          if (!search && page === 1 && !isOnline) {\r\n            this.usersCache = [...users];\r\n            this.lastFetchTime = Date.now();\r\n            this.logger.debug(\r\n              'MessageService',\r\n              `User cache updated with ${users.length} users`\r\n            );\r\n          }\r\n\r\n          // Store pagination metadata in a property for component access\r\n          this.currentUserPagination = {\r\n            totalCount: paginatedResponse.totalCount,\r\n            totalPages: paginatedResponse.totalPages,\r\n            currentPage: paginatedResponse.currentPage,\r\n            hasNextPage: paginatedResponse.hasNextPage,\r\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\r\n          };\r\n\r\n          return users;\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('MessageService', 'Error fetching users:', error);\r\n\r\n          if (error.graphQLErrors) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'GraphQL errors:',\r\n              error.graphQLErrors\r\n            );\r\n          }\r\n\r\n          if (error.networkError) {\r\n            this.logger.error(\r\n              'MessageService',\r\n              'Network error:',\r\n              error.networkError\r\n            );\r\n          }\r\n\r\n          // Return cache if available (only for first page)\r\n          if (\r\n            this.usersCache.length > 0 &&\r\n            page === 1 &&\r\n            !search &&\r\n            !isOnline\r\n          ) {\r\n            this.logger.warn(\r\n              'MessageService',\r\n              `Returning ${this.usersCache.length} cached users due to fetch error`\r\n            );\r\n            return of([...this.usersCache]);\r\n          }\r\n\r\n          return throwError(\r\n            () =>\r\n              new Error(\r\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\r\n              )\r\n          );\r\n        })\r\n      );\r\n  }\r\n  getOneUser(userId: string): Observable<User> {\r\n    return this.apollo\r\n      .watchQuery<GetOneUserResponse>({\r\n        query: GET_USER_QUERY,\r\n        variables: { id: userId },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\r\n        catchError((error) => {\r\n          this.logger.error('MessageService', 'Error fetching user:', error);\r\n          return throwError(() => new Error('Failed to fetch user'));\r\n        })\r\n      );\r\n  }\r\n  getCurrentUser(): Observable<User> {\r\n    return this.apollo\r\n      .watchQuery<getCurrentUserResponse>({\r\n        query: GET_CURRENT_USER_QUERY,\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error fetching current user:',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to fetch current user'));\r\n        })\r\n      );\r\n  }\r\n  setUserOnline(userId: string): Observable<User> {\r\n    return this.apollo\r\n      .mutate<SetUserOnlineResponse>({\r\n        mutation: SET_USER_ONLINE_MUTATION,\r\n        variables: { userId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.setUserOnline)\r\n            throw new Error('Failed to set user online');\r\n          return this.normalizeUser(result.data.setUserOnline);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error setting user online:',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to set user online'));\r\n        })\r\n      );\r\n  }\r\n  setUserOffline(userId: string): Observable<User> {\r\n    return this.apollo\r\n      .mutate<SetUserOfflineResponse>({\r\n        mutation: SET_USER_OFFLINE_MUTATION,\r\n        variables: { userId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.setUserOffline)\r\n            throw new Error('Failed to set user offline');\r\n          return this.normalizeUser(result.data.setUserOffline);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error setting user offline:',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to set user offline'));\r\n        })\r\n      );\r\n  }\r\n\r\n  // Group methods\r\n  getGroup(groupId: string): Observable<Group> {\r\n    return this.apollo\r\n      .watchQuery<GetGroupResponse>({\r\n        query: GET_GROUP_QUERY,\r\n        variables: { id: groupId },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map((result) => {\r\n          const group = result.data?.getGroup;\r\n          if (!group) throw new Error('Group not found');\r\n\r\n          return {\r\n            ...group,\r\n            participants:\r\n              group.participants?.map((p) => this.normalizeUser(p)) || [],\r\n            admins: group.admins?.map((a) => this.normalizeUser(a)) || [],\r\n            createdAt: new Date(),\r\n            updatedAt: new Date(),\r\n          };\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('MessageService', 'Error fetching group:', error);\r\n          return throwError(() => new Error('Failed to fetch group'));\r\n        })\r\n      );\r\n  }\r\n  getUserGroups(userId: string): Observable<Group[]> {\r\n    return this.apollo\r\n      .watchQuery<GetUserGroupsResponse>({\r\n        query: GET_USER_GROUPS_QUERY,\r\n        variables: { userId },\r\n        fetchPolicy: 'network-only',\r\n      })\r\n      .valueChanges.pipe(\r\n        map(\r\n          (result) =>\r\n            result.data?.getUserGroups?.map((group) => ({\r\n              ...group,\r\n              participants:\r\n                group.participants?.map((p) => this.normalizeUser(p)) || [],\r\n              admins: group.admins?.map((a) => this.normalizeUser(a)) || [],\r\n              createdAt: new Date(),\r\n              updatedAt: new Date(),\r\n            })) || []\r\n        ),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error fetching user groups:',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to fetch user groups'));\r\n        })\r\n      );\r\n  }\r\n  createGroup(\r\n    name: string,\r\n    participantIds: string[],\r\n    photo?: File,\r\n    description?: string\r\n  ): Observable<Group> {\r\n    const variables = photo\r\n      ? { name, participantIds, photo, description }\r\n      : { name, participantIds, description };\r\n    const context = photo ? { useMultipart: true, file: photo } : undefined;\r\n\r\n    return this.apollo\r\n      .mutate<CreateGroupResponse>({\r\n        mutation: CREATE_GROUP_MUTATION,\r\n        variables,\r\n        context,\r\n        refetchQueries: [\r\n          {\r\n            query: GET_USER_GROUPS_QUERY,\r\n            variables: { userId: this.getCurrentUserId() },\r\n          },\r\n        ],\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.createGroup)\r\n            throw new Error('Failed to create group');\r\n          return {\r\n            ...result.data.createGroup,\r\n            participants:\r\n              result.data.createGroup.participants?.map((p) =>\r\n                this.normalizeUser(p)\r\n              ) || [],\r\n            admins:\r\n              result.data.createGroup.admins?.map((a) =>\r\n                this.normalizeUser(a)\r\n              ) || [],\r\n          };\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('MessageService', 'Error creating group:', error);\r\n          return throwError(() => new Error('Failed to create group'));\r\n        })\r\n      );\r\n  }\r\n  updateGroup(\r\n    groupId: string,\r\n    input: {\r\n      name?: string;\r\n      photo?: File;\r\n      description?: string;\r\n      addParticipants?: string[];\r\n      removeParticipants?: string[];\r\n      addAdmins?: string[];\r\n      removeAdmins?: string[];\r\n    }\r\n  ): Observable<Group> {\r\n    const context = input.photo\r\n      ? { useMultipart: true, file: input.photo }\r\n      : undefined;\r\n    const { photo, ...inputWithoutPhoto } = input;\r\n\r\n    return this.apollo\r\n      .mutate<UpdateGroupResponse>({\r\n        mutation: UPDATE_GROUP_MUTATION,\r\n        variables: { id: groupId, input: inputWithoutPhoto },\r\n        context,\r\n        refetchQueries: [\r\n          { query: GET_GROUP_QUERY, variables: { id: groupId } },\r\n          {\r\n            query: GET_USER_GROUPS_QUERY,\r\n            variables: { userId: this.getCurrentUserId() },\r\n          },\r\n        ],\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          if (!result.data?.updateGroup)\r\n            throw new Error('Failed to update group');\r\n          return {\r\n            ...result.data.updateGroup,\r\n            participants:\r\n              result.data.updateGroup.participants?.map((p) =>\r\n                this.normalizeUser(p)\r\n              ) || [],\r\n            admins:\r\n              result.data.updateGroup.admins?.map((a) =>\r\n                this.normalizeUser(a)\r\n              ) || [],\r\n          };\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('MessageService', 'Error updating group:', error);\r\n          return throwError(() => new Error('Failed to update group'));\r\n        })\r\n      );\r\n  }\r\n\r\n  // --------------------------------------------------------------------------\r\n  // Section 4: Subscriptions et Gestion Temps Réel\r\n  // --------------------------------------------------------------------------\r\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\r\n    // Vérifier si l'utilisateur est connecté avec un token valide\r\n    if (!this.isTokenValid()) {\r\n      this.logger.warn(\r\n        \"Tentative d'abonnement aux messages avec un token invalide ou expiré\"\r\n      );\r\n      return of(null as unknown as Message);\r\n    }\r\n\r\n    this.logger.debug(\r\n      `Démarrage de l'abonnement aux nouveaux messages pour la conversation: ${conversationId}`\r\n    );\r\n\r\n    const sub$ = this.apollo\r\n      .subscribe<{ messageSent: Message }>({\r\n        query: MESSAGE_SENT_SUBSCRIPTION,\r\n        variables: { conversationId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const msg = result.data?.messageSent;\r\n          if (!msg) {\r\n            this.logger.warn('No message payload received');\r\n            throw new Error('No message payload received');\r\n          }\r\n\r\n          // Vérifier que l'ID est présent\r\n          if (!msg.id && !msg._id) {\r\n            this.logger.warn('Message without ID received:', msg);\r\n            // Générer un ID temporaire si nécessaire\r\n            msg.id = `temp-${Date.now()}`;\r\n          }\r\n\r\n          try {\r\n            // Utiliser normalizeMessage pour une normalisation complète\r\n            const normalizedMessage = this.normalizeMessage(msg);\r\n\r\n            // Si c'est un message vocal, s'assurer qu'il est correctement traité\r\n            if (\r\n              normalizedMessage.type === MessageType.AUDIO ||\r\n              (normalizedMessage.attachments &&\r\n                normalizedMessage.attachments.some(\r\n                  (att) => att.type === 'audio'\r\n                ))\r\n            ) {\r\n              this.logger.debug(\r\n                'MessageService',\r\n                'Voice message received in real-time',\r\n                normalizedMessage\r\n              );\r\n\r\n              // Mettre à jour la conversation avec le nouveau message\r\n              this.updateConversationWithNewMessage(\r\n                conversationId,\r\n                normalizedMessage\r\n              );\r\n            }\r\n\r\n            return normalizedMessage;\r\n          } catch (err) {\r\n            this.logger.error('Error normalizing message:', err);\r\n\r\n            // Créer un message minimal mais valide\r\n            const minimalMessage: Message = {\r\n              id: msg.id || msg._id || `temp-${Date.now()}`,\r\n              content: msg.content || '',\r\n              type: msg.type || MessageType.TEXT,\r\n              timestamp: this.safeDate(msg.timestamp),\r\n              isRead: false,\r\n              sender: msg.sender\r\n                ? this.normalizeUser(msg.sender)\r\n                : {\r\n                    id: this.getCurrentUserId(),\r\n                    username: 'Unknown',\r\n                  },\r\n            };\r\n\r\n            return minimalMessage;\r\n          }\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Message subscription error:',\r\n            error\r\n          );\r\n          // Retourner un observable vide au lieu de null\r\n          return EMPTY;\r\n        }),\r\n        // Filtrer les valeurs null\r\n        filter((message) => !!message),\r\n        // Réessayer après un délai en cas d'erreur\r\n        retry(3)\r\n      );\r\n\r\n    const sub = sub$.subscribe({\r\n      next: (message) => {\r\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\r\n        this.logger.debug('MessageService', 'New message received:', message);\r\n\r\n        // Mettre à jour la conversation avec le nouveau message\r\n        this.updateConversationWithNewMessage(conversationId, message);\r\n      },\r\n      error: (err) => {\r\n        this.logger.error('Error in message subscription:', err);\r\n      },\r\n    });\r\n\r\n    this.subscriptions.push(sub);\r\n    return sub$;\r\n  }\r\n\r\n  /**\r\n   * Met à jour une conversation avec un nouveau message\r\n   * @param conversationId ID de la conversation\r\n   * @param message Nouveau message\r\n   */\r\n  private updateConversationWithNewMessage(\r\n    conversationId: string,\r\n    message: Message\r\n  ): void {\r\n    // Forcer une mise à jour de la conversation en récupérant les données à jour\r\n    this.getConversation(conversationId).subscribe({\r\n      next: (conversation) => {\r\n        this.logger.debug(\r\n          'MessageService',\r\n          `Conversation ${conversationId} refreshed with new message ${\r\n            message.id\r\n          }, has ${conversation?.messages?.length || 0} messages`\r\n        );\r\n\r\n        // Émettre un événement pour informer les composants que la conversation a été mise à jour\r\n        this.activeConversation.next(conversationId);\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageService',\r\n          `Error refreshing conversation ${conversationId}:`,\r\n          error\r\n        );\r\n      },\r\n    });\r\n  }\r\n  subscribeToUserStatus(): Observable<User> {\r\n    // Vérifier si l'utilisateur est connecté avec un token valide\r\n    if (!this.isTokenValid()) {\r\n      this.logger.warn(\r\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\r\n      );\r\n      return throwError(() => new Error('Invalid or expired token'));\r\n    }\r\n\r\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\r\n\r\n    const sub$ = this.apollo\r\n      .subscribe<{ userStatusChanged: User }>({\r\n        query: USER_STATUS_SUBSCRIPTION,\r\n      })\r\n      .pipe(\r\n        tap((result) =>\r\n          this.logger.debug(\r\n            \"Données reçues de l'abonnement au statut utilisateur:\",\r\n            result\r\n          )\r\n        ),\r\n        map((result) => {\r\n          const user = result.data?.userStatusChanged;\r\n          if (!user) {\r\n            this.logger.error('No status payload received');\r\n            throw new Error('No status payload received');\r\n          }\r\n          return this.normalizeUser(user);\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error('Status subscription error:', error as Error);\r\n          return throwError(() => new Error('Status subscription failed'));\r\n        }),\r\n        retry(3) // Réessayer 3 fois en cas d'erreur\r\n      );\r\n\r\n    const sub = sub$.subscribe();\r\n    this.subscriptions.push(sub);\r\n    return sub$;\r\n  }\r\n  subscribeToConversationUpdates(\r\n    conversationId: string\r\n  ): Observable<Conversation> {\r\n    const sub$ = this.apollo\r\n      .subscribe<{ conversationUpdated: Conversation }>({\r\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\r\n        variables: { conversationId },\r\n      })\r\n      .pipe(\r\n        map((result) => {\r\n          const conv = result.data?.conversationUpdated;\r\n          if (!conv) throw new Error('No conversation payload received');\r\n\r\n          const normalizedConversation: Conversation = {\r\n            ...conv,\r\n            participants:\r\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\r\n            lastMessage: conv.lastMessage\r\n              ? {\r\n                  ...conv.lastMessage,\r\n                  sender: this.normalizeUser(conv.lastMessage.sender),\r\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\r\n                  readAt: conv.lastMessage.readAt\r\n                    ? this.safeDate(conv.lastMessage.readAt)\r\n                    : undefined,\r\n                  // Conservez toutes les autres propriétés du message\r\n                  id: conv.lastMessage.id,\r\n                  content: conv.lastMessage.content,\r\n                  type: conv.lastMessage.type,\r\n                  isRead: conv.lastMessage.isRead,\r\n                  // ... autres propriétés nécessaires\r\n                }\r\n              : null, // On conserve null comme dans votre version originale\r\n          };\r\n\r\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\r\n        }),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Conversation subscription error:',\r\n            error\r\n          );\r\n          return throwError(\r\n            () => new Error('Conversation subscription failed')\r\n          );\r\n        })\r\n      );\r\n\r\n    const sub = sub$.subscribe();\r\n    this.subscriptions.push(sub);\r\n    return sub$;\r\n  }\r\n  subscribeToTypingIndicator(\r\n    conversationId: string\r\n  ): Observable<TypingIndicatorEvent> {\r\n    const sub$ = this.apollo\r\n      .subscribe<TypingIndicatorEvents>({\r\n        query: TYPING_INDICATOR_SUBSCRIPTION,\r\n        variables: { conversationId },\r\n      })\r\n      .pipe(\r\n        map((result) => result.data?.typingIndicator),\r\n        filter(Boolean),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Typing indicator subscription error:',\r\n            error\r\n          );\r\n          return throwError(\r\n            () => new Error('Typing indicator subscription failed')\r\n          );\r\n        })\r\n      );\r\n\r\n    const sub = sub$.subscribe();\r\n    this.subscriptions.push(sub);\r\n    return sub$;\r\n  }\r\n  private isTokenValid(): boolean {\r\n    const token = localStorage.getItem('token');\r\n    if (!token) {\r\n      this.logger.warn('Aucun token trouvé');\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      // Décoder le token JWT (format: header.payload.signature)\r\n      const parts = token.split('.');\r\n      if (parts.length !== 3) {\r\n        this.logger.warn('Format de token invalide');\r\n        return false;\r\n      }\r\n\r\n      // Décoder le payload (deuxième partie du token)\r\n      const payload = JSON.parse(atob(parts[1]));\r\n\r\n      // Vérifier l'expiration\r\n      if (!payload.exp) {\r\n        this.logger.warn(\"Token sans date d'expiration\");\r\n        return false;\r\n      }\r\n\r\n      const expirationDate = new Date(payload.exp * 1000);\r\n      const now = new Date();\r\n\r\n      if (expirationDate < now) {\r\n        this.logger.warn('Token expiré', {\r\n          expiration: expirationDate.toISOString(),\r\n          now: now.toISOString(),\r\n        });\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'Erreur lors de la vérification du token:',\r\n        error as Error\r\n      );\r\n      return false;\r\n    }\r\n  }\r\n\r\n  subscribeToNotificationsRead(): Observable<string[]> {\r\n    // Vérifier si l'utilisateur est connecté avec un token valide\r\n    if (!this.isTokenValid()) {\r\n      this.logger.warn(\r\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\r\n      );\r\n      return of([]);\r\n    }\r\n\r\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\r\n\r\n    const sub$ = this.apollo\r\n      .subscribe<NotificationsReadEvent>({\r\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\r\n      })\r\n      .pipe(\r\n        tap((result) =>\r\n          this.logger.debug(\r\n            \"Données reçues de l'abonnement aux notifications lues:\",\r\n            result\r\n          )\r\n        ),\r\n        map((result) => {\r\n          const notificationIds = result.data?.notificationsRead || [];\r\n          this.logger.debug(\r\n            'Notifications marquées comme lues:',\r\n            notificationIds\r\n          );\r\n          this.updateNotificationStatus(notificationIds, true);\r\n          return notificationIds;\r\n        }),\r\n        catchError((err) => {\r\n          this.logger.error(\r\n            'Notifications read subscription error:',\r\n            err as Error\r\n          );\r\n          // Retourner un tableau vide au lieu de propager l'erreur\r\n          return of([]);\r\n        }),\r\n        // Réessayer après un délai en cas d'erreur\r\n        retry(3) // Réessayer 3 fois en cas d'erreur\r\n      );\r\n\r\n    const sub = sub$.subscribe();\r\n    this.subscriptions.push(sub);\r\n    return sub$;\r\n  }\r\n  subscribeToNewNotifications(): Observable<Notification> {\r\n    // Vérifier si l'utilisateur est connecté\r\n    const token = localStorage.getItem('token');\r\n    if (!token) {\r\n      this.logger.warn(\r\n        \"Tentative d'abonnement aux notifications sans être connecté\"\r\n      );\r\n      // Créer un Observable vide plutôt que de retourner null\r\n      return EMPTY;\r\n    }\r\n\r\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\r\n      query: NOTIFICATION_SUBSCRIPTION,\r\n    });\r\n\r\n    const processed$ = source$.pipe(\r\n      map((result) => {\r\n        const notification = result.data?.notificationReceived;\r\n        if (!notification) {\r\n          throw new Error('No notification payload received');\r\n        }\r\n\r\n        const normalized = this.normalizeNotification(notification);\r\n\r\n        // Vérifier si cette notification existe déjà dans le cache\r\n        if (this.notificationCache.has(normalized.id)) {\r\n          this.logger.debug(\r\n            'MessageService',\r\n            `Notification ${normalized.id} already exists in cache, skipping`\r\n          );\r\n          // Utiliser une technique différente pour ignorer cette notification\r\n          throw new Error('Notification already exists in cache');\r\n        }\r\n\r\n        // Jouer le son de notification\r\n        this.playNotificationSound();\r\n\r\n        // Mettre à jour le cache et émettre immédiatement la nouvelle notification\r\n        this.updateNotificationCache(normalized);\r\n\r\n        this.logger.debug(\r\n          'MessageService',\r\n          'New notification received and processed',\r\n          normalized\r\n        );\r\n\r\n        return normalized;\r\n      }),\r\n      // Utiliser catchError pour gérer les erreurs spécifiques\r\n      catchError((err) => {\r\n        // Si c'est l'erreur spécifique pour les notifications déjà existantes, on ignore silencieusement\r\n        if (\r\n          err instanceof Error &&\r\n          err.message === 'Notification already exists in cache'\r\n        ) {\r\n          return EMPTY;\r\n        }\r\n\r\n        this.logger.error('New notification subscription error:', err as Error);\r\n        // Retourner un Observable vide au lieu de null\r\n        return EMPTY;\r\n      })\r\n    );\r\n\r\n    const sub = processed$.subscribe({\r\n      next: (notification) => {\r\n        this.logger.debug(\r\n          'MessageService',\r\n          'Notification subscription next handler',\r\n          notification\r\n        );\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessageService',\r\n          'Error in notification subscription',\r\n          error\r\n        );\r\n      },\r\n    });\r\n\r\n    this.subscriptions.push(sub);\r\n    return processed$;\r\n  }\r\n  // --------------------------------------------------------------------------\r\n  // Helpers et Utilitaires\r\n  // --------------------------------------------------------------------------\r\n\r\n  private startCleanupInterval(): void {\r\n    this.cleanupInterval = setInterval(() => {\r\n      this.cleanupExpiredNotifications();\r\n    }, 3600000);\r\n  }\r\n  private cleanupExpiredNotifications(): void {\r\n    const now = new Date();\r\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\r\n\r\n    let expiredCount = 0;\r\n\r\n    this.notificationCache.forEach((notification, id) => {\r\n      const notificationDate = new Date(notification.timestamp);\r\n      if (notificationDate < thirtyDaysAgo) {\r\n        this.notificationCache.delete(id);\r\n        expiredCount++;\r\n      }\r\n    });\r\n\r\n    if (expiredCount > 0) {\r\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\r\n      this.notifications.next(Array.from(this.notificationCache.values()));\r\n      this.updateUnreadCount();\r\n    }\r\n  }\r\n  private getCurrentUserId(): string {\r\n    return localStorage.getItem('userId') || '';\r\n  }\r\n  private normalizeMessage(message: Message): Message {\r\n    if (!message) {\r\n      this.logger.error(\r\n        '[MessageService] Cannot normalize null or undefined message'\r\n      );\r\n      throw new Error('Message object is required');\r\n    }\r\n\r\n    try {\r\n      // Vérification des champs obligatoires\r\n      if (!message.id && !message._id) {\r\n        this.logger.error(\r\n          '[MessageService] Message ID is missing',\r\n          undefined,\r\n          message\r\n        );\r\n        throw new Error('Message ID is required');\r\n      }\r\n\r\n      // Normaliser le sender avec gestion d'erreur\r\n      let normalizedSender;\r\n      try {\r\n        normalizedSender = message.sender\r\n          ? this.normalizeUser(message.sender)\r\n          : undefined;\r\n      } catch (error) {\r\n        this.logger.warn(\r\n          '[MessageService] Error normalizing message sender, using default values',\r\n          error\r\n        );\r\n        normalizedSender = {\r\n          _id: message.senderId || 'unknown',\r\n          id: message.senderId || 'unknown',\r\n          username: 'Unknown User',\r\n          email: '<EMAIL>',\r\n          role: 'user',\r\n          isActive: true,\r\n        };\r\n      }\r\n\r\n      // Normaliser le receiver si présent\r\n      let normalizedReceiver;\r\n      if (message.receiver) {\r\n        try {\r\n          normalizedReceiver = this.normalizeUser(message.receiver);\r\n        } catch (error) {\r\n          this.logger.warn(\r\n            '[MessageService] Error normalizing message receiver, using default values',\r\n            error\r\n          );\r\n          normalizedReceiver = {\r\n            _id: message.receiverId || 'unknown',\r\n            id: message.receiverId || 'unknown',\r\n            username: 'Unknown User',\r\n            email: '<EMAIL>',\r\n            role: 'user',\r\n            isActive: true,\r\n          };\r\n        }\r\n      }\r\n\r\n      // Normaliser les pièces jointes si présentes\r\n      const normalizedAttachments =\r\n        message.attachments?.map((att) => ({\r\n          id: att.id || att._id || `attachment-${Date.now()}`,\r\n          url: att.url || '',\r\n          type: att.type || 'unknown',\r\n          name: att.name || 'attachment',\r\n          size: att.size || 0,\r\n          duration: att.duration || 0,\r\n        })) || [];\r\n\r\n      // Construire le message normalisé\r\n      const normalizedMessage = {\r\n        ...message,\r\n        _id: message.id || message._id,\r\n        id: message.id || message._id,\r\n        content: message.content || '',\r\n        sender: normalizedSender,\r\n        timestamp: this.normalizeDate(message.timestamp),\r\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\r\n        attachments: normalizedAttachments,\r\n        metadata: message.metadata || null,\r\n      };\r\n\r\n      // Ajouter le receiver seulement s'il existe\r\n      if (normalizedReceiver) {\r\n        normalizedMessage.receiver = normalizedReceiver;\r\n      }\r\n\r\n      this.logger.debug('[MessageService] Message normalized successfully', {\r\n        messageId: normalizedMessage.id,\r\n        senderId: normalizedMessage.sender?.id,\r\n      });\r\n\r\n      return normalizedMessage;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        '[MessageService] Error normalizing message:',\r\n        error instanceof Error ? error : new Error(String(error)),\r\n        message\r\n      );\r\n      throw new Error(\r\n        `Failed to normalize message: ${\r\n          error instanceof Error ? error.message : String(error)\r\n        }`\r\n      );\r\n    }\r\n  }\r\n  private normalizeNotMessage(message: any) {\r\n    return {\r\n      ...message,\r\n      ...(message.attachments && {\r\n        attachments: message.attachments.map((att: any) => ({\r\n          url: att.url,\r\n          type: att.type,\r\n          ...(att.name && { name: att.name }),\r\n          ...(att.size && { size: att.size }),\r\n        })),\r\n      }),\r\n    };\r\n  }\r\n  public normalizeUser(user: any): User {\r\n    if (!user) {\r\n      throw new Error('User object is required');\r\n    }\r\n\r\n    // Vérification des champs obligatoires avec valeurs par défaut\r\n    const userId = user.id || user._id;\r\n    if (!userId) {\r\n      throw new Error('User ID is required');\r\n    }\r\n\r\n    // Utiliser des valeurs par défaut pour les champs manquants\r\n    const username = user.username || 'Unknown User';\r\n    const email = user.email || `user-${userId}@example.com`;\r\n    const isActive =\r\n      user.isActive !== undefined && user.isActive !== null\r\n        ? user.isActive\r\n        : true;\r\n    const role = user.role || 'user';\r\n\r\n    // Construire l'objet utilisateur normalisé\r\n    return {\r\n      _id: userId,\r\n      id: userId,\r\n      username: username,\r\n      email: email,\r\n      role: role,\r\n      isActive: isActive,\r\n      // Champs optionnels\r\n      image: user.image ?? null,\r\n      bio: user.bio,\r\n      isOnline: user.isOnline || false,\r\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\r\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\r\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\r\n      followingCount: user.followingCount,\r\n      followersCount: user.followersCount,\r\n      postCount: user.postCount,\r\n    };\r\n  }\r\n  private normalizeConversation(conv: Conversation): Conversation {\r\n    if (!conv) {\r\n      this.logger.error(\r\n        '[MessageService] Cannot normalize null or undefined conversation'\r\n      );\r\n      throw new Error('Conversation object is required');\r\n    }\r\n\r\n    try {\r\n      // Vérification des champs obligatoires\r\n      if (!conv.id && !conv._id) {\r\n        this.logger.error(\r\n          '[MessageService] Conversation ID is missing',\r\n          undefined,\r\n          conv\r\n        );\r\n        throw new Error('Conversation ID is required');\r\n      }\r\n\r\n      // Normaliser les participants avec gestion d'erreur\r\n      const normalizedParticipants = [];\r\n      if (conv.participants && Array.isArray(conv.participants)) {\r\n        for (const participant of conv.participants) {\r\n          try {\r\n            if (participant) {\r\n              normalizedParticipants.push(this.normalizeUser(participant));\r\n            }\r\n          } catch (error) {\r\n            this.logger.warn(\r\n              '[MessageService] Error normalizing participant, skipping',\r\n              error\r\n            );\r\n          }\r\n        }\r\n      } else {\r\n        this.logger.warn(\r\n          '[MessageService] Conversation has no participants or invalid participants array',\r\n          conv\r\n        );\r\n      }\r\n\r\n      // Normaliser les messages avec gestion d'erreur\r\n      const normalizedMessages = [];\r\n      if (conv.messages && Array.isArray(conv.messages)) {\r\n        this.logger.debug('[MessageService] Processing conversation messages', {\r\n          count: conv.messages.length,\r\n        });\r\n\r\n        for (const message of conv.messages) {\r\n          try {\r\n            if (message) {\r\n              const normalizedMessage = this.normalizeMessage(message);\r\n              this.logger.debug(\r\n                '[MessageService] Successfully normalized message',\r\n                {\r\n                  messageId: normalizedMessage.id,\r\n                  content: normalizedMessage.content?.substring(0, 20),\r\n                  sender: normalizedMessage.sender?.username,\r\n                }\r\n              );\r\n              normalizedMessages.push(normalizedMessage);\r\n            }\r\n          } catch (error) {\r\n            this.logger.warn(\r\n              '[MessageService] Error normalizing message in conversation, skipping',\r\n              error\r\n            );\r\n          }\r\n        }\r\n      } else {\r\n        this.logger.debug(\r\n          '[MessageService] No messages found in conversation or invalid messages array'\r\n        );\r\n      }\r\n\r\n      // Normaliser le dernier message avec gestion d'erreur\r\n      let normalizedLastMessage = null;\r\n      if (conv.lastMessage) {\r\n        try {\r\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\r\n        } catch (error) {\r\n          this.logger.warn(\r\n            '[MessageService] Error normalizing last message, using null',\r\n            error\r\n          );\r\n        }\r\n      }\r\n\r\n      // Construire la conversation normalisée\r\n      const normalizedConversation = {\r\n        ...conv,\r\n        _id: conv.id || conv._id,\r\n        id: conv.id || conv._id,\r\n        participants: normalizedParticipants,\r\n        messages: normalizedMessages,\r\n        lastMessage: normalizedLastMessage,\r\n        unreadCount: conv.unreadCount || 0,\r\n        isGroup: !!conv.isGroup,\r\n        createdAt: this.normalizeDate(conv.createdAt),\r\n        updatedAt: this.normalizeDate(conv.updatedAt),\r\n      };\r\n\r\n      this.logger.debug(\r\n        '[MessageService] Conversation normalized successfully',\r\n        {\r\n          conversationId: normalizedConversation.id,\r\n          participantCount: normalizedParticipants.length,\r\n          messageCount: normalizedMessages.length,\r\n        }\r\n      );\r\n\r\n      return normalizedConversation;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        '[MessageService] Error normalizing conversation:',\r\n        error instanceof Error ? error : new Error(String(error)),\r\n        conv\r\n      );\r\n      throw new Error(\r\n        `Failed to normalize conversation: ${\r\n          error instanceof Error ? error.message : String(error)\r\n        }`\r\n      );\r\n    }\r\n  }\r\n  private normalizeDate(date: string | Date | undefined): Date {\r\n    if (!date) return new Date();\r\n    try {\r\n      return typeof date === 'string' ? new Date(date) : date;\r\n    } catch (error) {\r\n      this.logger.warn(`Failed to parse date: ${date}`, error);\r\n      return new Date();\r\n    }\r\n  }\r\n\r\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\r\n  private safeDate(date: string | Date | undefined): Date {\r\n    if (!date) return new Date();\r\n    try {\r\n      return typeof date === 'string' ? new Date(date) : date;\r\n    } catch (error) {\r\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\r\n      return new Date();\r\n    }\r\n  }\r\n  private toSafeISOString = (\r\n    date: Date | string | undefined\r\n  ): string | undefined => {\r\n    if (!date) return undefined;\r\n    return typeof date === 'string' ? date : date.toISOString();\r\n  };\r\n  private normalizeNotification(notification: Notification): Notification {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      'Normalizing notification',\r\n      notification\r\n    );\r\n\r\n    if (!notification) {\r\n      this.logger.error('MessageService', 'Notification is null or undefined');\r\n      throw new Error('Notification is required');\r\n    }\r\n\r\n    // Vérifier et normaliser l'ID\r\n    const notificationId = notification.id || (notification as any)._id;\r\n    if (!notificationId) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        'Notification ID is missing',\r\n        notification\r\n      );\r\n      throw new Error('Notification ID is required');\r\n    }\r\n\r\n    if (!notification.timestamp) {\r\n      this.logger.warn(\r\n        'MessageService',\r\n        'Notification timestamp is missing, using current time',\r\n        notification\r\n      );\r\n      notification.timestamp = new Date();\r\n    }\r\n\r\n    try {\r\n      const normalized = {\r\n        ...notification,\r\n        _id: notificationId, // Conserver l'ID MongoDB original\r\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\r\n        timestamp: new Date(notification.timestamp),\r\n        ...(notification.senderId && {\r\n          senderId: this.normalizeSender(notification.senderId),\r\n        }),\r\n        ...(notification.message && {\r\n          message: this.normalizeNotMessage(notification.message),\r\n        }),\r\n      };\r\n\r\n      this.logger.debug(\r\n        'MessageService',\r\n        'Normalized notification result',\r\n        normalized\r\n      );\r\n      return normalized;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        'MessageService',\r\n        'Error in normalizeNotification',\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n  private normalizeSender(sender: any) {\r\n    return {\r\n      id: sender.id,\r\n      username: sender.username,\r\n      ...(sender.image && { image: sender.image }),\r\n    };\r\n  }\r\n  private updateCache(notifications: Notification[]) {\r\n    this.logger.debug(\r\n      'MessageService',\r\n      `Updating notification cache with ${notifications.length} notifications`\r\n    );\r\n\r\n    if (notifications.length === 0) {\r\n      this.logger.warn('MessageService', 'No notifications to update in cache');\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      `Starting to update cache with ${notifications.length} notifications`\r\n    );\r\n\r\n    // Vérifier si les notifications ont des IDs valides\r\n    const validNotifications = notifications.filter(\r\n      (notif) => notif && (notif.id || (notif as any)._id)\r\n    );\r\n\r\n    if (validNotifications.length !== notifications.length) {\r\n      console.warn(\r\n        `Found ${\r\n          notifications.length - validNotifications.length\r\n        } notifications without valid IDs`\r\n      );\r\n    }\r\n\r\n    // Traiter chaque notification\r\n    validNotifications.forEach((notif, index) => {\r\n      try {\r\n        // S'assurer que la notification a un ID\r\n        const notifId = notif.id || (notif as any)._id;\r\n        if (!notifId) {\r\n          console.error('Notification without ID:', notif);\r\n          return;\r\n        }\r\n\r\n        // Normaliser la notification\r\n        const normalized = this.normalizeNotification(notif);\r\n\r\n        // Vérifier si cette notification existe déjà dans le cache\r\n        if (this.notificationCache.has(normalized.id)) {\r\n          console.log(\r\n            `Notification ${normalized.id} already exists in cache, skipping`\r\n          );\r\n          return;\r\n        }\r\n\r\n        // Ajouter au cache\r\n        this.notificationCache.set(normalized.id, normalized);\r\n\r\n        console.log(`Added notification ${normalized.id} to cache`);\r\n      } catch (error) {\r\n        console.error(`Error processing notification ${index + 1}:`, error);\r\n        console.error('Problematic notification:', notif);\r\n      }\r\n    });\r\n\r\n    console.log(\r\n      `Notification cache updated, now contains ${this.notificationCache.size} notifications`\r\n    );\r\n\r\n    // Sauvegarder les notifications dans le localStorage après la mise à jour du cache\r\n    this.saveNotificationsToLocalStorage();\r\n  }\r\n  private updateUnreadCount() {\r\n    const count = Array.from(this.notificationCache.values()).filter(\r\n      (n) => !n.isRead\r\n    ).length;\r\n    this.notificationCount.next(count);\r\n  }\r\n  private updateNotificationCache(notification: Notification): void {\r\n    // Vérifier si la notification existe déjà dans le cache (pour éviter les doublons)\r\n    if (!this.notificationCache.has(notification.id)) {\r\n      this.notificationCache.set(notification.id, notification);\r\n      this.notifications.next(Array.from(this.notificationCache.values()));\r\n      this.updateUnreadCount();\r\n      // Sauvegarder les notifications dans le localStorage après chaque mise à jour\r\n      this.saveNotificationsToLocalStorage();\r\n    } else {\r\n      this.logger.debug(\r\n        'MessageService',\r\n        `Notification ${notification.id} already exists in cache, skipping`\r\n      );\r\n    }\r\n  }\r\n  private updateNotificationStatus(ids: string[], isRead: boolean) {\r\n    ids.forEach((id) => {\r\n      const notif = this.notificationCache.get(id);\r\n      if (notif) {\r\n        this.notificationCache.set(id, { ...notif, isRead });\r\n      }\r\n    });\r\n    this.notifications.next(Array.from(this.notificationCache.values()));\r\n    this.updateUnreadCount();\r\n  }\r\n  // Typing indicators\r\n  startTyping(conversationId: string): Observable<boolean> {\r\n    const userId = this.getCurrentUserId();\r\n    if (!userId) {\r\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\r\n      return of(false);\r\n    }\r\n\r\n    return this.apollo\r\n      .mutate<StartTupingResponse>({\r\n        mutation: START_TYPING_MUTATION,\r\n        variables: {\r\n          input: {\r\n            conversationId,\r\n            userId,\r\n          },\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => result.data?.startTyping || false),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error starting typing indicator',\r\n            error\r\n          );\r\n          return throwError(\r\n            () => new Error('Failed to start typing indicator')\r\n          );\r\n        })\r\n      );\r\n  }\r\n\r\n  stopTyping(conversationId: string): Observable<boolean> {\r\n    const userId = this.getCurrentUserId();\r\n    if (!userId) {\r\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\r\n      return of(false);\r\n    }\r\n\r\n    return this.apollo\r\n      .mutate<StopTypingResponse>({\r\n        mutation: STOP_TYPING_MUTATION,\r\n        variables: {\r\n          input: {\r\n            conversationId,\r\n            userId,\r\n          },\r\n        },\r\n      })\r\n      .pipe(\r\n        map((result) => result.data?.stopTyping || false),\r\n        catchError((error) => {\r\n          this.logger.error(\r\n            'MessageService',\r\n            'Error stopping typing indicator',\r\n            error\r\n          );\r\n          return throwError(() => new Error('Failed to stop typing indicator'));\r\n        })\r\n      );\r\n  }\r\n\r\n  // destroy\r\n  cleanupSubscriptions(): void {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    this.subscriptions = [];\r\n    if (this.cleanupInterval) {\r\n      clearInterval(this.cleanupInterval);\r\n    }\r\n    this.notificationCache.clear();\r\n    this.logger.debug('NotificationService destroyed');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.cleanupSubscriptions();\r\n  }\r\n}\r\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,QAGJ,gBAAgB;AACvB,SAASC,IAAI,QAAQ,MAAM;AAC3B,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC,EAKjCC,sBAAsB,EACtBC,yBAAyB,EACzBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAIjE,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAkE,aAAa,GAAG,IAAIlE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAmE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAIrE,eAAe,CAAS,CAAC,CAAC;IAClD,KAAAsE,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAI1E,eAAe,CAAc,IAAI,CAAC;IACnD,KAAA2E,YAAY,GAAG,IAAI3E,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAA4E,WAAW,GAAG,IAAI5E,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAA6E,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIpF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAAqF,aAAa,GAAG,IAAIrF,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAAsF,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAggCrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IAkzEO,KAAAC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IAxzGC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAM/C,aAAa,GAAGkD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QACtE,IAAI,CAAClD,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,iBAAiBpD,aAAa,CAACqD,MAAM,uCAAuC,CAC7E;QAED;QACA,IAAI,CAACpD,iBAAiB,CAACqD,KAAK,EAAE;QAE9B;QACAtD,aAAa,CAACuD,OAAO,CAAEC,YAAY,IAAI;UACrC;UACA,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF;QACA,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;QAExB,IAAI,CAACjE,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,GAAG,IAAI,CAACnD,iBAAiB,CAAC8D,IAAI,uCAAuC,CACtE;;KAEJ,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;;EAEL;EACQpB,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,IAAI,CAACmE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;IAC7C,CAAC,CAAC;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACzE,MAAM,CACfuE,SAAS,CAAiC;MACzCI,KAAK,EAAE/E;KACR,CAAC,CACDgF,IAAI,CACHpI,GAAG,CAAC,CAAC;MAAEqI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEhE,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAACiE,kBAAkB,CAACD,IAAI,CAAChE,YAAY,CAAC;MAC1C,OAAOgE,IAAI,CAAChE,YAAY;IAC1B,CAAC,CAAC,EACFpE,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAOhI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ0I,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAC9E,MAAM,CAACuD,KAAK,CAAC,wBAAwB,EAAEuB,IAAI,CAAC;IACjD,IAAI,CAAClE,YAAY,CAACkD,IAAI,CAACgB,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ9B,aAAaA,CAAA;IACnB,IAAI,CAAC+B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAACjD,MAAM,CAAC6C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC9C,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAE5B;MACAE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAACjD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;MAEF,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,eAAe0B,IAAI,KAAKC,IAAI,GAAG,CAAC;KACrE,CAAC,OAAOf,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;;EAEL;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAACjD,KAAK,EAAE;MACd,IAAI,CAACtC,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,OAAO0B,IAAI,kBAAkB,CAAC;MAClE;;IAGF,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV,IAAI,CAACxF,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,OAAOR,IAAI,aAAa,CAAC;QAC5D;;MAGF;MACAO,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB;MACA,IAAI,CAAC,IAAI,CAAClD,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACE,WAAW,GAAG,CAAC;QACrBF,KAAK,CAACT,IAAI,EAAE,CAACY,KAAK,CAAExB,KAAK,IAAI;UAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;QACH,CAAC,CAAC;QACF,IAAI,CAAC9B,SAAS,CAAC4C,IAAI,CAAC,GAAG,IAAI;QAC3B,IAAI,CAACjF,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,mBAAmB0B,IAAI,aAAaM,IAAI,EAAE,CAC3C;;KAEJ,CAAC,OAAOpB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoCc,IAAI,GAAG,EAC3Cd,KAAK,CACN;;EAEL;EAEA;;;;EAIAyB,IAAIA,CAACX,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAACpD,MAAM,CAAC6C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV,IAAI,CAACxF,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,OAAOR,IAAI,aAAa,CAAC;QAC5D;;MAGF;MACA,IAAI,IAAI,CAAC5C,SAAS,CAAC4C,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACK,KAAK,EAAE;QACbL,KAAK,CAACE,WAAW,GAAG,CAAC;QACrB,IAAI,CAACrD,SAAS,CAAC4C,IAAI,CAAC,GAAG,KAAK;QAC5B,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,eAAe0B,IAAI,EAAE,CAAC;;KAE7D,CAAC,OAAOd,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiCc,IAAI,GAAG,EACxCd,KAAK,CACN;;EAEL;EAEA;;;EAGA2B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5D,MAAM,CAAC,CAACsB,OAAO,CAAEuB,IAAI,IAAI;MACxC,IAAI,CAACW,IAAI,CAACX,IAAI,CAAC;IACjB,CAAC,CAAC;IACF,IAAI,CAACjF,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,+BAA+B,CAAC;EACtE;EAEA;;;;EAIA0C,QAAQA,CAAC3D,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACtC,MAAM,CAACkG,IAAI,CAAC,gBAAgB,EAAE,OAAO5D,KAAK,GAAG,WAAW,GAAG,QAAQ,EAAE,CAAC;IAE3E,IAAIA,KAAK,EAAE;MACT,IAAI,CAACwD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAK,OAAOA,CAAA;IACL,OAAO,IAAI,CAAC7D,KAAK;EACnB;EAEA;;;EAGA8D,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAChE,KAAK,EAAE;MACd+D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,MAAMC,UAAU,GAAGJ,YAAY,CAACK,gBAAgB,EAAE;MAClD,MAAMC,QAAQ,GAAGN,YAAY,CAACO,UAAU,EAAE;MAE1C;MACAH,UAAU,CAACI,IAAI,GAAG,MAAM;MACxBJ,UAAU,CAACK,SAAS,CAACC,cAAc,CAAC,GAAG,EAAEV,YAAY,CAACb,WAAW,CAAC,CAAC,CAAC;MAEpE;MACAmB,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEV,YAAY,CAACb,WAAW,CAAC;MACzDmB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHZ,YAAY,CAACb,WAAW,GAAG,IAAI,CAChC;MACDmB,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,CAAC,EAAEZ,YAAY,CAACb,WAAW,GAAG,GAAG,CAAC;MAExE;MACAiB,UAAU,CAACS,OAAO,CAACP,QAAQ,CAAC;MAC5BA,QAAQ,CAACO,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;MAE1C;MACAV,UAAU,CAACW,KAAK,CAACf,YAAY,CAACb,WAAW,CAAC;MAC1CiB,UAAU,CAACf,IAAI,CAACW,YAAY,CAACb,WAAW,GAAG,GAAG,CAAC;MAE/CW,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;KACtE,CAAC,OAAOnC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACoC,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBpC,KAAK,CAACJ,IAAI,EAAE,CAACY,KAAK,CAAE6B,GAAG,IAAI;UACzBnB,OAAO,CAAClC,KAAK,CACX,2DAA2D,EAC3DqD,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBpB,OAAO,CAAClC,KAAK,CACX,8DAA8D,EAC9DsD,UAAU,CACX;;;EAGP;EACA;EACA;EACA;EAEA;;;;;;;;EAQAC,gBAAgBA,CACdC,UAAkB,EAClBC,SAAe,EACfC,cAAuB,EACvBC,QAAiB;IAEjB,IAAI,CAAC9H,MAAM,CAACuD,KAAK,CACf,kDAAkDoE,UAAU,eAAeG,QAAQ,GAAG,CACvF;IAED;IACA,IAAI,CAACF,SAAS,IAAIA,SAAS,CAAC1D,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAClE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,CAAC;MACxD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D;IACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;IAC5B,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACR,SAAS,CAAC,EAAE,iBAAiBI,SAAS,OAAO,EAAE;MACzEjB,IAAI,EAAE,YAAY;MAClBsB,YAAY,EAAEL;KACf,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,IAAIA,SAAS,CAACjE,IAAI,KAAK,CAAC,EAAE;MACtC,IAAI,CAAClE,MAAM,CAACmE,KAAK,CAAC,8CAA8C,CAAC;MACjE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,wCAAwC4E,SAAS,CAAClD,IAAI,WAAWkD,SAAS,CAACjE,IAAI,QAAQ,CACxF;IAED;IACA,MAAMoE,QAAQ,GAAG;MACfR,QAAQ,EAAEA,QAAQ,IAAI,CAAC;MACvBS,cAAc,EAAE,IAAI;MACpBP,SAAS,EAAEA;KACZ;IAED;IACA;IACA,OAAO,IAAI,CAACQ,WAAW,CACrBb,UAAU,EACV,GAAG;IAAE;IACLQ,SAAS,EACTtL,WAAW,CAAC4L,aAAa,EACzBZ,cAAc,EACdjF,SAAS,EACT0F,QAAQ,CACT;EACH;EAEA;;;;;EAKAI,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAM3D,KAAK,GAAG,IAAIC,KAAK,CAACuD,QAAQ,CAAC;MAEjCxD,KAAK,CAAC4D,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAED1D,KAAK,CAAC6D,OAAO,GAAI7E,KAAK,IAAI;QACxB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACY,KAAK,CAAExB,KAAK,IAAI;QAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjE2E,MAAM,CAAC3E,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIA8E,gBAAgBA,CAAA;IACd,IAAI,CAACjJ,MAAM,CAACuD,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAACxD,MAAM,CACfmJ,UAAU,CAA+B;MACxCxE,KAAK,EAAE9E,wBAAwB;MAC/BuJ,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAACzE,IAAI,EAAEqE,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACjJ,MAAM,CAACuD,KAAK,CACf,8BAA8B+F,aAAa,CAAC9F,MAAM,iBAAiB,CACpE;MACD,OAAO8F,aAAa;IACtB,CAAC,CAAC,EACF9M,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAwB,WAAWA,CACTC,QAAgB,EAChB7B,UAAkB,EAClBE,cAAsB,EACtB4B,IAAA,GAAe,CAAC,EAChBjH,KAAA,GAAgB,EAAE;IAElB,OAAO,IAAI,CAACzC,MAAM,CACfmJ,UAAU,CAA6B;MACtCxE,KAAK,EAAE9F,kBAAkB;MACzB8K,SAAS,EAAE;QAAEF,QAAQ;QAAE7B,UAAU;QAAEE,cAAc;QAAErF,KAAK;QAAEiH;MAAI,CAAE;MAChEN,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMM,QAAQ,GAAGN,MAAM,CAACzE,IAAI,EAAE2E,WAAW,IAAI,EAAE;MAC/C,OAAOI,QAAQ,CAACpN,GAAG,CAAEqN,GAAG,IAAK,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC,CAAC;IAC1D,CAAC,CAAC,EACFpN,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACA+B,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAACjK,MAAM,CACfkK,MAAM,CAA2B;MAChCC,QAAQ,EAAExL,qBAAqB;MAC/BgL,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDrF,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEkF,WAAW,EAAE;QAC7B,MAAM,IAAI/B,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAAC8B,gBAAgB,CAACR,MAAM,CAACzE,IAAI,CAACkF,WAAW,CAAC;IACvD,CAAC,CAAC,EACFtN,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAoC,aAAaA,CAACJ,SAAiB;IAC7B,OAAO,IAAI,CAAChK,MAAM,CACfkK,MAAM,CAA6B;MAClCC,QAAQ,EAAEvL,uBAAuB;MACjC+K,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDpF,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEuF,aAAa,EAAE;QAC/B,MAAM,IAAIpC,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAAC8B,gBAAgB,CAACR,MAAM,CAACzE,IAAI,CAACuF,aAAa,CAAC;IACzD,CAAC,CAAC,EACF3N,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAS,WAAWA,CACTb,UAAkB,EAClByC,OAAe,EACfC,IAAW,EACXC,WAAA,GAA2BzN,WAAW,CAAC0N,IAAI,EAC3C1C,cAAuB,EACvB2C,OAAgB,EAChBlC,QAAc;IAEd,IAAI,CAACtI,MAAM,CAACkG,IAAI,CACd,wCAAwCyB,UAAU,cAAc,CAAC,CAAC0C,IAAI,EAAE,CACzE;IACD,IAAI,CAACrK,MAAM,CAACuD,KAAK,CACf,sCAAsC6G,OAAO,EAAEK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC7DL,OAAO,EAAE5G,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EACjC,GAAG,CACJ;IAED;IACA,MAAMkH,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACpD,MAAM,CAACuD,KAAK,CACf,uEAAuE,CAAC,CAACmH,KAAK,EAAE,CACjF;IAED;IACA,IAAIC,gBAAgB,GAAGL,WAAW;IAElC;IACA,IAAID,IAAI,EAAE;MACR;MACA,IAAIC,WAAW,KAAKzN,WAAW,CAAC4L,aAAa,EAAE;QAC7CkC,gBAAgB,GAAG9N,WAAW,CAAC4L,aAAa;QAC5C,IAAI,CAACzI,MAAM,CAACuD,KAAK,CAAC,oDAAoD,CAAC;;MAEzE;MAAA,KACK,IAAI+G,WAAW,KAAKzN,WAAW,CAAC0N,IAAI,EAAE;QACzC,IAAIF,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UAClCD,gBAAgB,GAAG9N,WAAW,CAACgO,KAAK;SACrC,MAAM,IAAIR,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzCD,gBAAgB,GAAG9N,WAAW,CAACiO,KAAK;SACrC,MAAM,IAAIT,IAAI,CAACtD,IAAI,CAAC6D,UAAU,CAAC,QAAQ,CAAC,EAAE;UACzC;UACA,IAAItC,QAAQ,IAAIA,QAAQ,CAACC,cAAc,EAAE;YACvCoC,gBAAgB,GAAG9N,WAAW,CAAC4L,aAAa;WAC7C,MAAM;YACLkC,gBAAgB,GAAG9N,WAAW,CAACkO,KAAK;;SAEvC,MAAM;UACLJ,gBAAgB,GAAG9N,WAAW,CAACmO,IAAI;;;;IAKzC,IAAI,CAAChL,MAAM,CAACuD,KAAK,CACf,6CAA6CoH,gBAAgB,EAAE,CAChE;IAED;IACA;IACA,MAAMjB,SAAS,GAAQ;MACrB/B,UAAU;MACVyC,OAAO;MACPrD,IAAI,EAAE4D,gBAAgB,CAAE;KACzB;IAED;IACA;IACA,IAAIjB,SAAS,CAAC3C,IAAI,EAAE;MAClBhB,MAAM,CAACkF,cAAc,CAACvB,SAAS,EAAE,MAAM,EAAE;QACvCwB,KAAK,EAAEP,gBAAgB;QACvBQ,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE;OACX,CAAC;;IAGJ;IACA,IAAI9C,QAAQ,EAAE;MACZoB,SAAS,CAACpB,QAAQ,GAAGA,QAAQ;MAC7B,IAAI,CAACtI,MAAM,CAACuD,KAAK,CAAC,qCAAqC,EAAE+E,QAAQ,CAAC;;IAGpE,IAAI+B,IAAI,EAAE;MACRX,SAAS,CAACW,IAAI,GAAGA,IAAI;MACrB,IAAI,CAACrK,MAAM,CAACuD,KAAK,CACf,mCAAmC8G,IAAI,CAACpF,IAAI,WAAWoF,IAAI,CAACnG,IAAI,WAAWmG,IAAI,CAACtD,IAAI,kBAAkB4D,gBAAgB,EAAE,CACzH;;IAEH,IAAI9C,cAAc,EAAE;MAClB6B,SAAS,CAAC7B,cAAc,GAAGA,cAAc;MACzC,IAAI,CAAC7H,MAAM,CAACuD,KAAK,CACf,iDAAiDsE,cAAc,EAAE,CAClE;;IAEH,IAAI2C,OAAO,EAAE;MACXd,SAAS,CAACc,OAAO,GAAGA,OAAO;MAC3B,IAAI,CAACxK,MAAM,CAACuD,KAAK,CAAC,yCAAyCiH,OAAO,EAAE,CAAC;;IAGvE,MAAMa,OAAO,GAAGhB,IAAI,GAAG;MAAEiB,YAAY,EAAE,IAAI;MAAEjB;IAAI,CAAE,GAAGzH,SAAS;IAE/D,IAAI,CAAC5C,MAAM,CAACuD,KAAK,CACf,2DAA2D,EAC3DmG,SAAS,CACV;IAED,OAAO,IAAI,CAAC3J,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAE9M,qBAAqB;MAC/BsM,SAAS;MACT2B;KACD,CAAC,CACD1G,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CAAC,yCAAyC,EAAE8F,MAAM,CAAC;MAEpE,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAE4D,WAAW,EAAE;QAC7B,IAAI,CAACxI,MAAM,CAACmE,KAAK,CACf,2DAA2D,CAC5D;QACD,MAAM,IAAI4D,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI;QACF,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,2CAA2C,EAC3C8F,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QACD,MAAM+C,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAC7CR,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CACxB;QAED,IAAI,CAACxI,MAAM,CAACkG,IAAI,CACd,+CAA+CqF,iBAAiB,CAAC3H,EAAE,EAAE,CACtE;QACD,OAAO2H,iBAAiB;OACzB,CAAC,OAAOC,kBAAkB,EAAE;QAC3B,IAAI,CAACxL,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CqH,kBAAkB,CACnB;QAED;QACA,MAAMC,cAAc,GAAY;UAC9B7H,EAAE,EAAEyF,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC5E,EAAE,IAAI,OAAO,GAAGqE,IAAI,CAACC,GAAG,EAAE;UACtDkC,OAAO,EAAEf,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAAC4B,OAAO,IAAI,EAAE;UAC9CrD,IAAI,EAAEsC,MAAM,CAACzE,IAAI,CAAC4D,WAAW,CAACzB,IAAI,IAAIlK,WAAW,CAAC0N,IAAI;UACtDvC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrByD,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE;YACN/H,EAAE,EAAE,IAAI,CAACgI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEb;QAED,IAAI,CAAC7L,MAAM,CAACkG,IAAI,CACd,+CAA+CuF,cAAc,CAAC7H,EAAE,EAAE,CACnE;QACD,OAAO6H,cAAc;;IAEzB,CAAC,CAAC,EACFjP,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MACnE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA+D,iBAAiBA,CAAC/B,SAAiB;IACjC,OAAO,IAAI,CAAChK,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAE7M,qBAAqB;MAC/BqM,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDpF,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEkH,iBAAiB,EACjC,MAAM,IAAI/D,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACkH,iBAAiB;QAChCC,MAAM,EAAE,IAAI9D,IAAI;OACjB;IACH,CAAC,CAAC,EACFzL,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAiE,cAAcA,CAACjC,SAAiB,EAAEkC,KAAa;IAC7C,OAAO,IAAI,CAAClM,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAE3L,yBAAyB;MACnCmL,SAAS,EAAE;QAAEK,SAAS;QAAEkC;MAAK;KAC9B,CAAC,CACDtH,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEoH,cAAc,EAC9B,MAAM,IAAIjE,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOsB,MAAM,CAACzE,IAAI,CAACoH,cAAc;IACnC,CAAC,CAAC,EACFxP,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAmE,cAAcA,CACZnC,SAAiB,EACjBoC,eAAyB;IAEzB,OAAO,IAAI,CAACpM,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAE1L,wBAAwB;MAClCkL,SAAS,EAAE;QAAEK,SAAS;QAAEoC;MAAe;KACxC,CAAC,CACDxH,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEsH,cAAc,EAC9B,MAAM,IAAInE,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOsB,MAAM,CAACzE,IAAI,CAACsH,cAAc,CAAC3P,GAAG,CAAEqN,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACN5B,SAAS,EAAE4B,GAAG,CAAC5B,SAAS,GACpB,IAAI,CAACoE,aAAa,CAACxC,GAAG,CAAC5B,SAAS,CAAC,GACjC,IAAIC,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACFzL,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAsE,UAAUA,CAACtC,SAAiB,EAAElC,cAAsB;IAClD,OAAO,IAAI,CAAC9H,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAEzL,oBAAoB;MAC9BiL,SAAS,EAAE;QAAEK,SAAS;QAAElC;MAAc;KACvC,CAAC,CACDlD,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEyH,UAAU,EAC1B,MAAM,IAAItE,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACyH,UAAU;QACzBC,QAAQ,EAAE,IAAIrE,IAAI;OACnB;IACH,CAAC,CAAC,EACFzL,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEAwE,cAAcA,CACZ7H,KAAa,EACbmD,cAAuB,EACvB2E,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAACzM,MAAM,CACfmJ,UAAU,CAAyB;MAClCxE,KAAK,EAAE/G,qBAAqB;MAC5B+L,SAAS,EAAE;QACThF,KAAK;QACLmD,cAAc;QACd,GAAG2E,OAAO;QACVC,QAAQ,EAAE,IAAI,CAAC/J,eAAe,CAAC8J,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAAChK,eAAe,CAAC8J,OAAO,CAACE,MAAM;OAC5C;MACDvD,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CACA8M,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAE2H,cAAc,EAAEhQ,GAAG,CAAEqN,GAAG,KAAM;MACzC,GAAGA,GAAG;MACN5B,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;MACvC2D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDnP,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEA8E,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAAC/M,MAAM,CACfmJ,UAAU,CAA4B;MACrCxE,KAAK,EAAE9G,yBAAyB;MAChC8L,SAAS,EAAE;QAAEoD;MAAM,CAAE;MACrB3D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CACA8M,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAEiI,iBAAiB,EAAEtQ,GAAG,CAAEqN,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACN5B,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;MACvC2D,MAAM,EAAE,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDnP,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAgF,qBAAqBA,CAAClF,cAAsB;IAC1C,IAAI,CAAC3H,kBAAkB,CAAC4D,IAAI,CAAC+D,cAAc,CAAC;EAC9C;EAEAmF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjN,MAAM,CACfmJ,UAAU,CAA2B;MACpCxE,KAAK,EAAE1H,uBAAuB;MAC9BmM,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAM4D,aAAa,GAAG5D,MAAM,CAACzE,IAAI,EAAEoI,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAC1Q,GAAG,CAAE2Q,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACF1Q,UAAU,CAAE2H,KAAK,IAAI;MACnBkC,OAAO,CAAClC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAqF,eAAeA,CACbvF,cAAsB,EACtBrF,KAAc,EACdiH,IAAa;IAEb,IAAI,CAACzJ,MAAM,CAACkG,IAAI,CACd,0CAA0C2B,cAAc,YAAYrF,KAAK,WAAWiH,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAE7B;IAAc,CAAE;IAEzC;IACA,IAAIrF,KAAK,KAAKI,SAAS,EAAE;MACvB8G,SAAS,CAAClH,KAAK,GAAGA,KAAK;KACxB,MAAM;MACLkH,SAAS,CAAClH,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAIiH,IAAI,KAAK7G,SAAS,EAAE;MACtB;MACA,MAAMyK,MAAM,GAAG,CAAC5D,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAClH,KAAK;MAC3CkH,SAAS,CAAC2D,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACrN,MAAM,CAACuD,KAAK,CACf,uCAAuC8J,MAAM,eAAe5D,IAAI,eAAeC,SAAS,CAAClH,KAAK,EAAE,CACjG;KACF,MAAM;MACLkH,SAAS,CAAC2D,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACrN,MAAM,CAACuD,KAAK,CACf,uDAAuDmG,SAAS,CAAClH,KAAK,YAAYkH,SAAS,CAAC2D,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAACtN,MAAM,CACfmJ,UAAU,CAA0B;MACnCxE,KAAK,EAAEvH,sBAAsB;MAC7BuM,SAAS,EAAEA,SAAS;MACpBP,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD8F,MAAM,CACP;MAED,MAAM6D,IAAI,GAAG7D,MAAM,CAACzE,IAAI,EAAEwI,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAAClN,MAAM,CAACmE,KAAK,CACf,4CAA4C0D,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,8CAA8CsE,cAAc,EAAE,CAC/D;MACD,MAAMyF,sBAAsB,GAAG,IAAI,CAACH,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAAClN,MAAM,CAACkG,IAAI,CACd,sDAAsD2B,cAAc,mBAClEyF,sBAAsB,CAACC,YAAY,EAAE/J,MAAM,IAAI,CACjD,eAAe8J,sBAAsB,CAAC3D,QAAQ,EAAEnG,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAO8J,sBAAsB;IAC/B,CAAC,CAAC,EACF9Q,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEAyF,kBAAkBA,CAACV,MAAc;IAC/B,IAAI,CAAC9M,MAAM,CAACkG,IAAI,CACd,qDAAqD4G,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAChI,MAAM,CACfkK,MAAM,CAAuC;MAC5CC,QAAQ,EAAElL,4BAA4B;MACtC0K,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD8F,MAAM,CACP;MAED,MAAMoE,YAAY,GAAGpE,MAAM,CAACzE,IAAI,EAAE4I,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAACzN,MAAM,CAACmE,KAAK,CACf,6DAA6D2I,MAAM,EAAE,CACtE;QACD,MAAM,IAAI/E,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAMuF,sBAAsB,GAC1B,IAAI,CAACH,qBAAqB,CAACM,YAAY,CAAC;QAC1C,IAAI,CAACzN,MAAM,CAACkG,IAAI,CACd,uDAAuDoH,sBAAsB,CAAC1J,EAAE,EAAE,CACnF;QACD,OAAO0J,sBAAsB;OAC9B,CAAC,OAAOnJ,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI4D,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACFvL,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D2I,MAAM,GAAG,EACnE3I,KAAK,CACN;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,kCAAkC5D,KAAK,CAACuJ,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACb,MAAc;IACpC,IAAI,CAAC9M,MAAM,CAACkG,IAAI,CACd,gEAAgE4G,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACmE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACiF,gBAAgB,EAAE,CAACrI,IAAI,CACjCpI,GAAG,CAAE0Q,aAAa,IAAI;MACpB;MACA,MAAMW,aAAa,GAAG,IAAI,CAAChC,gBAAgB,EAAE;MAE7C;MACA,MAAMiC,oBAAoB,GAAGZ,aAAa,CAACa,IAAI,CAAEZ,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACa,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBd,IAAI,CAACK,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAAKA,CAAC,CAACrK,EAAE,IAAIqK,CAAC,CAACC,GAAG,CAAC,IAAI,EAAE;QACpD,OACEF,cAAc,CAACG,QAAQ,CAACrB,MAAM,CAAC,IAC/BkB,cAAc,CAACG,QAAQ,CAACP,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIC,oBAAoB,EAAE;QACxB,IAAI,CAAC7N,MAAM,CAACkG,IAAI,CACd,iDAAiD2H,oBAAoB,CAACjK,EAAE,EAAE,CAC3E;QACD,OAAOiK,oBAAoB;;MAG7B;MACA,MAAM,IAAI9F,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACFvL,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACkG,IAAI,CACd,sEAAsE/B,KAAK,CAACuJ,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACV,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAsB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACf5E,IAAI,GAAG,CAAC,EACRjH,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,oCAAoCmI,OAAO,WAAW5E,IAAI,YAAYjH,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDmB,KAAK,EAAEzH;KACR,CAAC;IAEF;IACA;IACA,IAAIoR,OAAO,EAAE;MACX,IAAI,CAACrO,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAAChB,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAG2H,IAAI;IAC9C,IAAI,CAAClH,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAM8L,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACvO,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,SAAS+K,sBAAsB,CAACpK,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAACnE,MAAM,CACfmJ,UAAU,CAA+B;MACxCxE,KAAK,EAAEzH,uBAAuB;MAC9ByM,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACVjH,KAAK,EAAEA;OACR;MACD2G,WAAW,EAAEkF,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDjF,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAI8F,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACmF,MAAM,CACd;QACD,MAAM,IAAIzG,KAAK,CAACsB,MAAM,CAACmF,MAAM,CAACjS,GAAG,CAAEkS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAMvO,aAAa,GAAGkJ,MAAM,CAACzE,IAAI,EAAE+J,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAAC3O,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,YAAYpD,aAAa,CAACqD,MAAM,uCAAuCiG,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAClH,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAACqD,MAAM,IAAIhB,KAAK;MAE/B,IAAIrC,aAAa,CAACqD,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACxD,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAC3D,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMmM,qBAAqB,GAAGzO,aAAa,CAACzD,MAAM,CAC/CmS,KAAK,IAAK,CAACP,sBAAsB,CAACQ,GAAG,CAACD,KAAK,CAACjL,EAAE,CAAC,CACjD;MAED,IAAI,CAAC5D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBACEpD,aAAa,CAACqD,MAAM,GAAGoL,qBAAqB,CAACpL,MAC/C,wBAAwB,CACzB;MAED;MACAoL,qBAAqB,CAAClL,OAAO,CAAC,CAACmL,KAAK,EAAEE,KAAK,KAAI;QAC7C1I,OAAO,CAACC,GAAG,CAAC,gBAAgByI,KAAK,GAAG,CAAC,UAAUtF,IAAI,IAAI,EAAE;UACvD7F,EAAE,EAAEiL,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG;UAClCnH,IAAI,EAAE8H,KAAK,CAAC9H,IAAI;UAChBqD,OAAO,EAAEyE,KAAK,CAACzE,OAAO;UACtBsB,MAAM,EAAEmD,KAAK,CAACnD;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACsD,WAAW,CAACJ,qBAAqB,CAAC;MAEvC;MACA,MAAMK,mBAAmB,GAAGlL,KAAK,CAACnH,IAAI,CACpC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAChC;MAEDqC,OAAO,CAACC,GAAG,CACT,8CAA8C2I,mBAAmB,CAACzL,MAAM,EAAE,CAC3E;MAED;MACA,IAAI,CAACrD,aAAa,CAAC2D,IAAI,CAACmL,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAAChL,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACiL,+BAA+B,EAAE;MAEtC,OAAOD,mBAAmB;IAC5B,CAAC,CAAC,EACFzS,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAACgL,aAAa,EAAE;QACvB,IAAI,CAACnP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACgL,aAAa,CACpB;;MAGH,IAAIhL,KAAK,CAACiL,YAAY,EAAE;QACtB,IAAI,CAACpP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACiL,YAAY,CACnB;;MAGH,OAAOhT,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQwG,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMc,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMpM,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAOmM,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClCjM,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAC3G,GAAG,CAAEiT,CAAe,IAAKA,CAAC,CAAC5L,EAAE,CAAC,CAC9D;MAED;MACA,MAAM6L,mBAAmB,GACvB,IAAI,CAAC1P,MAAM,CAAC2P,MAAM,CAACC,SAAS,CAA+B;QACzDjL,KAAK,EAAEzH;OACR,CAAC,EAAE0R,oBAAoB,IAAI,EAAE;MAEhC;MACAc,mBAAmB,CAAC/L,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAAC4L,oBAAoB,CAACT,GAAG,CAACnL,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9CyL,UAAU,CAACO,GAAG,CAACjM,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAOyL,UAAU;KAClB,CAAC,OAAOlL,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAImL,GAAG,EAAU;;EAE5B;EAEA;EACA7M,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAoN,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACvN,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAACsM,gBAAgB,CAC1B,KAAK,EACL0B,QAAQ,EACR,IAAI,CAACvN,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAuN,mBAAmBA,CAACnM,EAAU;IAC5B,OAAO,IAAI,CAAC1B,cAAc,CAACyC,IAAI,CAC7BpI,GAAG,CAAE4D,aAAa,IAAKA,aAAa,CAAC2N,IAAI,CAAE0B,CAAC,IAAKA,CAAC,CAAC5L,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DpH,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACAiI,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC7P,aAAa,CAAC+K,KAAK,EAAE1H,MAAM,IAAI,CAAC;EAC9C;EACAyM,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAACnQ,MAAM,CACf2E,KAAK,CAAkC;MACtCA,KAAK,EAAE7F,8BAA8B;MACrC6K,SAAS,EAAE;QAAE9F,EAAE,EAAEsM;MAAc,CAAE;MACjC/G,WAAW,EAAE;KACd,CAAC,CACDxE,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEqL,0BAA0B,IAAI,EAAE,CAAC,EAC9DzT,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAoI,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACjO,cAAc,CAACyC,IAAI,CAC7BpI,GAAG,CAAE4D,aAAa,IAAKA,aAAa,CAACzD,MAAM,CAAE8S,CAAC,IAAK,CAACA,CAAC,CAAC9D,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKA0E,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAAClQ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kCAAkC2M,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAClQ,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAOrJ,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAI,CAAC3H,iBAAiB,CAACiQ,MAAM,CAACH,cAAc,CAAC;IAC7C,IAAI,CAAC/P,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACiL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAAgE;MACrEC,QAAQ,EAAEjL,4BAA4B;MACtCyK,SAAS,EAAE;QAAEwG;MAAc;KAC5B,CAAC,CACDvL,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAEwL,kBAAkB;MAChD,IAAI,CAACE,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7B+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF9T,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,mDAAmD,EACnDA,KAAK,CACN;MAED;MACA,OAAOhI,EAAE,CAAC;QACRoU,OAAO,EAAE,IAAI;QACb7C,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQwB,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAM/O,aAAa,GAAG4D,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC;MACjEb,YAAY,CAACqN,OAAO,CAAC,eAAe,EAAEnN,IAAI,CAACoN,SAAS,CAACtQ,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOY,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIAuM,sBAAsBA,CAAA;IAKpB,IAAI,CAAC1Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMoN,KAAK,GAAG,IAAI,CAACvQ,iBAAiB,CAAC8D,IAAI;IACzC,IAAI,CAAC9D,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACtD,aAAa,CAAC2D,IAAI,CAAC,EAAE,CAAC;IAC3B,IAAI,CAACxD,iBAAiB,CAACwD,IAAI,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACoL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAMJ;MACDC,QAAQ,EAAE/K;KACX,CAAC,CACDwF,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAE8L,sBAAsB;MACpD,IAAI,CAACJ,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzD+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF9T,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4DAA4D,EAC5DA,KAAK,CACN;MAED;MACA,OAAOhI,EAAE,CAAC;QACRoU,OAAO,EAAE,IAAI;QACbI,KAAK;QACLjD,OAAO,EAAE,GAAGiD,KAAK;OAClB,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAAC7Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kBAAkBsN,eAAe,CAACrN,MAAM,gBAAgB,CACzD;IAED,IAAI,CAACqN,eAAe,IAAIA,eAAe,CAACrN,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOrJ,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,IAAI4I,KAAK,GAAG,CAAC;IACbE,eAAe,CAACnN,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACxD,iBAAiB,CAAC0O,GAAG,CAAClL,EAAE,CAAC,EAAE;QAClC,IAAI,CAACxD,iBAAiB,CAACiQ,MAAM,CAACzM,EAAE,CAAC;QACjC+M,KAAK,EAAE;;IAEX,CAAC,CAAC;IAEF,IAAI,CAACxQ,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACiL,+BAA+B,EAAE;IAEtC;IACA,OAAO,IAAI,CAACnP,MAAM,CACfkK,MAAM,CAMJ;MACDC,QAAQ,EAAEhL,sCAAsC;MAChDwK,SAAS,EAAE;QAAEmH;MAAe;KAC7B,CAAC,CACDlM,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMiH,QAAQ,GAAGjH,MAAM,CAACzE,IAAI,EAAEgM,2BAA2B;MACzD,IAAI,CAACN,QAAQ,EAAE;QACb,MAAM,IAAIvI,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtC+M,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF9T,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,0DAA0D,EAC1DA,KAAK,CACN;MAED;MACA,OAAOhI,EAAE,CAAC;QACRoU,OAAO,EAAEI,KAAK,GAAG,CAAC;QAClBA,KAAK;QACLjD,OAAO,EAAE,GAAGiD,KAAK;OAClB,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACAG,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAAC5O,cAAc,CAACyC,IAAI,CAC7BpI,GAAG,CAAE4D,aAAa,IAAI;MACpB,MAAM4Q,MAAM,GAAG,IAAI1Q,GAAG,EAAoC;MAC1DF,aAAa,CAACuD,OAAO,CAAEmL,KAAK,IAAI;QAC9B,IAAI,CAACkC,MAAM,CAACjC,GAAG,CAACD,KAAK,CAAC9H,IAAI,CAAC,EAAE;UAC3BgK,MAAM,CAAClN,GAAG,CAACgL,KAAK,CAAC9H,IAAI,EAAE,EAAE,CAAC;;QAE5BgK,MAAM,CAACC,GAAG,CAACnC,KAAK,CAAC9H,IAAI,CAAC,EAAEkK,IAAI,CAACpC,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAOkC,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAAC7Q,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,kCAAkCsN,eAAe,EAAEnC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAACmC,eAAe,IAAIA,eAAe,CAACrN,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAOtJ,EAAE,CAAC;QACRoU,OAAO,EAAE,KAAK;QACdY,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAAC9Q,iBAAiB,CAAC4K;OACxC,CAAC;;IAGJ;IACA,MAAMmG,QAAQ,GAAGR,eAAe,CAACnU,MAAM,CACpCkH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC0N,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAAC7N,MAAM,KAAKqN,eAAe,CAACrN,MAAM,EAAE;MAC9C,IAAI,CAACxD,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvEoN,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAOjV,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChD8N,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBpB,OAAO,EAAE,IAAI;QACbY,SAAS,EAAEE,QAAQ,CAAC7N,MAAM;QAC1B4N,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACvR,iBAAiB,CAAC4K,KAAK,GAAGmG,QAAQ,CAAC7N,MAAM;;KAGnD;IAED;IACA6C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtEuK,eAAe,EAAEQ;KAClB,CAAC;IACFhL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAExH,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACiB,MAAM,CACfkK,MAAM,CAAkC;MACvCC,QAAQ,EAAEpL,+BAA+B;MACzC4K,SAAS,EAAE;QAAEmH,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCI,WAAW,EAAE,KAAK;MAClB3I,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDxE,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAE8F,MAAM,CAAC;MAC9DhD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+C,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBkF,MAAM,CAACmF,MAAM,CACd;QACDnI,OAAO,CAAClC,KAAK,CAAC,iBAAiB,EAAEkF,MAAM,CAACmF,MAAM,CAAC;;MAGjD;MACA,MAAM8B,QAAQ,GACZjH,MAAM,CAACzE,IAAI,EAAE+M,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOrB,QAAQ;IACjB,CAAC,CAAC,EACF9T,UAAU,CAAE2H,KAAY,IAAI;MAC1B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDkC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAOhI,EAAE,CAAC;QACRoU,OAAO,EAAE,IAAI;QACbY,SAAS,EAAEE,QAAQ,CAAC7N,MAAM;QAC1B4N,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACvR,iBAAiB,CAAC4K,KAAK,GAAGmG,QAAQ,CAAC7N,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;;;;EAQAuO,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBpK,cAAuB,EACvBqK,OAAqB;IAErB,OAAO,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC,CAACtN,IAAI,CAC1ChI,SAAS,CAAEyV,MAAM,IAAI;MACnB,IAAI,CAACtR,WAAW,GAAGsR,MAAM;MACzB,IAAI,CAAC/Q,YAAY,CAACyC,IAAI,CAACsO,MAAM,CAAC;MAE9B;MACA,IAAI,CAACpR,cAAc,GAAG,IAAIqR,iBAAiB,CAAC,IAAI,CAAC9Q,SAAS,CAAC;MAE3D;MACA6Q,MAAM,CAACE,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;QACnC,IAAI,CAACvR,cAAe,CAACwR,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACpR,cAAc,CAACyR,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjB,IAAI,CAACC,cAAc,EAAE,EACrB,eAAe,EACfxP,IAAI,CAACoN,SAAS,CAACiC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC3R,cAAc,CAAC8R,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC3R,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIgS,WAAW,EAAE;UACrC,IAAI,CAACzR,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC/C,YAAY,CAAC;;QAE5C2R,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;UAC7C,IAAI,CAACxR,YAAa,CAACyR,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,OAAO3V,IAAI,CAAC,IAAI,CAACoE,cAAc,CAACiS,WAAW,EAAE,CAAC,CAACtO,IAAI,CACjDhI,SAAS,CAAEuW,KAAK,IAAI;QAClB,OAAOtW,IAAI,CAAC,IAAI,CAACoE,cAAe,CAACmS,mBAAmB,CAACD,KAAK,CAAC,CAAC,CAACvO,IAAI,CAC/DpI,GAAG,CAAC,MAAM2W,KAAK,CAAC,CACjB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFvW,SAAS,CAAEuW,KAAK,IAAI;MAClB;MACA,MAAME,MAAM,GAAG,IAAI,CAACP,cAAc,EAAE;MAEpC;MACA,OAAO,IAAI,CAAC9S,MAAM,CACfkK,MAAM,CAAyB;QAC9BC,QAAQ,EAAE9K,sBAAsB;QAChCsK,SAAS,EAAE;UACTsI,WAAW;UACXC,QAAQ;UACRmB,MAAM;UACNF,KAAK,EAAE7P,IAAI,CAACoN,SAAS,CAACyC,KAAK,CAAC;UAC5BrL,cAAc;UACdqK;;OAEH,CAAC,CACDvN,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEmN,YAAY;QACtC,IAAI,CAACjN,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,yBAAyB,CAAC;;QAG5C;QACA,IAAI,CAACpH,UAAU,CAACmD,IAAI,CAACgB,IAAI,CAAC;QAE1B;QACA,MAAMuO,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3CxO,IAAI,CAAClB,EAAE,CACR,CAACU,SAAS,EAAE;QACb,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACoC,SAAS,CAAC;QAElC,OAAOvO,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFtI,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOnX,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH;EACH;EAEA;;;;;EAKAyL,UAAUA,CAAC5S,YAA0B;IACnC,IAAI,CAACgF,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAACuM,iBAAiB,CAACvR,YAAY,CAACmG,IAAI,CAAC,CAACpC,IAAI,CACnDhI,SAAS,CAAEyV,MAAM,IAAI;MACnB,IAAI,CAACtR,WAAW,GAAGsR,MAAM;MACzB,IAAI,CAAC/Q,YAAY,CAACyC,IAAI,CAACsO,MAAM,CAAC;MAE9B;MACA,IAAI,CAACpR,cAAc,GAAG,IAAIqR,iBAAiB,CAAC,IAAI,CAAC9Q,SAAS,CAAC;MAE3D;MACA6Q,MAAM,CAACE,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;QACnC,IAAI,CAACvR,cAAe,CAACwR,QAAQ,CAACD,KAAK,EAAEH,MAAM,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,IAAI,CAACpR,cAAc,CAACyR,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAI,CAACC,cAAc,CACjBhS,YAAY,CAACgD,EAAE,EACf,eAAe,EACfP,IAAI,CAACoN,SAAS,CAACiC,KAAK,CAACC,SAAS,CAAC,CAChC;;MAEL,CAAC;MAED;MACA,IAAI,CAAC3R,cAAc,CAAC8R,OAAO,GAAIJ,KAAK,IAAI;QACtC,IAAI,CAAC,IAAI,CAAC3R,YAAY,EAAE;UACtB,IAAI,CAACA,YAAY,GAAG,IAAIgS,WAAW,EAAE;UACrC,IAAI,CAACzR,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC/C,YAAY,CAAC;;QAE5C2R,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAACV,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAI;UAC7C,IAAI,CAACxR,YAAa,CAACyR,QAAQ,CAACD,KAAK,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC;MAED;MACA,MAAMW,KAAK,GAAG7P,IAAI,CAACC,KAAK,CAAC1C,YAAY,CAACsS,KAAK,CAAC;MAC5C,OAAOtW,IAAI,CACT,IAAI,CAACoE,cAAc,CAACyS,oBAAoB,CACtC,IAAIC,qBAAqB,CAACR,KAAK,CAAC,CACjC,CACF,CAACvO,IAAI,CACJhI,SAAS,CAAC,MAAMC,IAAI,CAAC,IAAI,CAACoE,cAAe,CAAC2S,YAAY,EAAE,CAAC,CAAC,EAC1DhX,SAAS,CAAEiX,MAAM,IAAI;QACnB,OAAOhX,IAAI,CAAC,IAAI,CAACoE,cAAe,CAACmS,mBAAmB,CAACS,MAAM,CAAC,CAAC,CAACjP,IAAI,CAChEpI,GAAG,CAAC,MAAMqX,MAAM,CAAC,CAClB;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFjX,SAAS,CAAEiX,MAAM,IAAI;MACnB;MACA,OAAO,IAAI,CAAC7T,MAAM,CACfkK,MAAM,CAAuB;QAC5BC,QAAQ,EAAE5K,oBAAoB;QAC9BoK,SAAS,EAAE;UACT0J,MAAM,EAAExS,YAAY,CAACgD,EAAE;UACvBgQ,MAAM,EAAEvQ,IAAI,CAACoN,SAAS,CAACmD,MAAM;;OAEhC,CAAC,CACDjP,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;QACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAE4O,UAAU;QACpC,IAAI,CAAC1O,IAAI,EAAE;UACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;QAG1C;QACA,IAAI,CAAChD,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI,CAACpE,UAAU,CAACmD,IAAI,CAAC;UACnB,GAAGgB,IAAI;UACP+O,MAAM,EAAEjT,YAAY,CAACiT,MAAM;UAC3B9M,IAAI,EAAEnG,YAAY,CAACmG,IAAI;UACvBc,cAAc,EAAEjH,YAAY,CAACiH;SAC9B,CAAC;QAEF;QACA,MAAMwL,SAAS,GAAG,IAAI,CAACC,sBAAsB,CAC3C1S,YAAY,CAACgD,EAAE,CAChB,CAACU,SAAS,EAAE;QACb,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACoC,SAAS,CAAC;QAElC;QACA,IAAI,CAACzS,YAAY,CAACkD,IAAI,CAAC,IAAI,CAAC;QAE5B,OAAOgB,IAAI;MACb,CAAC,CAAC,CACH;IACL,CAAC,CAAC,EACFtI,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOnX,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACH;EAEA;;;;;;EAMA+L,UAAUA,CAACV,MAAc,EAAEW,MAAe;IACxC,IAAI,CAACnO,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAC7F,MAAM,CACfkK,MAAM,CAAuB;MAC5BC,QAAQ,EAAE3K,oBAAoB;MAC9BmK,SAAS,EAAE;QACT0J,MAAM;QACNW;;KAEH,CAAC,CACDpP,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEkP,UAAU;MACpC,IAAI,CAAChP,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,uBAAuB,CAAC;;MAG1C;MACA,IAAI,CAACnH,YAAY,CAACkD,IAAI,CAAC,IAAI,CAAC;MAE5B,OAAOgB,IAAI;IACb,CAAC,CAAC,EACFtI,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAiM,OAAOA,CAACZ,MAAc,EAAEa,QAAuB;IAC7C,IAAI,CAACrO,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACb,IAAI,CAAC,UAAU,CAAC;IAErB,OAAO,IAAI,CAAChF,MAAM,CACfkK,MAAM,CAAoB;MACzBC,QAAQ,EAAE1K,iBAAiB;MAC3BkK,SAAS,EAAE;QACT0J,MAAM;QACNa;;KAEH,CAAC,CACDtP,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMvE,IAAI,GAAGuE,MAAM,CAACzE,IAAI,EAAEoP,OAAO;MACjC,IAAI,CAAClP,IAAI,EAAE;QACT,MAAM,IAAIiD,KAAK,CAAC,oBAAoB,CAAC;;MAGvC;MACA,IAAI,CAACwL,WAAW,EAAE;MAElB;MACA,IAAI,CAAC5S,UAAU,CAACmD,IAAI,CAAC,IAAI,CAAC;MAE1B,OAAOgB,IAAI;IACb,CAAC,CAAC,EACFtI,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACoP,WAAW,EAAE;MAClB,OAAOnX,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAmM,WAAWA,CACTd,MAAc,EACde,KAAe,EACfhP,KAAe;IAEf,IAAI,IAAI,CAACrE,WAAW,EAAE;MACpB;MACA,IAAIqT,KAAK,KAAKvR,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACsT,cAAc,EAAE,CAAC1Q,OAAO,CAAE6O,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGF,KAAK;QACvB,CAAC,CAAC;;MAGJ,IAAIhP,KAAK,KAAKvC,SAAS,EAAE;QACvB,IAAI,CAAC9B,WAAW,CAACwT,cAAc,EAAE,CAAC5Q,OAAO,CAAE6O,KAAK,IAAI;UAClDA,KAAK,CAAC8B,OAAO,GAAGlP,KAAK;QACvB,CAAC,CAAC;;;IAIN,OAAO,IAAI,CAACpF,MAAM,CACfkK,MAAM,CAAmC;MACxCC,QAAQ,EAAEzK,0BAA0B;MACpCiK,SAAS,EAAE;QACT0J,MAAM;QACNe,KAAK;QACLhP;;KAEH,CAAC,CACDR,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMkH,OAAO,GAAGlH,MAAM,CAACzE,IAAI,EAAE2P,eAAe;MAC5C,IAAI,CAAChE,OAAO,EAAE;QACZ,MAAM,IAAIxI,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAOwI,OAAO;IAChB,CAAC,CAAC,EACF/T,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAChD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAuL,sBAAsBA,CAACF,MAAc;IACnC,OAAO,IAAI,CAACrT,MAAM,CACfuE,SAAS,CAA6B;MACrCI,KAAK,EAAEhF,wBAAwB;MAC/BgK,SAAS,EAAE;QAAE0J;MAAM;KACpB,CAAC,CACDzO,IAAI,CACHpI,GAAG,CAAC,CAAC;MAAEqI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE4P,UAAU,EAAE;QACrB,MAAM,IAAIzM,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOnD,IAAI,CAAC4P,UAAU;IACxB,CAAC,CAAC,EACF/X,GAAG,CAAEgY,MAAM,IAAI;MACb,IAAI,CAAC5T,WAAW,CAACiD,IAAI,CAAC2Q,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACFjY,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOA6K,cAAcA,CACZQ,MAAc,EACduB,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAAC7U,MAAM,CACfkK,MAAM,CAAkC;MACvCC,QAAQ,EAAE7K,yBAAyB;MACnCqK,SAAS,EAAE;QACT0J,MAAM;QACNuB,UAAU;QACVC;;KAEH,CAAC,CACDjQ,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMkH,OAAO,GAAGlH,MAAM,CAACzE,IAAI,EAAEgO,cAAc;MAC3C,IAAI,CAACrC,OAAO,EAAE;QACZ,MAAM,IAAIxI,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAOwI,OAAO;IAChB,CAAC,CAAC,EACF/T,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQ2M,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAAC1N,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAAC8N,kBAAkB,CAACJ,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACK,YAAY,CAACL,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACM,aAAa,CAACN,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACO,gBAAgB,CAACP,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAACzU,MAAM,CAACuD,KAAK,CAAC,0BAA0BkR,MAAM,CAAC1N,IAAI,EAAE,EAAE0N,MAAM,CAAC;;EAExE;EAEA;;;;EAIQI,kBAAkBA,CAACJ,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAACzT,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAMwO,SAAS,GAAGtP,IAAI,CAACC,KAAK,CAACmR,MAAM,CAAC7P,IAAI,CAAC;MACzC,IAAI,CAAC5D,cAAc,CAChBiU,eAAe,CAAC,IAAIC,eAAe,CAACvC,SAAS,CAAC,CAAC,CAC/ChN,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQ2Q,YAAYA,CAACL,MAAkB;IACrC,IAAI,CAAC,IAAI,CAACzT,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAMyP,MAAM,GAAGvQ,IAAI,CAACC,KAAK,CAACmR,MAAM,CAAC7P,IAAI,CAAC;MACtC,IAAI,CAAC5D,cAAc,CAChByS,oBAAoB,CAAC,IAAIC,qBAAqB,CAACE,MAAM,CAAC,CAAC,CACvDjO,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQ4Q,aAAaA,CAACN,MAAkB;IACtC,IAAI,CAAC7O,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC2N,WAAW,EAAE;IAElB;IACA,MAAM4B,WAAW,GAAG,IAAI,CAACxU,UAAU,CAACuK,KAAK;IACzC,IAAIiK,WAAW,IAAIA,WAAW,CAACvR,EAAE,KAAK6Q,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACzS,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGqR,WAAW;QACdC,MAAM,EAAErY,UAAU,CAACsY,KAAK;QACxBC,OAAO,EAAE,IAAIrN,IAAI,EAAE,CAACpF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQmS,gBAAgBA,CAACP,MAAkB;IACzC,IAAI,CAAC7O,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC2N,WAAW,EAAE;IAElB;IACA,MAAM4B,WAAW,GAAG,IAAI,CAACxU,UAAU,CAACuK,KAAK;IACzC,IAAIiK,WAAW,IAAIA,WAAW,CAACvR,EAAE,KAAK6Q,MAAM,CAACrB,MAAM,EAAE;MACnD,IAAI,CAACzS,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGqR,WAAW;QACdC,MAAM,EAAErY,UAAU,CAACwY,QAAQ;QAC3BD,OAAO,EAAE,IAAIrN,IAAI,EAAE,CAACpF,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQ0Q,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACzS,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACwR,SAAS,EAAE,CAAC5O,OAAO,CAAE6O,KAAK,IAAKA,KAAK,CAAC3M,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC9E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACyC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC9C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACwU,KAAK,EAAE;MAC3B,IAAI,CAACxU,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQqO,iBAAiBA,CAACF,QAAkB;IAC1C,MAAMwD,WAAW,GAA2B;MAC1CtQ,KAAK,EAAE,IAAI;MACXgP,KAAK,EACHlC,QAAQ,KAAKnV,QAAQ,CAACiO,KAAK,GACvB;QACE2K,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAIzZ,UAAU,CAAe2Z,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACP,WAAW,CAAC,CACzBQ,IAAI,CAAE7D,MAAM,IAAI;QACfyD,QAAQ,CAAC/R,IAAI,CAACsO,MAAM,CAAC;QACrByD,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDvQ,KAAK,CAAExB,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzD0R,QAAQ,CAAC1R,KAAK,CAAC,IAAI4D,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQ8K,cAAcA,CAAA;IACpB,OAAO5K,IAAI,CAACC,GAAG,EAAE,CAACiO,QAAQ,EAAE,GAAGvE,IAAI,CAACwE,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAAC1L,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACA4L,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACf9M,IAAA,GAAe,CAAC,EAChBjH,KAAA,GAAgB,EAAE,EAClBgU,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAAC1W,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,2CAA2CoQ,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAU9M,IAAI,WAAWjH,KAAK,YAAYgU,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAMxO,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;IACtB,MAAMyO,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAAC5U,UAAU,CAAC8B,MAAM,GAAG,CAAC,IAC1B0E,GAAG,GAAG,IAAI,CAACxH,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAAC8V,MAAM,IACP9M,IAAI,KAAK,CAAC,IACVjH,KAAK,IAAI,IAAI,CAACd,UAAU,CAAC8B,MAAM;IAEjC;IACA,IAAImT,UAAU,EAAE;MACd,IAAI,CAAC3W,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAC7B,UAAU,CAAC8B,MAAM,SAAS,CACvD;MACD,OAAOrH,EAAE,CAAC,CAAC,GAAG,IAAI,CAACuF,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,2DACE+S,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAACvW,MAAM,CACfmJ,UAAU,CAAM;MACfxE,KAAK,EAAEjH,kBAAkB;MACzBiM,SAAS,EAAE;QACT6M,MAAM;QACN9M,IAAI;QACJjH,KAAK;QACLgU,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAK9T,SAAS,GAAG8T,QAAQ,GAAG;OAC/C;MACDvN,WAAW,EAAEmN,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACDlN,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzB8F,MAAM,CACP;MAED,IAAIA,MAAM,CAACmF,MAAM,EAAE;QACjB,IAAI,CAACxO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCkF,MAAM,CAACmF,MAAM,CACd;QACD,MAAM,IAAIzG,KAAK,CAACsB,MAAM,CAACmF,MAAM,CAACjS,GAAG,CAAEkS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACrF,MAAM,CAACzE,IAAI,EAAEyR,WAAW,EAAE;QAC7B,IAAI,CAACrW,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAMmR,iBAAiB,GAAGvN,MAAM,CAACzE,IAAI,CAACyR,WAAW;MAEjD;MACA,IAAI,CAACrW,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D3B,UAAU,EAAEgV,iBAAiB,CAAChV,UAAU;QACxCC,UAAU,EAAE+U,iBAAiB,CAAC/U,UAAU;QACxCC,WAAW,EAAE8U,iBAAiB,CAAC9U,WAAW;QAC1CC,WAAW,EAAE6U,iBAAiB,CAAC7U,WAAW;QAC1CC,eAAe,EAAE4U,iBAAiB,CAAC5U;OACpC,CAAC;MAEF;MACA,MAAM6U,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAAC5F,IAAI,CAAC,IAAI,CAACrE,aAAa,CAACkK,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAO3S,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCtB,KAAK,CACN;;;MAIL,IAAI,CAACnE,MAAM,CAACkG,IAAI,CACd,gBAAgB,EAChB,YAAY2Q,KAAK,CAACrT,MAAM,4BAA4BoT,iBAAiB,CAAC9U,WAAW,OAAO8U,iBAAiB,CAAC/U,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAAC0U,MAAM,IAAI9M,IAAI,KAAK,CAAC,IAAI,CAACiN,QAAQ,EAAE;QACtC,IAAI,CAAChV,UAAU,GAAG,CAAC,GAAGmV,KAAK,CAAC;QAC5B,IAAI,CAACnW,aAAa,GAAGuH,IAAI,CAACC,GAAG,EAAE;QAC/B,IAAI,CAAClI,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,2BAA2BsT,KAAK,CAACrT,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAAC7B,qBAAqB,GAAG;QAC3BC,UAAU,EAAEgV,iBAAiB,CAAChV,UAAU;QACxCC,UAAU,EAAE+U,iBAAiB,CAAC/U,UAAU;QACxCC,WAAW,EAAE8U,iBAAiB,CAAC9U,WAAW;QAC1CC,WAAW,EAAE6U,iBAAiB,CAAC7U,WAAW;QAC1CC,eAAe,EAAE4U,iBAAiB,CAAC5U;OACpC;MAED,OAAO6U,KAAK;IACd,CAAC,CAAC,EACFra,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAACgL,aAAa,EAAE;QACvB,IAAI,CAACnP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACgL,aAAa,CACpB;;MAGH,IAAIhL,KAAK,CAACiL,YAAY,EAAE;QACtB,IAAI,CAACpP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACiL,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAAC1N,UAAU,CAAC8B,MAAM,GAAG,CAAC,IAC1BiG,IAAI,KAAK,CAAC,IACV,CAAC8M,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAAC1W,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAAC/D,UAAU,CAAC8B,MAAM,kCAAkC,CACtE;QACD,OAAOrH,EAAE,CAAC,CAAC,GAAG,IAAI,CAACuF,UAAU,CAAC,CAAC;;MAGjC,OAAOtF,UAAU,CACf,MACE,IAAI2L,KAAK,CACP,0BAA0B5D,KAAK,CAACuJ,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACAqJ,UAAUA,CAACjK,MAAc;IACvB,OAAO,IAAI,CAAC/M,MAAM,CACfmJ,UAAU,CAAqB;MAC9BxE,KAAK,EAAElH,cAAc;MACrBkM,SAAS,EAAE;QAAE9F,EAAE,EAAEkJ;MAAM,CAAE;MACzB3D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAK,IAAI,CAACuD,aAAa,CAACvD,MAAM,CAACzE,IAAI,EAAEmS,UAAU,CAAC,CAAC,EAC5Dva,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACAiP,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACjX,MAAM,CACfmJ,UAAU,CAAyB;MAClCxE,KAAK,EAAEpG,sBAAsB;MAC7B6K,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAK,IAAI,CAACuD,aAAa,CAACvD,MAAM,CAACzE,IAAI,EAAEoS,cAAc,CAAC,CAAC,EAChExa,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACAkP,aAAaA,CAACnK,MAAc;IAC1B,OAAO,IAAI,CAAC/M,MAAM,CACfkK,MAAM,CAAwB;MAC7BC,QAAQ,EAAErM,wBAAwB;MAClC6L,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEqS,aAAa,EAC7B,MAAM,IAAIlP,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAAC6E,aAAa,CAACvD,MAAM,CAACzE,IAAI,CAACqS,aAAa,CAAC;IACtD,CAAC,CAAC,EACFza,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACAmP,cAAcA,CAACpK,MAAc;IAC3B,OAAO,IAAI,CAAC/M,MAAM,CACfkK,MAAM,CAAyB;MAC9BC,QAAQ,EAAEpM,yBAAyB;MACnC4L,SAAS,EAAE;QAAEoD;MAAM;KACpB,CAAC,CACDnI,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEsS,cAAc,EAC9B,MAAM,IAAInP,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAAC6E,aAAa,CAACvD,MAAM,CAACzE,IAAI,CAACsS,cAAc,CAAC;IACvD,CAAC,CAAC,EACF1a,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACAoP,QAAQA,CAACC,OAAe;IACtB,OAAO,IAAI,CAACrX,MAAM,CACfmJ,UAAU,CAAmB;MAC5BxE,KAAK,EAAE3G,eAAe;MACtB2L,SAAS,EAAE;QAAE9F,EAAE,EAAEwT;MAAO,CAAE;MAC1BjO,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMgO,KAAK,GAAGhO,MAAM,CAACzE,IAAI,EAAEuS,QAAQ;MACnC,IAAI,CAACE,KAAK,EAAE,MAAM,IAAItP,KAAK,CAAC,iBAAiB,CAAC;MAE9C,OAAO;QACL,GAAGsP,KAAK;QACR9J,YAAY,EACV8J,KAAK,CAAC9J,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAAK,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7DqJ,MAAM,EAAED,KAAK,CAACC,MAAM,EAAE/a,GAAG,CAAEgb,CAAC,IAAK,IAAI,CAAC3K,aAAa,CAAC2K,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7DC,SAAS,EAAE,IAAIvP,IAAI,EAAE;QACrBwP,SAAS,EAAE,IAAIxP,IAAI;OACpB;IACH,CAAC,CAAC,EACFzL,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EACA2P,aAAaA,CAAC5K,MAAc;IAC1B,OAAO,IAAI,CAAC/M,MAAM,CACfmJ,UAAU,CAAwB;MACjCxE,KAAK,EAAE1G,qBAAqB;MAC5B0L,SAAS,EAAE;QAAEoD;MAAM,CAAE;MACrB3D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAACzE,IAAI,CAChBpI,GAAG,CACA8M,MAAM,IACLA,MAAM,CAACzE,IAAI,EAAE8S,aAAa,EAAEnb,GAAG,CAAE8a,KAAK,KAAM;MAC1C,GAAGA,KAAK;MACR9J,YAAY,EACV8J,KAAK,CAAC9J,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAAK,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CAAC,IAAI,EAAE;MAC7DqJ,MAAM,EAAED,KAAK,CAACC,MAAM,EAAE/a,GAAG,CAAEgb,CAAC,IAAK,IAAI,CAAC3K,aAAa,CAAC2K,CAAC,CAAC,CAAC,IAAI,EAAE;MAC7DC,SAAS,EAAE,IAAIvP,IAAI,EAAE;MACrBwP,SAAS,EAAE,IAAIxP,IAAI;KACpB,CAAC,CAAC,IAAI,EAAE,CACZ,EACDzL,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACA4P,WAAWA,CACT1S,IAAY,EACZ+I,cAAwB,EACxB4J,KAAY,EACZC,WAAoB;IAEpB,MAAMnO,SAAS,GAAGkO,KAAK,GACnB;MAAE3S,IAAI;MAAE+I,cAAc;MAAE4J,KAAK;MAAEC;IAAW,CAAE,GAC5C;MAAE5S,IAAI;MAAE+I,cAAc;MAAE6J;IAAW,CAAE;IACzC,MAAMxM,OAAO,GAAGuM,KAAK,GAAG;MAAEtM,YAAY,EAAE,IAAI;MAAEjB,IAAI,EAAEuN;IAAK,CAAE,GAAGhV,SAAS;IAEvE,OAAO,IAAI,CAAC7C,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAEjM,qBAAqB;MAC/ByL,SAAS;MACT2B,OAAO;MACPyM,cAAc,EAAE,CACd;QACEpT,KAAK,EAAE1G,qBAAqB;QAC5B0L,SAAS,EAAE;UAAEoD,MAAM,EAAE,IAAI,CAAClB,gBAAgB;QAAE;OAC7C;KAEJ,CAAC,CACDjH,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAE+S,WAAW,EAC3B,MAAM,IAAI5P,KAAK,CAAC,wBAAwB,CAAC;MAC3C,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAAC+S,WAAW;QAC1BpK,YAAY,EACVlE,MAAM,CAACzE,IAAI,CAAC+S,WAAW,CAACpK,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAC1C,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CACtB,IAAI,EAAE;QACTqJ,MAAM,EACJjO,MAAM,CAACzE,IAAI,CAAC+S,WAAW,CAACL,MAAM,EAAE/a,GAAG,CAAEgb,CAAC,IACpC,IAAI,CAAC3K,aAAa,CAAC2K,CAAC,CAAC,CACtB,IAAI;OACR;IACH,CAAC,CAAC,EACF/a,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EACAgQ,WAAWA,CACTX,OAAe,EACfY,KAQC;IAED,MAAM3M,OAAO,GAAG2M,KAAK,CAACJ,KAAK,GACvB;MAAEtM,YAAY,EAAE,IAAI;MAAEjB,IAAI,EAAE2N,KAAK,CAACJ;IAAK,CAAE,GACzChV,SAAS;IACb,MAAM;MAAEgV,KAAK;MAAE,GAAGK;IAAiB,CAAE,GAAGD,KAAK;IAE7C,OAAO,IAAI,CAACjY,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAEhM,qBAAqB;MAC/BwL,SAAS,EAAE;QAAE9F,EAAE,EAAEwT,OAAO;QAAEY,KAAK,EAAEC;MAAiB,CAAE;MACpD5M,OAAO;MACPyM,cAAc,EAAE,CACd;QAAEpT,KAAK,EAAE3G,eAAe;QAAE2L,SAAS,EAAE;UAAE9F,EAAE,EAAEwT;QAAO;MAAE,CAAE,EACtD;QACE1S,KAAK,EAAE1G,qBAAqB;QAC5B0L,SAAS,EAAE;UAAEoD,MAAM,EAAE,IAAI,CAAClB,gBAAgB;QAAE;OAC7C;KAEJ,CAAC,CACDjH,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACzE,IAAI,EAAEmT,WAAW,EAC3B,MAAM,IAAIhQ,KAAK,CAAC,wBAAwB,CAAC;MAC3C,OAAO;QACL,GAAGsB,MAAM,CAACzE,IAAI,CAACmT,WAAW;QAC1BxK,YAAY,EACVlE,MAAM,CAACzE,IAAI,CAACmT,WAAW,CAACxK,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAC1C,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CACtB,IAAI,EAAE;QACTqJ,MAAM,EACJjO,MAAM,CAACzE,IAAI,CAACmT,WAAW,CAACT,MAAM,EAAE/a,GAAG,CAAEgb,CAAC,IACpC,IAAI,CAAC3K,aAAa,CAAC2K,CAAC,CAAC,CACtB,IAAI;OACR;IACH,CAAC,CAAC,EACF/a,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EACAmQ,sBAAsBA,CAACrQ,cAAsB;IAC3C;IACA,IAAI,CAAC,IAAI,CAACsQ,YAAY,EAAE,EAAE;MACxB,IAAI,CAACnY,MAAM,CAACyF,IAAI,CACd,sEAAsE,CACvE;MACD,OAAOtJ,EAAE,CAAC,IAA0B,CAAC;;IAGvC,IAAI,CAAC6D,MAAM,CAACuD,KAAK,CACf,yEAAyEsE,cAAc,EAAE,CAC1F;IAED,MAAMuQ,IAAI,GAAG,IAAI,CAACrY,MAAM,CACrBuE,SAAS,CAA2B;MACnCI,KAAK,EAAEpH,yBAAyB;MAChCoM,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMO,GAAG,GAAGP,MAAM,CAACzE,IAAI,EAAEyT,WAAW;MACpC,IAAI,CAACzO,GAAG,EAAE;QACR,IAAI,CAAC5J,MAAM,CAACyF,IAAI,CAAC,6BAA6B,CAAC;QAC/C,MAAM,IAAIsC,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,IAAI,CAAC6B,GAAG,CAAChG,EAAE,IAAI,CAACgG,GAAG,CAACsE,GAAG,EAAE;QACvB,IAAI,CAAClO,MAAM,CAACyF,IAAI,CAAC,8BAA8B,EAAEmE,GAAG,CAAC;QACrD;QACAA,GAAG,CAAChG,EAAE,GAAG,QAAQqE,IAAI,CAACC,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMqD,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAACD,GAAG,CAAC;QAEpD;QACA,IACE2B,iBAAiB,CAACxE,IAAI,KAAKlK,WAAW,CAACkO,KAAK,IAC3CQ,iBAAiB,CAAC+M,WAAW,IAC5B/M,iBAAiB,CAAC+M,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAACzR,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAAC/G,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,qCAAqC,EACrCgI,iBAAiB,CAClB;UAED;UACA,IAAI,CAACkN,gCAAgC,CACnC5Q,cAAc,EACd0D,iBAAiB,CAClB;;QAGH,OAAOA,iBAAiB;OACzB,CAAC,OAAO/D,GAAG,EAAE;QACZ,IAAI,CAACxH,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEqD,GAAG,CAAC;QAEpD;QACA,MAAMiE,cAAc,GAAY;UAC9B7H,EAAE,EAAEgG,GAAG,CAAChG,EAAE,IAAIgG,GAAG,CAACsE,GAAG,IAAI,QAAQjG,IAAI,CAACC,GAAG,EAAE,EAAE;UAC7CkC,OAAO,EAAER,GAAG,CAACQ,OAAO,IAAI,EAAE;UAC1BrD,IAAI,EAAE6C,GAAG,CAAC7C,IAAI,IAAIlK,WAAW,CAAC0N,IAAI;UAClCvC,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAAC/C,GAAG,CAAC5B,SAAS,CAAC;UACvC0D,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE/B,GAAG,CAAC+B,MAAM,GACd,IAAI,CAACiB,aAAa,CAAChD,GAAG,CAAC+B,MAAM,CAAC,GAC9B;YACE/H,EAAE,EAAE,IAAI,CAACgI,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEjB;QAED,OAAOJ,cAAc;;IAEzB,CAAC,CAAC,EACFjP,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAO7H,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEgR,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACArR,KAAK,CAAC,CAAC,CAAC,CACT;IAEH,MAAMqc,GAAG,GAAGN,IAAI,CAAC9T,SAAS,CAAC;MACzBR,IAAI,EAAG4J,OAAO,IAAI;QAChB;QACA,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEmK,OAAO,CAAC;QAErE;QACA,IAAI,CAAC+K,gCAAgC,CAAC5Q,cAAc,EAAE6F,OAAO,CAAC;MAChE,CAAC;MACDvJ,KAAK,EAAGqD,GAAG,IAAI;QACb,IAAI,CAACxH,MAAM,CAACmE,KAAK,CAAC,gCAAgC,EAAEqD,GAAG,CAAC;MAC1D;KACD,CAAC;IAEF,IAAI,CAAChH,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EAEA;;;;;EAKQK,gCAAgCA,CACtC5Q,cAAsB,EACtB6F,OAAgB;IAEhB;IACA,IAAI,CAACN,eAAe,CAACvF,cAAc,CAAC,CAACvD,SAAS,CAAC;MAC7CR,IAAI,EAAG2J,YAAY,IAAI;QACrB,IAAI,CAACzN,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgBsE,cAAc,+BAC5B6F,OAAO,CAAC9J,EACV,SAAS6J,YAAY,EAAE9D,QAAQ,EAAEnG,MAAM,IAAI,CAAC,WAAW,CACxD;QAED;QACA,IAAI,CAACtD,kBAAkB,CAAC4D,IAAI,CAAC+D,cAAc,CAAC;MAC9C,CAAC;MACD1D,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC0D,cAAc,GAAG,EAClD1D,KAAK,CACN;MACH;KACD,CAAC;EACJ;EACAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAAC0T,YAAY,EAAE,EAAE;MACxB,IAAI,CAACnY,MAAM,CAACyF,IAAI,CACd,+EAA+E,CAChF;MACD,OAAOrJ,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC/H,MAAM,CAACuD,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAM6U,IAAI,GAAG,IAAI,CAACrY,MAAM,CACrBuE,SAAS,CAA8B;MACtCI,KAAK,EAAEnH;KACR,CAAC,CACDoH,IAAI,CACHlI,GAAG,CAAE4M,MAAM,IACT,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,uDAAuD,EACvD8F,MAAM,CACP,CACF,EACD9M,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMyN,IAAI,GAAGzN,MAAM,CAACzE,IAAI,EAAE+T,iBAAiB;MAC3C,IAAI,CAAC7B,IAAI,EAAE;QACT,IAAI,CAAC9W,MAAM,CAACmE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAAC6E,aAAa,CAACkK,IAAI,CAAC;IACjC,CAAC,CAAC,EACFta,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF1L,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMqc,GAAG,GAAGN,IAAI,CAAC9T,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAQ,8BAA8BA,CAC5B/Q,cAAsB;IAEtB,MAAMuQ,IAAI,GAAG,IAAI,CAACrY,MAAM,CACrBuE,SAAS,CAAwC;MAChDI,KAAK,EAAEhH,iCAAiC;MACxCgM,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAM6D,IAAI,GAAG7D,MAAM,CAACzE,IAAI,EAAEiU,mBAAmB;MAC7C,IAAI,CAAC3L,IAAI,EAAE,MAAM,IAAInF,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAMuF,sBAAsB,GAAiB;QAC3C,GAAGJ,IAAI;QACPK,YAAY,EACVL,IAAI,CAACK,YAAY,EAAEhR,GAAG,CAAE0R,CAAC,IAAK,IAAI,CAACrB,aAAa,CAACqB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D6K,WAAW,EAAE5L,IAAI,CAAC4L,WAAW,GACzB;UACE,GAAG5L,IAAI,CAAC4L,WAAW;UACnBnN,MAAM,EAAE,IAAI,CAACiB,aAAa,CAACM,IAAI,CAAC4L,WAAW,CAACnN,MAAM,CAAC;UACnD3D,SAAS,EAAE,IAAI,CAAC2E,QAAQ,CAACO,IAAI,CAAC4L,WAAW,CAAC9Q,SAAS,CAAC;UACpD+D,MAAM,EAAEmB,IAAI,CAAC4L,WAAW,CAAC/M,MAAM,GAC3B,IAAI,CAACY,QAAQ,CAACO,IAAI,CAAC4L,WAAW,CAAC/M,MAAM,CAAC,GACtCnJ,SAAS;UACb;UACAgB,EAAE,EAAEsJ,IAAI,CAAC4L,WAAW,CAAClV,EAAE;UACvBwG,OAAO,EAAE8C,IAAI,CAAC4L,WAAW,CAAC1O,OAAO;UACjCrD,IAAI,EAAEmG,IAAI,CAAC4L,WAAW,CAAC/R,IAAI;UAC3B2E,MAAM,EAAEwB,IAAI,CAAC4L,WAAW,CAACpN;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAO4B,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACF9Q,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAM2Q,GAAG,GAAGN,IAAI,CAAC9T,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACAW,0BAA0BA,CACxBlR,cAAsB;IAEtB,MAAMuQ,IAAI,GAAG,IAAI,CAACrY,MAAM,CACrBuE,SAAS,CAAwB;MAChCI,KAAK,EAAErG,6BAA6B;MACpCqL,SAAS,EAAE;QAAE7B;MAAc;KAC5B,CAAC,CACDlD,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEoU,eAAe,CAAC,EAC7Ctc,MAAM,CAACuc,OAAO,CAAC,EACfzc,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAM2Q,GAAG,GAAGN,IAAI,CAAC9T,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACQD,YAAYA,CAAA;IAClB,MAAMzN,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACsH,KAAK,EAAE;MACV,IAAI,CAAC1K,MAAM,CAACyF,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAMyT,KAAK,GAAGxO,KAAK,CAACyO,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAAC1V,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAM2T,OAAO,GAAG/V,IAAI,CAACC,KAAK,CAAC+V,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAACtZ,MAAM,CAACyF,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM8T,cAAc,GAAG,IAAItR,IAAI,CAACmR,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAMpR,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAIsR,cAAc,GAAGrR,GAAG,EAAE;QACxB,IAAI,CAAClI,MAAM,CAACyF,IAAI,CAAC,cAAc,EAAE;UAC/B+T,UAAU,EAAED,cAAc,CAAC1W,WAAW,EAAE;UACxCqF,GAAG,EAAEA,GAAG,CAACrF,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC4T,YAAY,EAAE,EAAE;MACxB,IAAI,CAACnY,MAAM,CAACyF,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAOtJ,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAAC6D,MAAM,CAACuD,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAM6U,IAAI,GAAG,IAAI,CAACrY,MAAM,CACrBuE,SAAS,CAAyB;MACjCI,KAAK,EAAE3F;KACR,CAAC,CACD4F,IAAI,CACHlI,GAAG,CAAE4M,MAAM,IACT,IAAI,CAACrJ,MAAM,CAACuD,KAAK,CACf,wDAAwD,EACxD8F,MAAM,CACP,CACF,EACD9M,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAMwH,eAAe,GAAGxH,MAAM,CAACzE,IAAI,EAAE6U,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACzZ,MAAM,CAACuD,KAAK,CACf,oCAAoC,EACpCsN,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACFrU,UAAU,CAAEgL,GAAG,IAAI;MACjB,IAAI,CAACxH,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCqD,GAAY,CACb;MACD;MACA,OAAOrL,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMqc,GAAG,GAAGN,IAAI,CAAC9T,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAON,IAAI;EACb;EACA/T,2BAA2BA,CAAA;IACzB;IACA,MAAMqG,KAAK,GAAGvH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACsH,KAAK,EAAE;MACV,IAAI,CAAC1K,MAAM,CAACyF,IAAI,CACd,6DAA6D,CAC9D;MACD;MACA,OAAOnJ,KAAK;;IAGd,MAAMod,OAAO,GAAG,IAAI,CAAC3Z,MAAM,CAACuE,SAAS,CAA4B;MAC/DI,KAAK,EAAExH;KACR,CAAC;IAEF,MAAMyc,UAAU,GAAGD,OAAO,CAAC/U,IAAI,CAC7BpI,GAAG,CAAE8M,MAAM,IAAI;MACb,MAAM1F,YAAY,GAAG0F,MAAM,CAACzE,IAAI,EAAEgV,oBAAoB;MACtD,IAAI,CAACjW,YAAY,EAAE;QACjB,MAAM,IAAIoE,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,MAAM8R,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACnW,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACvD,iBAAiB,CAAC0O,GAAG,CAAC+K,UAAU,CAACjW,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC5D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgBsW,UAAU,CAACjW,EAAE,oCAAoC,CAClE;QACD;QACA,MAAM,IAAImE,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC3B,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAAC2T,uBAAuB,CAACF,UAAU,CAAC;MAExC,IAAI,CAAC7Z,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,yCAAyC,EACzCsW,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACArd,UAAU,CAAEgL,GAAG,IAAI;MACjB;MACA,IACEA,GAAG,YAAYO,KAAK,IACpBP,GAAG,CAACkG,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOpR,KAAK;;MAGd,IAAI,CAAC0D,MAAM,CAACmE,KAAK,CAAC,sCAAsC,EAAEqD,GAAY,CAAC;MACvE;MACA,OAAOlL,KAAK;IACd,CAAC,CAAC,CACH;IAED,MAAMoc,GAAG,GAAGiB,UAAU,CAACrV,SAAS,CAAC;MAC/BR,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAAC3D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,wCAAwC,EACxCI,YAAY,CACb;MACH,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,oCAAoC,EACpCA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3D,aAAa,CAACyQ,IAAI,CAACyH,GAAG,CAAC;IAC5B,OAAOiB,UAAU;EACnB;EACA;EACA;EACA;EAEQ3W,oBAAoBA,CAAA;IAC1B,IAAI,CAACgX,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAMhS,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMkS,aAAa,GAAG,IAAIlS,IAAI,CAACC,GAAG,CAACkS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACja,iBAAiB,CAACsD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAM0W,gBAAgB,GAAG,IAAIrS,IAAI,CAACtE,YAAY,CAACqE,SAAS,CAAC;MACzD,IAAIsS,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAAC/Z,iBAAiB,CAACiQ,MAAM,CAACzM,EAAE,CAAC;QACjCyW,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACra,MAAM,CAACuD,KAAK,CAAC,cAAc8W,YAAY,wBAAwB,CAAC;MACrE,IAAI,CAACla,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;MACpE,IAAI,CAACC,iBAAiB,EAAE;;EAE5B;EACQ2H,gBAAgBA,CAAA;IACtB,OAAOzI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQyG,gBAAgBA,CAAC6D,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC1N,MAAM,CAACmE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI4D,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAAC2F,OAAO,CAAC9J,EAAE,IAAI,CAAC8J,OAAO,CAACQ,GAAG,EAAE;QAC/B,IAAI,CAAClO,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCvB,SAAS,EACT8K,OAAO,CACR;QACD,MAAM,IAAI3F,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAIwS,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAG7M,OAAO,CAAC/B,MAAM,GAC7B,IAAI,CAACiB,aAAa,CAACc,OAAO,CAAC/B,MAAM,CAAC,GAClC/I,SAAS;OACd,CAAC,OAAOuB,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,yEAAyE,EACzEtB,KAAK,CACN;QACDoW,gBAAgB,GAAG;UACjBrM,GAAG,EAAER,OAAO,CAAClE,QAAQ,IAAI,SAAS;UAClC5F,EAAE,EAAE8J,OAAO,CAAClE,QAAQ,IAAI,SAAS;UACjCqC,QAAQ,EAAE,cAAc;UACxB2O,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAIjN,OAAO,CAACkN,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAAC/N,aAAa,CAACc,OAAO,CAACkN,QAAQ,CAAC;SAC1D,CAAC,OAAOzW,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,2EAA2E,EAC3EtB,KAAK,CACN;UACDwW,kBAAkB,GAAG;YACnBzM,GAAG,EAAER,OAAO,CAAC/F,UAAU,IAAI,SAAS;YACpC/D,EAAE,EAAE8J,OAAO,CAAC/F,UAAU,IAAI,SAAS;YACnCkE,QAAQ,EAAE,cAAc;YACxB2O,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzBnN,OAAO,CAAC4K,WAAW,EAAE/b,GAAG,CAAEic,GAAG,KAAM;QACjC5U,EAAE,EAAE4U,GAAG,CAAC5U,EAAE,IAAI4U,GAAG,CAACtK,GAAG,IAAI,cAAcjG,IAAI,CAACC,GAAG,EAAE,EAAE;QACnD4S,GAAG,EAAEtC,GAAG,CAACsC,GAAG,IAAI,EAAE;QAClB/T,IAAI,EAAEyR,GAAG,CAACzR,IAAI,IAAI,SAAS;QAC3B9B,IAAI,EAAEuT,GAAG,CAACvT,IAAI,IAAI,YAAY;QAC9Bf,IAAI,EAAEsU,GAAG,CAACtU,IAAI,IAAI,CAAC;QACnB4D,QAAQ,EAAE0Q,GAAG,CAAC1Q,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAMyD,iBAAiB,GAAG;QACxB,GAAGmC,OAAO;QACVQ,GAAG,EAAER,OAAO,CAAC9J,EAAE,IAAI8J,OAAO,CAACQ,GAAG;QAC9BtK,EAAE,EAAE8J,OAAO,CAAC9J,EAAE,IAAI8J,OAAO,CAACQ,GAAG;QAC7B9D,OAAO,EAAEsD,OAAO,CAACtD,OAAO,IAAI,EAAE;QAC9BuB,MAAM,EAAE4O,gBAAgB;QACxBvS,SAAS,EAAE,IAAI,CAACoE,aAAa,CAACsB,OAAO,CAAC1F,SAAS,CAAC;QAChD+D,MAAM,EAAE2B,OAAO,CAAC3B,MAAM,GAAG,IAAI,CAACK,aAAa,CAACsB,OAAO,CAAC3B,MAAM,CAAC,GAAGnJ,SAAS;QACvE0V,WAAW,EAAEuC,qBAAqB;QAClCvS,QAAQ,EAAEoF,OAAO,CAACpF,QAAQ,IAAI;OAC/B;MAED;MACA,IAAIqS,kBAAkB,EAAE;QACtBpP,iBAAiB,CAACqP,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAAC3a,MAAM,CAACuD,KAAK,CAAC,kDAAkD,EAAE;QACpEwG,SAAS,EAAEwB,iBAAiB,CAAC3H,EAAE;QAC/B4F,QAAQ,EAAE+B,iBAAiB,CAACI,MAAM,EAAE/H;OACrC,CAAC;MAEF,OAAO2H,iBAAiB;KACzB,CAAC,OAAOpH,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAACgT,MAAM,CAAC5W,KAAK,CAAC,CAAC,EACzDuJ,OAAO,CACR;MACD,MAAM,IAAI3F,KAAK,CACb,gCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAACuJ,OAAO,GAAGqN,MAAM,CAAC5W,KAAK,CACvD,EAAE,CACH;;EAEL;EACQ6W,mBAAmBA,CAACtN,OAAY;IACtC,OAAO;MACL,GAAGA,OAAO;MACV,IAAIA,OAAO,CAAC4K,WAAW,IAAI;QACzBA,WAAW,EAAE5K,OAAO,CAAC4K,WAAW,CAAC/b,GAAG,CAAEic,GAAQ,KAAM;UAClDsC,GAAG,EAAEtC,GAAG,CAACsC,GAAG;UACZ/T,IAAI,EAAEyR,GAAG,CAACzR,IAAI;UACd,IAAIyR,GAAG,CAACvT,IAAI,IAAI;YAAEA,IAAI,EAAEuT,GAAG,CAACvT;UAAI,CAAE,CAAC;UACnC,IAAIuT,GAAG,CAACtU,IAAI,IAAI;YAAEA,IAAI,EAAEsU,GAAG,CAACtU;UAAI,CAAE;SACnC,CAAC;OACH;KACF;EACH;EACO0I,aAAaA,CAACkK,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAI/O,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAM+E,MAAM,GAAGgK,IAAI,CAAClT,EAAE,IAAIkT,IAAI,CAAC5I,GAAG;IAClC,IAAI,CAACpB,MAAM,EAAE;MACX,MAAM,IAAI/E,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAM8D,QAAQ,GAAGiL,IAAI,CAACjL,QAAQ,IAAI,cAAc;IAChD,MAAM2O,KAAK,GAAG1D,IAAI,CAAC0D,KAAK,IAAI,QAAQ1N,MAAM,cAAc;IACxD,MAAM4N,QAAQ,GACZ5D,IAAI,CAAC4D,QAAQ,KAAK9X,SAAS,IAAIkU,IAAI,CAAC4D,QAAQ,KAAK,IAAI,GACjD5D,IAAI,CAAC4D,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAG3D,IAAI,CAAC2D,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACLvM,GAAG,EAAEpB,MAAM;MACXlJ,EAAE,EAAEkJ,MAAM;MACVjB,QAAQ,EAAEA,QAAQ;MAClB2O,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAEnE,IAAI,CAACmE,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEpE,IAAI,CAACoE,GAAG;MACbxE,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChCyE,UAAU,EAAErE,IAAI,CAACqE,UAAU,GAAG,IAAIlT,IAAI,CAAC6O,IAAI,CAACqE,UAAU,CAAC,GAAGvY,SAAS;MACnE4U,SAAS,EAAEV,IAAI,CAACU,SAAS,GAAG,IAAIvP,IAAI,CAAC6O,IAAI,CAACU,SAAS,CAAC,GAAG5U,SAAS;MAChE6U,SAAS,EAAEX,IAAI,CAACW,SAAS,GAAG,IAAIxP,IAAI,CAAC6O,IAAI,CAACW,SAAS,CAAC,GAAG7U,SAAS;MAChEwY,cAAc,EAAEtE,IAAI,CAACsE,cAAc;MACnCC,cAAc,EAAEvE,IAAI,CAACuE,cAAc;MACnCC,SAAS,EAAExE,IAAI,CAACwE;KACjB;EACH;EACQnO,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAClN,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI4D,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACmF,IAAI,CAACtJ,EAAE,IAAI,CAACsJ,IAAI,CAACgB,GAAG,EAAE;QACzB,IAAI,CAAClO,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CvB,SAAS,EACTsK,IAAI,CACL;QACD,MAAM,IAAInF,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMwT,sBAAsB,GAAG,EAAE;MACjC,IAAIrO,IAAI,CAACK,YAAY,IAAIxJ,KAAK,CAACyX,OAAO,CAACtO,IAAI,CAACK,YAAY,CAAC,EAAE;QACzD,KAAK,MAAMkO,WAAW,IAAIvO,IAAI,CAACK,YAAY,EAAE;UAC3C,IAAI;YACF,IAAIkO,WAAW,EAAE;cACfF,sBAAsB,CAACtK,IAAI,CAAC,IAAI,CAACrE,aAAa,CAAC6O,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAOtX,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,0DAA0D,EAC1DtB,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,iFAAiF,EACjFyH,IAAI,CACL;;MAGH;MACA,MAAMwO,kBAAkB,GAAG,EAAE;MAC7B,IAAIxO,IAAI,CAACvD,QAAQ,IAAI5F,KAAK,CAACyX,OAAO,CAACtO,IAAI,CAACvD,QAAQ,CAAC,EAAE;QACjD,IAAI,CAAC3J,MAAM,CAACuD,KAAK,CAAC,mDAAmD,EAAE;UACrEoN,KAAK,EAAEzD,IAAI,CAACvD,QAAQ,CAACnG;SACtB,CAAC;QAEF,KAAK,MAAMkK,OAAO,IAAIR,IAAI,CAACvD,QAAQ,EAAE;UACnC,IAAI;YACF,IAAI+D,OAAO,EAAE;cACX,MAAMnC,iBAAiB,GAAG,IAAI,CAAC1B,gBAAgB,CAAC6D,OAAO,CAAC;cACxD,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CACf,kDAAkD,EAClD;gBACEwG,SAAS,EAAEwB,iBAAiB,CAAC3H,EAAE;gBAC/BwG,OAAO,EAAEmB,iBAAiB,CAACnB,OAAO,EAAEK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDkB,MAAM,EAAEJ,iBAAiB,CAACI,MAAM,EAAEE;eACnC,CACF;cACD6P,kBAAkB,CAACzK,IAAI,CAAC1F,iBAAiB,CAAC;;WAE7C,CAAC,OAAOpH,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,sEAAsE,EACtEtB,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACuD,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAIoY,qBAAqB,GAAG,IAAI;MAChC,IAAIzO,IAAI,CAAC4L,WAAW,EAAE;QACpB,IAAI;UACF6C,qBAAqB,GAAG,IAAI,CAAC9R,gBAAgB,CAACqD,IAAI,CAAC4L,WAAW,CAAC;SAChE,CAAC,OAAO3U,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CACd,6DAA6D,EAC7DtB,KAAK,CACN;;;MAIL;MACA,MAAMmJ,sBAAsB,GAAG;QAC7B,GAAGJ,IAAI;QACPgB,GAAG,EAAEhB,IAAI,CAACtJ,EAAE,IAAIsJ,IAAI,CAACgB,GAAG;QACxBtK,EAAE,EAAEsJ,IAAI,CAACtJ,EAAE,IAAIsJ,IAAI,CAACgB,GAAG;QACvBX,YAAY,EAAEgO,sBAAsB;QACpC5R,QAAQ,EAAE+R,kBAAkB;QAC5B5C,WAAW,EAAE6C,qBAAqB;QAClCC,WAAW,EAAE1O,IAAI,CAAC0O,WAAW,IAAI,CAAC;QAClC7N,OAAO,EAAE,CAAC,CAACb,IAAI,CAACa,OAAO;QACvByJ,SAAS,EAAE,IAAI,CAACpL,aAAa,CAACc,IAAI,CAACsK,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAACrL,aAAa,CAACc,IAAI,CAACuK,SAAS;OAC7C;MAED,IAAI,CAACzX,MAAM,CAACuD,KAAK,CACf,uDAAuD,EACvD;QACEsE,cAAc,EAAEyF,sBAAsB,CAAC1J,EAAE;QACzCiY,gBAAgB,EAAEN,sBAAsB,CAAC/X,MAAM;QAC/CsY,YAAY,EAAEJ,kBAAkB,CAAClY;OAClC,CACF;MAED,OAAO8J,sBAAsB;KAC9B,CAAC,OAAOnJ,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,GAAG,IAAI4D,KAAK,CAACgT,MAAM,CAAC5W,KAAK,CAAC,CAAC,EACzD+I,IAAI,CACL;MACD,MAAM,IAAInF,KAAK,CACb,qCACE5D,KAAK,YAAY4D,KAAK,GAAG5D,KAAK,CAACuJ,OAAO,GAAGqN,MAAM,CAAC5W,KAAK,CACvD,EAAE,CACH;;EAEL;EACQiI,aAAaA,CAACzJ,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtF,IAAI,KAAK,QAAQ,GAAG,IAAIsF,IAAI,CAACtF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CAAC,yBAAyB9C,IAAI,EAAE,EAAEwB,KAAK,CAAC;MACxD,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAEA;EACQ0E,QAAQA,CAAChK,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsF,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtF,IAAI,KAAK,QAAQ,GAAG,IAAIsF,IAAI,CAACtF,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOwB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACyF,IAAI,CAAC,+BAA+B9C,IAAI,EAAE,EAAEwB,KAAK,CAAC;MAC9D,OAAO,IAAI8D,IAAI,EAAE;;EAErB;EAOQ6R,qBAAqBA,CAACnW,YAA0B;IACtD,IAAI,CAAC3D,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BI,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAAC3D,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI4D,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMmI,cAAc,GAAGvM,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAACuK,GAAG;IACnE,IAAI,CAACgC,cAAc,EAAE;MACnB,IAAI,CAAClQ,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BR,YAAY,CACb;MACD,MAAM,IAAIoE,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACpE,YAAY,CAACqE,SAAS,EAAE;MAC3B,IAAI,CAAChI,MAAM,CAACyF,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvD9B,YAAY,CACb;MACDA,YAAY,CAACqE,SAAS,GAAG,IAAIC,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAM4R,UAAU,GAAG;QACjB,GAAGlW,YAAY;QACfuK,GAAG,EAAEgC,cAAc;QACnBtM,EAAE,EAAEsM,cAAc;QAClBlI,SAAS,EAAE,IAAIC,IAAI,CAACtE,YAAY,CAACqE,SAAS,CAAC;QAC3C,IAAIrE,YAAY,CAAC6F,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACuS,eAAe,CAACpY,YAAY,CAAC6F,QAAQ;SACrD,CAAC;QACF,IAAI7F,YAAY,CAAC+J,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAACsN,mBAAmB,CAACrX,YAAY,CAAC+J,OAAO;SACvD;OACF;MAED,IAAI,CAAC1N,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCsW,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAO1V,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQ4X,eAAeA,CAACpQ,MAAW;IACjC,OAAO;MACL/H,EAAE,EAAE+H,MAAM,CAAC/H,EAAE;MACbiI,QAAQ,EAAEF,MAAM,CAACE,QAAQ;MACzB,IAAIF,MAAM,CAACsP,KAAK,IAAI;QAAEA,KAAK,EAAEtP,MAAM,CAACsP;MAAK,CAAE;KAC5C;EACH;EACQjM,WAAWA,CAAC7O,aAA6B;IAC/C,IAAI,CAACH,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,oCAAoCpD,aAAa,CAACqD,MAAM,gBAAgB,CACzE;IAED,IAAIrD,aAAa,CAACqD,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACxD,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGFY,OAAO,CAACC,GAAG,CACT,iCAAiCnG,aAAa,CAACqD,MAAM,gBAAgB,CACtE;IAED;IACA,MAAMwY,kBAAkB,GAAG7b,aAAa,CAACzD,MAAM,CAC5CmS,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG,CAAC,CACrD;IAED,IAAI8N,kBAAkB,CAACxY,MAAM,KAAKrD,aAAa,CAACqD,MAAM,EAAE;MACtD6C,OAAO,CAACZ,IAAI,CACV,SACEtF,aAAa,CAACqD,MAAM,GAAGwY,kBAAkB,CAACxY,MAC5C,kCAAkC,CACnC;;IAGH;IACAwY,kBAAkB,CAACtY,OAAO,CAAC,CAACmL,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAMkN,OAAO,GAAGpN,KAAK,CAACjL,EAAE,IAAKiL,KAAa,CAACX,GAAG;QAC9C,IAAI,CAAC+N,OAAO,EAAE;UACZ5V,OAAO,CAAClC,KAAK,CAAC,0BAA0B,EAAE0K,KAAK,CAAC;UAChD;;QAGF;QACA,MAAMgL,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACjL,KAAK,CAAC;QAEpD;QACA,IAAI,IAAI,CAACzO,iBAAiB,CAAC0O,GAAG,CAAC+K,UAAU,CAACjW,EAAE,CAAC,EAAE;UAC7CyC,OAAO,CAACC,GAAG,CACT,gBAAgBuT,UAAU,CAACjW,EAAE,oCAAoC,CAClE;UACD;;QAGF;QACA,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACgW,UAAU,CAACjW,EAAE,EAAEiW,UAAU,CAAC;QAErDxT,OAAO,CAACC,GAAG,CAAC,sBAAsBuT,UAAU,CAACjW,EAAE,WAAW,CAAC;OAC5D,CAAC,OAAOO,KAAK,EAAE;QACdkC,OAAO,CAAClC,KAAK,CAAC,iCAAiC4K,KAAK,GAAG,CAAC,GAAG,EAAE5K,KAAK,CAAC;QACnEkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAE0K,KAAK,CAAC;;IAErD,CAAC,CAAC;IAEFxI,OAAO,CAACC,GAAG,CACT,4CAA4C,IAAI,CAAClG,iBAAiB,CAAC8D,IAAI,gBAAgB,CACxF;IAED;IACA,IAAI,CAACgL,+BAA+B,EAAE;EACxC;EACQjL,iBAAiBA,CAAA;IACvB,MAAM0M,KAAK,GAAG5M,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAACtH,MAAM,CAC7D8S,CAAC,IAAK,CAACA,CAAC,CAAC9D,MAAM,CACjB,CAAClI,MAAM;IACR,IAAI,CAAClD,iBAAiB,CAACwD,IAAI,CAAC6M,KAAK,CAAC;EACpC;EACQoJ,uBAAuBA,CAACpW,YAA0B;IACxD;IACA,IAAI,CAAC,IAAI,CAACvD,iBAAiB,CAAC0O,GAAG,CAACnL,YAAY,CAACC,EAAE,CAAC,EAAE;MAChD,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;MACzD,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;MACpE,IAAI,CAACC,iBAAiB,EAAE;MACxB;MACA,IAAI,CAACiL,+BAA+B,EAAE;KACvC,MAAM;MACL,IAAI,CAAClP,MAAM,CAACuD,KAAK,CACf,gBAAgB,EAChB,gBAAgBI,YAAY,CAACC,EAAE,oCAAoC,CACpE;;EAEL;EACQ6N,wBAAwBA,CAACyK,GAAa,EAAExQ,MAAe;IAC7DwQ,GAAG,CAACxY,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMiL,KAAK,GAAG,IAAI,CAACzO,iBAAiB,CAAC4Q,GAAG,CAACpN,EAAE,CAAC;MAC5C,IAAIiL,KAAK,EAAE;QACT,IAAI,CAACzO,iBAAiB,CAACyD,GAAG,CAACD,EAAE,EAAE;UAAE,GAAGiL,KAAK;UAAEnD;QAAM,CAAE,CAAC;;IAExD,CAAC,CAAC;IACF,IAAI,CAACvL,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACnH,IAAI,CAAC,IAAI,CAACwD,iBAAiB,CAAC4D,MAAM,EAAE,CAAC,CAAC;IACpE,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EACA;EACAkY,WAAWA,CAACtU,cAAsB;IAChC,MAAMiF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOtJ,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC4D,MAAM,CACfkK,MAAM,CAAsB;MAC3BC,QAAQ,EAAE/L,qBAAqB;MAC/BuL,SAAS,EAAE;QACTsO,KAAK,EAAE;UACLnQ,cAAc;UACdiF;;;KAGL,CAAC,CACDnI,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEuX,WAAW,IAAI,KAAK,CAAC,EAClD3f,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO/H,UAAU,CACf,MAAM,IAAI2L,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEAqU,UAAUA,CAACvU,cAAsB;IAC/B,MAAMiF,MAAM,GAAG,IAAI,CAAClB,gBAAgB,EAAE;IACtC,IAAI,CAACkB,MAAM,EAAE;MACX,IAAI,CAAC9M,MAAM,CAACyF,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAOtJ,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC4D,MAAM,CACfkK,MAAM,CAAqB;MAC1BC,QAAQ,EAAE9L,oBAAoB;MAC9BsL,SAAS,EAAE;QACTsO,KAAK,EAAE;UACLnQ,cAAc;UACdiF;;;KAGL,CAAC,CACDnI,IAAI,CACHpI,GAAG,CAAE8M,MAAM,IAAKA,MAAM,CAACzE,IAAI,EAAEwX,UAAU,IAAI,KAAK,CAAC,EACjD5f,UAAU,CAAE2H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO/H,UAAU,CAAC,MAAM,IAAI2L,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACAsU,oBAAoBA,CAAA;IAClB,IAAI,CAAC7b,aAAa,CAACkD,OAAO,CAAEgV,GAAG,IAAKA,GAAG,CAAC4D,WAAW,EAAE,CAAC;IACtD,IAAI,CAAC9b,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAACwZ,eAAe,EAAE;MACxBuC,aAAa,CAAC,IAAI,CAACvC,eAAe,CAAC;;IAErC,IAAI,CAAC5Z,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACzD,MAAM,CAACuD,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEAiZ,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBA5mHWxc,cAAc,EAAA4c,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAdld,cAAc;MAAAmd,OAAA,EAAdnd,cAAc,CAAAod,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}