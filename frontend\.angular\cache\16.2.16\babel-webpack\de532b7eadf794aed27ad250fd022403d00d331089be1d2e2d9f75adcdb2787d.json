{"ast": null, "code": "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n  constructor() {\n    this._itemSize = 20;\n    this._minBufferPx = 100;\n    this._maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n    this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  /** The size of the items in the list (in pixels). */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  static {\n    this.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n      return new (t || CdkFixedSizeVirtualScroll)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFixedSizeVirtualScroll,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n      inputs: {\n        itemSize: \"itemSize\",\n        minBufferPx: \"minBufferPx\",\n        maxBufferPx: \"maxBufferPx\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      standalone: true,\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n  constructor(_ngZone, _platform, document) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n    this._scrolled = new Subject();\n    /** Keeps track of the global `scroll` and `resize` subscriptions. */\n    this._globalSubscription = null;\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n    this._scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n    this.scrollContainers = new Map();\n    this._document = document;\n  }\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n    return new Observable(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      }\n      // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    });\n  }\n  ngOnDestroy() {\n    this._removeGlobalListener();\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement;\n    // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n    return false;\n  }\n  /** Sets up the global scroll listeners. */\n  _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      const window = this._getWindow();\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n  /** Cleans up the global scroll listener. */\n  _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n      this._globalSubscription = null;\n    }\n  }\n  static {\n    this.ɵfac = function ScrollDispatcher_Factory(t) {\n      return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollDispatcher,\n      factory: ScrollDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    this.elementRef = elementRef;\n    this.scrollDispatcher = scrollDispatcher;\n    this.ngZone = ngZone;\n    this.dir = dir;\n    this._destroyed = new Subject();\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n  ngOnInit() {\n    this.scrollDispatcher.register(this);\n  }\n  ngOnDestroy() {\n    this.scrollDispatcher.deregister(this);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    // Rewrite start & end offsets as right or left offsets.\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    }\n    // Rewrite the bottom offset as a top offset.\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    }\n    // Rewrite the right offset as a left offset.\n    if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n      if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n    this._applyScrollToOptions(options);\n  }\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    }\n    // Rewrite start & end as left or right offsets.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n    if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function CdkScrollable_Factory(t) {\n      return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkScrollable,\n      selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n  constructor(_platform, ngZone, document) {\n    this._platform = _platform;\n    /** Stream of viewport change events. */\n    this._change = new Subject();\n    /** Event listener that will be used to handle the viewport change events. */\n    this._changeListener = event => {\n      this._change.next(event);\n    };\n    this._document = document;\n    ngZone.runOutsideAngular(() => {\n      if (_platform.isBrowser) {\n        const window = this._getWindow();\n        // Note that bind the events ourselves, rather than going through something like RxJS's\n        // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n        window.addEventListener('resize', this._changeListener);\n        window.addEventListener('orientationchange', this._changeListener);\n      }\n      // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n  ngOnDestroy() {\n    if (this._platform.isBrowser) {\n      const window = this._getWindow();\n      window.removeEventListener('resize', this._changeListener);\n      window.removeEventListener('orientationchange', this._changeListener);\n    }\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    };\n    // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n    return output;\n  }\n  /** Gets a ClientRect for the viewport's bounds. */\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n    const document = this._document;\n    const window = this._getWindow();\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n  _updateViewportSize() {\n    const window = this._getWindow();\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    this.ɵfac = function ViewportRuler_Factory(t) {\n      return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ViewportRuler,\n      factory: ViewportRuler.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n  }\n  /**\n   * Measure the viewport size for the provided orientation.\n   *\n   * @param orientation The orientation to measure the size from.\n   */\n  measureViewportSize(orientation) {\n    const viewportEl = this.elementRef.nativeElement;\n    return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n  static {\n    this.ɵfac = function CdkVirtualScrollable_Factory(t) {\n      return new (t || CdkVirtualScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollable,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollable, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n  /** The direction the viewport scrolls. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n      this._calculateSpacerSize();\n    }\n  }\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(value) {\n    this._appendOnly = coerceBooleanProperty(value);\n  }\n  constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n    this.elementRef = elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollStrategy = _scrollStrategy;\n    this.scrollable = scrollable;\n    this._platform = inject(Platform);\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n    this._detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n    this._renderedRangeSubject = new Subject();\n    this._orientation = 'vertical';\n    this._appendOnly = false;\n    // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n    /** Emits when the index of the first element visible in the viewport changes. */\n    this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** A stream that emits whenever the rendered range changes. */\n    this.renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n    this._totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n    this._totalContentWidth = '';\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n    this._totalContentHeight = '';\n    /** The currently rendered range of indices. */\n    this._renderedRange = {\n      start: 0,\n      end: 0\n    };\n    /** The length of the data bound to this viewport (in number of items). */\n    this._dataLength = 0;\n    /** The size of the viewport (in pixels). */\n    this._viewportSize = 0;\n    /** The last rendered content offset that was set. */\n    this._renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n    this._renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n    this._isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n    this._runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n    this._viewportChanges = Subscription.EMPTY;\n    if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n    if (!this.scrollable) {\n      // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n      this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n      this.scrollable = this;\n    }\n  }\n  ngOnInit() {\n    // Scrolling depends on the element dimensions which we can't get during SSR.\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    if (this.scrollable === this) {\n      super.ngOnInit();\n    }\n    // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n      this._scrollStrategy.attach(this);\n      this.scrollable.elementScrolled().pipe(\n      // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null),\n      // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER),\n      // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n      // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n      // to unsubscribe here just in case.\n      takeUntil(this._destroyed)).subscribe(() => this._scrollStrategy.onContentScrolled());\n      this._markChangeDetectionNeeded();\n    }));\n  }\n  ngOnDestroy() {\n    this.detach();\n    this._scrollStrategy.detach();\n    // Complete all subjects\n    this._renderedRangeSubject.complete();\n    this._detachedSubject.complete();\n    this._viewportChanges.unsubscribe();\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    }\n    // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n          this._scrollStrategy.onDataLengthChanged();\n        }\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n  detach() {\n    this._forOf = null;\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n  getViewportSize() {\n    return this._viewportSize;\n  }\n  // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n  /** Get the current rendered range of items. */\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n      this._calculateSpacerSize();\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n      this._renderedRangeSubject.next(this._renderedRange = range);\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`;\n      // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n    this.scrollable.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the scrollable (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n  measureScrollOffset(from) {\n    // This is to break the call cycle\n    let measureScrollOffset;\n    if (this.scrollable == this) {\n      measureScrollOffset = _from => super.measureScrollOffset(_from);\n    } else {\n      measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n    }\n    return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) - this.measureViewportOffset());\n  }\n  /**\n   * Measures the offset of the viewport from the scrolling container\n   * @param from The edge to measure from.\n   */\n  measureViewportOffset(from) {\n    let fromRect;\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const isRtl = this.dir?.value == 'rtl';\n    if (from == 'start') {\n      fromRect = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      fromRect = isRtl ? LEFT : RIGHT;\n    } else if (from) {\n      fromRect = from;\n    } else {\n      fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n    }\n    const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n    const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n    return viewportClientRect - scrollerClientRect;\n  }\n  /** Measure the combined size of all of the rendered items. */\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n  _measureViewportSize() {\n    this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n  }\n  /** Queue up change detection to run. */\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    }\n    // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n  _doChangeDetection() {\n    this._isChangeDetectionPending = false;\n    // Apply the content transform. The transform can't be set via an Angular binding because\n    // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n    // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n    // the `Number` function first to coerce it to a numeric value.\n    this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n    // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n    // from the root, since the repeated items are content projected in. Calling `detectChanges`\n    // instead does not properly check the projected content.\n    this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n    const runAfterChangeDetection = this._runAfterChangeDetection;\n    this._runAfterChangeDetection = [];\n    for (const fn of runAfterChangeDetection) {\n      fn();\n    }\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n  _calculateSpacerSize() {\n    this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n    this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n  }\n  static {\n    this.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n      return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler), i0.ɵɵdirectiveInject(VIRTUAL_SCROLLABLE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkVirtualScrollViewport,\n      selectors: [[\"cdk-virtual-scroll-viewport\"]],\n      viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n        }\n      },\n      hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n      hostVars: 4,\n      hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n        }\n      },\n      inputs: {\n        orientation: \"orientation\",\n        appendOnly: \"appendOnly\"\n      },\n      outputs: {\n        scrolledIndexChange: \"scrolledIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"cdk-virtual-scroll-content-wrapper\"], [\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-spacer\"]],\n      template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n        }\n      },\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      providers: [{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: ViewportRuler\n    }, {\n      type: CdkVirtualScrollable,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLLABLE]\n      }]\n    }];\n  }, {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n  /** The DataSource to display. */\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  /** The template used to stamp out new elements. */\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  constructor( /** The view container to add items to. */\n  _viewContainerRef, /** The template to use when stamping out new items. */\n  _template, /** The set of available differs. */\n  _differs, /** The strategy used to render items in the virtual scroll viewport. */\n  _viewRepeater, /** The virtual scrolling viewport that these items are being rendered in. */\n  _viewport, ngZone) {\n    this._viewContainerRef = _viewContainerRef;\n    this._template = _template;\n    this._differs = _differs;\n    this._viewRepeater = _viewRepeater;\n    this._viewport = _viewport;\n    /** Emits when the rendered view of the data changes. */\n    this.viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n    this._dataSourceChanges = new Subject();\n    /** Emits whenever the data in the current DataSource changes. */\n    this.dataStream = this._dataSourceChanges.pipe(\n    // Start off with null `DataSource`.\n    startWith(null),\n    // Bundle up the previous and current data sources so we can work with both.\n    pairwise(),\n    // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n    // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n    this._differ = null;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n    this._needsUpdate = false;\n    this._destroyed = new Subject();\n    this.dataStream.subscribe(data => {\n      this._data = data;\n      this._onRenderedDataChange();\n    });\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n      this._onRenderedDataChange();\n    });\n    this._viewport.attach(this);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    }\n    // The index into the list of rendered views for the first item in the range.\n    const renderedStartIndex = range.start - this._renderedRange.start;\n    // The length of the range we're measuring.\n    const rangeLen = range.end - range.start;\n    // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n    let firstNode;\n    let lastNode;\n    // Find the first node by starting from the beginning and going forwards.\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    }\n    // Find the last node by starting from the end and going backwards.\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n      this._needsUpdate = false;\n    }\n  }\n  ngOnDestroy() {\n    this._viewport.detach();\n    this._dataSourceChanges.next(undefined);\n    this._dataSourceChanges.complete();\n    this.viewChange.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n    // Update $implicit for any items that had an identity change.\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n      view.context.$implicit = record.item;\n    });\n    // Update the context variables on all items.\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n  static {\n    this.ɵfac = function CdkVirtualForOf_Factory(t) {\n      return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualForOf,\n      selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n      inputs: {\n        cdkVirtualForOf: \"cdkVirtualForOf\",\n        cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n        cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n        cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }],\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i2$1._RecycleViewRepeaterStrategy,\n      decorators: [{\n        type: Inject,\n        args: [_VIEW_REPEATER_STRATEGY]\n      }]\n    }, {\n      type: CdkVirtualScrollViewport,\n      decorators: [{\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n  }\n  static {\n    this.ɵfac = function CdkVirtualScrollableElement_Factory(t) {\n      return new (t || CdkVirtualScrollableElement)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableElement,\n      selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n      hostAttrs: [1, \"cdk-virtual-scrollable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableElement, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualScrollingElement]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }],\n      standalone: true,\n      host: {\n        'class': 'cdk-virtual-scrollable'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n  constructor(scrollDispatcher, ngZone, dir) {\n    super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n  measureBoundingClientRectWithScrollOffset(from) {\n    return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n  }\n  static {\n    this.ɵfac = function CdkVirtualScrollableWindow_Factory(t) {\n      return new (t || CdkVirtualScrollableWindow)(i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableWindow,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollableWindow, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n      providers: [{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }],\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nclass CdkScrollableModule {\n  static {\n    this.ɵfac = function CdkScrollableModule_Factory(t) {\n      return new (t || CdkScrollableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkScrollableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      imports: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n  static {\n    this.ɵfac = function ScrollingModule_Factory(t) {\n      return new (t || ScrollingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ScrollingModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollableWindow, CdkVirtualScrollableElement],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollableWindow, CdkVirtualScrollableElement]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["coerceNumberProperty", "coerceElement", "coerceBooleanProperty", "i0", "InjectionToken", "forwardRef", "Directive", "Input", "Injectable", "Optional", "Inject", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "SkipSelf", "ElementRef", "NgModule", "Subject", "of", "Observable", "fromEvent", "animationFrameScheduler", "asapScheduler", "Subscription", "isObservable", "distinctUntilChanged", "auditTime", "filter", "takeUntil", "startWith", "pairwise", "switchMap", "shareReplay", "i1", "getRtlScrollAxisType", "supportsScrollBehavior", "Platform", "DOCUMENT", "i2", "BidiModule", "i2$1", "isDataSource", "ArrayDataSource", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_c0", "_c1", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "CdkFixedSizeVirtualScroll_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "ɵɵNgOnChangesFeature", "ɵsetClassMetadata", "args", "selector", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "document", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "_document", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_addGlobalListener", "subscription", "_removeGlobalListener", "ngOnDestroy", "for<PERSON>ach", "_", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "_getWindow", "defaultView", "window", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "runOutsideAngular", "ScrollDispatcher_Factory", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "undefined", "decorators", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "_destroyed", "_elementScrolled", "ngOnInit", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "CdkScrollable_Factory", "ɵɵdirectiveInject", "Directionality", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "event", "addEventListener", "change", "_viewportSize", "removeEventListener", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_orientation", "_calculateSpacerSize", "appendOnly", "_appendOnly", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "Promise", "resolve", "then", "run", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "fn", "CdkVirtualScrollViewport_Factory", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "CdkVirtualScrollViewport_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "outputs", "virtualScrollable", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "host", "None", "OnPush", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "cdkVirtualForOf", "_cdkVirtualForOf", "_dataSourceChanges", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "cdkVirtualForTemplate", "_template", "cdkVirtualForTemplateCacheSize", "_view<PERSON><PERSON><PERSON>er", "viewCacheSize", "_viewContainerRef", "_differs", "viewChange", "prev", "cur", "_changeDataSource", "_differ", "_data", "_onRenderedDataChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "i", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "last", "even", "odd", "templateRef", "CdkVirtualForOf_Factory", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkVirtualScrollableElement", "CdkVirtualScrollableElement_Factory", "useExisting", "CdkVirtualScrollableWindow", "CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "CdkScrollableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "imports", "ScrollingModule", "ScrollingModule_Factory"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    standalone: true,\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollable, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(value) {\n        this._appendOnly = coerceBooleanProperty(value);\n    }\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        this.scrollable = scrollable;\n        this._platform = inject(Platform);\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        this._appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }, { token: VIRTUAL_SCROLLABLE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: \"appendOnly\" }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLL_STRATEGY]\n                }] }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: ScrollDispatcher }, { type: ViewportRuler }, { type: CdkVirtualScrollable, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLLABLE]\n                }] }]; }, propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.NgZone }]; }, propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor(scrollDispatcher, ngZone, dir) {\n        super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [{ token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\nclass CdkScrollableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,uBAAuB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5N,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,QAAQ,MAAM;AAC7H,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,oBAAoB,EAAEC,sBAAsB,EAAEC,QAAQ,QAAQ,uBAAuB;AAC9F,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,eAAe,EAAEC,uBAAuB,EAAEC,4BAA4B,QAAQ,0BAA0B;;AAE/H;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,uBAAuB,GAAG,IAAI9C,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAM+C,8BAA8B,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,IAAIpC,OAAO,CAAC,CAAC;IACzC;IACA,IAAI,CAACqC,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAACE,IAAI,CAAC9B,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI,CAAC+B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIQ,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACL,SAAS,GAAGK,QAAQ;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACX,oBAAoB,CAACY,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACT,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,uBAAuBA,CAAChB,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACX,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACU,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACnB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACoB,cAAc,CAACF,KAAK,GAAG,IAAI,CAACjB,SAAS,EAAEkB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACqB,mBAAmB,CAAC,IAAI,CAACrB,SAAS,CAACsB,aAAa,CAAC,CAAC,GAAG,IAAI,CAACrB,SAAS,CAAC;EACvF;EACA;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;MACjB;IACJ;IACA,MAAMuB,aAAa,GAAG,IAAI,CAACvB,SAAS,CAACwB,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAC5B,SAAS,CAAC6B,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAAC9B,SAAS,CAACsB,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAAC/B,SAAS,CAACgC,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAAChC,SAAS,GAAG,CAAC,GAAG8B,YAAY,GAAG,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAC9E;IACA,IAAIwB,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC3B,SAAS,CAAC;MAChE,MAAMoC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACpC,SAAS;QAC/CwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAACzB,SAAS;IAClE,IAAIwC,WAAW,GAAG,IAAI,CAACvC,YAAY,IAAIuB,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGsC,WAAW,IAAI,IAAI,CAACxC,SAAS,CAAC;MACjFwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM0C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC1B,SAAS,IAAI8B,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAACzC,YAAY,IAAIuB,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGwC,SAAS,IAAI,IAAI,CAAC1C,SAAS,CAAC;QAC7E,IAAI2C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAAC6C,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAACzB,SAAS,CAAC8C,wBAAwB,CAAC,IAAI,CAAC7C,SAAS,GAAGwB,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAAC7B,oBAAoB,CAACkD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5B1D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACQ,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB;IACA,IAAI,CAAC+C,eAAe,GAAG,IAAI1D,8BAA8B,CAAC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EAChH;EACA;EACA,IAAIF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACO,SAAS;EACzB;EACA,IAAIP,QAAQA,CAAC0D,KAAK,EAAE;IAChB,IAAI,CAACnD,SAAS,GAAG5D,oBAAoB,CAAC+G,KAAK,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACI,IAAIzD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACO,YAAY;EAC5B;EACA,IAAIP,WAAWA,CAACyD,KAAK,EAAE;IACnB,IAAI,CAAClD,YAAY,GAAG7D,oBAAoB,CAAC+G,KAAK,CAAC;EACnD;EACA;AACJ;AACA;EACI,IAAIxD,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACO,YAAY;EAC5B;EACA,IAAIP,WAAWA,CAACwD,KAAK,EAAE;IACnB,IAAI,CAACjD,YAAY,GAAG9D,oBAAoB,CAAC+G,KAAK,CAAC;EACnD;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;EACnG;EACA;IAAS,IAAI,CAAC0D,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFL,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACM,IAAI,kBAD8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EACJR,yBAAyB;MAAAS,SAAA;MAAAC,MAAA;QAAAnE,QAAA;QAAAC,WAAA;QAAAC,WAAA;MAAA;MAAAkE,UAAA;MAAAC,QAAA,GADvBvH,EAAE,CAAAwH,kBAAA,CACmM,CAC7R;QACIC,OAAO,EAAE1E,uBAAuB;QAChC2E,UAAU,EAAElB,sCAAsC;QAClDmB,IAAI,EAAE,CAACzH,UAAU,CAAC,MAAMyG,yBAAyB,CAAC;MACtD,CAAC,CACJ,GAP2F3G,EAAE,CAAA4H,oBAAA;IAAA,EAOvD;EAAE;AACjD;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAToGnE,EAAE,CAAA6H,iBAAA,CASXlB,yBAAyB,EAAc,CAAC;IACvHQ,IAAI,EAAEhH,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCAAuC;MACjDT,UAAU,EAAE,IAAI;MAChBU,SAAS,EAAE,CACP;QACIP,OAAO,EAAE1E,uBAAuB;QAChC2E,UAAU,EAAElB,sCAAsC;QAClDmB,IAAI,EAAE,CAACzH,UAAU,CAAC,MAAMyG,yBAAyB,CAAC;MACtD,CAAC;IAET,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzD,QAAQ,EAAE,CAAC;MACzBiE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAE+C,WAAW,EAAE,CAAC;MACdgE,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEgD,WAAW,EAAE,CAAC;MACd+D,IAAI,EAAE/G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM6H,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBjF,WAAWA,CAACkF,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAE;IACtC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACE,SAAS,GAAG,IAAIrH,OAAO,CAAC,CAAC;IAC9B;IACA,IAAI,CAACsH,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,SAAS,GAAGN,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIO,QAAQA,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAACK,GAAG,CAACD,UAAU,CAAC,EAAE;MACxC,IAAI,CAACJ,gBAAgB,CAACM,GAAG,CAACF,UAAU,EAAEA,UAAU,CAACG,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAAC/B,IAAI,CAACsC,UAAU,CAAC,CAAC,CAAC;IACxH;EACJ;EACA;AACJ;AACA;AACA;EACIK,UAAUA,CAACL,UAAU,EAAE;IACnB,MAAMM,mBAAmB,GAAG,IAAI,CAACV,gBAAgB,CAACW,GAAG,CAACP,UAAU,CAAC;IACjE,IAAIM,mBAAmB,EAAE;MACrBA,mBAAmB,CAACE,WAAW,CAAC,CAAC;MACjC,IAAI,CAACZ,gBAAgB,CAACa,MAAM,CAACT,UAAU,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,QAAQA,CAACC,aAAa,GAAGvB,mBAAmB,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAOvI,EAAE,CAAC,CAAC;IACf;IACA,OAAO,IAAIC,UAAU,CAAEuI,QAAQ,IAAK;MAChC,IAAI,CAAC,IAAI,CAACnB,mBAAmB,EAAE;QAC3B,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MAC7B;MACA;MACA;MACA,MAAMC,YAAY,GAAGJ,aAAa,GAAG,CAAC,GAChC,IAAI,CAAClB,SAAS,CAAC/E,IAAI,CAAC7B,SAAS,CAAC8H,aAAa,CAAC,CAAC,CAACP,SAAS,CAACS,QAAQ,CAAC,GACjE,IAAI,CAACpB,SAAS,CAACW,SAAS,CAACS,QAAQ,CAAC;MACxC,IAAI,CAAClB,cAAc,EAAE;MACrB,OAAO,MAAM;QACToB,YAAY,CAACP,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACb,cAAc,EAAE;QACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;UACtB,IAAI,CAACqB,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,CAAC,CAACC,CAAC,EAAEC,SAAS,KAAK,IAAI,CAACf,UAAU,CAACe,SAAS,CAAC,CAAC;IAC3E,IAAI,CAAC3B,SAAS,CAACrE,QAAQ,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiG,gBAAgBA,CAACC,mBAAmB,EAAEX,aAAa,EAAE;IACjD,MAAMY,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;IACvE,OAAO,IAAI,CAACZ,QAAQ,CAACC,aAAa,CAAC,CAACjG,IAAI,CAAC5B,MAAM,CAAC2I,MAAM,IAAI;MACtD,OAAO,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;EACP;EACA;EACAD,2BAA2BA,CAACF,mBAAmB,EAAE;IAC7C,MAAMK,mBAAmB,GAAG,EAAE;IAC9B,IAAI,CAAC/B,gBAAgB,CAACsB,OAAO,CAAC,CAACU,aAAa,EAAE5B,UAAU,KAAK;MACzD,IAAI,IAAI,CAAC6B,0BAA0B,CAAC7B,UAAU,EAAEsB,mBAAmB,CAAC,EAAE;QAClEK,mBAAmB,CAACG,IAAI,CAAC9B,UAAU,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAO2B,mBAAmB;EAC9B;EACA;EACAI,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjC,SAAS,CAACkC,WAAW,IAAIC,MAAM;EAC/C;EACA;EACAJ,0BAA0BA,CAAC7B,UAAU,EAAEsB,mBAAmB,EAAE;IACxD,IAAIY,OAAO,GAAGjL,aAAa,CAACqK,mBAAmB,CAAC;IAChD,IAAIa,iBAAiB,GAAGnC,UAAU,CAACoC,aAAa,CAAC,CAAC,CAACC,aAAa;IAChE;IACA;IACA,GAAG;MACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;QAC9B,OAAO,IAAI;MACf;IACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;IACzC,OAAO,KAAK;EAChB;EACA;EACAxB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACiD,iBAAiB,CAAC,MAAM;MAC5D,MAAMN,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MAChC,OAAOxJ,SAAS,CAAC0J,MAAM,CAACzC,QAAQ,EAAE,QAAQ,CAAC,CAACY,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAAC/B,IAAI,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC;EACN;EACA;EACAsD,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACtB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACc,WAAW,CAAC,CAAC;MACtC,IAAI,CAACd,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACA;IAAS,IAAI,CAACzB,IAAI,YAAAuE,yBAAArE,CAAA;MAAA,YAAAA,CAAA,IAAwFkB,gBAAgB,EArK1BlI,EAAE,CAAAsL,QAAA,CAqK0CtL,EAAE,CAACuL,MAAM,GArKrDvL,EAAE,CAAAsL,QAAA,CAqKgErJ,EAAE,CAACG,QAAQ,GArK7EpC,EAAE,CAAAsL,QAAA,CAqKwFjJ,QAAQ;IAAA,CAA6D;EAAE;EACjQ;IAAS,IAAI,CAACmJ,KAAK,kBAtK6ExL,EAAE,CAAAyL,kBAAA;MAAAC,KAAA,EAsKYxD,gBAAgB;MAAAyD,OAAA,EAAhBzD,gBAAgB,CAAApB,IAAA;MAAA8E,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAzH,SAAA,oBAAAA,SAAA,KAxKoGnE,EAAE,CAAA6H,iBAAA,CAwKXK,gBAAgB,EAAc,CAAC;IAC9Gf,IAAI,EAAE9G,UAAU;IAChByH,IAAI,EAAE,CAAC;MAAE8D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzE,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAElF,EAAE,CAACG;IAAS,CAAC,EAAE;MAAE+E,IAAI,EAAE0E,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1G3E,IAAI,EAAE7G;MACV,CAAC,EAAE;QACC6G,IAAI,EAAE5G,MAAM;QACZuH,IAAI,EAAE,CAACzF,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAM0J,aAAa,CAAC;EAChB9I,WAAWA,CAAC+I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAG,IAAInL,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACoL,gBAAgB,GAAG,IAAIlL,UAAU,CAAEuI,QAAQ,IAAK,IAAI,CAACwC,MAAM,CAACd,iBAAiB,CAAC,MAAMhK,SAAS,CAAC,IAAI,CAAC4K,UAAU,CAACd,aAAa,EAAE,QAAQ,CAAC,CACtI3H,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACwK,UAAU,CAAC,CAAC,CAChCnD,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;EAC9B;EACA4C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,gBAAgB,CAACrD,QAAQ,CAAC,IAAI,CAAC;EACxC;EACAkB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmC,gBAAgB,CAAC/C,UAAU,CAAC,IAAI,CAAC;IACtC,IAAI,CAACkD,UAAU,CAAC7F,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6F,UAAU,CAACnI,QAAQ,CAAC,CAAC;EAC9B;EACA;EACA+E,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACqD,gBAAgB;EAChC;EACA;EACApB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACe,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,QAAQA,CAACC,OAAO,EAAE;IACd,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACd,aAAa;IACxC,MAAMwB,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACvF,KAAK,IAAI,KAAK;IACjD;IACA,IAAI4F,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;MACtBH,OAAO,CAACG,IAAI,GAAGD,KAAK,GAAGF,OAAO,CAACrH,GAAG,GAAGqH,OAAO,CAACtH,KAAK;IACtD;IACA,IAAIsH,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;MACvBJ,OAAO,CAACI,KAAK,GAAGF,KAAK,GAAGF,OAAO,CAACtH,KAAK,GAAGsH,OAAO,CAACrH,GAAG;IACvD;IACA;IACA,IAAIqH,OAAO,CAACK,MAAM,IAAI,IAAI,EAAE;MACxBL,OAAO,CAACM,GAAG,GACPL,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGR,OAAO,CAACK,MAAM;IAC1D;IACA;IACA,IAAIH,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,gCAAgC;MACrE,IAAIsK,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBH,OAAO,CAACI,KAAK,GACTH,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGV,OAAO,CAACG,IAAI;MACtD;MACA,IAAIzK,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,kCAAkC;QAC9DsK,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK;MAChC,CAAC,MACI,IAAI1K,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,iCAAiC;QAClEsK,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK,GAAG,CAACJ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;MACjE;IACJ,CAAC,MACI;MACD,IAAIJ,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;QACvBJ,OAAO,CAACG,IAAI,GACRF,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGV,OAAO,CAACI,KAAK;MACvD;IACJ;IACA,IAAI,CAACO,qBAAqB,CAACX,OAAO,CAAC;EACvC;EACAW,qBAAqBA,CAACX,OAAO,EAAE;IAC3B,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACd,aAAa;IACxC,IAAI/I,sBAAsB,CAAC,CAAC,EAAE;MAC1BsK,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;IACxB,CAAC,MACI;MACD,IAAIA,OAAO,CAACM,GAAG,IAAI,IAAI,EAAE;QACrBL,EAAE,CAACW,SAAS,GAAGZ,OAAO,CAACM,GAAG;MAC9B;MACA,IAAIN,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBF,EAAE,CAACY,UAAU,GAAGb,OAAO,CAACG,IAAI;MAChC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInH,mBAAmBA,CAAC8H,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMf,EAAE,GAAG,IAAI,CAACT,UAAU,CAACd,aAAa;IACxC,IAAIoC,IAAI,IAAI,KAAK,EAAE;MACf,OAAOb,EAAE,CAACW,SAAS;IACvB;IACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;MAClB,OAAOb,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACW,SAAS;IAC3D;IACA;IACA,MAAMV,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACvF,KAAK,IAAI,KAAK;IACjD,IAAI0G,IAAI,IAAI,OAAO,EAAE;MACjBA,IAAI,GAAGZ,KAAK,GAAGc,KAAK,GAAGD,IAAI;IAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBA,IAAI,GAAGZ,KAAK,GAAGa,IAAI,GAAGC,KAAK;IAC/B;IACA,IAAId,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,kCAAkC;MACvE;MACA;MACA,IAAIoL,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACY,UAAU;MAC1D,CAAC,MACI;QACD,OAAOZ,EAAE,CAACY,UAAU;MACxB;IACJ,CAAC,MACI,IAAIX,KAAK,IAAIxK,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,iCAAiC;MAC3E;MACA;MACA,IAAIoL,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACY,UAAU,GAAGZ,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW;MAC1D,CAAC,MACI;QACD,OAAO,CAACT,EAAE,CAACY,UAAU;MACzB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;QACd,OAAOd,EAAE,CAACY,UAAU;MACxB,CAAC,MACI;QACD,OAAOZ,EAAE,CAACQ,WAAW,GAAGR,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACY,UAAU;MAC1D;IACJ;EACJ;EACA;IAAS,IAAI,CAACvG,IAAI,YAAA2G,sBAAAzG,CAAA;MAAA,YAAAA,CAAA,IAAwF+E,aAAa,EAtUvB/L,EAAE,CAAA0N,iBAAA,CAsUuC1N,EAAE,CAACe,UAAU,GAtUtDf,EAAE,CAAA0N,iBAAA,CAsUiExF,gBAAgB,GAtUnFlI,EAAE,CAAA0N,iBAAA,CAsU8F1N,EAAE,CAACuL,MAAM,GAtUzGvL,EAAE,CAAA0N,iBAAA,CAsUoHpL,EAAE,CAACqL,cAAc;IAAA,CAA4D;EAAE;EACrS;IAAS,IAAI,CAAC1G,IAAI,kBAvU8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EAuUJ4E,aAAa;MAAA3E,SAAA;MAAAE,UAAA;IAAA,EAAoF;EAAE;AACrM;AACA;EAAA,QAAAnD,SAAA,oBAAAA,SAAA,KAzUoGnE,EAAE,CAAA6H,iBAAA,CAyUXkE,aAAa,EAAc,CAAC;IAC3G5E,IAAI,EAAEhH,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mCAAmC;MAC7CT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEnH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEoG,IAAI,EAAEe;IAAiB,CAAC,EAAE;MAAEf,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE7E,EAAE,CAACqL,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAChJ3E,IAAI,EAAE7G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMsN,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB5K,WAAWA,CAACmF,SAAS,EAAE8D,MAAM,EAAE7D,QAAQ,EAAE;IACrC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC0F,OAAO,GAAG,IAAI7M,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC8M,eAAe,GAAIC,KAAK,IAAK;MAC9B,IAAI,CAACF,OAAO,CAACvH,IAAI,CAACyH,KAAK,CAAC;IAC5B,CAAC;IACD,IAAI,CAACrF,SAAS,GAAGN,QAAQ;IACzB6D,MAAM,CAACd,iBAAiB,CAAC,MAAM;MAC3B,IAAIhD,SAAS,CAACqB,SAAS,EAAE;QACrB,MAAMqB,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;QAChC;QACA;QACAE,MAAM,CAACmD,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;QACvDjD,MAAM,CAACmD,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAACF,eAAe,CAAC;MACtE;MACA;MACA;MACA,IAAI,CAACG,MAAM,CAAC,CAAC,CAACjF,SAAS,CAAC,MAAO,IAAI,CAACkF,aAAa,GAAG,IAAK,CAAC;IAC9D,CAAC,CAAC;EACN;EACArE,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1B,SAAS,CAACqB,SAAS,EAAE;MAC1B,MAAMqB,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MAChCE,MAAM,CAACsD,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACL,eAAe,CAAC;MAC1DjD,MAAM,CAACsD,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAACL,eAAe,CAAC;IACzE;IACA,IAAI,CAACD,OAAO,CAAC7J,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAoB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC8I,aAAa,EAAE;MACrB,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMC,MAAM,GAAG;MAAEC,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK;MAAEC,MAAM,EAAE,IAAI,CAACL,aAAa,CAACK;IAAO,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACpG,SAAS,CAACqB,SAAS,EAAE;MAC3B,IAAI,CAAC0E,aAAa,GAAG,IAAI;IAC7B;IACA,OAAOG,MAAM;EACjB;EACA;EACAG,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACvD,MAAM;MAAEJ,KAAK;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACnJ,eAAe,CAAC,CAAC;IAChD,OAAO;MACHyH,GAAG,EAAE4B,cAAc,CAAC5B,GAAG;MACvBH,IAAI,EAAE+B,cAAc,CAAC/B,IAAI;MACzBE,MAAM,EAAE6B,cAAc,CAAC5B,GAAG,GAAG0B,MAAM;MACnC5B,KAAK,EAAE8B,cAAc,CAAC/B,IAAI,GAAG4B,KAAK;MAClCC,MAAM;MACND;IACJ,CAAC;EACL;EACA;EACAI,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAACvG,SAAS,CAACqB,SAAS,EAAE;MAC3B,OAAO;QAAEqD,GAAG,EAAE,CAAC;QAAEH,IAAI,EAAE;MAAE,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMtE,QAAQ,GAAG,IAAI,CAACM,SAAS;IAC/B,MAAMmC,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;IAChC,MAAMgE,eAAe,GAAGvG,QAAQ,CAACuG,eAAe;IAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,CAAC,CAAC;IAC5D,MAAMhC,GAAG,GAAG,CAAC+B,YAAY,CAAC/B,GAAG,IACzBzE,QAAQ,CAAC0G,IAAI,CAAC3B,SAAS,IACvBtC,MAAM,CAACkE,OAAO,IACdJ,eAAe,CAACxB,SAAS,IACzB,CAAC;IACL,MAAMT,IAAI,GAAG,CAACkC,YAAY,CAAClC,IAAI,IAC3BtE,QAAQ,CAAC0G,IAAI,CAAC1B,UAAU,IACxBvC,MAAM,CAACmE,OAAO,IACdL,eAAe,CAACvB,UAAU,IAC1B,CAAC;IACL,OAAO;MAAEP,GAAG;MAAEH;IAAK,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIuB,MAAMA,CAACgB,YAAY,GAAGtB,mBAAmB,EAAE;IACvC,OAAOsB,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACvK,IAAI,CAAC7B,SAAS,CAACwN,YAAY,CAAC,CAAC,GAAG,IAAI,CAACpB,OAAO;EACvF;EACA;EACAlD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjC,SAAS,CAACkC,WAAW,IAAIC,MAAM;EAC/C;EACA;EACAuD,mBAAmBA,CAAA,EAAG;IAClB,MAAMvD,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;IAChC,IAAI,CAACuD,aAAa,GAAG,IAAI,CAAC/F,SAAS,CAACqB,SAAS,GACvC;MAAE8E,KAAK,EAAEzD,MAAM,CAACqE,UAAU;MAAEX,MAAM,EAAE1D,MAAM,CAACsE;IAAY,CAAC,GACxD;MAAEb,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EACjC;EACA;IAAS,IAAI,CAAC1H,IAAI,YAAAuI,sBAAArI,CAAA;MAAA,YAAAA,CAAA,IAAwF6G,aAAa,EA1cvB7N,EAAE,CAAAsL,QAAA,CA0cuCrJ,EAAE,CAACG,QAAQ,GA1cpDpC,EAAE,CAAAsL,QAAA,CA0c+DtL,EAAE,CAACuL,MAAM,GA1c1EvL,EAAE,CAAAsL,QAAA,CA0cqFjJ,QAAQ;IAAA,CAA6D;EAAE;EAC9P;IAAS,IAAI,CAACmJ,KAAK,kBA3c6ExL,EAAE,CAAAyL,kBAAA;MAAAC,KAAA,EA2cYmC,aAAa;MAAAlC,OAAA,EAAbkC,aAAa,CAAA/G,IAAA;MAAA8E,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAzH,SAAA,oBAAAA,SAAA,KA7coGnE,EAAE,CAAA6H,iBAAA,CA6cXgG,aAAa,EAAc,CAAC;IAC3G1G,IAAI,EAAE9G,UAAU;IAChByH,IAAI,EAAE,CAAC;MAAE8D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEzE,IAAI,EAAElF,EAAE,CAACG;IAAS,CAAC,EAAE;MAAE+E,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE0E,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1G3E,IAAI,EAAE7G;MACV,CAAC,EAAE;QACC6G,IAAI,EAAE5G,MAAM;QACZuH,IAAI,EAAE,CAACzF,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMiN,kBAAkB,GAAG,IAAIrP,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AACA,MAAMsP,oBAAoB,SAASxD,aAAa,CAAC;EAC7C9I,WAAWA,CAAC+I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACIqD,mBAAmBA,CAACC,WAAW,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI,CAAC1D,UAAU,CAACd,aAAa;IAChD,OAAOuE,WAAW,KAAK,YAAY,GAAGC,UAAU,CAACxC,WAAW,GAAGwC,UAAU,CAAC1C,YAAY;EAC1F;EACA;IAAS,IAAI,CAAClG,IAAI,YAAA6I,6BAAA3I,CAAA;MAAA,YAAAA,CAAA,IAAwFuI,oBAAoB,EAxe9BvP,EAAE,CAAA0N,iBAAA,CAwe8C1N,EAAE,CAACe,UAAU,GAxe7Df,EAAE,CAAA0N,iBAAA,CAwewExF,gBAAgB,GAxe1FlI,EAAE,CAAA0N,iBAAA,CAweqG1N,EAAE,CAACuL,MAAM,GAxehHvL,EAAE,CAAA0N,iBAAA,CAwe2HpL,EAAE,CAACqL,cAAc;IAAA,CAA4D;EAAE;EAC5S;IAAS,IAAI,CAAC1G,IAAI,kBAze8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EAyeJoI,oBAAoB;MAAAhI,QAAA,GAzelBvH,EAAE,CAAA4P,0BAAA;IAAA,EAyewD;EAAE;AAChK;AACA;EAAA,QAAAzL,SAAA,oBAAAA,SAAA,KA3eoGnE,EAAE,CAAA6H,iBAAA,CA2eX0H,oBAAoB,EAAc,CAAC;IAClHpI,IAAI,EAAEhH;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgH,IAAI,EAAEnH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEoG,IAAI,EAAEe;IAAiB,CAAC,EAAE;MAAEf,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE7E,EAAE,CAACqL,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAChJ3E,IAAI,EAAE7G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,SAASuP,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAAC5K,KAAK,IAAI6K,EAAE,CAAC7K,KAAK,IAAI4K,EAAE,CAAC3K,GAAG,IAAI4K,EAAE,CAAC5K,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6K,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAG5O,uBAAuB,GAAGC,aAAa;AAC/G;AACA,MAAM4O,wBAAwB,SAASX,oBAAoB,CAAC;EACxD;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACU,YAAY;EAC5B;EACA,IAAIV,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,IAAI,CAACU,YAAY,KAAKV,WAAW,EAAE;MACnC,IAAI,CAACU,YAAY,GAAGV,WAAW;MAC/B,IAAI,CAACW,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACzJ,KAAK,EAAE;IAClB,IAAI,CAAC0J,WAAW,GAAGvQ,qBAAqB,CAAC6G,KAAK,CAAC;EACnD;EACA3D,WAAWA,CAAC+I,UAAU,EAAEuE,kBAAkB,EAAErE,MAAM,EAAExF,eAAe,EAAEyF,GAAG,EAAEF,gBAAgB,EAAEuE,aAAa,EAAE3H,UAAU,EAAE;IACnH,KAAK,CAACmD,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IAChD,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACuE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC7J,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACmC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACT,SAAS,GAAG5H,MAAM,CAAC4B,QAAQ,CAAC;IACjC;IACA,IAAI,CAACqO,gBAAgB,GAAG,IAAIxP,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACyP,qBAAqB,GAAG,IAAIzP,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACkP,YAAY,GAAG,UAAU;IAC9B,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAChN,mBAAmB,GAAG,IAAInC,UAAU,CAAEuI,QAAQ,IAAK,IAAI,CAAChD,eAAe,CAACpD,mBAAmB,CAAC2F,SAAS,CAACvE,KAAK,IAAIiM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC3E,MAAM,CAAC4E,GAAG,CAAC,MAAMpH,QAAQ,CAACnD,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/L;IACA,IAAI,CAACqM,mBAAmB,GAAG,IAAI,CAACL,qBAAqB;IACrD;AACR;AACA;IACQ,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC1B;IACA,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACC,cAAc,GAAG;MAAEjM,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAC1C;IACA,IAAI,CAACiM,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACjD,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACkD,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,KAAK;IAC/C;IACA,IAAI,CAACC,yBAAyB,GAAG,KAAK;IACtC;IACA,IAAI,CAACC,wBAAwB,GAAG,EAAE;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAGlQ,YAAY,CAACmQ,KAAK;IAC1C,IAAI,CAAChL,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMC,KAAK,CAAC,gFAAgF,CAAC;IACjG;IACA,IAAI,CAACqN,gBAAgB,GAAGjB,aAAa,CAACtC,MAAM,CAAC,CAAC,CAACjF,SAAS,CAAC,MAAM;MAC3D,IAAI,CAAC0I,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAAC9I,UAAU,EAAE;MAClB;MACA,IAAI,CAACmD,UAAU,CAACd,aAAa,CAAC0G,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrE,IAAI,CAAChJ,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAyD,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAAClE,SAAS,CAACqB,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACZ,UAAU,KAAK,IAAI,EAAE;MAC1B,KAAK,CAACyD,QAAQ,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA,IAAI,CAACJ,MAAM,CAACd,iBAAiB,CAAC,MAAMuF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC7D,IAAI,CAACiB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACpL,eAAe,CAAC9C,MAAM,CAAC,IAAI,CAAC;MACjC,IAAI,CAACiF,UAAU,CACVG,eAAe,CAAC,CAAC,CACjBzF,IAAI;MACT;MACA1B,SAAS,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACAH,SAAS,CAAC,CAAC,EAAEsO,gBAAgB,CAAC;MAC9B;MACA;MACA;MACApO,SAAS,CAAC,IAAI,CAACwK,UAAU,CAAC,CAAC,CACtBnD,SAAS,CAAC,MAAM,IAAI,CAACvC,eAAe,CAACrC,iBAAiB,CAAC,CAAC,CAAC;MAC9D,IAAI,CAAC0N,0BAA0B,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;EACP;EACAjI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9F,MAAM,CAAC,CAAC;IACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC0M,qBAAqB,CAACzM,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACwM,gBAAgB,CAACxM,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACwN,gBAAgB,CAACpI,WAAW,CAAC,CAAC;IACnC,KAAK,CAACS,WAAW,CAAC,CAAC;EACvB;EACA;EACAlG,MAAMA,CAACoO,KAAK,EAAE;IACV,IAAI,IAAI,CAACC,MAAM,KAAK,OAAO9N,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAAC8H,MAAM,CAACd,iBAAiB,CAAC,MAAM;MAChC,IAAI,CAAC6G,MAAM,GAAGD,KAAK;MACnB,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC3O,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAAC6O,gBAAgB,CAAC,CAAC,CAACxH,SAAS,CAACkJ,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;QAC7B,IAAID,SAAS,KAAK,IAAI,CAAChB,WAAW,EAAE;UAChC,IAAI,CAACA,WAAW,GAAGgB,SAAS;UAC5B,IAAI,CAAC1L,eAAe,CAACpC,mBAAmB,CAAC,CAAC;QAC9C;QACA,IAAI,CAACgO,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAtO,MAAMA,CAAA,EAAG;IACL,IAAI,CAACiO,MAAM,GAAG,IAAI;IAClB,IAAI,CAACxB,gBAAgB,CAAClK,IAAI,CAAC,CAAC;EAChC;EACA;EACAzB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACsM,WAAW;EAC3B;EACA;EACA/L,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8I,aAAa;EAC7B;EACA;EACA;EACA;EACA;EACA;EACAnJ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACmM,cAAc;EAC9B;EACAoB,yCAAyCA,CAACjF,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACrC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC4D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC;EAC3E;EACA;AACJ;AACA;AACA;EACIzI,mBAAmBA,CAAC2N,IAAI,EAAE;IACtB,IAAI,IAAI,CAACxB,iBAAiB,KAAKwB,IAAI,EAAE;MACjC,IAAI,CAACxB,iBAAiB,GAAGwB,IAAI;MAC7B,IAAI,CAACpC,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC2B,0BAA0B,CAAC,CAAC;IACrC;EACJ;EACA;EACA1L,gBAAgBA,CAACoM,KAAK,EAAE;IACpB,IAAI,CAAC5C,WAAW,CAAC,IAAI,CAACsB,cAAc,EAAEsB,KAAK,CAAC,EAAE;MAC1C,IAAI,IAAI,CAACpC,UAAU,EAAE;QACjBoC,KAAK,GAAG;UAAEvN,KAAK,EAAE,CAAC;UAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACqL,cAAc,CAAChM,GAAG,EAAEsN,KAAK,CAACtN,GAAG;QAAE,CAAC;MAC3E;MACA,IAAI,CAACuL,qBAAqB,CAACnK,IAAI,CAAE,IAAI,CAAC4K,cAAc,GAAGsB,KAAM,CAAC;MAC9D,IAAI,CAACV,0BAA0B,CAAC,MAAM,IAAI,CAACrL,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;IACnF;EACJ;EACA;AACJ;AACA;EACImO,+BAA+BA,CAAA,EAAG;IAC9B,OAAO,IAAI,CAACpB,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;EACvF;EACA;AACJ;AACA;AACA;EACI/K,wBAAwBA,CAACqM,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;IAC9C;IACAD,MAAM,GAAG,IAAI,CAACtC,UAAU,IAAIuC,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;IAC1D;IACA;IACA,MAAMjG,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAACvF,KAAK,IAAI,KAAK;IACjD,MAAMiM,YAAY,GAAG,IAAI,CAACpD,WAAW,IAAI,YAAY;IACrD,MAAMqD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;IACrC,MAAME,aAAa,GAAGF,YAAY,IAAInG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,IAAIsG,SAAS,GAAI,YAAWF,IAAK,IAAGG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAE,KAAI;IACvE,IAAI,CAACtB,sBAAsB,GAAGsB,MAAM;IACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;MACjBI,SAAS,IAAK,aAAYF,IAAK,SAAQ;MACvC;MACA;MACA;MACA,IAAI,CAACxB,kCAAkC,GAAG,IAAI;IAClD;IACA,IAAI,IAAI,CAAC4B,yBAAyB,IAAIF,SAAS,EAAE;MAC7C;MACA;MACA,IAAI,CAACE,yBAAyB,GAAGF,SAAS;MAC1C,IAAI,CAACjB,0BAA0B,CAAC,MAAM;QAClC,IAAI,IAAI,CAACT,kCAAkC,EAAE;UACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAAC8B,0BAA0B,CAAC,CAAC;UAChE,IAAI,CAAC7B,kCAAkC,GAAG,KAAK;UAC/C,IAAI,CAAChL,wBAAwB,CAAC,IAAI,CAAC+K,sBAAsB,CAAC;QAC9D,CAAC,MACI;UACD,IAAI,CAAC3K,eAAe,CAAClC,uBAAuB,CAAC,CAAC;QAClD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,cAAcA,CAAC+N,MAAM,EAAEhO,QAAQ,GAAG,MAAM,EAAE;IACtC,MAAM6H,OAAO,GAAG;MAAE7H;IAAS,CAAC;IAC5B,IAAI,IAAI,CAAC8K,WAAW,KAAK,YAAY,EAAE;MACnCjD,OAAO,CAACtH,KAAK,GAAGyN,MAAM;IAC1B,CAAC,MACI;MACDnG,OAAO,CAACM,GAAG,GAAG6F,MAAM;IACxB;IACA,IAAI,CAAC9J,UAAU,CAAC0D,QAAQ,CAACC,OAAO,CAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACI/H,aAAaA,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;IACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;EACIa,mBAAmBA,CAAC8H,IAAI,EAAE;IACtB;IACA,IAAI9H,mBAAmB;IACvB,IAAI,IAAI,CAACqD,UAAU,IAAI,IAAI,EAAE;MACzBrD,mBAAmB,GAAI4N,KAAK,IAAK,KAAK,CAAC5N,mBAAmB,CAAC4N,KAAK,CAAC;IACrE,CAAC,MACI;MACD5N,mBAAmB,GAAI4N,KAAK,IAAK,IAAI,CAACvK,UAAU,CAACrD,mBAAmB,CAAC4N,KAAK,CAAC;IAC/E;IACA,OAAOzN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAAC8H,IAAI,KAAK,IAAI,CAACmC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAAC4D,qBAAqB,CAAC,CAAC,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIA,qBAAqBA,CAAC/F,IAAI,EAAE;IACxB,IAAIgG,QAAQ;IACZ,MAAM/F,IAAI,GAAG,MAAM;IACnB,MAAMC,KAAK,GAAG,OAAO;IACrB,MAAMd,KAAK,GAAG,IAAI,CAACP,GAAG,EAAEvF,KAAK,IAAI,KAAK;IACtC,IAAI0G,IAAI,IAAI,OAAO,EAAE;MACjBgG,QAAQ,GAAG5G,KAAK,GAAGc,KAAK,GAAGD,IAAI;IACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;MACpBgG,QAAQ,GAAG5G,KAAK,GAAGa,IAAI,GAAGC,KAAK;IACnC,CAAC,MACI,IAAIF,IAAI,EAAE;MACXgG,QAAQ,GAAGhG,IAAI;IACnB,CAAC,MACI;MACDgG,QAAQ,GAAG,IAAI,CAAC7D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;IACjE;IACA,MAAM8D,kBAAkB,GAAG,IAAI,CAAC1K,UAAU,CAAC0J,yCAAyC,CAACe,QAAQ,CAAC;IAC9F,MAAME,kBAAkB,GAAG,IAAI,CAACxH,UAAU,CAACd,aAAa,CAAC4D,qBAAqB,CAAC,CAAC,CAACwE,QAAQ,CAAC;IAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;EAClD;EACA;EACAJ,0BAA0BA,CAAA,EAAG;IACzB,MAAMM,SAAS,GAAG,IAAI,CAACC,eAAe,CAACxI,aAAa;IACpD,OAAO,IAAI,CAACuE,WAAW,KAAK,YAAY,GAAGgE,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACG,YAAY;EAC7F;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACpB,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;MACd,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACA,MAAM,CAAC4B,gBAAgB,CAACpB,KAAK,EAAE,IAAI,CAAChD,WAAW,CAAC;EAChE;EACA;EACAkC,iBAAiBA,CAAA,EAAG;IAChB;IACA,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACpL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;EAC9C;EACA;EACAwN,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC3D,aAAa,GAAG,IAAI,CAACtF,UAAU,CAAC2G,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;EAC9E;EACA;EACAsC,0BAA0BA,CAAC+B,QAAQ,EAAE;IACjC,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACtC,wBAAwB,CAAC7G,IAAI,CAACmJ,QAAQ,CAAC;IAChD;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvC,yBAAyB,EAAE;MACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;MACrC,IAAI,CAACrF,MAAM,CAACd,iBAAiB,CAAC,MAAMuF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACyB,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC;IACP;EACJ;EACA;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACf,yBAAyB,GAAG,KAAK;IACtC;IACA;IACA;IACA;IACA,IAAI,CAACmC,eAAe,CAACxI,aAAa,CAAC6I,KAAK,CAACf,SAAS,GAAG,IAAI,CAACE,yBAAyB;IACnF;IACA;IACA;IACA,IAAI,CAAChH,MAAM,CAAC4E,GAAG,CAAC,MAAM,IAAI,CAACP,kBAAkB,CAACyD,YAAY,CAAC,CAAC,CAAC;IAC7D,MAAMC,uBAAuB,GAAG,IAAI,CAACzC,wBAAwB;IAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;IAClC,KAAK,MAAM0C,EAAE,IAAID,uBAAuB,EAAE;MACtCC,EAAE,CAAC,CAAC;IACR;EACJ;EACA;EACA9D,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACc,mBAAmB,GACpB,IAAI,CAACzB,WAAW,KAAK,YAAY,GAAG,EAAE,GAAI,GAAE,IAAI,CAACuB,iBAAkB,IAAG;IAC1E,IAAI,CAACC,kBAAkB,GACnB,IAAI,CAACxB,WAAW,KAAK,YAAY,GAAI,GAAE,IAAI,CAACuB,iBAAkB,IAAG,GAAG,EAAE;EAC9E;EACA;IAAS,IAAI,CAAClK,IAAI,YAAAqN,iCAAAnN,CAAA;MAAA,YAAAA,CAAA,IAAwFkJ,wBAAwB,EAt2BlClQ,EAAE,CAAA0N,iBAAA,CAs2BkD1N,EAAE,CAACe,UAAU,GAt2BjEf,EAAE,CAAA0N,iBAAA,CAs2B4E1N,EAAE,CAACoU,iBAAiB,GAt2BlGpU,EAAE,CAAA0N,iBAAA,CAs2B6G1N,EAAE,CAACuL,MAAM,GAt2BxHvL,EAAE,CAAA0N,iBAAA,CAs2BmI3K,uBAAuB,MAt2B5J/C,EAAE,CAAA0N,iBAAA,CAs2BuLpL,EAAE,CAACqL,cAAc,MAt2B1M3N,EAAE,CAAA0N,iBAAA,CAs2BqOxF,gBAAgB,GAt2BvPlI,EAAE,CAAA0N,iBAAA,CAs2BkQG,aAAa,GAt2BjR7N,EAAE,CAAA0N,iBAAA,CAs2B4R4B,kBAAkB;IAAA,CAA4D;EAAE;EAC9c;IAAS,IAAI,CAAC+E,IAAI,kBAv2B8ErU,EAAE,CAAAsU,iBAAA;MAAAnN,IAAA,EAu2BJ+I,wBAAwB;MAAA9I,SAAA;MAAAmN,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAv2BtBzU,EAAE,CAAA2U,WAAA,CAAA9R,GAAA;QAAA;QAAA,IAAA4R,EAAA;UAAA,IAAAG,EAAA;UAAF5U,EAAE,CAAA6U,cAAA,CAAAD,EAAA,GAAF5U,EAAE,CAAA8U,WAAA,QAAAJ,GAAA,CAAAhB,eAAA,GAAAkB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzU,EAAE,CAAAoV,WAAA,8CAAAV,GAAA,CAAAjF,WAAA,8DAAAiF,GAAA,CAAAjF,WAAA;QAAA;MAAA;MAAApI,MAAA;QAAAoI,WAAA;QAAAY,UAAA;MAAA;MAAAgF,OAAA;QAAA/R,mBAAA;MAAA;MAAAgE,UAAA;MAAAC,QAAA,GAAFvH,EAAE,CAAAwH,kBAAA,CAu2B0c,CACpiB;QACIC,OAAO,EAAEsE,aAAa;QACtBrE,UAAU,EAAEA,CAAC4N,iBAAiB,EAAEzR,QAAQ,KAAKyR,iBAAiB,IAAIzR,QAAQ;QAC1E8D,IAAI,EAAE,CAAC,CAAC,IAAIrH,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAAC+O,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;MACrF,CAAC,CACJ,GA72B2FlQ,EAAE,CAAA4P,0BAAA,EAAF5P,EAAE,CAAAuV,mBAAA;MAAAC,kBAAA,EAAA1S,GAAA;MAAA2S,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAApB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzU,EAAE,CAAA8V,eAAA;UAAF9V,EAAE,CAAA+V,cAAA,eA62B6Q,CAAC;UA72BhR/V,EAAE,CAAAgW,YAAA,EA62B0S,CAAC;UA72B7ShW,EAAE,CAAAiW,YAAA,CA62BkT,CAAC;UA72BrTjW,EAAE,CAAAkW,SAAA,YA62BumB,CAAC;QAAA;QAAA,IAAAzB,EAAA;UA72B1mBzU,EAAE,CAAAmW,SAAA,EA62ByjB,CAAC;UA72B5jBnW,EAAE,CAAAoW,WAAA,UAAA1B,GAAA,CAAAzD,kBA62ByjB,CAAC,WAAAyD,GAAA,CAAAxD,mBAAD,CAAC;QAAA;MAAA;MAAAmF,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAo1D;EAAE;AACt/E;AACA;EAAA,QAAApS,SAAA,oBAAAA,SAAA,KA/2BoGnE,EAAE,CAAA6H,iBAAA,CA+2BXqI,wBAAwB,EAAc,CAAC;IACtH/I,IAAI,EAAE1G,SAAS;IACfqH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEyO,IAAI,EAAE;QAC5C,OAAO,EAAE,6BAA6B;QACtC,mDAAmD,EAAE,8BAA8B;QACnF,iDAAiD,EAAE;MACvD,CAAC;MAAEF,aAAa,EAAE5V,iBAAiB,CAAC+V,IAAI;MAAEF,eAAe,EAAE5V,uBAAuB,CAAC+V,MAAM;MAAEpP,UAAU,EAAE,IAAI;MAAEU,SAAS,EAAE,CACpH;QACIP,OAAO,EAAEsE,aAAa;QACtBrE,UAAU,EAAEA,CAAC4N,iBAAiB,EAAEzR,QAAQ,KAAKyR,iBAAiB,IAAIzR,QAAQ;QAC1E8D,IAAI,EAAE,CAAC,CAAC,IAAIrH,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAAC+O,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;MACrF,CAAC,CACJ;MAAE0F,QAAQ,EAAE,shBAAshB;MAAES,MAAM,EAAE,CAAC,srDAAsrD;IAAE,CAAC;EACnvE,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElP,IAAI,EAAEnH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEoG,IAAI,EAAEnH,EAAE,CAACoU;IAAkB,CAAC,EAAE;MAAEjN,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE0E,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5I3E,IAAI,EAAE7G;MACV,CAAC,EAAE;QACC6G,IAAI,EAAE5G,MAAM;QACZuH,IAAI,EAAE,CAAC/E,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEoE,IAAI,EAAE7E,EAAE,CAACqL,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAC1C3E,IAAI,EAAE7G;MACV,CAAC;IAAE,CAAC,EAAE;MAAE6G,IAAI,EAAEe;IAAiB,CAAC,EAAE;MAAEf,IAAI,EAAE0G;IAAc,CAAC,EAAE;MAAE1G,IAAI,EAAEoI,oBAAoB;MAAEzD,UAAU,EAAE,CAAC;QAClG3E,IAAI,EAAE7G;MACV,CAAC,EAAE;QACC6G,IAAI,EAAE5G,MAAM;QACZuH,IAAI,EAAE,CAACwH,kBAAkB;MAC7B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEG,WAAW,EAAE,CAAC;MAC1CtI,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEiQ,UAAU,EAAE,CAAC;MACblJ,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEkD,mBAAmB,EAAE,CAAC;MACtB6D,IAAI,EAAEvG;IACV,CAAC,CAAC;IAAE8S,eAAe,EAAE,CAAC;MAClBvM,IAAI,EAAEtG,SAAS;MACfiH,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAE6O,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,SAASC,SAASA,CAACnH,WAAW,EAAEoH,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAMrK,EAAE,GAAGqK,IAAI;EACf,IAAI,CAACrK,EAAE,CAACqC,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAMiI,IAAI,GAAGtK,EAAE,CAACqC,qBAAqB,CAAC,CAAC;EACvC,IAAIW,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOoH,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACpK,IAAI,GAAGoK,IAAI,CAACnK,KAAK;EACzD;EACA,OAAOiK,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACjK,GAAG,GAAGiK,IAAI,CAAClK,MAAM;AACzD;AACA;AACA;AACA;AACA;AACA,MAAMmK,eAAe,CAAC;EAClB;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB;EAChC;EACA,IAAID,eAAeA,CAACrQ,KAAK,EAAE;IACvB,IAAI,CAACsQ,gBAAgB,GAAGtQ,KAAK;IAC7B,IAAInE,YAAY,CAACmE,KAAK,CAAC,EAAE;MACrB,IAAI,CAACuQ,kBAAkB,CAAC5Q,IAAI,CAACK,KAAK,CAAC;IACvC,CAAC,MACI;MACD;MACA,IAAI,CAACuQ,kBAAkB,CAAC5Q,IAAI,CAAC,IAAI7D,eAAe,CAAClB,YAAY,CAACoF,KAAK,CAAC,GAAGA,KAAK,GAAGwQ,KAAK,CAAC9J,IAAI,CAAC1G,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5G;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIyQ,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACnD,EAAE,EAAE;IACzB,IAAI,CAACqD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACD,qBAAqB,GAAGpD,EAAE,GACzB,CAACxP,KAAK,EAAE8S,IAAI,KAAKtD,EAAE,CAACxP,KAAK,IAAI,IAAI,CAACyM,cAAc,GAAG,IAAI,CAACA,cAAc,CAACjM,KAAK,GAAG,CAAC,CAAC,EAAEsS,IAAI,CAAC,GACxF3L,SAAS;EACnB;EACA;EACA,IAAI4L,qBAAqBA,CAAC7Q,KAAK,EAAE;IAC7B,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC2Q,YAAY,GAAG,IAAI;MACxB,IAAI,CAACG,SAAS,GAAG9Q,KAAK;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAI+Q,8BAA8BA,CAAA,EAAG;IACjC,OAAO,IAAI,CAACC,aAAa,CAACC,aAAa;EAC3C;EACA,IAAIF,8BAA8BA,CAACnF,IAAI,EAAE;IACrC,IAAI,CAACoF,aAAa,CAACC,aAAa,GAAGhY,oBAAoB,CAAC2S,IAAI,CAAC;EACjE;EACAvP,WAAWA,CAAA,CACX;EACA6U,iBAAiB,EACjB;EACAJ,SAAS,EACT;EACAK,QAAQ,EACR;EACAH,aAAa,EACb;EACApU,SAAS,EAAE0I,MAAM,EAAE;IACf,IAAI,CAAC4L,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACpU,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACwU,UAAU,GAAG,IAAI/W,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACkW,kBAAkB,GAAG,IAAIlW,OAAO,CAAC,CAAC;IACvC;IACA,IAAI,CAACiR,UAAU,GAAG,IAAI,CAACiF,kBAAkB,CAAC5T,IAAI;IAC9C;IACA1B,SAAS,CAAC,IAAI,CAAC;IACf;IACAC,QAAQ,CAAC,CAAC;IACV;IACA;IACA;IACAC,SAAS,CAAC,CAAC,CAACkW,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;IAC7D;IACAlW,WAAW,CAAC,CAAC,CAAC,CAAC;IACf;IACA,IAAI,CAACoW,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACb,YAAY,GAAG,KAAK;IACzB,IAAI,CAACnL,UAAU,GAAG,IAAInL,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACiR,UAAU,CAACjJ,SAAS,CAACkJ,IAAI,IAAI;MAC9B,IAAI,CAACkG,KAAK,GAAGlG,IAAI;MACjB,IAAI,CAACmG,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAC9U,SAAS,CAACuN,mBAAmB,CAACxN,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACwK,UAAU,CAAC,CAAC,CAACnD,SAAS,CAACwJ,KAAK,IAAI;MACnF,IAAI,CAACtB,cAAc,GAAGsB,KAAK;MAC3B,IAAI,IAAI,CAACuF,UAAU,CAACO,SAAS,CAAClG,MAAM,EAAE;QAClCnG,MAAM,CAAC4E,GAAG,CAAC,MAAM,IAAI,CAACkH,UAAU,CAACzR,IAAI,CAAC,IAAI,CAAC4K,cAAc,CAAC,CAAC;MAC/D;MACA,IAAI,CAACmH,qBAAqB,CAAC,CAAC;IAChC,CAAC,CAAC;IACF,IAAI,CAAC9U,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACIiQ,gBAAgBA,CAACpB,KAAK,EAAEhD,WAAW,EAAE;IACjC,IAAIgD,KAAK,CAACvN,KAAK,IAAIuN,KAAK,CAACtN,GAAG,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,IAAI,CAACsN,KAAK,CAACvN,KAAK,GAAG,IAAI,CAACiM,cAAc,CAACjM,KAAK,IAAIuN,KAAK,CAACtN,GAAG,GAAG,IAAI,CAACgM,cAAc,CAAChM,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAE,0DAAyD,CAAC;IAC3E;IACA;IACA,MAAMoU,kBAAkB,GAAG/F,KAAK,CAACvN,KAAK,GAAG,IAAI,CAACiM,cAAc,CAACjM,KAAK;IAClE;IACA,MAAMuT,QAAQ,GAAGhG,KAAK,CAACtN,GAAG,GAAGsN,KAAK,CAACvN,KAAK;IACxC;IACA;IACA,IAAIwT,SAAS;IACb,IAAIC,QAAQ;IACZ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,EAAEG,CAAC,EAAE,EAAE;MAC/B,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAC1O,GAAG,CAACwP,CAAC,GAAGJ,kBAAkB,CAAC;MAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACzG,MAAM,EAAE;QAC/BqG,SAAS,GAAGC,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ;IACA;IACA,KAAK,IAAIF,CAAC,GAAGH,QAAQ,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAC1O,GAAG,CAACwP,CAAC,GAAGJ,kBAAkB,CAAC;MAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACzG,MAAM,EAAE;QAC/BsG,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAACD,IAAI,CAACC,SAAS,CAACzG,MAAM,GAAG,CAAC,CAAC;QACpD;MACJ;IACJ;IACA,OAAOqG,SAAS,IAAIC,QAAQ,GACtB/B,SAAS,CAACnH,WAAW,EAAE,KAAK,EAAEkJ,QAAQ,CAAC,GAAG/B,SAAS,CAACnH,WAAW,EAAE,OAAO,EAAEiJ,SAAS,CAAC,GACpF,CAAC;EACX;EACAK,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACb,YAAY,EAAE;MACnC;MACA;MACA;MACA,MAAMyB,OAAO,GAAG,IAAI,CAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC;MACtD,IAAI,CAACF,OAAO,EAAE;QACV,IAAI,CAACG,cAAc,CAAC,CAAC;MACzB,CAAC,MACI;QACD,IAAI,CAACC,aAAa,CAACJ,OAAO,CAAC;MAC/B;MACA,IAAI,CAACzB,YAAY,GAAG,KAAK;IAC7B;EACJ;EACAzN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtG,SAAS,CAACQ,MAAM,CAAC,CAAC;IACvB,IAAI,CAACmT,kBAAkB,CAAC5Q,IAAI,CAACsF,SAAS,CAAC;IACvC,IAAI,CAACsL,kBAAkB,CAAClT,QAAQ,CAAC,CAAC;IAClC,IAAI,CAAC+T,UAAU,CAAC/T,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACmI,UAAU,CAAC7F,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC6F,UAAU,CAACnI,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC2T,aAAa,CAAC5T,MAAM,CAAC,CAAC;EAC/B;EACA;EACAsU,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACnH,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAAC+H,cAAc,GAAG,IAAI,CAACb,KAAK,CAACgB,KAAK,CAAC,IAAI,CAAClI,cAAc,CAACjM,KAAK,EAAE,IAAI,CAACiM,cAAc,CAAChM,GAAG,CAAC;IAC1F,IAAI,CAAC,IAAI,CAACiT,OAAO,EAAE;MACf;MACA;MACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACL,QAAQ,CAACuB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAAC,CAACK,MAAM,CAAC,CAAC7U,KAAK,EAAE8S,IAAI,KAAK;QAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAAC3S,KAAK,EAAE8S,IAAI,CAAC,GAAGA,IAAI;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACD,YAAY,GAAG,IAAI;EAC5B;EACA;EACAY,iBAAiBA,CAACqB,KAAK,EAAEC,KAAK,EAAE;IAC5B,IAAID,KAAK,EAAE;MACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,CAACnC,YAAY,GAAG,IAAI;IACxB,OAAOkC,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAGzY,EAAE,CAAC,CAAC;EAC7C;EACA;EACAiY,cAAcA,CAAA,EAAG;IACb,MAAMS,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAChG,MAAM;IAC/B,IAAIuG,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAACzF,MAAM;IACrC,OAAOuG,CAAC,EAAE,EAAE;MACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAC1O,GAAG,CAACwP,CAAC,CAAC;MAC1CC,IAAI,CAACgB,OAAO,CAACnV,KAAK,GAAG,IAAI,CAACyM,cAAc,CAACjM,KAAK,GAAG0T,CAAC;MAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;MACnDhB,IAAI,CAACkB,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAX,aAAaA,CAACJ,OAAO,EAAE;IACnB,IAAI,CAACpB,aAAa,CAACoC,YAAY,CAAChB,OAAO,EAAE,IAAI,CAAClB,iBAAiB,EAAE,CAACmC,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACzC,IAAI,CAAC;IAC1L;IACAwB,OAAO,CAACqB,qBAAqB,CAAEJ,MAAM,IAAK;MACtC,MAAMpB,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAC1O,GAAG,CAAC6Q,MAAM,CAACE,YAAY,CAAC;MAC5DtB,IAAI,CAACgB,OAAO,CAACS,SAAS,GAAGL,MAAM,CAACzC,IAAI;IACxC,CAAC,CAAC;IACF;IACA,MAAMoC,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAChG,MAAM;IAC/B,IAAIuG,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAACzF,MAAM;IACrC,OAAOuG,CAAC,EAAE,EAAE;MACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAAC1O,GAAG,CAACwP,CAAC,CAAC;MAC1CC,IAAI,CAACgB,OAAO,CAACnV,KAAK,GAAG,IAAI,CAACyM,cAAc,CAACjM,KAAK,GAAG0T,CAAC;MAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;IACvD;EACJ;EACA;EACAC,gCAAgCA,CAACD,OAAO,EAAE;IACtCA,OAAO,CAAC9E,KAAK,GAAG8E,OAAO,CAACnV,KAAK,KAAK,CAAC;IACnCmV,OAAO,CAACU,IAAI,GAAGV,OAAO,CAACnV,KAAK,KAAKmV,OAAO,CAACD,KAAK,GAAG,CAAC;IAClDC,OAAO,CAACW,IAAI,GAAGX,OAAO,CAACnV,KAAK,GAAG,CAAC,KAAK,CAAC;IACtCmV,OAAO,CAACY,GAAG,GAAG,CAACZ,OAAO,CAACW,IAAI;EAC/B;EACAJ,oBAAoBA,CAACH,MAAM,EAAEvV,KAAK,EAAE;IAChC;IACA;IACA;IACA;IACA,OAAO;MACHgW,WAAW,EAAE,IAAI,CAAChD,SAAS;MAC3BmC,OAAO,EAAE;QACLS,SAAS,EAAEL,MAAM,CAACzC,IAAI;QACtB;QACA;QACAP,eAAe,EAAE,IAAI,CAACC,gBAAgB;QACtCxS,KAAK,EAAE,CAAC,CAAC;QACTkV,KAAK,EAAE,CAAC,CAAC;QACT7E,KAAK,EAAE,KAAK;QACZwF,IAAI,EAAE,KAAK;QACXE,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE;MACV,CAAC;MACD9V;IACJ,CAAC;EACL;EACA;IAAS,IAAI,CAACoC,IAAI,YAAA6T,wBAAA3T,CAAA;MAAA,YAAAA,CAAA,IAAwFgQ,eAAe,EArpCzBhX,EAAE,CAAA0N,iBAAA,CAqpCyC1N,EAAE,CAAC4a,gBAAgB,GArpC9D5a,EAAE,CAAA0N,iBAAA,CAqpCyE1N,EAAE,CAAC6a,WAAW,GArpCzF7a,EAAE,CAAA0N,iBAAA,CAqpCoG1N,EAAE,CAAC8a,eAAe,GArpCxH9a,EAAE,CAAA0N,iBAAA,CAqpCmI/K,uBAAuB,GArpC5J3C,EAAE,CAAA0N,iBAAA,CAqpCuKwC,wBAAwB,MArpCjMlQ,EAAE,CAAA0N,iBAAA,CAqpC4N1N,EAAE,CAACuL,MAAM;IAAA,CAA4C;EAAE;EACrX;IAAS,IAAI,CAACtE,IAAI,kBAtpC8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EAspCJ6P,eAAe;MAAA5P,SAAA;MAAAC,MAAA;QAAA4P,eAAA;QAAAI,oBAAA;QAAAI,qBAAA;QAAAE,8BAAA;MAAA;MAAArQ,UAAA;MAAAC,QAAA,GAtpCbvH,EAAE,CAAAwH,kBAAA,CAspC0S,CAAC;QAAEC,OAAO,EAAE9E,uBAAuB;QAAEoY,QAAQ,EAAEnY;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACjf;AACA;EAAA,QAAAuB,SAAA,oBAAAA,SAAA,KAxpCoGnE,EAAE,CAAA6H,iBAAA,CAwpCXmP,eAAe,EAAc,CAAC;IAC7G7P,IAAI,EAAEhH,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE9E,uBAAuB;QAAEoY,QAAQ,EAAEnY;MAA6B,CAAC,CAAC;MACzF0E,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEnH,EAAE,CAAC4a;IAAiB,CAAC,EAAE;MAAEzT,IAAI,EAAEnH,EAAE,CAAC6a;IAAY,CAAC,EAAE;MAAE1T,IAAI,EAAEnH,EAAE,CAAC8a;IAAgB,CAAC,EAAE;MAAE3T,IAAI,EAAE3E,IAAI,CAACI,4BAA4B;MAAEkJ,UAAU,EAAE,CAAC;QAC7K3E,IAAI,EAAE5G,MAAM;QACZuH,IAAI,EAAE,CAACnF,uBAAuB;MAClC,CAAC;IAAE,CAAC,EAAE;MAAEwE,IAAI,EAAE+I,wBAAwB;MAAEpE,UAAU,EAAE,CAAC;QACjD3E,IAAI,EAAErG;MACV,CAAC;IAAE,CAAC,EAAE;MAAEqG,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0L,eAAe,EAAE,CAAC;MACnE9P,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEiX,oBAAoB,EAAE,CAAC;MACvBlQ,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEqX,qBAAqB,EAAE,CAAC;MACxBtQ,IAAI,EAAE/G;IACV,CAAC,CAAC;IAAEuX,8BAA8B,EAAE,CAAC;MACjCxQ,IAAI,EAAE/G;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAM4a,2BAA2B,SAASzL,oBAAoB,CAAC;EAC3DtM,WAAWA,CAAC+I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;EACpD;EACAoG,yCAAyCA,CAACjF,IAAI,EAAE;IAC5C,OAAQ,IAAI,CAACrC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC4D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC,GACpE,IAAI,CAAC9H,mBAAmB,CAAC8H,IAAI,CAAC;EACtC;EACA;IAAS,IAAI,CAACxG,IAAI,YAAAmU,oCAAAjU,CAAA;MAAA,YAAAA,CAAA,IAAwFgU,2BAA2B,EAzrCrChb,EAAE,CAAA0N,iBAAA,CAyrCqD1N,EAAE,CAACe,UAAU,GAzrCpEf,EAAE,CAAA0N,iBAAA,CAyrC+ExF,gBAAgB,GAzrCjGlI,EAAE,CAAA0N,iBAAA,CAyrC4G1N,EAAE,CAACuL,MAAM,GAzrCvHvL,EAAE,CAAA0N,iBAAA,CAyrCkIpL,EAAE,CAACqL,cAAc;IAAA,CAA4D;EAAE;EACnT;IAAS,IAAI,CAAC1G,IAAI,kBA1rC8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EA0rCJ6T,2BAA2B;MAAA5T,SAAA;MAAA4N,SAAA;MAAA1N,UAAA;MAAAC,QAAA,GA1rCzBvH,EAAE,CAAAwH,kBAAA,CA0rCsJ,CAAC;QAAEC,OAAO,EAAE6H,kBAAkB;QAAE4L,WAAW,EAAEF;MAA4B,CAAC,CAAC,GA1rCnOhb,EAAE,CAAA4P,0BAAA;IAAA,EA0rCyQ;EAAE;AACjX;AACA;EAAA,QAAAzL,SAAA,oBAAAA,SAAA,KA5rCoGnE,EAAE,CAAA6H,iBAAA,CA4rCXmT,2BAA2B,EAAc,CAAC;IACzH7T,IAAI,EAAEhH,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BAA8B;MACxCC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE6H,kBAAkB;QAAE4L,WAAW,EAAEF;MAA4B,CAAC,CAAC;MACtF1T,UAAU,EAAE,IAAI;MAChBkP,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErP,IAAI,EAAEnH,EAAE,CAACe;IAAW,CAAC,EAAE;MAAEoG,IAAI,EAAEe;IAAiB,CAAC,EAAE;MAAEf,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE7E,EAAE,CAACqL,cAAc;MAAE7B,UAAU,EAAE,CAAC;QAChJ3E,IAAI,EAAE7G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA,MAAM6a,0BAA0B,SAAS5L,oBAAoB,CAAC;EAC1DtM,WAAWA,CAACgJ,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;IACvC,KAAK,CAAC,IAAIpL,UAAU,CAACsH,QAAQ,CAACuG,eAAe,CAAC,EAAE3C,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IAC9E,IAAI,CAACE,gBAAgB,GAAG,IAAIlL,UAAU,CAAEuI,QAAQ,IAAK,IAAI,CAACwC,MAAM,CAACd,iBAAiB,CAAC,MAAMhK,SAAS,CAACiH,QAAQ,EAAE,QAAQ,CAAC,CAAC9E,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACwK,UAAU,CAAC,CAAC,CAACnD,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;EACjL;EACA6I,yCAAyCA,CAACjF,IAAI,EAAE;IAC5C,OAAO,IAAI,CAACrC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC4D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC;EAC3E;EACA;IAAS,IAAI,CAACxG,IAAI,YAAAsU,mCAAApU,CAAA;MAAA,YAAAA,CAAA,IAAwFmU,0BAA0B,EArtCpCnb,EAAE,CAAA0N,iBAAA,CAqtCoDxF,gBAAgB,GArtCtElI,EAAE,CAAA0N,iBAAA,CAqtCiF1N,EAAE,CAACuL,MAAM,GArtC5FvL,EAAE,CAAA0N,iBAAA,CAqtCuGpL,EAAE,CAACqL,cAAc;IAAA,CAA4D;EAAE;EACxR;IAAS,IAAI,CAAC1G,IAAI,kBAttC8EjH,EAAE,CAAAkH,iBAAA;MAAAC,IAAA,EAstCJgU,0BAA0B;MAAA/T,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAttCxBvH,EAAE,CAAAwH,kBAAA,CAstC8G,CAAC;QAAEC,OAAO,EAAE6H,kBAAkB;QAAE4L,WAAW,EAAEC;MAA2B,CAAC,CAAC,GAttC1Lnb,EAAE,CAAA4P,0BAAA;IAAA,EAstCgO;EAAE;AACxU;AACA;EAAA,QAAAzL,SAAA,oBAAAA,SAAA,KAxtCoGnE,EAAE,CAAA6H,iBAAA,CAwtCXsT,0BAA0B,EAAc,CAAC;IACxHhU,IAAI,EAAEhH,SAAS;IACf2H,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,SAAS,EAAE,CAAC;QAAEP,OAAO,EAAE6H,kBAAkB;QAAE4L,WAAW,EAAEC;MAA2B,CAAC,CAAC;MACrF7T,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAEe;IAAiB,CAAC,EAAE;MAAEf,IAAI,EAAEnH,EAAE,CAACuL;IAAO,CAAC,EAAE;MAAEpE,IAAI,EAAE7E,EAAE,CAACqL,cAAc;MAAE7B,UAAU,EAAE,CAAC;QACvH3E,IAAI,EAAE7G;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAM+a,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACvU,IAAI,YAAAwU,4BAAAtU,CAAA;MAAA,YAAAA,CAAA,IAAwFqU,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAACE,IAAI,kBAruC8Evb,EAAE,CAAAwb,gBAAA;MAAArU,IAAA,EAquCSkU;IAAmB,EAAuD;EAAE;EACvL;IAAS,IAAI,CAACI,IAAI,kBAtuC8Ezb,EAAE,CAAA0b,gBAAA,IAsuC+B;EAAE;AACvI;AACA;EAAA,QAAAvX,SAAA,oBAAAA,SAAA,KAxuCoGnE,EAAE,CAAA6H,iBAAA,CAwuCXwT,mBAAmB,EAAc,CAAC;IACjHlU,IAAI,EAAEnG,QAAQ;IACd8G,IAAI,EAAE,CAAC;MACC6T,OAAO,EAAE,CAAC5P,aAAa,CAAC;MACxB6P,OAAO,EAAE,CAAC7P,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAM8P,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC/U,IAAI,YAAAgV,wBAAA9U,CAAA;MAAA,YAAAA,CAAA,IAAwF6U,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACN,IAAI,kBApvC8Evb,EAAE,CAAAwb,gBAAA;MAAArU,IAAA,EAovCS0U;IAAe,EAQnF;EAAE;EACzC;IAAS,IAAI,CAACJ,IAAI,kBA7vC8Ezb,EAAE,CAAA0b,gBAAA;MAAAE,OAAA,GA6vCoCrZ,UAAU,EACxI8Y,mBAAmB,EAAE9Y,UAAU,EAAE8Y,mBAAmB;IAAA,EAAI;EAAE;AACtE;AACA;EAAA,QAAAlX,SAAA,oBAAAA,SAAA,KAhwCoGnE,EAAE,CAAA6H,iBAAA,CAgwCXgU,eAAe,EAAc,CAAC;IAC7G1U,IAAI,EAAEnG,QAAQ;IACd8G,IAAI,EAAE,CAAC;MACC8T,OAAO,EAAE,CACLrZ,UAAU,EACV8Y,mBAAmB,EACnBnL,wBAAwB,EACxBvJ,yBAAyB,EACzBqQ,eAAe,EACfmE,0BAA0B,EAC1BH,2BAA2B,CAC9B;MACDW,OAAO,EAAE,CACLpZ,UAAU,EACV8Y,mBAAmB,EACnB1U,yBAAyB,EACzBqQ,eAAe,EACf9G,wBAAwB,EACxBiL,0BAA0B,EAC1BH,2BAA2B;IAEnC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASrU,yBAAyB,EAAEoF,aAAa,EAAEsP,mBAAmB,EAAErE,eAAe,EAAE9G,wBAAwB,EAAEX,oBAAoB,EAAEyL,2BAA2B,EAAEG,0BAA0B,EAAEvN,mBAAmB,EAAE3F,mBAAmB,EAAEjF,8BAA8B,EAAEkF,gBAAgB,EAAE2T,eAAe,EAAEvM,kBAAkB,EAAEvM,uBAAuB,EAAE8K,aAAa,EAAErH,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}