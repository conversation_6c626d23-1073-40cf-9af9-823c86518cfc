{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ChangePasswordComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Current password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChangePasswordComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" New password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ChangePasswordComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41);\n    i0.ɵɵelement(3, \"i\", 42)(4, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"p\", 45);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction ChangePasswordComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 40)(2, \"div\", 47);\n    i0.ɵɵelement(3, \"i\", 48)(4, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"p\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\nexport class ChangePasswordComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.message = '';\n    this.error = '';\n    this.form = this.fb.group({\n      currentPassword: ['', Validators.required],\n      newPassword: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  onSubmit() {\n    if (this.form.invalid) return;\n    const token = localStorage.getItem('token');\n    this.authService.changePassword(this.form.value, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        this.form.reset();\n        setTimeout(() => this.router.navigate(['/profile']), 1500);\n      },\n      error: err => {\n        this.error = err.error.message || 'Failed to change password';\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ChangePasswordComponent_Factory(t) {\n      return new (t || ChangePasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChangePasswordComponent,\n      selectors: [[\"app-change-password\"]],\n      decls: 60,\n      vars: 6,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-key\", \"mr-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"type\", \"password\", \"formControlName\", \"currentPassword\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\", 4, \"ngIf\"], [1, \"fas\", \"fa-key\", \"mr-1.5\", \"text-xs\"], [\"type\", \"password\", \"formControlName\", \"newPassword\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/profile\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-arrow-left\", \"mr-1.5\", \"text-xs\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1.5\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n      template: function ChangePasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵelement(23, \"i\", 13);\n          i0.ɵɵtext(24, \" Change Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 14);\n          i0.ɵɵtext(26, \" Update your account password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"form\", 16);\n          i0.ɵɵlistener(\"ngSubmit\", function ChangePasswordComponent_Template_form_ngSubmit_28_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(29, \"div\", 17)(30, \"label\", 18);\n          i0.ɵɵelement(31, \"i\", 19);\n          i0.ɵɵtext(32, \" Current Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 20);\n          i0.ɵɵelement(34, \"input\", 21);\n          i0.ɵɵelementStart(35, \"div\", 22);\n          i0.ɵɵelement(36, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, ChangePasswordComponent_div_37_Template, 3, 0, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 17)(39, \"label\", 18);\n          i0.ɵɵelement(40, \"i\", 25);\n          i0.ɵɵtext(41, \" New Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 20);\n          i0.ɵɵelement(43, \"input\", 26);\n          i0.ɵɵelementStart(44, \"div\", 22);\n          i0.ɵɵelement(45, \"div\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(46, ChangePasswordComponent_div_46_Template, 3, 0, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, ChangePasswordComponent_div_47_Template, 8, 1, \"div\", 27);\n          i0.ɵɵtemplate(48, ChangePasswordComponent_div_48_Template, 8, 1, \"div\", 28);\n          i0.ɵɵelementStart(49, \"button\", 29);\n          i0.ɵɵelement(50, \"div\", 30)(51, \"div\", 31);\n          i0.ɵɵelementStart(52, \"span\", 32);\n          i0.ɵɵelement(53, \"i\", 33);\n          i0.ɵɵtext(54, \" Update Password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 34)(56, \"div\")(57, \"a\", 35);\n          i0.ɵɵelement(58, \"i\", 36);\n          i0.ɵɵtext(59, \" Back to Profile \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.form.get(\"currentPassword\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.form.get(\"currentPassword\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.form.get(\"newPassword\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.form.get(\"newPassword\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjaGFuZ2UtcGFzc3dvcmQuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvY2hhbmdlLXBhc3N3b3JkL2NoYW5nZS1wYXNzd29yZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "error", "ctx_r3", "message", "ChangePasswordComponent", "constructor", "fb", "authService", "router", "form", "group", "currentPassword", "required", "newPassword", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "invalid", "token", "localStorage", "getItem", "changePassword", "value", "subscribe", "next", "res", "reset", "setTimeout", "navigate", "err", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ChangePasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ChangePasswordComponent_Template_form_ngSubmit_28_listener", "ɵɵtemplate", "ChangePasswordComponent_div_37_Template", "ChangePasswordComponent_div_46_Template", "ChangePasswordComponent_div_47_Template", "ChangePasswordComponent_div_48_Template", "ɵɵproperty", "tmp_1_0", "get", "touched", "tmp_2_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\change-password\\change-password.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\change-password\\change-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-change-password',\r\n  templateUrl: './change-password.component.html',\r\n  styleUrls: ['./change-password.component.css'],\r\n})\r\nexport class ChangePasswordComponent {\r\n  form: FormGroup;\r\n  message = '';\r\n  error = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {\r\n    this.form = this.fb.group({\r\n      currentPassword: ['', Validators.required],\r\n      newPassword: ['', [Validators.required, Validators.minLength(6)]],\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.form.invalid) return;\r\n\r\n    const token = localStorage.getItem('token');\r\n    this.authService.changePassword(this.form.value, token!).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n        this.form.reset();\r\n        setTimeout(() => this.router.navigate(['/profile']), 1500);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error.message || 'Failed to change password';\r\n        this.message = '';\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen flex items-center justify-center relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-key mr-2\"></i>\r\n          Change Password\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          Update your account password\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"p-6\">\r\n        <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"space-y-5\">\r\n          <!-- Current Password -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-lock mr-1.5 text-xs\"></i>\r\n              Current Password\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                type=\"password\"\r\n                formControlName=\"currentPassword\"\r\n                placeholder=\"••••••••\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"\r\n                form.get('currentPassword')?.invalid &&\r\n                form.get('currentPassword')?.touched\r\n              \"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              Current password is required\r\n            </div>\r\n          </div>\r\n\r\n          <!-- New Password -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-key mr-1.5 text-xs\"></i>\r\n              New Password\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                type=\"password\"\r\n                formControlName=\"newPassword\"\r\n                placeholder=\"••••••••\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"\r\n                form.get('newPassword')?.invalid &&\r\n                form.get('newPassword')?.touched\r\n              \"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              New password is required\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n            [disabled]=\"form.invalid\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <i class=\"fas fa-save mr-2\"></i>\r\n              Update Password\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Back Link -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div>\r\n              <a\r\n                routerLink=\"/profile\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium flex items-center justify-center\"\r\n              >\r\n                <i class=\"fas fa-arrow-left mr-1.5 text-xs\"></i>\r\n                Back to Profile\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICiFvDC,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAG,MAAA,qCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IA0BNJ,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAIRJ,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;ADhKhB,OAAM,MAAOC,uBAAuB;EAKlCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IAOR,IAAI,CAACQ,IAAI,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MACxBC,eAAe,EAAE,CAAC,EAAE,EAAEnB,UAAU,CAACoB,QAAQ,CAAC;MAC1CC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,CAAC;KACjE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,IAAI,CAACO,OAAO,EAAE;IAEvB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACZ,WAAW,CAACa,cAAc,CAAC,IAAI,CAACX,IAAI,CAACY,KAAK,EAAEJ,KAAM,CAAC,CAACK,SAAS,CAAC;MACjEC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrB,OAAO,GAAGqB,GAAG,CAACrB,OAAO;QAC1B,IAAI,CAACF,KAAK,GAAG,EAAE;QACf,IAAI,CAACQ,IAAI,CAACgB,KAAK,EAAE;QACjBC,UAAU,CAAC,MAAM,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC;MAC5D,CAAC;MACD1B,KAAK,EAAG2B,GAAG,IAAI;QACb,IAAI,CAAC3B,KAAK,GAAG2B,GAAG,CAAC3B,KAAK,CAACE,OAAO,IAAI,2BAA2B;QAC7D,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBAhCWC,uBAAuB,EAAAX,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtC,EAAA,CAAAoC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAoC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB/B,uBAAuB;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpCjD,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAI,YAAA,EAAM;UAIVJ,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAG,MAAA,sCACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAINJ,EAAA,CAAAC,cAAA,eAAiB;UACUD,EAAA,CAAAmD,UAAA,sBAAAC,2DAAA;YAAA,OAAYF,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAE9CtB,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAG,MAAA,0BACF;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAqD,UAAA,KAAAC,uCAAA,kBASM;UACRtD,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAAyC;UACzCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAqD,UAAA,KAAAE,uCAAA,kBASM;UACRvD,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAqD,UAAA,KAAAG,uCAAA,kBAoBM;UAGNxD,EAAA,CAAAqD,UAAA,KAAAI,uCAAA,kBAoBM;UAGNzD,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAE,SAAA,aAAgC;UAChCF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAITJ,EAAA,CAAAC,cAAA,eAEC;UAMKD,EAAA,CAAAE,SAAA,aAAgD;UAChDF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;UApJJJ,EAAA,CAAAK,SAAA,IAAkB;UAAlBL,EAAA,CAAA0D,UAAA,cAAAR,GAAA,CAAAlC,IAAA,CAAkB;UAyBjBhB,EAAA,CAAAK,SAAA,GAGD;UAHCL,EAAA,CAAA0D,UAAA,WAAAC,OAAA,GAAAT,GAAA,CAAAlC,IAAA,CAAA4C,GAAA,sCAAAD,OAAA,CAAApC,OAAA,OAAAoC,OAAA,GAAAT,GAAA,CAAAlC,IAAA,CAAA4C,GAAA,sCAAAD,OAAA,CAAAE,OAAA,EAGD;UAgCC7D,EAAA,CAAAK,SAAA,GAGD;UAHCL,EAAA,CAAA0D,UAAA,WAAAI,OAAA,GAAAZ,GAAA,CAAAlC,IAAA,CAAA4C,GAAA,kCAAAE,OAAA,CAAAvC,OAAA,OAAAuC,OAAA,GAAAZ,GAAA,CAAAlC,IAAA,CAAA4C,GAAA,kCAAAE,OAAA,CAAAD,OAAA,EAGD;UAUD7D,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA0D,UAAA,SAAAR,GAAA,CAAA1C,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA0D,UAAA,SAAAR,GAAA,CAAAxC,OAAA,CAAa;UAyBdV,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA0D,UAAA,aAAAR,GAAA,CAAAlC,IAAA,CAAAO,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}