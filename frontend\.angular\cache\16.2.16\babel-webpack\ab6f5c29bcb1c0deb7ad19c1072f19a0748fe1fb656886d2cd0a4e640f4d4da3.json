{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from './active-call.component';\nimport * as i0 from \"@angular/core\";\nexport class ActiveCallModule {\n  static {\n    this.ɵfac = function ActiveCallModule_Factory(t) {\n      return new (t || ActiveCallModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ActiveCallModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ActiveCallModule, {\n    declarations: [ActiveCallComponent],\n    imports: [CommonModule],\n    exports: [ActiveCallComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ActiveCallComponent", "ActiveCallModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ActiveCallComponent } from './active-call.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ActiveCallComponent\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    ActiveCallComponent\r\n  ]\r\n})\r\nexport class ActiveCallModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;;AAa7D,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBANzBF,YAAY;IAAA;EAAA;;;2EAMHE,gBAAgB;IAAAC,YAAA,GATzBF,mBAAmB;IAAAG,OAAA,GAGnBJ,YAAY;IAAAK,OAAA,GAGZJ,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}