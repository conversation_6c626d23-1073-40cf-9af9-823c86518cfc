{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { parse } from 'graphql';\nvar docCache = new Map();\nvar fragmentSourceMap = new Map();\nvar printFragmentWarnings = true;\nvar experimentalFragmentVariables = false;\nfunction normalize(string) {\n  return string.replace(/[\\s,]+/g, ' ').trim();\n}\nfunction cacheKeyFromLoc(loc) {\n  return normalize(loc.source.body.substring(loc.start, loc.end));\n}\nfunction processFragments(ast) {\n  var seenKeys = new Set();\n  var definitions = [];\n  ast.definitions.forEach(function (fragmentDefinition) {\n    if (fragmentDefinition.kind === 'FragmentDefinition') {\n      var fragmentName = fragmentDefinition.name.value;\n      var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc);\n      var sourceKeySet = fragmentSourceMap.get(fragmentName);\n      if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n        if (printFragmentWarnings) {\n          console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\" + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\" + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n        }\n      } else if (!sourceKeySet) {\n        fragmentSourceMap.set(fragmentName, sourceKeySet = new Set());\n      }\n      sourceKeySet.add(sourceKey);\n      if (!seenKeys.has(sourceKey)) {\n        seenKeys.add(sourceKey);\n        definitions.push(fragmentDefinition);\n      }\n    } else {\n      definitions.push(fragmentDefinition);\n    }\n  });\n  return __assign(__assign({}, ast), {\n    definitions: definitions\n  });\n}\nfunction stripLoc(doc) {\n  var workSet = new Set(doc.definitions);\n  workSet.forEach(function (node) {\n    if (node.loc) delete node.loc;\n    Object.keys(node).forEach(function (key) {\n      var value = node[key];\n      if (value && typeof value === 'object') {\n        workSet.add(value);\n      }\n    });\n  });\n  var loc = doc.loc;\n  if (loc) {\n    delete loc.startToken;\n    delete loc.endToken;\n  }\n  return doc;\n}\nfunction parseDocument(source) {\n  var cacheKey = normalize(source);\n  if (!docCache.has(cacheKey)) {\n    var parsed = parse(source, {\n      experimentalFragmentVariables: experimentalFragmentVariables,\n      allowLegacyFragmentVariables: experimentalFragmentVariables\n    });\n    if (!parsed || parsed.kind !== 'Document') {\n      throw new Error('Not a valid GraphQL document.');\n    }\n    docCache.set(cacheKey, stripLoc(processFragments(parsed)));\n  }\n  return docCache.get(cacheKey);\n}\nexport function gql(literals) {\n  var args = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    args[_i - 1] = arguments[_i];\n  }\n  if (typeof literals === 'string') {\n    literals = [literals];\n  }\n  var result = literals[0];\n  args.forEach(function (arg, i) {\n    if (arg && arg.kind === 'Document') {\n      result += arg.loc.source.body;\n    } else {\n      result += arg;\n    }\n    result += literals[i + 1];\n  });\n  return parseDocument(result);\n}\nexport function resetCaches() {\n  docCache.clear();\n  fragmentSourceMap.clear();\n}\nexport function disableFragmentWarnings() {\n  printFragmentWarnings = false;\n}\nexport function enableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = true;\n}\nexport function disableExperimentalFragmentVariables() {\n  experimentalFragmentVariables = false;\n}\nvar extras = {\n  gql: gql,\n  resetCaches: resetCaches,\n  disableFragmentWarnings: disableFragmentWarnings,\n  enableExperimentalFragmentVariables: enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables: disableExperimentalFragmentVariables\n};\n(function (gql_1) {\n  gql_1.gql = extras.gql, gql_1.resetCaches = extras.resetCaches, gql_1.disableFragmentWarnings = extras.disableFragmentWarnings, gql_1.enableExperimentalFragmentVariables = extras.enableExperimentalFragmentVariables, gql_1.disableExperimentalFragmentVariables = extras.disableExperimentalFragmentVariables;\n})(gql || (gql = {}));\ngql[\"default\"] = gql;\nexport default gql;", "map": {"version": 3, "names": ["__assign", "parse", "<PERSON><PERSON><PERSON><PERSON>", "Map", "fragmentSourceMap", "printFragmentWarnings", "experimentalFragmentVariables", "normalize", "string", "replace", "trim", "cacheKeyFromLoc", "loc", "source", "body", "substring", "start", "end", "processFragments", "ast", "<PERSON><PERSON><PERSON><PERSON>", "Set", "definitions", "for<PERSON>ach", "fragmentDefinition", "kind", "fragmentName", "name", "value", "sourceKey", "sourceKeySet", "get", "has", "console", "warn", "set", "add", "push", "stripLoc", "doc", "workSet", "node", "Object", "keys", "key", "startToken", "endToken", "parseDocument", "cache<PERSON>ey", "parsed", "allowLegacyFragmentVariables", "Error", "gql", "literals", "args", "_i", "arguments", "length", "result", "arg", "i", "resetCaches", "clear", "disableFragmentWarnings", "enableExperimentalFragmentVariables", "disableExperimentalFragmentVariables", "extras", "gql_1"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql-tag/lib/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { parse } from 'graphql';\nvar docCache = new Map();\nvar fragmentSourceMap = new Map();\nvar printFragmentWarnings = true;\nvar experimentalFragmentVariables = false;\nfunction normalize(string) {\n    return string.replace(/[\\s,]+/g, ' ').trim();\n}\nfunction cacheKeyFromLoc(loc) {\n    return normalize(loc.source.body.substring(loc.start, loc.end));\n}\nfunction processFragments(ast) {\n    var seenKeys = new Set();\n    var definitions = [];\n    ast.definitions.forEach(function (fragmentDefinition) {\n        if (fragmentDefinition.kind === 'FragmentDefinition') {\n            var fragmentName = fragmentDefinition.name.value;\n            var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc);\n            var sourceKeySet = fragmentSourceMap.get(fragmentName);\n            if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n                if (printFragmentWarnings) {\n                    console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n                        + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n                        + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n                }\n            }\n            else if (!sourceKeySet) {\n                fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n            }\n            sourceKeySet.add(sourceKey);\n            if (!seenKeys.has(sourceKey)) {\n                seenKeys.add(sourceKey);\n                definitions.push(fragmentDefinition);\n            }\n        }\n        else {\n            definitions.push(fragmentDefinition);\n        }\n    });\n    return __assign(__assign({}, ast), { definitions: definitions });\n}\nfunction stripLoc(doc) {\n    var workSet = new Set(doc.definitions);\n    workSet.forEach(function (node) {\n        if (node.loc)\n            delete node.loc;\n        Object.keys(node).forEach(function (key) {\n            var value = node[key];\n            if (value && typeof value === 'object') {\n                workSet.add(value);\n            }\n        });\n    });\n    var loc = doc.loc;\n    if (loc) {\n        delete loc.startToken;\n        delete loc.endToken;\n    }\n    return doc;\n}\nfunction parseDocument(source) {\n    var cacheKey = normalize(source);\n    if (!docCache.has(cacheKey)) {\n        var parsed = parse(source, {\n            experimentalFragmentVariables: experimentalFragmentVariables,\n            allowLegacyFragmentVariables: experimentalFragmentVariables\n        });\n        if (!parsed || parsed.kind !== 'Document') {\n            throw new Error('Not a valid GraphQL document.');\n        }\n        docCache.set(cacheKey, stripLoc(processFragments(parsed)));\n    }\n    return docCache.get(cacheKey);\n}\nexport function gql(literals) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (typeof literals === 'string') {\n        literals = [literals];\n    }\n    var result = literals[0];\n    args.forEach(function (arg, i) {\n        if (arg && arg.kind === 'Document') {\n            result += arg.loc.source.body;\n        }\n        else {\n            result += arg;\n        }\n        result += literals[i + 1];\n    });\n    return parseDocument(result);\n}\nexport function resetCaches() {\n    docCache.clear();\n    fragmentSourceMap.clear();\n}\nexport function disableFragmentWarnings() {\n    printFragmentWarnings = false;\n}\nexport function enableExperimentalFragmentVariables() {\n    experimentalFragmentVariables = true;\n}\nexport function disableExperimentalFragmentVariables() {\n    experimentalFragmentVariables = false;\n}\nvar extras = {\n    gql: gql,\n    resetCaches: resetCaches,\n    disableFragmentWarnings: disableFragmentWarnings,\n    enableExperimentalFragmentVariables: enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables: disableExperimentalFragmentVariables\n};\n(function (gql_1) {\n    gql_1.gql = extras.gql, gql_1.resetCaches = extras.resetCaches, gql_1.disableFragmentWarnings = extras.disableFragmentWarnings, gql_1.enableExperimentalFragmentVariables = extras.enableExperimentalFragmentVariables, gql_1.disableExperimentalFragmentVariables = extras.disableExperimentalFragmentVariables;\n})(gql || (gql = {}));\ngql[\"default\"] = gql;\nexport default gql;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,KAAK,QAAQ,SAAS;AAC/B,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AACxB,IAAIC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC;AACjC,IAAIE,qBAAqB,GAAG,IAAI;AAChC,IAAIC,6BAA6B,GAAG,KAAK;AACzC,SAASC,SAASA,CAACC,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;AAChD;AACA,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAOL,SAAS,CAACK,GAAG,CAACC,MAAM,CAACC,IAAI,CAACC,SAAS,CAACH,GAAG,CAACI,KAAK,EAAEJ,GAAG,CAACK,GAAG,CAAC,CAAC;AACnE;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB,IAAIC,WAAW,GAAG,EAAE;EACpBH,GAAG,CAACG,WAAW,CAACC,OAAO,CAAC,UAAUC,kBAAkB,EAAE;IAClD,IAAIA,kBAAkB,CAACC,IAAI,KAAK,oBAAoB,EAAE;MAClD,IAAIC,YAAY,GAAGF,kBAAkB,CAACG,IAAI,CAACC,KAAK;MAChD,IAAIC,SAAS,GAAGlB,eAAe,CAACa,kBAAkB,CAACZ,GAAG,CAAC;MACvD,IAAIkB,YAAY,GAAG1B,iBAAiB,CAAC2B,GAAG,CAACL,YAAY,CAAC;MACtD,IAAII,YAAY,IAAI,CAACA,YAAY,CAACE,GAAG,CAACH,SAAS,CAAC,EAAE;QAC9C,IAAIxB,qBAAqB,EAAE;UACvB4B,OAAO,CAACC,IAAI,CAAC,8BAA8B,GAAGR,YAAY,GAAG,oBAAoB,GAC3E,iGAAiG,GACjG,8EAA8E,CAAC;QACzF;MACJ,CAAC,MACI,IAAI,CAACI,YAAY,EAAE;QACpB1B,iBAAiB,CAAC+B,GAAG,CAACT,YAAY,EAAEI,YAAY,GAAG,IAAIT,GAAG,CAAD,CAAC,CAAC;MAC/D;MACAS,YAAY,CAACM,GAAG,CAACP,SAAS,CAAC;MAC3B,IAAI,CAACT,QAAQ,CAACY,GAAG,CAACH,SAAS,CAAC,EAAE;QAC1BT,QAAQ,CAACgB,GAAG,CAACP,SAAS,CAAC;QACvBP,WAAW,CAACe,IAAI,CAACb,kBAAkB,CAAC;MACxC;IACJ,CAAC,MACI;MACDF,WAAW,CAACe,IAAI,CAACb,kBAAkB,CAAC;IACxC;EACJ,CAAC,CAAC;EACF,OAAOxB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmB,GAAG,CAAC,EAAE;IAAEG,WAAW,EAAEA;EAAY,CAAC,CAAC;AACpE;AACA,SAASgB,QAAQA,CAACC,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAG,IAAInB,GAAG,CAACkB,GAAG,CAACjB,WAAW,CAAC;EACtCkB,OAAO,CAACjB,OAAO,CAAC,UAAUkB,IAAI,EAAE;IAC5B,IAAIA,IAAI,CAAC7B,GAAG,EACR,OAAO6B,IAAI,CAAC7B,GAAG;IACnB8B,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAAClB,OAAO,CAAC,UAAUqB,GAAG,EAAE;MACrC,IAAIhB,KAAK,GAAGa,IAAI,CAACG,GAAG,CAAC;MACrB,IAAIhB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACpCY,OAAO,CAACJ,GAAG,CAACR,KAAK,CAAC;MACtB;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,IAAIhB,GAAG,GAAG2B,GAAG,CAAC3B,GAAG;EACjB,IAAIA,GAAG,EAAE;IACL,OAAOA,GAAG,CAACiC,UAAU;IACrB,OAAOjC,GAAG,CAACkC,QAAQ;EACvB;EACA,OAAOP,GAAG;AACd;AACA,SAASQ,aAAaA,CAAClC,MAAM,EAAE;EAC3B,IAAImC,QAAQ,GAAGzC,SAAS,CAACM,MAAM,CAAC;EAChC,IAAI,CAACX,QAAQ,CAAC8B,GAAG,CAACgB,QAAQ,CAAC,EAAE;IACzB,IAAIC,MAAM,GAAGhD,KAAK,CAACY,MAAM,EAAE;MACvBP,6BAA6B,EAAEA,6BAA6B;MAC5D4C,4BAA4B,EAAE5C;IAClC,CAAC,CAAC;IACF,IAAI,CAAC2C,MAAM,IAAIA,MAAM,CAACxB,IAAI,KAAK,UAAU,EAAE;MACvC,MAAM,IAAI0B,KAAK,CAAC,+BAA+B,CAAC;IACpD;IACAjD,QAAQ,CAACiC,GAAG,CAACa,QAAQ,EAAEV,QAAQ,CAACpB,gBAAgB,CAAC+B,MAAM,CAAC,CAAC,CAAC;EAC9D;EACA,OAAO/C,QAAQ,CAAC6B,GAAG,CAACiB,QAAQ,CAAC;AACjC;AACA,OAAO,SAASI,GAAGA,CAACC,QAAQ,EAAE;EAC1B,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAChC;EACA,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;IAC9BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACzB;EACA,IAAIK,MAAM,GAAGL,QAAQ,CAAC,CAAC,CAAC;EACxBC,IAAI,CAAC/B,OAAO,CAAC,UAAUoC,GAAG,EAAEC,CAAC,EAAE;IAC3B,IAAID,GAAG,IAAIA,GAAG,CAAClC,IAAI,KAAK,UAAU,EAAE;MAChCiC,MAAM,IAAIC,GAAG,CAAC/C,GAAG,CAACC,MAAM,CAACC,IAAI;IACjC,CAAC,MACI;MACD4C,MAAM,IAAIC,GAAG;IACjB;IACAD,MAAM,IAAIL,QAAQ,CAACO,CAAC,GAAG,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOb,aAAa,CAACW,MAAM,CAAC;AAChC;AACA,OAAO,SAASG,WAAWA,CAAA,EAAG;EAC1B3D,QAAQ,CAAC4D,KAAK,CAAC,CAAC;EAChB1D,iBAAiB,CAAC0D,KAAK,CAAC,CAAC;AAC7B;AACA,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACtC1D,qBAAqB,GAAG,KAAK;AACjC;AACA,OAAO,SAAS2D,mCAAmCA,CAAA,EAAG;EAClD1D,6BAA6B,GAAG,IAAI;AACxC;AACA,OAAO,SAAS2D,oCAAoCA,CAAA,EAAG;EACnD3D,6BAA6B,GAAG,KAAK;AACzC;AACA,IAAI4D,MAAM,GAAG;EACTd,GAAG,EAAEA,GAAG;EACRS,WAAW,EAAEA,WAAW;EACxBE,uBAAuB,EAAEA,uBAAuB;EAChDC,mCAAmC,EAAEA,mCAAmC;EACxEC,oCAAoC,EAAEA;AAC1C,CAAC;AACD,CAAC,UAAUE,KAAK,EAAE;EACdA,KAAK,CAACf,GAAG,GAAGc,MAAM,CAACd,GAAG,EAAEe,KAAK,CAACN,WAAW,GAAGK,MAAM,CAACL,WAAW,EAAEM,KAAK,CAACJ,uBAAuB,GAAGG,MAAM,CAACH,uBAAuB,EAAEI,KAAK,CAACH,mCAAmC,GAAGE,MAAM,CAACF,mCAAmC,EAAEG,KAAK,CAACF,oCAAoC,GAAGC,MAAM,CAACD,oCAAoC;AACpT,CAAC,EAAEb,GAAG,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrBA,GAAG,CAAC,SAAS,CAAC,GAAGA,GAAG;AACpB,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}