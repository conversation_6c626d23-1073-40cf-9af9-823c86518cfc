{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { getNamedType, isLeafType } from '../../type/definition.mjs';\n\n/**\n * <PERSON>alar leafs\n *\n * A GraphQL document is valid only if all leaf fields (fields without\n * sub selections) are of scalar or enum types.\n */\nexport function ScalarLeafsRule(context) {\n  return {\n    Field(node) {\n      const type = context.getType();\n      const selectionSet = node.selectionSet;\n      if (type) {\n        if (isLeafType(getNamedType(type))) {\n          if (selectionSet) {\n            const fieldName = node.name.value;\n            const typeStr = inspect(type);\n            context.reportError(new GraphQLError(`Field \"${fieldName}\" must not have a selection since type \"${typeStr}\" has no subfields.`, {\n              nodes: selectionSet\n            }));\n          }\n        } else if (!selectionSet) {\n          const fieldName = node.name.value;\n          const typeStr = inspect(type);\n          context.reportError(new GraphQLError(`Field \"${fieldName}\" of type \"${typeStr}\" must have a selection of subfields. Did you mean \"${fieldName} { ... }\"?`, {\n            nodes: node\n          }));\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["inspect", "GraphQLError", "getNamedType", "isLeafType", "ScalarLeafsRule", "context", "Field", "node", "type", "getType", "selectionSet", "fieldName", "name", "value", "typeStr", "reportError", "nodes"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/ScalarLeafsRule.mjs"], "sourcesContent": ["import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { getNamedType, isLeafType } from '../../type/definition.mjs';\n\n/**\n * <PERSON>ala<PERSON> leafs\n *\n * A GraphQL document is valid only if all leaf fields (fields without\n * sub selections) are of scalar or enum types.\n */\nexport function ScalarLeafsRule(context) {\n  return {\n    Field(node) {\n      const type = context.getType();\n      const selectionSet = node.selectionSet;\n\n      if (type) {\n        if (isLeafType(getNamedType(type))) {\n          if (selectionSet) {\n            const fieldName = node.name.value;\n            const typeStr = inspect(type);\n            context.reportError(\n              new GraphQLError(\n                `Field \"${fieldName}\" must not have a selection since type \"${typeStr}\" has no subfields.`,\n                {\n                  nodes: selectionSet,\n                },\n              ),\n            );\n          }\n        } else if (!selectionSet) {\n          const fieldName = node.name.value;\n          const typeStr = inspect(type);\n          context.reportError(\n            new GraphQLError(\n              `Field \"${fieldName}\" of type \"${typeStr}\" must have a selection of subfields. Did you mean \"${fieldName} { ... }\"?`,\n              {\n                nodes: node,\n              },\n            ),\n          );\n        }\n      }\n    },\n  };\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,2BAA2B;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAE;EACvC,OAAO;IACLC,KAAKA,CAACC,IAAI,EAAE;MACV,MAAMC,IAAI,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC;MAC9B,MAAMC,YAAY,GAAGH,IAAI,CAACG,YAAY;MAEtC,IAAIF,IAAI,EAAE;QACR,IAAIL,UAAU,CAACD,YAAY,CAACM,IAAI,CAAC,CAAC,EAAE;UAClC,IAAIE,YAAY,EAAE;YAChB,MAAMC,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAACC,KAAK;YACjC,MAAMC,OAAO,GAAGd,OAAO,CAACQ,IAAI,CAAC;YAC7BH,OAAO,CAACU,WAAW,CACjB,IAAId,YAAY,CACb,UAASU,SAAU,2CAA0CG,OAAQ,qBAAoB,EAC1F;cACEE,KAAK,EAAEN;YACT,CACF,CACF,CAAC;UACH;QACF,CAAC,MAAM,IAAI,CAACA,YAAY,EAAE;UACxB,MAAMC,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAACC,KAAK;UACjC,MAAMC,OAAO,GAAGd,OAAO,CAACQ,IAAI,CAAC;UAC7BH,OAAO,CAACU,WAAW,CACjB,IAAId,YAAY,CACb,UAASU,SAAU,cAAaG,OAAQ,uDAAsDH,SAAU,YAAW,EACpH;YACEK,KAAK,EAAET;UACT,CACF,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}