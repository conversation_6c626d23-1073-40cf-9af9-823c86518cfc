{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n  return operate((source, subscriber) => {\n    let window;\n    let closingSubscriber;\n    const handleError = err => {\n      window.error(err);\n      subscriber.error(err);\n    };\n    const openWindow = () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window === null || window === void 0 ? void 0 : window.complete();\n      window = new Subject();\n      subscriber.next(window.asObservable());\n      let closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector());\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      closingNotifier.subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError));\n    };\n    openWindow();\n    source.subscribe(createOperatorSubscriber(subscriber, value => window.next(value), () => {\n      window.complete();\n      subscriber.complete();\n    }, handleError, () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "innerFrom", "windowWhen", "closingSelector", "source", "subscriber", "window", "closingSubscriber", "handleError", "err", "error", "openWindow", "unsubscribe", "complete", "next", "asObservable", "closingNotifier", "subscribe", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/rxjs/dist/esm/internal/operators/windowWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n    return operate((source, subscriber) => {\n        let window;\n        let closingSubscriber;\n        const handleError = (err) => {\n            window.error(err);\n            subscriber.error(err);\n        };\n        const openWindow = () => {\n            closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n            window === null || window === void 0 ? void 0 : window.complete();\n            window = new Subject();\n            subscriber.next(window.asObservable());\n            let closingNotifier;\n            try {\n                closingNotifier = innerFrom(closingSelector());\n            }\n            catch (err) {\n                handleError(err);\n                return;\n            }\n            closingNotifier.subscribe((closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError)));\n        };\n        openWindow();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => window.next(value), () => {\n            window.complete();\n            subscriber.complete();\n        }, handleError, () => {\n            closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n            window = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAE;EACxC,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,MAAM;IACV,IAAIC,iBAAiB;IACrB,MAAMC,WAAW,GAAIC,GAAG,IAAK;MACzBH,MAAM,CAACI,KAAK,CAACD,GAAG,CAAC;MACjBJ,UAAU,CAACK,KAAK,CAACD,GAAG,CAAC;IACzB,CAAC;IACD,MAAME,UAAU,GAAGA,CAAA,KAAM;MACrBJ,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,WAAW,CAAC,CAAC;MACrGN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,QAAQ,CAAC,CAAC;MACjEP,MAAM,GAAG,IAAIR,OAAO,CAAC,CAAC;MACtBO,UAAU,CAACS,IAAI,CAACR,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC;MACtC,IAAIC,eAAe;MACnB,IAAI;QACAA,eAAe,GAAGf,SAAS,CAACE,eAAe,CAAC,CAAC,CAAC;MAClD,CAAC,CACD,OAAOM,GAAG,EAAE;QACRD,WAAW,CAACC,GAAG,CAAC;QAChB;MACJ;MACAO,eAAe,CAACC,SAAS,CAAEV,iBAAiB,GAAGP,wBAAwB,CAACK,UAAU,EAAEM,UAAU,EAAEA,UAAU,EAAEH,WAAW,CAAE,CAAC;IAC9H,CAAC;IACDG,UAAU,CAAC,CAAC;IACZP,MAAM,CAACa,SAAS,CAACjB,wBAAwB,CAACK,UAAU,EAAGa,KAAK,IAAKZ,MAAM,CAACQ,IAAI,CAACI,KAAK,CAAC,EAAE,MAAM;MACvFZ,MAAM,CAACO,QAAQ,CAAC,CAAC;MACjBR,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEL,WAAW,EAAE,MAAM;MAClBD,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,WAAW,CAAC,CAAC;MACrGN,MAAM,GAAG,IAAI;IACjB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}