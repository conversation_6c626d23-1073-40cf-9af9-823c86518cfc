{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class FileService {\n  constructor() {}\n  /**\n   * Génère une URL de téléchargement pour un fichier\n   * @param filePath Chemin du fichier\n   * @returns URL de téléchargement\n   */\n  getDownloadUrl(filePath) {\n    // Si le chemin est vide ou null, retourner une chaîne vide\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier, peu importe le format du chemin\n    let fileName = filePath;\n    // Si c'est un chemin complet (contient C:/ ou autre)\n    if (filePath.includes('C:') || filePath.includes('/') || filePath.includes('\\\\')) {\n      // Prendre uniquement le nom du fichier (dernière partie après / ou \\)\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  /**\n   * Extrait le nom du fichier à partir d'un chemin\n   * @param filePath Chemin du fichier\n   * @returns Nom du fichier\n   */\n  getFileName(filePath) {\n    if (!filePath) return 'fichier';\n    // Si c'est un chemin complet (contient / ou \\)\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function FileService_Factory(t) {\n      return new (t || FileService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileService,\n      factory: FileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "FileService", "constructor", "getDownloadUrl", "filePath", "fileName", "includes", "parts", "split", "length", "urlBackend", "getFileName", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\file.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class FileService {\r\n  \r\n  constructor() { }\r\n  \r\n  /**\r\n   * Génère une URL de téléchargement pour un fichier\r\n   * @param filePath Chemin du fichier\r\n   * @returns URL de téléchargement\r\n   */\r\n  getDownloadUrl(filePath: string): string {\r\n    // Si le chemin est vide ou null, retourner une chaîne vide\r\n    if (!filePath) return '';\r\n    \r\n    // Extraire uniquement le nom du fichier, peu importe le format du chemin\r\n    let fileName = filePath;\r\n    \r\n    // Si c'est un chemin complet (contient C:/ ou autre)\r\n    if (filePath.includes('C:') || filePath.includes('/') || filePath.includes('\\\\')) {\r\n      // Prendre uniquement le nom du fichier (dernière partie après / ou \\)\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n    \r\n    // Utiliser l'endpoint API spécifique pour le téléchargement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n  \r\n  /**\r\n   * Extrait le nom du fichier à partir d'un chemin\r\n   * @param filePath Chemin du fichier\r\n   * @returns Nom du fichier\r\n   */\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'fichier';\r\n    \r\n    // Si c'est un chemin complet (contient / ou \\)\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n    \r\n    return filePath;\r\n  }\r\n}\r\n\r\n\r\n\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,8BAA8B;;AAK1D,OAAM,MAAOC,WAAW;EAEtBC,YAAA,GAAgB;EAEhB;;;;;EAKAC,cAAcA,CAACC,QAAgB;IAC7B;IACA,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MAChF;MACA,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGT,WAAW,CAACU,UAAU,uBAAuBL,QAAQ,EAAE;EACnE;EAEA;;;;;EAKAM,WAAWA,CAACP,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOL,QAAQ;EACjB;;;uBA1CWH,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAW,OAAA,EAAXX,WAAW,CAAAY,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}