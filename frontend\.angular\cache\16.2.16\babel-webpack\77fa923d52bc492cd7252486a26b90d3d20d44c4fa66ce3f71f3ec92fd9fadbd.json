{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { equal } from \"@wry/equality\";\nimport { DeepMerger } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/index.js\";\nimport { isNonEmptyArray, graphQLResultHasError, canUseWeakMap } from \"../utilities/index.js\";\nimport { NetworkStatus } from \"./networkStatus.js\";\nvar destructiveMethodCounts = new (canUseWeakMap ? WeakMap : Map)();\nfunction wrapDestructiveCacheMethod(cache, methodName) {\n  var original = cache[methodName];\n  if (typeof original === \"function\") {\n    // @ts-expect-error this is just too generic to be typed correctly\n    cache[methodName] = function () {\n      destructiveMethodCounts.set(cache,\n      // The %1e15 allows the count to wrap around to 0 safely every\n      // quadrillion evictions, so there's no risk of overflow. To be\n      // clear, this is more of a pedantic principle than something\n      // that matters in any conceivable practical scenario.\n      (destructiveMethodCounts.get(cache) + 1) % 1e15);\n      // @ts-expect-error this is just too generic to be typed correctly\n      return original.apply(this, arguments);\n    };\n  }\n}\n// A QueryInfo object represents a single query managed by the\n// QueryManager, which tracks all QueryInfo objects by queryId in its\n// this.queries Map. QueryInfo objects store the latest results and errors\n// for the given query, and are responsible for reporting those results to\n// the corresponding ObservableQuery, via the QueryInfo.notify method.\n// Results are reported asynchronously whenever setDiff marks the\n// QueryInfo object as dirty, though a call to the QueryManager's\n// broadcastQueries method may trigger the notification before it happens\n// automatically. This class used to be a simple interface type without\n// any field privacy or meaningful methods, which is why it still has so\n// many public fields. The effort to lock down and simplify the QueryInfo\n// interface is ongoing, and further improvements are welcome.\nvar QueryInfo = /** @class */function () {\n  function QueryInfo(queryManager, queryId) {\n    if (queryId === void 0) {\n      queryId = queryManager.generateQueryId();\n    }\n    this.queryId = queryId;\n    this.document = null;\n    this.lastRequestId = 1;\n    this.stopped = false;\n    this.observableQuery = null;\n    var cache = this.cache = queryManager.cache;\n    // Track how often cache.evict is called, since we want eviction to\n    // override the feud-stopping logic in the markResult method, by\n    // causing shouldWrite to return true. Wrapping the cache.evict method\n    // is a bit of a hack, but it saves us from having to make eviction\n    // counting an official part of the ApolloCache API.\n    if (!destructiveMethodCounts.has(cache)) {\n      destructiveMethodCounts.set(cache, 0);\n      wrapDestructiveCacheMethod(cache, \"evict\");\n      wrapDestructiveCacheMethod(cache, \"modify\");\n      wrapDestructiveCacheMethod(cache, \"reset\");\n    }\n  }\n  QueryInfo.prototype.init = function (query) {\n    var networkStatus = query.networkStatus || NetworkStatus.loading;\n    if (this.variables && this.networkStatus !== NetworkStatus.loading && !equal(this.variables, query.variables)) {\n      networkStatus = NetworkStatus.setVariables;\n    }\n    if (!equal(query.variables, this.variables)) {\n      this.lastDiff = void 0;\n      // Ensure we don't continue to receive cache updates for old variables\n      this.cancel();\n    }\n    Object.assign(this, {\n      document: query.document,\n      variables: query.variables,\n      networkError: null,\n      graphQLErrors: this.graphQLErrors || [],\n      networkStatus: networkStatus\n    });\n    if (query.observableQuery) {\n      this.setObservableQuery(query.observableQuery);\n    }\n    if (query.lastRequestId) {\n      this.lastRequestId = query.lastRequestId;\n    }\n    return this;\n  };\n  QueryInfo.prototype.resetDiff = function () {\n    this.lastDiff = void 0;\n  };\n  QueryInfo.prototype.getDiff = function () {\n    var options = this.getDiffOptions();\n    if (this.lastDiff && equal(options, this.lastDiff.options)) {\n      return this.lastDiff.diff;\n    }\n    this.updateWatch(this.variables);\n    var oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return {\n        complete: false\n      };\n    }\n    var diff = this.cache.diff(options);\n    this.updateLastDiff(diff, options);\n    return diff;\n  };\n  QueryInfo.prototype.updateLastDiff = function (diff, options) {\n    this.lastDiff = diff ? {\n      diff: diff,\n      options: options || this.getDiffOptions()\n    } : void 0;\n  };\n  QueryInfo.prototype.getDiffOptions = function (variables) {\n    var _a;\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    return {\n      query: this.document,\n      variables: variables,\n      returnPartialData: true,\n      optimistic: true,\n      canonizeResults: (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.options.canonizeResults\n    };\n  };\n  QueryInfo.prototype.setDiff = function (diff) {\n    var _a, _b;\n    var oldDiff = this.lastDiff && this.lastDiff.diff;\n    // If we are trying to deliver an incomplete cache result, we avoid\n    // reporting it if the query has errored, otherwise we let the broadcast try\n    // and repair the partial result by refetching the query. This check avoids\n    // a situation where a query that errors and another succeeds with\n    // overlapping data does not report the partial data result to the errored\n    // query.\n    //\n    // See https://github.com/apollographql/apollo-client/issues/11400 for more\n    // information on this issue.\n    if (diff && !diff.complete && ((_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.getLastError())) {\n      return;\n    }\n    this.updateLastDiff(diff);\n    if (!equal(oldDiff && oldDiff.result, diff && diff.result)) {\n      (_b = this.observableQuery) === null || _b === void 0 ? void 0 : _b[\"scheduleNotify\"]();\n    }\n  };\n  QueryInfo.prototype.setObservableQuery = function (oq) {\n    if (oq === this.observableQuery) return;\n    this.observableQuery = oq;\n    if (oq) {\n      oq[\"queryInfo\"] = this;\n    }\n  };\n  QueryInfo.prototype.stop = function () {\n    var _a;\n    if (!this.stopped) {\n      this.stopped = true;\n      // Cancel the pending notify timeout\n      (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n      this.cancel();\n      var oq = this.observableQuery;\n      if (oq) oq.stopPolling();\n    }\n  };\n  QueryInfo.prototype.cancel = function () {\n    var _a;\n    (_a = this.cancelWatch) === null || _a === void 0 ? void 0 : _a.call(this);\n    this.cancelWatch = void 0;\n  };\n  QueryInfo.prototype.updateWatch = function (variables) {\n    var _this = this;\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    var oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return;\n    }\n    var watchOptions = __assign(__assign({}, this.getDiffOptions(variables)), {\n      watcher: this,\n      callback: function (diff) {\n        return _this.setDiff(diff);\n      }\n    });\n    if (!this.lastWatch || !equal(watchOptions, this.lastWatch)) {\n      this.cancel();\n      this.cancelWatch = this.cache.watch(this.lastWatch = watchOptions);\n    }\n  };\n  QueryInfo.prototype.resetLastWrite = function () {\n    this.lastWrite = void 0;\n  };\n  QueryInfo.prototype.shouldWrite = function (result, variables) {\n    var lastWrite = this.lastWrite;\n    return !(lastWrite &&\n    // If cache.evict has been called since the last time we wrote this\n    // data into the cache, there's a chance writing this result into\n    // the cache will repair what was evicted.\n    lastWrite.dmCount === destructiveMethodCounts.get(this.cache) && equal(variables, lastWrite.variables) && equal(result.data, lastWrite.result.data));\n  };\n  QueryInfo.prototype.markResult = function (result, document, options, cacheWriteBehavior) {\n    var _this = this;\n    var _a;\n    var merger = new DeepMerger();\n    var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n    // Cancel the pending notify timeout (if it exists) to prevent extraneous network\n    // requests. To allow future notify timeouts, diff and dirty are reset as well.\n    (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n    if (\"incremental\" in result && isNonEmptyArray(result.incremental)) {\n      var mergedData = mergeIncrementalData(this.getDiff().result, result);\n      result.data = mergedData;\n      // Detect the first chunk of a deferred query and merge it with existing\n      // cache data. This ensures a `cache-first` fetch policy that returns\n      // partial cache data or a `cache-and-network` fetch policy that already\n      // has full data in the cache does not complain when trying to merge the\n      // initial deferred server data with existing cache data.\n    } else if (\"hasNext\" in result && result.hasNext) {\n      var diff = this.getDiff();\n      result.data = merger.merge(diff.result, result.data);\n    }\n    this.graphQLErrors = graphQLErrors;\n    if (options.fetchPolicy === \"no-cache\") {\n      this.updateLastDiff({\n        result: result.data,\n        complete: true\n      }, this.getDiffOptions(options.variables));\n    } else if (cacheWriteBehavior !== 0 /* CacheWriteBehavior.FORBID */) {\n      if (shouldWriteResult(result, options.errorPolicy)) {\n        // Using a transaction here so we have a chance to read the result\n        // back from the cache before the watch callback fires as a result\n        // of writeQuery, so we can store the new diff quietly and ignore\n        // it when we receive it redundantly from the watch callback.\n        this.cache.performTransaction(function (cache) {\n          if (_this.shouldWrite(result, options.variables)) {\n            cache.writeQuery({\n              query: document,\n              data: result.data,\n              variables: options.variables,\n              overwrite: cacheWriteBehavior === 1 /* CacheWriteBehavior.OVERWRITE */\n            });\n\n            _this.lastWrite = {\n              result: result,\n              variables: options.variables,\n              dmCount: destructiveMethodCounts.get(_this.cache)\n            };\n          } else {\n            // If result is the same as the last result we received from\n            // the network (and the variables match too), avoid writing\n            // result into the cache again. The wisdom of skipping this\n            // cache write is far from obvious, since any cache write\n            // could be the one that puts the cache back into a desired\n            // state, fixing corruption or missing data. However, if we\n            // always write every network result into the cache, we enable\n            // feuds between queries competing to update the same data in\n            // incompatible ways, which can lead to an endless cycle of\n            // cache broadcasts and useless network requests. As with any\n            // feud, eventually one side must step back from the brink,\n            // letting the other side(s) have the last word(s). There may\n            // be other points where we could break this cycle, such as\n            // silencing the broadcast for cache.writeQuery (not a good\n            // idea, since it just delays the feud a bit) or somehow\n            // avoiding the network request that just happened (also bad,\n            // because the server could return useful new data). All\n            // options considered, skipping this cache write seems to be\n            // the least damaging place to break the cycle, because it\n            // reflects the intuition that we recently wrote this exact\n            // result into the cache, so the cache *should* already/still\n            // contain this data. If some other query has clobbered that\n            // data in the meantime, that's too bad, but there will be no\n            // winners if every query blindly reverts to its own version\n            // of the data. This approach also gives the network a chance\n            // to return new data, which will be written into the cache as\n            // usual, notifying only those queries that are directly\n            // affected by the cache updates, as usual. In the future, an\n            // even more sophisticated cache could perhaps prevent or\n            // mitigate the clobbering somehow, but that would make this\n            // particular cache write even less important, and thus\n            // skipping it would be even safer than it is today.\n            if (_this.lastDiff && _this.lastDiff.diff.complete) {\n              // Reuse data from the last good (complete) diff that we\n              // received, when possible.\n              result.data = _this.lastDiff.diff.result;\n              return;\n            }\n            // If the previous this.diff was incomplete, fall through to\n            // re-reading the latest data with cache.diff, below.\n          }\n\n          var diffOptions = _this.getDiffOptions(options.variables);\n          var diff = cache.diff(diffOptions);\n          // In case the QueryManager stops this QueryInfo before its\n          // results are delivered, it's important to avoid restarting the\n          // cache watch when markResult is called. We also avoid updating\n          // the watch if we are writing a result that doesn't match the current\n          // variables to avoid race conditions from broadcasting the wrong\n          // result.\n          if (!_this.stopped && equal(_this.variables, options.variables)) {\n            // Any time we're about to update this.diff, we need to make\n            // sure we've started watching the cache.\n            _this.updateWatch(options.variables);\n          }\n          // If we're allowed to write to the cache, and we can read a\n          // complete result from the cache, update result.data to be the\n          // result from the cache, rather than the raw network result.\n          // Set without setDiff to avoid triggering a notify call, since\n          // we have other ways of notifying for this result.\n          _this.updateLastDiff(diff, diffOptions);\n          if (diff.complete) {\n            result.data = diff.result;\n          }\n        });\n      } else {\n        this.lastWrite = void 0;\n      }\n    }\n  };\n  QueryInfo.prototype.markReady = function () {\n    this.networkError = null;\n    return this.networkStatus = NetworkStatus.ready;\n  };\n  QueryInfo.prototype.markError = function (error) {\n    var _a;\n    this.networkStatus = NetworkStatus.error;\n    this.lastWrite = void 0;\n    (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n    if (error.graphQLErrors) {\n      this.graphQLErrors = error.graphQLErrors;\n    }\n    if (error.networkError) {\n      this.networkError = error.networkError;\n    }\n    return error;\n  };\n  return QueryInfo;\n}();\nexport { QueryInfo };\nexport function shouldWriteResult(result, errorPolicy) {\n  if (errorPolicy === void 0) {\n    errorPolicy = \"none\";\n  }\n  var ignoreErrors = errorPolicy === \"ignore\" || errorPolicy === \"all\";\n  var writeWithErrors = !graphQLResultHasError(result);\n  if (!writeWithErrors && ignoreErrors && result.data) {\n    writeWithErrors = true;\n  }\n  return writeWithErrors;\n}", "map": {"version": 3, "names": ["__assign", "equal", "DeepMerger", "mergeIncrementalData", "isNonEmptyArray", "graphQLResultHasError", "canUseWeakMap", "NetworkStatus", "destructiveMethodCounts", "WeakMap", "Map", "wrapDestructiveCacheMethod", "cache", "methodName", "original", "set", "get", "apply", "arguments", "QueryInfo", "query<PERSON>anager", "queryId", "generateQueryId", "document", "lastRequestId", "stopped", "observableQuery", "has", "prototype", "init", "query", "networkStatus", "loading", "variables", "setVariables", "lastDiff", "cancel", "Object", "assign", "networkError", "graphQLErrors", "setObservableQuery", "resetDiff", "getDiff", "options", "getDiffOptions", "diff", "updateWatch", "oq", "fetchPolicy", "complete", "updateLastDiff", "_a", "returnPartialData", "optimistic", "canon<PERSON><PERSON><PERSON><PERSON><PERSON>", "setDiff", "_b", "oldDiff", "getLastError", "result", "stop", "stopPolling", "cancelWatch", "call", "_this", "watchOptions", "watcher", "callback", "lastWatch", "watch", "resetLastWrite", "lastWrite", "shouldWrite", "dmCount", "data", "<PERSON><PERSON><PERSON><PERSON>", "cacheWriteBehavior", "merger", "errors", "slice", "incremental", "mergedData", "hasNext", "merge", "shouldWriteResult", "errorPolicy", "performTransaction", "writeQuery", "overwrite", "diffOptions", "<PERSON><PERSON><PERSON><PERSON>", "ready", "<PERSON><PERSON><PERSON><PERSON>", "error", "ignoreErrors", "writeWithErrors"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/QueryInfo.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { equal } from \"@wry/equality\";\nimport { DeepMerger } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/index.js\";\nimport { isNonEmptyArray, graphQLResultHasError, canUseWeakMap, } from \"../utilities/index.js\";\nimport { NetworkStatus } from \"./networkStatus.js\";\nvar destructiveMethodCounts = new (canUseWeakMap ? WeakMap : Map)();\nfunction wrapDestructiveCacheMethod(cache, methodName) {\n    var original = cache[methodName];\n    if (typeof original === \"function\") {\n        // @ts-expect-error this is just too generic to be typed correctly\n        cache[methodName] = function () {\n            destructiveMethodCounts.set(cache, \n            // The %1e15 allows the count to wrap around to 0 safely every\n            // quadrillion evictions, so there's no risk of overflow. To be\n            // clear, this is more of a pedantic principle than something\n            // that matters in any conceivable practical scenario.\n            (destructiveMethodCounts.get(cache) + 1) % 1e15);\n            // @ts-expect-error this is just too generic to be typed correctly\n            return original.apply(this, arguments);\n        };\n    }\n}\n// A QueryInfo object represents a single query managed by the\n// QueryManager, which tracks all QueryInfo objects by queryId in its\n// this.queries Map. QueryInfo objects store the latest results and errors\n// for the given query, and are responsible for reporting those results to\n// the corresponding ObservableQuery, via the QueryInfo.notify method.\n// Results are reported asynchronously whenever setDiff marks the\n// QueryInfo object as dirty, though a call to the QueryManager's\n// broadcastQueries method may trigger the notification before it happens\n// automatically. This class used to be a simple interface type without\n// any field privacy or meaningful methods, which is why it still has so\n// many public fields. The effort to lock down and simplify the QueryInfo\n// interface is ongoing, and further improvements are welcome.\nvar QueryInfo = /** @class */ (function () {\n    function QueryInfo(queryManager, queryId) {\n        if (queryId === void 0) { queryId = queryManager.generateQueryId(); }\n        this.queryId = queryId;\n        this.document = null;\n        this.lastRequestId = 1;\n        this.stopped = false;\n        this.observableQuery = null;\n        var cache = (this.cache = queryManager.cache);\n        // Track how often cache.evict is called, since we want eviction to\n        // override the feud-stopping logic in the markResult method, by\n        // causing shouldWrite to return true. Wrapping the cache.evict method\n        // is a bit of a hack, but it saves us from having to make eviction\n        // counting an official part of the ApolloCache API.\n        if (!destructiveMethodCounts.has(cache)) {\n            destructiveMethodCounts.set(cache, 0);\n            wrapDestructiveCacheMethod(cache, \"evict\");\n            wrapDestructiveCacheMethod(cache, \"modify\");\n            wrapDestructiveCacheMethod(cache, \"reset\");\n        }\n    }\n    QueryInfo.prototype.init = function (query) {\n        var networkStatus = query.networkStatus || NetworkStatus.loading;\n        if (this.variables &&\n            this.networkStatus !== NetworkStatus.loading &&\n            !equal(this.variables, query.variables)) {\n            networkStatus = NetworkStatus.setVariables;\n        }\n        if (!equal(query.variables, this.variables)) {\n            this.lastDiff = void 0;\n            // Ensure we don't continue to receive cache updates for old variables\n            this.cancel();\n        }\n        Object.assign(this, {\n            document: query.document,\n            variables: query.variables,\n            networkError: null,\n            graphQLErrors: this.graphQLErrors || [],\n            networkStatus: networkStatus,\n        });\n        if (query.observableQuery) {\n            this.setObservableQuery(query.observableQuery);\n        }\n        if (query.lastRequestId) {\n            this.lastRequestId = query.lastRequestId;\n        }\n        return this;\n    };\n    QueryInfo.prototype.resetDiff = function () {\n        this.lastDiff = void 0;\n    };\n    QueryInfo.prototype.getDiff = function () {\n        var options = this.getDiffOptions();\n        if (this.lastDiff && equal(options, this.lastDiff.options)) {\n            return this.lastDiff.diff;\n        }\n        this.updateWatch(this.variables);\n        var oq = this.observableQuery;\n        if (oq && oq.options.fetchPolicy === \"no-cache\") {\n            return { complete: false };\n        }\n        var diff = this.cache.diff(options);\n        this.updateLastDiff(diff, options);\n        return diff;\n    };\n    QueryInfo.prototype.updateLastDiff = function (diff, options) {\n        this.lastDiff =\n            diff ?\n                {\n                    diff: diff,\n                    options: options || this.getDiffOptions(),\n                }\n                : void 0;\n    };\n    QueryInfo.prototype.getDiffOptions = function (variables) {\n        var _a;\n        if (variables === void 0) { variables = this.variables; }\n        return {\n            query: this.document,\n            variables: variables,\n            returnPartialData: true,\n            optimistic: true,\n            canonizeResults: (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.options.canonizeResults,\n        };\n    };\n    QueryInfo.prototype.setDiff = function (diff) {\n        var _a, _b;\n        var oldDiff = this.lastDiff && this.lastDiff.diff;\n        // If we are trying to deliver an incomplete cache result, we avoid\n        // reporting it if the query has errored, otherwise we let the broadcast try\n        // and repair the partial result by refetching the query. This check avoids\n        // a situation where a query that errors and another succeeds with\n        // overlapping data does not report the partial data result to the errored\n        // query.\n        //\n        // See https://github.com/apollographql/apollo-client/issues/11400 for more\n        // information on this issue.\n        if (diff && !diff.complete && ((_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.getLastError())) {\n            return;\n        }\n        this.updateLastDiff(diff);\n        if (!equal(oldDiff && oldDiff.result, diff && diff.result)) {\n            (_b = this.observableQuery) === null || _b === void 0 ? void 0 : _b[\"scheduleNotify\"]();\n        }\n    };\n    QueryInfo.prototype.setObservableQuery = function (oq) {\n        if (oq === this.observableQuery)\n            return;\n        this.observableQuery = oq;\n        if (oq) {\n            oq[\"queryInfo\"] = this;\n        }\n    };\n    QueryInfo.prototype.stop = function () {\n        var _a;\n        if (!this.stopped) {\n            this.stopped = true;\n            // Cancel the pending notify timeout\n            (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n            this.cancel();\n            var oq = this.observableQuery;\n            if (oq)\n                oq.stopPolling();\n        }\n    };\n    QueryInfo.prototype.cancel = function () {\n        var _a;\n        (_a = this.cancelWatch) === null || _a === void 0 ? void 0 : _a.call(this);\n        this.cancelWatch = void 0;\n    };\n    QueryInfo.prototype.updateWatch = function (variables) {\n        var _this = this;\n        if (variables === void 0) { variables = this.variables; }\n        var oq = this.observableQuery;\n        if (oq && oq.options.fetchPolicy === \"no-cache\") {\n            return;\n        }\n        var watchOptions = __assign(__assign({}, this.getDiffOptions(variables)), { watcher: this, callback: function (diff) { return _this.setDiff(diff); } });\n        if (!this.lastWatch || !equal(watchOptions, this.lastWatch)) {\n            this.cancel();\n            this.cancelWatch = this.cache.watch((this.lastWatch = watchOptions));\n        }\n    };\n    QueryInfo.prototype.resetLastWrite = function () {\n        this.lastWrite = void 0;\n    };\n    QueryInfo.prototype.shouldWrite = function (result, variables) {\n        var lastWrite = this.lastWrite;\n        return !(lastWrite &&\n            // If cache.evict has been called since the last time we wrote this\n            // data into the cache, there's a chance writing this result into\n            // the cache will repair what was evicted.\n            lastWrite.dmCount === destructiveMethodCounts.get(this.cache) &&\n            equal(variables, lastWrite.variables) &&\n            equal(result.data, lastWrite.result.data));\n    };\n    QueryInfo.prototype.markResult = function (result, document, options, cacheWriteBehavior) {\n        var _this = this;\n        var _a;\n        var merger = new DeepMerger();\n        var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n        // Cancel the pending notify timeout (if it exists) to prevent extraneous network\n        // requests. To allow future notify timeouts, diff and dirty are reset as well.\n        (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n        if (\"incremental\" in result && isNonEmptyArray(result.incremental)) {\n            var mergedData = mergeIncrementalData(this.getDiff().result, result);\n            result.data = mergedData;\n            // Detect the first chunk of a deferred query and merge it with existing\n            // cache data. This ensures a `cache-first` fetch policy that returns\n            // partial cache data or a `cache-and-network` fetch policy that already\n            // has full data in the cache does not complain when trying to merge the\n            // initial deferred server data with existing cache data.\n        }\n        else if (\"hasNext\" in result && result.hasNext) {\n            var diff = this.getDiff();\n            result.data = merger.merge(diff.result, result.data);\n        }\n        this.graphQLErrors = graphQLErrors;\n        if (options.fetchPolicy === \"no-cache\") {\n            this.updateLastDiff({ result: result.data, complete: true }, this.getDiffOptions(options.variables));\n        }\n        else if (cacheWriteBehavior !== 0 /* CacheWriteBehavior.FORBID */) {\n            if (shouldWriteResult(result, options.errorPolicy)) {\n                // Using a transaction here so we have a chance to read the result\n                // back from the cache before the watch callback fires as a result\n                // of writeQuery, so we can store the new diff quietly and ignore\n                // it when we receive it redundantly from the watch callback.\n                this.cache.performTransaction(function (cache) {\n                    if (_this.shouldWrite(result, options.variables)) {\n                        cache.writeQuery({\n                            query: document,\n                            data: result.data,\n                            variables: options.variables,\n                            overwrite: cacheWriteBehavior === 1 /* CacheWriteBehavior.OVERWRITE */,\n                        });\n                        _this.lastWrite = {\n                            result: result,\n                            variables: options.variables,\n                            dmCount: destructiveMethodCounts.get(_this.cache),\n                        };\n                    }\n                    else {\n                        // If result is the same as the last result we received from\n                        // the network (and the variables match too), avoid writing\n                        // result into the cache again. The wisdom of skipping this\n                        // cache write is far from obvious, since any cache write\n                        // could be the one that puts the cache back into a desired\n                        // state, fixing corruption or missing data. However, if we\n                        // always write every network result into the cache, we enable\n                        // feuds between queries competing to update the same data in\n                        // incompatible ways, which can lead to an endless cycle of\n                        // cache broadcasts and useless network requests. As with any\n                        // feud, eventually one side must step back from the brink,\n                        // letting the other side(s) have the last word(s). There may\n                        // be other points where we could break this cycle, such as\n                        // silencing the broadcast for cache.writeQuery (not a good\n                        // idea, since it just delays the feud a bit) or somehow\n                        // avoiding the network request that just happened (also bad,\n                        // because the server could return useful new data). All\n                        // options considered, skipping this cache write seems to be\n                        // the least damaging place to break the cycle, because it\n                        // reflects the intuition that we recently wrote this exact\n                        // result into the cache, so the cache *should* already/still\n                        // contain this data. If some other query has clobbered that\n                        // data in the meantime, that's too bad, but there will be no\n                        // winners if every query blindly reverts to its own version\n                        // of the data. This approach also gives the network a chance\n                        // to return new data, which will be written into the cache as\n                        // usual, notifying only those queries that are directly\n                        // affected by the cache updates, as usual. In the future, an\n                        // even more sophisticated cache could perhaps prevent or\n                        // mitigate the clobbering somehow, but that would make this\n                        // particular cache write even less important, and thus\n                        // skipping it would be even safer than it is today.\n                        if (_this.lastDiff && _this.lastDiff.diff.complete) {\n                            // Reuse data from the last good (complete) diff that we\n                            // received, when possible.\n                            result.data = _this.lastDiff.diff.result;\n                            return;\n                        }\n                        // If the previous this.diff was incomplete, fall through to\n                        // re-reading the latest data with cache.diff, below.\n                    }\n                    var diffOptions = _this.getDiffOptions(options.variables);\n                    var diff = cache.diff(diffOptions);\n                    // In case the QueryManager stops this QueryInfo before its\n                    // results are delivered, it's important to avoid restarting the\n                    // cache watch when markResult is called. We also avoid updating\n                    // the watch if we are writing a result that doesn't match the current\n                    // variables to avoid race conditions from broadcasting the wrong\n                    // result.\n                    if (!_this.stopped && equal(_this.variables, options.variables)) {\n                        // Any time we're about to update this.diff, we need to make\n                        // sure we've started watching the cache.\n                        _this.updateWatch(options.variables);\n                    }\n                    // If we're allowed to write to the cache, and we can read a\n                    // complete result from the cache, update result.data to be the\n                    // result from the cache, rather than the raw network result.\n                    // Set without setDiff to avoid triggering a notify call, since\n                    // we have other ways of notifying for this result.\n                    _this.updateLastDiff(diff, diffOptions);\n                    if (diff.complete) {\n                        result.data = diff.result;\n                    }\n                });\n            }\n            else {\n                this.lastWrite = void 0;\n            }\n        }\n    };\n    QueryInfo.prototype.markReady = function () {\n        this.networkError = null;\n        return (this.networkStatus = NetworkStatus.ready);\n    };\n    QueryInfo.prototype.markError = function (error) {\n        var _a;\n        this.networkStatus = NetworkStatus.error;\n        this.lastWrite = void 0;\n        (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n        if (error.graphQLErrors) {\n            this.graphQLErrors = error.graphQLErrors;\n        }\n        if (error.networkError) {\n            this.networkError = error.networkError;\n        }\n        return error;\n    };\n    return QueryInfo;\n}());\nexport { QueryInfo };\nexport function shouldWriteResult(result, errorPolicy) {\n    if (errorPolicy === void 0) { errorPolicy = \"none\"; }\n    var ignoreErrors = errorPolicy === \"ignore\" || errorPolicy === \"all\";\n    var writeWithErrors = !graphQLResultHasError(result);\n    if (!writeWithErrors && ignoreErrors && result.data) {\n        writeWithErrors = true;\n    }\n    return writeWithErrors;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,eAAe,EAAEC,qBAAqB,EAAEC,aAAa,QAAS,uBAAuB;AAC9F,SAASC,aAAa,QAAQ,oBAAoB;AAClD,IAAIC,uBAAuB,GAAG,KAAKF,aAAa,GAAGG,OAAO,GAAGC,GAAG,EAAE,CAAC;AACnE,SAASC,0BAA0BA,CAACC,KAAK,EAAEC,UAAU,EAAE;EACnD,IAAIC,QAAQ,GAAGF,KAAK,CAACC,UAAU,CAAC;EAChC,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;IAChC;IACAF,KAAK,CAACC,UAAU,CAAC,GAAG,YAAY;MAC5BL,uBAAuB,CAACO,GAAG,CAACH,KAAK;MACjC;MACA;MACA;MACA;MACA,CAACJ,uBAAuB,CAACQ,GAAG,CAACJ,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;MAChD;MACA,OAAOE,QAAQ,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC1C,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,YAAY,EAAEC,OAAO,EAAE;IACtC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGD,YAAY,CAACE,eAAe,CAAC,CAAC;IAAE;IACpE,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAId,KAAK,GAAI,IAAI,CAACA,KAAK,GAAGQ,YAAY,CAACR,KAAM;IAC7C;IACA;IACA;IACA;IACA;IACA,IAAI,CAACJ,uBAAuB,CAACmB,GAAG,CAACf,KAAK,CAAC,EAAE;MACrCJ,uBAAuB,CAACO,GAAG,CAACH,KAAK,EAAE,CAAC,CAAC;MACrCD,0BAA0B,CAACC,KAAK,EAAE,OAAO,CAAC;MAC1CD,0BAA0B,CAACC,KAAK,EAAE,QAAQ,CAAC;MAC3CD,0BAA0B,CAACC,KAAK,EAAE,OAAO,CAAC;IAC9C;EACJ;EACAO,SAAS,CAACS,SAAS,CAACC,IAAI,GAAG,UAAUC,KAAK,EAAE;IACxC,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa,IAAIxB,aAAa,CAACyB,OAAO;IAChE,IAAI,IAAI,CAACC,SAAS,IACd,IAAI,CAACF,aAAa,KAAKxB,aAAa,CAACyB,OAAO,IAC5C,CAAC/B,KAAK,CAAC,IAAI,CAACgC,SAAS,EAAEH,KAAK,CAACG,SAAS,CAAC,EAAE;MACzCF,aAAa,GAAGxB,aAAa,CAAC2B,YAAY;IAC9C;IACA,IAAI,CAACjC,KAAK,CAAC6B,KAAK,CAACG,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,EAAE;MACzC,IAAI,CAACE,QAAQ,GAAG,KAAK,CAAC;MACtB;MACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACAC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAE;MAChBf,QAAQ,EAAEO,KAAK,CAACP,QAAQ;MACxBU,SAAS,EAAEH,KAAK,CAACG,SAAS;MAC1BM,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI,CAACA,aAAa,IAAI,EAAE;MACvCT,aAAa,EAAEA;IACnB,CAAC,CAAC;IACF,IAAID,KAAK,CAACJ,eAAe,EAAE;MACvB,IAAI,CAACe,kBAAkB,CAACX,KAAK,CAACJ,eAAe,CAAC;IAClD;IACA,IAAII,KAAK,CAACN,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAGM,KAAK,CAACN,aAAa;IAC5C;IACA,OAAO,IAAI;EACf,CAAC;EACDL,SAAS,CAACS,SAAS,CAACc,SAAS,GAAG,YAAY;IACxC,IAAI,CAACP,QAAQ,GAAG,KAAK,CAAC;EAC1B,CAAC;EACDhB,SAAS,CAACS,SAAS,CAACe,OAAO,GAAG,YAAY;IACtC,IAAIC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,IAAI,CAACV,QAAQ,IAAIlC,KAAK,CAAC2C,OAAO,EAAE,IAAI,CAACT,QAAQ,CAACS,OAAO,CAAC,EAAE;MACxD,OAAO,IAAI,CAACT,QAAQ,CAACW,IAAI;IAC7B;IACA,IAAI,CAACC,WAAW,CAAC,IAAI,CAACd,SAAS,CAAC;IAChC,IAAIe,EAAE,GAAG,IAAI,CAACtB,eAAe;IAC7B,IAAIsB,EAAE,IAAIA,EAAE,CAACJ,OAAO,CAACK,WAAW,KAAK,UAAU,EAAE;MAC7C,OAAO;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC9B;IACA,IAAIJ,IAAI,GAAG,IAAI,CAAClC,KAAK,CAACkC,IAAI,CAACF,OAAO,CAAC;IACnC,IAAI,CAACO,cAAc,CAACL,IAAI,EAAEF,OAAO,CAAC;IAClC,OAAOE,IAAI;EACf,CAAC;EACD3B,SAAS,CAACS,SAAS,CAACuB,cAAc,GAAG,UAAUL,IAAI,EAAEF,OAAO,EAAE;IAC1D,IAAI,CAACT,QAAQ,GACTW,IAAI,GACA;MACIA,IAAI,EAAEA,IAAI;MACVF,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACC,cAAc,CAAC;IAC5C,CAAC,GACC,KAAK,CAAC;EACpB,CAAC;EACD1B,SAAS,CAACS,SAAS,CAACiB,cAAc,GAAG,UAAUZ,SAAS,EAAE;IACtD,IAAImB,EAAE;IACN,IAAInB,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAG,IAAI,CAACA,SAAS;IAAE;IACxD,OAAO;MACHH,KAAK,EAAE,IAAI,CAACP,QAAQ;MACpBU,SAAS,EAAEA,SAAS;MACpBoB,iBAAiB,EAAE,IAAI;MACvBC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAACH,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACR,OAAO,CAACW;IACjG,CAAC;EACL,CAAC;EACDpC,SAAS,CAACS,SAAS,CAAC4B,OAAO,GAAG,UAAUV,IAAI,EAAE;IAC1C,IAAIM,EAAE,EAAEK,EAAE;IACV,IAAIC,OAAO,GAAG,IAAI,CAACvB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACW,IAAI;IACjD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,IAAI,IAAI,CAACA,IAAI,CAACI,QAAQ,KAAK,CAACE,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,YAAY,CAAC,CAAC,CAAC,EAAE;MAChH;IACJ;IACA,IAAI,CAACR,cAAc,CAACL,IAAI,CAAC;IACzB,IAAI,CAAC7C,KAAK,CAACyD,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAEd,IAAI,IAAIA,IAAI,CAACc,MAAM,CAAC,EAAE;MACxD,CAACH,EAAE,GAAG,IAAI,CAAC/B,eAAe,MAAM,IAAI,IAAI+B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3F;EACJ,CAAC;EACDtC,SAAS,CAACS,SAAS,CAACa,kBAAkB,GAAG,UAAUO,EAAE,EAAE;IACnD,IAAIA,EAAE,KAAK,IAAI,CAACtB,eAAe,EAC3B;IACJ,IAAI,CAACA,eAAe,GAAGsB,EAAE;IACzB,IAAIA,EAAE,EAAE;MACJA,EAAE,CAAC,WAAW,CAAC,GAAG,IAAI;IAC1B;EACJ,CAAC;EACD7B,SAAS,CAACS,SAAS,CAACiC,IAAI,GAAG,YAAY;IACnC,IAAIT,EAAE;IACN,IAAI,CAAC,IAAI,CAAC3B,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB;MACA,CAAC2B,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC;MAC3F,IAAI,CAAChB,MAAM,CAAC,CAAC;MACb,IAAIY,EAAE,GAAG,IAAI,CAACtB,eAAe;MAC7B,IAAIsB,EAAE,EACFA,EAAE,CAACc,WAAW,CAAC,CAAC;IACxB;EACJ,CAAC;EACD3C,SAAS,CAACS,SAAS,CAACQ,MAAM,GAAG,YAAY;IACrC,IAAIgB,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACW,WAAW,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,CAAC,IAAI,CAAC;IAC1E,IAAI,CAACD,WAAW,GAAG,KAAK,CAAC;EAC7B,CAAC;EACD5C,SAAS,CAACS,SAAS,CAACmB,WAAW,GAAG,UAAUd,SAAS,EAAE;IACnD,IAAIgC,KAAK,GAAG,IAAI;IAChB,IAAIhC,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAG,IAAI,CAACA,SAAS;IAAE;IACxD,IAAIe,EAAE,GAAG,IAAI,CAACtB,eAAe;IAC7B,IAAIsB,EAAE,IAAIA,EAAE,CAACJ,OAAO,CAACK,WAAW,KAAK,UAAU,EAAE;MAC7C;IACJ;IACA,IAAIiB,YAAY,GAAGlE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6C,cAAc,CAACZ,SAAS,CAAC,CAAC,EAAE;MAAEkC,OAAO,EAAE,IAAI;MAAEC,QAAQ,EAAE,SAAAA,CAAUtB,IAAI,EAAE;QAAE,OAAOmB,KAAK,CAACT,OAAO,CAACV,IAAI,CAAC;MAAE;IAAE,CAAC,CAAC;IACvJ,IAAI,CAAC,IAAI,CAACuB,SAAS,IAAI,CAACpE,KAAK,CAACiE,YAAY,EAAE,IAAI,CAACG,SAAS,CAAC,EAAE;MACzD,IAAI,CAACjC,MAAM,CAAC,CAAC;MACb,IAAI,CAAC2B,WAAW,GAAG,IAAI,CAACnD,KAAK,CAAC0D,KAAK,CAAE,IAAI,CAACD,SAAS,GAAGH,YAAa,CAAC;IACxE;EACJ,CAAC;EACD/C,SAAS,CAACS,SAAS,CAAC2C,cAAc,GAAG,YAAY;IAC7C,IAAI,CAACC,SAAS,GAAG,KAAK,CAAC;EAC3B,CAAC;EACDrD,SAAS,CAACS,SAAS,CAAC6C,WAAW,GAAG,UAAUb,MAAM,EAAE3B,SAAS,EAAE;IAC3D,IAAIuC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,OAAO,EAAEA,SAAS;IACd;IACA;IACA;IACAA,SAAS,CAACE,OAAO,KAAKlE,uBAAuB,CAACQ,GAAG,CAAC,IAAI,CAACJ,KAAK,CAAC,IAC7DX,KAAK,CAACgC,SAAS,EAAEuC,SAAS,CAACvC,SAAS,CAAC,IACrChC,KAAK,CAAC2D,MAAM,CAACe,IAAI,EAAEH,SAAS,CAACZ,MAAM,CAACe,IAAI,CAAC,CAAC;EAClD,CAAC;EACDxD,SAAS,CAACS,SAAS,CAACgD,UAAU,GAAG,UAAUhB,MAAM,EAAErC,QAAQ,EAAEqB,OAAO,EAAEiC,kBAAkB,EAAE;IACtF,IAAIZ,KAAK,GAAG,IAAI;IAChB,IAAIb,EAAE;IACN,IAAI0B,MAAM,GAAG,IAAI5E,UAAU,CAAC,CAAC;IAC7B,IAAIsC,aAAa,GAAGpC,eAAe,CAACwD,MAAM,CAACmB,MAAM,CAAC,GAAGnB,MAAM,CAACmB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;IAChF;IACA;IACA,CAAC5B,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC3F,IAAI,aAAa,IAAIQ,MAAM,IAAIxD,eAAe,CAACwD,MAAM,CAACqB,WAAW,CAAC,EAAE;MAChE,IAAIC,UAAU,GAAG/E,oBAAoB,CAAC,IAAI,CAACwC,OAAO,CAAC,CAAC,CAACiB,MAAM,EAAEA,MAAM,CAAC;MACpEA,MAAM,CAACe,IAAI,GAAGO,UAAU;MACxB;MACA;MACA;MACA;MACA;IACJ,CAAC,MACI,IAAI,SAAS,IAAItB,MAAM,IAAIA,MAAM,CAACuB,OAAO,EAAE;MAC5C,IAAIrC,IAAI,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;MACzBiB,MAAM,CAACe,IAAI,GAAGG,MAAM,CAACM,KAAK,CAACtC,IAAI,CAACc,MAAM,EAAEA,MAAM,CAACe,IAAI,CAAC;IACxD;IACA,IAAI,CAACnC,aAAa,GAAGA,aAAa;IAClC,IAAII,OAAO,CAACK,WAAW,KAAK,UAAU,EAAE;MACpC,IAAI,CAACE,cAAc,CAAC;QAAES,MAAM,EAAEA,MAAM,CAACe,IAAI;QAAEzB,QAAQ,EAAE;MAAK,CAAC,EAAE,IAAI,CAACL,cAAc,CAACD,OAAO,CAACX,SAAS,CAAC,CAAC;IACxG,CAAC,MACI,IAAI4C,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MAC/D,IAAIQ,iBAAiB,CAACzB,MAAM,EAAEhB,OAAO,CAAC0C,WAAW,CAAC,EAAE;QAChD;QACA;QACA;QACA;QACA,IAAI,CAAC1E,KAAK,CAAC2E,kBAAkB,CAAC,UAAU3E,KAAK,EAAE;UAC3C,IAAIqD,KAAK,CAACQ,WAAW,CAACb,MAAM,EAAEhB,OAAO,CAACX,SAAS,CAAC,EAAE;YAC9CrB,KAAK,CAAC4E,UAAU,CAAC;cACb1D,KAAK,EAAEP,QAAQ;cACfoD,IAAI,EAAEf,MAAM,CAACe,IAAI;cACjB1C,SAAS,EAAEW,OAAO,CAACX,SAAS;cAC5BwD,SAAS,EAAEZ,kBAAkB,KAAK,CAAC,CAAC;YACxC,CAAC,CAAC;;YACFZ,KAAK,CAACO,SAAS,GAAG;cACdZ,MAAM,EAAEA,MAAM;cACd3B,SAAS,EAAEW,OAAO,CAACX,SAAS;cAC5ByC,OAAO,EAAElE,uBAAuB,CAACQ,GAAG,CAACiD,KAAK,CAACrD,KAAK;YACpD,CAAC;UACL,CAAC,MACI;YACD;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIqD,KAAK,CAAC9B,QAAQ,IAAI8B,KAAK,CAAC9B,QAAQ,CAACW,IAAI,CAACI,QAAQ,EAAE;cAChD;cACA;cACAU,MAAM,CAACe,IAAI,GAAGV,KAAK,CAAC9B,QAAQ,CAACW,IAAI,CAACc,MAAM;cACxC;YACJ;YACA;YACA;UACJ;;UACA,IAAI8B,WAAW,GAAGzB,KAAK,CAACpB,cAAc,CAACD,OAAO,CAACX,SAAS,CAAC;UACzD,IAAIa,IAAI,GAAGlC,KAAK,CAACkC,IAAI,CAAC4C,WAAW,CAAC;UAClC;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACzB,KAAK,CAACxC,OAAO,IAAIxB,KAAK,CAACgE,KAAK,CAAChC,SAAS,EAAEW,OAAO,CAACX,SAAS,CAAC,EAAE;YAC7D;YACA;YACAgC,KAAK,CAAClB,WAAW,CAACH,OAAO,CAACX,SAAS,CAAC;UACxC;UACA;UACA;UACA;UACA;UACA;UACAgC,KAAK,CAACd,cAAc,CAACL,IAAI,EAAE4C,WAAW,CAAC;UACvC,IAAI5C,IAAI,CAACI,QAAQ,EAAE;YACfU,MAAM,CAACe,IAAI,GAAG7B,IAAI,CAACc,MAAM;UAC7B;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACY,SAAS,GAAG,KAAK,CAAC;MAC3B;IACJ;EACJ,CAAC;EACDrD,SAAS,CAACS,SAAS,CAAC+D,SAAS,GAAG,YAAY;IACxC,IAAI,CAACpD,YAAY,GAAG,IAAI;IACxB,OAAQ,IAAI,CAACR,aAAa,GAAGxB,aAAa,CAACqF,KAAK;EACpD,CAAC;EACDzE,SAAS,CAACS,SAAS,CAACiE,SAAS,GAAG,UAAUC,KAAK,EAAE;IAC7C,IAAI1C,EAAE;IACN,IAAI,CAACrB,aAAa,GAAGxB,aAAa,CAACuF,KAAK;IACxC,IAAI,CAACtB,SAAS,GAAG,KAAK,CAAC;IACvB,CAACpB,EAAE,GAAG,IAAI,CAAC1B,eAAe,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC3F,IAAI0C,KAAK,CAACtD,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAGsD,KAAK,CAACtD,aAAa;IAC5C;IACA,IAAIsD,KAAK,CAACvD,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAGuD,KAAK,CAACvD,YAAY;IAC1C;IACA,OAAOuD,KAAK;EAChB,CAAC;EACD,OAAO3E,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,OAAO,SAASkE,iBAAiBA,CAACzB,MAAM,EAAE0B,WAAW,EAAE;EACnD,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAAEA,WAAW,GAAG,MAAM;EAAE;EACpD,IAAIS,YAAY,GAAGT,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,KAAK;EACpE,IAAIU,eAAe,GAAG,CAAC3F,qBAAqB,CAACuD,MAAM,CAAC;EACpD,IAAI,CAACoC,eAAe,IAAID,YAAY,IAAInC,MAAM,CAACe,IAAI,EAAE;IACjDqB,eAAe,GAAG,IAAI;EAC1B;EACA,OAAOA,eAAe;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}