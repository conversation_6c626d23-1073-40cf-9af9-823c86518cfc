{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logger.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"audioPlayer\"];\nfunction VoiceMessagePlayerComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isCurrentUser ? \"from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5\" : \"from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5\");\n  }\n}\nfunction VoiceMessagePlayerComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.isCurrentUser ? \"bg-[#4f5fad] dark:bg-[#6d78c9]\" : \"bg-[#4f5fad] dark:bg-[#6d78c9]\");\n  }\n}\nfunction VoiceMessagePlayerComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9]\": a0,\n    \"bg-[#bdc6cc] dark:bg-[#4a4a4a]\": a1\n  };\n};\nfunction VoiceMessagePlayerComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, VoiceMessagePlayerComponent_div_12_div_1_Template, 1, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r3.getRandomBarHeight(i_r4), \"px\")(\"transition-delay\", i_r4 * 20, \"ms\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, ctx_r3.isPlaying && ctx_r3.progressPercentage > i_r4 * 3.7, !ctx_r3.isPlaying || ctx_r3.progressPercentage <= i_r4 * 3.7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isPlaying && ctx_r3.progressPercentage > i_r4 * 3.7);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10\": a0,\n    \"bg-gradient-to-r from-[#3d4a85]/20 to-[#4f5fad]/20 dark:from-[#6d78c9]/20 dark:to-[#4f5fad]/20\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9] text-white\": a0,\n    \"bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#4f5fad] dark:text-[#6d78c9]\": a1\n  };\n};\nconst _c4 = function (a0, a1) {\n  return {\n    \"opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\": a0,\n    \"opacity-0 group-hover:opacity-50 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30\": a1\n  };\n};\nconst _c5 = function () {\n  return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];\n};\nconst _c6 = function (a0, a1) {\n  return {\n    \"opacity-100\": a0,\n    \"opacity-70\": a1\n  };\n};\nexport class VoiceMessagePlayerComponent {\n  // Ajouter une classe CSS lorsque l'audio est en lecture\n  get playingClass() {\n    return this.isPlaying;\n  }\n  // Ajouter une classe CSS pour les messages de l'utilisateur courant\n  get currentUserClass() {\n    return this.isCurrentUser;\n  }\n  constructor(logger, renderer, cdr) {\n    this.logger = logger;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.audioUrl = '';\n    this.duration = 0;\n    this.isCurrentUser = false;\n    this.isPlaying = false;\n    this.currentTime = 0;\n    // Tableau des indices pour les barres de l'onde sonore\n    this.waveformBars = Array.from({\n      length: 27\n    }, (_, i) => i);\n    // Hauteurs pré-calculées pour les barres de l'onde sonore (style Messenger)\n    this.barHeights = [];\n  }\n  ngOnInit() {\n    // Générer des hauteurs aléatoires pour les barres de l'onde sonore\n    this.generateWaveformPattern();\n    // Appliquer les styles immédiatement\n    this.applyMessengerStyles();\n    // Forcer la détection des changements immédiatement\n    this.cdr.detectChanges();\n  }\n  ngAfterViewInit() {\n    // Forcer la détection des changements après le rendu initial\n    setTimeout(() => {\n      this.applyMessengerStyles();\n      this.cdr.detectChanges();\n    }, 0);\n    // Vérifier si l'audio est chargé\n    if (this.audioPlayerRef?.nativeElement) {\n      this.audioPlayerRef.nativeElement.onloadedmetadata = () => {\n        this.logger.debug('Audio metadata loaded');\n        this.cdr.detectChanges();\n      };\n    }\n  }\n  ngOnDestroy() {\n    this.stopProgressTracking();\n  }\n  /**\n   * Applique les styles Messenger directement aux éléments DOM\n   * pour s'assurer qu'ils sont appliqués immédiatement sans rechargement\n   */\n  applyMessengerStyles() {\n    try {\n      // Appliquer les styles directement via le renderer pour contourner les problèmes de rafraîchissement\n      if (this.audioPlayerRef?.nativeElement) {\n        this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'outline', 'none');\n        this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'border', 'none');\n        this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'box-shadow', 'none');\n      }\n      // Forcer la détection des changements\n      this.cdr.detectChanges();\n    } catch (error) {\n      this.logger.error('Error applying Messenger styles:', error);\n    }\n  }\n  /**\n   * Génère un motif d'onde sonore pseudo-aléatoire mais cohérent\n   * similaire à celui de Messenger\n   */\n  generateWaveformPattern() {\n    // Réinitialiser les hauteurs\n    this.barHeights = [];\n    // Créer un motif avec des hauteurs variables (entre 4 et 20px)\n    // Le motif est pseudo-aléatoire mais suit une courbe naturelle\n    const basePattern = [8, 10, 14, 16, 18, 16, 12, 10, 8, 12, 16, 20, 18, 14, 10, 8, 10, 14, 18, 16, 12, 8, 10, 14, 12, 8, 6];\n    // Ajouter une légère variation aléatoire pour chaque barre\n    for (let i = 0; i < 27; i++) {\n      const baseHeight = basePattern[i] || 10;\n      const variation = Math.floor(Math.random() * 5) - 2; // Variation de -2 à +2\n      this.barHeights.push(Math.max(4, Math.min(20, baseHeight + variation)));\n    }\n  }\n  /**\n   * Retourne la hauteur d'une barre spécifique de l'onde sonore\n   */\n  getRandomBarHeight(index) {\n    return this.barHeights[index] || 10;\n  }\n  /**\n   * Joue ou met en pause l'audio\n   */\n  togglePlay() {\n    const audioPlayer = this.audioPlayerRef.nativeElement;\n    if (this.isPlaying) {\n      audioPlayer.pause();\n      this.isPlaying = false;\n      this.stopProgressTracking();\n      this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\n    } else {\n      // Réinitialiser l'audio si la lecture est terminée\n      if (audioPlayer.ended || audioPlayer.currentTime >= audioPlayer.duration) {\n        audioPlayer.currentTime = 0;\n      }\n      // Jouer l'audio avec gestion d'erreur améliorée\n      audioPlayer.play().then(() => {\n        this.logger.debug('Audio playback started successfully');\n        // Forcer la mise à jour de l'UI\n        this.cdr.detectChanges();\n      }).catch(error => {\n        this.logger.error('Error playing audio:', error);\n        this.isPlaying = false;\n        this.cdr.detectChanges();\n      });\n      this.isPlaying = true;\n      this.startProgressTracking();\n      this.cdr.detectChanges(); // Forcer la mise à jour de l'UI immédiatement\n    }\n  }\n  /**\n   * Démarre le suivi de la progression de la lecture\n   */\n  startProgressTracking() {\n    this.stopProgressTracking();\n    // Utiliser requestAnimationFrame pour une animation plus fluide\n    const updateProgress = () => {\n      if (!this.isPlaying) return;\n      const audioPlayer = this.audioPlayerRef.nativeElement;\n      // Mettre à jour le temps actuel\n      this.currentTime = audioPlayer.currentTime;\n      // Si la lecture est terminée\n      if (audioPlayer.ended) {\n        this.isPlaying = false;\n        this.currentTime = 0;\n        this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\n        return;\n      }\n      // Forcer la détection des changements pour mettre à jour l'UI\n      this.cdr.detectChanges();\n      // Continuer la boucle d'animation\n      this.progressInterval = requestAnimationFrame(updateProgress);\n    };\n    // Démarrer la boucle d'animation\n    this.progressInterval = requestAnimationFrame(updateProgress);\n  }\n  /**\n   * Arrête le suivi de la progression\n   */\n  stopProgressTracking() {\n    if (this.progressInterval) {\n      cancelAnimationFrame(this.progressInterval);\n      this.progressInterval = null;\n    }\n  }\n  /**\n   * Gère l'événement de fin de lecture\n   */\n  onAudioEnded() {\n    this.isPlaying = false;\n    this.currentTime = 0;\n    this.stopProgressTracking();\n    // Forcer la mise à jour de l'UI\n    this.cdr.detectChanges();\n    // Réinitialiser l'audio pour la prochaine lecture\n    if (this.audioPlayerRef?.nativeElement) {\n      this.audioPlayerRef.nativeElement.currentTime = 0;\n    }\n  }\n  /**\n   * Calcule la progression de la lecture en pourcentage\n   */\n  get progressPercentage() {\n    if (!this.audioPlayerRef) return 0;\n    const audioPlayer = this.audioPlayerRef.nativeElement;\n    // Si la durée n'est pas disponible, utiliser la durée fournie en entrée\n    const totalDuration = audioPlayer.duration || this.duration || 1;\n    return this.currentTime / totalDuration * 100;\n  }\n  /**\n   * Formate le temps de lecture en MM:SS\n   */\n  get formattedTime() {\n    const totalSeconds = Math.floor(this.currentTime);\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Formate la durée totale en MM:SS\n   */\n  get formattedDuration() {\n    if (!this.audioPlayerRef || !this.audioPlayerRef.nativeElement.duration) {\n      const totalSeconds = this.duration || 0;\n      const minutes = Math.floor(totalSeconds / 60);\n      const seconds = Math.floor(totalSeconds % 60);\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    const totalSeconds = Math.floor(this.audioPlayerRef.nativeElement.duration);\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }\n  static {\n    this.ɵfac = function VoiceMessagePlayerComponent_Factory(t) {\n      return new (t || VoiceMessagePlayerComponent)(i0.ɵɵdirectiveInject(i1.LoggerService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VoiceMessagePlayerComponent,\n      selectors: [[\"app-voice-message-player\"]],\n      viewQuery: function VoiceMessagePlayerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayerRef = _t.first);\n        }\n      },\n      hostVars: 4,\n      hostBindings: function VoiceMessagePlayerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"is-playing\", ctx.playingClass)(\"is-current-user\", ctx.currentUserClass);\n        }\n      },\n      inputs: {\n        audioUrl: \"audioUrl\",\n        duration: \"duration\",\n        isCurrentUser: \"isCurrentUser\"\n      },\n      decls: 17,\n      vars: 25,\n      consts: [[1, \"flex\", \"items-center\", \"p-2\", \"rounded-lg\", \"relative\", \"overflow-hidden\", 3, \"ngClass\"], [\"preload\", \"metadata\", 2, \"display\", \"none\", 3, \"src\", \"ended\"], [\"audioPlayer\", \"\"], [1, \"absolute\", \"inset-0\", \"pointer-events-none\", \"overflow-hidden\"], [\"class\", \"absolute inset-0 bg-gradient-to-r opacity-30 animate-pulse\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full animate-ping opacity-30\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group\", \"mr-3\", \"z-10\", 3, \"ngClass\", \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"blur-md\", \"transition-opacity\", 3, \"ngClass\"], [1, \"fas\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\", 3, \"ngClass\"], [1, \"flex-1\", \"relative\", \"z-10\"], [1, \"relative\"], [1, \"flex\", \"items-center\", \"h-8\", \"space-x-0.5\"], [\"class\", \"w-1 rounded-full transition-all duration-300\", 3, \"ngClass\", \"height\", \"transition-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"h-0.5\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"transition-all\", \"duration-300\"], [1, \"mt-1\", \"text-xs\", \"text-right\", \"transition-opacity\", \"duration-300\", 3, \"ngClass\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"opacity-30\", \"animate-pulse\", 3, \"ngClass\"], [1, \"absolute\", \"-left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"w-8\", \"h-8\", \"rounded-full\", \"animate-ping\", \"opacity-30\", 3, \"ngClass\"], [1, \"w-1\", \"rounded-full\", \"transition-all\", \"duration-300\", 3, \"ngClass\"], [\"class\", \"absolute inset-0 opacity-70 blur-sm rounded-full -z-10 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"opacity-70\", \"blur-sm\", \"rounded-full\", \"-z-10\", \"bg-[#4f5fad]/50\", \"dark:bg-[#6d78c9]/50\"]],\n      template: function VoiceMessagePlayerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"audio\", 1, 2);\n          i0.ɵɵlistener(\"ended\", function VoiceMessagePlayerComponent_Template_audio_ended_1_listener() {\n            return ctx.onAudioEnded();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵtemplate(4, VoiceMessagePlayerComponent_div_4_Template, 1, 1, \"div\", 4);\n          i0.ɵɵtemplate(5, VoiceMessagePlayerComponent_div_5_Template, 1, 1, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function VoiceMessagePlayerComponent_Template_button_click_6_listener() {\n            return ctx.togglePlay();\n          });\n          i0.ɵɵelement(7, \"div\", 7)(8, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11);\n          i0.ɵɵtemplate(12, VoiceMessagePlayerComponent_div_12_Template, 2, 9, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 14)(15, \"span\", 15);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c2, !ctx.isCurrentUser, ctx.isCurrentUser));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"src\", ctx.audioUrl, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaying);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaying);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c3, ctx.isPlaying, !ctx.isPlaying));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx.isPlaying, !ctx.isPlaying));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", ctx.isPlaying ? \"fa-pause\" : \"fa-play\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(21, _c5));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"width\", ctx.progressPercentage, \"%\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(22, _c6, ctx.isPlaying, !ctx.isPlaying));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.formattedDuration, \" \");\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.voice-message-player[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: transparent;\\n  border-radius: 18px;\\n  padding: 8px 12px;\\n  width: 100%;\\n  max-width: 240px;\\n  border: none;\\n  outline: none;\\n  box-shadow: none;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  color: #4f5fad;\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);\\n}\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]:hover {\\n  background-color: #ffffff;\\n  box-shadow: 0 2px 12px rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #4f5fad;\\n  box-shadow: 0 2px 8px rgba(79, 95, 173, 0.2);\\n}\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  box-shadow: 0 2px 12px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%] {\\n  background-color: #2a2a2a;\\n  color: #6d78c9;\\n  box-shadow: 0 2px 8px rgba(109, 120, 201, 0.2);\\n}\\n\\n.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover, .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover {\\n  background-color: #333333;\\n  box-shadow: 0 2px 12px rgba(109, 120, 201, 0.3);\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  transition: transform 0.3s ease;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .waveform[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 24px;\\n  margin-bottom: 4px;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .waveform[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%] {\\n  width: 2px;\\n  border-radius: 1px;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.4);\\n}\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9);\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.3);\\n}\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.9);\\n}\\n\\n\\n\\n.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(109, 120, 201, 0.3);\\n}\\n\\n.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%] {\\n  background-color: rgba(109, 120, 201, 0.9);\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  font-size: 11px;\\n  margin-top: 2px;\\n  transition: opacity 0.3s ease;\\n}\\n\\n\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: rgba(79, 95, 173, 0.9);\\n}\\n\\n\\n\\n.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%] {\\n  color: rgba(109, 120, 201, 0.9);\\n}\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.7;\\n    filter: brightness(0.9);\\n  }\\n  50% {\\n    opacity: 1;\\n    filter: brightness(1.2);\\n  }\\n  100% {\\n    opacity: 0.7;\\n    filter: brightness(0.9);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-button {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 0 rgba(79, 95, 173, 0.4);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    box-shadow: 0 0 10px 3px rgba(79, 95, 173, 0.3);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 0 rgba(79, 95, 173, 0.4);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-button-current-user {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    box-shadow: 0 0 10px 3px rgba(255, 255, 255, 0.3);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bar-animation {\\n  0% {\\n    transform: scaleY(0.8);\\n  }\\n  50% {\\n    transform: scaleY(1.2);\\n  }\\n  100% {\\n    transform: scaleY(0.8);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_glow {\\n  0% {\\n    filter: drop-shadow(0 0 2px rgba(79, 95, 173, 0.5));\\n  }\\n  50% {\\n    filter: drop-shadow(0 0 5px rgba(79, 95, 173, 0.8));\\n  }\\n  100% {\\n    filter: drop-shadow(0 0 2px rgba(79, 95, 173, 0.5));\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_glow-current-user {\\n  0% {\\n    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));\\n  }\\n  50% {\\n    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));\\n  }\\n  100% {\\n    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fade-in {\\n  from {\\n    opacity: 0.7;\\n    transform: translateY(2px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.voice-message-player.is-playing[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite, _ngcontent-%COMP%_bar-animation 1.2s ease-in-out infinite,\\n    _ngcontent-%COMP%_glow 2s infinite;\\n}\\n\\n.voice-message-player.is-playing.is-current-user[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite, _ngcontent-%COMP%_bar-animation 1.2s ease-in-out infinite,\\n    _ngcontent-%COMP%_glow-current-user 2s infinite;\\n}\\n\\n.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-button 2s infinite;\\n}\\n\\n.voice-message-player.is-current-user[_ngcontent-%COMP%]   .pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse-button-current-user 2s infinite;\\n}\\n\\n.voice-message-player[_ngcontent-%COMP%]   .fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fade-in 0.5s ease-in-out;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%] {\\n  transition: background-color 0.3s ease, transform 0.3s ease, filter 0.3s ease;\\n}\\n\\n\\n\\n.voice-message-player[_ngcontent-%COMP%]:hover   .waveform-bar[_ngcontent-%COMP%] {\\n  transform: scaleY(1.05);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "isCurrentUser", "ctx_r2", "ɵɵelementStart", "ɵɵtemplate", "VoiceMessagePlayerComponent_div_12_div_1_Template", "ɵɵelementEnd", "ɵɵstyleProp", "ctx_r3", "getRandomBarHeight", "i_r4", "ɵɵpureFunction2", "_c1", "isPlaying", "progressPercentage", "ɵɵadvance", "VoiceMessagePlayerComponent", "playingClass", "currentUserClass", "constructor", "logger", "renderer", "cdr", "audioUrl", "duration", "currentTime", "waveformBars", "Array", "from", "length", "_", "i", "barHeights", "ngOnInit", "generateWaveformPattern", "applyMessengerStyles", "detectChanges", "ngAfterViewInit", "setTimeout", "audioPlayerRef", "nativeElement", "onloadedmetadata", "debug", "ngOnDestroy", "stopProgressTracking", "setStyle", "error", "basePattern", "baseHeight", "variation", "Math", "floor", "random", "push", "max", "min", "index", "togglePlay", "audioPlayer", "pause", "ended", "play", "then", "catch", "startProgressTracking", "updateProgress", "progressInterval", "requestAnimationFrame", "cancelAnimationFrame", "onAudioEnded", "totalDuration", "formattedTime", "totalSeconds", "minutes", "seconds", "toString", "padStart", "formattedDuration", "ɵɵdirectiveInject", "i1", "LoggerService", "Renderer2", "ChangeDetectorRef", "selectors", "viewQuery", "VoiceMessagePlayerComponent_Query", "rf", "ctx", "ɵɵlistener", "VoiceMessagePlayerComponent_Template_audio_ended_1_listener", "VoiceMessagePlayerComponent_div_4_Template", "VoiceMessagePlayerComponent_div_5_Template", "VoiceMessagePlayerComponent_Template_button_click_6_listener", "VoiceMessagePlayerComponent_div_12_Template", "ɵɵtext", "_c2", "ɵɵsanitizeUrl", "_c3", "_c4", "ɵɵpureFunction0", "_c5", "_c6", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\voice-message-player\\voice-message-player.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\voice-message-player\\voice-message-player.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  OnDestroy,\r\n  Input,\r\n  ElementRef,\r\n  ViewChild,\r\n  HostBinding,\r\n  Renderer2,\r\n  ChangeDetectorRef,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { LoggerService } from '../../services/logger.service';\r\n\r\n@Component({\r\n  selector: 'app-voice-message-player',\r\n  templateUrl: './voice-message-player.component.html',\r\n  styleUrls: ['./voice-message-player.component.css'],\r\n})\r\nexport class VoiceMessagePlayerComponent\r\n  implements OnInit, OnDestroy, AfterViewInit\r\n{\r\n  @Input() audioUrl: string = '';\r\n  @Input() duration: number = 0;\r\n  @Input() isCurrentUser: boolean = false;\r\n  @ViewChild('audioPlayer') audioPlayerRef!: ElementRef<HTMLAudioElement>;\r\n\r\n  // Ajouter une classe CSS lorsque l'audio est en lecture\r\n  @HostBinding('class.is-playing') get playingClass() {\r\n    return this.isPlaying;\r\n  }\r\n\r\n  // Ajouter une classe CSS pour les messages de l'utilisateur courant\r\n  @HostBinding('class.is-current-user') get currentUserClass() {\r\n    return this.isCurrentUser;\r\n  }\r\n\r\n  isPlaying = false;\r\n  currentTime = 0;\r\n  progressInterval: any;\r\n\r\n  // Tableau des indices pour les barres de l'onde sonore\r\n  waveformBars: number[] = Array.from({ length: 27 }, (_, i) => i);\r\n\r\n  // Hauteurs pré-calculées pour les barres de l'onde sonore (style Messenger)\r\n  private barHeights: number[] = [];\r\n\r\n  constructor(\r\n    private logger: LoggerService,\r\n    private renderer: Renderer2,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Générer des hauteurs aléatoires pour les barres de l'onde sonore\r\n    this.generateWaveformPattern();\r\n\r\n    // Appliquer les styles immédiatement\r\n    this.applyMessengerStyles();\r\n\r\n    // Forcer la détection des changements immédiatement\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Forcer la détection des changements après le rendu initial\r\n    setTimeout(() => {\r\n      this.applyMessengerStyles();\r\n      this.cdr.detectChanges();\r\n    }, 0);\r\n\r\n    // Vérifier si l'audio est chargé\r\n    if (this.audioPlayerRef?.nativeElement) {\r\n      this.audioPlayerRef.nativeElement.onloadedmetadata = () => {\r\n        this.logger.debug('Audio metadata loaded');\r\n        this.cdr.detectChanges();\r\n      };\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.stopProgressTracking();\r\n  }\r\n\r\n  /**\r\n   * Applique les styles Messenger directement aux éléments DOM\r\n   * pour s'assurer qu'ils sont appliqués immédiatement sans rechargement\r\n   */\r\n  private applyMessengerStyles(): void {\r\n    try {\r\n      // Appliquer les styles directement via le renderer pour contourner les problèmes de rafraîchissement\r\n      if (this.audioPlayerRef?.nativeElement) {\r\n        this.renderer.setStyle(\r\n          this.audioPlayerRef.nativeElement,\r\n          'outline',\r\n          'none'\r\n        );\r\n        this.renderer.setStyle(\r\n          this.audioPlayerRef.nativeElement,\r\n          'border',\r\n          'none'\r\n        );\r\n        this.renderer.setStyle(\r\n          this.audioPlayerRef.nativeElement,\r\n          'box-shadow',\r\n          'none'\r\n        );\r\n      }\r\n\r\n      // Forcer la détection des changements\r\n      this.cdr.detectChanges();\r\n    } catch (error) {\r\n      this.logger.error('Error applying Messenger styles:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Génère un motif d'onde sonore pseudo-aléatoire mais cohérent\r\n   * similaire à celui de Messenger\r\n   */\r\n  private generateWaveformPattern(): void {\r\n    // Réinitialiser les hauteurs\r\n    this.barHeights = [];\r\n\r\n    // Créer un motif avec des hauteurs variables (entre 4 et 20px)\r\n    // Le motif est pseudo-aléatoire mais suit une courbe naturelle\r\n    const basePattern = [\r\n      8, 10, 14, 16, 18, 16, 12, 10, 8, 12, 16, 20, 18, 14, 10, 8, 10, 14, 18,\r\n      16, 12, 8, 10, 14, 12, 8, 6,\r\n    ];\r\n\r\n    // Ajouter une légère variation aléatoire pour chaque barre\r\n    for (let i = 0; i < 27; i++) {\r\n      const baseHeight = basePattern[i] || 10;\r\n      const variation = Math.floor(Math.random() * 5) - 2; // Variation de -2 à +2\r\n      this.barHeights.push(Math.max(4, Math.min(20, baseHeight + variation)));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retourne la hauteur d'une barre spécifique de l'onde sonore\r\n   */\r\n  getRandomBarHeight(index: number): number {\r\n    return this.barHeights[index] || 10;\r\n  }\r\n\r\n  /**\r\n   * Joue ou met en pause l'audio\r\n   */\r\n  togglePlay(): void {\r\n    const audioPlayer = this.audioPlayerRef.nativeElement;\r\n\r\n    if (this.isPlaying) {\r\n      audioPlayer.pause();\r\n      this.isPlaying = false;\r\n      this.stopProgressTracking();\r\n      this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\r\n    } else {\r\n      // Réinitialiser l'audio si la lecture est terminée\r\n      if (\r\n        audioPlayer.ended ||\r\n        audioPlayer.currentTime >= audioPlayer.duration\r\n      ) {\r\n        audioPlayer.currentTime = 0;\r\n      }\r\n\r\n      // Jouer l'audio avec gestion d'erreur améliorée\r\n      audioPlayer\r\n        .play()\r\n        .then(() => {\r\n          this.logger.debug('Audio playback started successfully');\r\n          // Forcer la mise à jour de l'UI\r\n          this.cdr.detectChanges();\r\n        })\r\n        .catch((error) => {\r\n          this.logger.error('Error playing audio:', error);\r\n          this.isPlaying = false;\r\n          this.cdr.detectChanges();\r\n        });\r\n\r\n      this.isPlaying = true;\r\n      this.startProgressTracking();\r\n      this.cdr.detectChanges(); // Forcer la mise à jour de l'UI immédiatement\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Démarre le suivi de la progression de la lecture\r\n   */\r\n  private startProgressTracking(): void {\r\n    this.stopProgressTracking();\r\n\r\n    // Utiliser requestAnimationFrame pour une animation plus fluide\r\n    const updateProgress = () => {\r\n      if (!this.isPlaying) return;\r\n\r\n      const audioPlayer = this.audioPlayerRef.nativeElement;\r\n\r\n      // Mettre à jour le temps actuel\r\n      this.currentTime = audioPlayer.currentTime;\r\n\r\n      // Si la lecture est terminée\r\n      if (audioPlayer.ended) {\r\n        this.isPlaying = false;\r\n        this.currentTime = 0;\r\n        this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\r\n        return;\r\n      }\r\n\r\n      // Forcer la détection des changements pour mettre à jour l'UI\r\n      this.cdr.detectChanges();\r\n\r\n      // Continuer la boucle d'animation\r\n      this.progressInterval = requestAnimationFrame(updateProgress);\r\n    };\r\n\r\n    // Démarrer la boucle d'animation\r\n    this.progressInterval = requestAnimationFrame(updateProgress);\r\n  }\r\n\r\n  /**\r\n   * Arrête le suivi de la progression\r\n   */\r\n  private stopProgressTracking(): void {\r\n    if (this.progressInterval) {\r\n      cancelAnimationFrame(this.progressInterval);\r\n      this.progressInterval = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gère l'événement de fin de lecture\r\n   */\r\n  onAudioEnded(): void {\r\n    this.isPlaying = false;\r\n    this.currentTime = 0;\r\n    this.stopProgressTracking();\r\n\r\n    // Forcer la mise à jour de l'UI\r\n    this.cdr.detectChanges();\r\n\r\n    // Réinitialiser l'audio pour la prochaine lecture\r\n    if (this.audioPlayerRef?.nativeElement) {\r\n      this.audioPlayerRef.nativeElement.currentTime = 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calcule la progression de la lecture en pourcentage\r\n   */\r\n  get progressPercentage(): number {\r\n    if (!this.audioPlayerRef) return 0;\r\n    const audioPlayer = this.audioPlayerRef.nativeElement;\r\n\r\n    // Si la durée n'est pas disponible, utiliser la durée fournie en entrée\r\n    const totalDuration = audioPlayer.duration || this.duration || 1;\r\n    return (this.currentTime / totalDuration) * 100;\r\n  }\r\n\r\n  /**\r\n   * Formate le temps de lecture en MM:SS\r\n   */\r\n  get formattedTime(): string {\r\n    const totalSeconds = Math.floor(this.currentTime);\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds\r\n      .toString()\r\n      .padStart(2, '0')}`;\r\n  }\r\n\r\n  /**\r\n   * Formate la durée totale en MM:SS\r\n   */\r\n  get formattedDuration(): string {\r\n    if (!this.audioPlayerRef || !this.audioPlayerRef.nativeElement.duration) {\r\n      const totalSeconds = this.duration || 0;\r\n      const minutes = Math.floor(totalSeconds / 60);\r\n      const seconds = Math.floor(totalSeconds % 60);\r\n      return `${minutes.toString().padStart(2, '0')}:${seconds\r\n        .toString()\r\n        .padStart(2, '0')}`;\r\n    }\r\n\r\n    const totalSeconds = Math.floor(this.audioPlayerRef.nativeElement.duration);\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds\r\n      .toString()\r\n      .padStart(2, '0')}`;\r\n  }\r\n}\r\n", "<div\r\n  class=\"flex items-center p-2 rounded-lg relative overflow-hidden\"\r\n  [ngClass]=\"{\r\n    'bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10':\r\n      !isCurrentUser,\r\n    'bg-gradient-to-r from-[#3d4a85]/20 to-[#4f5fad]/20 dark:from-[#6d78c9]/20 dark:to-[#4f5fad]/20':\r\n      isCurrentUser\r\n  }\"\r\n>\r\n  <!-- Élément audio caché -->\r\n  <audio\r\n    #audioPlayer\r\n    [src]=\"audioUrl\"\r\n    (ended)=\"onAudioEnded()\"\r\n    preload=\"metadata\"\r\n    style=\"display: none\"\r\n  ></audio>\r\n\r\n  <!-- Decorative background elements -->\r\n  <div class=\"absolute inset-0 pointer-events-none overflow-hidden\">\r\n    <div\r\n      *ngIf=\"isPlaying\"\r\n      class=\"absolute inset-0 bg-gradient-to-r opacity-30 animate-pulse\"\r\n      [ngClass]=\"\r\n        isCurrentUser\r\n          ? 'from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5'\r\n          : 'from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5'\r\n      \"\r\n    ></div>\r\n\r\n    <!-- Animated pulse ring when playing -->\r\n    <div\r\n      *ngIf=\"isPlaying\"\r\n      class=\"absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full animate-ping opacity-30\"\r\n      [ngClass]=\"\r\n        isCurrentUser\r\n          ? 'bg-[#4f5fad] dark:bg-[#6d78c9]'\r\n          : 'bg-[#4f5fad] dark:bg-[#6d78c9]'\r\n      \"\r\n    ></div>\r\n  </div>\r\n\r\n  <!-- Bouton de lecture/pause avec effet de pulsation -->\r\n  <button\r\n    class=\"w-10 h-10 rounded-full flex items-center justify-center relative group mr-3 z-10\"\r\n    (click)=\"togglePlay()\"\r\n    [ngClass]=\"{\r\n      'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': isPlaying,\r\n      'bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#4f5fad] dark:text-[#6d78c9]':\r\n        !isPlaying\r\n    }\"\r\n  >\r\n    <!-- Glow effect -->\r\n    <div\r\n      class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\r\n      [ngClass]=\"{\r\n        'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': isPlaying,\r\n        'opacity-0 group-hover:opacity-50 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\r\n          !isPlaying\r\n      }\"\r\n    ></div>\r\n\r\n    <i\r\n      class=\"fas relative z-10 group-hover:scale-110 transition-transform\"\r\n      [ngClass]=\"isPlaying ? 'fa-pause' : 'fa-play'\"\r\n    ></i>\r\n  </button>\r\n\r\n  <div class=\"flex-1 relative z-10\">\r\n    <!-- Visualisation de l'onde sonore -->\r\n    <div class=\"relative\">\r\n      <!-- Génération de barres pour l'onde sonore -->\r\n      <div class=\"flex items-center h-8 space-x-0.5\">\r\n        <div\r\n          *ngFor=\"\r\n            let i of [\r\n              0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,\r\n              19, 20, 21, 22, 23, 24, 25, 26\r\n            ]\r\n          \"\r\n          class=\"w-1 rounded-full transition-all duration-300\"\r\n          [ngClass]=\"{\r\n            'bg-[#4f5fad] dark:bg-[#6d78c9]':\r\n              isPlaying && progressPercentage > i * 3.7,\r\n            'bg-[#bdc6cc] dark:bg-[#4a4a4a]':\r\n              !isPlaying || progressPercentage <= i * 3.7\r\n          }\"\r\n          [style.height.px]=\"getRandomBarHeight(i)\"\r\n          [style.transition-delay.ms]=\"i * 20\"\r\n        >\r\n          <!-- Glow effect for active bars -->\r\n          <div\r\n            *ngIf=\"isPlaying && progressPercentage > i * 3.7\"\r\n            class=\"absolute inset-0 opacity-70 blur-sm rounded-full -z-10 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Progress indicator -->\r\n      <div\r\n        class=\"absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] transition-all duration-300\"\r\n        [style.width.%]=\"progressPercentage\"\r\n      ></div>\r\n    </div>\r\n\r\n    <!-- Affichage du temps avec animation de fondu -->\r\n    <div\r\n      class=\"mt-1 text-xs text-right transition-opacity duration-300\"\r\n      [ngClass]=\"{ 'opacity-100': isPlaying, 'opacity-70': !isPlaying }\"\r\n    >\r\n      <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">\r\n        {{ formattedDuration }}\r\n      </span>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;ICoBIA,EAAA,CAAAC,SAAA,cAQO;;;;IALLD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,aAAA,6JAIC;;;;;IAIHJ,EAAA,CAAAC,SAAA,cAQO;;;;IALLD,EAAA,CAAAE,UAAA,YAAAG,MAAA,CAAAD,aAAA,uEAIC;;;;;IAqDGJ,EAAA,CAAAC,SAAA,cAGO;;;;;;;;;;;IArBTD,EAAA,CAAAM,cAAA,cAgBC;IAECN,EAAA,CAAAO,UAAA,IAAAC,iDAAA,kBAGO;IACTR,EAAA,CAAAS,YAAA,EAAM;;;;;IARJT,EAAA,CAAAU,WAAA,WAAAC,MAAA,CAAAC,kBAAA,CAAAC,IAAA,QAAyC,qBAAAA,IAAA;IANzCb,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAK,SAAA,IAAAL,MAAA,CAAAM,kBAAA,GAAAJ,IAAA,SAAAF,MAAA,CAAAK,SAAA,IAAAL,MAAA,CAAAM,kBAAA,IAAAJ,IAAA,QAKE;IAMCb,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAAE,UAAA,SAAAS,MAAA,CAAAK,SAAA,IAAAL,MAAA,CAAAM,kBAAA,GAAAJ,IAAA,OAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADzE5D,OAAM,MAAOM,2BAA2B;EAQtC;EACA,IAAqCC,YAAYA,CAAA;IAC/C,OAAO,IAAI,CAACJ,SAAS;EACvB;EAEA;EACA,IAA0CK,gBAAgBA,CAAA;IACxD,OAAO,IAAI,CAACjB,aAAa;EAC3B;EAYAkB,YACUC,MAAqB,EACrBC,QAAmB,EACnBC,GAAsB;IAFtB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,GAAG,GAAHA,GAAG;IA5BJ,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAvB,aAAa,GAAY,KAAK;IAavC,KAAAY,SAAS,GAAG,KAAK;IACjB,KAAAY,WAAW,GAAG,CAAC;IAGf;IACA,KAAAC,YAAY,GAAaC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAEhE;IACQ,KAAAC,UAAU,GAAa,EAAE;EAM9B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACC,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACb,GAAG,CAACc,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACH,oBAAoB,EAAE;MAC3B,IAAI,CAACb,GAAG,CAACc,aAAa,EAAE;IAC1B,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI,IAAI,CAACG,cAAc,EAAEC,aAAa,EAAE;MACtC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACC,gBAAgB,GAAG,MAAK;QACxD,IAAI,CAACrB,MAAM,CAACsB,KAAK,CAAC,uBAAuB,CAAC;QAC1C,IAAI,CAACpB,GAAG,CAACc,aAAa,EAAE;MAC1B,CAAC;;EAEL;EAEAO,WAAWA,CAAA;IACT,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;;EAIQT,oBAAoBA,CAAA;IAC1B,IAAI;MACF;MACA,IAAI,IAAI,CAACI,cAAc,EAAEC,aAAa,EAAE;QACtC,IAAI,CAACnB,QAAQ,CAACwB,QAAQ,CACpB,IAAI,CAACN,cAAc,CAACC,aAAa,EACjC,SAAS,EACT,MAAM,CACP;QACD,IAAI,CAACnB,QAAQ,CAACwB,QAAQ,CACpB,IAAI,CAACN,cAAc,CAACC,aAAa,EACjC,QAAQ,EACR,MAAM,CACP;QACD,IAAI,CAACnB,QAAQ,CAACwB,QAAQ,CACpB,IAAI,CAACN,cAAc,CAACC,aAAa,EACjC,YAAY,EACZ,MAAM,CACP;;MAGH;MACA,IAAI,CAAClB,GAAG,CAACc,aAAa,EAAE;KACzB,CAAC,OAAOU,KAAK,EAAE;MACd,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;EAEhE;EAEA;;;;EAIQZ,uBAAuBA,CAAA;IAC7B;IACA,IAAI,CAACF,UAAU,GAAG,EAAE;IAEpB;IACA;IACA,MAAMe,WAAW,GAAG,CAClB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACvE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAC5B;IAED;IACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMiB,UAAU,GAAGD,WAAW,CAAChB,CAAC,CAAC,IAAI,EAAE;MACvC,MAAMkB,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MACrD,IAAI,CAACpB,UAAU,CAACqB,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEJ,IAAI,CAACK,GAAG,CAAC,EAAE,EAAEP,UAAU,GAAGC,SAAS,CAAC,CAAC,CAAC;;EAE3E;EAEA;;;EAGAxC,kBAAkBA,CAAC+C,KAAa;IAC9B,OAAO,IAAI,CAACxB,UAAU,CAACwB,KAAK,CAAC,IAAI,EAAE;EACrC;EAEA;;;EAGAC,UAAUA,CAAA;IACR,MAAMC,WAAW,GAAG,IAAI,CAACnB,cAAc,CAACC,aAAa;IAErD,IAAI,IAAI,CAAC3B,SAAS,EAAE;MAClB6C,WAAW,CAACC,KAAK,EAAE;MACnB,IAAI,CAAC9C,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC+B,oBAAoB,EAAE;MAC3B,IAAI,CAACtB,GAAG,CAACc,aAAa,EAAE,CAAC,CAAC;KAC3B,MAAM;MACL;MACA,IACEsB,WAAW,CAACE,KAAK,IACjBF,WAAW,CAACjC,WAAW,IAAIiC,WAAW,CAAClC,QAAQ,EAC/C;QACAkC,WAAW,CAACjC,WAAW,GAAG,CAAC;;MAG7B;MACAiC,WAAW,CACRG,IAAI,EAAE,CACNC,IAAI,CAAC,MAAK;QACT,IAAI,CAAC1C,MAAM,CAACsB,KAAK,CAAC,qCAAqC,CAAC;QACxD;QACA,IAAI,CAACpB,GAAG,CAACc,aAAa,EAAE;MAC1B,CAAC,CAAC,CACD2B,KAAK,CAAEjB,KAAK,IAAI;QACf,IAAI,CAAC1B,MAAM,CAAC0B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACjC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,GAAG,CAACc,aAAa,EAAE;MAC1B,CAAC,CAAC;MAEJ,IAAI,CAACvB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACmD,qBAAqB,EAAE;MAC5B,IAAI,CAAC1C,GAAG,CAACc,aAAa,EAAE,CAAC,CAAC;;EAE9B;EAEA;;;EAGQ4B,qBAAqBA,CAAA;IAC3B,IAAI,CAACpB,oBAAoB,EAAE;IAE3B;IACA,MAAMqB,cAAc,GAAGA,CAAA,KAAK;MAC1B,IAAI,CAAC,IAAI,CAACpD,SAAS,EAAE;MAErB,MAAM6C,WAAW,GAAG,IAAI,CAACnB,cAAc,CAACC,aAAa;MAErD;MACA,IAAI,CAACf,WAAW,GAAGiC,WAAW,CAACjC,WAAW;MAE1C;MACA,IAAIiC,WAAW,CAACE,KAAK,EAAE;QACrB,IAAI,CAAC/C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACY,WAAW,GAAG,CAAC;QACpB,IAAI,CAACH,GAAG,CAACc,aAAa,EAAE,CAAC,CAAC;QAC1B;;MAGF;MACA,IAAI,CAACd,GAAG,CAACc,aAAa,EAAE;MAExB;MACA,IAAI,CAAC8B,gBAAgB,GAAGC,qBAAqB,CAACF,cAAc,CAAC;IAC/D,CAAC;IAED;IACA,IAAI,CAACC,gBAAgB,GAAGC,qBAAqB,CAACF,cAAc,CAAC;EAC/D;EAEA;;;EAGQrB,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACsB,gBAAgB,EAAE;MACzBE,oBAAoB,CAAC,IAAI,CAACF,gBAAgB,CAAC;MAC3C,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;;;EAGAG,YAAYA,CAAA;IACV,IAAI,CAACxD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACY,WAAW,GAAG,CAAC;IACpB,IAAI,CAACmB,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACtB,GAAG,CAACc,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACG,cAAc,EAAEC,aAAa,EAAE;MACtC,IAAI,CAACD,cAAc,CAACC,aAAa,CAACf,WAAW,GAAG,CAAC;;EAErD;EAEA;;;EAGA,IAAIX,kBAAkBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACyB,cAAc,EAAE,OAAO,CAAC;IAClC,MAAMmB,WAAW,GAAG,IAAI,CAACnB,cAAc,CAACC,aAAa;IAErD;IACA,MAAM8B,aAAa,GAAGZ,WAAW,CAAClC,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,CAAC;IAChE,OAAQ,IAAI,CAACC,WAAW,GAAG6C,aAAa,GAAI,GAAG;EACjD;EAEA;;;EAGA,IAAIC,aAAaA,CAAA;IACf,MAAMC,YAAY,GAAGtB,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC1B,WAAW,CAAC;IACjD,MAAMgD,OAAO,GAAGvB,IAAI,CAACC,KAAK,CAACqB,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAME,OAAO,GAAGF,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACE,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CACrDC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;EAEA;;;EAGA,IAAIC,iBAAiBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACtC,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACC,aAAa,CAAChB,QAAQ,EAAE;MACvE,MAAMgD,YAAY,GAAG,IAAI,CAAChD,QAAQ,IAAI,CAAC;MACvC,MAAMiD,OAAO,GAAGvB,IAAI,CAACC,KAAK,CAACqB,YAAY,GAAG,EAAE,CAAC;MAC7C,MAAME,OAAO,GAAGxB,IAAI,CAACC,KAAK,CAACqB,YAAY,GAAG,EAAE,CAAC;MAC7C,OAAO,GAAGC,OAAO,CAACE,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CACrDC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAGvB,MAAMJ,YAAY,GAAGtB,IAAI,CAACC,KAAK,CAAC,IAAI,CAACZ,cAAc,CAACC,aAAa,CAAChB,QAAQ,CAAC;IAC3E,MAAMiD,OAAO,GAAGvB,IAAI,CAACC,KAAK,CAACqB,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAME,OAAO,GAAGF,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACE,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CACrDC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACvB;;;uBA/QW5D,2BAA2B,EAAAnB,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnF,EAAA,CAAAiF,iBAAA,CAAAjF,EAAA,CAAAoF,SAAA,GAAApF,EAAA,CAAAiF,iBAAA,CAAAjF,EAAA,CAAAqF,iBAAA;IAAA;EAAA;;;YAA3BlE,2BAA2B;MAAAmE,SAAA;MAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;UCnBxCzF,EAAA,CAAAM,cAAA,aAQC;UAKGN,EAAA,CAAA2F,UAAA,mBAAAC,4DAAA;YAAA,OAASF,GAAA,CAAAlB,YAAA,EAAc;UAAA,EAAC;UAGzBxE,EAAA,CAAAS,YAAA,EAAQ;UAGTT,EAAA,CAAAM,cAAA,aAAkE;UAChEN,EAAA,CAAAO,UAAA,IAAAsF,0CAAA,iBAQO;UAGP7F,EAAA,CAAAO,UAAA,IAAAuF,0CAAA,iBAQO;UACT9F,EAAA,CAAAS,YAAA,EAAM;UAGNT,EAAA,CAAAM,cAAA,gBAQC;UANCN,EAAA,CAAA2F,UAAA,mBAAAI,6DAAA;YAAA,OAASL,GAAA,CAAA9B,UAAA,EAAY;UAAA,EAAC;UAQtB5D,EAAA,CAAAC,SAAA,aAOO;UAMTD,EAAA,CAAAS,YAAA,EAAS;UAETT,EAAA,CAAAM,cAAA,aAAkC;UAK5BN,EAAA,CAAAO,UAAA,KAAAyF,2CAAA,kBAsBM;UACRhG,EAAA,CAAAS,YAAA,EAAM;UAGNT,EAAA,CAAAC,SAAA,eAGO;UACTD,EAAA,CAAAS,YAAA,EAAM;UAGNT,EAAA,CAAAM,cAAA,eAGC;UAEGN,EAAA,CAAAiG,MAAA,IACF;UAAAjG,EAAA,CAAAS,YAAA,EAAO;;;UA9GXT,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAc,eAAA,KAAAoF,GAAA,GAAAR,GAAA,CAAAtF,aAAA,EAAAsF,GAAA,CAAAtF,aAAA,EAKE;UAKAJ,EAAA,CAAAkB,SAAA,GAAgB;UAAhBlB,EAAA,CAAAE,UAAA,QAAAwF,GAAA,CAAAhE,QAAA,EAAA1B,EAAA,CAAAmG,aAAA,CAAgB;UASbnG,EAAA,CAAAkB,SAAA,GAAe;UAAflB,EAAA,CAAAE,UAAA,SAAAwF,GAAA,CAAA1E,SAAA,CAAe;UAWfhB,EAAA,CAAAkB,SAAA,GAAe;UAAflB,EAAA,CAAAE,UAAA,SAAAwF,GAAA,CAAA1E,SAAA,CAAe;UAclBhB,EAAA,CAAAkB,SAAA,GAIE;UAJFlB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAc,eAAA,KAAAsF,GAAA,EAAAV,GAAA,CAAA1E,SAAA,GAAA0E,GAAA,CAAA1E,SAAA,EAIE;UAKAhB,EAAA,CAAAkB,SAAA,GAIE;UAJFlB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAc,eAAA,KAAAuF,GAAA,EAAAX,GAAA,CAAA1E,SAAA,GAAA0E,GAAA,CAAA1E,SAAA,EAIE;UAKFhB,EAAA,CAAAkB,SAAA,GAA8C;UAA9ClB,EAAA,CAAAE,UAAA,YAAAwF,GAAA,CAAA1E,SAAA,0BAA8C;UAY7ChB,EAAA,CAAAkB,SAAA,GAGC;UAHDlB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAsG,eAAA,KAAAC,GAAA,EAGC;UAsBAvG,EAAA,CAAAkB,SAAA,GAAoC;UAApClB,EAAA,CAAAU,WAAA,UAAAgF,GAAA,CAAAzE,kBAAA,MAAoC;UAOtCjB,EAAA,CAAAkB,SAAA,GAAkE;UAAlElB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAc,eAAA,KAAA0F,GAAA,EAAAd,GAAA,CAAA1E,SAAA,GAAA0E,GAAA,CAAA1E,SAAA,EAAkE;UAGhEhB,EAAA,CAAAkB,SAAA,GACF;UADElB,EAAA,CAAAyG,kBAAA,MAAAf,GAAA,CAAAV,iBAAA,MACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}