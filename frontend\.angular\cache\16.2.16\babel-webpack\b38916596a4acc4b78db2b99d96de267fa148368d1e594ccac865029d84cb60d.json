{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/planning.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction PlanningFormComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Titre is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"At least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, PlanningFormComponent_div_12_span_1_Template, 2, 0, \"span\", 21);\n    i0.ɵɵtemplate(2, PlanningFormComponent_div_12_span_2_Template, 2, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningFormComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r6._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r6.username);\n  }\n}\nfunction PlanningFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Please select at least one participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent__svg_svg_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 23);\n    i0.ɵɵelement(1, \"circle\", 24)(2, \"path\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PlanningFormComponent {\n  constructor(fb, userService, planningService, router) {\n    this.fb = fb;\n    this.userService = userService;\n    this.planningService = planningService;\n    this.router = router;\n    this.isLoading = false;\n    this.users$ = this.userService.getAllUsers();\n  }\n  ngOnInit() {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      lieu: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      participants: [[], Validators.required]\n    });\n  }\n  submit() {\n    if (this.planningForm.valid) {\n      this.isLoading = true;\n      const planning = this.planningForm.value;\n      // Call the createPlanning method to add the new planning\n      this.planningService.createPlanning(planning).subscribe(newPlanning => {\n        console.log('Planning created:', newPlanning);\n        this.isLoading = false;\n        // Optionally, reset the form or navigate to another page after successful creation\n        this.planningForm.reset();\n        this.router.navigate(['plannings']);\n      }, error => {\n        console.error('Error creating planning:', error);\n        this.isLoading = false;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function PlanningFormComponent_Factory(t) {\n      return new (t || PlanningFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PlanningFormComponent,\n      selectors: [[\"app-planning-form\"]],\n      decls: 40,\n      vars: 11,\n      consts: [[1, \"bg-gray-50\", \"p-5\", \"sm:p-6\", \"rounded-xl\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"grid\", \"grid-cols-1\", \"gap-y-4\", \"gap-x-4\", \"sm:grid-cols-6\"], [1, \"sm:col-span-3\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"class\", \"text-sm text-red-600 mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"sm:col-span-6\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"ml-3\", \"inline-flex\", \"justify-center\", \"py-2\", \"px-4\", \"border\", \"border-transparent\", \"shadow-sm\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-purple-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\"], [\"class\", \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"text-sm\", \"text-red-600\", \"mt-1\"], [4, \"ngIf\"], [3, \"value\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n      template: function PlanningFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Planning Form\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Fill in the details for the event\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function PlanningFormComponent_Template_form_ngSubmit_6_listener() {\n            return ctx.submit();\n          });\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"Titre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 8);\n          i0.ɵɵtemplate(12, PlanningFormComponent_div_12_Template, 3, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"label\", 7);\n          i0.ɵɵtext(15, \"Lieu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"input\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 7);\n          i0.ɵɵtext(19, \"Date de d\\u00E9but\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 6)(22, \"label\", 7);\n          i0.ɵɵtext(23, \"Date de fin\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 7);\n          i0.ɵɵtext(27, \"Participants\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"select\", 14);\n          i0.ɵɵtemplate(29, PlanningFormComponent_option_29_Template, 2, 2, \"option\", 15);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, PlanningFormComponent_div_31_Template, 2, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"label\", 7);\n          i0.ɵɵtext(34, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"textarea\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n          i0.ɵɵtemplate(38, PlanningFormComponent__svg_svg_38_Template, 3, 0, \"svg\", 19);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"border-red-300\", ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(30, 9, ctx.users$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.planningForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : \"Save Planning\", \" \");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1mb3JtLmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWZvcm0vcGxhbm5pbmctZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "PlanningFormComponent_div_12_span_1_Template", "PlanningFormComponent_div_12_span_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "planningForm", "get", "errors", "tmp_1_0", "user_r6", "_id", "ɵɵtextInterpolate", "username", "ɵɵnamespaceSVG", "ɵɵelement", "PlanningFormComponent", "constructor", "fb", "userService", "planningService", "router", "isLoading", "users$", "getAllUsers", "ngOnInit", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "lieu", "dateDebut", "dateFin", "participants", "submit", "valid", "planning", "value", "createPlanning", "subscribe", "newPlanning", "console", "log", "reset", "navigate", "error", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "DataService", "i3", "PlanningService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "PlanningFormComponent_Template", "rf", "ctx", "ɵɵlistener", "PlanningFormComponent_Template_form_ngSubmit_6_listener", "PlanningFormComponent_div_12_Template", "PlanningFormComponent_option_29_Template", "PlanningFormComponent_div_31_Template", "PlanningFormComponent__svg_svg_38_Template", "ɵɵclassProp", "invalid", "touched", "tmp_2_0", "ɵɵpipeBind1", "tmp_4_0", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-form\\planning-form.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\planning-form\\planning-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport {FormB<PERSON>er, FormGroup, Validators} from \"@angular/forms\";\r\nimport {Observable} from \"rxjs\";\r\nimport {User} from \"@app/models/user.model\";\r\nimport {DataService} from \"@app/services/data.service\";\r\nimport {Planning} from \"@app/models/planning.model\";\r\nimport {PlanningService} from \"@app/services/planning.service\";\r\nimport {Router} from \"@angular/router\";\r\n\r\n@Component({\r\n  selector: 'app-planning-form',\r\n  templateUrl: './planning-form.component.html',\r\n  styleUrls: ['./planning-form.component.css']\r\n})\r\nexport class PlanningFormComponent implements OnInit {\r\n  planningForm!: FormGroup;\r\n  isLoading = false;\r\n  users$: Observable<User[]> = this.userService.getAllUsers();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private userService: DataService,\r\n    private planningService: PlanningService,\r\n    private router:Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.planningForm = this.fb.group({\r\n      titre: ['', [Validators.required, Validators.minLength(3)]],\r\n      description: [''],\r\n      lieu: [''],\r\n      dateDebut: ['', Validators.required],\r\n      dateFin: ['', Validators.required],\r\n      participants: [[], Validators.required]\r\n    });\r\n  }\r\n\r\n  submit(): void {\r\n    if (this.planningForm.valid) {\r\n      this.isLoading = true;\r\n      const planning: Planning = this.planningForm.value;\r\n\r\n      // Call the createPlanning method to add the new planning\r\n      this.planningService.createPlanning(planning).subscribe(\r\n        (newPlanning:any) => {\r\n          console.log('Planning created:', newPlanning);\r\n          this.isLoading = false;\r\n          // Optionally, reset the form or navigate to another page after successful creation\r\n          this.planningForm.reset();\r\n          this.router.navigate(['plannings'])\r\n        },\r\n        (error:any) => {\r\n          console.error('Error creating planning:', error);\r\n          this.isLoading = false;\r\n        }\r\n      );\r\n    }\r\n  }\r\n}", "<section class=\"bg-gray-50 p-5 sm:p-6 rounded-xl\">\r\n  <div class=\"mb-4\">\r\n    <h2 class=\"text-lg font-medium text-gray-900\">Planning Form</h2>\r\n    <p class=\"text-sm text-gray-500 mt-1\">Fill in the details for the event</p>\r\n  </div>\r\n\r\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"submit()\" novalidate>\r\n    <div class=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6\">\r\n      <!-- Titre -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Titre</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"titre\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n          [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\r\n        />\r\n        <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-sm text-red-600 mt-1\">\r\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Titre is required</span>\r\n          <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">At least 3 characters</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Lieu -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Lieu</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"lieu\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Date début -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Date de début</label>\r\n        <input\r\n          type=\"date\"\r\n          formControlName=\"dateDebut\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Date fin -->\r\n      <div class=\"sm:col-span-3\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Date de fin</label>\r\n        <input\r\n          type=\"date\"\r\n          formControlName=\"dateFin\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Participants -->\r\n      <div class=\"sm:col-span-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Participants</label>\r\n        <select\r\n          formControlName=\"participants\"\r\n          multiple\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n        >\r\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\">{{ user.username }}</option>\r\n        </select>\r\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-sm text-red-600 mt-1\">\r\n          Please select at least one participant\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Description -->\r\n      <div class=\"sm:col-span-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Description</label>\r\n        <textarea\r\n          formControlName=\"description\"\r\n          class=\"mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm\"\r\n          rows=\"3\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mt-6 flex justify-end\">\r\n      <button\r\n        type=\"submit\"\r\n        [disabled]=\"planningForm.invalid || isLoading\"\r\n        class=\"ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n      >\r\n        <svg\r\n          *ngIf=\"isLoading\"\r\n          class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n        >\r\n          <circle\r\n            class=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            stroke-width=\"4\"\r\n          ></circle>\r\n          <path\r\n            class=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          ></path>\r\n        </svg>\r\n        {{ isLoading ? 'Saving...' : 'Save Planning' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</section>"], "mappings": "AACA,SAAgCA,UAAU,QAAO,gBAAgB;;;;;;;;;ICiBvDC,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF7FH,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,mBAAsF;IACtFL,EAAA,CAAAI,UAAA,IAAAE,4CAAA,mBAA2F;IAC7FN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDb,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IA0C7Db,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA/CH,EAAA,CAAAQ,UAAA,UAAAO,OAAA,CAAAC,GAAA,CAAkB;IAAChB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAiB,iBAAA,CAAAF,OAAA,CAAAG,QAAA,CAAmB;;;;;IAEpFlB,EAAA,CAAAC,cAAA,cAAsI;IACpID,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAmB,cAAA,EAMC;IANDnB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAoB,SAAA,iBAOU;IAMZpB,EAAA,CAAAG,YAAA,EAAM;;;AD3Fd,OAAM,MAAOkB,qBAAqB;EAKhCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,eAAgC,EAChCC,MAAa;IAHb,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAuB,IAAI,CAACJ,WAAW,CAACK,WAAW,EAAE;EAOxD;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACnB,YAAY,GAAG,IAAI,CAACY,EAAE,CAACQ,KAAK,CAAC;MAChCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAACkC,QAAQ,EAAElC,UAAU,CAACmC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACkC,QAAQ,CAAC;MACpCK,OAAO,EAAE,CAAC,EAAE,EAAEvC,UAAU,CAACkC,QAAQ,CAAC;MAClCM,YAAY,EAAE,CAAC,EAAE,EAAExC,UAAU,CAACkC,QAAQ;KACvC,CAAC;EACJ;EAEAO,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC7B,YAAY,CAAC8B,KAAK,EAAE;MAC3B,IAAI,CAACd,SAAS,GAAG,IAAI;MACrB,MAAMe,QAAQ,GAAa,IAAI,CAAC/B,YAAY,CAACgC,KAAK;MAElD;MACA,IAAI,CAAClB,eAAe,CAACmB,cAAc,CAACF,QAAQ,CAAC,CAACG,SAAS,CACpDC,WAAe,IAAI;QAClBC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,WAAW,CAAC;QAC7C,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB;QACA,IAAI,CAAChB,YAAY,CAACsC,KAAK,EAAE;QACzB,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC,EACAC,KAAS,IAAI;QACZJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACxB,SAAS,GAAG,KAAK;MACxB,CAAC,CACF;;EAEL;;;uBA3CWN,qBAAqB,EAAArB,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxD,EAAA,CAAAoD,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA1D,EAAA,CAAAoD,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBvC,qBAAqB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCnE,EAAA,CAAAC,cAAA,iBAAkD;UAEAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,WAAsC;UAAAD,EAAA,CAAAE,MAAA,wCAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7EH,EAAA,CAAAC,cAAA,cAAkE;UAAjCD,EAAA,CAAAqE,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAA5B,MAAA,EAAQ;UAAA,EAAC;UACpDxC,EAAA,CAAAC,cAAA,aAA6D;UAGFD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAoB,SAAA,gBAKE;UACFpB,EAAA,CAAAI,UAAA,KAAAmE,qCAAA,iBAGM;UACRvE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAoB,SAAA,iBAIE;UACJpB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAI,UAAA,KAAAoE,wCAAA,qBAA2F;;UAC7FxE,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAI,UAAA,KAAAqE,qCAAA,iBAEM;UACRzE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA2B;UAC8BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1EH,EAAA,CAAAoB,SAAA,oBAIY;UACdpB,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAmC;UAM/BD,EAAA,CAAAI,UAAA,KAAAsE,0CAAA,kBAoBM;UACN1E,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;UArGPH,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAAQ,UAAA,cAAA4D,GAAA,CAAAzD,YAAA,CAA0B;UASxBX,EAAA,CAAAO,SAAA,GAAiG;UAAjGP,EAAA,CAAA2E,WAAA,qBAAA7D,OAAA,GAAAsD,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAA8D,OAAA,OAAA9D,OAAA,GAAAsD,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAA+D,OAAA,EAAiG;UAE7F7E,EAAA,CAAAO,SAAA,GAA8E;UAA9EP,EAAA,CAAAQ,UAAA,WAAAsE,OAAA,GAAAV,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAkE,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAV,GAAA,CAAAzD,YAAA,CAAAC,GAAA,4BAAAkE,OAAA,CAAAD,OAAA,EAA8E;UA4CzD7E,EAAA,CAAAO,SAAA,IAAiB;UAAjBP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAA+E,WAAA,QAAAX,GAAA,CAAAxC,MAAA,EAAiB;UAEtC5B,EAAA,CAAAO,SAAA,GAA4F;UAA5FP,EAAA,CAAAQ,UAAA,WAAAwE,OAAA,GAAAZ,GAAA,CAAAzD,YAAA,CAAAC,GAAA,mCAAAoE,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAZ,GAAA,CAAAzD,YAAA,CAAAC,GAAA,mCAAAoE,OAAA,CAAAH,OAAA,EAA4F;UAmBlG7E,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,UAAA,aAAA4D,GAAA,CAAAzD,YAAA,CAAAiE,OAAA,IAAAR,GAAA,CAAAzC,SAAA,CAA8C;UAI3C3B,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAA4D,GAAA,CAAAzC,SAAA,CAAe;UAoBlB3B,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAiF,kBAAA,MAAAb,GAAA,CAAAzC,SAAA,sCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}