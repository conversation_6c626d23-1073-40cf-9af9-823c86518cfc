{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HomeRoutingModule } from './home-routing.module';\nimport { HomeComponent } from './home.component';\nimport * as i0 from \"@angular/core\";\nexport class HomeModule {\n  static {\n    this.ɵfac = function HomeModule_Factory(t) {\n      return new (t || HomeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, HomeRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeModule, {\n    declarations: [HomeComponent],\n    imports: [CommonModule, HomeRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "HomeRoutingModule", "HomeComponent", "HomeModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { HomeRoutingModule } from './home-routing.module';\r\nimport { HomeComponent } from './home.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HomeComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    HomeRoutingModule\r\n  ]\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,kBAAkB;;AAYhD,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAJnBH,YAAY,EACZC,iBAAiB;IAAA;EAAA;;;2EAGRE,UAAU;IAAAC,YAAA,GAPnBF,aAAa;IAAAG,OAAA,GAGbL,YAAY,EACZC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}