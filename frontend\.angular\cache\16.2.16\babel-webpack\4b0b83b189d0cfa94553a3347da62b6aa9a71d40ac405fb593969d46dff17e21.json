{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction SignupComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38);\n    i0.ɵɵelement(3, \"i\", 39)(4, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"p\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction SignupComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 37)(2, \"div\", 44);\n    i0.ɵɵelement(3, \"i\", 45)(4, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"p\", 47);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nexport class SignupComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.message = '';\n    this.error = '';\n    this.submittedEmail = '';\n    this.signupForm = this.fb.group({\n      fullName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', Validators.required]\n    });\n  }\n  onSignupSubmit() {\n    if (this.signupForm.invalid) return;\n    const signupData = this.signupForm.value;\n    this.submittedEmail = signupData.email;\n    this.authService.signup(signupData).subscribe({\n      next: res => {\n        console.log('Signup successful:', res);\n        this.message = res.message;\n        this.error = '';\n        // Attendre un court instant avant de rediriger\n        setTimeout(() => {\n          // Rediriger vers le composant de vérification d'email\n          this.router.navigate(['/verify-email'], {\n            queryParams: {\n              email: this.submittedEmail\n            }\n          });\n        }, 500);\n      },\n      error: err => {\n        console.error('Signup error:', err);\n        // Si l'utilisateur existe déjà mais que nous avons besoin de vérifier l'email\n        if (err.error && err.error.message === 'Email already exists' && err.error.needsVerification) {\n          // Rediriger vers la vérification d'email\n          this.router.navigate(['/verify-email'], {\n            queryParams: {\n              email: this.submittedEmail\n            }\n          });\n          return;\n        }\n        // Gérer les autres erreurs\n        this.error = err.error?.message || 'Signup failed';\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 65,\n      vars: 3,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-user\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"formControlName\", \"fullName\", \"placeholder\", \"John Doe\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [\"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [\"formControlName\", \"password\", \"type\", \"password\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵtext(23, \" Create Account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 13);\n          i0.ɵɵtext(25, \" Sign up to join DevBridge \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.onSignupSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵtext(31, \" Full Name \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"div\", 21);\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 16)(37, \"label\", 17);\n          i0.ɵɵelement(38, \"i\", 23);\n          i0.ɵɵtext(39, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 19);\n          i0.ɵɵelement(41, \"input\", 24);\n          i0.ɵɵelementStart(42, \"div\", 21);\n          i0.ɵɵelement(43, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 16)(45, \"label\", 17);\n          i0.ɵɵelement(46, \"i\", 25);\n          i0.ɵɵtext(47, \" Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 19);\n          i0.ɵɵelement(49, \"input\", 26);\n          i0.ɵɵelementStart(50, \"div\", 21);\n          i0.ɵɵelement(51, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(52, SignupComponent_div_52_Template, 8, 1, \"div\", 27);\n          i0.ɵɵtemplate(53, SignupComponent_div_53_Template, 8, 1, \"div\", 28);\n          i0.ɵɵelementStart(54, \"button\", 29);\n          i0.ɵɵelement(55, \"div\", 30)(56, \"div\", 31);\n          i0.ɵɵelementStart(57, \"span\", 32);\n          i0.ɵɵelement(58, \"i\", 33);\n          i0.ɵɵtext(59, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\");\n          i0.ɵɵtext(62, \" Already have an account? \");\n          i0.ɵɵelementStart(63, \"a\", 35);\n          i0.ɵɵtext(64, \" Sign in \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"formGroup\", ctx.signupForm);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzaWdudXAuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvc2lnbnVwL3NpZ251cC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsZ0tBQWdLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ctx_r1", "message", "SignupComponent", "constructor", "fb", "authService", "router", "submittedEmail", "signupForm", "group", "fullName", "required", "email", "password", "onSignupSubmit", "invalid", "signupData", "value", "signup", "subscribe", "next", "res", "console", "log", "setTimeout", "navigate", "queryParams", "err", "needsVerification", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "SignupComponent_div_52_Template", "SignupComponent_div_53_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\signup\\signup.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-signup',\r\n  templateUrl: './signup.component.html',\r\n  styleUrls: ['./signup.component.css'],\r\n})\r\nexport class SignupComponent {\r\n  signupForm: FormGroup;\r\n  message = '';\r\n  error = '';\r\n  submittedEmail = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {\r\n    this.signupForm = this.fb.group({\r\n      fullName: ['', Validators.required],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  onSignupSubmit() {\r\n    if (this.signupForm.invalid) return;\r\n\r\n    const signupData = this.signupForm.value;\r\n    this.submittedEmail = signupData.email;\r\n\r\n    this.authService.signup(signupData).subscribe({\r\n      next: (res: any) => {\r\n        console.log('Signup successful:', res);\r\n        this.message = res.message;\r\n        this.error = '';\r\n\r\n        // Attendre un court instant avant de rediriger\r\n        setTimeout(() => {\r\n          // Rediriger vers le composant de vérification d'email\r\n          this.router.navigate(['/verify-email'], {\r\n            queryParams: { email: this.submittedEmail },\r\n          });\r\n        }, 500);\r\n      },\r\n      error: (err) => {\r\n        console.error('Signup error:', err);\r\n\r\n        // Si l'utilisateur existe déjà mais que nous avons besoin de vérifier l'email\r\n        if (\r\n          err.error &&\r\n          err.error.message === 'Email already exists' &&\r\n          err.error.needsVerification\r\n        ) {\r\n          // Rediriger vers la vérification d'email\r\n          this.router.navigate(['/verify-email'], {\r\n            queryParams: { email: this.submittedEmail },\r\n          });\r\n          return;\r\n        }\r\n\r\n        // Gérer les autres erreurs\r\n        this.error = err.error?.message || 'Signup failed';\r\n        this.message = '';\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div\r\n  class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] flex items-center justify-center p-4 relative futuristic-layout\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          Create Account\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          Sign up to join DevBridge\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"p-6\">\r\n        <!-- Signup Form -->\r\n        <form\r\n          [formGroup]=\"signupForm\"\r\n          (ngSubmit)=\"onSignupSubmit()\"\r\n          class=\"space-y-5\"\r\n        >\r\n          <!-- Full Name -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-user mr-1.5 text-xs\"></i>\r\n              Full Name\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                formControlName=\"fullName\"\r\n                placeholder=\"John Doe\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Email -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\r\n              Email\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                formControlName=\"email\"\r\n                type=\"email\"\r\n                placeholder=\"<EMAIL>\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Password -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-lock mr-1.5 text-xs\"></i>\r\n              Password\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                formControlName=\"password\"\r\n                type=\"password\"\r\n                placeholder=\"••••••••\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <i class=\"fas fa-user-plus mr-2\"></i>\r\n              Sign Up\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Link to login -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div>\r\n              Already have an account?\r\n              <a\r\n                routerLink=\"/login\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium\"\r\n              >\r\n                Sign in\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICwIzDC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;ADvKhB,OAAM,MAAOC,eAAe;EAM1BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IACV,KAAAQ,cAAc,GAAG,EAAE;IAOjB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC9BC,QAAQ,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACqB,QAAQ,CAAC;MACnCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACsB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACqB,QAAQ;KACnC,CAAC;EACJ;EAEAG,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACN,UAAU,CAACO,OAAO,EAAE;IAE7B,MAAMC,UAAU,GAAG,IAAI,CAACR,UAAU,CAACS,KAAK;IACxC,IAAI,CAACV,cAAc,GAAGS,UAAU,CAACJ,KAAK;IAEtC,IAAI,CAACP,WAAW,CAACa,MAAM,CAACF,UAAU,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,GAAG,CAAC;QACtC,IAAI,CAACpB,OAAO,GAAGoB,GAAG,CAACpB,OAAO;QAC1B,IAAI,CAACF,KAAK,GAAG,EAAE;QAEf;QACAyB,UAAU,CAAC,MAAK;UACd;UACA,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE;YACtCC,WAAW,EAAE;cAAEd,KAAK,EAAE,IAAI,CAACL;YAAc;WAC1C,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDR,KAAK,EAAG4B,GAAG,IAAI;QACbL,OAAO,CAACvB,KAAK,CAAC,eAAe,EAAE4B,GAAG,CAAC;QAEnC;QACA,IACEA,GAAG,CAAC5B,KAAK,IACT4B,GAAG,CAAC5B,KAAK,CAACE,OAAO,KAAK,sBAAsB,IAC5C0B,GAAG,CAAC5B,KAAK,CAAC6B,iBAAiB,EAC3B;UACA;UACA,IAAI,CAACtB,MAAM,CAACmB,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE;YACtCC,WAAW,EAAE;cAAEd,KAAK,EAAE,IAAI,CAACL;YAAc;WAC1C,CAAC;UACF;;QAGF;QACA,IAAI,CAACR,KAAK,GAAG4B,GAAG,CAAC5B,KAAK,EAAEE,OAAO,IAAI,eAAe;QAClD,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBA3DWC,eAAe,EAAAX,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAsC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfjC,eAAe;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BnD,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAI,MAAA,wBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,mCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,eAAiB;UAIbD,EAAA,CAAAqD,UAAA,sBAAAC,mDAAA;YAAA,OAAYF,GAAA,CAAA7B,cAAA,EAAgB;UAAA,EAAC;UAI7BvB,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAI,MAAA,mBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAIE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA8C;UAC9CF,EAAA,CAAAI,MAAA,eACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAI,MAAA,kBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAuD,UAAA,KAAAC,+BAAA,kBAoBM;UAGNxD,EAAA,CAAAuD,UAAA,KAAAE,+BAAA,kBAoBM;UAGNzD,EAAA,CAAAC,cAAA,kBAGC;UACCD,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAE,SAAA,aAAqC;UACrCF,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAITH,EAAA,CAAAC,cAAA,eAEC;UAEGD,EAAA,CAAAI,MAAA,kCACA;UAAAJ,EAAA,CAAAC,cAAA,aAGC;UACCD,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;UA1JRH,EAAA,CAAAK,SAAA,IAAwB;UAAxBL,EAAA,CAAA0D,UAAA,cAAAN,GAAA,CAAAnC,UAAA,CAAwB;UAgFrBjB,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAA0D,UAAA,SAAAN,GAAA,CAAA5C,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA0D,UAAA,SAAAN,GAAA,CAAA1C,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}