{"ast": null, "code": "import { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/logger.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction ActiveCallComponent_div_0_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r5.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.getOtherParticipantName());\n  }\n}\nfunction ActiveCallComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"video\", 14, 15)(4, \"div\", 16);\n    i0.ɵɵtemplate(5, ActiveCallComponent_div_0_div_1_div_5_Template, 6, 2, \"div\", 17);\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19);\n    i0.ɵɵelement(8, \"div\", 20);\n    i0.ɵɵelementStart(9, \"span\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 22)(12, \"h3\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 24);\n    i0.ɵɵelement(15, \"video\", 25, 26)(17, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !_r4.srcObject);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getCallStatusText());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getOtherParticipantName(), \" \");\n  }\n}\nfunction ActiveCallComponent_div_0_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42);\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", 10 + i_r8 % 5 * 6, \"px\")(\"animation-delay\", i_r8 * 100, \"ms\");\n  }\n}\nconst _c2 = function () {\n  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];\n};\nfunction ActiveCallComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 32)(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"h2\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵelement(10, \"div\", 20);\n    i0.ɵɵelementStart(11, \"span\", 21);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 40);\n    i0.ɵɵtemplate(14, ActiveCallComponent_div_0_div_2_div_14_Template, 1, 4, \"div\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.getOtherParticipantName());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getCallStatusText());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c2));\n  }\n}\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9] text-white\": a0,\n    \"bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]\": a1\n  };\n};\nconst _c4 = function (a0, a1) {\n  return {\n    \"opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\": a0,\n    \"opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30\": a1\n  };\n};\nfunction ActiveCallComponent_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"div\", 6)(2, \"i\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c3, !ctx_r3.isVideoMuted, ctx_r3.isVideoMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c4, !ctx_r3.isVideoMuted, ctx_r3.isVideoMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, ActiveCallComponent_div_0_div_1_Template, 18, 3, \"div\", 2);\n    i0.ɵɵtemplate(2, ActiveCallComponent_div_0_div_2_Template, 15, 6, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.toggleMicrophone());\n    });\n    i0.ɵɵelement(5, \"div\", 6)(6, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ActiveCallComponent_div_0_button_7_Template, 3, 9, \"button\", 8);\n    i0.ɵɵelementStart(8, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggleSpeaker());\n    });\n    i0.ɵɵelement(9, \"div\", 6)(10, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.endCall());\n    });\n    i0.ɵɵelement(12, \"div\", 10)(13, \"i\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoCall());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c3, !ctx_r0.isAudioMuted, ctx_r0.isAudioMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c4, !ctx_r0.isAudioMuted, ctx_r0.isAudioMuted));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c3, ctx_r0.isSpeakerOn, !ctx_r0.isSpeakerOn));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx_r0.isSpeakerOn, !ctx_r0.isSpeakerOn));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-mute\");\n  }\n}\nexport class ActiveCallComponent {\n  constructor(messageService, logger) {\n    this.messageService = messageService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.callStartTime = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n    this.CallStatus = CallStatus;\n  }\n  ngOnInit() {\n    // S'abonner à l'appel actif\n    const activeCallSub = this.messageService.activeCall$.subscribe(call => {\n      const previousCall = this.activeCall;\n      this.activeCall = call;\n      if (call && call.status === CallStatus.CONNECTED) {\n        if (!previousCall || previousCall.id !== call.id) {\n          // Nouvel appel connecté\n          this.startCallTimer();\n        }\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\n        // Appel terminé ou non connecté\n        this.stopCallTimer();\n      }\n    });\n    this.subscriptions.push(activeCallSub);\n    // S'abonner au flux local\n    const localStreamSub = this.messageService.localStream$.subscribe(stream => {\n      if (stream && this.localVideo?.nativeElement) {\n        this.localVideo.nativeElement.srcObject = stream;\n      }\n    });\n    this.subscriptions.push(localStreamSub);\n    // S'abonner au flux distant\n    const remoteStreamSub = this.messageService.remoteStream$.subscribe(stream => {\n      if (stream && this.remoteVideo?.nativeElement) {\n        this.remoteVideo.nativeElement.srcObject = stream;\n      }\n    });\n    this.subscriptions.push(remoteStreamSub);\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après le rendu du composant\n    const localStream = this.messageService.localStream$.getValue();\n    if (localStream && this.localVideo?.nativeElement) {\n      this.localVideo.nativeElement.srcObject = localStream;\n    }\n    const remoteStream = this.messageService.remoteStream$.getValue();\n    if (remoteStream && this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.srcObject = remoteStream;\n    }\n  }\n  // Démarrer le minuteur d'appel\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.stopCallTimer(); // Arrêter tout minuteur existant\n    this.durationInterval = setInterval(() => {\n      if (!this.callStartTime) return;\n      const now = new Date();\n      const diff = Math.floor((now.getTime() - this.callStartTime.getTime()) / 1000);\n      const minutes = Math.floor(diff / 60).toString().padStart(2, '0');\n      const seconds = (diff % 60).toString().padStart(2, '0');\n      this.callDuration = `${minutes}:${seconds}`;\n    }, 1000);\n  }\n  // Arrêter le minuteur d'appel\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n  }\n  // Terminer l'appel\n  endCall() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.logger.debug('Ending call', {\n      callId: this.activeCall.id\n    });\n    this.messageService.endCall(this.activeCall.id).subscribe({\n      next: call => {\n        this.logger.debug('Call ended successfully', {\n          callId: call.id,\n          duration: call.duration\n        });\n      },\n      error: error => {\n        this.logger.error('Error ending call', error);\n      }\n    });\n  }\n  // Basculer le micro\n  toggleMicrophone() {\n    if (!this.activeCall) {\n      return;\n    }\n    this.isAudioMuted = !this.isAudioMuted;\n    this.messageService.toggleMedia(this.activeCall.id, undefined, !this.isAudioMuted).subscribe({\n      next: result => {\n        this.logger.debug('Microphone toggled', {\n          muted: this.isAudioMuted\n        });\n      },\n      error: error => {\n        this.logger.error('Error toggling microphone', error);\n        // Revenir à l'état précédent en cas d'erreur\n        this.isAudioMuted = !this.isAudioMuted;\n      }\n    });\n  }\n  // Basculer la caméra\n  toggleCamera() {\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\n      return;\n    }\n    this.isVideoMuted = !this.isVideoMuted;\n    this.messageService.toggleMedia(this.activeCall.id, !this.isVideoMuted, undefined).subscribe({\n      next: result => {\n        this.logger.debug('Camera toggled', {\n          muted: this.isVideoMuted\n        });\n      },\n      error: error => {\n        this.logger.error('Error toggling camera', error);\n        // Revenir à l'état précédent en cas d'erreur\n        this.isVideoMuted = !this.isVideoMuted;\n      }\n    });\n  }\n  // Basculer le haut-parleur\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    if (this.remoteVideo?.nativeElement) {\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\n    }\n    this.logger.debug('Speaker toggled', {\n      on: this.isSpeakerOn\n    });\n  }\n  // Obtenir le nom de l'autre participant\n  getOtherParticipantName() {\n    if (!this.activeCall) {\n      return '';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    return isCurrentUserCaller ? this.activeCall.recipient.username : this.activeCall.caller.username;\n  }\n  // Obtenir l'avatar de l'autre participant\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) {\n      return 'assets/images/default-avatar.png';\n    }\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\n    const currentUserId = localStorage.getItem('userId');\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\n    const avatar = isCurrentUserCaller ? this.activeCall.recipient.image : this.activeCall.caller.image;\n    return avatar || 'assets/images/default-avatar.png';\n  }\n  // Obtenir le statut de l'appel sous forme de texte\n  getCallStatusText() {\n    if (!this.activeCall) {\n      return '';\n    }\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Appel en cours...';\n      case CallStatus.CONNECTED:\n        return this.callDuration;\n      case CallStatus.ENDED:\n        return 'Appel terminé';\n      case CallStatus.MISSED:\n        return 'Appel manqué';\n      case CallStatus.REJECTED:\n        return 'Appel rejeté';\n      case CallStatus.FAILED:\n        return \"Échec de l'appel\";\n      default:\n        return '';\n    }\n  }\n  // Vérifier si l'appel est connecté\n  isCallConnected() {\n    return this.activeCall?.status === CallStatus.CONNECTED;\n  }\n  // Vérifier si l'appel est en cours de sonnerie\n  isCallRinging() {\n    return this.activeCall?.status === CallStatus.RINGING;\n  }\n  // Vérifier si l'appel est un appel vidéo\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO || this.activeCall?.type === CallType.VIDEO_ONLY;\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements et le minuteur\n    this.stopCallTimer();\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      viewQuery: function ActiveCallComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 z-50 bg-[#121212]/90 backdrop-blur-sm flex flex-col\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"bg-[#121212]/90\", \"backdrop-blur-sm\", \"flex\", \"flex-col\"], [\"class\", \"flex-1 relative\", 4, \"ngIf\"], [\"class\", \"flex-1 flex flex-col items-center justify-center\", 4, \"ngIf\"], [1, \"p-6\", \"flex\", \"justify-center\", \"space-x-4\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group\", 3, \"ngClass\", \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"blur-md\", \"transition-opacity\", 3, \"ngClass\"], [1, \"fas\", \"text-lg\", \"relative\", \"z-10\", 3, \"ngClass\"], [\"class\", \"w-14 h-14 rounded-full flex items-center justify-center relative group\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"text-white\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/50\", \"rounded-full\", \"blur-md\", \"opacity-70\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"fas\", \"fa-phone-slash\", \"text-lg\", \"relative\", \"z-10\"], [1, \"flex-1\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"remoteVideo\", \"\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-[#121212]/80\", \"to-transparent\", \"pointer-events-none\"], [\"class\", \"absolute inset-0 flex items-center justify-center bg-[#121212]/70\", 4, \"ngIf\"], [1, \"absolute\", \"top-6\", \"left-0\", \"right-0\", \"flex\", \"justify-center\", \"pointer-events-none\"], [1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-[#121212]/50\", \"backdrop-blur-sm\", \"border\", \"border-[#2a2a2a]\", \"flex\", \"items-center\", \"space-x-2\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"animate-pulse\"], [1, \"text-white\", \"text-sm\", \"font-medium\"], [1, \"absolute\", \"bottom-24\", \"left-0\", \"right-0\", \"text-center\", \"pointer-events-none\"], [1, \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"absolute\", \"top-4\", \"right-4\", \"w-32\", \"h-48\", \"rounded-lg\", \"overflow-hidden\", \"shadow-lg\", \"border\", \"border-[#2a2a2a]\", \"z-10\"], [\"autoplay\", \"\", \"playsinline\", \"\", \"muted\", \"\", 1, \"w-full\", \"h-full\", \"object-cover\"], [\"localVideo\", \"\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-[#121212]/30\", \"to-transparent\", \"pointer-events-none\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"bg-[#121212]/70\"], [1, \"relative\"], [1, \"w-32\", \"h-32\", \"rounded-full\", \"overflow-hidden\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/30\", \"dark:bg-[#6d78c9]/30\", \"rounded-full\", \"blur-md\"], [1, \"absolute\", \"inset-0\", \"border-2\", \"border-[#4f5fad]/40\", \"dark:border-[#6d78c9]/40\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\"], [1, \"relative\", \"mb-8\"], [1, \"absolute\", \"-inset-4\", \"border-2\", \"border-[#4f5fad]/10\", \"dark:border-[#6d78c9]/10\", \"rounded-full\", \"animate-ping\", \"opacity-75\"], [1, \"absolute\", \"-inset-8\", \"border\", \"border-[#4f5fad]/5\", \"dark:border-[#6d78c9]/5\", \"rounded-full\", \"animate-ping\", \"opacity-50\", \"delay-300\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"px-4\", \"py-2\", \"rounded-full\", \"bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"border\", \"border-[#2a2a2a]\", \"flex\", \"items-center\", \"space-x-2\", \"mb-8\"], [1, \"flex\", \"items-center\", \"h-16\", \"space-x-1\", \"mb-8\"], [\"class\", \"w-1 bg-gradient-to-t from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\", 3, \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-1\", \"bg-gradient-to-t\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 14, 21, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.active-call-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 1000;\\n  background-color: var(--dark-bg);\\n  border-radius: var(--border-radius-lg) 0 0 0;\\n  box-shadow: 0 -4px 30px rgba(0, 0, 0, 0.3);\\n  overflow: hidden;\\n  transition: all var(--transition-medium);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.active-call-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.5;\\n  z-index: 1;\\n}\\n\\n.active-call-container.video-call[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 0;\\n  background-color: var(--dark-bg);\\n}\\n\\n\\n\\n.video-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.video-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    transparent 50%,\\n    var(--dark-bg) 150%\\n  );\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.remote-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.remote-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, var(--medium-bg), var(--dark-bg));\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 300px;\\n  height: 300px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.1;\\n  filter: blur(40px);\\n  animation: _ngcontent-%COMP%_pulse 4s infinite;\\n}\\n\\n.video-placeholder[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 2;\\n  position: relative;\\n}\\n\\n.call-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  left: 30px;\\n  color: var(--text-light);\\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 15px 20px;\\n  border-radius: var(--border-radius-md);\\n  border-left: 3px solid var(--accent-color);\\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.participant-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: none;\\n}\\n\\n.call-status[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  font-size: 16px;\\n  opacity: 0.9;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.call-status[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  margin-right: 8px;\\n  box-shadow: 0 0 8px var(--accent-color);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 30px;\\n  width: 200px;\\n  height: 130px;\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  border: 2px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n  z-index: 10;\\n  transition: all var(--transition-medium);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.5);\\n}\\n\\n.local-video-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border: 1px solid rgba(0, 247, 255, 0.5);\\n  border-radius: var(--border-radius-md);\\n  pointer-events: none;\\n}\\n\\n.local-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transform: scaleX(-1); \\n\\n}\\n\\n\\n\\n.audio-call-info[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  text-align: center;\\n  width: 350px;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.05),\\n    rgba(157, 78, 221, 0.05)\\n  );\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 100px;\\n  height: 100px;\\n  background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);\\n  opacity: 0.3;\\n  filter: blur(20px);\\n  animation: _ngcontent-%COMP%_pulse 3s infinite;\\n  z-index: -1;\\n}\\n\\n.participant-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  border: 3px solid var(--accent-color);\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.call-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 20px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.video-call[_ngcontent-%COMP%]   .call-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.control-btn[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: none;\\n  margin: 0 12px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.control-btn[_ngcontent-%COMP%]::before, .end-call-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.1) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover::before, .end-call-btn[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.control-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--text-light);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  border: none;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n.control-btn.active[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.7);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3547, #ff5252);\\n  color: white;\\n  box-shadow: 0 0 15px rgba(255, 53, 71, 0.3);\\n}\\n\\n.end-call-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 0 20px rgba(255, 53, 71, 0.5);\\n}\\n\\n.control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .end-call-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n  50% {\\n    opacity: 0.6;\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    opacity: 0.3;\\n    transform: scale(0.95);\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"@keyframes _ngcontent-%COMP%_audio-visualizer {\\n    0%,\\n    100% {\\n      height: var(--min-height, 10px);\\n    }\\n    50% {\\n      height: var(--max-height, 40px);\\n    }\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "getOtherParticipantName", "ɵɵtemplate", "ActiveCallComponent_div_0_div_1_div_5_Template", "ɵɵtext", "_r4", "srcObject", "ɵɵtextInterpolate", "ctx_r1", "getCallStatusText", "ɵɵtextInterpolate1", "ɵɵstyleProp", "i_r8", "ActiveCallComponent_div_0_div_2_div_14_Template", "ctx_r2", "ɵɵpureFunction0", "_c2", "ɵɵlistener", "ActiveCallComponent_div_0_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵpureFunction2", "_c3", "ctx_r3", "isVideoMuted", "_c4", "ActiveCallComponent_div_0_div_1_Template", "ActiveCallComponent_div_0_div_2_Template", "ActiveCallComponent_div_0_Template_button_click_4_listener", "_r12", "ctx_r11", "toggleMicrophone", "ActiveCallComponent_div_0_button_7_Template", "ActiveCallComponent_div_0_Template_button_click_8_listener", "ctx_r13", "toggleSpeaker", "ActiveCallComponent_div_0_Template_button_click_11_listener", "ctx_r14", "endCall", "ctx_r0", "isVideoCall", "isAudioMuted", "isSpeakerOn", "ActiveCallComponent", "constructor", "messageService", "logger", "activeCall", "callDuration", "callStartTime", "subscriptions", "ngOnInit", "activeCallSub", "activeCall$", "subscribe", "call", "previousCall", "status", "CONNECTED", "id", "startCallTimer", "stopCallTimer", "push", "localStreamSub", "localStream$", "stream", "localVideo", "nativeElement", "remoteStreamSub", "remoteStream$", "remoteVideo", "ngAfterViewInit", "localStream", "getValue", "remoteStream", "Date", "durationInterval", "setInterval", "now", "diff", "Math", "floor", "getTime", "minutes", "toString", "padStart", "seconds", "clearInterval", "debug", "callId", "next", "duration", "error", "toggleMedia", "undefined", "result", "muted", "type", "AUDIO", "volume", "on", "currentUserId", "localStorage", "getItem", "isCurrentUserCaller", "caller", "recipient", "username", "avatar", "image", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "isCallConnected", "isCallRinging", "VIDEO", "VIDEO_ONLY", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "LoggerService", "selectors", "viewQuery", "ActiveCallComponent_Query", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  <PERSON><PERSON>nit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  ViewChild,\r\n  ElementRef,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { Subscription } from 'rxjs';\r\nimport { Call, CallType, CallStatus } from '../../models/message.model';\r\nimport { MessageService } from '../../services/message.service';\r\nimport { LoggerService } from '../../services/logger.service';\r\n\r\n@Component({\r\n  selector: 'app-active-call',\r\n  templateUrl: './active-call.component.html',\r\n  styleUrls: ['./active-call.component.css'],\r\n})\r\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\r\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  activeCall: Call | null = null;\r\n  callDuration: string = '00:00';\r\n  isAudioMuted: boolean = false;\r\n  isVideoMuted: boolean = false;\r\n  isSpeakerOn: boolean = true;\r\n\r\n  private durationInterval: any;\r\n  private callStartTime: Date | null = null;\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  // Exposer les énums au template\r\n  CallType = CallType;\r\n  CallStatus = CallStatus;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private logger: LoggerService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // S'abonner à l'appel actif\r\n    const activeCallSub = this.messageService.activeCall$.subscribe((call) => {\r\n      const previousCall = this.activeCall;\r\n      this.activeCall = call;\r\n\r\n      if (call && call.status === CallStatus.CONNECTED) {\r\n        if (!previousCall || previousCall.id !== call.id) {\r\n          // Nouvel appel connecté\r\n          this.startCallTimer();\r\n        }\r\n      } else if (!call || call.status !== CallStatus.CONNECTED) {\r\n        // Appel terminé ou non connecté\r\n        this.stopCallTimer();\r\n      }\r\n    });\r\n\r\n    this.subscriptions.push(activeCallSub);\r\n\r\n    // S'abonner au flux local\r\n    const localStreamSub = this.messageService.localStream$.subscribe(\r\n      (stream) => {\r\n        if (stream && this.localVideo?.nativeElement) {\r\n          this.localVideo.nativeElement.srcObject = stream;\r\n        }\r\n      }\r\n    );\r\n\r\n    this.subscriptions.push(localStreamSub);\r\n\r\n    // S'abonner au flux distant\r\n    const remoteStreamSub = this.messageService.remoteStream$.subscribe(\r\n      (stream) => {\r\n        if (stream && this.remoteVideo?.nativeElement) {\r\n          this.remoteVideo.nativeElement.srcObject = stream;\r\n        }\r\n      }\r\n    );\r\n\r\n    this.subscriptions.push(remoteStreamSub);\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Configurer les éléments vidéo après le rendu du composant\r\n    const localStream = this.messageService.localStream$.getValue();\r\n    if (localStream && this.localVideo?.nativeElement) {\r\n      this.localVideo.nativeElement.srcObject = localStream;\r\n    }\r\n\r\n    const remoteStream = this.messageService.remoteStream$.getValue();\r\n    if (remoteStream && this.remoteVideo?.nativeElement) {\r\n      this.remoteVideo.nativeElement.srcObject = remoteStream;\r\n    }\r\n  }\r\n\r\n  // Démarrer le minuteur d'appel\r\n  private startCallTimer(): void {\r\n    this.callStartTime = new Date();\r\n    this.stopCallTimer(); // Arrêter tout minuteur existant\r\n\r\n    this.durationInterval = setInterval(() => {\r\n      if (!this.callStartTime) return;\r\n\r\n      const now = new Date();\r\n      const diff = Math.floor(\r\n        (now.getTime() - this.callStartTime.getTime()) / 1000\r\n      );\r\n\r\n      const minutes = Math.floor(diff / 60)\r\n        .toString()\r\n        .padStart(2, '0');\r\n      const seconds = (diff % 60).toString().padStart(2, '0');\r\n\r\n      this.callDuration = `${minutes}:${seconds}`;\r\n    }, 1000);\r\n  }\r\n\r\n  // Arrêter le minuteur d'appel\r\n  private stopCallTimer(): void {\r\n    if (this.durationInterval) {\r\n      clearInterval(this.durationInterval);\r\n      this.durationInterval = null;\r\n    }\r\n  }\r\n\r\n  // Terminer l'appel\r\n  endCall(): void {\r\n    if (!this.activeCall) {\r\n      return;\r\n    }\r\n\r\n    this.logger.debug('Ending call', { callId: this.activeCall.id });\r\n\r\n    this.messageService.endCall(this.activeCall.id).subscribe({\r\n      next: (call) => {\r\n        this.logger.debug('Call ended successfully', {\r\n          callId: call.id,\r\n          duration: call.duration,\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('Error ending call', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Basculer le micro\r\n  toggleMicrophone(): void {\r\n    if (!this.activeCall) {\r\n      return;\r\n    }\r\n\r\n    this.isAudioMuted = !this.isAudioMuted;\r\n\r\n    this.messageService\r\n      .toggleMedia(this.activeCall.id, undefined, !this.isAudioMuted)\r\n      .subscribe({\r\n        next: (result) => {\r\n          this.logger.debug('Microphone toggled', { muted: this.isAudioMuted });\r\n        },\r\n        error: (error) => {\r\n          this.logger.error('Error toggling microphone', error);\r\n          // Revenir à l'état précédent en cas d'erreur\r\n          this.isAudioMuted = !this.isAudioMuted;\r\n        },\r\n      });\r\n  }\r\n\r\n  // Basculer la caméra\r\n  toggleCamera(): void {\r\n    if (!this.activeCall || this.activeCall.type === CallType.AUDIO) {\r\n      return;\r\n    }\r\n\r\n    this.isVideoMuted = !this.isVideoMuted;\r\n\r\n    this.messageService\r\n      .toggleMedia(this.activeCall.id, !this.isVideoMuted, undefined)\r\n      .subscribe({\r\n        next: (result) => {\r\n          this.logger.debug('Camera toggled', { muted: this.isVideoMuted });\r\n        },\r\n        error: (error) => {\r\n          this.logger.error('Error toggling camera', error);\r\n          // Revenir à l'état précédent en cas d'erreur\r\n          this.isVideoMuted = !this.isVideoMuted;\r\n        },\r\n      });\r\n  }\r\n\r\n  // Basculer le haut-parleur\r\n  toggleSpeaker(): void {\r\n    this.isSpeakerOn = !this.isSpeakerOn;\r\n\r\n    if (this.remoteVideo?.nativeElement) {\r\n      this.remoteVideo.nativeElement.volume = this.isSpeakerOn ? 1 : 0;\r\n    }\r\n\r\n    this.logger.debug('Speaker toggled', { on: this.isSpeakerOn });\r\n  }\r\n\r\n  // Obtenir le nom de l'autre participant\r\n  getOtherParticipantName(): string {\r\n    if (!this.activeCall) {\r\n      return '';\r\n    }\r\n\r\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\r\n    const currentUserId = localStorage.getItem('userId');\r\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\r\n\r\n    return isCurrentUserCaller\r\n      ? this.activeCall.recipient.username\r\n      : this.activeCall.caller.username;\r\n  }\r\n\r\n  // Obtenir l'avatar de l'autre participant\r\n  getOtherParticipantAvatar(): string {\r\n    if (!this.activeCall) {\r\n      return 'assets/images/default-avatar.png';\r\n    }\r\n\r\n    // Déterminer si l'utilisateur actuel est l'appelant ou le destinataire\r\n    const currentUserId = localStorage.getItem('userId');\r\n    const isCurrentUserCaller = this.activeCall.caller.id === currentUserId;\r\n\r\n    const avatar = isCurrentUserCaller\r\n      ? this.activeCall.recipient.image\r\n      : this.activeCall.caller.image;\r\n\r\n    return avatar || 'assets/images/default-avatar.png';\r\n  }\r\n\r\n  // Obtenir le statut de l'appel sous forme de texte\r\n  getCallStatusText(): string {\r\n    if (!this.activeCall) {\r\n      return '';\r\n    }\r\n\r\n    switch (this.activeCall.status) {\r\n      case CallStatus.RINGING:\r\n        return 'Appel en cours...';\r\n      case CallStatus.CONNECTED:\r\n        return this.callDuration;\r\n      case CallStatus.ENDED:\r\n        return 'Appel terminé';\r\n      case CallStatus.MISSED:\r\n        return 'Appel manqué';\r\n      case CallStatus.REJECTED:\r\n        return 'Appel rejeté';\r\n      case CallStatus.FAILED:\r\n        return \"Échec de l'appel\";\r\n      default:\r\n        return '';\r\n    }\r\n  }\r\n\r\n  // Vérifier si l'appel est connecté\r\n  isCallConnected(): boolean {\r\n    return this.activeCall?.status === CallStatus.CONNECTED;\r\n  }\r\n\r\n  // Vérifier si l'appel est en cours de sonnerie\r\n  isCallRinging(): boolean {\r\n    return this.activeCall?.status === CallStatus.RINGING;\r\n  }\r\n\r\n  // Vérifier si l'appel est un appel vidéo\r\n  isVideoCall(): boolean {\r\n    return (\r\n      this.activeCall?.type === CallType.VIDEO ||\r\n      this.activeCall?.type === CallType.VIDEO_ONLY\r\n    );\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Nettoyer les abonnements et le minuteur\r\n    this.stopCallTimer();\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n  }\r\n}\r\n", "<div\r\n  *ngIf=\"activeCall\"\r\n  class=\"fixed inset-0 z-50 bg-[#121212]/90 backdrop-blur-sm flex flex-col\"\r\n>\r\n  <!-- Appel vidéo -->\r\n  <div *ngIf=\"isVideoCall()\" class=\"flex-1 relative\">\r\n    <!-- Vidéo distante -->\r\n    <div class=\"absolute inset-0 overflow-hidden\">\r\n      <video\r\n        #remoteVideo\r\n        autoplay\r\n        playsinline\r\n        class=\"w-full h-full object-cover\"\r\n      ></video>\r\n\r\n      <!-- Overlay gradient -->\r\n      <div\r\n        class=\"absolute inset-0 bg-gradient-to-t from-[#121212]/80 to-transparent pointer-events-none\"\r\n      ></div>\r\n\r\n      <!-- Afficher l'avatar si la vidéo est désactivée -->\r\n      <div\r\n        *ngIf=\"!remoteVideo.srcObject\"\r\n        class=\"absolute inset-0 flex items-center justify-center bg-[#121212]/70\"\r\n      >\r\n        <div class=\"relative\">\r\n          <div\r\n            class=\"w-32 h-32 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9]\"\r\n          >\r\n            <img\r\n              [src]=\"getOtherParticipantAvatar()\"\r\n              [alt]=\"getOtherParticipantName()\"\r\n              class=\"w-full h-full object-cover\"\r\n            />\r\n          </div>\r\n\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md\"\r\n          ></div>\r\n\r\n          <!-- Animated rings -->\r\n          <div\r\n            class=\"absolute inset-0 border-2 border-[#4f5fad]/40 dark:border-[#6d78c9]/40 rounded-full animate-ping opacity-75\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Informations sur l'appel -->\r\n      <div\r\n        class=\"absolute top-6 left-0 right-0 flex justify-center pointer-events-none\"\r\n      >\r\n        <div\r\n          class=\"px-4 py-2 rounded-full bg-[#121212]/50 backdrop-blur-sm border border-[#2a2a2a] flex items-center space-x-2\"\r\n        >\r\n          <div\r\n            class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] animate-pulse\"\r\n          ></div>\r\n          <span class=\"text-white text-sm font-medium\">{{\r\n            getCallStatusText()\r\n          }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div\r\n        class=\"absolute bottom-24 left-0 right-0 text-center pointer-events-none\"\r\n      >\r\n        <h3\r\n          class=\"text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          {{ getOtherParticipantName() }}\r\n        </h3>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Vidéo locale -->\r\n    <div\r\n      class=\"absolute top-4 right-4 w-32 h-48 rounded-lg overflow-hidden shadow-lg border border-[#2a2a2a] z-10\"\r\n    >\r\n      <video\r\n        #localVideo\r\n        autoplay\r\n        playsinline\r\n        muted\r\n        class=\"w-full h-full object-cover\"\r\n      ></video>\r\n\r\n      <!-- Overlay gradient -->\r\n      <div\r\n        class=\"absolute inset-0 bg-gradient-to-t from-[#121212]/30 to-transparent pointer-events-none\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Appel audio -->\r\n  <div\r\n    *ngIf=\"!isVideoCall()\"\r\n    class=\"flex-1 flex flex-col items-center justify-center\"\r\n  >\r\n    <div class=\"relative mb-8\">\r\n      <div\r\n        class=\"w-32 h-32 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9]\"\r\n      >\r\n        <img\r\n          [src]=\"getOtherParticipantAvatar()\"\r\n          [alt]=\"getOtherParticipantName()\"\r\n          class=\"w-full h-full object-cover\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md\"\r\n      ></div>\r\n\r\n      <!-- Animated rings -->\r\n      <div\r\n        class=\"absolute -inset-4 border-2 border-[#4f5fad]/10 dark:border-[#6d78c9]/10 rounded-full animate-ping opacity-75\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute -inset-8 border border-[#4f5fad]/5 dark:border-[#6d78c9]/5 rounded-full animate-ping opacity-50 delay-300\"\r\n      ></div>\r\n    </div>\r\n\r\n    <h2\r\n      class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\r\n    >\r\n      {{ getOtherParticipantName() }}\r\n    </h2>\r\n\r\n    <div\r\n      class=\"px-4 py-2 rounded-full bg-[#1e1e1e]/80 backdrop-blur-sm border border-[#2a2a2a] flex items-center space-x-2 mb-8\"\r\n    >\r\n      <div\r\n        class=\"w-2 h-2 rounded-full bg-[#4f5fad] dark:bg-[#6d78c9] animate-pulse\"\r\n      ></div>\r\n      <span class=\"text-white text-sm font-medium\">{{\r\n        getCallStatusText()\r\n      }}</span>\r\n    </div>\r\n\r\n    <!-- Visualisation audio -->\r\n    <div class=\"flex items-center h-16 space-x-1 mb-8\">\r\n      <div\r\n        *ngFor=\"let i of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\"\r\n        class=\"w-1 bg-gradient-to-t from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n        [style.height.px]=\"10 + (i % 5) * 6\"\r\n        [style.animation-delay.ms]=\"i * 100\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Contrôles d'appel -->\r\n  <div class=\"p-6 flex justify-center space-x-4\">\r\n    <button\r\n      (click)=\"toggleMicrophone()\"\r\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\r\n      [ngClass]=\"{\r\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': !isAudioMuted,\r\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': isAudioMuted\r\n      }\"\r\n    >\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\r\n        [ngClass]=\"{\r\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': !isAudioMuted,\r\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\r\n            isAudioMuted\r\n        }\"\r\n      ></div>\r\n      <i\r\n        class=\"fas text-lg relative z-10\"\r\n        [ngClass]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\r\n      ></i>\r\n    </button>\r\n\r\n    <button\r\n      *ngIf=\"isVideoCall()\"\r\n      (click)=\"toggleCamera()\"\r\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\r\n      [ngClass]=\"{\r\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': !isVideoMuted,\r\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': isVideoMuted\r\n      }\"\r\n    >\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\r\n        [ngClass]=\"{\r\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': !isVideoMuted,\r\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\r\n            isVideoMuted\r\n        }\"\r\n      ></div>\r\n      <i\r\n        class=\"fas text-lg relative z-10\"\r\n        [ngClass]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"\r\n      ></i>\r\n    </button>\r\n\r\n    <button\r\n      (click)=\"toggleSpeaker()\"\r\n      class=\"w-14 h-14 rounded-full flex items-center justify-center relative group\"\r\n      [ngClass]=\"{\r\n        'bg-[#4f5fad] dark:bg-[#6d78c9] text-white': isSpeakerOn,\r\n        'bg-[#1e1e1e] text-[#6d6870] border border-[#2a2a2a]': !isSpeakerOn\r\n      }\"\r\n    >\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 rounded-full blur-md transition-opacity\"\r\n        [ngClass]=\"{\r\n          'opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50': isSpeakerOn,\r\n          'opacity-0 group-hover:opacity-30 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30':\r\n            !isSpeakerOn\r\n        }\"\r\n      ></div>\r\n      <i\r\n        class=\"fas text-lg relative z-10\"\r\n        [ngClass]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'\"\r\n      ></i>\r\n    </button>\r\n\r\n    <button\r\n      (click)=\"endCall()\"\r\n      class=\"w-14 h-14 rounded-full flex items-center justify-center bg-gradient-to-r from-[#ff6b69] to-[#ff8785] text-white relative group\"\r\n    >\r\n      <!-- Glow effect -->\r\n      <div\r\n        class=\"absolute inset-0 bg-[#ff6b69]/50 rounded-full blur-md opacity-70 group-hover:opacity-100 transition-opacity\"\r\n      ></div>\r\n      <i class=\"fas fa-phone-slash text-lg relative z-10\"></i>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<style>\r\n  @keyframes audio-visualizer {\r\n    0%,\r\n    100% {\r\n      height: var(--min-height, 10px);\r\n    }\r\n    50% {\r\n      height: var(--max-height, 40px);\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": "AASA,SAAeA,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;;;ICYjEC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,cAEO;IAMTF,EAAA,CAAAG,YAAA,EAAM;;;;IAfAH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,UAAA,QAAAC,MAAA,CAAAC,yBAAA,IAAAP,EAAA,CAAAQ,aAAA,CAAmC,QAAAF,MAAA,CAAAG,uBAAA;;;;;IAzB/CT,EAAA,CAAAC,cAAA,cAAmD;IAG/CD,EAAA,CAAAE,SAAA,oBAKS;IAQTF,EAAA,CAAAU,UAAA,IAAAC,8CAAA,kBAyBM;IAGNX,EAAA,CAAAC,cAAA,cAEC;IAIGD,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAY,MAAA,IAE3C;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAIbH,EAAA,CAAAC,cAAA,eAEC;IAIGD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAKTH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,qBAMS;IAMXF,EAAA,CAAAG,YAAA,EAAM;;;;;IArEDH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,UAAA,UAAAQ,GAAA,CAAAC,SAAA,CAA4B;IAoCkBd,EAAA,CAAAI,SAAA,GAE3C;IAF2CJ,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,GAE3C;IAUFjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAF,MAAA,CAAAP,uBAAA,QACF;;;;;IAwEFT,EAAA,CAAAE,SAAA,cAKO;;;;IAFLF,EAAA,CAAAmB,WAAA,gBAAAC,IAAA,eAAoC,oBAAAA,IAAA;;;;;;;;IAnD1CpB,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,cAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAE,SAAA,cAEO;IASTF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAEC;IACCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,SAAA,eAEO;IACPF,EAAA,CAAAC,cAAA,gBAA6C;IAAAD,EAAA,CAAAY,MAAA,IAE3C;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAIXH,EAAA,CAAAC,cAAA,eAAmD;IACjDD,EAAA,CAAAU,UAAA,KAAAW,+CAAA,kBAKO;IACTrB,EAAA,CAAAG,YAAA,EAAM;;;;IA7CAH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,UAAA,QAAAiB,MAAA,CAAAf,yBAAA,IAAAP,EAAA,CAAAQ,aAAA,CAAmC,QAAAc,MAAA,CAAAb,uBAAA;IAuBvCT,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAI,MAAA,CAAAb,uBAAA,QACF;IAQ+CT,EAAA,CAAAI,SAAA,GAE3C;IAF2CJ,EAAA,CAAAe,iBAAA,CAAAO,MAAA,CAAAL,iBAAA,GAE3C;IAMcjB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAsD;;;;;;;;;;;;;;;;;;IAiCxExB,EAAA,CAAAC,cAAA,gBAQC;IANCD,EAAA,CAAAyB,UAAA,mBAAAC,oEAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAQxBhC,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;;;;IAlBPH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAGE;IAKApC,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAI,GAAA,GAAAF,MAAA,CAAAC,YAAA,EAAAD,MAAA,CAAAC,YAAA,EAIE;IAIFpC,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,UAAA,YAAA8B,MAAA,CAAAC,YAAA,iCAAwD;;;;;;IArMhEpC,EAAA,CAAAC,cAAA,aAGC;IAECD,EAAA,CAAAU,UAAA,IAAA4B,wCAAA,kBAuFM;IAGNtC,EAAA,CAAAU,UAAA,IAAA6B,wCAAA,kBAuDM;IAGNvC,EAAA,CAAAC,cAAA,aAA+C;IAE3CD,EAAA,CAAAyB,UAAA,mBAAAe,2DAAA;MAAAxC,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAW,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAQ5B3C,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAU,UAAA,IAAAkC,2CAAA,oBAsBS;IAET5C,EAAA,CAAAC,cAAA,gBAOC;IANCD,EAAA,CAAAyB,UAAA,mBAAAoB,2DAAA;MAAA7C,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAK,OAAA,GAAA9C,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAe,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAQzB/C,EAAA,CAAAE,SAAA,aAOO;IAKTF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAyB,UAAA,mBAAAuB,4DAAA;MAAAhD,EAAA,CAAA2B,aAAA,CAAAc,IAAA;MAAA,MAAAQ,OAAA,GAAAjD,EAAA,CAAA8B,aAAA;MAAA,OAAS9B,EAAA,CAAA+B,WAAA,CAAAkB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAInBlD,EAAA,CAAAE,SAAA,eAEO;IAETF,EAAA,CAAAG,YAAA,EAAS;;;;IApOLH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA8C,MAAA,CAAAC,WAAA,GAAmB;IA2FtBpD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,UAAA8C,MAAA,CAAAC,WAAA,GAAoB;IA6DnBpD,EAAA,CAAAI,SAAA,GAGE;IAHFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,IAAAC,GAAA,GAAAiB,MAAA,CAAAE,YAAA,EAAAF,MAAA,CAAAE,YAAA,EAGE;IAKArD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAI,GAAA,GAAAc,MAAA,CAAAE,YAAA,EAAAF,MAAA,CAAAE,YAAA,EAIE;IAIFrD,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,UAAA,YAAA8C,MAAA,CAAAE,YAAA,2CAAkE;IAKnErD,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA8C,MAAA,CAAAC,WAAA,GAAmB;IA0BpBpD,EAAA,CAAAI,SAAA,GAGE;IAHFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAC,GAAA,EAAAiB,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAG,WAAA,EAGE;IAKAtD,EAAA,CAAAI,SAAA,GAIE;IAJFJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAiC,eAAA,KAAAI,GAAA,EAAAc,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAG,WAAA,EAIE;IAIFtD,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,UAAA,YAAA8C,MAAA,CAAAG,WAAA,qCAA2D;;;AD1MnE,OAAM,MAAOC,mBAAmB;EAkB9BC,YACUC,cAA8B,EAC9BC,MAAqB;IADrB,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAhBhB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAP,YAAY,GAAY,KAAK;IAC7B,KAAAjB,YAAY,GAAY,KAAK;IAC7B,KAAAkB,WAAW,GAAY,IAAI;IAGnB,KAAAO,aAAa,GAAgB,IAAI;IACjC,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAhE,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,UAAU,GAAGA,UAAU;EAKpB;EAEHgE,QAAQA,CAAA;IACN;IACA,MAAMC,aAAa,GAAG,IAAI,CAACP,cAAc,CAACQ,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MACvE,MAAMC,YAAY,GAAG,IAAI,CAACT,UAAU;MACpC,IAAI,CAACA,UAAU,GAAGQ,IAAI;MAEtB,IAAIA,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAKtE,UAAU,CAACuE,SAAS,EAAE;QAChD,IAAI,CAACF,YAAY,IAAIA,YAAY,CAACG,EAAE,KAAKJ,IAAI,CAACI,EAAE,EAAE;UAChD;UACA,IAAI,CAACC,cAAc,EAAE;;OAExB,MAAM,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAKtE,UAAU,CAACuE,SAAS,EAAE;QACxD;QACA,IAAI,CAACG,aAAa,EAAE;;IAExB,CAAC,CAAC;IAEF,IAAI,CAACX,aAAa,CAACY,IAAI,CAACV,aAAa,CAAC;IAEtC;IACA,MAAMW,cAAc,GAAG,IAAI,CAAClB,cAAc,CAACmB,YAAY,CAACV,SAAS,CAC9DW,MAAM,IAAI;MACT,IAAIA,MAAM,IAAI,IAAI,CAACC,UAAU,EAAEC,aAAa,EAAE;QAC5C,IAAI,CAACD,UAAU,CAACC,aAAa,CAACjE,SAAS,GAAG+D,MAAM;;IAEpD,CAAC,CACF;IAED,IAAI,CAACf,aAAa,CAACY,IAAI,CAACC,cAAc,CAAC;IAEvC;IACA,MAAMK,eAAe,GAAG,IAAI,CAACvB,cAAc,CAACwB,aAAa,CAACf,SAAS,CAChEW,MAAM,IAAI;MACT,IAAIA,MAAM,IAAI,IAAI,CAACK,WAAW,EAAEH,aAAa,EAAE;QAC7C,IAAI,CAACG,WAAW,CAACH,aAAa,CAACjE,SAAS,GAAG+D,MAAM;;IAErD,CAAC,CACF;IAED,IAAI,CAACf,aAAa,CAACY,IAAI,CAACM,eAAe,CAAC;EAC1C;EAEAG,eAAeA,CAAA;IACb;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC3B,cAAc,CAACmB,YAAY,CAACS,QAAQ,EAAE;IAC/D,IAAID,WAAW,IAAI,IAAI,CAACN,UAAU,EAAEC,aAAa,EAAE;MACjD,IAAI,CAACD,UAAU,CAACC,aAAa,CAACjE,SAAS,GAAGsE,WAAW;;IAGvD,MAAME,YAAY,GAAG,IAAI,CAAC7B,cAAc,CAACwB,aAAa,CAACI,QAAQ,EAAE;IACjE,IAAIC,YAAY,IAAI,IAAI,CAACJ,WAAW,EAAEH,aAAa,EAAE;MACnD,IAAI,CAACG,WAAW,CAACH,aAAa,CAACjE,SAAS,GAAGwE,YAAY;;EAE3D;EAEA;EACQd,cAAcA,CAAA;IACpB,IAAI,CAACX,aAAa,GAAG,IAAI0B,IAAI,EAAE;IAC/B,IAAI,CAACd,aAAa,EAAE,CAAC,CAAC;IAEtB,IAAI,CAACe,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,CAAC,IAAI,CAAC5B,aAAa,EAAE;MAEzB,MAAM6B,GAAG,GAAG,IAAIH,IAAI,EAAE;MACtB,MAAMI,IAAI,GAAGC,IAAI,CAACC,KAAK,CACrB,CAACH,GAAG,CAACI,OAAO,EAAE,GAAG,IAAI,CAACjC,aAAa,CAACiC,OAAO,EAAE,IAAI,IAAI,CACtD;MAED,MAAMC,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC,CAClCK,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,CAACP,IAAI,GAAG,EAAE,EAAEK,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEvD,IAAI,CAACrC,YAAY,GAAG,GAAGmC,OAAO,IAAIG,OAAO,EAAE;IAC7C,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQzB,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACe,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;EAEhC;EAEA;EACAtC,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACS,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACD,MAAM,CAAC0C,KAAK,CAAC,aAAa,EAAE;MAAEC,MAAM,EAAE,IAAI,CAAC1C,UAAU,CAACY;IAAE,CAAE,CAAC;IAEhE,IAAI,CAACd,cAAc,CAACP,OAAO,CAAC,IAAI,CAACS,UAAU,CAACY,EAAE,CAAC,CAACL,SAAS,CAAC;MACxDoC,IAAI,EAAGnC,IAAI,IAAI;QACb,IAAI,CAACT,MAAM,CAAC0C,KAAK,CAAC,yBAAyB,EAAE;UAC3CC,MAAM,EAAElC,IAAI,CAACI,EAAE;UACfgC,QAAQ,EAAEpC,IAAI,CAACoC;SAChB,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACA7D,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACgB,UAAU,EAAE;MACpB;;IAGF,IAAI,CAACN,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACI,cAAc,CAChBgD,WAAW,CAAC,IAAI,CAAC9C,UAAU,CAACY,EAAE,EAAEmC,SAAS,EAAE,CAAC,IAAI,CAACrD,YAAY,CAAC,CAC9Da,SAAS,CAAC;MACToC,IAAI,EAAGK,MAAM,IAAI;QACf,IAAI,CAACjD,MAAM,CAAC0C,KAAK,CAAC,oBAAoB,EAAE;UAAEQ,KAAK,EAAE,IAAI,CAACvD;QAAY,CAAE,CAAC;MACvE,CAAC;MACDmD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACnD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACxC;KACD,CAAC;EACN;EAEA;EACArB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC2B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACkD,IAAI,KAAK/G,QAAQ,CAACgH,KAAK,EAAE;MAC/D;;IAGF,IAAI,CAAC1E,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,CAACqB,cAAc,CAChBgD,WAAW,CAAC,IAAI,CAAC9C,UAAU,CAACY,EAAE,EAAE,CAAC,IAAI,CAACnC,YAAY,EAAEsE,SAAS,CAAC,CAC9DxC,SAAS,CAAC;MACToC,IAAI,EAAGK,MAAM,IAAI;QACf,IAAI,CAACjD,MAAM,CAAC0C,KAAK,CAAC,gBAAgB,EAAE;UAAEQ,KAAK,EAAE,IAAI,CAACxE;QAAY,CAAE,CAAC;MACnE,CAAC;MACDoE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD;QACA,IAAI,CAACpE,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACxC;KACD,CAAC;EACN;EAEA;EACAW,aAAaA,CAAA;IACX,IAAI,CAACO,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,IAAI,CAAC4B,WAAW,EAAEH,aAAa,EAAE;MACnC,IAAI,CAACG,WAAW,CAACH,aAAa,CAACgC,MAAM,GAAG,IAAI,CAACzD,WAAW,GAAG,CAAC,GAAG,CAAC;;IAGlE,IAAI,CAACI,MAAM,CAAC0C,KAAK,CAAC,iBAAiB,EAAE;MAAEY,EAAE,EAAE,IAAI,CAAC1D;IAAW,CAAE,CAAC;EAChE;EAEA;EACA7C,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACkD,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX;IACA,MAAMsD,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACzD,UAAU,CAAC0D,MAAM,CAAC9C,EAAE,KAAK0C,aAAa;IAEvE,OAAOG,mBAAmB,GACtB,IAAI,CAACzD,UAAU,CAAC2D,SAAS,CAACC,QAAQ,GAClC,IAAI,CAAC5D,UAAU,CAAC0D,MAAM,CAACE,QAAQ;EACrC;EAEA;EACAhH,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACoD,UAAU,EAAE;MACpB,OAAO,kCAAkC;;IAG3C;IACA,MAAMsD,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpD,MAAMC,mBAAmB,GAAG,IAAI,CAACzD,UAAU,CAAC0D,MAAM,CAAC9C,EAAE,KAAK0C,aAAa;IAEvE,MAAMO,MAAM,GAAGJ,mBAAmB,GAC9B,IAAI,CAACzD,UAAU,CAAC2D,SAAS,CAACG,KAAK,GAC/B,IAAI,CAAC9D,UAAU,CAAC0D,MAAM,CAACI,KAAK;IAEhC,OAAOD,MAAM,IAAI,kCAAkC;EACrD;EAEA;EACAvG,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC0C,UAAU,EAAE;MACpB,OAAO,EAAE;;IAGX,QAAQ,IAAI,CAACA,UAAU,CAACU,MAAM;MAC5B,KAAKtE,UAAU,CAAC2H,OAAO;QACrB,OAAO,mBAAmB;MAC5B,KAAK3H,UAAU,CAACuE,SAAS;QACvB,OAAO,IAAI,CAACV,YAAY;MAC1B,KAAK7D,UAAU,CAAC4H,KAAK;QACnB,OAAO,eAAe;MACxB,KAAK5H,UAAU,CAAC6H,MAAM;QACpB,OAAO,cAAc;MACvB,KAAK7H,UAAU,CAAC8H,QAAQ;QACtB,OAAO,cAAc;MACvB,KAAK9H,UAAU,CAAC+H,MAAM;QACpB,OAAO,kBAAkB;MAC3B;QACE,OAAO,EAAE;;EAEf;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpE,UAAU,EAAEU,MAAM,KAAKtE,UAAU,CAACuE,SAAS;EACzD;EAEA;EACA0D,aAAaA,CAAA;IACX,OAAO,IAAI,CAACrE,UAAU,EAAEU,MAAM,KAAKtE,UAAU,CAAC2H,OAAO;EACvD;EAEA;EACAtE,WAAWA,CAAA;IACT,OACE,IAAI,CAACO,UAAU,EAAEkD,IAAI,KAAK/G,QAAQ,CAACmI,KAAK,IACxC,IAAI,CAACtE,UAAU,EAAEkD,IAAI,KAAK/G,QAAQ,CAACoI,UAAU;EAEjD;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAAC1D,aAAa,EAAE;IACpB,IAAI,CAACX,aAAa,CAACsE,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBAtQW/E,mBAAmB,EAAAvD,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzI,EAAA,CAAAuI,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnBpF,mBAAmB;MAAAqF,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UClBhC/I,EAAA,CAAAU,UAAA,IAAAuI,kCAAA,mBA2OM;;;UA1OHjJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAArF,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}