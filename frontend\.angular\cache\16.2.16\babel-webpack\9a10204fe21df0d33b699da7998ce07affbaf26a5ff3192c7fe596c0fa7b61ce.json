{"ast": null, "code": "import { isNonNullObject } from \"./objects.js\";\nexport function deepFreeze(value) {\n  var workSet = new Set([value]);\n  workSet.forEach(function (obj) {\n    if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n      Object.getOwnPropertyNames(obj).forEach(function (name) {\n        if (isNonNullObject(obj[name])) workSet.add(obj[name]);\n      });\n    }\n  });\n  return value;\n}\nfunction shallowFreeze(obj) {\n  if (globalThis.__DEV__ !== false && !Object.isFrozen(obj)) {\n    try {\n      Object.freeze(obj);\n    } catch (e) {\n      // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n      // they all throw a TypeError when you try, so we re-throw any exceptions\n      // that are not TypeErrors, since that would be unexpected.\n      if (e instanceof TypeError) return null;\n      throw e;\n    }\n  }\n  return obj;\n}\nexport function maybeDeepFreeze(obj) {\n  if (globalThis.__DEV__ !== false) {\n    deepFreeze(obj);\n  }\n  return obj;\n}", "map": {"version": 3, "names": ["isNonNullObject", "deepFreeze", "value", "workSet", "Set", "for<PERSON>ach", "obj", "shallowFreeze", "Object", "getOwnPropertyNames", "name", "add", "globalThis", "__DEV__", "isFrozen", "freeze", "e", "TypeError", "maybeDeepFreeze"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/maybeDeepFreeze.js"], "sourcesContent": ["import { isNonNullObject } from \"./objects.js\";\nexport function deepFreeze(value) {\n    var workSet = new Set([value]);\n    workSet.forEach(function (obj) {\n        if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n            Object.getOwnPropertyNames(obj).forEach(function (name) {\n                if (isNonNullObject(obj[name]))\n                    workSet.add(obj[name]);\n            });\n        }\n    });\n    return value;\n}\nfunction shallowFreeze(obj) {\n    if (globalThis.__DEV__ !== false && !Object.isFrozen(obj)) {\n        try {\n            Object.freeze(obj);\n        }\n        catch (e) {\n            // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n            // they all throw a TypeError when you try, so we re-throw any exceptions\n            // that are not TypeErrors, since that would be unexpected.\n            if (e instanceof TypeError)\n                return null;\n            throw e;\n        }\n    }\n    return obj;\n}\nexport function maybeDeepFreeze(obj) {\n    if (globalThis.__DEV__ !== false) {\n        deepFreeze(obj);\n    }\n    return obj;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,cAAc;AAC9C,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAACF,KAAK,CAAC,CAAC;EAC9BC,OAAO,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3B,IAAIN,eAAe,CAACM,GAAG,CAAC,IAAIC,aAAa,CAACD,GAAG,CAAC,KAAKA,GAAG,EAAE;MACpDE,MAAM,CAACC,mBAAmB,CAACH,GAAG,CAAC,CAACD,OAAO,CAAC,UAAUK,IAAI,EAAE;QACpD,IAAIV,eAAe,CAACM,GAAG,CAACI,IAAI,CAAC,CAAC,EAC1BP,OAAO,CAACQ,GAAG,CAACL,GAAG,CAACI,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAOR,KAAK;AAChB;AACA,SAASK,aAAaA,CAACD,GAAG,EAAE;EACxB,IAAIM,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI,CAACL,MAAM,CAACM,QAAQ,CAACR,GAAG,CAAC,EAAE;IACvD,IAAI;MACAE,MAAM,CAACO,MAAM,CAACT,GAAG,CAAC;IACtB,CAAC,CACD,OAAOU,CAAC,EAAE;MACN;MACA;MACA;MACA,IAAIA,CAAC,YAAYC,SAAS,EACtB,OAAO,IAAI;MACf,MAAMD,CAAC;IACX;EACJ;EACA,OAAOV,GAAG;AACd;AACA,OAAO,SAASY,eAAeA,CAACZ,GAAG,EAAE;EACjC,IAAIM,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;IAC9BZ,UAAU,CAACK,GAAG,CAAC;EACnB;EACA,OAAOA,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}