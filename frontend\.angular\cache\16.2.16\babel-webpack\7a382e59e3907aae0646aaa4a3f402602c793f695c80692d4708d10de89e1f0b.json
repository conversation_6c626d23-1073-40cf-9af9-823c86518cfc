{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isTypeDefinitionNode, isTypeSystemDefinitionNode, isTypeSystemExtensionNode } from '../../language/predicates.mjs';\nimport { introspectionTypes } from '../../type/introspection.mjs';\nimport { specifiedScalarTypes } from '../../type/scalars.mjs';\n\n/**\n * Known type names\n *\n * A GraphQL document is only valid if referenced types (specifically\n * variable definitions and fragment conditions) are defined by the type schema.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Spread-Type-Existence\n */\nexport function KnownTypeNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypesMap = schema ? schema.getTypeMap() : Object.create(null);\n  const definedTypes = Object.create(null);\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = true;\n    }\n  }\n  const typeNames = [...Object.keys(existingTypesMap), ...Object.keys(definedTypes)];\n  return {\n    NamedType(node, _1, parent, _2, ancestors) {\n      const typeName = node.name.value;\n      if (!existingTypesMap[typeName] && !definedTypes[typeName]) {\n        var _ancestors$;\n        const definitionNode = (_ancestors$ = ancestors[2]) !== null && _ancestors$ !== void 0 ? _ancestors$ : parent;\n        const isSDL = definitionNode != null && isSDLNode(definitionNode);\n        if (isSDL && standardTypeNames.includes(typeName)) {\n          return;\n        }\n        const suggestedTypes = suggestionList(typeName, isSDL ? standardTypeNames.concat(typeNames) : typeNames);\n        context.reportError(new GraphQLError(`Unknown type \"${typeName}\".` + didYouMean(suggestedTypes), {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nconst standardTypeNames = [...specifiedScalarTypes, ...introspectionTypes].map(type => type.name);\nfunction isSDLNode(value) {\n  return 'kind' in value && (isTypeSystemDefinitionNode(value) || isTypeSystemExtensionNode(value));\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "suggestionList", "GraphQLError", "isTypeDefinitionNode", "isTypeSystemDefinitionNode", "isTypeSystemExtensionNode", "introspectionTypes", "specifiedScalarTypes", "KnownTypeNamesRule", "context", "schema", "getSchema", "existingTypesMap", "getTypeMap", "Object", "create", "definedTypes", "def", "getDocument", "definitions", "name", "value", "typeNames", "keys", "NamedType", "node", "_1", "parent", "_2", "ancestors", "typeName", "_ancestors$", "definitionNode", "isSDL", "isSDLNode", "standardTypeNames", "includes", "suggestedTypes", "concat", "reportError", "nodes", "map", "type"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/graphql/validation/rules/KnownTypeNamesRule.mjs"], "sourcesContent": ["import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport {\n  isTypeDefinitionNode,\n  isTypeSystemDefinitionNode,\n  isTypeSystemExtensionNode,\n} from '../../language/predicates.mjs';\nimport { introspectionTypes } from '../../type/introspection.mjs';\nimport { specifiedScalarTypes } from '../../type/scalars.mjs';\n\n/**\n * Known type names\n *\n * A GraphQL document is only valid if referenced types (specifically\n * variable definitions and fragment conditions) are defined by the type schema.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Spread-Type-Existence\n */\nexport function KnownTypeNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypesMap = schema ? schema.getTypeMap() : Object.create(null);\n  const definedTypes = Object.create(null);\n\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = true;\n    }\n  }\n\n  const typeNames = [\n    ...Object.keys(existingTypesMap),\n    ...Object.keys(definedTypes),\n  ];\n  return {\n    NamedType(node, _1, parent, _2, ancestors) {\n      const typeName = node.name.value;\n\n      if (!existingTypesMap[typeName] && !definedTypes[typeName]) {\n        var _ancestors$;\n\n        const definitionNode =\n          (_ancestors$ = ancestors[2]) !== null && _ancestors$ !== void 0\n            ? _ancestors$\n            : parent;\n        const isSDL = definitionNode != null && isSDLNode(definitionNode);\n\n        if (isSDL && standardTypeNames.includes(typeName)) {\n          return;\n        }\n\n        const suggestedTypes = suggestionList(\n          typeName,\n          isSDL ? standardTypeNames.concat(typeNames) : typeNames,\n        );\n        context.reportError(\n          new GraphQLError(\n            `Unknown type \"${typeName}\".` + didYouMean(suggestedTypes),\n            {\n              nodes: node,\n            },\n          ),\n        );\n      }\n    },\n  };\n}\nconst standardTypeNames = [...specifiedScalarTypes, ...introspectionTypes].map(\n  (type) => type.name,\n);\n\nfunction isSDLNode(value) {\n  return (\n    'kind' in value &&\n    (isTypeSystemDefinitionNode(value) || isTypeSystemExtensionNode(value))\n  );\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,QACpB,+BAA+B;AACtC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,oBAAoB,QAAQ,wBAAwB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAC1C,MAAMC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAClC,MAAMC,gBAAgB,GAAGF,MAAM,GAAGA,MAAM,CAACG,UAAU,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC3E,MAAMC,YAAY,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAExC,KAAK,MAAME,GAAG,IAAIR,OAAO,CAACS,WAAW,CAAC,CAAC,CAACC,WAAW,EAAE;IACnD,IAAIhB,oBAAoB,CAACc,GAAG,CAAC,EAAE;MAC7BD,YAAY,CAACC,GAAG,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;IACrC;EACF;EAEA,MAAMC,SAAS,GAAG,CAChB,GAAGR,MAAM,CAACS,IAAI,CAACX,gBAAgB,CAAC,EAChC,GAAGE,MAAM,CAACS,IAAI,CAACP,YAAY,CAAC,CAC7B;EACD,OAAO;IACLQ,SAASA,CAACC,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,EAAE,EAAEC,SAAS,EAAE;MACzC,MAAMC,QAAQ,GAAGL,IAAI,CAACL,IAAI,CAACC,KAAK;MAEhC,IAAI,CAACT,gBAAgB,CAACkB,QAAQ,CAAC,IAAI,CAACd,YAAY,CAACc,QAAQ,CAAC,EAAE;QAC1D,IAAIC,WAAW;QAEf,MAAMC,cAAc,GAClB,CAACD,WAAW,GAAGF,SAAS,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIE,WAAW,KAAK,KAAK,CAAC,GAC3DA,WAAW,GACXJ,MAAM;QACZ,MAAMM,KAAK,GAAGD,cAAc,IAAI,IAAI,IAAIE,SAAS,CAACF,cAAc,CAAC;QAEjE,IAAIC,KAAK,IAAIE,iBAAiB,CAACC,QAAQ,CAACN,QAAQ,CAAC,EAAE;UACjD;QACF;QAEA,MAAMO,cAAc,GAAGpC,cAAc,CACnC6B,QAAQ,EACRG,KAAK,GAAGE,iBAAiB,CAACG,MAAM,CAAChB,SAAS,CAAC,GAAGA,SAChD,CAAC;QACDb,OAAO,CAAC8B,WAAW,CACjB,IAAIrC,YAAY,CACb,iBAAgB4B,QAAS,IAAG,GAAG9B,UAAU,CAACqC,cAAc,CAAC,EAC1D;UACEG,KAAK,EAAEf;QACT,CACF,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH;AACA,MAAMU,iBAAiB,GAAG,CAAC,GAAG5B,oBAAoB,EAAE,GAAGD,kBAAkB,CAAC,CAACmC,GAAG,CAC3EC,IAAI,IAAKA,IAAI,CAACtB,IACjB,CAAC;AAED,SAASc,SAASA,CAACb,KAAK,EAAE;EACxB,OACE,MAAM,IAAIA,KAAK,KACdjB,0BAA0B,CAACiB,KAAK,CAAC,IAAIhB,yBAAyB,CAACgB,KAAK,CAAC,CAAC;AAE3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}