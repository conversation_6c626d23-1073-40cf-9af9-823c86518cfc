{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { cloneDeep, compact, getOperationDefinition, Observable, iterateObserversSafely, fixObservableSubclass, getQueryDefinition, preventUnhandledRejection } from \"../utilities/index.js\";\nimport { ApolloError, isApolloError } from \"../errors/index.js\";\nimport { equalByQuery } from \"./equalByQuery.js\";\nimport { Slot } from \"optimism\";\nvar assign = Object.assign,\n  hasOwnProperty = Object.hasOwnProperty;\nvar ObservableQuery = /** @class */function (_super) {\n  __extends(ObservableQuery, _super);\n  function ObservableQuery(_a) {\n    var queryManager = _a.queryManager,\n      queryInfo = _a.queryInfo,\n      options = _a.options;\n    var _this = this;\n    var startedInactive = ObservableQuery.inactiveOnCreation.getValue();\n    _this = _super.call(this, function (observer) {\n      if (startedInactive) {\n        queryManager[\"queries\"].set(_this.queryId, queryInfo);\n        startedInactive = false;\n      }\n      // Zen Observable has its own error function, so in order to log correctly\n      // we need to provide a custom error callback.\n      try {\n        var subObserver = observer._subscription._observer;\n        if (subObserver && !subObserver.error) {\n          subObserver.error = defaultSubscriptionObserverErrorCallback;\n        }\n      } catch (_a) {}\n      var first = !_this.observers.size;\n      _this.observers.add(observer);\n      // Deliver most recent error or result.\n      var last = _this.last;\n      if (last && last.error) {\n        observer.error && observer.error(last.error);\n      } else if (last && last.result) {\n        observer.next && observer.next(_this.maskResult(last.result));\n      }\n      // Initiate observation of this query if it hasn't been reported to\n      // the QueryManager yet.\n      if (first) {\n        // Blindly catching here prevents unhandled promise rejections,\n        // and is safe because the ObservableQuery handles this error with\n        // this.observer.error, so we're not just swallowing the error by\n        // ignoring it here.\n        _this.reobserve().catch(function () {});\n      }\n      return function () {\n        if (_this.observers.delete(observer) && !_this.observers.size) {\n          _this.tearDownQuery();\n        }\n      };\n    }) || this;\n    _this.observers = new Set();\n    _this.subscriptions = new Set();\n    _this.dirty = false;\n    // related classes\n    _this.queryInfo = queryInfo;\n    _this.queryManager = queryManager;\n    // active state\n    _this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy);\n    _this.isTornDown = false;\n    _this.subscribeToMore = _this.subscribeToMore.bind(_this);\n    _this.maskResult = _this.maskResult.bind(_this);\n    var _b = queryManager.defaultOptions.watchQuery,\n      _c = _b === void 0 ? {} : _b,\n      _d = _c.fetchPolicy,\n      defaultFetchPolicy = _d === void 0 ? \"cache-first\" : _d;\n    var _e = options.fetchPolicy,\n      fetchPolicy = _e === void 0 ? defaultFetchPolicy : _e,\n      // Make sure we don't store \"standby\" as the initialFetchPolicy.\n      _f = options.initialFetchPolicy,\n      // Make sure we don't store \"standby\" as the initialFetchPolicy.\n      initialFetchPolicy = _f === void 0 ? fetchPolicy === \"standby\" ? defaultFetchPolicy : fetchPolicy : _f;\n    _this.options = __assign(__assign({}, options), {\n      // Remember the initial options.fetchPolicy so we can revert back to this\n      // policy when variables change. This information can also be specified\n      // (or overridden) by providing options.initialFetchPolicy explicitly.\n      initialFetchPolicy: initialFetchPolicy,\n      // This ensures this.options.fetchPolicy always has a string value, in\n      // case options.fetchPolicy was not provided.\n      fetchPolicy: fetchPolicy\n    });\n    _this.queryId = queryInfo.queryId || queryManager.generateQueryId();\n    var opDef = getOperationDefinition(_this.query);\n    _this.queryName = opDef && opDef.name && opDef.name.value;\n    return _this;\n  }\n  Object.defineProperty(ObservableQuery.prototype, \"query\", {\n    // The `query` computed property will always reflect the document transformed\n    // by the last run query. `this.options.query` will always reflect the raw\n    // untransformed query to ensure document transforms with runtime conditionals\n    // are run on the original document.\n    get: function () {\n      return this.lastQuery || this.options.query;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(ObservableQuery.prototype, \"variables\", {\n    // Computed shorthand for this.options.variables, preserved for\n    // backwards compatibility.\n    /**\n     * An object containing the variables that were provided for the query.\n     */\n    get: function () {\n      return this.options.variables;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  ObservableQuery.prototype.result = function () {\n    var _this = this;\n    return new Promise(function (resolve, reject) {\n      // TODO: this code doesn’t actually make sense insofar as the observer\n      // will never exist in this.observers due how zen-observable wraps observables.\n      // https://github.com/zenparsing/zen-observable/blob/master/src/Observable.js#L169\n      var observer = {\n        next: function (result) {\n          resolve(result);\n          // Stop the query within the QueryManager if we can before\n          // this function returns.\n          //\n          // We do this in order to prevent observers piling up within\n          // the QueryManager. Notice that we only fully unsubscribe\n          // from the subscription in a setTimeout(..., 0)  call. This call can\n          // actually be handled by the browser at a much later time. If queries\n          // are fired in the meantime, observers that should have been removed\n          // from the QueryManager will continue to fire, causing an unnecessary\n          // performance hit.\n          _this.observers.delete(observer);\n          if (!_this.observers.size) {\n            _this.queryManager.removeQuery(_this.queryId);\n          }\n          setTimeout(function () {\n            subscription.unsubscribe();\n          }, 0);\n        },\n        error: reject\n      };\n      var subscription = _this.subscribe(observer);\n    });\n  };\n  /** @internal */\n  ObservableQuery.prototype.resetDiff = function () {\n    this.queryInfo.resetDiff();\n  };\n  ObservableQuery.prototype.getCurrentFullResult = function (saveAsLastResult) {\n    if (saveAsLastResult === void 0) {\n      saveAsLastResult = true;\n    }\n    // Use the last result as long as the variables match this.variables.\n    var lastResult = this.getLastResult(true);\n    var networkStatus = this.queryInfo.networkStatus || lastResult && lastResult.networkStatus || NetworkStatus.ready;\n    var result = __assign(__assign({}, lastResult), {\n      loading: isNetworkRequestInFlight(networkStatus),\n      networkStatus: networkStatus\n    });\n    var _a = this.options.fetchPolicy,\n      fetchPolicy = _a === void 0 ? \"cache-first\" : _a;\n    if (\n    // These fetch policies should never deliver data from the cache, unless\n    // redelivering a previously delivered result.\n    skipCacheDataFor(fetchPolicy) ||\n    // If this.options.query has @client(always: true) fields, we cannot\n    // trust diff.result, since it was read from the cache without running\n    // local resolvers (and it's too late to run resolvers now, since we must\n    // return a result synchronously).\n    this.queryManager.getDocumentInfo(this.query).hasForcedResolvers) {\n      // Fall through.\n    } else if (this.waitForOwnResult) {\n      // This would usually be a part of `QueryInfo.getDiff()`.\n      // which we skip in the waitForOwnResult case since we are not\n      // interested in the diff.\n      this.queryInfo[\"updateWatch\"]();\n    } else {\n      var diff = this.queryInfo.getDiff();\n      if (diff.complete || this.options.returnPartialData) {\n        result.data = diff.result;\n      }\n      if (equal(result.data, {})) {\n        result.data = void 0;\n      }\n      if (diff.complete) {\n        // Similar to setting result.partial to false, but taking advantage of the\n        // falsiness of missing fields.\n        delete result.partial;\n        // If the diff is complete, and we're using a FetchPolicy that\n        // terminates after a complete cache read, we can assume the next result\n        // we receive will have NetworkStatus.ready and !loading.\n        if (diff.complete && result.networkStatus === NetworkStatus.loading && (fetchPolicy === \"cache-first\" || fetchPolicy === \"cache-only\")) {\n          result.networkStatus = NetworkStatus.ready;\n          result.loading = false;\n        }\n      } else {\n        result.partial = true;\n      }\n      // We need to check for both both `error` and `errors` field because there\n      // are cases where sometimes `error` is set, but not `errors` and\n      // vice-versa. This will be updated in the next major version when\n      // `errors` is deprecated in favor of `error`.\n      if (result.networkStatus === NetworkStatus.ready && (result.error || result.errors)) {\n        result.networkStatus = NetworkStatus.error;\n      }\n      if (globalThis.__DEV__ !== false && !diff.complete && !this.options.partialRefetch && !result.loading && !result.data && !result.error) {\n        logMissingFieldErrors(diff.missing);\n      }\n    }\n    if (saveAsLastResult) {\n      this.updateLastResult(result);\n    }\n    return result;\n  };\n  ObservableQuery.prototype.getCurrentResult = function (saveAsLastResult) {\n    if (saveAsLastResult === void 0) {\n      saveAsLastResult = true;\n    }\n    return this.maskResult(this.getCurrentFullResult(saveAsLastResult));\n  };\n  // Compares newResult to the snapshot we took of this.lastResult when it was\n  // first received.\n  ObservableQuery.prototype.isDifferentFromLastResult = function (newResult, variables) {\n    if (!this.last) {\n      return true;\n    }\n    var documentInfo = this.queryManager.getDocumentInfo(this.query);\n    var dataMasking = this.queryManager.dataMasking;\n    var query = dataMasking ? documentInfo.nonReactiveQuery : this.query;\n    var resultIsDifferent = dataMasking || documentInfo.hasNonreactiveDirective ? !equalByQuery(query, this.last.result, newResult, this.variables) : !equal(this.last.result, newResult);\n    return resultIsDifferent || variables && !equal(this.last.variables, variables);\n  };\n  ObservableQuery.prototype.getLast = function (key, variablesMustMatch) {\n    var last = this.last;\n    if (last && last[key] && (!variablesMustMatch || equal(last.variables, this.variables))) {\n      return last[key];\n    }\n  };\n  ObservableQuery.prototype.getLastResult = function (variablesMustMatch) {\n    return this.getLast(\"result\", variablesMustMatch);\n  };\n  ObservableQuery.prototype.getLastError = function (variablesMustMatch) {\n    return this.getLast(\"error\", variablesMustMatch);\n  };\n  ObservableQuery.prototype.resetLastResults = function () {\n    delete this.last;\n    this.isTornDown = false;\n  };\n  ObservableQuery.prototype.resetQueryStoreErrors = function () {\n    this.queryManager.resetErrors(this.queryId);\n  };\n  /**\n   * Update the variables of this observable query, and fetch the new results.\n   * This method should be preferred over `setVariables` in most use cases.\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  ObservableQuery.prototype.refetch = function (variables) {\n    var _a;\n    var reobserveOptions = {\n      // Always disable polling for refetches.\n      pollInterval: 0\n    };\n    // Unless the provided fetchPolicy always consults the network\n    // (no-cache, network-only, or cache-and-network), override it with\n    // network-only to force the refetch for this fetchQuery call.\n    var fetchPolicy = this.options.fetchPolicy;\n    if (fetchPolicy === \"no-cache\") {\n      reobserveOptions.fetchPolicy = \"no-cache\";\n    } else {\n      reobserveOptions.fetchPolicy = \"network-only\";\n    }\n    if (globalThis.__DEV__ !== false && variables && hasOwnProperty.call(variables, \"variables\")) {\n      var queryDef = getQueryDefinition(this.query);\n      var vars = queryDef.variableDefinitions;\n      if (!vars || !vars.some(function (v) {\n        return v.variable.name.value === \"variables\";\n      })) {\n        globalThis.__DEV__ !== false && invariant.warn(21, variables, ((_a = queryDef.name) === null || _a === void 0 ? void 0 : _a.value) || queryDef);\n      }\n    }\n    if (variables && !equal(this.options.variables, variables)) {\n      // Update the existing options with new variables\n      reobserveOptions.variables = this.options.variables = __assign(__assign({}, this.options.variables), variables);\n    }\n    this.queryInfo.resetLastWrite();\n    return this.reobserve(reobserveOptions, NetworkStatus.refetch);\n  };\n  /**\n   * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).\n   */\n  ObservableQuery.prototype.fetchMore = function (fetchMoreOptions) {\n    var _this = this;\n    var combinedOptions = __assign(__assign({}, fetchMoreOptions.query ? fetchMoreOptions : __assign(__assign(__assign(__assign({}, this.options), {\n      query: this.options.query\n    }), fetchMoreOptions), {\n      variables: __assign(__assign({}, this.options.variables), fetchMoreOptions.variables)\n    })), {\n      // The fetchMore request goes immediately to the network and does\n      // not automatically write its result to the cache (hence no-cache\n      // instead of network-only), because we allow the caller of\n      // fetchMore to provide an updateQuery callback that determines how\n      // the data gets written to the cache.\n      fetchPolicy: \"no-cache\"\n    });\n    combinedOptions.query = this.transformDocument(combinedOptions.query);\n    var qid = this.queryManager.generateQueryId();\n    // If a temporary query is passed to `fetchMore`, we don't want to store\n    // it as the last query result since it may be an optimized query for\n    // pagination. We will however run the transforms on the original document\n    // as well as the document passed in `fetchMoreOptions` to ensure the cache\n    // uses the most up-to-date document which may rely on runtime conditionals.\n    this.lastQuery = fetchMoreOptions.query ? this.transformDocument(this.options.query) : combinedOptions.query;\n    // Simulate a loading result for the original query with\n    // result.networkStatus === NetworkStatus.fetchMore.\n    var queryInfo = this.queryInfo;\n    var originalNetworkStatus = queryInfo.networkStatus;\n    queryInfo.networkStatus = NetworkStatus.fetchMore;\n    if (combinedOptions.notifyOnNetworkStatusChange) {\n      this.observe();\n    }\n    var updatedQuerySet = new Set();\n    var updateQuery = fetchMoreOptions === null || fetchMoreOptions === void 0 ? void 0 : fetchMoreOptions.updateQuery;\n    var isCached = this.options.fetchPolicy !== \"no-cache\";\n    if (!isCached) {\n      invariant(updateQuery, 22);\n    }\n    return this.queryManager.fetchQuery(qid, combinedOptions, NetworkStatus.fetchMore).then(function (fetchMoreResult) {\n      _this.queryManager.removeQuery(qid);\n      if (queryInfo.networkStatus === NetworkStatus.fetchMore) {\n        queryInfo.networkStatus = originalNetworkStatus;\n      }\n      if (isCached) {\n        // Performing this cache update inside a cache.batch transaction ensures\n        // any affected cache.watch watchers are notified at most once about any\n        // updates. Most watchers will be using the QueryInfo class, which\n        // responds to notifications by calling reobserveCacheFirst to deliver\n        // fetchMore cache results back to this ObservableQuery.\n        _this.queryManager.cache.batch({\n          update: function (cache) {\n            var updateQuery = fetchMoreOptions.updateQuery;\n            if (updateQuery) {\n              cache.updateQuery({\n                query: _this.query,\n                variables: _this.variables,\n                returnPartialData: true,\n                optimistic: false\n              }, function (previous) {\n                return updateQuery(previous, {\n                  fetchMoreResult: fetchMoreResult.data,\n                  variables: combinedOptions.variables\n                });\n              });\n            } else {\n              // If we're using a field policy instead of updateQuery, the only\n              // thing we need to do is write the new data to the cache using\n              // combinedOptions.variables (instead of this.variables, which is\n              // what this.updateQuery uses, because it works by abusing the\n              // original field value, keyed by the original variables).\n              cache.writeQuery({\n                query: combinedOptions.query,\n                variables: combinedOptions.variables,\n                data: fetchMoreResult.data\n              });\n            }\n          },\n          onWatchUpdated: function (watch) {\n            // Record the DocumentNode associated with any watched query whose\n            // data were updated by the cache writes above.\n            updatedQuerySet.add(watch.query);\n          }\n        });\n      } else {\n        // There is a possibility `lastResult` may not be set when\n        // `fetchMore` is called which would cause this to crash. This should\n        // only happen if we haven't previously reported a result. We don't\n        // quite know what the right behavior should be here since this block\n        // of code runs after the fetch result has executed on the network.\n        // We plan to let it crash in the meantime.\n        //\n        // If we get bug reports due to the `data` property access on\n        // undefined, this should give us a real-world scenario that we can\n        // use to test against and determine the right behavior. If we do end\n        // up changing this behavior, this may require, for example, an\n        // adjustment to the types on `updateQuery` since that function\n        // expects that the first argument always contains previous result\n        // data, but not `undefined`.\n        var lastResult = _this.getLast(\"result\");\n        var data = updateQuery(lastResult.data, {\n          fetchMoreResult: fetchMoreResult.data,\n          variables: combinedOptions.variables\n        });\n        _this.reportResult(__assign(__assign({}, lastResult), {\n          networkStatus: originalNetworkStatus,\n          loading: isNetworkRequestInFlight(originalNetworkStatus),\n          data: data\n        }), _this.variables);\n      }\n      return _this.maskResult(fetchMoreResult);\n    }).finally(function () {\n      // In case the cache writes above did not generate a broadcast\n      // notification (which would have been intercepted by onWatchUpdated),\n      // likely because the written data were the same as what was already in\n      // the cache, we still want fetchMore to deliver its final loading:false\n      // result with the unchanged data.\n      if (isCached && !updatedQuerySet.has(_this.query)) {\n        _this.reobserveCacheFirst();\n      }\n    });\n  };\n  // XXX the subscription variables are separate from the query variables.\n  // if you want to update subscription variables, right now you have to do that separately,\n  // and you can only do it by stopping the subscription and then subscribing again with new variables.\n  /**\n   * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.\n   *\n   * This function returns _another_ function that you can call to terminate the subscription.\n   */\n  ObservableQuery.prototype.subscribeToMore = function (options) {\n    var _this = this;\n    var subscription = this.queryManager.startGraphQLSubscription({\n      query: options.document,\n      variables: options.variables,\n      context: options.context\n    }).subscribe({\n      next: function (subscriptionData) {\n        var updateQuery = options.updateQuery;\n        if (updateQuery) {\n          _this.updateQuery(function (previous, updateOptions) {\n            return updateQuery(previous, __assign({\n              subscriptionData: subscriptionData\n            }, updateOptions));\n          });\n        }\n      },\n      error: function (err) {\n        if (options.onError) {\n          options.onError(err);\n          return;\n        }\n        globalThis.__DEV__ !== false && invariant.error(23, err);\n      }\n    });\n    this.subscriptions.add(subscription);\n    return function () {\n      if (_this.subscriptions.delete(subscription)) {\n        subscription.unsubscribe();\n      }\n    };\n  };\n  ObservableQuery.prototype.setOptions = function (newOptions) {\n    return this.reobserve(newOptions);\n  };\n  ObservableQuery.prototype.silentSetOptions = function (newOptions) {\n    var mergedOptions = compact(this.options, newOptions || {});\n    assign(this.options, mergedOptions);\n  };\n  /**\n   * Update the variables of this observable query, and fetch the new results\n   * if they've changed. Most users should prefer `refetch` instead of\n   * `setVariables` in order to to be properly notified of results even when\n   * they come from the cache.\n   *\n   * Note: the `next` callback will *not* fire if the variables have not changed\n   * or if the result is coming from cache.\n   *\n   * Note: the promise will return the old results immediately if the variables\n   * have not changed.\n   *\n   * Note: the promise will return null immediately if the query is not active\n   * (there are no subscribers).\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  ObservableQuery.prototype.setVariables = function (variables) {\n    if (equal(this.variables, variables)) {\n      // If we have no observers, then we don't actually want to make a network\n      // request. As soon as someone observes the query, the request will kick\n      // off. For now, we just store any changes. (See #1077)\n      return this.observers.size ? this.result() : Promise.resolve();\n    }\n    this.options.variables = variables;\n    // See comment above\n    if (!this.observers.size) {\n      return Promise.resolve();\n    }\n    return this.reobserve({\n      // Reset options.fetchPolicy to its original value.\n      fetchPolicy: this.options.initialFetchPolicy,\n      variables: variables\n    }, NetworkStatus.setVariables);\n  };\n  /**\n   * A function that enables you to update the query's cached result without executing a followup GraphQL operation.\n   *\n   * See [using updateQuery and updateFragment](https://www.apollographql.com/docs/react/caching/cache-interaction/#using-updatequery-and-updatefragment) for additional information.\n   */\n  ObservableQuery.prototype.updateQuery = function (mapFn) {\n    var queryManager = this.queryManager;\n    var _a = queryManager.cache.diff({\n        query: this.options.query,\n        variables: this.variables,\n        returnPartialData: true,\n        optimistic: false\n      }),\n      result = _a.result,\n      complete = _a.complete;\n    var newResult = mapFn(result, {\n      variables: this.variables,\n      complete: !!complete,\n      previousData: result\n    });\n    if (newResult) {\n      queryManager.cache.writeQuery({\n        query: this.options.query,\n        data: newResult,\n        variables: this.variables\n      });\n      queryManager.broadcastQueries();\n    }\n  };\n  /**\n   * A function that instructs the query to begin re-executing at a specified interval (in milliseconds).\n   */\n  ObservableQuery.prototype.startPolling = function (pollInterval) {\n    this.options.pollInterval = pollInterval;\n    this.updatePolling();\n  };\n  /**\n   * A function that instructs the query to stop polling after a previous call to `startPolling`.\n   */\n  ObservableQuery.prototype.stopPolling = function () {\n    this.options.pollInterval = 0;\n    this.updatePolling();\n  };\n  // Update options.fetchPolicy according to options.nextFetchPolicy.\n  ObservableQuery.prototype.applyNextFetchPolicy = function (reason,\n  // It's possible to use this method to apply options.nextFetchPolicy to\n  // options.fetchPolicy even if options !== this.options, though that happens\n  // most often when the options are temporary, used for only one request and\n  // then thrown away, so nextFetchPolicy may not end up mattering.\n  options) {\n    if (options.nextFetchPolicy) {\n      var _a = options.fetchPolicy,\n        fetchPolicy = _a === void 0 ? \"cache-first\" : _a,\n        _b = options.initialFetchPolicy,\n        initialFetchPolicy = _b === void 0 ? fetchPolicy : _b;\n      if (fetchPolicy === \"standby\") {\n        // Do nothing, leaving options.fetchPolicy unchanged.\n      } else if (typeof options.nextFetchPolicy === \"function\") {\n        // When someone chooses \"cache-and-network\" or \"network-only\" as their\n        // initial FetchPolicy, they often do not want future cache updates to\n        // trigger unconditional network requests, which is what repeatedly\n        // applying the \"cache-and-network\" or \"network-only\" policies would\n        // seem to imply. Instead, when the cache reports an update after the\n        // initial network request, it may be desirable for subsequent network\n        // requests to be triggered only if the cache result is incomplete. To\n        // that end, the options.nextFetchPolicy option provides an easy way to\n        // update options.fetchPolicy after the initial network request, without\n        // having to call observableQuery.setOptions.\n        options.fetchPolicy = options.nextFetchPolicy(fetchPolicy, {\n          reason: reason,\n          options: options,\n          observable: this,\n          initialFetchPolicy: initialFetchPolicy\n        });\n      } else if (reason === \"variables-changed\") {\n        options.fetchPolicy = initialFetchPolicy;\n      } else {\n        options.fetchPolicy = options.nextFetchPolicy;\n      }\n    }\n    return options.fetchPolicy;\n  };\n  ObservableQuery.prototype.fetch = function (options, newNetworkStatus, query) {\n    // TODO Make sure we update the networkStatus (and infer fetchVariables)\n    // before actually committing to the fetch.\n    var queryInfo = this.queryManager.getOrCreateQuery(this.queryId);\n    queryInfo.setObservableQuery(this);\n    return this.queryManager[\"fetchConcastWithInfo\"](queryInfo, options, newNetworkStatus, query);\n  };\n  // Turns polling on or off based on this.options.pollInterval.\n  ObservableQuery.prototype.updatePolling = function () {\n    var _this = this;\n    // Avoid polling in SSR mode\n    if (this.queryManager.ssrMode) {\n      return;\n    }\n    var _a = this,\n      pollingInfo = _a.pollingInfo,\n      pollInterval = _a.options.pollInterval;\n    if (!pollInterval || !this.hasObservers()) {\n      if (pollingInfo) {\n        clearTimeout(pollingInfo.timeout);\n        delete this.pollingInfo;\n      }\n      return;\n    }\n    if (pollingInfo && pollingInfo.interval === pollInterval) {\n      return;\n    }\n    invariant(pollInterval, 24);\n    var info = pollingInfo || (this.pollingInfo = {});\n    info.interval = pollInterval;\n    var maybeFetch = function () {\n      var _a, _b;\n      if (_this.pollingInfo) {\n        if (!isNetworkRequestInFlight(_this.queryInfo.networkStatus) && !((_b = (_a = _this.options).skipPollAttempt) === null || _b === void 0 ? void 0 : _b.call(_a))) {\n          _this.reobserve({\n            // Most fetchPolicy options don't make sense to use in a polling context, as\n            // users wouldn't want to be polling the cache directly. However, network-only and\n            // no-cache are both useful for when the user wants to control whether or not the\n            // polled results are written to the cache.\n            fetchPolicy: _this.options.initialFetchPolicy === \"no-cache\" ? \"no-cache\" : \"network-only\"\n          }, NetworkStatus.poll).then(poll, poll);\n        } else {\n          poll();\n        }\n      }\n    };\n    var poll = function () {\n      var info = _this.pollingInfo;\n      if (info) {\n        clearTimeout(info.timeout);\n        info.timeout = setTimeout(maybeFetch, info.interval);\n      }\n    };\n    poll();\n  };\n  ObservableQuery.prototype.updateLastResult = function (newResult, variables) {\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    var error = this.getLastError();\n    // Preserve this.last.error unless the variables have changed.\n    if (error && this.last && !equal(variables, this.last.variables)) {\n      error = void 0;\n    }\n    return this.last = __assign({\n      result: this.queryManager.assumeImmutableResults ? newResult : cloneDeep(newResult),\n      variables: variables\n    }, error ? {\n      error: error\n    } : null);\n  };\n  ObservableQuery.prototype.reobserveAsConcast = function (newOptions, newNetworkStatus) {\n    var _this = this;\n    this.isTornDown = false;\n    var useDisposableConcast =\n    // Refetching uses a disposable Concast to allow refetches using different\n    // options/variables, without permanently altering the options of the\n    // original ObservableQuery.\n    newNetworkStatus === NetworkStatus.refetch ||\n    // The fetchMore method does not actually call the reobserve method, but,\n    // if it did, it would definitely use a disposable Concast.\n    newNetworkStatus === NetworkStatus.fetchMore ||\n    // Polling uses a disposable Concast so the polling options (which force\n    // fetchPolicy to be \"network-only\" or \"no-cache\") won't override the original options.\n    newNetworkStatus === NetworkStatus.poll;\n    // Save the old variables, since Object.assign may modify them below.\n    var oldVariables = this.options.variables;\n    var oldFetchPolicy = this.options.fetchPolicy;\n    var mergedOptions = compact(this.options, newOptions || {});\n    var options = useDisposableConcast ?\n    // Disposable Concast fetches receive a shallow copy of this.options\n    // (merged with newOptions), leaving this.options unmodified.\n    mergedOptions : assign(this.options, mergedOptions);\n    // Don't update options.query with the transformed query to avoid\n    // overwriting this.options.query when we aren't using a disposable concast.\n    // We want to ensure we can re-run the custom document transforms the next\n    // time a request is made against the original query.\n    var query = this.transformDocument(options.query);\n    this.lastQuery = query;\n    if (!useDisposableConcast) {\n      // We can skip calling updatePolling if we're not changing this.options.\n      this.updatePolling();\n      // Reset options.fetchPolicy to its original value when variables change,\n      // unless a new fetchPolicy was provided by newOptions.\n      if (newOptions && newOptions.variables && !equal(newOptions.variables, oldVariables) &&\n      // Don't mess with the fetchPolicy if it's currently \"standby\".\n      options.fetchPolicy !== \"standby\" && (\n      // If we're changing the fetchPolicy anyway, don't try to change it here\n      // using applyNextFetchPolicy. The explicit options.fetchPolicy wins.\n      options.fetchPolicy === oldFetchPolicy ||\n      // A `nextFetchPolicy` function has even higher priority, though,\n      // so in that case `applyNextFetchPolicy` must be called.\n      typeof options.nextFetchPolicy === \"function\")) {\n        this.applyNextFetchPolicy(\"variables-changed\", options);\n        if (newNetworkStatus === void 0) {\n          newNetworkStatus = NetworkStatus.setVariables;\n        }\n      }\n    }\n    this.waitForOwnResult && (this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy));\n    var finishWaitingForOwnResult = function () {\n      if (_this.concast === concast) {\n        _this.waitForOwnResult = false;\n      }\n    };\n    var variables = options.variables && __assign({}, options.variables);\n    var _a = this.fetch(options, newNetworkStatus, query),\n      concast = _a.concast,\n      fromLink = _a.fromLink;\n    var observer = {\n      next: function (result) {\n        if (equal(_this.variables, variables)) {\n          finishWaitingForOwnResult();\n          _this.reportResult(result, variables);\n        }\n      },\n      error: function (error) {\n        if (equal(_this.variables, variables)) {\n          // Coming from `getResultsFromLink`, `error` here should always be an `ApolloError`.\n          // However, calling `concast.cancel` can inject another type of error, so we have to\n          // wrap it again here.\n          if (!isApolloError(error)) {\n            error = new ApolloError({\n              networkError: error\n            });\n          }\n          finishWaitingForOwnResult();\n          _this.reportError(error, variables);\n        }\n      }\n    };\n    if (!useDisposableConcast && (fromLink || !this.concast)) {\n      // We use the {add,remove}Observer methods directly to avoid wrapping\n      // observer with an unnecessary SubscriptionObserver object.\n      if (this.concast && this.observer) {\n        this.concast.removeObserver(this.observer);\n      }\n      this.concast = concast;\n      this.observer = observer;\n    }\n    concast.addObserver(observer);\n    return concast;\n  };\n  ObservableQuery.prototype.reobserve = function (newOptions, newNetworkStatus) {\n    return preventUnhandledRejection(this.reobserveAsConcast(newOptions, newNetworkStatus).promise.then(this.maskResult));\n  };\n  ObservableQuery.prototype.resubscribeAfterError = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    // If `lastError` is set in the current when the subscription is re-created,\n    // the subscription will immediately receive the error, which will\n    // cause it to terminate again. To avoid this, we first clear\n    // the last error/result from the `observableQuery` before re-starting\n    // the subscription, and restore the last value afterwards so that the\n    // subscription has a chance to stay open.\n    var last = this.last;\n    this.resetLastResults();\n    var subscription = this.subscribe.apply(this, args);\n    this.last = last;\n    return subscription;\n  };\n  // (Re)deliver the current result to this.observers without applying fetch\n  // policies or making network requests.\n  ObservableQuery.prototype.observe = function () {\n    this.reportResult(\n    // Passing false is important so that this.getCurrentResult doesn't\n    // save the fetchMore result as this.lastResult, causing it to be\n    // ignored due to the this.isDifferentFromLastResult check in\n    // this.reportResult.\n    this.getCurrentFullResult(false), this.variables);\n  };\n  ObservableQuery.prototype.reportResult = function (result, variables) {\n    var lastError = this.getLastError();\n    var isDifferent = this.isDifferentFromLastResult(result, variables);\n    // Update the last result even when isDifferentFromLastResult returns false,\n    // because the query may be using the @nonreactive directive, and we want to\n    // save the the latest version of any nonreactive subtrees (in case\n    // getCurrentResult is called), even though we skip broadcasting changes.\n    if (lastError || !result.partial || this.options.returnPartialData) {\n      this.updateLastResult(result, variables);\n    }\n    if (lastError || isDifferent) {\n      iterateObserversSafely(this.observers, \"next\", this.maskResult(result));\n    }\n  };\n  ObservableQuery.prototype.reportError = function (error, variables) {\n    // Since we don't get the current result on errors, only the error, we\n    // must mirror the updates that occur in QueryStore.markQueryError here\n    var errorResult = __assign(__assign({}, this.getLastResult()), {\n      error: error,\n      errors: error.graphQLErrors,\n      networkStatus: NetworkStatus.error,\n      loading: false\n    });\n    this.updateLastResult(errorResult, variables);\n    iterateObserversSafely(this.observers, \"error\", this.last.error = error);\n  };\n  ObservableQuery.prototype.hasObservers = function () {\n    return this.observers.size > 0;\n  };\n  ObservableQuery.prototype.tearDownQuery = function () {\n    if (this.isTornDown) return;\n    if (this.concast && this.observer) {\n      this.concast.removeObserver(this.observer);\n      delete this.concast;\n      delete this.observer;\n    }\n    this.stopPolling();\n    // stop all active GraphQL subscriptions\n    this.subscriptions.forEach(function (sub) {\n      return sub.unsubscribe();\n    });\n    this.subscriptions.clear();\n    this.queryManager.stopQuery(this.queryId);\n    this.observers.clear();\n    this.isTornDown = true;\n  };\n  ObservableQuery.prototype.transformDocument = function (document) {\n    return this.queryManager.transform(document);\n  };\n  ObservableQuery.prototype.maskResult = function (result) {\n    return result && \"data\" in result ? __assign(__assign({}, result), {\n      data: this.queryManager.maskOperation({\n        document: this.query,\n        data: result.data,\n        fetchPolicy: this.options.fetchPolicy,\n        id: this.queryId\n      })\n    }) : result;\n  };\n  /** @internal */\n  ObservableQuery.prototype.resetNotifications = function () {\n    this.cancelNotifyTimeout();\n    this.dirty = false;\n  };\n  ObservableQuery.prototype.cancelNotifyTimeout = function () {\n    if (this.notifyTimeout) {\n      clearTimeout(this.notifyTimeout);\n      this.notifyTimeout = void 0;\n    }\n  };\n  /** @internal */\n  ObservableQuery.prototype.scheduleNotify = function () {\n    var _this = this;\n    if (this.dirty) return;\n    this.dirty = true;\n    if (!this.notifyTimeout) {\n      this.notifyTimeout = setTimeout(function () {\n        return _this.notify();\n      }, 0);\n    }\n  };\n  /** @internal */\n  ObservableQuery.prototype.notify = function () {\n    this.cancelNotifyTimeout();\n    if (this.dirty) {\n      if (this.options.fetchPolicy == \"cache-only\" || this.options.fetchPolicy == \"cache-and-network\" || !isNetworkRequestInFlight(this.queryInfo.networkStatus)) {\n        var diff = this.queryInfo.getDiff();\n        if (diff.fromOptimisticTransaction) {\n          // If this diff came from an optimistic transaction, deliver the\n          // current cache data to the ObservableQuery, but don't perform a\n          // reobservation, since oq.reobserveCacheFirst might make a network\n          // request, and we never want to trigger network requests in the\n          // middle of optimistic updates.\n          this.observe();\n        } else {\n          // Otherwise, make the ObservableQuery \"reobserve\" the latest data\n          // using a temporary fetch policy of \"cache-first\", so complete cache\n          // results have a chance to be delivered without triggering additional\n          // network requests, even when options.fetchPolicy is \"network-only\"\n          // or \"cache-and-network\". All other fetch policies are preserved by\n          // this method, and are handled by calling oq.reobserve(). If this\n          // reobservation is spurious, isDifferentFromLastResult still has a\n          // chance to catch it before delivery to ObservableQuery subscribers.\n          this.reobserveCacheFirst();\n        }\n      }\n    }\n    this.dirty = false;\n  };\n  // Reobserve with fetchPolicy effectively set to \"cache-first\", triggering\n  // delivery of any new data from the cache, possibly falling back to the network\n  // if any cache data are missing. This allows _complete_ cache results to be\n  // delivered without also kicking off unnecessary network requests when\n  // this.options.fetchPolicy is \"cache-and-network\" or \"network-only\". When\n  // this.options.fetchPolicy is any other policy (\"cache-first\", \"cache-only\",\n  // \"standby\", or \"no-cache\"), we call this.reobserve() as usual.\n  ObservableQuery.prototype.reobserveCacheFirst = function () {\n    var _a = this.options,\n      fetchPolicy = _a.fetchPolicy,\n      nextFetchPolicy = _a.nextFetchPolicy;\n    if (fetchPolicy === \"cache-and-network\" || fetchPolicy === \"network-only\") {\n      return this.reobserve({\n        fetchPolicy: \"cache-first\",\n        // Use a temporary nextFetchPolicy function that replaces itself with the\n        // previous nextFetchPolicy value and returns the original fetchPolicy.\n        nextFetchPolicy: function (currentFetchPolicy, context) {\n          // Replace this nextFetchPolicy function in the options object with the\n          // original this.options.nextFetchPolicy value.\n          this.nextFetchPolicy = nextFetchPolicy;\n          // If the original nextFetchPolicy value was a function, give it a\n          // chance to decide what happens here.\n          if (typeof this.nextFetchPolicy === \"function\") {\n            return this.nextFetchPolicy(currentFetchPolicy, context);\n          }\n          // Otherwise go back to the original this.options.fetchPolicy.\n          return fetchPolicy;\n        }\n      });\n    }\n    return this.reobserve();\n  };\n  /**\n   * @internal\n   * A slot used by the `useQuery` hook to indicate that `client.watchQuery`\n   * should not register the query immediately, but instead wait for the query to\n   * be started registered with the `QueryManager` when `useSyncExternalStore`\n   * actively subscribes to it.\n   */\n  ObservableQuery.inactiveOnCreation = new Slot();\n  return ObservableQuery;\n}(Observable);\nexport { ObservableQuery };\n// Necessary because the ObservableQuery constructor has a different\n// signature than the Observable constructor.\nfixObservableSubclass(ObservableQuery);\nfunction defaultSubscriptionObserverErrorCallback(error) {\n  globalThis.__DEV__ !== false && invariant.error(25, error.message, error.stack);\n}\nexport function logMissingFieldErrors(missing) {\n  if (globalThis.__DEV__ !== false && missing) {\n    globalThis.__DEV__ !== false && invariant.debug(26, missing);\n  }\n}\nfunction skipCacheDataFor(fetchPolicy /* `undefined` would mean `\"cache-first\"` */) {\n  return fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\" || fetchPolicy === \"standby\";\n}", "map": {"version": 3, "names": ["__assign", "__extends", "invariant", "equal", "NetworkStatus", "isNetworkRequestInFlight", "cloneDeep", "compact", "getOperationDefinition", "Observable", "iterateObserversSafely", "fixObservableSubclass", "getQueryDefinition", "preventUnhandledRejection", "ApolloError", "isApolloError", "equalBy<PERSON>uery", "Slot", "assign", "Object", "hasOwnProperty", "ObservableQuery", "_super", "_a", "query<PERSON>anager", "queryInfo", "options", "_this", "startedInactive", "inactiveOnCreation", "getValue", "call", "observer", "set", "queryId", "subObserver", "_subscription", "_observer", "error", "defaultSubscriptionObserverErrorCallback", "first", "observers", "size", "add", "last", "result", "next", "maskResult", "reobserve", "catch", "delete", "tearDownQuery", "Set", "subscriptions", "dirty", "waitForOwnResult", "skipCacheDataFor", "fetchPolicy", "isTornDown", "subscribeToMore", "bind", "_b", "defaultOptions", "watch<PERSON><PERSON>y", "_c", "_d", "defaultFetchPolicy", "_e", "_f", "initialFetchPolicy", "generateQueryId", "opDef", "query", "queryName", "name", "value", "defineProperty", "prototype", "get", "last<PERSON><PERSON>y", "enumerable", "configurable", "variables", "Promise", "resolve", "reject", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "subscription", "unsubscribe", "subscribe", "resetDiff", "getCurrentFullResult", "saveAsLastResult", "lastResult", "getLastResult", "networkStatus", "ready", "loading", "getDocumentInfo", "hasForcedResolvers", "diff", "getDiff", "complete", "returnPartialData", "data", "partial", "errors", "globalThis", "__DEV__", "partialRefetch", "logMissingFieldErrors", "missing", "updateLastResult", "getCurrentResult", "isDifferentFromLastResult", "newResult", "documentInfo", "dataMasking", "nonReactiveQuery", "resultIsDifferent", "hasNonreactiveDirective", "getLast", "key", "variablesMustMatch", "getLastError", "resetLastResults", "resetQueryStoreErrors", "resetErrors", "refetch", "reobserveOptions", "pollInterval", "queryDef", "vars", "variableDefinitions", "some", "v", "variable", "warn", "resetLastWrite", "fetchMore", "fetchMoreOptions", "combinedOptions", "transformDocument", "qid", "originalNetworkStatus", "notifyOnNetworkStatusChange", "observe", "updatedQuerySet", "updateQuery", "<PERSON><PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "then", "fetchMoreResult", "cache", "batch", "update", "optimistic", "previous", "writeQuery", "onWatchUpdated", "watch", "reportResult", "finally", "has", "reobserveCacheFirst", "startGraphQLSubscription", "document", "context", "subscriptionData", "updateOptions", "err", "onError", "setOptions", "newOptions", "silentSetOptions", "mergedOptions", "setVariables", "mapFn", "previousData", "broadcastQueries", "startPolling", "updatePolling", "stopPolling", "applyNextFetchPolicy", "reason", "nextFetchPolicy", "observable", "fetch", "newNetworkStatus", "getOrCreateQuery", "setObservableQuery", "ssrMode", "pollingInfo", "hasObservers", "clearTimeout", "timeout", "interval", "info", "maybe<PERSON><PERSON>ch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poll", "assumeImmutableResults", "reobserveAsConcast", "useDisposableConcast", "oldVariables", "oldFetchPolicy", "finishWaitingForOwnResult", "concast", "fromLink", "networkError", "reportError", "removeObserver", "addObserver", "promise", "resubscribeAfterError", "args", "_i", "arguments", "length", "apply", "lastError", "isDifferent", "errorResult", "graphQLErrors", "for<PERSON>ach", "sub", "clear", "stopQuery", "transform", "maskOperation", "id", "resetNotifications", "cancelNotifyTimeout", "notifyTimeout", "scheduleNotify", "notify", "fromOptimisticTransaction", "currentFetchPolicy", "message", "stack", "debug"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/ObservableQuery.js"], "sourcesContent": ["import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { cloneDeep, compact, getOperationDefinition, Observable, iterateObserversSafely, fixObservableSubclass, getQueryDefinition, preventUnhandledRejection, } from \"../utilities/index.js\";\nimport { ApolloError, isApolloError } from \"../errors/index.js\";\nimport { equalByQuery } from \"./equalByQuery.js\";\nimport { Slot } from \"optimism\";\nvar assign = Object.assign, hasOwnProperty = Object.hasOwnProperty;\nvar ObservableQuery = /** @class */ (function (_super) {\n    __extends(ObservableQuery, _super);\n    function ObservableQuery(_a) {\n        var queryManager = _a.queryManager, queryInfo = _a.queryInfo, options = _a.options;\n        var _this = this;\n        var startedInactive = ObservableQuery.inactiveOnCreation.getValue();\n        _this = _super.call(this, function (observer) {\n            if (startedInactive) {\n                queryManager[\"queries\"].set(_this.queryId, queryInfo);\n                startedInactive = false;\n            }\n            // Zen Observable has its own error function, so in order to log correctly\n            // we need to provide a custom error callback.\n            try {\n                var subObserver = observer._subscription._observer;\n                if (subObserver && !subObserver.error) {\n                    subObserver.error = defaultSubscriptionObserverErrorCallback;\n                }\n            }\n            catch (_a) { }\n            var first = !_this.observers.size;\n            _this.observers.add(observer);\n            // Deliver most recent error or result.\n            var last = _this.last;\n            if (last && last.error) {\n                observer.error && observer.error(last.error);\n            }\n            else if (last && last.result) {\n                observer.next && observer.next(_this.maskResult(last.result));\n            }\n            // Initiate observation of this query if it hasn't been reported to\n            // the QueryManager yet.\n            if (first) {\n                // Blindly catching here prevents unhandled promise rejections,\n                // and is safe because the ObservableQuery handles this error with\n                // this.observer.error, so we're not just swallowing the error by\n                // ignoring it here.\n                _this.reobserve().catch(function () { });\n            }\n            return function () {\n                if (_this.observers.delete(observer) && !_this.observers.size) {\n                    _this.tearDownQuery();\n                }\n            };\n        }) || this;\n        _this.observers = new Set();\n        _this.subscriptions = new Set();\n        _this.dirty = false;\n        // related classes\n        _this.queryInfo = queryInfo;\n        _this.queryManager = queryManager;\n        // active state\n        _this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy);\n        _this.isTornDown = false;\n        _this.subscribeToMore = _this.subscribeToMore.bind(_this);\n        _this.maskResult = _this.maskResult.bind(_this);\n        var _b = queryManager.defaultOptions.watchQuery, _c = _b === void 0 ? {} : _b, _d = _c.fetchPolicy, defaultFetchPolicy = _d === void 0 ? \"cache-first\" : _d;\n        var _e = options.fetchPolicy, fetchPolicy = _e === void 0 ? defaultFetchPolicy : _e, \n        // Make sure we don't store \"standby\" as the initialFetchPolicy.\n        _f = options.initialFetchPolicy, \n        // Make sure we don't store \"standby\" as the initialFetchPolicy.\n        initialFetchPolicy = _f === void 0 ? fetchPolicy === \"standby\" ? defaultFetchPolicy : (fetchPolicy) : _f;\n        _this.options = __assign(__assign({}, options), { \n            // Remember the initial options.fetchPolicy so we can revert back to this\n            // policy when variables change. This information can also be specified\n            // (or overridden) by providing options.initialFetchPolicy explicitly.\n            initialFetchPolicy: initialFetchPolicy, \n            // This ensures this.options.fetchPolicy always has a string value, in\n            // case options.fetchPolicy was not provided.\n            fetchPolicy: fetchPolicy });\n        _this.queryId = queryInfo.queryId || queryManager.generateQueryId();\n        var opDef = getOperationDefinition(_this.query);\n        _this.queryName = opDef && opDef.name && opDef.name.value;\n        return _this;\n    }\n    Object.defineProperty(ObservableQuery.prototype, \"query\", {\n        // The `query` computed property will always reflect the document transformed\n        // by the last run query. `this.options.query` will always reflect the raw\n        // untransformed query to ensure document transforms with runtime conditionals\n        // are run on the original document.\n        get: function () {\n            return this.lastQuery || this.options.query;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ObservableQuery.prototype, \"variables\", {\n        // Computed shorthand for this.options.variables, preserved for\n        // backwards compatibility.\n        /**\n         * An object containing the variables that were provided for the query.\n         */\n        get: function () {\n            return this.options.variables;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ObservableQuery.prototype.result = function () {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            // TODO: this code doesn’t actually make sense insofar as the observer\n            // will never exist in this.observers due how zen-observable wraps observables.\n            // https://github.com/zenparsing/zen-observable/blob/master/src/Observable.js#L169\n            var observer = {\n                next: function (result) {\n                    resolve(result);\n                    // Stop the query within the QueryManager if we can before\n                    // this function returns.\n                    //\n                    // We do this in order to prevent observers piling up within\n                    // the QueryManager. Notice that we only fully unsubscribe\n                    // from the subscription in a setTimeout(..., 0)  call. This call can\n                    // actually be handled by the browser at a much later time. If queries\n                    // are fired in the meantime, observers that should have been removed\n                    // from the QueryManager will continue to fire, causing an unnecessary\n                    // performance hit.\n                    _this.observers.delete(observer);\n                    if (!_this.observers.size) {\n                        _this.queryManager.removeQuery(_this.queryId);\n                    }\n                    setTimeout(function () {\n                        subscription.unsubscribe();\n                    }, 0);\n                },\n                error: reject,\n            };\n            var subscription = _this.subscribe(observer);\n        });\n    };\n    /** @internal */\n    ObservableQuery.prototype.resetDiff = function () {\n        this.queryInfo.resetDiff();\n    };\n    ObservableQuery.prototype.getCurrentFullResult = function (saveAsLastResult) {\n        if (saveAsLastResult === void 0) { saveAsLastResult = true; }\n        // Use the last result as long as the variables match this.variables.\n        var lastResult = this.getLastResult(true);\n        var networkStatus = this.queryInfo.networkStatus ||\n            (lastResult && lastResult.networkStatus) ||\n            NetworkStatus.ready;\n        var result = __assign(__assign({}, lastResult), { loading: isNetworkRequestInFlight(networkStatus), networkStatus: networkStatus });\n        var _a = this.options.fetchPolicy, fetchPolicy = _a === void 0 ? \"cache-first\" : _a;\n        if (\n        // These fetch policies should never deliver data from the cache, unless\n        // redelivering a previously delivered result.\n        skipCacheDataFor(fetchPolicy) ||\n            // If this.options.query has @client(always: true) fields, we cannot\n            // trust diff.result, since it was read from the cache without running\n            // local resolvers (and it's too late to run resolvers now, since we must\n            // return a result synchronously).\n            this.queryManager.getDocumentInfo(this.query).hasForcedResolvers) {\n            // Fall through.\n        }\n        else if (this.waitForOwnResult) {\n            // This would usually be a part of `QueryInfo.getDiff()`.\n            // which we skip in the waitForOwnResult case since we are not\n            // interested in the diff.\n            this.queryInfo[\"updateWatch\"]();\n        }\n        else {\n            var diff = this.queryInfo.getDiff();\n            if (diff.complete || this.options.returnPartialData) {\n                result.data = diff.result;\n            }\n            if (equal(result.data, {})) {\n                result.data = void 0;\n            }\n            if (diff.complete) {\n                // Similar to setting result.partial to false, but taking advantage of the\n                // falsiness of missing fields.\n                delete result.partial;\n                // If the diff is complete, and we're using a FetchPolicy that\n                // terminates after a complete cache read, we can assume the next result\n                // we receive will have NetworkStatus.ready and !loading.\n                if (diff.complete &&\n                    result.networkStatus === NetworkStatus.loading &&\n                    (fetchPolicy === \"cache-first\" || fetchPolicy === \"cache-only\")) {\n                    result.networkStatus = NetworkStatus.ready;\n                    result.loading = false;\n                }\n            }\n            else {\n                result.partial = true;\n            }\n            // We need to check for both both `error` and `errors` field because there\n            // are cases where sometimes `error` is set, but not `errors` and\n            // vice-versa. This will be updated in the next major version when\n            // `errors` is deprecated in favor of `error`.\n            if (result.networkStatus === NetworkStatus.ready &&\n                (result.error || result.errors)) {\n                result.networkStatus = NetworkStatus.error;\n            }\n            if (globalThis.__DEV__ !== false &&\n                !diff.complete &&\n                !this.options.partialRefetch &&\n                !result.loading &&\n                !result.data &&\n                !result.error) {\n                logMissingFieldErrors(diff.missing);\n            }\n        }\n        if (saveAsLastResult) {\n            this.updateLastResult(result);\n        }\n        return result;\n    };\n    ObservableQuery.prototype.getCurrentResult = function (saveAsLastResult) {\n        if (saveAsLastResult === void 0) { saveAsLastResult = true; }\n        return this.maskResult(this.getCurrentFullResult(saveAsLastResult));\n    };\n    // Compares newResult to the snapshot we took of this.lastResult when it was\n    // first received.\n    ObservableQuery.prototype.isDifferentFromLastResult = function (newResult, variables) {\n        if (!this.last) {\n            return true;\n        }\n        var documentInfo = this.queryManager.getDocumentInfo(this.query);\n        var dataMasking = this.queryManager.dataMasking;\n        var query = dataMasking ? documentInfo.nonReactiveQuery : this.query;\n        var resultIsDifferent = dataMasking || documentInfo.hasNonreactiveDirective ?\n            !equalByQuery(query, this.last.result, newResult, this.variables)\n            : !equal(this.last.result, newResult);\n        return (resultIsDifferent || (variables && !equal(this.last.variables, variables)));\n    };\n    ObservableQuery.prototype.getLast = function (key, variablesMustMatch) {\n        var last = this.last;\n        if (last &&\n            last[key] &&\n            (!variablesMustMatch || equal(last.variables, this.variables))) {\n            return last[key];\n        }\n    };\n    ObservableQuery.prototype.getLastResult = function (variablesMustMatch) {\n        return this.getLast(\"result\", variablesMustMatch);\n    };\n    ObservableQuery.prototype.getLastError = function (variablesMustMatch) {\n        return this.getLast(\"error\", variablesMustMatch);\n    };\n    ObservableQuery.prototype.resetLastResults = function () {\n        delete this.last;\n        this.isTornDown = false;\n    };\n    ObservableQuery.prototype.resetQueryStoreErrors = function () {\n        this.queryManager.resetErrors(this.queryId);\n    };\n    /**\n     * Update the variables of this observable query, and fetch the new results.\n     * This method should be preferred over `setVariables` in most use cases.\n     *\n     * @param variables - The new set of variables. If there are missing variables,\n     * the previous values of those variables will be used.\n     */\n    ObservableQuery.prototype.refetch = function (variables) {\n        var _a;\n        var reobserveOptions = {\n            // Always disable polling for refetches.\n            pollInterval: 0,\n        };\n        // Unless the provided fetchPolicy always consults the network\n        // (no-cache, network-only, or cache-and-network), override it with\n        // network-only to force the refetch for this fetchQuery call.\n        var fetchPolicy = this.options.fetchPolicy;\n        if (fetchPolicy === \"no-cache\") {\n            reobserveOptions.fetchPolicy = \"no-cache\";\n        }\n        else {\n            reobserveOptions.fetchPolicy = \"network-only\";\n        }\n        if (globalThis.__DEV__ !== false && variables && hasOwnProperty.call(variables, \"variables\")) {\n            var queryDef = getQueryDefinition(this.query);\n            var vars = queryDef.variableDefinitions;\n            if (!vars || !vars.some(function (v) { return v.variable.name.value === \"variables\"; })) {\n                globalThis.__DEV__ !== false && invariant.warn(\n                    21,\n                    variables,\n                    ((_a = queryDef.name) === null || _a === void 0 ? void 0 : _a.value) || queryDef\n                );\n            }\n        }\n        if (variables && !equal(this.options.variables, variables)) {\n            // Update the existing options with new variables\n            reobserveOptions.variables = this.options.variables = __assign(__assign({}, this.options.variables), variables);\n        }\n        this.queryInfo.resetLastWrite();\n        return this.reobserve(reobserveOptions, NetworkStatus.refetch);\n    };\n    /**\n     * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).\n     */\n    ObservableQuery.prototype.fetchMore = function (fetchMoreOptions) {\n        var _this = this;\n        var combinedOptions = __assign(__assign({}, (fetchMoreOptions.query ? fetchMoreOptions : (__assign(__assign(__assign(__assign({}, this.options), { query: this.options.query }), fetchMoreOptions), { variables: __assign(__assign({}, this.options.variables), fetchMoreOptions.variables) })))), { \n            // The fetchMore request goes immediately to the network and does\n            // not automatically write its result to the cache (hence no-cache\n            // instead of network-only), because we allow the caller of\n            // fetchMore to provide an updateQuery callback that determines how\n            // the data gets written to the cache.\n            fetchPolicy: \"no-cache\" });\n        combinedOptions.query = this.transformDocument(combinedOptions.query);\n        var qid = this.queryManager.generateQueryId();\n        // If a temporary query is passed to `fetchMore`, we don't want to store\n        // it as the last query result since it may be an optimized query for\n        // pagination. We will however run the transforms on the original document\n        // as well as the document passed in `fetchMoreOptions` to ensure the cache\n        // uses the most up-to-date document which may rely on runtime conditionals.\n        this.lastQuery =\n            fetchMoreOptions.query ?\n                this.transformDocument(this.options.query)\n                : combinedOptions.query;\n        // Simulate a loading result for the original query with\n        // result.networkStatus === NetworkStatus.fetchMore.\n        var queryInfo = this.queryInfo;\n        var originalNetworkStatus = queryInfo.networkStatus;\n        queryInfo.networkStatus = NetworkStatus.fetchMore;\n        if (combinedOptions.notifyOnNetworkStatusChange) {\n            this.observe();\n        }\n        var updatedQuerySet = new Set();\n        var updateQuery = fetchMoreOptions === null || fetchMoreOptions === void 0 ? void 0 : fetchMoreOptions.updateQuery;\n        var isCached = this.options.fetchPolicy !== \"no-cache\";\n        if (!isCached) {\n            invariant(updateQuery, 22);\n        }\n        return this.queryManager\n            .fetchQuery(qid, combinedOptions, NetworkStatus.fetchMore)\n            .then(function (fetchMoreResult) {\n            _this.queryManager.removeQuery(qid);\n            if (queryInfo.networkStatus === NetworkStatus.fetchMore) {\n                queryInfo.networkStatus = originalNetworkStatus;\n            }\n            if (isCached) {\n                // Performing this cache update inside a cache.batch transaction ensures\n                // any affected cache.watch watchers are notified at most once about any\n                // updates. Most watchers will be using the QueryInfo class, which\n                // responds to notifications by calling reobserveCacheFirst to deliver\n                // fetchMore cache results back to this ObservableQuery.\n                _this.queryManager.cache.batch({\n                    update: function (cache) {\n                        var updateQuery = fetchMoreOptions.updateQuery;\n                        if (updateQuery) {\n                            cache.updateQuery({\n                                query: _this.query,\n                                variables: _this.variables,\n                                returnPartialData: true,\n                                optimistic: false,\n                            }, function (previous) {\n                                return updateQuery(previous, {\n                                    fetchMoreResult: fetchMoreResult.data,\n                                    variables: combinedOptions.variables,\n                                });\n                            });\n                        }\n                        else {\n                            // If we're using a field policy instead of updateQuery, the only\n                            // thing we need to do is write the new data to the cache using\n                            // combinedOptions.variables (instead of this.variables, which is\n                            // what this.updateQuery uses, because it works by abusing the\n                            // original field value, keyed by the original variables).\n                            cache.writeQuery({\n                                query: combinedOptions.query,\n                                variables: combinedOptions.variables,\n                                data: fetchMoreResult.data,\n                            });\n                        }\n                    },\n                    onWatchUpdated: function (watch) {\n                        // Record the DocumentNode associated with any watched query whose\n                        // data were updated by the cache writes above.\n                        updatedQuerySet.add(watch.query);\n                    },\n                });\n            }\n            else {\n                // There is a possibility `lastResult` may not be set when\n                // `fetchMore` is called which would cause this to crash. This should\n                // only happen if we haven't previously reported a result. We don't\n                // quite know what the right behavior should be here since this block\n                // of code runs after the fetch result has executed on the network.\n                // We plan to let it crash in the meantime.\n                //\n                // If we get bug reports due to the `data` property access on\n                // undefined, this should give us a real-world scenario that we can\n                // use to test against and determine the right behavior. If we do end\n                // up changing this behavior, this may require, for example, an\n                // adjustment to the types on `updateQuery` since that function\n                // expects that the first argument always contains previous result\n                // data, but not `undefined`.\n                var lastResult = _this.getLast(\"result\");\n                var data = updateQuery(lastResult.data, {\n                    fetchMoreResult: fetchMoreResult.data,\n                    variables: combinedOptions.variables,\n                });\n                _this.reportResult(__assign(__assign({}, lastResult), { networkStatus: originalNetworkStatus, loading: isNetworkRequestInFlight(originalNetworkStatus), data: data }), _this.variables);\n            }\n            return _this.maskResult(fetchMoreResult);\n        })\n            .finally(function () {\n            // In case the cache writes above did not generate a broadcast\n            // notification (which would have been intercepted by onWatchUpdated),\n            // likely because the written data were the same as what was already in\n            // the cache, we still want fetchMore to deliver its final loading:false\n            // result with the unchanged data.\n            if (isCached && !updatedQuerySet.has(_this.query)) {\n                _this.reobserveCacheFirst();\n            }\n        });\n    };\n    // XXX the subscription variables are separate from the query variables.\n    // if you want to update subscription variables, right now you have to do that separately,\n    // and you can only do it by stopping the subscription and then subscribing again with new variables.\n    /**\n     * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.\n     *\n     * This function returns _another_ function that you can call to terminate the subscription.\n     */\n    ObservableQuery.prototype.subscribeToMore = function (options) {\n        var _this = this;\n        var subscription = this.queryManager\n            .startGraphQLSubscription({\n            query: options.document,\n            variables: options.variables,\n            context: options.context,\n        })\n            .subscribe({\n            next: function (subscriptionData) {\n                var updateQuery = options.updateQuery;\n                if (updateQuery) {\n                    _this.updateQuery(function (previous, updateOptions) {\n                        return updateQuery(previous, __assign({ subscriptionData: subscriptionData }, updateOptions));\n                    });\n                }\n            },\n            error: function (err) {\n                if (options.onError) {\n                    options.onError(err);\n                    return;\n                }\n                globalThis.__DEV__ !== false && invariant.error(23, err);\n            },\n        });\n        this.subscriptions.add(subscription);\n        return function () {\n            if (_this.subscriptions.delete(subscription)) {\n                subscription.unsubscribe();\n            }\n        };\n    };\n    ObservableQuery.prototype.setOptions = function (newOptions) {\n        return this.reobserve(newOptions);\n    };\n    ObservableQuery.prototype.silentSetOptions = function (newOptions) {\n        var mergedOptions = compact(this.options, newOptions || {});\n        assign(this.options, mergedOptions);\n    };\n    /**\n     * Update the variables of this observable query, and fetch the new results\n     * if they've changed. Most users should prefer `refetch` instead of\n     * `setVariables` in order to to be properly notified of results even when\n     * they come from the cache.\n     *\n     * Note: the `next` callback will *not* fire if the variables have not changed\n     * or if the result is coming from cache.\n     *\n     * Note: the promise will return the old results immediately if the variables\n     * have not changed.\n     *\n     * Note: the promise will return null immediately if the query is not active\n     * (there are no subscribers).\n     *\n     * @param variables - The new set of variables. If there are missing variables,\n     * the previous values of those variables will be used.\n     */\n    ObservableQuery.prototype.setVariables = function (variables) {\n        if (equal(this.variables, variables)) {\n            // If we have no observers, then we don't actually want to make a network\n            // request. As soon as someone observes the query, the request will kick\n            // off. For now, we just store any changes. (See #1077)\n            return this.observers.size ? this.result() : Promise.resolve();\n        }\n        this.options.variables = variables;\n        // See comment above\n        if (!this.observers.size) {\n            return Promise.resolve();\n        }\n        return this.reobserve({\n            // Reset options.fetchPolicy to its original value.\n            fetchPolicy: this.options.initialFetchPolicy,\n            variables: variables,\n        }, NetworkStatus.setVariables);\n    };\n    /**\n     * A function that enables you to update the query's cached result without executing a followup GraphQL operation.\n     *\n     * See [using updateQuery and updateFragment](https://www.apollographql.com/docs/react/caching/cache-interaction/#using-updatequery-and-updatefragment) for additional information.\n     */\n    ObservableQuery.prototype.updateQuery = function (mapFn) {\n        var queryManager = this.queryManager;\n        var _a = queryManager.cache.diff({\n            query: this.options.query,\n            variables: this.variables,\n            returnPartialData: true,\n            optimistic: false,\n        }), result = _a.result, complete = _a.complete;\n        var newResult = mapFn(result, {\n            variables: this.variables,\n            complete: !!complete,\n            previousData: result,\n        });\n        if (newResult) {\n            queryManager.cache.writeQuery({\n                query: this.options.query,\n                data: newResult,\n                variables: this.variables,\n            });\n            queryManager.broadcastQueries();\n        }\n    };\n    /**\n     * A function that instructs the query to begin re-executing at a specified interval (in milliseconds).\n     */\n    ObservableQuery.prototype.startPolling = function (pollInterval) {\n        this.options.pollInterval = pollInterval;\n        this.updatePolling();\n    };\n    /**\n     * A function that instructs the query to stop polling after a previous call to `startPolling`.\n     */\n    ObservableQuery.prototype.stopPolling = function () {\n        this.options.pollInterval = 0;\n        this.updatePolling();\n    };\n    // Update options.fetchPolicy according to options.nextFetchPolicy.\n    ObservableQuery.prototype.applyNextFetchPolicy = function (reason, \n    // It's possible to use this method to apply options.nextFetchPolicy to\n    // options.fetchPolicy even if options !== this.options, though that happens\n    // most often when the options are temporary, used for only one request and\n    // then thrown away, so nextFetchPolicy may not end up mattering.\n    options) {\n        if (options.nextFetchPolicy) {\n            var _a = options.fetchPolicy, fetchPolicy = _a === void 0 ? \"cache-first\" : _a, _b = options.initialFetchPolicy, initialFetchPolicy = _b === void 0 ? fetchPolicy : _b;\n            if (fetchPolicy === \"standby\") {\n                // Do nothing, leaving options.fetchPolicy unchanged.\n            }\n            else if (typeof options.nextFetchPolicy === \"function\") {\n                // When someone chooses \"cache-and-network\" or \"network-only\" as their\n                // initial FetchPolicy, they often do not want future cache updates to\n                // trigger unconditional network requests, which is what repeatedly\n                // applying the \"cache-and-network\" or \"network-only\" policies would\n                // seem to imply. Instead, when the cache reports an update after the\n                // initial network request, it may be desirable for subsequent network\n                // requests to be triggered only if the cache result is incomplete. To\n                // that end, the options.nextFetchPolicy option provides an easy way to\n                // update options.fetchPolicy after the initial network request, without\n                // having to call observableQuery.setOptions.\n                options.fetchPolicy = options.nextFetchPolicy(fetchPolicy, {\n                    reason: reason,\n                    options: options,\n                    observable: this,\n                    initialFetchPolicy: initialFetchPolicy,\n                });\n            }\n            else if (reason === \"variables-changed\") {\n                options.fetchPolicy = initialFetchPolicy;\n            }\n            else {\n                options.fetchPolicy = options.nextFetchPolicy;\n            }\n        }\n        return options.fetchPolicy;\n    };\n    ObservableQuery.prototype.fetch = function (options, newNetworkStatus, query) {\n        // TODO Make sure we update the networkStatus (and infer fetchVariables)\n        // before actually committing to the fetch.\n        var queryInfo = this.queryManager.getOrCreateQuery(this.queryId);\n        queryInfo.setObservableQuery(this);\n        return this.queryManager[\"fetchConcastWithInfo\"](queryInfo, options, newNetworkStatus, query);\n    };\n    // Turns polling on or off based on this.options.pollInterval.\n    ObservableQuery.prototype.updatePolling = function () {\n        var _this = this;\n        // Avoid polling in SSR mode\n        if (this.queryManager.ssrMode) {\n            return;\n        }\n        var _a = this, pollingInfo = _a.pollingInfo, pollInterval = _a.options.pollInterval;\n        if (!pollInterval || !this.hasObservers()) {\n            if (pollingInfo) {\n                clearTimeout(pollingInfo.timeout);\n                delete this.pollingInfo;\n            }\n            return;\n        }\n        if (pollingInfo && pollingInfo.interval === pollInterval) {\n            return;\n        }\n        invariant(pollInterval, 24);\n        var info = pollingInfo || (this.pollingInfo = {});\n        info.interval = pollInterval;\n        var maybeFetch = function () {\n            var _a, _b;\n            if (_this.pollingInfo) {\n                if (!isNetworkRequestInFlight(_this.queryInfo.networkStatus) &&\n                    !((_b = (_a = _this.options).skipPollAttempt) === null || _b === void 0 ? void 0 : _b.call(_a))) {\n                    _this.reobserve({\n                        // Most fetchPolicy options don't make sense to use in a polling context, as\n                        // users wouldn't want to be polling the cache directly. However, network-only and\n                        // no-cache are both useful for when the user wants to control whether or not the\n                        // polled results are written to the cache.\n                        fetchPolicy: _this.options.initialFetchPolicy === \"no-cache\" ?\n                            \"no-cache\"\n                            : \"network-only\",\n                    }, NetworkStatus.poll).then(poll, poll);\n                }\n                else {\n                    poll();\n                }\n            }\n        };\n        var poll = function () {\n            var info = _this.pollingInfo;\n            if (info) {\n                clearTimeout(info.timeout);\n                info.timeout = setTimeout(maybeFetch, info.interval);\n            }\n        };\n        poll();\n    };\n    ObservableQuery.prototype.updateLastResult = function (newResult, variables) {\n        if (variables === void 0) { variables = this.variables; }\n        var error = this.getLastError();\n        // Preserve this.last.error unless the variables have changed.\n        if (error && this.last && !equal(variables, this.last.variables)) {\n            error = void 0;\n        }\n        return (this.last = __assign({ result: this.queryManager.assumeImmutableResults ?\n                newResult\n                : cloneDeep(newResult), variables: variables }, (error ? { error: error } : null)));\n    };\n    ObservableQuery.prototype.reobserveAsConcast = function (newOptions, newNetworkStatus) {\n        var _this = this;\n        this.isTornDown = false;\n        var useDisposableConcast = \n        // Refetching uses a disposable Concast to allow refetches using different\n        // options/variables, without permanently altering the options of the\n        // original ObservableQuery.\n        newNetworkStatus === NetworkStatus.refetch ||\n            // The fetchMore method does not actually call the reobserve method, but,\n            // if it did, it would definitely use a disposable Concast.\n            newNetworkStatus === NetworkStatus.fetchMore ||\n            // Polling uses a disposable Concast so the polling options (which force\n            // fetchPolicy to be \"network-only\" or \"no-cache\") won't override the original options.\n            newNetworkStatus === NetworkStatus.poll;\n        // Save the old variables, since Object.assign may modify them below.\n        var oldVariables = this.options.variables;\n        var oldFetchPolicy = this.options.fetchPolicy;\n        var mergedOptions = compact(this.options, newOptions || {});\n        var options = useDisposableConcast ?\n            // Disposable Concast fetches receive a shallow copy of this.options\n            // (merged with newOptions), leaving this.options unmodified.\n            mergedOptions\n            : assign(this.options, mergedOptions);\n        // Don't update options.query with the transformed query to avoid\n        // overwriting this.options.query when we aren't using a disposable concast.\n        // We want to ensure we can re-run the custom document transforms the next\n        // time a request is made against the original query.\n        var query = this.transformDocument(options.query);\n        this.lastQuery = query;\n        if (!useDisposableConcast) {\n            // We can skip calling updatePolling if we're not changing this.options.\n            this.updatePolling();\n            // Reset options.fetchPolicy to its original value when variables change,\n            // unless a new fetchPolicy was provided by newOptions.\n            if (newOptions &&\n                newOptions.variables &&\n                !equal(newOptions.variables, oldVariables) &&\n                // Don't mess with the fetchPolicy if it's currently \"standby\".\n                options.fetchPolicy !== \"standby\" &&\n                // If we're changing the fetchPolicy anyway, don't try to change it here\n                // using applyNextFetchPolicy. The explicit options.fetchPolicy wins.\n                (options.fetchPolicy === oldFetchPolicy ||\n                    // A `nextFetchPolicy` function has even higher priority, though,\n                    // so in that case `applyNextFetchPolicy` must be called.\n                    typeof options.nextFetchPolicy === \"function\")) {\n                this.applyNextFetchPolicy(\"variables-changed\", options);\n                if (newNetworkStatus === void 0) {\n                    newNetworkStatus = NetworkStatus.setVariables;\n                }\n            }\n        }\n        this.waitForOwnResult && (this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy));\n        var finishWaitingForOwnResult = function () {\n            if (_this.concast === concast) {\n                _this.waitForOwnResult = false;\n            }\n        };\n        var variables = options.variables && __assign({}, options.variables);\n        var _a = this.fetch(options, newNetworkStatus, query), concast = _a.concast, fromLink = _a.fromLink;\n        var observer = {\n            next: function (result) {\n                if (equal(_this.variables, variables)) {\n                    finishWaitingForOwnResult();\n                    _this.reportResult(result, variables);\n                }\n            },\n            error: function (error) {\n                if (equal(_this.variables, variables)) {\n                    // Coming from `getResultsFromLink`, `error` here should always be an `ApolloError`.\n                    // However, calling `concast.cancel` can inject another type of error, so we have to\n                    // wrap it again here.\n                    if (!isApolloError(error)) {\n                        error = new ApolloError({ networkError: error });\n                    }\n                    finishWaitingForOwnResult();\n                    _this.reportError(error, variables);\n                }\n            },\n        };\n        if (!useDisposableConcast && (fromLink || !this.concast)) {\n            // We use the {add,remove}Observer methods directly to avoid wrapping\n            // observer with an unnecessary SubscriptionObserver object.\n            if (this.concast && this.observer) {\n                this.concast.removeObserver(this.observer);\n            }\n            this.concast = concast;\n            this.observer = observer;\n        }\n        concast.addObserver(observer);\n        return concast;\n    };\n    ObservableQuery.prototype.reobserve = function (newOptions, newNetworkStatus) {\n        return preventUnhandledRejection(this.reobserveAsConcast(newOptions, newNetworkStatus).promise.then(this.maskResult));\n    };\n    ObservableQuery.prototype.resubscribeAfterError = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        // If `lastError` is set in the current when the subscription is re-created,\n        // the subscription will immediately receive the error, which will\n        // cause it to terminate again. To avoid this, we first clear\n        // the last error/result from the `observableQuery` before re-starting\n        // the subscription, and restore the last value afterwards so that the\n        // subscription has a chance to stay open.\n        var last = this.last;\n        this.resetLastResults();\n        var subscription = this.subscribe.apply(this, args);\n        this.last = last;\n        return subscription;\n    };\n    // (Re)deliver the current result to this.observers without applying fetch\n    // policies or making network requests.\n    ObservableQuery.prototype.observe = function () {\n        this.reportResult(\n        // Passing false is important so that this.getCurrentResult doesn't\n        // save the fetchMore result as this.lastResult, causing it to be\n        // ignored due to the this.isDifferentFromLastResult check in\n        // this.reportResult.\n        this.getCurrentFullResult(false), this.variables);\n    };\n    ObservableQuery.prototype.reportResult = function (result, variables) {\n        var lastError = this.getLastError();\n        var isDifferent = this.isDifferentFromLastResult(result, variables);\n        // Update the last result even when isDifferentFromLastResult returns false,\n        // because the query may be using the @nonreactive directive, and we want to\n        // save the the latest version of any nonreactive subtrees (in case\n        // getCurrentResult is called), even though we skip broadcasting changes.\n        if (lastError || !result.partial || this.options.returnPartialData) {\n            this.updateLastResult(result, variables);\n        }\n        if (lastError || isDifferent) {\n            iterateObserversSafely(this.observers, \"next\", this.maskResult(result));\n        }\n    };\n    ObservableQuery.prototype.reportError = function (error, variables) {\n        // Since we don't get the current result on errors, only the error, we\n        // must mirror the updates that occur in QueryStore.markQueryError here\n        var errorResult = __assign(__assign({}, this.getLastResult()), { error: error, errors: error.graphQLErrors, networkStatus: NetworkStatus.error, loading: false });\n        this.updateLastResult(errorResult, variables);\n        iterateObserversSafely(this.observers, \"error\", (this.last.error = error));\n    };\n    ObservableQuery.prototype.hasObservers = function () {\n        return this.observers.size > 0;\n    };\n    ObservableQuery.prototype.tearDownQuery = function () {\n        if (this.isTornDown)\n            return;\n        if (this.concast && this.observer) {\n            this.concast.removeObserver(this.observer);\n            delete this.concast;\n            delete this.observer;\n        }\n        this.stopPolling();\n        // stop all active GraphQL subscriptions\n        this.subscriptions.forEach(function (sub) { return sub.unsubscribe(); });\n        this.subscriptions.clear();\n        this.queryManager.stopQuery(this.queryId);\n        this.observers.clear();\n        this.isTornDown = true;\n    };\n    ObservableQuery.prototype.transformDocument = function (document) {\n        return this.queryManager.transform(document);\n    };\n    ObservableQuery.prototype.maskResult = function (result) {\n        return result && \"data\" in result ? __assign(__assign({}, result), { data: this.queryManager.maskOperation({\n                document: this.query,\n                data: result.data,\n                fetchPolicy: this.options.fetchPolicy,\n                id: this.queryId,\n            }) }) : result;\n    };\n    /** @internal */\n    ObservableQuery.prototype.resetNotifications = function () {\n        this.cancelNotifyTimeout();\n        this.dirty = false;\n    };\n    ObservableQuery.prototype.cancelNotifyTimeout = function () {\n        if (this.notifyTimeout) {\n            clearTimeout(this.notifyTimeout);\n            this.notifyTimeout = void 0;\n        }\n    };\n    /** @internal */\n    ObservableQuery.prototype.scheduleNotify = function () {\n        var _this = this;\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        if (!this.notifyTimeout) {\n            this.notifyTimeout = setTimeout(function () { return _this.notify(); }, 0);\n        }\n    };\n    /** @internal */\n    ObservableQuery.prototype.notify = function () {\n        this.cancelNotifyTimeout();\n        if (this.dirty) {\n            if (this.options.fetchPolicy == \"cache-only\" ||\n                this.options.fetchPolicy == \"cache-and-network\" ||\n                !isNetworkRequestInFlight(this.queryInfo.networkStatus)) {\n                var diff = this.queryInfo.getDiff();\n                if (diff.fromOptimisticTransaction) {\n                    // If this diff came from an optimistic transaction, deliver the\n                    // current cache data to the ObservableQuery, but don't perform a\n                    // reobservation, since oq.reobserveCacheFirst might make a network\n                    // request, and we never want to trigger network requests in the\n                    // middle of optimistic updates.\n                    this.observe();\n                }\n                else {\n                    // Otherwise, make the ObservableQuery \"reobserve\" the latest data\n                    // using a temporary fetch policy of \"cache-first\", so complete cache\n                    // results have a chance to be delivered without triggering additional\n                    // network requests, even when options.fetchPolicy is \"network-only\"\n                    // or \"cache-and-network\". All other fetch policies are preserved by\n                    // this method, and are handled by calling oq.reobserve(). If this\n                    // reobservation is spurious, isDifferentFromLastResult still has a\n                    // chance to catch it before delivery to ObservableQuery subscribers.\n                    this.reobserveCacheFirst();\n                }\n            }\n        }\n        this.dirty = false;\n    };\n    // Reobserve with fetchPolicy effectively set to \"cache-first\", triggering\n    // delivery of any new data from the cache, possibly falling back to the network\n    // if any cache data are missing. This allows _complete_ cache results to be\n    // delivered without also kicking off unnecessary network requests when\n    // this.options.fetchPolicy is \"cache-and-network\" or \"network-only\". When\n    // this.options.fetchPolicy is any other policy (\"cache-first\", \"cache-only\",\n    // \"standby\", or \"no-cache\"), we call this.reobserve() as usual.\n    ObservableQuery.prototype.reobserveCacheFirst = function () {\n        var _a = this.options, fetchPolicy = _a.fetchPolicy, nextFetchPolicy = _a.nextFetchPolicy;\n        if (fetchPolicy === \"cache-and-network\" || fetchPolicy === \"network-only\") {\n            return this.reobserve({\n                fetchPolicy: \"cache-first\",\n                // Use a temporary nextFetchPolicy function that replaces itself with the\n                // previous nextFetchPolicy value and returns the original fetchPolicy.\n                nextFetchPolicy: function (currentFetchPolicy, context) {\n                    // Replace this nextFetchPolicy function in the options object with the\n                    // original this.options.nextFetchPolicy value.\n                    this.nextFetchPolicy = nextFetchPolicy;\n                    // If the original nextFetchPolicy value was a function, give it a\n                    // chance to decide what happens here.\n                    if (typeof this.nextFetchPolicy === \"function\") {\n                        return this.nextFetchPolicy(currentFetchPolicy, context);\n                    }\n                    // Otherwise go back to the original this.options.fetchPolicy.\n                    return fetchPolicy;\n                },\n            });\n        }\n        return this.reobserve();\n    };\n    /**\n     * @internal\n     * A slot used by the `useQuery` hook to indicate that `client.watchQuery`\n     * should not register the query immediately, but instead wait for the query to\n     * be started registered with the `QueryManager` when `useSyncExternalStore`\n     * actively subscribes to it.\n     */\n    ObservableQuery.inactiveOnCreation = new Slot();\n    return ObservableQuery;\n}(Observable));\nexport { ObservableQuery };\n// Necessary because the ObservableQuery constructor has a different\n// signature than the Observable constructor.\nfixObservableSubclass(ObservableQuery);\nfunction defaultSubscriptionObserverErrorCallback(error) {\n    globalThis.__DEV__ !== false && invariant.error(25, error.message, error.stack);\n}\nexport function logMissingFieldErrors(missing) {\n    if (globalThis.__DEV__ !== false && missing) {\n        globalThis.__DEV__ !== false && invariant.debug(26, missing);\n    }\n}\nfunction skipCacheDataFor(fetchPolicy /* `undefined` would mean `\"cache-first\"` */) {\n    return (fetchPolicy === \"network-only\" ||\n        fetchPolicy === \"no-cache\" ||\n        fetchPolicy === \"standby\");\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,aAAa,EAAEC,wBAAwB,QAAQ,oBAAoB;AAC5E,SAASC,SAAS,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,UAAU,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,yBAAyB,QAAS,uBAAuB;AAC7L,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,IAAI,QAAQ,UAAU;AAC/B,IAAIC,MAAM,GAAGC,MAAM,CAACD,MAAM;EAAEE,cAAc,GAAGD,MAAM,CAACC,cAAc;AAClE,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDrB,SAAS,CAACoB,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,EAAE,EAAE;IACzB,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAY;MAAEC,SAAS,GAAGF,EAAE,CAACE,SAAS;MAAEC,OAAO,GAAGH,EAAE,CAACG,OAAO;IAClF,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,eAAe,GAAGP,eAAe,CAACQ,kBAAkB,CAACC,QAAQ,CAAC,CAAC;IACnEH,KAAK,GAAGL,MAAM,CAACS,IAAI,CAAC,IAAI,EAAE,UAAUC,QAAQ,EAAE;MAC1C,IAAIJ,eAAe,EAAE;QACjBJ,YAAY,CAAC,SAAS,CAAC,CAACS,GAAG,CAACN,KAAK,CAACO,OAAO,EAAET,SAAS,CAAC;QACrDG,eAAe,GAAG,KAAK;MAC3B;MACA;MACA;MACA,IAAI;QACA,IAAIO,WAAW,GAAGH,QAAQ,CAACI,aAAa,CAACC,SAAS;QAClD,IAAIF,WAAW,IAAI,CAACA,WAAW,CAACG,KAAK,EAAE;UACnCH,WAAW,CAACG,KAAK,GAAGC,wCAAwC;QAChE;MACJ,CAAC,CACD,OAAOhB,EAAE,EAAE,CAAE;MACb,IAAIiB,KAAK,GAAG,CAACb,KAAK,CAACc,SAAS,CAACC,IAAI;MACjCf,KAAK,CAACc,SAAS,CAACE,GAAG,CAACX,QAAQ,CAAC;MAC7B;MACA,IAAIY,IAAI,GAAGjB,KAAK,CAACiB,IAAI;MACrB,IAAIA,IAAI,IAAIA,IAAI,CAACN,KAAK,EAAE;QACpBN,QAAQ,CAACM,KAAK,IAAIN,QAAQ,CAACM,KAAK,CAACM,IAAI,CAACN,KAAK,CAAC;MAChD,CAAC,MACI,IAAIM,IAAI,IAAIA,IAAI,CAACC,MAAM,EAAE;QAC1Bb,QAAQ,CAACc,IAAI,IAAId,QAAQ,CAACc,IAAI,CAACnB,KAAK,CAACoB,UAAU,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC;MACjE;MACA;MACA;MACA,IAAIL,KAAK,EAAE;QACP;QACA;QACA;QACA;QACAb,KAAK,CAACqB,SAAS,CAAC,CAAC,CAACC,KAAK,CAAC,YAAY,CAAE,CAAC,CAAC;MAC5C;MACA,OAAO,YAAY;QACf,IAAItB,KAAK,CAACc,SAAS,CAACS,MAAM,CAAClB,QAAQ,CAAC,IAAI,CAACL,KAAK,CAACc,SAAS,CAACC,IAAI,EAAE;UAC3Df,KAAK,CAACwB,aAAa,CAAC,CAAC;QACzB;MACJ,CAAC;IACL,CAAC,CAAC,IAAI,IAAI;IACVxB,KAAK,CAACc,SAAS,GAAG,IAAIW,GAAG,CAAC,CAAC;IAC3BzB,KAAK,CAAC0B,aAAa,GAAG,IAAID,GAAG,CAAC,CAAC;IAC/BzB,KAAK,CAAC2B,KAAK,GAAG,KAAK;IACnB;IACA3B,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3BE,KAAK,CAACH,YAAY,GAAGA,YAAY;IACjC;IACAG,KAAK,CAAC4B,gBAAgB,GAAGC,gBAAgB,CAAC9B,OAAO,CAAC+B,WAAW,CAAC;IAC9D9B,KAAK,CAAC+B,UAAU,GAAG,KAAK;IACxB/B,KAAK,CAACgC,eAAe,GAAGhC,KAAK,CAACgC,eAAe,CAACC,IAAI,CAACjC,KAAK,CAAC;IACzDA,KAAK,CAACoB,UAAU,GAAGpB,KAAK,CAACoB,UAAU,CAACa,IAAI,CAACjC,KAAK,CAAC;IAC/C,IAAIkC,EAAE,GAAGrC,YAAY,CAACsC,cAAc,CAACC,UAAU;MAAEC,EAAE,GAAGH,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;MAAEI,EAAE,GAAGD,EAAE,CAACP,WAAW;MAAES,kBAAkB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,EAAE;IAC3J,IAAIE,EAAE,GAAGzC,OAAO,CAAC+B,WAAW;MAAEA,WAAW,GAAGU,EAAE,KAAK,KAAK,CAAC,GAAGD,kBAAkB,GAAGC,EAAE;MACnF;MACAC,EAAE,GAAG1C,OAAO,CAAC2C,kBAAkB;MAC/B;MACAA,kBAAkB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGX,WAAW,KAAK,SAAS,GAAGS,kBAAkB,GAAIT,WAAY,GAAGW,EAAE;IACxGzC,KAAK,CAACD,OAAO,GAAG1B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0B,OAAO,CAAC,EAAE;MAC5C;MACA;MACA;MACA2C,kBAAkB,EAAEA,kBAAkB;MACtC;MACA;MACAZ,WAAW,EAAEA;IAAY,CAAC,CAAC;IAC/B9B,KAAK,CAACO,OAAO,GAAGT,SAAS,CAACS,OAAO,IAAIV,YAAY,CAAC8C,eAAe,CAAC,CAAC;IACnE,IAAIC,KAAK,GAAG/D,sBAAsB,CAACmB,KAAK,CAAC6C,KAAK,CAAC;IAC/C7C,KAAK,CAAC8C,SAAS,GAAGF,KAAK,IAAIA,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACG,IAAI,CAACC,KAAK;IACzD,OAAOhD,KAAK;EAChB;EACAR,MAAM,CAACyD,cAAc,CAACvD,eAAe,CAACwD,SAAS,EAAE,OAAO,EAAE;IACtD;IACA;IACA;IACA;IACAC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACrD,OAAO,CAAC8C,KAAK;IAC/C,CAAC;IACDQ,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF9D,MAAM,CAACyD,cAAc,CAACvD,eAAe,CAACwD,SAAS,EAAE,WAAW,EAAE;IAC1D;IACA;IACA;AACR;AACA;IACQC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACpD,OAAO,CAACwD,SAAS;IACjC,CAAC;IACDF,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF5D,eAAe,CAACwD,SAAS,CAAChC,MAAM,GAAG,YAAY;IAC3C,IAAIlB,KAAK,GAAG,IAAI;IAChB,OAAO,IAAIwD,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC1C;MACA;MACA;MACA,IAAIrD,QAAQ,GAAG;QACXc,IAAI,EAAE,SAAAA,CAAUD,MAAM,EAAE;UACpBuC,OAAO,CAACvC,MAAM,CAAC;UACf;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAlB,KAAK,CAACc,SAAS,CAACS,MAAM,CAAClB,QAAQ,CAAC;UAChC,IAAI,CAACL,KAAK,CAACc,SAAS,CAACC,IAAI,EAAE;YACvBf,KAAK,CAACH,YAAY,CAAC8D,WAAW,CAAC3D,KAAK,CAACO,OAAO,CAAC;UACjD;UACAqD,UAAU,CAAC,YAAY;YACnBC,YAAY,CAACC,WAAW,CAAC,CAAC;UAC9B,CAAC,EAAE,CAAC,CAAC;QACT,CAAC;QACDnD,KAAK,EAAE+C;MACX,CAAC;MACD,IAAIG,YAAY,GAAG7D,KAAK,CAAC+D,SAAS,CAAC1D,QAAQ,CAAC;IAChD,CAAC,CAAC;EACN,CAAC;EACD;EACAX,eAAe,CAACwD,SAAS,CAACc,SAAS,GAAG,YAAY;IAC9C,IAAI,CAAClE,SAAS,CAACkE,SAAS,CAAC,CAAC;EAC9B,CAAC;EACDtE,eAAe,CAACwD,SAAS,CAACe,oBAAoB,GAAG,UAAUC,gBAAgB,EAAE;IACzE,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;MAAEA,gBAAgB,GAAG,IAAI;IAAE;IAC5D;IACA,IAAIC,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;IACzC,IAAIC,aAAa,GAAG,IAAI,CAACvE,SAAS,CAACuE,aAAa,IAC3CF,UAAU,IAAIA,UAAU,CAACE,aAAc,IACxC5F,aAAa,CAAC6F,KAAK;IACvB,IAAIpD,MAAM,GAAG7C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8F,UAAU,CAAC,EAAE;MAAEI,OAAO,EAAE7F,wBAAwB,CAAC2F,aAAa,CAAC;MAAEA,aAAa,EAAEA;IAAc,CAAC,CAAC;IACnI,IAAIzE,EAAE,GAAG,IAAI,CAACG,OAAO,CAAC+B,WAAW;MAAEA,WAAW,GAAGlC,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,EAAE;IACnF;IACA;IACA;IACAiC,gBAAgB,CAACC,WAAW,CAAC;IACzB;IACA;IACA;IACA;IACA,IAAI,CAACjC,YAAY,CAAC2E,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC,CAAC4B,kBAAkB,EAAE;MAClE;IAAA,CACH,MACI,IAAI,IAAI,CAAC7C,gBAAgB,EAAE;MAC5B;MACA;MACA;MACA,IAAI,CAAC9B,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;IACnC,CAAC,MACI;MACD,IAAI4E,IAAI,GAAG,IAAI,CAAC5E,SAAS,CAAC6E,OAAO,CAAC,CAAC;MACnC,IAAID,IAAI,CAACE,QAAQ,IAAI,IAAI,CAAC7E,OAAO,CAAC8E,iBAAiB,EAAE;QACjD3D,MAAM,CAAC4D,IAAI,GAAGJ,IAAI,CAACxD,MAAM;MAC7B;MACA,IAAI1C,KAAK,CAAC0C,MAAM,CAAC4D,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QACxB5D,MAAM,CAAC4D,IAAI,GAAG,KAAK,CAAC;MACxB;MACA,IAAIJ,IAAI,CAACE,QAAQ,EAAE;QACf;QACA;QACA,OAAO1D,MAAM,CAAC6D,OAAO;QACrB;QACA;QACA;QACA,IAAIL,IAAI,CAACE,QAAQ,IACb1D,MAAM,CAACmD,aAAa,KAAK5F,aAAa,CAAC8F,OAAO,KAC7CzC,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,YAAY,CAAC,EAAE;UACjEZ,MAAM,CAACmD,aAAa,GAAG5F,aAAa,CAAC6F,KAAK;UAC1CpD,MAAM,CAACqD,OAAO,GAAG,KAAK;QAC1B;MACJ,CAAC,MACI;QACDrD,MAAM,CAAC6D,OAAO,GAAG,IAAI;MACzB;MACA;MACA;MACA;MACA;MACA,IAAI7D,MAAM,CAACmD,aAAa,KAAK5F,aAAa,CAAC6F,KAAK,KAC3CpD,MAAM,CAACP,KAAK,IAAIO,MAAM,CAAC8D,MAAM,CAAC,EAAE;QACjC9D,MAAM,CAACmD,aAAa,GAAG5F,aAAa,CAACkC,KAAK;MAC9C;MACA,IAAIsE,UAAU,CAACC,OAAO,KAAK,KAAK,IAC5B,CAACR,IAAI,CAACE,QAAQ,IACd,CAAC,IAAI,CAAC7E,OAAO,CAACoF,cAAc,IAC5B,CAACjE,MAAM,CAACqD,OAAO,IACf,CAACrD,MAAM,CAAC4D,IAAI,IACZ,CAAC5D,MAAM,CAACP,KAAK,EAAE;QACfyE,qBAAqB,CAACV,IAAI,CAACW,OAAO,CAAC;MACvC;IACJ;IACA,IAAInB,gBAAgB,EAAE;MAClB,IAAI,CAACoB,gBAAgB,CAACpE,MAAM,CAAC;IACjC;IACA,OAAOA,MAAM;EACjB,CAAC;EACDxB,eAAe,CAACwD,SAAS,CAACqC,gBAAgB,GAAG,UAAUrB,gBAAgB,EAAE;IACrE,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;MAAEA,gBAAgB,GAAG,IAAI;IAAE;IAC5D,OAAO,IAAI,CAAC9C,UAAU,CAAC,IAAI,CAAC6C,oBAAoB,CAACC,gBAAgB,CAAC,CAAC;EACvE,CAAC;EACD;EACA;EACAxE,eAAe,CAACwD,SAAS,CAACsC,yBAAyB,GAAG,UAAUC,SAAS,EAAElC,SAAS,EAAE;IAClF,IAAI,CAAC,IAAI,CAACtC,IAAI,EAAE;MACZ,OAAO,IAAI;IACf;IACA,IAAIyE,YAAY,GAAG,IAAI,CAAC7F,YAAY,CAAC2E,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC;IAChE,IAAI8C,WAAW,GAAG,IAAI,CAAC9F,YAAY,CAAC8F,WAAW;IAC/C,IAAI9C,KAAK,GAAG8C,WAAW,GAAGD,YAAY,CAACE,gBAAgB,GAAG,IAAI,CAAC/C,KAAK;IACpE,IAAIgD,iBAAiB,GAAGF,WAAW,IAAID,YAAY,CAACI,uBAAuB,GACvE,CAACzG,YAAY,CAACwD,KAAK,EAAE,IAAI,CAAC5B,IAAI,CAACC,MAAM,EAAEuE,SAAS,EAAE,IAAI,CAAClC,SAAS,CAAC,GAC/D,CAAC/E,KAAK,CAAC,IAAI,CAACyC,IAAI,CAACC,MAAM,EAAEuE,SAAS,CAAC;IACzC,OAAQI,iBAAiB,IAAKtC,SAAS,IAAI,CAAC/E,KAAK,CAAC,IAAI,CAACyC,IAAI,CAACsC,SAAS,EAAEA,SAAS,CAAE;EACtF,CAAC;EACD7D,eAAe,CAACwD,SAAS,CAAC6C,OAAO,GAAG,UAAUC,GAAG,EAAEC,kBAAkB,EAAE;IACnE,IAAIhF,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIA,IAAI,IACJA,IAAI,CAAC+E,GAAG,CAAC,KACR,CAACC,kBAAkB,IAAIzH,KAAK,CAACyC,IAAI,CAACsC,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,EAAE;MAChE,OAAOtC,IAAI,CAAC+E,GAAG,CAAC;IACpB;EACJ,CAAC;EACDtG,eAAe,CAACwD,SAAS,CAACkB,aAAa,GAAG,UAAU6B,kBAAkB,EAAE;IACpE,OAAO,IAAI,CAACF,OAAO,CAAC,QAAQ,EAAEE,kBAAkB,CAAC;EACrD,CAAC;EACDvG,eAAe,CAACwD,SAAS,CAACgD,YAAY,GAAG,UAAUD,kBAAkB,EAAE;IACnE,OAAO,IAAI,CAACF,OAAO,CAAC,OAAO,EAAEE,kBAAkB,CAAC;EACpD,CAAC;EACDvG,eAAe,CAACwD,SAAS,CAACiD,gBAAgB,GAAG,YAAY;IACrD,OAAO,IAAI,CAAClF,IAAI;IAChB,IAAI,CAACc,UAAU,GAAG,KAAK;EAC3B,CAAC;EACDrC,eAAe,CAACwD,SAAS,CAACkD,qBAAqB,GAAG,YAAY;IAC1D,IAAI,CAACvG,YAAY,CAACwG,WAAW,CAAC,IAAI,CAAC9F,OAAO,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIb,eAAe,CAACwD,SAAS,CAACoD,OAAO,GAAG,UAAU/C,SAAS,EAAE;IACrD,IAAI3D,EAAE;IACN,IAAI2G,gBAAgB,GAAG;MACnB;MACAC,YAAY,EAAE;IAClB,CAAC;IACD;IACA;IACA;IACA,IAAI1E,WAAW,GAAG,IAAI,CAAC/B,OAAO,CAAC+B,WAAW;IAC1C,IAAIA,WAAW,KAAK,UAAU,EAAE;MAC5ByE,gBAAgB,CAACzE,WAAW,GAAG,UAAU;IAC7C,CAAC,MACI;MACDyE,gBAAgB,CAACzE,WAAW,GAAG,cAAc;IACjD;IACA,IAAImD,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI3B,SAAS,IAAI9D,cAAc,CAACW,IAAI,CAACmD,SAAS,EAAE,WAAW,CAAC,EAAE;MAC1F,IAAIkD,QAAQ,GAAGxH,kBAAkB,CAAC,IAAI,CAAC4D,KAAK,CAAC;MAC7C,IAAI6D,IAAI,GAAGD,QAAQ,CAACE,mBAAmB;MACvC,IAAI,CAACD,IAAI,IAAI,CAACA,IAAI,CAACE,IAAI,CAAC,UAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACC,QAAQ,CAAC/D,IAAI,CAACC,KAAK,KAAK,WAAW;MAAE,CAAC,CAAC,EAAE;QACrFiC,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI3G,SAAS,CAACwI,IAAI,CAC1C,EAAE,EACFxD,SAAS,EACT,CAAC,CAAC3D,EAAE,GAAG6G,QAAQ,CAAC1D,IAAI,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoD,KAAK,KAAKyD,QAC5E,CAAC;MACL;IACJ;IACA,IAAIlD,SAAS,IAAI,CAAC/E,KAAK,CAAC,IAAI,CAACuB,OAAO,CAACwD,SAAS,EAAEA,SAAS,CAAC,EAAE;MACxD;MACAgD,gBAAgB,CAAChD,SAAS,GAAG,IAAI,CAACxD,OAAO,CAACwD,SAAS,GAAGlF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0B,OAAO,CAACwD,SAAS,CAAC,EAAEA,SAAS,CAAC;IACnH;IACA,IAAI,CAACzD,SAAS,CAACkH,cAAc,CAAC,CAAC;IAC/B,OAAO,IAAI,CAAC3F,SAAS,CAACkF,gBAAgB,EAAE9H,aAAa,CAAC6H,OAAO,CAAC;EAClE,CAAC;EACD;AACJ;AACA;EACI5G,eAAe,CAACwD,SAAS,CAAC+D,SAAS,GAAG,UAAUC,gBAAgB,EAAE;IAC9D,IAAIlH,KAAK,GAAG,IAAI;IAChB,IAAImH,eAAe,GAAG9I,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAG6I,gBAAgB,CAACrE,KAAK,GAAGqE,gBAAgB,GAAI7I,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0B,OAAO,CAAC,EAAE;MAAE8C,KAAK,EAAE,IAAI,CAAC9C,OAAO,CAAC8C;IAAM,CAAC,CAAC,EAAEqE,gBAAgB,CAAC,EAAE;MAAE3D,SAAS,EAAElF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0B,OAAO,CAACwD,SAAS,CAAC,EAAE2D,gBAAgB,CAAC3D,SAAS;IAAE,CAAC,CAAG,CAAC,EAAE;MAC/R;MACA;MACA;MACA;MACA;MACAzB,WAAW,EAAE;IAAW,CAAC,CAAC;IAC9BqF,eAAe,CAACtE,KAAK,GAAG,IAAI,CAACuE,iBAAiB,CAACD,eAAe,CAACtE,KAAK,CAAC;IACrE,IAAIwE,GAAG,GAAG,IAAI,CAACxH,YAAY,CAAC8C,eAAe,CAAC,CAAC;IAC7C;IACA;IACA;IACA;IACA;IACA,IAAI,CAACS,SAAS,GACV8D,gBAAgB,CAACrE,KAAK,GAClB,IAAI,CAACuE,iBAAiB,CAAC,IAAI,CAACrH,OAAO,CAAC8C,KAAK,CAAC,GACxCsE,eAAe,CAACtE,KAAK;IAC/B;IACA;IACA,IAAI/C,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIwH,qBAAqB,GAAGxH,SAAS,CAACuE,aAAa;IACnDvE,SAAS,CAACuE,aAAa,GAAG5F,aAAa,CAACwI,SAAS;IACjD,IAAIE,eAAe,CAACI,2BAA2B,EAAE;MAC7C,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;IACA,IAAIC,eAAe,GAAG,IAAIhG,GAAG,CAAC,CAAC;IAC/B,IAAIiG,WAAW,GAAGR,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACQ,WAAW;IAClH,IAAIC,QAAQ,GAAG,IAAI,CAAC5H,OAAO,CAAC+B,WAAW,KAAK,UAAU;IACtD,IAAI,CAAC6F,QAAQ,EAAE;MACXpJ,SAAS,CAACmJ,WAAW,EAAE,EAAE,CAAC;IAC9B;IACA,OAAO,IAAI,CAAC7H,YAAY,CACnB+H,UAAU,CAACP,GAAG,EAAEF,eAAe,EAAE1I,aAAa,CAACwI,SAAS,CAAC,CACzDY,IAAI,CAAC,UAAUC,eAAe,EAAE;MACjC9H,KAAK,CAACH,YAAY,CAAC8D,WAAW,CAAC0D,GAAG,CAAC;MACnC,IAAIvH,SAAS,CAACuE,aAAa,KAAK5F,aAAa,CAACwI,SAAS,EAAE;QACrDnH,SAAS,CAACuE,aAAa,GAAGiD,qBAAqB;MACnD;MACA,IAAIK,QAAQ,EAAE;QACV;QACA;QACA;QACA;QACA;QACA3H,KAAK,CAACH,YAAY,CAACkI,KAAK,CAACC,KAAK,CAAC;UAC3BC,MAAM,EAAE,SAAAA,CAAUF,KAAK,EAAE;YACrB,IAAIL,WAAW,GAAGR,gBAAgB,CAACQ,WAAW;YAC9C,IAAIA,WAAW,EAAE;cACbK,KAAK,CAACL,WAAW,CAAC;gBACd7E,KAAK,EAAE7C,KAAK,CAAC6C,KAAK;gBAClBU,SAAS,EAAEvD,KAAK,CAACuD,SAAS;gBAC1BsB,iBAAiB,EAAE,IAAI;gBACvBqD,UAAU,EAAE;cAChB,CAAC,EAAE,UAAUC,QAAQ,EAAE;gBACnB,OAAOT,WAAW,CAACS,QAAQ,EAAE;kBACzBL,eAAe,EAAEA,eAAe,CAAChD,IAAI;kBACrCvB,SAAS,EAAE4D,eAAe,CAAC5D;gBAC/B,CAAC,CAAC;cACN,CAAC,CAAC;YACN,CAAC,MACI;cACD;cACA;cACA;cACA;cACA;cACAwE,KAAK,CAACK,UAAU,CAAC;gBACbvF,KAAK,EAAEsE,eAAe,CAACtE,KAAK;gBAC5BU,SAAS,EAAE4D,eAAe,CAAC5D,SAAS;gBACpCuB,IAAI,EAAEgD,eAAe,CAAChD;cAC1B,CAAC,CAAC;YACN;UACJ,CAAC;UACDuD,cAAc,EAAE,SAAAA,CAAUC,KAAK,EAAE;YAC7B;YACA;YACAb,eAAe,CAACzG,GAAG,CAACsH,KAAK,CAACzF,KAAK,CAAC;UACpC;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIsB,UAAU,GAAGnE,KAAK,CAAC+F,OAAO,CAAC,QAAQ,CAAC;QACxC,IAAIjB,IAAI,GAAG4C,WAAW,CAACvD,UAAU,CAACW,IAAI,EAAE;UACpCgD,eAAe,EAAEA,eAAe,CAAChD,IAAI;UACrCvB,SAAS,EAAE4D,eAAe,CAAC5D;QAC/B,CAAC,CAAC;QACFvD,KAAK,CAACuI,YAAY,CAAClK,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8F,UAAU,CAAC,EAAE;UAAEE,aAAa,EAAEiD,qBAAqB;UAAE/C,OAAO,EAAE7F,wBAAwB,CAAC4I,qBAAqB,CAAC;UAAExC,IAAI,EAAEA;QAAK,CAAC,CAAC,EAAE9E,KAAK,CAACuD,SAAS,CAAC;MAC3L;MACA,OAAOvD,KAAK,CAACoB,UAAU,CAAC0G,eAAe,CAAC;IAC5C,CAAC,CAAC,CACGU,OAAO,CAAC,YAAY;MACrB;MACA;MACA;MACA;MACA;MACA,IAAIb,QAAQ,IAAI,CAACF,eAAe,CAACgB,GAAG,CAACzI,KAAK,CAAC6C,KAAK,CAAC,EAAE;QAC/C7C,KAAK,CAAC0I,mBAAmB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN,CAAC;EACD;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACIhJ,eAAe,CAACwD,SAAS,CAAClB,eAAe,GAAG,UAAUjC,OAAO,EAAE;IAC3D,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI6D,YAAY,GAAG,IAAI,CAAChE,YAAY,CAC/B8I,wBAAwB,CAAC;MAC1B9F,KAAK,EAAE9C,OAAO,CAAC6I,QAAQ;MACvBrF,SAAS,EAAExD,OAAO,CAACwD,SAAS;MAC5BsF,OAAO,EAAE9I,OAAO,CAAC8I;IACrB,CAAC,CAAC,CACG9E,SAAS,CAAC;MACX5C,IAAI,EAAE,SAAAA,CAAU2H,gBAAgB,EAAE;QAC9B,IAAIpB,WAAW,GAAG3H,OAAO,CAAC2H,WAAW;QACrC,IAAIA,WAAW,EAAE;UACb1H,KAAK,CAAC0H,WAAW,CAAC,UAAUS,QAAQ,EAAEY,aAAa,EAAE;YACjD,OAAOrB,WAAW,CAACS,QAAQ,EAAE9J,QAAQ,CAAC;cAAEyK,gBAAgB,EAAEA;YAAiB,CAAC,EAAEC,aAAa,CAAC,CAAC;UACjG,CAAC,CAAC;QACN;MACJ,CAAC;MACDpI,KAAK,EAAE,SAAAA,CAAUqI,GAAG,EAAE;QAClB,IAAIjJ,OAAO,CAACkJ,OAAO,EAAE;UACjBlJ,OAAO,CAACkJ,OAAO,CAACD,GAAG,CAAC;UACpB;QACJ;QACA/D,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI3G,SAAS,CAACoC,KAAK,CAAC,EAAE,EAAEqI,GAAG,CAAC;MAC5D;IACJ,CAAC,CAAC;IACF,IAAI,CAACtH,aAAa,CAACV,GAAG,CAAC6C,YAAY,CAAC;IACpC,OAAO,YAAY;MACf,IAAI7D,KAAK,CAAC0B,aAAa,CAACH,MAAM,CAACsC,YAAY,CAAC,EAAE;QAC1CA,YAAY,CAACC,WAAW,CAAC,CAAC;MAC9B;IACJ,CAAC;EACL,CAAC;EACDpE,eAAe,CAACwD,SAAS,CAACgG,UAAU,GAAG,UAAUC,UAAU,EAAE;IACzD,OAAO,IAAI,CAAC9H,SAAS,CAAC8H,UAAU,CAAC;EACrC,CAAC;EACDzJ,eAAe,CAACwD,SAAS,CAACkG,gBAAgB,GAAG,UAAUD,UAAU,EAAE;IAC/D,IAAIE,aAAa,GAAGzK,OAAO,CAAC,IAAI,CAACmB,OAAO,EAAEoJ,UAAU,IAAI,CAAC,CAAC,CAAC;IAC3D5J,MAAM,CAAC,IAAI,CAACQ,OAAO,EAAEsJ,aAAa,CAAC;EACvC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3J,eAAe,CAACwD,SAAS,CAACoG,YAAY,GAAG,UAAU/F,SAAS,EAAE;IAC1D,IAAI/E,KAAK,CAAC,IAAI,CAAC+E,SAAS,EAAEA,SAAS,CAAC,EAAE;MAClC;MACA;MACA;MACA,OAAO,IAAI,CAACzC,SAAS,CAACC,IAAI,GAAG,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGsC,OAAO,CAACC,OAAO,CAAC,CAAC;IAClE;IACA,IAAI,CAAC1D,OAAO,CAACwD,SAAS,GAAGA,SAAS;IAClC;IACA,IAAI,CAAC,IAAI,CAACzC,SAAS,CAACC,IAAI,EAAE;MACtB,OAAOyC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACpC,SAAS,CAAC;MAClB;MACAS,WAAW,EAAE,IAAI,CAAC/B,OAAO,CAAC2C,kBAAkB;MAC5Ca,SAAS,EAAEA;IACf,CAAC,EAAE9E,aAAa,CAAC6K,YAAY,CAAC;EAClC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI5J,eAAe,CAACwD,SAAS,CAACwE,WAAW,GAAG,UAAU6B,KAAK,EAAE;IACrD,IAAI1J,YAAY,GAAG,IAAI,CAACA,YAAY;IACpC,IAAID,EAAE,GAAGC,YAAY,CAACkI,KAAK,CAACrD,IAAI,CAAC;QAC7B7B,KAAK,EAAE,IAAI,CAAC9C,OAAO,CAAC8C,KAAK;QACzBU,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBsB,iBAAiB,EAAE,IAAI;QACvBqD,UAAU,EAAE;MAChB,CAAC,CAAC;MAAEhH,MAAM,GAAGtB,EAAE,CAACsB,MAAM;MAAE0D,QAAQ,GAAGhF,EAAE,CAACgF,QAAQ;IAC9C,IAAIa,SAAS,GAAG8D,KAAK,CAACrI,MAAM,EAAE;MAC1BqC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBqB,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB4E,YAAY,EAAEtI;IAClB,CAAC,CAAC;IACF,IAAIuE,SAAS,EAAE;MACX5F,YAAY,CAACkI,KAAK,CAACK,UAAU,CAAC;QAC1BvF,KAAK,EAAE,IAAI,CAAC9C,OAAO,CAAC8C,KAAK;QACzBiC,IAAI,EAAEW,SAAS;QACflC,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF1D,YAAY,CAAC4J,gBAAgB,CAAC,CAAC;IACnC;EACJ,CAAC;EACD;AACJ;AACA;EACI/J,eAAe,CAACwD,SAAS,CAACwG,YAAY,GAAG,UAAUlD,YAAY,EAAE;IAC7D,IAAI,CAACzG,OAAO,CAACyG,YAAY,GAAGA,YAAY;IACxC,IAAI,CAACmD,aAAa,CAAC,CAAC;EACxB,CAAC;EACD;AACJ;AACA;EACIjK,eAAe,CAACwD,SAAS,CAAC0G,WAAW,GAAG,YAAY;IAChD,IAAI,CAAC7J,OAAO,CAACyG,YAAY,GAAG,CAAC;IAC7B,IAAI,CAACmD,aAAa,CAAC,CAAC;EACxB,CAAC;EACD;EACAjK,eAAe,CAACwD,SAAS,CAAC2G,oBAAoB,GAAG,UAAUC,MAAM;EACjE;EACA;EACA;EACA;EACA/J,OAAO,EAAE;IACL,IAAIA,OAAO,CAACgK,eAAe,EAAE;MACzB,IAAInK,EAAE,GAAGG,OAAO,CAAC+B,WAAW;QAAEA,WAAW,GAAGlC,EAAE,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,EAAE;QAAEsC,EAAE,GAAGnC,OAAO,CAAC2C,kBAAkB;QAAEA,kBAAkB,GAAGR,EAAE,KAAK,KAAK,CAAC,GAAGJ,WAAW,GAAGI,EAAE;MACtK,IAAIJ,WAAW,KAAK,SAAS,EAAE;QAC3B;MAAA,CACH,MACI,IAAI,OAAO/B,OAAO,CAACgK,eAAe,KAAK,UAAU,EAAE;QACpD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAhK,OAAO,CAAC+B,WAAW,GAAG/B,OAAO,CAACgK,eAAe,CAACjI,WAAW,EAAE;UACvDgI,MAAM,EAAEA,MAAM;UACd/J,OAAO,EAAEA,OAAO;UAChBiK,UAAU,EAAE,IAAI;UAChBtH,kBAAkB,EAAEA;QACxB,CAAC,CAAC;MACN,CAAC,MACI,IAAIoH,MAAM,KAAK,mBAAmB,EAAE;QACrC/J,OAAO,CAAC+B,WAAW,GAAGY,kBAAkB;MAC5C,CAAC,MACI;QACD3C,OAAO,CAAC+B,WAAW,GAAG/B,OAAO,CAACgK,eAAe;MACjD;IACJ;IACA,OAAOhK,OAAO,CAAC+B,WAAW;EAC9B,CAAC;EACDpC,eAAe,CAACwD,SAAS,CAAC+G,KAAK,GAAG,UAAUlK,OAAO,EAAEmK,gBAAgB,EAAErH,KAAK,EAAE;IAC1E;IACA;IACA,IAAI/C,SAAS,GAAG,IAAI,CAACD,YAAY,CAACsK,gBAAgB,CAAC,IAAI,CAAC5J,OAAO,CAAC;IAChET,SAAS,CAACsK,kBAAkB,CAAC,IAAI,CAAC;IAClC,OAAO,IAAI,CAACvK,YAAY,CAAC,sBAAsB,CAAC,CAACC,SAAS,EAAEC,OAAO,EAAEmK,gBAAgB,EAAErH,KAAK,CAAC;EACjG,CAAC;EACD;EACAnD,eAAe,CAACwD,SAAS,CAACyG,aAAa,GAAG,YAAY;IAClD,IAAI3J,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,IAAI,CAACH,YAAY,CAACwK,OAAO,EAAE;MAC3B;IACJ;IACA,IAAIzK,EAAE,GAAG,IAAI;MAAE0K,WAAW,GAAG1K,EAAE,CAAC0K,WAAW;MAAE9D,YAAY,GAAG5G,EAAE,CAACG,OAAO,CAACyG,YAAY;IACnF,IAAI,CAACA,YAAY,IAAI,CAAC,IAAI,CAAC+D,YAAY,CAAC,CAAC,EAAE;MACvC,IAAID,WAAW,EAAE;QACbE,YAAY,CAACF,WAAW,CAACG,OAAO,CAAC;QACjC,OAAO,IAAI,CAACH,WAAW;MAC3B;MACA;IACJ;IACA,IAAIA,WAAW,IAAIA,WAAW,CAACI,QAAQ,KAAKlE,YAAY,EAAE;MACtD;IACJ;IACAjI,SAAS,CAACiI,YAAY,EAAE,EAAE,CAAC;IAC3B,IAAImE,IAAI,GAAGL,WAAW,KAAK,IAAI,CAACA,WAAW,GAAG,CAAC,CAAC,CAAC;IACjDK,IAAI,CAACD,QAAQ,GAAGlE,YAAY;IAC5B,IAAIoE,UAAU,GAAG,SAAAA,CAAA,EAAY;MACzB,IAAIhL,EAAE,EAAEsC,EAAE;MACV,IAAIlC,KAAK,CAACsK,WAAW,EAAE;QACnB,IAAI,CAAC5L,wBAAwB,CAACsB,KAAK,CAACF,SAAS,CAACuE,aAAa,CAAC,IACxD,EAAE,CAACnC,EAAE,GAAG,CAACtC,EAAE,GAAGI,KAAK,CAACD,OAAO,EAAE8K,eAAe,MAAM,IAAI,IAAI3I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9B,IAAI,CAACR,EAAE,CAAC,CAAC,EAAE;UACjGI,KAAK,CAACqB,SAAS,CAAC;YACZ;YACA;YACA;YACA;YACAS,WAAW,EAAE9B,KAAK,CAACD,OAAO,CAAC2C,kBAAkB,KAAK,UAAU,GACxD,UAAU,GACR;UACV,CAAC,EAAEjE,aAAa,CAACqM,IAAI,CAAC,CAACjD,IAAI,CAACiD,IAAI,EAAEA,IAAI,CAAC;QAC3C,CAAC,MACI;UACDA,IAAI,CAAC,CAAC;QACV;MACJ;IACJ,CAAC;IACD,IAAIA,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnB,IAAIH,IAAI,GAAG3K,KAAK,CAACsK,WAAW;MAC5B,IAAIK,IAAI,EAAE;QACNH,YAAY,CAACG,IAAI,CAACF,OAAO,CAAC;QAC1BE,IAAI,CAACF,OAAO,GAAG7G,UAAU,CAACgH,UAAU,EAAED,IAAI,CAACD,QAAQ,CAAC;MACxD;IACJ,CAAC;IACDI,IAAI,CAAC,CAAC;EACV,CAAC;EACDpL,eAAe,CAACwD,SAAS,CAACoC,gBAAgB,GAAG,UAAUG,SAAS,EAAElC,SAAS,EAAE;IACzE,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAG,IAAI,CAACA,SAAS;IAAE;IACxD,IAAI5C,KAAK,GAAG,IAAI,CAACuF,YAAY,CAAC,CAAC;IAC/B;IACA,IAAIvF,KAAK,IAAI,IAAI,CAACM,IAAI,IAAI,CAACzC,KAAK,CAAC+E,SAAS,EAAE,IAAI,CAACtC,IAAI,CAACsC,SAAS,CAAC,EAAE;MAC9D5C,KAAK,GAAG,KAAK,CAAC;IAClB;IACA,OAAQ,IAAI,CAACM,IAAI,GAAG5C,QAAQ,CAAC;MAAE6C,MAAM,EAAE,IAAI,CAACrB,YAAY,CAACkL,sBAAsB,GACvEtF,SAAS,GACP9G,SAAS,CAAC8G,SAAS,CAAC;MAAElC,SAAS,EAAEA;IAAU,CAAC,EAAG5C,KAAK,GAAG;MAAEA,KAAK,EAAEA;IAAM,CAAC,GAAG,IAAK,CAAC;EAC9F,CAAC;EACDjB,eAAe,CAACwD,SAAS,CAAC8H,kBAAkB,GAAG,UAAU7B,UAAU,EAAEe,gBAAgB,EAAE;IACnF,IAAIlK,KAAK,GAAG,IAAI;IAChB,IAAI,CAAC+B,UAAU,GAAG,KAAK;IACvB,IAAIkJ,oBAAoB;IACxB;IACA;IACA;IACAf,gBAAgB,KAAKzL,aAAa,CAAC6H,OAAO;IACtC;IACA;IACA4D,gBAAgB,KAAKzL,aAAa,CAACwI,SAAS;IAC5C;IACA;IACAiD,gBAAgB,KAAKzL,aAAa,CAACqM,IAAI;IAC3C;IACA,IAAII,YAAY,GAAG,IAAI,CAACnL,OAAO,CAACwD,SAAS;IACzC,IAAI4H,cAAc,GAAG,IAAI,CAACpL,OAAO,CAAC+B,WAAW;IAC7C,IAAIuH,aAAa,GAAGzK,OAAO,CAAC,IAAI,CAACmB,OAAO,EAAEoJ,UAAU,IAAI,CAAC,CAAC,CAAC;IAC3D,IAAIpJ,OAAO,GAAGkL,oBAAoB;IAC9B;IACA;IACA5B,aAAa,GACX9J,MAAM,CAAC,IAAI,CAACQ,OAAO,EAAEsJ,aAAa,CAAC;IACzC;IACA;IACA;IACA;IACA,IAAIxG,KAAK,GAAG,IAAI,CAACuE,iBAAiB,CAACrH,OAAO,CAAC8C,KAAK,CAAC;IACjD,IAAI,CAACO,SAAS,GAAGP,KAAK;IACtB,IAAI,CAACoI,oBAAoB,EAAE;MACvB;MACA,IAAI,CAACtB,aAAa,CAAC,CAAC;MACpB;MACA;MACA,IAAIR,UAAU,IACVA,UAAU,CAAC5F,SAAS,IACpB,CAAC/E,KAAK,CAAC2K,UAAU,CAAC5F,SAAS,EAAE2H,YAAY,CAAC;MAC1C;MACAnL,OAAO,CAAC+B,WAAW,KAAK,SAAS;MACjC;MACA;MACC/B,OAAO,CAAC+B,WAAW,KAAKqJ,cAAc;MACnC;MACA;MACA,OAAOpL,OAAO,CAACgK,eAAe,KAAK,UAAU,CAAC,EAAE;QACpD,IAAI,CAACF,oBAAoB,CAAC,mBAAmB,EAAE9J,OAAO,CAAC;QACvD,IAAImK,gBAAgB,KAAK,KAAK,CAAC,EAAE;UAC7BA,gBAAgB,GAAGzL,aAAa,CAAC6K,YAAY;QACjD;MACJ;IACJ;IACA,IAAI,CAAC1H,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAGC,gBAAgB,CAAC9B,OAAO,CAAC+B,WAAW,CAAC,CAAC;IACxF,IAAIsJ,yBAAyB,GAAG,SAAAA,CAAA,EAAY;MACxC,IAAIpL,KAAK,CAACqL,OAAO,KAAKA,OAAO,EAAE;QAC3BrL,KAAK,CAAC4B,gBAAgB,GAAG,KAAK;MAClC;IACJ,CAAC;IACD,IAAI2B,SAAS,GAAGxD,OAAO,CAACwD,SAAS,IAAIlF,QAAQ,CAAC,CAAC,CAAC,EAAE0B,OAAO,CAACwD,SAAS,CAAC;IACpE,IAAI3D,EAAE,GAAG,IAAI,CAACqK,KAAK,CAAClK,OAAO,EAAEmK,gBAAgB,EAAErH,KAAK,CAAC;MAAEwI,OAAO,GAAGzL,EAAE,CAACyL,OAAO;MAAEC,QAAQ,GAAG1L,EAAE,CAAC0L,QAAQ;IACnG,IAAIjL,QAAQ,GAAG;MACXc,IAAI,EAAE,SAAAA,CAAUD,MAAM,EAAE;QACpB,IAAI1C,KAAK,CAACwB,KAAK,CAACuD,SAAS,EAAEA,SAAS,CAAC,EAAE;UACnC6H,yBAAyB,CAAC,CAAC;UAC3BpL,KAAK,CAACuI,YAAY,CAACrH,MAAM,EAAEqC,SAAS,CAAC;QACzC;MACJ,CAAC;MACD5C,KAAK,EAAE,SAAAA,CAAUA,KAAK,EAAE;QACpB,IAAInC,KAAK,CAACwB,KAAK,CAACuD,SAAS,EAAEA,SAAS,CAAC,EAAE;UACnC;UACA;UACA;UACA,IAAI,CAACnE,aAAa,CAACuB,KAAK,CAAC,EAAE;YACvBA,KAAK,GAAG,IAAIxB,WAAW,CAAC;cAAEoM,YAAY,EAAE5K;YAAM,CAAC,CAAC;UACpD;UACAyK,yBAAyB,CAAC,CAAC;UAC3BpL,KAAK,CAACwL,WAAW,CAAC7K,KAAK,EAAE4C,SAAS,CAAC;QACvC;MACJ;IACJ,CAAC;IACD,IAAI,CAAC0H,oBAAoB,KAAKK,QAAQ,IAAI,CAAC,IAAI,CAACD,OAAO,CAAC,EAAE;MACtD;MACA;MACA,IAAI,IAAI,CAACA,OAAO,IAAI,IAAI,CAAChL,QAAQ,EAAE;QAC/B,IAAI,CAACgL,OAAO,CAACI,cAAc,CAAC,IAAI,CAACpL,QAAQ,CAAC;MAC9C;MACA,IAAI,CAACgL,OAAO,GAAGA,OAAO;MACtB,IAAI,CAAChL,QAAQ,GAAGA,QAAQ;IAC5B;IACAgL,OAAO,CAACK,WAAW,CAACrL,QAAQ,CAAC;IAC7B,OAAOgL,OAAO;EAClB,CAAC;EACD3L,eAAe,CAACwD,SAAS,CAAC7B,SAAS,GAAG,UAAU8H,UAAU,EAAEe,gBAAgB,EAAE;IAC1E,OAAOhL,yBAAyB,CAAC,IAAI,CAAC8L,kBAAkB,CAAC7B,UAAU,EAAEe,gBAAgB,CAAC,CAACyB,OAAO,CAAC9D,IAAI,CAAC,IAAI,CAACzG,UAAU,CAAC,CAAC;EACzH,CAAC;EACD1B,eAAe,CAACwD,SAAS,CAAC0I,qBAAqB,GAAG,YAAY;IAC1D,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI7K,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI,CAACkF,gBAAgB,CAAC,CAAC;IACvB,IAAItC,YAAY,GAAG,IAAI,CAACE,SAAS,CAACkI,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IACnD,IAAI,CAAC5K,IAAI,GAAGA,IAAI;IAChB,OAAO4C,YAAY;EACvB,CAAC;EACD;EACA;EACAnE,eAAe,CAACwD,SAAS,CAACsE,OAAO,GAAG,YAAY;IAC5C,IAAI,CAACe,YAAY;IACjB;IACA;IACA;IACA;IACA,IAAI,CAACtE,oBAAoB,CAAC,KAAK,CAAC,EAAE,IAAI,CAACV,SAAS,CAAC;EACrD,CAAC;EACD7D,eAAe,CAACwD,SAAS,CAACqF,YAAY,GAAG,UAAUrH,MAAM,EAAEqC,SAAS,EAAE;IAClE,IAAI2I,SAAS,GAAG,IAAI,CAAChG,YAAY,CAAC,CAAC;IACnC,IAAIiG,WAAW,GAAG,IAAI,CAAC3G,yBAAyB,CAACtE,MAAM,EAAEqC,SAAS,CAAC;IACnE;IACA;IACA;IACA;IACA,IAAI2I,SAAS,IAAI,CAAChL,MAAM,CAAC6D,OAAO,IAAI,IAAI,CAAChF,OAAO,CAAC8E,iBAAiB,EAAE;MAChE,IAAI,CAACS,gBAAgB,CAACpE,MAAM,EAAEqC,SAAS,CAAC;IAC5C;IACA,IAAI2I,SAAS,IAAIC,WAAW,EAAE;MAC1BpN,sBAAsB,CAAC,IAAI,CAAC+B,SAAS,EAAE,MAAM,EAAE,IAAI,CAACM,UAAU,CAACF,MAAM,CAAC,CAAC;IAC3E;EACJ,CAAC;EACDxB,eAAe,CAACwD,SAAS,CAACsI,WAAW,GAAG,UAAU7K,KAAK,EAAE4C,SAAS,EAAE;IAChE;IACA;IACA,IAAI6I,WAAW,GAAG/N,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+F,aAAa,CAAC,CAAC,CAAC,EAAE;MAAEzD,KAAK,EAAEA,KAAK;MAAEqE,MAAM,EAAErE,KAAK,CAAC0L,aAAa;MAAEhI,aAAa,EAAE5F,aAAa,CAACkC,KAAK;MAAE4D,OAAO,EAAE;IAAM,CAAC,CAAC;IACjK,IAAI,CAACe,gBAAgB,CAAC8G,WAAW,EAAE7I,SAAS,CAAC;IAC7CxE,sBAAsB,CAAC,IAAI,CAAC+B,SAAS,EAAE,OAAO,EAAG,IAAI,CAACG,IAAI,CAACN,KAAK,GAAGA,KAAM,CAAC;EAC9E,CAAC;EACDjB,eAAe,CAACwD,SAAS,CAACqH,YAAY,GAAG,YAAY;IACjD,OAAO,IAAI,CAACzJ,SAAS,CAACC,IAAI,GAAG,CAAC;EAClC,CAAC;EACDrB,eAAe,CAACwD,SAAS,CAAC1B,aAAa,GAAG,YAAY;IAClD,IAAI,IAAI,CAACO,UAAU,EACf;IACJ,IAAI,IAAI,CAACsJ,OAAO,IAAI,IAAI,CAAChL,QAAQ,EAAE;MAC/B,IAAI,CAACgL,OAAO,CAACI,cAAc,CAAC,IAAI,CAACpL,QAAQ,CAAC;MAC1C,OAAO,IAAI,CAACgL,OAAO;MACnB,OAAO,IAAI,CAAChL,QAAQ;IACxB;IACA,IAAI,CAACuJ,WAAW,CAAC,CAAC;IAClB;IACA,IAAI,CAAClI,aAAa,CAAC4K,OAAO,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOA,GAAG,CAACzI,WAAW,CAAC,CAAC;IAAE,CAAC,CAAC;IACxE,IAAI,CAACpC,aAAa,CAAC8K,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC3M,YAAY,CAAC4M,SAAS,CAAC,IAAI,CAAClM,OAAO,CAAC;IACzC,IAAI,CAACO,SAAS,CAAC0L,KAAK,CAAC,CAAC;IACtB,IAAI,CAACzK,UAAU,GAAG,IAAI;EAC1B,CAAC;EACDrC,eAAe,CAACwD,SAAS,CAACkE,iBAAiB,GAAG,UAAUwB,QAAQ,EAAE;IAC9D,OAAO,IAAI,CAAC/I,YAAY,CAAC6M,SAAS,CAAC9D,QAAQ,CAAC;EAChD,CAAC;EACDlJ,eAAe,CAACwD,SAAS,CAAC9B,UAAU,GAAG,UAAUF,MAAM,EAAE;IACrD,OAAOA,MAAM,IAAI,MAAM,IAAIA,MAAM,GAAG7C,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6C,MAAM,CAAC,EAAE;MAAE4D,IAAI,EAAE,IAAI,CAACjF,YAAY,CAAC8M,aAAa,CAAC;QACnG/D,QAAQ,EAAE,IAAI,CAAC/F,KAAK;QACpBiC,IAAI,EAAE5D,MAAM,CAAC4D,IAAI;QACjBhD,WAAW,EAAE,IAAI,CAAC/B,OAAO,CAAC+B,WAAW;QACrC8K,EAAE,EAAE,IAAI,CAACrM;MACb,CAAC;IAAE,CAAC,CAAC,GAAGW,MAAM;EACtB,CAAC;EACD;EACAxB,eAAe,CAACwD,SAAS,CAAC2J,kBAAkB,GAAG,YAAY;IACvD,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACnL,KAAK,GAAG,KAAK;EACtB,CAAC;EACDjC,eAAe,CAACwD,SAAS,CAAC4J,mBAAmB,GAAG,YAAY;IACxD,IAAI,IAAI,CAACC,aAAa,EAAE;MACpBvC,YAAY,CAAC,IAAI,CAACuC,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,KAAK,CAAC;IAC/B;EACJ,CAAC;EACD;EACArN,eAAe,CAACwD,SAAS,CAAC8J,cAAc,GAAG,YAAY;IACnD,IAAIhN,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAAC2B,KAAK,EACV;IACJ,IAAI,CAACA,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC,IAAI,CAACoL,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAGnJ,UAAU,CAAC,YAAY;QAAE,OAAO5D,KAAK,CAACiN,MAAM,CAAC,CAAC;MAAE,CAAC,EAAE,CAAC,CAAC;IAC9E;EACJ,CAAC;EACD;EACAvN,eAAe,CAACwD,SAAS,CAAC+J,MAAM,GAAG,YAAY;IAC3C,IAAI,CAACH,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACnL,KAAK,EAAE;MACZ,IAAI,IAAI,CAAC5B,OAAO,CAAC+B,WAAW,IAAI,YAAY,IACxC,IAAI,CAAC/B,OAAO,CAAC+B,WAAW,IAAI,mBAAmB,IAC/C,CAACpD,wBAAwB,CAAC,IAAI,CAACoB,SAAS,CAACuE,aAAa,CAAC,EAAE;QACzD,IAAIK,IAAI,GAAG,IAAI,CAAC5E,SAAS,CAAC6E,OAAO,CAAC,CAAC;QACnC,IAAID,IAAI,CAACwI,yBAAyB,EAAE;UAChC;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC1F,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACkB,mBAAmB,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,CAAC/G,KAAK,GAAG,KAAK;EACtB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAjC,eAAe,CAACwD,SAAS,CAACwF,mBAAmB,GAAG,YAAY;IACxD,IAAI9I,EAAE,GAAG,IAAI,CAACG,OAAO;MAAE+B,WAAW,GAAGlC,EAAE,CAACkC,WAAW;MAAEiI,eAAe,GAAGnK,EAAE,CAACmK,eAAe;IACzF,IAAIjI,WAAW,KAAK,mBAAmB,IAAIA,WAAW,KAAK,cAAc,EAAE;MACvE,OAAO,IAAI,CAACT,SAAS,CAAC;QAClBS,WAAW,EAAE,aAAa;QAC1B;QACA;QACAiI,eAAe,EAAE,SAAAA,CAAUoD,kBAAkB,EAAEtE,OAAO,EAAE;UACpD;UACA;UACA,IAAI,CAACkB,eAAe,GAAGA,eAAe;UACtC;UACA;UACA,IAAI,OAAO,IAAI,CAACA,eAAe,KAAK,UAAU,EAAE;YAC5C,OAAO,IAAI,CAACA,eAAe,CAACoD,kBAAkB,EAAEtE,OAAO,CAAC;UAC5D;UACA;UACA,OAAO/G,WAAW;QACtB;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACT,SAAS,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3B,eAAe,CAACQ,kBAAkB,GAAG,IAAIZ,IAAI,CAAC,CAAC;EAC/C,OAAOI,eAAe;AAC1B,CAAC,CAACZ,UAAU,CAAE;AACd,SAASY,eAAe;AACxB;AACA;AACAV,qBAAqB,CAACU,eAAe,CAAC;AACtC,SAASkB,wCAAwCA,CAACD,KAAK,EAAE;EACrDsE,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI3G,SAAS,CAACoC,KAAK,CAAC,EAAE,EAAEA,KAAK,CAACyM,OAAO,EAAEzM,KAAK,CAAC0M,KAAK,CAAC;AACnF;AACA,OAAO,SAASjI,qBAAqBA,CAACC,OAAO,EAAE;EAC3C,IAAIJ,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIG,OAAO,EAAE;IACzCJ,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI3G,SAAS,CAAC+O,KAAK,CAAC,EAAE,EAAEjI,OAAO,CAAC;EAChE;AACJ;AACA,SAASxD,gBAAgBA,CAACC,WAAW,CAAC,8CAA8C;EAChF,OAAQA,WAAW,KAAK,cAAc,IAClCA,WAAW,KAAK,UAAU,IAC1BA,WAAW,KAAK,SAAS;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}