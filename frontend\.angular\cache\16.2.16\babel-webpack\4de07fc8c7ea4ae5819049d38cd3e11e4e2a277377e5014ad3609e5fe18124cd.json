{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ReunionSchedulerComponent {\n  static {\n    this.ɵfac = function ReunionSchedulerComponent_Factory(t) {\n      return new (t || ReunionSchedulerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionSchedulerComponent,\n      selectors: [[\"app-reunion-scheduler\"]],\n      decls: 2,\n      vars: 0,\n      template: function ReunionSchedulerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"reunion-scheduler works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLXNjaGVkdWxlci5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1zY2hlZHVsZXIvcmV1bmlvbi1zY2hlZHVsZXIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ReunionSchedulerComponent", "selectors", "decls", "vars", "template", "ReunionSchedulerComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-scheduler\\reunion-scheduler.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-scheduler\\reunion-scheduler.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-reunion-scheduler',\r\n  templateUrl: './reunion-scheduler.component.html',\r\n  styleUrls: ['./reunion-scheduler.component.css']\r\n})\r\nexport class ReunionSchedulerComponent {\r\n\r\n}\r\n\r\n", "<p>reunion-scheduler works!</p>\r\n"], "mappings": ";AAOA,OAAM,MAAOA,yBAAyB;;;uBAAzBA,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPtCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,+BAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}