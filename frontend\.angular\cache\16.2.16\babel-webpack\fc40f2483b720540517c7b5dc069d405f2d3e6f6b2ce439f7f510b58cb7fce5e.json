{"ast": null, "code": "\"use strict\";\n\nconst {\n  ApolloLink,\n  Observable\n} = require(\"@apollo/client/core\");\nconst {\n  createSignalIfSupported,\n  fallbackHttpConfig,\n  parseAndCheckHttpResponse,\n  rewriteURIForGET,\n  selectHttpOptionsAndBody,\n  selectURI,\n  serializeFetchParameter\n} = require(\"@apollo/client/link/http\");\nconst extractFiles = require(\"extract-files/public/extractFiles.js\");\nconst formDataAppendFile = require(\"./formDataAppendFile.js\");\nconst isExtractableFile = require(\"./isExtractableFile.js\");\n\n/**\n * Creates a\n * [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link)\n * for [Apollo Client](https://apollographql.com/docs/react) that fetches a\n * [GraphQL multipart request](https://github.com/jaydenseric/graphql-multipart-request-spec)\n * if the GraphQL variables contain files (by default\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/FileList),\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob), or\n * [`ReactNativeFile`](#class-reactnativefile) instances), or else fetches a\n * regular\n * [GraphQL POST or GET request](https://apollographql.com/docs/apollo-server/requests)\n * (depending on the config and GraphQL operation).\n *\n * Some of the options are similar to the\n * [`createHttpLink` options](https://apollographql.com/docs/react/api/link/apollo-link-http/#httplink-constructor-options).\n * @see [GraphQL multipart request spec](https://github.com/jaydenseric/graphql-multipart-request-spec).\n * @kind function\n * @name createUploadLink\n * @param {object} options Options.\n * @param {string} [options.uri=\"/graphql\"] GraphQL endpoint URI.\n * @param {boolean} [options.useGETForQueries] Should GET be used to fetch queries, if there are no files to upload.\n * @param {ExtractableFileMatcher} [options.isExtractableFile=isExtractableFile] Customizes how files are matched in the GraphQL operation for extraction.\n * @param {class} [options.FormData] [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) implementation to use, defaulting to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) global.\n * @param {FormDataFileAppender} [options.formDataAppendFile=formDataAppendFile] Customizes how extracted files are appended to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance.\n * @param {Function} [options.fetch] [`fetch`](https://fetch.spec.whatwg.org) implementation to use, defaulting to the [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch) global.\n * @param {FetchOptions} [options.fetchOptions] [`fetch` options]{@link FetchOptions}; overridden by upload requirements.\n * @param {string} [options.credentials] Overrides `options.fetchOptions.credentials`.\n * @param {object} [options.headers] Merges with and overrides `options.fetchOptions.headers`.\n * @param {boolean} [options.includeExtensions=false] Toggles sending `extensions` fields to the GraphQL server.\n * @returns {ApolloLink} A [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { createUploadLink } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { createUploadLink } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const createUploadLink = require(\"apollo-upload-client/public/createUploadLink.js\");\n * ```\n * @example <caption>A basic Apollo Client setup.</caption>\n * ```js\n * import { ApolloClient, InMemoryCache } from \"@apollo/client\";\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n *\n * const client = new ApolloClient({\n *   cache: new InMemoryCache(),\n *   link: createUploadLink(),\n * });\n * ```\n */\nmodule.exports = function createUploadLink({\n  uri: fetchUri = \"/graphql\",\n  useGETForQueries,\n  isExtractableFile: customIsExtractableFile = isExtractableFile,\n  FormData: CustomFormData,\n  formDataAppendFile: customFormDataAppendFile = formDataAppendFile,\n  fetch: customFetch,\n  fetchOptions,\n  credentials,\n  headers,\n  includeExtensions\n} = {}) {\n  const linkConfig = {\n    http: {\n      includeExtensions\n    },\n    options: fetchOptions,\n    credentials,\n    headers\n  };\n  return new ApolloLink(operation => {\n    const context = operation.getContext();\n    const {\n      // Apollo Studio client awareness `name` and `version` can be configured\n      // via `ApolloClient` constructor options:\n      // https://apollographql.com/docs/studio/client-awareness/#using-apollo-server-and-apollo-client\n      clientAwareness: {\n        name,\n        version\n      } = {},\n      headers\n    } = context;\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: {\n        // Client awareness headers can be overridden by context `headers`.\n        ...(name && {\n          \"apollographql-client-name\": name\n        }),\n        ...(version && {\n          \"apollographql-client-version\": version\n        }),\n        ...headers\n      }\n    };\n    const {\n      options,\n      body\n    } = selectHttpOptionsAndBody(operation, fallbackHttpConfig, linkConfig, contextConfig);\n    const {\n      clone,\n      files\n    } = extractFiles(body, \"\", customIsExtractableFile);\n    let uri = selectURI(operation, fetchUri);\n    if (files.size) {\n      // Automatically set by `fetch` when the `body` is a `FormData` instance.\n      delete options.headers[\"content-type\"];\n\n      // GraphQL multipart request spec:\n      // https://github.com/jaydenseric/graphql-multipart-request-spec\n\n      const RuntimeFormData = CustomFormData || FormData;\n      const form = new RuntimeFormData();\n      form.append(\"operations\", serializeFetchParameter(clone, \"Payload\"));\n      const map = {};\n      let i = 0;\n      files.forEach(paths => {\n        map[++i] = paths;\n      });\n      form.append(\"map\", JSON.stringify(map));\n      i = 0;\n      files.forEach((paths, file) => {\n        customFormDataAppendFile(form, ++i, file);\n      });\n      options.body = form;\n    } else {\n      if (useGETForQueries &&\n      // If the operation contains some mutations GET shouldn’t be used.\n      !operation.query.definitions.some(definition => definition.kind === \"OperationDefinition\" && definition.operation === \"mutation\")) options.method = \"GET\";\n      if (options.method === \"GET\") {\n        const {\n          newURI,\n          parseError\n        } = rewriteURIForGET(uri, body);\n        if (parseError)\n          // Apollo’s `HttpLink` uses `fromError` for this, but it’s not\n          // exported from `@apollo/client/link/http`.\n          return new Observable(observer => {\n            observer.error(parseError);\n          });\n        uri = newURI;\n      } else options.body = serializeFetchParameter(clone, \"Payload\");\n    }\n    const {\n      controller\n    } = createSignalIfSupported();\n    if (controller) {\n      if (options.signal)\n        // Respect the user configured abort controller signal.\n        options.signal.aborted ?\n        // Signal already aborted, so immediately abort.\n        controller.abort() :\n        // Signal not already aborted, so setup a listener to abort when it\n        // does.\n        options.signal.addEventListener(\"abort\", () => {\n          controller.abort();\n        }, {\n          // Prevent a memory leak if the user configured abort controller\n          // is long lasting, or controls multiple things.\n          once: true\n        });\n      options.signal = controller.signal;\n    }\n    const runtimeFetch = customFetch || fetch;\n    return new Observable(observer => {\n      // Used to track if the observable is being cleaned up.\n      let cleaningUp;\n      runtimeFetch(uri, options).then(response => {\n        // Forward the response on the context.\n        operation.setContext({\n          response\n        });\n        return response;\n      }).then(parseAndCheckHttpResponse(operation)).then(result => {\n        observer.next(result);\n        observer.complete();\n      }).catch(error => {\n        // If the observable is being cleaned up, there is no need to call\n        // next or error because there are no more subscribers. An error after\n        // cleanup begins is likely from the cleanup function aborting the\n        // fetch.\n        if (!cleaningUp) {\n          // For errors such as an invalid fetch URI there will be no GraphQL\n          // result with errors or data to forward.\n          if (error.result && error.result.errors && error.result.data) observer.next(error.result);\n          observer.error(error);\n        }\n      });\n\n      // Cleanup function.\n      return () => {\n        cleaningUp = true;\n\n        // Abort fetch. It’s ok to signal an abort even when not fetching.\n        if (controller) controller.abort();\n      };\n    });\n  });\n};", "map": {"version": 3, "names": ["ApolloLink", "Observable", "require", "createSignalIfSupported", "fallbackHttpConfig", "parseAndCheckHttpResponse", "rewriteURIForGET", "selectHttpOptionsAndBody", "selectURI", "serializeFetchParameter", "extractFiles", "formDataAppendFile", "isExtractableFile", "module", "exports", "createUploadLink", "uri", "<PERSON><PERSON><PERSON>", "useGETForQueries", "customIsExtractableFile", "FormData", "CustomFormData", "customFormDataAppendFile", "fetch", "customFetch", "fetchOptions", "credentials", "headers", "includeExtensions", "linkConfig", "http", "options", "operation", "context", "getContext", "clientAwareness", "name", "version", "contextConfig", "body", "clone", "files", "size", "RuntimeFormData", "form", "append", "map", "i", "for<PERSON>ach", "paths", "JSON", "stringify", "file", "query", "definitions", "some", "definition", "kind", "method", "newURI", "parseError", "observer", "error", "controller", "signal", "aborted", "abort", "addEventListener", "once", "runtimeFetch", "cleaningUp", "then", "response", "setContext", "result", "next", "complete", "catch", "errors", "data"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-upload-client/public/createUploadLink.js"], "sourcesContent": ["\"use strict\";\n\nconst { ApolloLink, Observable } = require(\"@apollo/client/core\");\nconst {\n  createSignalIfSupported,\n  fallbackHttpConfig,\n  parseAndCheckHttpResponse,\n  rewriteURIForGET,\n  selectHttpOptionsAndBody,\n  selectURI,\n  serializeFetchParameter,\n} = require(\"@apollo/client/link/http\");\nconst extractFiles = require(\"extract-files/public/extractFiles.js\");\nconst formDataAppendFile = require(\"./formDataAppendFile.js\");\nconst isExtractableFile = require(\"./isExtractableFile.js\");\n\n/**\n * Creates a\n * [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link)\n * for [Apollo Client](https://apollographql.com/docs/react) that fetches a\n * [GraphQL multipart request](https://github.com/jaydenseric/graphql-multipart-request-spec)\n * if the GraphQL variables contain files (by default\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/FileList),\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob), or\n * [`ReactNativeFile`](#class-reactnativefile) instances), or else fetches a\n * regular\n * [GraphQL POST or GET request](https://apollographql.com/docs/apollo-server/requests)\n * (depending on the config and GraphQL operation).\n *\n * Some of the options are similar to the\n * [`createHttpLink` options](https://apollographql.com/docs/react/api/link/apollo-link-http/#httplink-constructor-options).\n * @see [GraphQL multipart request spec](https://github.com/jaydenseric/graphql-multipart-request-spec).\n * @kind function\n * @name createUploadLink\n * @param {object} options Options.\n * @param {string} [options.uri=\"/graphql\"] GraphQL endpoint URI.\n * @param {boolean} [options.useGETForQueries] Should GET be used to fetch queries, if there are no files to upload.\n * @param {ExtractableFileMatcher} [options.isExtractableFile=isExtractableFile] Customizes how files are matched in the GraphQL operation for extraction.\n * @param {class} [options.FormData] [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) implementation to use, defaulting to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) global.\n * @param {FormDataFileAppender} [options.formDataAppendFile=formDataAppendFile] Customizes how extracted files are appended to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance.\n * @param {Function} [options.fetch] [`fetch`](https://fetch.spec.whatwg.org) implementation to use, defaulting to the [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch) global.\n * @param {FetchOptions} [options.fetchOptions] [`fetch` options]{@link FetchOptions}; overridden by upload requirements.\n * @param {string} [options.credentials] Overrides `options.fetchOptions.credentials`.\n * @param {object} [options.headers] Merges with and overrides `options.fetchOptions.headers`.\n * @param {boolean} [options.includeExtensions=false] Toggles sending `extensions` fields to the GraphQL server.\n * @returns {ApolloLink} A [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { createUploadLink } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { createUploadLink } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const createUploadLink = require(\"apollo-upload-client/public/createUploadLink.js\");\n * ```\n * @example <caption>A basic Apollo Client setup.</caption>\n * ```js\n * import { ApolloClient, InMemoryCache } from \"@apollo/client\";\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n *\n * const client = new ApolloClient({\n *   cache: new InMemoryCache(),\n *   link: createUploadLink(),\n * });\n * ```\n */\nmodule.exports = function createUploadLink({\n  uri: fetchUri = \"/graphql\",\n  useGETForQueries,\n  isExtractableFile: customIsExtractableFile = isExtractableFile,\n  FormData: CustomFormData,\n  formDataAppendFile: customFormDataAppendFile = formDataAppendFile,\n  fetch: customFetch,\n  fetchOptions,\n  credentials,\n  headers,\n  includeExtensions,\n} = {}) {\n  const linkConfig = {\n    http: { includeExtensions },\n    options: fetchOptions,\n    credentials,\n    headers,\n  };\n\n  return new ApolloLink((operation) => {\n    const context = operation.getContext();\n    const {\n      // Apollo Studio client awareness `name` and `version` can be configured\n      // via `ApolloClient` constructor options:\n      // https://apollographql.com/docs/studio/client-awareness/#using-apollo-server-and-apollo-client\n      clientAwareness: { name, version } = {},\n      headers,\n    } = context;\n\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: {\n        // Client awareness headers can be overridden by context `headers`.\n        ...(name && { \"apollographql-client-name\": name }),\n        ...(version && { \"apollographql-client-version\": version }),\n        ...headers,\n      },\n    };\n\n    const { options, body } = selectHttpOptionsAndBody(\n      operation,\n      fallbackHttpConfig,\n      linkConfig,\n      contextConfig\n    );\n\n    const { clone, files } = extractFiles(body, \"\", customIsExtractableFile);\n\n    let uri = selectURI(operation, fetchUri);\n\n    if (files.size) {\n      // Automatically set by `fetch` when the `body` is a `FormData` instance.\n      delete options.headers[\"content-type\"];\n\n      // GraphQL multipart request spec:\n      // https://github.com/jaydenseric/graphql-multipart-request-spec\n\n      const RuntimeFormData = CustomFormData || FormData;\n\n      const form = new RuntimeFormData();\n\n      form.append(\"operations\", serializeFetchParameter(clone, \"Payload\"));\n\n      const map = {};\n      let i = 0;\n      files.forEach((paths) => {\n        map[++i] = paths;\n      });\n      form.append(\"map\", JSON.stringify(map));\n\n      i = 0;\n      files.forEach((paths, file) => {\n        customFormDataAppendFile(form, ++i, file);\n      });\n\n      options.body = form;\n    } else {\n      if (\n        useGETForQueries &&\n        // If the operation contains some mutations GET shouldn’t be used.\n        !operation.query.definitions.some(\n          (definition) =>\n            definition.kind === \"OperationDefinition\" &&\n            definition.operation === \"mutation\"\n        )\n      )\n        options.method = \"GET\";\n\n      if (options.method === \"GET\") {\n        const { newURI, parseError } = rewriteURIForGET(uri, body);\n        if (parseError)\n          // Apollo’s `HttpLink` uses `fromError` for this, but it’s not\n          // exported from `@apollo/client/link/http`.\n          return new Observable((observer) => {\n            observer.error(parseError);\n          });\n        uri = newURI;\n      } else options.body = serializeFetchParameter(clone, \"Payload\");\n    }\n\n    const { controller } = createSignalIfSupported();\n\n    if (controller) {\n      if (options.signal)\n        // Respect the user configured abort controller signal.\n        options.signal.aborted\n          ? // Signal already aborted, so immediately abort.\n            controller.abort()\n          : // Signal not already aborted, so setup a listener to abort when it\n            // does.\n            options.signal.addEventListener(\n              \"abort\",\n              () => {\n                controller.abort();\n              },\n              {\n                // Prevent a memory leak if the user configured abort controller\n                // is long lasting, or controls multiple things.\n                once: true,\n              }\n            );\n\n      options.signal = controller.signal;\n    }\n\n    const runtimeFetch = customFetch || fetch;\n\n    return new Observable((observer) => {\n      // Used to track if the observable is being cleaned up.\n      let cleaningUp;\n\n      runtimeFetch(uri, options)\n        .then((response) => {\n          // Forward the response on the context.\n          operation.setContext({ response });\n          return response;\n        })\n        .then(parseAndCheckHttpResponse(operation))\n        .then((result) => {\n          observer.next(result);\n          observer.complete();\n        })\n        .catch((error) => {\n          // If the observable is being cleaned up, there is no need to call\n          // next or error because there are no more subscribers. An error after\n          // cleanup begins is likely from the cleanup function aborting the\n          // fetch.\n          if (!cleaningUp) {\n            // For errors such as an invalid fetch URI there will be no GraphQL\n            // result with errors or data to forward.\n            if (error.result && error.result.errors && error.result.data)\n              observer.next(error.result);\n\n            observer.error(error);\n          }\n        });\n\n      // Cleanup function.\n      return () => {\n        cleaningUp = true;\n\n        // Abort fetch. It’s ok to signal an abort even when not fetching.\n        if (controller) controller.abort();\n      };\n    });\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAM;EAAEA,UAAU;EAAEC;AAAW,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AACjE,MAAM;EACJC,uBAAuB;EACvBC,kBAAkB;EAClBC,yBAAyB;EACzBC,gBAAgB;EAChBC,wBAAwB;EACxBC,SAAS;EACTC;AACF,CAAC,GAAGP,OAAO,CAAC,0BAA0B,CAAC;AACvC,MAAMQ,YAAY,GAAGR,OAAO,CAAC,sCAAsC,CAAC;AACpE,MAAMS,kBAAkB,GAAGT,OAAO,CAAC,yBAAyB,CAAC;AAC7D,MAAMU,iBAAiB,GAAGV,OAAO,CAAC,wBAAwB,CAAC;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAW,MAAM,CAACC,OAAO,GAAG,SAASC,gBAAgBA,CAAC;EACzCC,GAAG,EAAEC,QAAQ,GAAG,UAAU;EAC1BC,gBAAgB;EAChBN,iBAAiB,EAAEO,uBAAuB,GAAGP,iBAAiB;EAC9DQ,QAAQ,EAAEC,cAAc;EACxBV,kBAAkB,EAAEW,wBAAwB,GAAGX,kBAAkB;EACjEY,KAAK,EAAEC,WAAW;EAClBC,YAAY;EACZC,WAAW;EACXC,OAAO;EACPC;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAE;MAAEF;IAAkB,CAAC;IAC3BG,OAAO,EAAEN,YAAY;IACrBC,WAAW;IACXC;EACF,CAAC;EAED,OAAO,IAAI3B,UAAU,CAAEgC,SAAS,IAAK;IACnC,MAAMC,OAAO,GAAGD,SAAS,CAACE,UAAU,CAAC,CAAC;IACtC,MAAM;MACJ;MACA;MACA;MACAC,eAAe,EAAE;QAAEC,IAAI;QAAEC;MAAQ,CAAC,GAAG,CAAC,CAAC;MACvCV;IACF,CAAC,GAAGM,OAAO;IAEX,MAAMK,aAAa,GAAG;MACpBR,IAAI,EAAEG,OAAO,CAACH,IAAI;MAClBC,OAAO,EAAEE,OAAO,CAACR,YAAY;MAC7BC,WAAW,EAAEO,OAAO,CAACP,WAAW;MAChCC,OAAO,EAAE;QACP;QACA,IAAIS,IAAI,IAAI;UAAE,2BAA2B,EAAEA;QAAK,CAAC,CAAC;QAClD,IAAIC,OAAO,IAAI;UAAE,8BAA8B,EAAEA;QAAQ,CAAC,CAAC;QAC3D,GAAGV;MACL;IACF,CAAC;IAED,MAAM;MAAEI,OAAO;MAAEQ;IAAK,CAAC,GAAGhC,wBAAwB,CAChDyB,SAAS,EACT5B,kBAAkB,EAClByB,UAAU,EACVS,aACF,CAAC;IAED,MAAM;MAAEE,KAAK;MAAEC;IAAM,CAAC,GAAG/B,YAAY,CAAC6B,IAAI,EAAE,EAAE,EAAEpB,uBAAuB,CAAC;IAExE,IAAIH,GAAG,GAAGR,SAAS,CAACwB,SAAS,EAAEf,QAAQ,CAAC;IAExC,IAAIwB,KAAK,CAACC,IAAI,EAAE;MACd;MACA,OAAOX,OAAO,CAACJ,OAAO,CAAC,cAAc,CAAC;;MAEtC;MACA;;MAEA,MAAMgB,eAAe,GAAGtB,cAAc,IAAID,QAAQ;MAElD,MAAMwB,IAAI,GAAG,IAAID,eAAe,CAAC,CAAC;MAElCC,IAAI,CAACC,MAAM,CAAC,YAAY,EAAEpC,uBAAuB,CAAC+B,KAAK,EAAE,SAAS,CAAC,CAAC;MAEpE,MAAMM,GAAG,GAAG,CAAC,CAAC;MACd,IAAIC,CAAC,GAAG,CAAC;MACTN,KAAK,CAACO,OAAO,CAAEC,KAAK,IAAK;QACvBH,GAAG,CAAC,EAAEC,CAAC,CAAC,GAAGE,KAAK;MAClB,CAAC,CAAC;MACFL,IAAI,CAACC,MAAM,CAAC,KAAK,EAAEK,IAAI,CAACC,SAAS,CAACL,GAAG,CAAC,CAAC;MAEvCC,CAAC,GAAG,CAAC;MACLN,KAAK,CAACO,OAAO,CAAC,CAACC,KAAK,EAAEG,IAAI,KAAK;QAC7B9B,wBAAwB,CAACsB,IAAI,EAAE,EAAEG,CAAC,EAAEK,IAAI,CAAC;MAC3C,CAAC,CAAC;MAEFrB,OAAO,CAACQ,IAAI,GAAGK,IAAI;IACrB,CAAC,MAAM;MACL,IACE1B,gBAAgB;MAChB;MACA,CAACc,SAAS,CAACqB,KAAK,CAACC,WAAW,CAACC,IAAI,CAC9BC,UAAU,IACTA,UAAU,CAACC,IAAI,KAAK,qBAAqB,IACzCD,UAAU,CAACxB,SAAS,KAAK,UAC7B,CAAC,EAEDD,OAAO,CAAC2B,MAAM,GAAG,KAAK;MAExB,IAAI3B,OAAO,CAAC2B,MAAM,KAAK,KAAK,EAAE;QAC5B,MAAM;UAAEC,MAAM;UAAEC;QAAW,CAAC,GAAGtD,gBAAgB,CAACU,GAAG,EAAEuB,IAAI,CAAC;QAC1D,IAAIqB,UAAU;UACZ;UACA;UACA,OAAO,IAAI3D,UAAU,CAAE4D,QAAQ,IAAK;YAClCA,QAAQ,CAACC,KAAK,CAACF,UAAU,CAAC;UAC5B,CAAC,CAAC;QACJ5C,GAAG,GAAG2C,MAAM;MACd,CAAC,MAAM5B,OAAO,CAACQ,IAAI,GAAG9B,uBAAuB,CAAC+B,KAAK,EAAE,SAAS,CAAC;IACjE;IAEA,MAAM;MAAEuB;IAAW,CAAC,GAAG5D,uBAAuB,CAAC,CAAC;IAEhD,IAAI4D,UAAU,EAAE;MACd,IAAIhC,OAAO,CAACiC,MAAM;QAChB;QACAjC,OAAO,CAACiC,MAAM,CAACC,OAAO;QAClB;QACAF,UAAU,CAACG,KAAK,CAAC,CAAC;QAClB;QACA;QACAnC,OAAO,CAACiC,MAAM,CAACG,gBAAgB,CAC7B,OAAO,EACP,MAAM;UACJJ,UAAU,CAACG,KAAK,CAAC,CAAC;QACpB,CAAC,EACD;UACE;UACA;UACAE,IAAI,EAAE;QACR,CACF,CAAC;MAEPrC,OAAO,CAACiC,MAAM,GAAGD,UAAU,CAACC,MAAM;IACpC;IAEA,MAAMK,YAAY,GAAG7C,WAAW,IAAID,KAAK;IAEzC,OAAO,IAAItB,UAAU,CAAE4D,QAAQ,IAAK;MAClC;MACA,IAAIS,UAAU;MAEdD,YAAY,CAACrD,GAAG,EAAEe,OAAO,CAAC,CACvBwC,IAAI,CAAEC,QAAQ,IAAK;QAClB;QACAxC,SAAS,CAACyC,UAAU,CAAC;UAAED;QAAS,CAAC,CAAC;QAClC,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACDD,IAAI,CAAClE,yBAAyB,CAAC2B,SAAS,CAAC,CAAC,CAC1CuC,IAAI,CAAEG,MAAM,IAAK;QAChBb,QAAQ,CAACc,IAAI,CAACD,MAAM,CAAC;QACrBb,QAAQ,CAACe,QAAQ,CAAC,CAAC;MACrB,CAAC,CAAC,CACDC,KAAK,CAAEf,KAAK,IAAK;QAChB;QACA;QACA;QACA;QACA,IAAI,CAACQ,UAAU,EAAE;UACf;UACA;UACA,IAAIR,KAAK,CAACY,MAAM,IAAIZ,KAAK,CAACY,MAAM,CAACI,MAAM,IAAIhB,KAAK,CAACY,MAAM,CAACK,IAAI,EAC1DlB,QAAQ,CAACc,IAAI,CAACb,KAAK,CAACY,MAAM,CAAC;UAE7Bb,QAAQ,CAACC,KAAK,CAACA,KAAK,CAAC;QACvB;MACF,CAAC,CAAC;;MAEJ;MACA,OAAO,MAAM;QACXQ,UAAU,GAAG,IAAI;;QAEjB;QACA,IAAIP,UAAU,EAAEA,UAAU,CAACG,KAAK,CAAC,CAAC;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}