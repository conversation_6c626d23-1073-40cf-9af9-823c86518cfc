{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserdetailsRoutingModule } from './userdetails-routing.module';\nimport { UserdetailsComponent } from './userdetails.component';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nexport class UserdetailsModule {\n  static {\n    this.ɵfac = function UserdetailsModule_Factory(t) {\n      return new (t || UserdetailsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UserdetailsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, UserdetailsRoutingModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserdetailsModule, {\n    declarations: [UserdetailsComponent],\n    imports: [CommonModule, UserdetailsRoutingModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UserdetailsRoutingModule", "UserdetailsComponent", "FormsModule", "UserdetailsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\userdetails\\userdetails.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { UserdetailsRoutingModule } from './userdetails-routing.module';\r\nimport { UserdetailsComponent } from './userdetails.component';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    UserdetailsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    UserdetailsRoutingModule,\r\n    FormsModule\r\n  ]\r\n})\r\nexport class UserdetailsModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,WAAW,QAAQ,gBAAgB;;AAa5C,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAL1BJ,YAAY,EACZC,wBAAwB,EACxBE,WAAW;IAAA;EAAA;;;2EAGFC,iBAAiB;IAAAC,YAAA,GAR1BH,oBAAoB;IAAAI,OAAA,GAGpBN,YAAY,EACZC,wBAAwB,EACxBE,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}