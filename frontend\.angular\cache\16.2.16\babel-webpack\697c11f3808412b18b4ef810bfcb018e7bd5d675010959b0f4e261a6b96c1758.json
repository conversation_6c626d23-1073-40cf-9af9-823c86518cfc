{"ast": null, "code": "import { ApolloLink } from \"./ApolloLink.js\";\nexport var split = ApolloLink.split;", "map": {"version": 3, "names": ["ApolloLink", "split"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/split.js"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\nexport var split = ApolloLink.split;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,KAAK,GAAGD,UAAU,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}