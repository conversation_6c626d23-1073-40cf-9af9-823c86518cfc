{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Optional, Inject, NgModule } from '@angular/core';\nimport { Observable, queueScheduler, observable, from } from 'rxjs';\nimport { NetworkStatus, ApolloClient, gql as gql$1 } from '@apollo/client/core';\nimport { map, startWith, observeOn } from 'rxjs/operators';\nfunction fromPromise(promiseFn) {\n  return new Observable(subscriber => {\n    promiseFn().then(result => {\n      if (!subscriber.closed) {\n        subscriber.next(result);\n        subscriber.complete();\n      }\n    }, error => {\n      if (!subscriber.closed) {\n        subscriber.error(error);\n      }\n    });\n    return () => subscriber.unsubscribe();\n  });\n}\nfunction useMutationLoading(source, enabled) {\n  if (!enabled) {\n    return source.pipe(map(result => ({\n      ...result,\n      loading: false\n    })));\n  }\n  return source.pipe(startWith({\n    loading: true\n  }), map(result => ({\n    ...result,\n    loading: !!result.loading\n  })));\n}\nclass ZoneScheduler {\n  zone;\n  constructor(zone) {\n    this.zone = zone;\n  }\n  now = Date.now ? Date.now : () => +new Date();\n  schedule(work, delay = 0, state) {\n    return this.zone.run(() => queueScheduler.schedule(work, delay, state));\n  }\n}\nfunction fixObservable(obs) {\n  obs[observable] = () => obs;\n  return obs;\n}\nfunction wrapWithZone(obs, ngZone) {\n  return obs.pipe(observeOn(new ZoneScheduler(ngZone)));\n}\nfunction pickFlag(flags, flag, defaultValue) {\n  return flags && typeof flags[flag] !== 'undefined' ? flags[flag] : defaultValue;\n}\nfunction useInitialLoading(obsQuery) {\n  return function useInitialLoadingOperator(source) {\n    return new Observable(function useInitialLoadingSubscription(subscriber) {\n      const currentResult = obsQuery.getCurrentResult();\n      const {\n        loading,\n        errors,\n        error,\n        partial,\n        data\n      } = currentResult;\n      const {\n        partialRefetch,\n        fetchPolicy\n      } = obsQuery.options;\n      const hasError = errors || error;\n      if (partialRefetch && partial && (!data || Object.keys(data).length === 0) && fetchPolicy !== 'cache-only' && !loading && !hasError) {\n        subscriber.next({\n          ...currentResult,\n          loading: true,\n          networkStatus: NetworkStatus.loading\n        });\n      }\n      return source.subscribe(subscriber);\n    });\n  };\n}\nclass QueryRef {\n  obsQuery;\n  valueChanges;\n  queryId;\n  constructor(obsQuery, ngZone, options) {\n    this.obsQuery = obsQuery;\n    const wrapped = wrapWithZone(from(fixObservable(this.obsQuery)), ngZone);\n    this.valueChanges = options.useInitialLoading ? wrapped.pipe(useInitialLoading(this.obsQuery)) : wrapped;\n    this.queryId = this.obsQuery.queryId;\n  }\n  // ObservableQuery's methods\n  get options() {\n    return this.obsQuery.options;\n  }\n  get variables() {\n    return this.obsQuery.variables;\n  }\n  result() {\n    return this.obsQuery.result();\n  }\n  getCurrentResult() {\n    return this.obsQuery.getCurrentResult();\n  }\n  getLastResult() {\n    return this.obsQuery.getLastResult();\n  }\n  getLastError() {\n    return this.obsQuery.getLastError();\n  }\n  resetLastResults() {\n    return this.obsQuery.resetLastResults();\n  }\n  refetch(variables) {\n    return this.obsQuery.refetch(variables);\n  }\n  fetchMore(fetchMoreOptions) {\n    return this.obsQuery.fetchMore(fetchMoreOptions);\n  }\n  subscribeToMore(options) {\n    // XXX: there's a bug in apollo-client typings\n    // it should not inherit types from ObservableQuery\n    return this.obsQuery.subscribeToMore(options);\n  }\n  updateQuery(mapFn) {\n    return this.obsQuery.updateQuery(mapFn);\n  }\n  stopPolling() {\n    return this.obsQuery.stopPolling();\n  }\n  startPolling(pollInterval) {\n    return this.obsQuery.startPolling(pollInterval);\n  }\n  setOptions(opts) {\n    return this.obsQuery.setOptions(opts);\n  }\n  setVariables(variables) {\n    return this.obsQuery.setVariables(variables);\n  }\n}\nconst APOLLO_FLAGS = new InjectionToken('APOLLO_FLAGS');\nconst APOLLO_OPTIONS = new InjectionToken('APOLLO_OPTIONS');\nconst APOLLO_NAMED_OPTIONS = new InjectionToken('APOLLO_NAMED_OPTIONS');\nclass ApolloBase {\n  ngZone;\n  flags;\n  _client;\n  useInitialLoading;\n  useMutationLoading;\n  constructor(ngZone, flags, _client) {\n    this.ngZone = ngZone;\n    this.flags = flags;\n    this._client = _client;\n    this.useInitialLoading = pickFlag(flags, 'useInitialLoading', false);\n    this.useMutationLoading = pickFlag(flags, 'useMutationLoading', false);\n  }\n  watchQuery(options) {\n    return new QueryRef(this.ensureClient().watchQuery({\n      ...options\n    }), this.ngZone, {\n      useInitialLoading: this.useInitialLoading,\n      ...options\n    });\n  }\n  query(options) {\n    return fromPromise(() => this.ensureClient().query({\n      ...options\n    }));\n  }\n  mutate(options) {\n    return useMutationLoading(fromPromise(() => this.ensureClient().mutate({\n      ...options\n    })), options.useMutationLoading ?? this.useMutationLoading);\n  }\n  subscribe(options, extra) {\n    const obs = from(fixObservable(this.ensureClient().subscribe({\n      ...options\n    })));\n    return extra && extra.useZone !== true ? obs : wrapWithZone(obs, this.ngZone);\n  }\n  /**\n   * Get an instance of ApolloClient\n   * @deprecated use `apollo.client` instead\n   */\n  getClient() {\n    return this.client;\n  }\n  /**\n   * Set a new instance of ApolloClient\n   * Remember to clean up the store before setting a new client.\n   * @deprecated use `apollo.client = client` instead\n   *\n   * @param client ApolloClient instance\n   */\n  setClient(client) {\n    this.client = client;\n  }\n  /**\n   * Get an instance of ApolloClient\n   */\n  get client() {\n    return this._client;\n  }\n  /**\n   * Set a new instance of ApolloClient\n   * Remember to clean up the store before setting a new client.\n   *\n   * @param client ApolloClient instance\n   */\n  set client(client) {\n    if (this._client) {\n      throw new Error('Client has been already defined');\n    }\n    this._client = client;\n  }\n  ensureClient() {\n    this.checkInstance();\n    return this._client;\n  }\n  checkInstance() {\n    if (!this._client) {\n      throw new Error('Client has not been defined yet');\n    }\n  }\n}\nclass Apollo extends ApolloBase {\n  _ngZone;\n  map = new Map();\n  constructor(_ngZone, apolloOptions, apolloNamedOptions, flags) {\n    super(_ngZone, flags);\n    this._ngZone = _ngZone;\n    if (apolloOptions) {\n      this.createDefault(apolloOptions);\n    }\n    if (apolloNamedOptions && typeof apolloNamedOptions === 'object') {\n      for (let name in apolloNamedOptions) {\n        if (apolloNamedOptions.hasOwnProperty(name)) {\n          const options = apolloNamedOptions[name];\n          this.create(options, name);\n        }\n      }\n    }\n  }\n  /**\n   * Create an instance of ApolloClient\n   * @param options Options required to create ApolloClient\n   * @param name client's name\n   */\n  create(options, name) {\n    if (isDefault(name)) {\n      this.createDefault(options);\n    } else {\n      this.createNamed(name, options);\n    }\n  }\n  /**\n   * Use a default ApolloClient\n   */\n  default() {\n    return this;\n  }\n  /**\n   * Use a named ApolloClient\n   * @param name client's name\n   */\n  use(name) {\n    if (isDefault(name)) {\n      return this.default();\n    }\n    return this.map.get(name);\n  }\n  /**\n   * Create a default ApolloClient, same as `apollo.create(options)`\n   * @param options ApolloClient's options\n   */\n  createDefault(options) {\n    if (this.getClient()) {\n      throw new Error('Apollo has been already created.');\n    }\n    return this.setClient(new ApolloClient(options));\n  }\n  /**\n   * Create a named ApolloClient, same as `apollo.create(options, name)`\n   * @param name client's name\n   * @param options ApolloClient's options\n   */\n  createNamed(name, options) {\n    if (this.map.has(name)) {\n      throw new Error(`Client ${name} has been already created`);\n    }\n    this.map.set(name, new ApolloBase(this._ngZone, this.flags, new ApolloClient(options)));\n  }\n  /**\n   * Remember to clean up the store before removing a client\n   * @param name client's name\n   */\n  removeClient(name) {\n    if (isDefault(name)) {\n      this._client = undefined;\n    } else {\n      this.map.delete(name);\n    }\n  }\n  static ɵfac = function Apollo_Factory(t) {\n    return new (t || Apollo)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(APOLLO_OPTIONS, 8), i0.ɵɵinject(APOLLO_NAMED_OPTIONS, 8), i0.ɵɵinject(APOLLO_FLAGS, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Apollo,\n    factory: Apollo.ɵfac\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Apollo, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [APOLLO_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APOLLO_NAMED_OPTIONS]\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APOLLO_FLAGS]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nfunction isDefault(name) {\n  return !name || name === 'default';\n}\nconst PROVIDERS = [Apollo];\nclass ApolloModule {\n  static ɵfac = function ApolloModule_Factory(t) {\n    return new (t || ApolloModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ApolloModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: PROVIDERS\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ApolloModule, [{\n    type: NgModule,\n    args: [{\n      providers: PROVIDERS\n    }]\n  }], null, null);\n})();\nclass Query {\n  apollo;\n  document;\n  client = 'default';\n  constructor(apollo) {\n    this.apollo = apollo;\n  }\n  watch(variables, options) {\n    return this.apollo.use(this.client).watchQuery({\n      ...options,\n      variables,\n      query: this.document\n    });\n  }\n  fetch(variables, options) {\n    return this.apollo.use(this.client).query({\n      ...options,\n      variables,\n      query: this.document\n    });\n  }\n  static ɵfac = function Query_Factory(t) {\n    return new (t || Query)(i0.ɵɵinject(Apollo));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Query,\n    factory: Query.ɵfac\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Query, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Apollo\n    }];\n  }, null);\n})();\nclass Mutation {\n  apollo;\n  document;\n  client = 'default';\n  constructor(apollo) {\n    this.apollo = apollo;\n  }\n  mutate(variables, options) {\n    return this.apollo.use(this.client).mutate({\n      ...options,\n      variables,\n      mutation: this.document\n    });\n  }\n  static ɵfac = function Mutation_Factory(t) {\n    return new (t || Mutation)(i0.ɵɵinject(Apollo));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Mutation,\n    factory: Mutation.ɵfac\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Mutation, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Apollo\n    }];\n  }, null);\n})();\nclass Subscription {\n  apollo;\n  document;\n  client = 'default';\n  constructor(apollo) {\n    this.apollo = apollo;\n  }\n  subscribe(variables, options, extra) {\n    return this.apollo.use(this.client).subscribe({\n      ...options,\n      variables,\n      query: this.document\n    }, extra);\n  }\n  static ɵfac = function Subscription_Factory(t) {\n    return new (t || Subscription)(i0.ɵɵinject(Apollo));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Subscription,\n    factory: Subscription.ɵfac\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Subscription, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: Apollo\n    }];\n  }, null);\n})();\nfunction typedGQLTag(literals, ...placeholders) {\n  return gql$1(literals, ...placeholders);\n}\nconst gql = typedGQLTag;\nconst graphql = typedGQLTag;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { APOLLO_FLAGS, APOLLO_NAMED_OPTIONS, APOLLO_OPTIONS, Apollo, ApolloBase, ApolloModule, Mutation, Query, QueryRef, Subscription, gql, graphql };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Optional", "Inject", "NgModule", "Observable", "queueScheduler", "observable", "from", "NetworkStatus", "ApolloClient", "gql", "gql$1", "map", "startWith", "observeOn", "fromPromise", "promiseFn", "subscriber", "then", "result", "closed", "next", "complete", "error", "unsubscribe", "useMutationLoading", "source", "enabled", "pipe", "loading", "ZoneScheduler", "zone", "constructor", "now", "Date", "schedule", "work", "delay", "state", "run", "fixObservable", "obs", "wrapWithZone", "ngZone", "pickFlag", "flags", "flag", "defaultValue", "useInitialLoading", "obsQuery", "useInitialLoadingOperator", "useInitialLoadingSubscription", "currentResult", "getCurrentResult", "errors", "partial", "data", "partialRefetch", "fetchPolicy", "options", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "networkStatus", "subscribe", "QueryRef", "valueChanges", "queryId", "wrapped", "variables", "getLastResult", "getLastError", "resetLastResults", "refetch", "fetchMore", "fetchMoreOptions", "subscribeToMore", "updateQuery", "mapFn", "stopPolling", "startPolling", "pollInterval", "setOptions", "opts", "setVariables", "APOLLO_FLAGS", "APOLLO_OPTIONS", "APOLLO_NAMED_OPTIONS", "ApolloBase", "_client", "watch<PERSON><PERSON>y", "ensureClient", "query", "mutate", "extra", "useZone", "getClient", "client", "setClient", "Error", "checkInstance", "Apollo", "_ngZone", "Map", "apolloOptions", "apolloNamedOptions", "createDefault", "name", "hasOwnProperty", "create", "isDefault", "createNamed", "default", "use", "get", "has", "set", "removeClient", "undefined", "delete", "ɵfac", "Apollo_Factory", "t", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "decorators", "args", "PROVIDERS", "ApolloModule", "ApolloModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "Query", "apollo", "document", "watch", "fetch", "Query_Factory", "Mutation", "mutation", "Mutation_Factory", "Subscription", "Subscription_Factory", "typedGQLTag", "literals", "placeholders", "graphql"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-angular/fesm2022/ngApollo.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Optional, Inject, NgModule } from '@angular/core';\nimport { Observable, queueScheduler, observable, from } from 'rxjs';\nimport { NetworkStatus, ApolloClient, gql as gql$1 } from '@apollo/client/core';\nimport { map, startWith, observeOn } from 'rxjs/operators';\n\nfunction fromPromise(promiseFn) {\n    return new Observable(subscriber => {\n        promiseFn().then(result => {\n            if (!subscriber.closed) {\n                subscriber.next(result);\n                subscriber.complete();\n            }\n        }, error => {\n            if (!subscriber.closed) {\n                subscriber.error(error);\n            }\n        });\n        return () => subscriber.unsubscribe();\n    });\n}\nfunction useMutationLoading(source, enabled) {\n    if (!enabled) {\n        return source.pipe(map(result => ({\n            ...result,\n            loading: false,\n        })));\n    }\n    return source.pipe(startWith({\n        loading: true,\n    }), map(result => ({\n        ...result,\n        loading: !!result.loading,\n    })));\n}\nclass ZoneScheduler {\n    zone;\n    constructor(zone) {\n        this.zone = zone;\n    }\n    now = Date.now ? Date.now : () => +new Date();\n    schedule(work, delay = 0, state) {\n        return this.zone.run(() => queueScheduler.schedule(work, delay, state));\n    }\n}\nfunction fixObservable(obs) {\n    obs[observable] = () => obs;\n    return obs;\n}\nfunction wrapWithZone(obs, ngZone) {\n    return obs.pipe(observeOn(new ZoneScheduler(ngZone)));\n}\nfunction pickFlag(flags, flag, defaultValue) {\n    return flags && typeof flags[flag] !== 'undefined' ? flags[flag] : defaultValue;\n}\n\nfunction useInitialLoading(obsQuery) {\n    return function useInitialLoadingOperator(source) {\n        return new Observable(function useInitialLoadingSubscription(subscriber) {\n            const currentResult = obsQuery.getCurrentResult();\n            const { loading, errors, error, partial, data } = currentResult;\n            const { partialRefetch, fetchPolicy } = obsQuery.options;\n            const hasError = errors || error;\n            if (partialRefetch &&\n                partial &&\n                (!data || Object.keys(data).length === 0) &&\n                fetchPolicy !== 'cache-only' &&\n                !loading &&\n                !hasError) {\n                subscriber.next({\n                    ...currentResult,\n                    loading: true,\n                    networkStatus: NetworkStatus.loading,\n                });\n            }\n            return source.subscribe(subscriber);\n        });\n    };\n}\nclass QueryRef {\n    obsQuery;\n    valueChanges;\n    queryId;\n    constructor(obsQuery, ngZone, options) {\n        this.obsQuery = obsQuery;\n        const wrapped = wrapWithZone(from(fixObservable(this.obsQuery)), ngZone);\n        this.valueChanges = options.useInitialLoading\n            ? wrapped.pipe(useInitialLoading(this.obsQuery))\n            : wrapped;\n        this.queryId = this.obsQuery.queryId;\n    }\n    // ObservableQuery's methods\n    get options() {\n        return this.obsQuery.options;\n    }\n    get variables() {\n        return this.obsQuery.variables;\n    }\n    result() {\n        return this.obsQuery.result();\n    }\n    getCurrentResult() {\n        return this.obsQuery.getCurrentResult();\n    }\n    getLastResult() {\n        return this.obsQuery.getLastResult();\n    }\n    getLastError() {\n        return this.obsQuery.getLastError();\n    }\n    resetLastResults() {\n        return this.obsQuery.resetLastResults();\n    }\n    refetch(variables) {\n        return this.obsQuery.refetch(variables);\n    }\n    fetchMore(fetchMoreOptions) {\n        return this.obsQuery.fetchMore(fetchMoreOptions);\n    }\n    subscribeToMore(options) {\n        // XXX: there's a bug in apollo-client typings\n        // it should not inherit types from ObservableQuery\n        return this.obsQuery.subscribeToMore(options);\n    }\n    updateQuery(mapFn) {\n        return this.obsQuery.updateQuery(mapFn);\n    }\n    stopPolling() {\n        return this.obsQuery.stopPolling();\n    }\n    startPolling(pollInterval) {\n        return this.obsQuery.startPolling(pollInterval);\n    }\n    setOptions(opts) {\n        return this.obsQuery.setOptions(opts);\n    }\n    setVariables(variables) {\n        return this.obsQuery.setVariables(variables);\n    }\n}\n\nconst APOLLO_FLAGS = new InjectionToken('APOLLO_FLAGS');\nconst APOLLO_OPTIONS = new InjectionToken('APOLLO_OPTIONS');\nconst APOLLO_NAMED_OPTIONS = new InjectionToken('APOLLO_NAMED_OPTIONS');\n\nclass ApolloBase {\n    ngZone;\n    flags;\n    _client;\n    useInitialLoading;\n    useMutationLoading;\n    constructor(ngZone, flags, _client) {\n        this.ngZone = ngZone;\n        this.flags = flags;\n        this._client = _client;\n        this.useInitialLoading = pickFlag(flags, 'useInitialLoading', false);\n        this.useMutationLoading = pickFlag(flags, 'useMutationLoading', false);\n    }\n    watchQuery(options) {\n        return new QueryRef(this.ensureClient().watchQuery({\n            ...options,\n        }), this.ngZone, {\n            useInitialLoading: this.useInitialLoading,\n            ...options,\n        });\n    }\n    query(options) {\n        return fromPromise(() => this.ensureClient().query({ ...options }));\n    }\n    mutate(options) {\n        return useMutationLoading(fromPromise(() => this.ensureClient().mutate({ ...options })), options.useMutationLoading ?? this.useMutationLoading);\n    }\n    subscribe(options, extra) {\n        const obs = from(fixObservable(this.ensureClient().subscribe({ ...options })));\n        return extra && extra.useZone !== true ? obs : wrapWithZone(obs, this.ngZone);\n    }\n    /**\n     * Get an instance of ApolloClient\n     * @deprecated use `apollo.client` instead\n     */\n    getClient() {\n        return this.client;\n    }\n    /**\n     * Set a new instance of ApolloClient\n     * Remember to clean up the store before setting a new client.\n     * @deprecated use `apollo.client = client` instead\n     *\n     * @param client ApolloClient instance\n     */\n    setClient(client) {\n        this.client = client;\n    }\n    /**\n     * Get an instance of ApolloClient\n     */\n    get client() {\n        return this._client;\n    }\n    /**\n     * Set a new instance of ApolloClient\n     * Remember to clean up the store before setting a new client.\n     *\n     * @param client ApolloClient instance\n     */\n    set client(client) {\n        if (this._client) {\n            throw new Error('Client has been already defined');\n        }\n        this._client = client;\n    }\n    ensureClient() {\n        this.checkInstance();\n        return this._client;\n    }\n    checkInstance() {\n        if (!this._client) {\n            throw new Error('Client has not been defined yet');\n        }\n    }\n}\nclass Apollo extends ApolloBase {\n    _ngZone;\n    map = new Map();\n    constructor(_ngZone, apolloOptions, apolloNamedOptions, flags) {\n        super(_ngZone, flags);\n        this._ngZone = _ngZone;\n        if (apolloOptions) {\n            this.createDefault(apolloOptions);\n        }\n        if (apolloNamedOptions && typeof apolloNamedOptions === 'object') {\n            for (let name in apolloNamedOptions) {\n                if (apolloNamedOptions.hasOwnProperty(name)) {\n                    const options = apolloNamedOptions[name];\n                    this.create(options, name);\n                }\n            }\n        }\n    }\n    /**\n     * Create an instance of ApolloClient\n     * @param options Options required to create ApolloClient\n     * @param name client's name\n     */\n    create(options, name) {\n        if (isDefault(name)) {\n            this.createDefault(options);\n        }\n        else {\n            this.createNamed(name, options);\n        }\n    }\n    /**\n     * Use a default ApolloClient\n     */\n    default() {\n        return this;\n    }\n    /**\n     * Use a named ApolloClient\n     * @param name client's name\n     */\n    use(name) {\n        if (isDefault(name)) {\n            return this.default();\n        }\n        return this.map.get(name);\n    }\n    /**\n     * Create a default ApolloClient, same as `apollo.create(options)`\n     * @param options ApolloClient's options\n     */\n    createDefault(options) {\n        if (this.getClient()) {\n            throw new Error('Apollo has been already created.');\n        }\n        return this.setClient(new ApolloClient(options));\n    }\n    /**\n     * Create a named ApolloClient, same as `apollo.create(options, name)`\n     * @param name client's name\n     * @param options ApolloClient's options\n     */\n    createNamed(name, options) {\n        if (this.map.has(name)) {\n            throw new Error(`Client ${name} has been already created`);\n        }\n        this.map.set(name, new ApolloBase(this._ngZone, this.flags, new ApolloClient(options)));\n    }\n    /**\n     * Remember to clean up the store before removing a client\n     * @param name client's name\n     */\n    removeClient(name) {\n        if (isDefault(name)) {\n            this._client = undefined;\n        }\n        else {\n            this.map.delete(name);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Apollo, deps: [{ token: i0.NgZone }, { token: APOLLO_OPTIONS, optional: true }, { token: APOLLO_NAMED_OPTIONS, optional: true }, { token: APOLLO_FLAGS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Apollo });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Apollo, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [APOLLO_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APOLLO_NAMED_OPTIONS]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APOLLO_FLAGS]\n                }, {\n                    type: Optional\n                }] }]; } });\nfunction isDefault(name) {\n    return !name || name === 'default';\n}\n\nconst PROVIDERS = [Apollo];\nclass ApolloModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: ApolloModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.2\", ngImport: i0, type: ApolloModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: ApolloModule, providers: PROVIDERS });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: ApolloModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: PROVIDERS,\n                }]\n        }] });\n\nclass Query {\n    apollo;\n    document;\n    client = 'default';\n    constructor(apollo) {\n        this.apollo = apollo;\n    }\n    watch(variables, options) {\n        return this.apollo.use(this.client).watchQuery({\n            ...options,\n            variables,\n            query: this.document,\n        });\n    }\n    fetch(variables, options) {\n        return this.apollo.use(this.client).query({\n            ...options,\n            variables,\n            query: this.document,\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Query, deps: [{ token: Apollo }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Query });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Query, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: Apollo }]; } });\n\nclass Mutation {\n    apollo;\n    document;\n    client = 'default';\n    constructor(apollo) {\n        this.apollo = apollo;\n    }\n    mutate(variables, options) {\n        return this.apollo.use(this.client).mutate({\n            ...options,\n            variables,\n            mutation: this.document,\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Mutation, deps: [{ token: Apollo }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Mutation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Mutation, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: Apollo }]; } });\n\nclass Subscription {\n    apollo;\n    document;\n    client = 'default';\n    constructor(apollo) {\n        this.apollo = apollo;\n    }\n    subscribe(variables, options, extra) {\n        return this.apollo.use(this.client).subscribe({\n            ...options,\n            variables,\n            query: this.document,\n        }, extra);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Subscription, deps: [{ token: Apollo }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Subscription });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: Subscription, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: Apollo }]; } });\n\nfunction typedGQLTag(literals, ...placeholders) {\n    return gql$1(literals, ...placeholders);\n}\nconst gql = typedGQLTag;\nconst graphql = typedGQLTag;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { APOLLO_FLAGS, APOLLO_NAMED_OPTIONS, APOLLO_OPTIONS, Apollo, ApolloBase, ApolloModule, Mutation, Query, QueryRef, Subscription, gql, graphql };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACtF,SAASC,UAAU,EAAEC,cAAc,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AACnE,SAASC,aAAa,EAAEC,YAAY,EAAEC,GAAG,IAAIC,KAAK,QAAQ,qBAAqB;AAC/E,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAE1D,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC5B,OAAO,IAAIZ,UAAU,CAACa,UAAU,IAAI;IAChCD,SAAS,CAAC,CAAC,CAACE,IAAI,CAACC,MAAM,IAAI;MACvB,IAAI,CAACF,UAAU,CAACG,MAAM,EAAE;QACpBH,UAAU,CAACI,IAAI,CAACF,MAAM,CAAC;QACvBF,UAAU,CAACK,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAEC,KAAK,IAAI;MACR,IAAI,CAACN,UAAU,CAACG,MAAM,EAAE;QACpBH,UAAU,CAACM,KAAK,CAACA,KAAK,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAO,MAAMN,UAAU,CAACO,WAAW,CAAC,CAAC;EACzC,CAAC,CAAC;AACN;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACzC,IAAI,CAACA,OAAO,EAAE;IACV,OAAOD,MAAM,CAACE,IAAI,CAAChB,GAAG,CAACO,MAAM,KAAK;MAC9B,GAAGA,MAAM;MACTU,OAAO,EAAE;IACb,CAAC,CAAC,CAAC,CAAC;EACR;EACA,OAAOH,MAAM,CAACE,IAAI,CAACf,SAAS,CAAC;IACzBgB,OAAO,EAAE;EACb,CAAC,CAAC,EAAEjB,GAAG,CAACO,MAAM,KAAK;IACf,GAAGA,MAAM;IACTU,OAAO,EAAE,CAAC,CAACV,MAAM,CAACU;EACtB,CAAC,CAAC,CAAC,CAAC;AACR;AACA,MAAMC,aAAa,CAAC;EAChBC,IAAI;EACJC,WAAWA,CAACD,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAE,GAAG,GAAGC,IAAI,CAACD,GAAG,GAAGC,IAAI,CAACD,GAAG,GAAG,MAAM,CAAC,IAAIC,IAAI,CAAC,CAAC;EAC7CC,QAAQA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAE;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAC,MAAMlC,cAAc,CAAC8B,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC,CAAC;EAC3E;AACJ;AACA,SAASE,aAAaA,CAACC,GAAG,EAAE;EACxBA,GAAG,CAACnC,UAAU,CAAC,GAAG,MAAMmC,GAAG;EAC3B,OAAOA,GAAG;AACd;AACA,SAASC,YAAYA,CAACD,GAAG,EAAEE,MAAM,EAAE;EAC/B,OAAOF,GAAG,CAACb,IAAI,CAACd,SAAS,CAAC,IAAIgB,aAAa,CAACa,MAAM,CAAC,CAAC,CAAC;AACzD;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzC,OAAOF,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAI,CAAC,KAAK,WAAW,GAAGD,KAAK,CAACC,IAAI,CAAC,GAAGC,YAAY;AACnF;AAEA,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,OAAO,SAASC,yBAAyBA,CAACxB,MAAM,EAAE;IAC9C,OAAO,IAAItB,UAAU,CAAC,SAAS+C,6BAA6BA,CAAClC,UAAU,EAAE;MACrE,MAAMmC,aAAa,GAAGH,QAAQ,CAACI,gBAAgB,CAAC,CAAC;MACjD,MAAM;QAAExB,OAAO;QAAEyB,MAAM;QAAE/B,KAAK;QAAEgC,OAAO;QAAEC;MAAK,CAAC,GAAGJ,aAAa;MAC/D,MAAM;QAAEK,cAAc;QAAEC;MAAY,CAAC,GAAGT,QAAQ,CAACU,OAAO;MACxD,MAAMC,QAAQ,GAAGN,MAAM,IAAI/B,KAAK;MAChC,IAAIkC,cAAc,IACdF,OAAO,KACN,CAACC,IAAI,IAAIK,MAAM,CAACC,IAAI,CAACN,IAAI,CAAC,CAACO,MAAM,KAAK,CAAC,CAAC,IACzCL,WAAW,KAAK,YAAY,IAC5B,CAAC7B,OAAO,IACR,CAAC+B,QAAQ,EAAE;QACX3C,UAAU,CAACI,IAAI,CAAC;UACZ,GAAG+B,aAAa;UAChBvB,OAAO,EAAE,IAAI;UACbmC,aAAa,EAAExD,aAAa,CAACqB;QACjC,CAAC,CAAC;MACN;MACA,OAAOH,MAAM,CAACuC,SAAS,CAAChD,UAAU,CAAC;IACvC,CAAC,CAAC;EACN,CAAC;AACL;AACA,MAAMiD,QAAQ,CAAC;EACXjB,QAAQ;EACRkB,YAAY;EACZC,OAAO;EACPpC,WAAWA,CAACiB,QAAQ,EAAEN,MAAM,EAAEgB,OAAO,EAAE;IACnC,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IACxB,MAAMoB,OAAO,GAAG3B,YAAY,CAACnC,IAAI,CAACiC,aAAa,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAEN,MAAM,CAAC;IACxE,IAAI,CAACwB,YAAY,GAAGR,OAAO,CAACX,iBAAiB,GACvCqB,OAAO,CAACzC,IAAI,CAACoB,iBAAiB,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,GAC9CoB,OAAO;IACb,IAAI,CAACD,OAAO,GAAG,IAAI,CAACnB,QAAQ,CAACmB,OAAO;EACxC;EACA;EACA,IAAIT,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACV,QAAQ,CAACU,OAAO;EAChC;EACA,IAAIW,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrB,QAAQ,CAACqB,SAAS;EAClC;EACAnD,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC8B,QAAQ,CAAC9B,MAAM,CAAC,CAAC;EACjC;EACAkC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,QAAQ,CAACI,gBAAgB,CAAC,CAAC;EAC3C;EACAkB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,QAAQ,CAACsB,aAAa,CAAC,CAAC;EACxC;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACvB,QAAQ,CAACuB,YAAY,CAAC,CAAC;EACvC;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACxB,QAAQ,CAACwB,gBAAgB,CAAC,CAAC;EAC3C;EACAC,OAAOA,CAACJ,SAAS,EAAE;IACf,OAAO,IAAI,CAACrB,QAAQ,CAACyB,OAAO,CAACJ,SAAS,CAAC;EAC3C;EACAK,SAASA,CAACC,gBAAgB,EAAE;IACxB,OAAO,IAAI,CAAC3B,QAAQ,CAAC0B,SAAS,CAACC,gBAAgB,CAAC;EACpD;EACAC,eAAeA,CAAClB,OAAO,EAAE;IACrB;IACA;IACA,OAAO,IAAI,CAACV,QAAQ,CAAC4B,eAAe,CAAClB,OAAO,CAAC;EACjD;EACAmB,WAAWA,CAACC,KAAK,EAAE;IACf,OAAO,IAAI,CAAC9B,QAAQ,CAAC6B,WAAW,CAACC,KAAK,CAAC;EAC3C;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/B,QAAQ,CAAC+B,WAAW,CAAC,CAAC;EACtC;EACAC,YAAYA,CAACC,YAAY,EAAE;IACvB,OAAO,IAAI,CAACjC,QAAQ,CAACgC,YAAY,CAACC,YAAY,CAAC;EACnD;EACAC,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAACnC,QAAQ,CAACkC,UAAU,CAACC,IAAI,CAAC;EACzC;EACAC,YAAYA,CAACf,SAAS,EAAE;IACpB,OAAO,IAAI,CAACrB,QAAQ,CAACoC,YAAY,CAACf,SAAS,CAAC;EAChD;AACJ;AAEA,MAAMgB,YAAY,GAAG,IAAIvF,cAAc,CAAC,cAAc,CAAC;AACvD,MAAMwF,cAAc,GAAG,IAAIxF,cAAc,CAAC,gBAAgB,CAAC;AAC3D,MAAMyF,oBAAoB,GAAG,IAAIzF,cAAc,CAAC,sBAAsB,CAAC;AAEvE,MAAM0F,UAAU,CAAC;EACb9C,MAAM;EACNE,KAAK;EACL6C,OAAO;EACP1C,iBAAiB;EACjBvB,kBAAkB;EAClBO,WAAWA,CAACW,MAAM,EAAEE,KAAK,EAAE6C,OAAO,EAAE;IAChC,IAAI,CAAC/C,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6C,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1C,iBAAiB,GAAGJ,QAAQ,CAACC,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC;IACpE,IAAI,CAACpB,kBAAkB,GAAGmB,QAAQ,CAACC,KAAK,EAAE,oBAAoB,EAAE,KAAK,CAAC;EAC1E;EACA8C,UAAUA,CAAChC,OAAO,EAAE;IAChB,OAAO,IAAIO,QAAQ,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAACD,UAAU,CAAC;MAC/C,GAAGhC;IACP,CAAC,CAAC,EAAE,IAAI,CAAChB,MAAM,EAAE;MACbK,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC,GAAGW;IACP,CAAC,CAAC;EACN;EACAkC,KAAKA,CAAClC,OAAO,EAAE;IACX,OAAO5C,WAAW,CAAC,MAAM,IAAI,CAAC6E,YAAY,CAAC,CAAC,CAACC,KAAK,CAAC;MAAE,GAAGlC;IAAQ,CAAC,CAAC,CAAC;EACvE;EACAmC,MAAMA,CAACnC,OAAO,EAAE;IACZ,OAAOlC,kBAAkB,CAACV,WAAW,CAAC,MAAM,IAAI,CAAC6E,YAAY,CAAC,CAAC,CAACE,MAAM,CAAC;MAAE,GAAGnC;IAAQ,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAClC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC;EACnJ;EACAwC,SAASA,CAACN,OAAO,EAAEoC,KAAK,EAAE;IACtB,MAAMtD,GAAG,GAAGlC,IAAI,CAACiC,aAAa,CAAC,IAAI,CAACoD,YAAY,CAAC,CAAC,CAAC3B,SAAS,CAAC;MAAE,GAAGN;IAAQ,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAOoC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,IAAI,GAAGvD,GAAG,GAAGC,YAAY,CAACD,GAAG,EAAE,IAAI,CAACE,MAAM,CAAC;EACjF;EACA;AACJ;AACA;AACA;EACIsD,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACD,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACI,IAAIA,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACR,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIQ,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,IAAI,CAACR,OAAO,EAAE;MACd,MAAM,IAAIU,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAI,CAACV,OAAO,GAAGQ,MAAM;EACzB;EACAN,YAAYA,CAAA,EAAG;IACX,IAAI,CAACS,aAAa,CAAC,CAAC;IACpB,OAAO,IAAI,CAACX,OAAO;EACvB;EACAW,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;MACf,MAAM,IAAIU,KAAK,CAAC,iCAAiC,CAAC;IACtD;EACJ;AACJ;AACA,MAAME,MAAM,SAASb,UAAU,CAAC;EAC5Bc,OAAO;EACP3F,GAAG,GAAG,IAAI4F,GAAG,CAAC,CAAC;EACfxE,WAAWA,CAACuE,OAAO,EAAEE,aAAa,EAAEC,kBAAkB,EAAE7D,KAAK,EAAE;IAC3D,KAAK,CAAC0D,OAAO,EAAE1D,KAAK,CAAC;IACrB,IAAI,CAAC0D,OAAO,GAAGA,OAAO;IACtB,IAAIE,aAAa,EAAE;MACf,IAAI,CAACE,aAAa,CAACF,aAAa,CAAC;IACrC;IACA,IAAIC,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;MAC9D,KAAK,IAAIE,IAAI,IAAIF,kBAAkB,EAAE;QACjC,IAAIA,kBAAkB,CAACG,cAAc,CAACD,IAAI,CAAC,EAAE;UACzC,MAAMjD,OAAO,GAAG+C,kBAAkB,CAACE,IAAI,CAAC;UACxC,IAAI,CAACE,MAAM,CAACnD,OAAO,EAAEiD,IAAI,CAAC;QAC9B;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAACnD,OAAO,EAAEiD,IAAI,EAAE;IAClB,IAAIG,SAAS,CAACH,IAAI,CAAC,EAAE;MACjB,IAAI,CAACD,aAAa,CAAChD,OAAO,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACqD,WAAW,CAACJ,IAAI,EAAEjD,OAAO,CAAC;IACnC;EACJ;EACA;AACJ;AACA;EACIsD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,GAAGA,CAACN,IAAI,EAAE;IACN,IAAIG,SAAS,CAACH,IAAI,CAAC,EAAE;MACjB,OAAO,IAAI,CAACK,OAAO,CAAC,CAAC;IACzB;IACA,OAAO,IAAI,CAACrG,GAAG,CAACuG,GAAG,CAACP,IAAI,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACID,aAAaA,CAAChD,OAAO,EAAE;IACnB,IAAI,IAAI,CAACsC,SAAS,CAAC,CAAC,EAAE;MAClB,MAAM,IAAIG,KAAK,CAAC,kCAAkC,CAAC;IACvD;IACA,OAAO,IAAI,CAACD,SAAS,CAAC,IAAI1F,YAAY,CAACkD,OAAO,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;EACIqD,WAAWA,CAACJ,IAAI,EAAEjD,OAAO,EAAE;IACvB,IAAI,IAAI,CAAC/C,GAAG,CAACwG,GAAG,CAACR,IAAI,CAAC,EAAE;MACpB,MAAM,IAAIR,KAAK,CAAE,UAASQ,IAAK,2BAA0B,CAAC;IAC9D;IACA,IAAI,CAAChG,GAAG,CAACyG,GAAG,CAACT,IAAI,EAAE,IAAInB,UAAU,CAAC,IAAI,CAACc,OAAO,EAAE,IAAI,CAAC1D,KAAK,EAAE,IAAIpC,YAAY,CAACkD,OAAO,CAAC,CAAC,CAAC;EAC3F;EACA;AACJ;AACA;AACA;EACI2D,YAAYA,CAACV,IAAI,EAAE;IACf,IAAIG,SAAS,CAACH,IAAI,CAAC,EAAE;MACjB,IAAI,CAAClB,OAAO,GAAG6B,SAAS;IAC5B,CAAC,MACI;MACD,IAAI,CAAC3G,GAAG,CAAC4G,MAAM,CAACZ,IAAI,CAAC;IACzB;EACJ;EACA,OAAOa,IAAI,YAAAC,eAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFrB,MAAM,EAAhBxG,EAAE,CAAA8H,QAAA,CAAgC9H,EAAE,CAAC+H,MAAM,GAA3C/H,EAAE,CAAA8H,QAAA,CAAsDrC,cAAc,MAAtEzF,EAAE,CAAA8H,QAAA,CAAiGpC,oBAAoB,MAAvH1F,EAAE,CAAA8H,QAAA,CAAkJtC,YAAY;EAAA;EACzP,OAAOwC,KAAK,kBAD6EhI,EAAE,CAAAiI,kBAAA;IAAAC,KAAA,EACY1B,MAAM;IAAA2B,OAAA,EAAN3B,MAAM,CAAAmB;EAAA;AACjH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAH6FpI,EAAE,CAAAqI,iBAAA,CAGJ7B,MAAM,EAAc,CAAC;IACpG8B,IAAI,EAAEpI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAEtI,EAAE,CAAC+H;IAAO,CAAC,EAAE;MAAEO,IAAI,EAAEb,SAAS;MAAEc,UAAU,EAAE,CAAC;QACnFD,IAAI,EAAEnI;MACV,CAAC,EAAE;QACCmI,IAAI,EAAElI,MAAM;QACZoI,IAAI,EAAE,CAAC/C,cAAc;MACzB,CAAC;IAAE,CAAC,EAAE;MAAE6C,IAAI,EAAEb,SAAS;MAAEc,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAElI,MAAM;QACZoI,IAAI,EAAE,CAAC9C,oBAAoB;MAC/B,CAAC,EAAE;QACC4C,IAAI,EAAEnI;MACV,CAAC;IAAE,CAAC,EAAE;MAAEmI,IAAI,EAAEb,SAAS;MAAEc,UAAU,EAAE,CAAC;QAClCD,IAAI,EAAElI,MAAM;QACZoI,IAAI,EAAE,CAAChD,YAAY;MACvB,CAAC,EAAE;QACC8C,IAAI,EAAEnI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,SAAS8G,SAASA,CAACH,IAAI,EAAE;EACrB,OAAO,CAACA,IAAI,IAAIA,IAAI,KAAK,SAAS;AACtC;AAEA,MAAM2B,SAAS,GAAG,CAACjC,MAAM,CAAC;AAC1B,MAAMkC,YAAY,CAAC;EACf,OAAOf,IAAI,YAAAgB,qBAAAd,CAAA;IAAA,YAAAA,CAAA,IAAwFa,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBA5B8E5I,EAAE,CAAA6I,gBAAA;IAAAP,IAAA,EA4BSI;EAAY;EAChH,OAAOI,IAAI,kBA7B8E9I,EAAE,CAAA+I,gBAAA;IAAAC,SAAA,EA6BkCP;EAAS;AAC1I;AACA;EAAA,QAAAL,SAAA,oBAAAA,SAAA,KA/B6FpI,EAAE,CAAAqI,iBAAA,CA+BJK,YAAY,EAAc,CAAC;IAC1GJ,IAAI,EAAEjI,QAAQ;IACdmI,IAAI,EAAE,CAAC;MACCQ,SAAS,EAAEP;IACf,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMQ,KAAK,CAAC;EACRC,MAAM;EACNC,QAAQ;EACR/C,MAAM,GAAG,SAAS;EAClBlE,WAAWA,CAACgH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAE,KAAKA,CAAC5E,SAAS,EAAEX,OAAO,EAAE;IACtB,OAAO,IAAI,CAACqF,MAAM,CAAC9B,GAAG,CAAC,IAAI,CAAChB,MAAM,CAAC,CAACP,UAAU,CAAC;MAC3C,GAAGhC,OAAO;MACVW,SAAS;MACTuB,KAAK,EAAE,IAAI,CAACoD;IAChB,CAAC,CAAC;EACN;EACAE,KAAKA,CAAC7E,SAAS,EAAEX,OAAO,EAAE;IACtB,OAAO,IAAI,CAACqF,MAAM,CAAC9B,GAAG,CAAC,IAAI,CAAChB,MAAM,CAAC,CAACL,KAAK,CAAC;MACtC,GAAGlC,OAAO;MACVW,SAAS;MACTuB,KAAK,EAAE,IAAI,CAACoD;IAChB,CAAC,CAAC;EACN;EACA,OAAOxB,IAAI,YAAA2B,cAAAzB,CAAA;IAAA,YAAAA,CAAA,IAAwFoB,KAAK,EA3DfjJ,EAAE,CAAA8H,QAAA,CA2D+BtB,MAAM;EAAA;EAChI,OAAOwB,KAAK,kBA5D6EhI,EAAE,CAAAiI,kBAAA;IAAAC,KAAA,EA4DYe,KAAK;IAAAd,OAAA,EAALc,KAAK,CAAAtB;EAAA;AAChH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA9D6FpI,EAAE,CAAAqI,iBAAA,CA8DJY,KAAK,EAAc,CAAC;IACnGX,IAAI,EAAEpI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE9B;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAEtE,MAAM+C,QAAQ,CAAC;EACXL,MAAM;EACNC,QAAQ;EACR/C,MAAM,GAAG,SAAS;EAClBlE,WAAWA,CAACgH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAlD,MAAMA,CAACxB,SAAS,EAAEX,OAAO,EAAE;IACvB,OAAO,IAAI,CAACqF,MAAM,CAAC9B,GAAG,CAAC,IAAI,CAAChB,MAAM,CAAC,CAACJ,MAAM,CAAC;MACvC,GAAGnC,OAAO;MACVW,SAAS;MACTgF,QAAQ,EAAE,IAAI,CAACL;IACnB,CAAC,CAAC;EACN;EACA,OAAOxB,IAAI,YAAA8B,iBAAA5B,CAAA;IAAA,YAAAA,CAAA,IAAwF0B,QAAQ,EAhFlBvJ,EAAE,CAAA8H,QAAA,CAgFkCtB,MAAM;EAAA;EACnI,OAAOwB,KAAK,kBAjF6EhI,EAAE,CAAAiI,kBAAA;IAAAC,KAAA,EAiFYqB,QAAQ;IAAApB,OAAA,EAARoB,QAAQ,CAAA5B;EAAA;AACnH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAnF6FpI,EAAE,CAAAqI,iBAAA,CAmFJkB,QAAQ,EAAc,CAAC;IACtGjB,IAAI,EAAEpI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE9B;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAEtE,MAAMkD,YAAY,CAAC;EACfR,MAAM;EACNC,QAAQ;EACR/C,MAAM,GAAG,SAAS;EAClBlE,WAAWA,CAACgH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA/E,SAASA,CAACK,SAAS,EAAEX,OAAO,EAAEoC,KAAK,EAAE;IACjC,OAAO,IAAI,CAACiD,MAAM,CAAC9B,GAAG,CAAC,IAAI,CAAChB,MAAM,CAAC,CAACjC,SAAS,CAAC;MAC1C,GAAGN,OAAO;MACVW,SAAS;MACTuB,KAAK,EAAE,IAAI,CAACoD;IAChB,CAAC,EAAElD,KAAK,CAAC;EACb;EACA,OAAO0B,IAAI,YAAAgC,qBAAA9B,CAAA;IAAA,YAAAA,CAAA,IAAwF6B,YAAY,EArGtB1J,EAAE,CAAA8H,QAAA,CAqGsCtB,MAAM;EAAA;EACvI,OAAOwB,KAAK,kBAtG6EhI,EAAE,CAAAiI,kBAAA;IAAAC,KAAA,EAsGYwB,YAAY;IAAAvB,OAAA,EAAZuB,YAAY,CAAA/B;EAAA;AACvH;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAxG6FpI,EAAE,CAAAqI,iBAAA,CAwGJqB,YAAY,EAAc,CAAC;IAC1GpB,IAAI,EAAEpI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEoI,IAAI,EAAE9B;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAEtE,SAASoD,WAAWA,CAACC,QAAQ,EAAE,GAAGC,YAAY,EAAE;EAC5C,OAAOjJ,KAAK,CAACgJ,QAAQ,EAAE,GAAGC,YAAY,CAAC;AAC3C;AACA,MAAMlJ,GAAG,GAAGgJ,WAAW;AACvB,MAAMG,OAAO,GAAGH,WAAW;;AAE3B;AACA;AACA;;AAEA,SAASpE,YAAY,EAAEE,oBAAoB,EAAED,cAAc,EAAEe,MAAM,EAAEb,UAAU,EAAE+C,YAAY,EAAEa,QAAQ,EAAEN,KAAK,EAAE7E,QAAQ,EAAEsF,YAAY,EAAE9I,GAAG,EAAEmJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}