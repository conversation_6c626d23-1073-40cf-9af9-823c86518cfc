{"ast": null, "code": "\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.formDataAppendFile` that uses the standard\n * [`FormData.append`](https://developer.mozilla.org/en-US/docs/Web/API/FormData/append)\n * method.\n * @kind function\n * @name formDataAppendFile\n * @type {FormDataFileAppender}\n * @param {FormData} formData [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance to append the specified file to.\n * @param {string} fieldName Field name for the file.\n * @param {*} file File to append.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { formDataAppendFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import formDataAppendFile from \"apollo-upload-client/public/formDataAppendFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { formDataAppendFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const formDataAppendFile = require(\"apollo-upload-client/public/formDataAppendFile.js\");\n * ```\n */\nmodule.exports = function formDataAppendFile(formData, fieldName, file) {\n  formData.append(fieldName, file, file.name);\n};", "map": {"version": 3, "names": ["module", "exports", "formDataAppendFile", "formData", "fieldName", "file", "append", "name"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-upload-client/public/formDataAppendFile.js"], "sourcesContent": ["\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.formDataAppendFile` that uses the standard\n * [`FormData.append`](https://developer.mozilla.org/en-US/docs/Web/API/FormData/append)\n * method.\n * @kind function\n * @name formDataAppendFile\n * @type {FormDataFileAppender}\n * @param {FormData} formData [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance to append the specified file to.\n * @param {string} fieldName Field name for the file.\n * @param {*} file File to append.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { formDataAppendFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import formDataAppendFile from \"apollo-upload-client/public/formDataAppendFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { formDataAppendFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const formDataAppendFile = require(\"apollo-upload-client/public/formDataAppendFile.js\");\n * ```\n */\nmodule.exports = function formDataAppendFile(formData, fieldName, file) {\n  formData.append(fieldName, file, file.name);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAE;EACtEF,QAAQ,CAACG,MAAM,CAACF,SAAS,EAAEC,IAAI,EAAEA,IAAI,CAACE,IAAI,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}