{"ast": null, "code": "import { print } from 'graphql';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { ApolloLink, Observable as Observable$1 } from '@apollo/client/core';\nimport { Observable } from 'rxjs';\nimport * as i1 from '@angular/common/http';\nimport { HttpHeaders } from '@angular/common/http';\nimport { BatchLink } from '@apollo/client/link/batch';\nconst fetch = (req, httpClient, extractFiles) => {\n  const shouldUseBody = ['POST', 'PUT', 'PATCH'].indexOf(req.method.toUpperCase()) !== -1;\n  const shouldStringify = param => ['variables', 'extensions'].indexOf(param.toLowerCase()) !== -1;\n  const isBatching = req.body.length;\n  let shouldUseMultipart = req.options && req.options.useMultipart;\n  let multipartInfo;\n  if (shouldUseMultipart) {\n    if (isBatching) {\n      return new Observable(observer => observer.error(new Error('File upload is not available when combined with Batching')));\n    }\n    if (!shouldUseBody) {\n      return new Observable(observer => observer.error(new Error('File upload is not available when GET is used')));\n    }\n    if (!extractFiles) {\n      return new Observable(observer => observer.error(new Error(`To use File upload you need to pass \"extractFiles\" function from \"extract-files\" library to HttpLink's options`)));\n    }\n    multipartInfo = extractFiles(req.body);\n    shouldUseMultipart = !!multipartInfo.files.size;\n  }\n  // `body` for some, `params` for others\n  let bodyOrParams = {};\n  if (isBatching) {\n    if (!shouldUseBody) {\n      return new Observable(observer => observer.error(new Error('Batching is not available for GET requests')));\n    }\n    bodyOrParams = {\n      body: req.body\n    };\n  } else {\n    const body = shouldUseMultipart ? multipartInfo.clone : req.body;\n    if (shouldUseBody) {\n      bodyOrParams = {\n        body\n      };\n    } else {\n      const params = Object.keys(req.body).reduce((obj, param) => {\n        const value = req.body[param];\n        obj[param] = shouldStringify(param) ? JSON.stringify(value) : value;\n        return obj;\n      }, {});\n      bodyOrParams = {\n        params: params\n      };\n    }\n  }\n  if (shouldUseMultipart && shouldUseBody) {\n    const form = new FormData();\n    form.append('operations', JSON.stringify(bodyOrParams.body));\n    const map = {};\n    const files = multipartInfo.files;\n    let i = 0;\n    files.forEach(paths => {\n      map[++i] = paths;\n    });\n    form.append('map', JSON.stringify(map));\n    i = 0;\n    files.forEach((_, file) => {\n      form.append(++i + '', file, file.name);\n    });\n    bodyOrParams.body = form;\n  }\n  // create a request\n  return httpClient.request(req.method, req.url, {\n    observe: 'response',\n    responseType: 'json',\n    reportProgress: false,\n    ...bodyOrParams,\n    ...req.options\n  });\n};\nconst mergeHeaders = (source, destination) => {\n  if (source && destination) {\n    const merged = destination.keys().reduce((headers, name) => headers.set(name, destination.getAll(name)), source);\n    return merged;\n  }\n  return destination || source;\n};\nfunction prioritize(...values) {\n  const picked = values.find(val => typeof val !== 'undefined');\n  if (typeof picked === 'undefined') {\n    return values[values.length - 1];\n  }\n  return picked;\n}\nfunction createHeadersWithClientAwareness(context) {\n  // `apollographql-client-*` headers are automatically set if a\n  // `clientAwareness` object is found in the context. These headers are\n  // set first, followed by the rest of the headers pulled from\n  // `context.headers`.\n  let headers = context.headers && context.headers instanceof HttpHeaders ? context.headers : new HttpHeaders(context.headers);\n  if (context.clientAwareness) {\n    const {\n      name,\n      version\n    } = context.clientAwareness;\n    // If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    if (name && !headers.has('apollographql-client-name')) {\n      headers = headers.set('apollographql-client-name', name);\n    }\n    if (version && !headers.has('apollographql-client-version')) {\n      headers = headers.set('apollographql-client-version', version);\n    }\n  }\n  return headers;\n}\n\n// XXX find a better name for it\nclass HttpLinkHandler extends ApolloLink {\n  httpClient;\n  options;\n  requester;\n  print = print;\n  constructor(httpClient, options) {\n    super();\n    this.httpClient = httpClient;\n    this.options = options;\n    if (this.options.operationPrinter) {\n      this.print = this.options.operationPrinter;\n    }\n    this.requester = operation => new Observable$1(observer => {\n      const context = operation.getContext();\n      // decides which value to pick, Context, Options or to just use the default\n      const pick = (key, init) => {\n        return prioritize(context[key], this.options[key], init);\n      };\n      let method = pick('method', 'POST');\n      const includeQuery = pick('includeQuery', true);\n      const includeExtensions = pick('includeExtensions', false);\n      const url = pick('uri', 'graphql');\n      const withCredentials = pick('withCredentials');\n      const useMultipart = pick('useMultipart');\n      const useGETForQueries = this.options.useGETForQueries === true;\n      const isQuery = operation.query.definitions.some(def => def.kind === 'OperationDefinition' && def.operation === 'query');\n      if (useGETForQueries && isQuery) {\n        method = 'GET';\n      }\n      const req = {\n        method,\n        url: typeof url === 'function' ? url(operation) : url,\n        body: {\n          operationName: operation.operationName,\n          variables: operation.variables\n        },\n        options: {\n          withCredentials,\n          useMultipart,\n          headers: this.options.headers\n        }\n      };\n      if (includeExtensions) {\n        req.body.extensions = operation.extensions;\n      }\n      if (includeQuery) {\n        req.body.query = this.print(operation.query);\n      }\n      const headers = createHeadersWithClientAwareness(context);\n      req.options.headers = mergeHeaders(req.options.headers, headers);\n      const sub = fetch(req, this.httpClient, this.options.extractFiles).subscribe({\n        next: response => {\n          operation.setContext({\n            response\n          });\n          observer.next(response.body);\n        },\n        error: err => observer.error(err),\n        complete: () => observer.complete()\n      });\n      return () => {\n        if (!sub.closed) {\n          sub.unsubscribe();\n        }\n      };\n    });\n  }\n  request(op) {\n    return this.requester(op);\n  }\n}\nclass HttpLink {\n  httpClient;\n  constructor(httpClient) {\n    this.httpClient = httpClient;\n  }\n  create(options) {\n    return new HttpLinkHandler(this.httpClient, options);\n  }\n  static ɵfac = function HttpLink_Factory(t) {\n    return new (t || HttpLink)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpLink,\n    factory: HttpLink.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpLink, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.HttpClient\n    }];\n  }, null);\n})();\nconst defaults = {\n  batchInterval: 10,\n  batchMax: 10,\n  uri: 'graphql',\n  method: 'POST'\n};\nclass HttpBatchLinkHandler extends ApolloLink {\n  httpClient;\n  options;\n  batcher;\n  batchInterval;\n  batchMax;\n  print = print;\n  constructor(httpClient, options) {\n    super();\n    this.httpClient = httpClient;\n    this.options = options;\n    this.batchInterval = options.batchInterval || defaults.batchInterval;\n    this.batchMax = options.batchMax || defaults.batchMax;\n    if (this.options.operationPrinter) {\n      this.print = this.options.operationPrinter;\n    }\n    const batchHandler = operations => {\n      return new Observable$1(observer => {\n        const body = this.createBody(operations);\n        const headers = this.createHeaders(operations);\n        const {\n          method,\n          uri,\n          withCredentials\n        } = this.createOptions(operations);\n        if (typeof uri === 'function') {\n          throw new Error(`Option 'uri' is a function, should be a string`);\n        }\n        const req = {\n          method,\n          url: uri,\n          body: body,\n          options: {\n            withCredentials,\n            headers\n          }\n        };\n        const sub = fetch(req, this.httpClient, () => {\n          throw new Error('File upload is not available when combined with Batching');\n        }).subscribe({\n          next: result => observer.next(result.body),\n          error: err => observer.error(err),\n          complete: () => observer.complete()\n        });\n        return () => {\n          if (!sub.closed) {\n            sub.unsubscribe();\n          }\n        };\n      });\n    };\n    const batchKey = options.batchKey || (operation => {\n      return this.createBatchKey(operation);\n    });\n    this.batcher = new BatchLink({\n      batchInterval: this.batchInterval,\n      batchMax: this.batchMax,\n      batchKey,\n      batchHandler\n    });\n  }\n  createOptions(operations) {\n    const context = operations[0].getContext();\n    return {\n      method: prioritize(context.method, this.options.method, defaults.method),\n      uri: prioritize(context.uri, this.options.uri, defaults.uri),\n      withCredentials: prioritize(context.withCredentials, this.options.withCredentials)\n    };\n  }\n  createBody(operations) {\n    return operations.map(operation => {\n      const includeExtensions = prioritize(operation.getContext().includeExtensions, this.options.includeExtensions, false);\n      const includeQuery = prioritize(operation.getContext().includeQuery, this.options.includeQuery, true);\n      const body = {\n        operationName: operation.operationName,\n        variables: operation.variables\n      };\n      if (includeExtensions) {\n        body.extensions = operation.extensions;\n      }\n      if (includeQuery) {\n        body.query = this.print(operation.query);\n      }\n      return body;\n    });\n  }\n  createHeaders(operations) {\n    return operations.reduce((headers, operation) => {\n      return mergeHeaders(headers, operation.getContext().headers);\n    }, createHeadersWithClientAwareness({\n      headers: this.options.headers,\n      clientAwareness: operations[0]?.getContext()?.clientAwareness\n    }));\n  }\n  createBatchKey(operation) {\n    const context = operation.getContext();\n    if (context.skipBatching) {\n      return Math.random().toString(36).substr(2, 9);\n    }\n    const headers = context.headers && context.headers.keys().map(k => context.headers.get(k));\n    const opts = JSON.stringify({\n      includeQuery: context.includeQuery,\n      includeExtensions: context.includeExtensions,\n      headers\n    });\n    return prioritize(context.uri, this.options.uri) + opts;\n  }\n  request(op) {\n    return this.batcher.request(op);\n  }\n}\nclass HttpBatchLink {\n  httpClient;\n  constructor(httpClient) {\n    this.httpClient = httpClient;\n  }\n  create(options) {\n    return new HttpBatchLinkHandler(this.httpClient, options);\n  }\n  static ɵfac = function HttpBatchLink_Factory(t) {\n    return new (t || HttpBatchLink)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpBatchLink,\n    factory: HttpBatchLink.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpBatchLink, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.HttpClient\n    }];\n  }, null);\n})();\n\n// http\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HttpBatchLink, HttpBatchLinkHandler, HttpLink, HttpLinkHandler };", "map": {"version": 3, "names": ["print", "i0", "Injectable", "ApolloLink", "Observable", "Observable$1", "i1", "HttpHeaders", "BatchLink", "fetch", "req", "httpClient", "extractFiles", "shouldUseBody", "indexOf", "method", "toUpperCase", "shouldStringify", "param", "toLowerCase", "isBatching", "body", "length", "shouldUseMultipart", "options", "useMultipart", "multipartInfo", "observer", "error", "Error", "files", "size", "bodyOrParams", "clone", "params", "Object", "keys", "reduce", "obj", "value", "JSON", "stringify", "form", "FormData", "append", "map", "i", "for<PERSON>ach", "paths", "_", "file", "name", "request", "url", "observe", "responseType", "reportProgress", "mergeHeaders", "source", "destination", "merged", "headers", "set", "getAll", "prioritize", "values", "picked", "find", "val", "createHeadersWithClientAwareness", "context", "clientAwareness", "version", "has", "HttpLinkHandler", "requester", "constructor", "operationPrinter", "operation", "getContext", "pick", "key", "init", "<PERSON><PERSON><PERSON><PERSON>", "includeExtensions", "withCredentials", "useGETForQueries", "<PERSON><PERSON><PERSON><PERSON>", "query", "definitions", "some", "def", "kind", "operationName", "variables", "extensions", "sub", "subscribe", "next", "response", "setContext", "err", "complete", "closed", "unsubscribe", "op", "HttpLink", "create", "ɵfac", "HttpLink_Factory", "t", "ɵɵinject", "HttpClient", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "defaults", "batchInterval", "batchMax", "uri", "HttpBatchLinkHandler", "batcher", "<PERSON><PERSON><PERSON><PERSON>", "operations", "createBody", "createHeaders", "createOptions", "result", "<PERSON><PERSON><PERSON>", "createBatchKey", "skipBatching", "Math", "random", "toString", "substr", "k", "get", "opts", "HttpBatchLink", "HttpBatchLink_Factory"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-angular/fesm2022/ngApolloLinkHttp.mjs"], "sourcesContent": ["import { print } from 'graphql';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { ApolloLink, Observable as Observable$1 } from '@apollo/client/core';\nimport { Observable } from 'rxjs';\nimport * as i1 from '@angular/common/http';\nimport { HttpHeaders } from '@angular/common/http';\nimport { BatchLink } from '@apollo/client/link/batch';\n\nconst fetch = (req, httpClient, extractFiles) => {\n    const shouldUseBody = ['POST', 'PUT', 'PATCH'].indexOf(req.method.toUpperCase()) !== -1;\n    const shouldStringify = (param) => ['variables', 'extensions'].indexOf(param.toLowerCase()) !== -1;\n    const isBatching = req.body.length;\n    let shouldUseMultipart = req.options && req.options.useMultipart;\n    let multipartInfo;\n    if (shouldUseMultipart) {\n        if (isBatching) {\n            return new Observable(observer => observer.error(new Error('File upload is not available when combined with Batching')));\n        }\n        if (!shouldUseBody) {\n            return new Observable(observer => observer.error(new Error('File upload is not available when GET is used')));\n        }\n        if (!extractFiles) {\n            return new Observable(observer => observer.error(new Error(`To use File upload you need to pass \"extractFiles\" function from \"extract-files\" library to HttpLink's options`)));\n        }\n        multipartInfo = extractFiles(req.body);\n        shouldUseMultipart = !!multipartInfo.files.size;\n    }\n    // `body` for some, `params` for others\n    let bodyOrParams = {};\n    if (isBatching) {\n        if (!shouldUseBody) {\n            return new Observable(observer => observer.error(new Error('Batching is not available for GET requests')));\n        }\n        bodyOrParams = {\n            body: req.body,\n        };\n    }\n    else {\n        const body = shouldUseMultipart ? multipartInfo.clone : req.body;\n        if (shouldUseBody) {\n            bodyOrParams = {\n                body,\n            };\n        }\n        else {\n            const params = Object.keys(req.body).reduce((obj, param) => {\n                const value = req.body[param];\n                obj[param] = shouldStringify(param) ? JSON.stringify(value) : value;\n                return obj;\n            }, {});\n            bodyOrParams = { params: params };\n        }\n    }\n    if (shouldUseMultipart && shouldUseBody) {\n        const form = new FormData();\n        form.append('operations', JSON.stringify(bodyOrParams.body));\n        const map = {};\n        const files = multipartInfo.files;\n        let i = 0;\n        files.forEach(paths => {\n            map[++i] = paths;\n        });\n        form.append('map', JSON.stringify(map));\n        i = 0;\n        files.forEach((_, file) => {\n            form.append(++i + '', file, file.name);\n        });\n        bodyOrParams.body = form;\n    }\n    // create a request\n    return httpClient.request(req.method, req.url, {\n        observe: 'response',\n        responseType: 'json',\n        reportProgress: false,\n        ...bodyOrParams,\n        ...req.options,\n    });\n};\nconst mergeHeaders = (source, destination) => {\n    if (source && destination) {\n        const merged = destination\n            .keys()\n            .reduce((headers, name) => headers.set(name, destination.getAll(name)), source);\n        return merged;\n    }\n    return destination || source;\n};\nfunction prioritize(...values) {\n    const picked = values.find(val => typeof val !== 'undefined');\n    if (typeof picked === 'undefined') {\n        return values[values.length - 1];\n    }\n    return picked;\n}\nfunction createHeadersWithClientAwareness(context) {\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`.\n    let headers = context.headers && context.headers instanceof HttpHeaders\n        ? context.headers\n        : new HttpHeaders(context.headers);\n    if (context.clientAwareness) {\n        const { name, version } = context.clientAwareness;\n        // If desired, `apollographql-client-*` headers set by\n        // the `clientAwareness` object can be overridden by\n        // `apollographql-client-*` headers set in `context.headers`.\n        if (name && !headers.has('apollographql-client-name')) {\n            headers = headers.set('apollographql-client-name', name);\n        }\n        if (version && !headers.has('apollographql-client-version')) {\n            headers = headers.set('apollographql-client-version', version);\n        }\n    }\n    return headers;\n}\n\n// XXX find a better name for it\nclass HttpLinkHandler extends ApolloLink {\n    httpClient;\n    options;\n    requester;\n    print = print;\n    constructor(httpClient, options) {\n        super();\n        this.httpClient = httpClient;\n        this.options = options;\n        if (this.options.operationPrinter) {\n            this.print = this.options.operationPrinter;\n        }\n        this.requester = (operation) => new Observable$1((observer) => {\n            const context = operation.getContext();\n            // decides which value to pick, Context, Options or to just use the default\n            const pick = (key, init) => {\n                return prioritize(context[key], this.options[key], init);\n            };\n            let method = pick('method', 'POST');\n            const includeQuery = pick('includeQuery', true);\n            const includeExtensions = pick('includeExtensions', false);\n            const url = pick('uri', 'graphql');\n            const withCredentials = pick('withCredentials');\n            const useMultipart = pick('useMultipart');\n            const useGETForQueries = this.options.useGETForQueries === true;\n            const isQuery = operation.query.definitions.some(def => def.kind === 'OperationDefinition' && def.operation === 'query');\n            if (useGETForQueries && isQuery) {\n                method = 'GET';\n            }\n            const req = {\n                method,\n                url: typeof url === 'function' ? url(operation) : url,\n                body: {\n                    operationName: operation.operationName,\n                    variables: operation.variables,\n                },\n                options: {\n                    withCredentials,\n                    useMultipart,\n                    headers: this.options.headers,\n                },\n            };\n            if (includeExtensions) {\n                req.body.extensions = operation.extensions;\n            }\n            if (includeQuery) {\n                req.body.query = this.print(operation.query);\n            }\n            const headers = createHeadersWithClientAwareness(context);\n            req.options.headers = mergeHeaders(req.options.headers, headers);\n            const sub = fetch(req, this.httpClient, this.options.extractFiles).subscribe({\n                next: response => {\n                    operation.setContext({ response });\n                    observer.next(response.body);\n                },\n                error: err => observer.error(err),\n                complete: () => observer.complete(),\n            });\n            return () => {\n                if (!sub.closed) {\n                    sub.unsubscribe();\n                }\n            };\n        });\n    }\n    request(op) {\n        return this.requester(op);\n    }\n}\nclass HttpLink {\n    httpClient;\n    constructor(httpClient) {\n        this.httpClient = httpClient;\n    }\n    create(options) {\n        return new HttpLinkHandler(this.httpClient, options);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpLink, deps: [{ token: i1.HttpClient }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpLink, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpLink, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HttpClient }]; } });\n\nconst defaults = {\n    batchInterval: 10,\n    batchMax: 10,\n    uri: 'graphql',\n    method: 'POST',\n};\nclass HttpBatchLinkHandler extends ApolloLink {\n    httpClient;\n    options;\n    batcher;\n    batchInterval;\n    batchMax;\n    print = print;\n    constructor(httpClient, options) {\n        super();\n        this.httpClient = httpClient;\n        this.options = options;\n        this.batchInterval = options.batchInterval || defaults.batchInterval;\n        this.batchMax = options.batchMax || defaults.batchMax;\n        if (this.options.operationPrinter) {\n            this.print = this.options.operationPrinter;\n        }\n        const batchHandler = (operations) => {\n            return new Observable$1((observer) => {\n                const body = this.createBody(operations);\n                const headers = this.createHeaders(operations);\n                const { method, uri, withCredentials } = this.createOptions(operations);\n                if (typeof uri === 'function') {\n                    throw new Error(`Option 'uri' is a function, should be a string`);\n                }\n                const req = {\n                    method,\n                    url: uri,\n                    body: body,\n                    options: {\n                        withCredentials,\n                        headers,\n                    },\n                };\n                const sub = fetch(req, this.httpClient, () => {\n                    throw new Error('File upload is not available when combined with Batching');\n                }).subscribe({\n                    next: result => observer.next(result.body),\n                    error: err => observer.error(err),\n                    complete: () => observer.complete(),\n                });\n                return () => {\n                    if (!sub.closed) {\n                        sub.unsubscribe();\n                    }\n                };\n            });\n        };\n        const batchKey = options.batchKey ||\n            ((operation) => {\n                return this.createBatchKey(operation);\n            });\n        this.batcher = new BatchLink({\n            batchInterval: this.batchInterval,\n            batchMax: this.batchMax,\n            batchKey,\n            batchHandler,\n        });\n    }\n    createOptions(operations) {\n        const context = operations[0].getContext();\n        return {\n            method: prioritize(context.method, this.options.method, defaults.method),\n            uri: prioritize(context.uri, this.options.uri, defaults.uri),\n            withCredentials: prioritize(context.withCredentials, this.options.withCredentials),\n        };\n    }\n    createBody(operations) {\n        return operations.map(operation => {\n            const includeExtensions = prioritize(operation.getContext().includeExtensions, this.options.includeExtensions, false);\n            const includeQuery = prioritize(operation.getContext().includeQuery, this.options.includeQuery, true);\n            const body = {\n                operationName: operation.operationName,\n                variables: operation.variables,\n            };\n            if (includeExtensions) {\n                body.extensions = operation.extensions;\n            }\n            if (includeQuery) {\n                body.query = this.print(operation.query);\n            }\n            return body;\n        });\n    }\n    createHeaders(operations) {\n        return operations.reduce((headers, operation) => {\n            return mergeHeaders(headers, operation.getContext().headers);\n        }, createHeadersWithClientAwareness({\n            headers: this.options.headers,\n            clientAwareness: operations[0]?.getContext()?.clientAwareness,\n        }));\n    }\n    createBatchKey(operation) {\n        const context = operation.getContext();\n        if (context.skipBatching) {\n            return Math.random().toString(36).substr(2, 9);\n        }\n        const headers = context.headers && context.headers.keys().map((k) => context.headers.get(k));\n        const opts = JSON.stringify({\n            includeQuery: context.includeQuery,\n            includeExtensions: context.includeExtensions,\n            headers,\n        });\n        return prioritize(context.uri, this.options.uri) + opts;\n    }\n    request(op) {\n        return this.batcher.request(op);\n    }\n}\nclass HttpBatchLink {\n    httpClient;\n    constructor(httpClient) {\n        this.httpClient = httpClient;\n    }\n    create(options) {\n        return new HttpBatchLinkHandler(this.httpClient, options);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpBatchLink, deps: [{ token: i1.HttpClient }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpBatchLink, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.2\", ngImport: i0, type: HttpBatchLink, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HttpClient }]; } });\n\n// http\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HttpBatchLink, HttpBatchLinkHandler, HttpLink, HttpLinkHandler };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,EAAEC,UAAU,IAAIC,YAAY,QAAQ,qBAAqB;AAC5E,SAASD,UAAU,QAAQ,MAAM;AACjC,OAAO,KAAKE,EAAE,MAAM,sBAAsB;AAC1C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,SAAS,QAAQ,2BAA2B;AAErD,MAAMC,KAAK,GAAGA,CAACC,GAAG,EAAEC,UAAU,EAAEC,YAAY,KAAK;EAC7C,MAAMC,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAACC,OAAO,CAACJ,GAAG,CAACK,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;EACvF,MAAMC,eAAe,GAAIC,KAAK,IAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAACJ,OAAO,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;EAClG,MAAMC,UAAU,GAAGV,GAAG,CAACW,IAAI,CAACC,MAAM;EAClC,IAAIC,kBAAkB,GAAGb,GAAG,CAACc,OAAO,IAAId,GAAG,CAACc,OAAO,CAACC,YAAY;EAChE,IAAIC,aAAa;EACjB,IAAIH,kBAAkB,EAAE;IACpB,IAAIH,UAAU,EAAE;MACZ,OAAO,IAAIhB,UAAU,CAACuB,QAAQ,IAAIA,QAAQ,CAACC,KAAK,CAAC,IAAIC,KAAK,CAAC,0DAA0D,CAAC,CAAC,CAAC;IAC5H;IACA,IAAI,CAAChB,aAAa,EAAE;MAChB,OAAO,IAAIT,UAAU,CAACuB,QAAQ,IAAIA,QAAQ,CAACC,KAAK,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAAC;IACjH;IACA,IAAI,CAACjB,YAAY,EAAE;MACf,OAAO,IAAIR,UAAU,CAACuB,QAAQ,IAAIA,QAAQ,CAACC,KAAK,CAAC,IAAIC,KAAK,CAAE,gHAA+G,CAAC,CAAC,CAAC;IAClL;IACAH,aAAa,GAAGd,YAAY,CAACF,GAAG,CAACW,IAAI,CAAC;IACtCE,kBAAkB,GAAG,CAAC,CAACG,aAAa,CAACI,KAAK,CAACC,IAAI;EACnD;EACA;EACA,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIZ,UAAU,EAAE;IACZ,IAAI,CAACP,aAAa,EAAE;MAChB,OAAO,IAAIT,UAAU,CAACuB,QAAQ,IAAIA,QAAQ,CAACC,KAAK,CAAC,IAAIC,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAC9G;IACAG,YAAY,GAAG;MACXX,IAAI,EAAEX,GAAG,CAACW;IACd,CAAC;EACL,CAAC,MACI;IACD,MAAMA,IAAI,GAAGE,kBAAkB,GAAGG,aAAa,CAACO,KAAK,GAAGvB,GAAG,CAACW,IAAI;IAChE,IAAIR,aAAa,EAAE;MACfmB,YAAY,GAAG;QACXX;MACJ,CAAC;IACL,CAAC,MACI;MACD,MAAMa,MAAM,GAAGC,MAAM,CAACC,IAAI,CAAC1B,GAAG,CAACW,IAAI,CAAC,CAACgB,MAAM,CAAC,CAACC,GAAG,EAAEpB,KAAK,KAAK;QACxD,MAAMqB,KAAK,GAAG7B,GAAG,CAACW,IAAI,CAACH,KAAK,CAAC;QAC7BoB,GAAG,CAACpB,KAAK,CAAC,GAAGD,eAAe,CAACC,KAAK,CAAC,GAAGsB,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,GAAGA,KAAK;QACnE,OAAOD,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACNN,YAAY,GAAG;QAAEE,MAAM,EAAEA;MAAO,CAAC;IACrC;EACJ;EACA,IAAIX,kBAAkB,IAAIV,aAAa,EAAE;IACrC,MAAM6B,IAAI,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC3BD,IAAI,CAACE,MAAM,CAAC,YAAY,EAAEJ,IAAI,CAACC,SAAS,CAACT,YAAY,CAACX,IAAI,CAAC,CAAC;IAC5D,MAAMwB,GAAG,GAAG,CAAC,CAAC;IACd,MAAMf,KAAK,GAAGJ,aAAa,CAACI,KAAK;IACjC,IAAIgB,CAAC,GAAG,CAAC;IACThB,KAAK,CAACiB,OAAO,CAACC,KAAK,IAAI;MACnBH,GAAG,CAAC,EAAEC,CAAC,CAAC,GAAGE,KAAK;IACpB,CAAC,CAAC;IACFN,IAAI,CAACE,MAAM,CAAC,KAAK,EAAEJ,IAAI,CAACC,SAAS,CAACI,GAAG,CAAC,CAAC;IACvCC,CAAC,GAAG,CAAC;IACLhB,KAAK,CAACiB,OAAO,CAAC,CAACE,CAAC,EAAEC,IAAI,KAAK;MACvBR,IAAI,CAACE,MAAM,CAAC,EAAEE,CAAC,GAAG,EAAE,EAAEI,IAAI,EAAEA,IAAI,CAACC,IAAI,CAAC;IAC1C,CAAC,CAAC;IACFnB,YAAY,CAACX,IAAI,GAAGqB,IAAI;EAC5B;EACA;EACA,OAAO/B,UAAU,CAACyC,OAAO,CAAC1C,GAAG,CAACK,MAAM,EAAEL,GAAG,CAAC2C,GAAG,EAAE;IAC3CC,OAAO,EAAE,UAAU;IACnBC,YAAY,EAAE,MAAM;IACpBC,cAAc,EAAE,KAAK;IACrB,GAAGxB,YAAY;IACf,GAAGtB,GAAG,CAACc;EACX,CAAC,CAAC;AACN,CAAC;AACD,MAAMiC,YAAY,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;EAC1C,IAAID,MAAM,IAAIC,WAAW,EAAE;IACvB,MAAMC,MAAM,GAAGD,WAAW,CACrBvB,IAAI,CAAC,CAAC,CACNC,MAAM,CAAC,CAACwB,OAAO,EAAEV,IAAI,KAAKU,OAAO,CAACC,GAAG,CAACX,IAAI,EAAEQ,WAAW,CAACI,MAAM,CAACZ,IAAI,CAAC,CAAC,EAAEO,MAAM,CAAC;IACnF,OAAOE,MAAM;EACjB;EACA,OAAOD,WAAW,IAAID,MAAM;AAChC,CAAC;AACD,SAASM,UAAUA,CAAC,GAAGC,MAAM,EAAE;EAC3B,MAAMC,MAAM,GAAGD,MAAM,CAACE,IAAI,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,WAAW,CAAC;EAC7D,IAAI,OAAOF,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOD,MAAM,CAACA,MAAM,CAAC3C,MAAM,GAAG,CAAC,CAAC;EACpC;EACA,OAAO4C,MAAM;AACjB;AACA,SAASG,gCAAgCA,CAACC,OAAO,EAAE;EAC/C;EACA;EACA;EACA;EACA,IAAIT,OAAO,GAAGS,OAAO,CAACT,OAAO,IAAIS,OAAO,CAACT,OAAO,YAAYtD,WAAW,GACjE+D,OAAO,CAACT,OAAO,GACf,IAAItD,WAAW,CAAC+D,OAAO,CAACT,OAAO,CAAC;EACtC,IAAIS,OAAO,CAACC,eAAe,EAAE;IACzB,MAAM;MAAEpB,IAAI;MAAEqB;IAAQ,CAAC,GAAGF,OAAO,CAACC,eAAe;IACjD;IACA;IACA;IACA,IAAIpB,IAAI,IAAI,CAACU,OAAO,CAACY,GAAG,CAAC,2BAA2B,CAAC,EAAE;MACnDZ,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEX,IAAI,CAAC;IAC5D;IACA,IAAIqB,OAAO,IAAI,CAACX,OAAO,CAACY,GAAG,CAAC,8BAA8B,CAAC,EAAE;MACzDZ,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEU,OAAO,CAAC;IAClE;EACJ;EACA,OAAOX,OAAO;AAClB;;AAEA;AACA,MAAMa,eAAe,SAASvE,UAAU,CAAC;EACrCQ,UAAU;EACVa,OAAO;EACPmD,SAAS;EACT3E,KAAK,GAAGA,KAAK;EACb4E,WAAWA,CAACjE,UAAU,EAAEa,OAAO,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAACb,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,OAAO,GAAGA,OAAO;IACtB,IAAI,IAAI,CAACA,OAAO,CAACqD,gBAAgB,EAAE;MAC/B,IAAI,CAAC7E,KAAK,GAAG,IAAI,CAACwB,OAAO,CAACqD,gBAAgB;IAC9C;IACA,IAAI,CAACF,SAAS,GAAIG,SAAS,IAAK,IAAIzE,YAAY,CAAEsB,QAAQ,IAAK;MAC3D,MAAM2C,OAAO,GAAGQ,SAAS,CAACC,UAAU,CAAC,CAAC;MACtC;MACA,MAAMC,IAAI,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;QACxB,OAAOlB,UAAU,CAACM,OAAO,CAACW,GAAG,CAAC,EAAE,IAAI,CAACzD,OAAO,CAACyD,GAAG,CAAC,EAAEC,IAAI,CAAC;MAC5D,CAAC;MACD,IAAInE,MAAM,GAAGiE,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC;MACnC,MAAMG,YAAY,GAAGH,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;MAC/C,MAAMI,iBAAiB,GAAGJ,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAC1D,MAAM3B,GAAG,GAAG2B,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC;MAClC,MAAMK,eAAe,GAAGL,IAAI,CAAC,iBAAiB,CAAC;MAC/C,MAAMvD,YAAY,GAAGuD,IAAI,CAAC,cAAc,CAAC;MACzC,MAAMM,gBAAgB,GAAG,IAAI,CAAC9D,OAAO,CAAC8D,gBAAgB,KAAK,IAAI;MAC/D,MAAMC,OAAO,GAAGT,SAAS,CAACU,KAAK,CAACC,WAAW,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,qBAAqB,IAAID,GAAG,CAACb,SAAS,KAAK,OAAO,CAAC;MACxH,IAAIQ,gBAAgB,IAAIC,OAAO,EAAE;QAC7BxE,MAAM,GAAG,KAAK;MAClB;MACA,MAAML,GAAG,GAAG;QACRK,MAAM;QACNsC,GAAG,EAAE,OAAOA,GAAG,KAAK,UAAU,GAAGA,GAAG,CAACyB,SAAS,CAAC,GAAGzB,GAAG;QACrDhC,IAAI,EAAE;UACFwE,aAAa,EAAEf,SAAS,CAACe,aAAa;UACtCC,SAAS,EAAEhB,SAAS,CAACgB;QACzB,CAAC;QACDtE,OAAO,EAAE;UACL6D,eAAe;UACf5D,YAAY;UACZoC,OAAO,EAAE,IAAI,CAACrC,OAAO,CAACqC;QAC1B;MACJ,CAAC;MACD,IAAIuB,iBAAiB,EAAE;QACnB1E,GAAG,CAACW,IAAI,CAAC0E,UAAU,GAAGjB,SAAS,CAACiB,UAAU;MAC9C;MACA,IAAIZ,YAAY,EAAE;QACdzE,GAAG,CAACW,IAAI,CAACmE,KAAK,GAAG,IAAI,CAACxF,KAAK,CAAC8E,SAAS,CAACU,KAAK,CAAC;MAChD;MACA,MAAM3B,OAAO,GAAGQ,gCAAgC,CAACC,OAAO,CAAC;MACzD5D,GAAG,CAACc,OAAO,CAACqC,OAAO,GAAGJ,YAAY,CAAC/C,GAAG,CAACc,OAAO,CAACqC,OAAO,EAAEA,OAAO,CAAC;MAChE,MAAMmC,GAAG,GAAGvF,KAAK,CAACC,GAAG,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACa,OAAO,CAACZ,YAAY,CAAC,CAACqF,SAAS,CAAC;QACzEC,IAAI,EAAEC,QAAQ,IAAI;UACdrB,SAAS,CAACsB,UAAU,CAAC;YAAED;UAAS,CAAC,CAAC;UAClCxE,QAAQ,CAACuE,IAAI,CAACC,QAAQ,CAAC9E,IAAI,CAAC;QAChC,CAAC;QACDO,KAAK,EAAEyE,GAAG,IAAI1E,QAAQ,CAACC,KAAK,CAACyE,GAAG,CAAC;QACjCC,QAAQ,EAAEA,CAAA,KAAM3E,QAAQ,CAAC2E,QAAQ,CAAC;MACtC,CAAC,CAAC;MACF,OAAO,MAAM;QACT,IAAI,CAACN,GAAG,CAACO,MAAM,EAAE;UACbP,GAAG,CAACQ,WAAW,CAAC,CAAC;QACrB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACApD,OAAOA,CAACqD,EAAE,EAAE;IACR,OAAO,IAAI,CAAC9B,SAAS,CAAC8B,EAAE,CAAC;EAC7B;AACJ;AACA,MAAMC,QAAQ,CAAC;EACX/F,UAAU;EACViE,WAAWA,CAACjE,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAgG,MAAMA,CAACnF,OAAO,EAAE;IACZ,OAAO,IAAIkD,eAAe,CAAC,IAAI,CAAC/D,UAAU,EAAEa,OAAO,CAAC;EACxD;EACA,OAAOoF,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFJ,QAAQ,EAAlBzG,EAAE,CAAA8G,QAAA,CAAkCzG,EAAE,CAAC0G,UAAU;EAAA;EAC1I,OAAOC,KAAK,kBAD6EhH,EAAE,CAAAiH,kBAAA;IAAAC,KAAA,EACYT,QAAQ;IAAAU,OAAA,EAARV,QAAQ,CAAAE,IAAA;IAAAS,UAAA,EAAc;EAAM;AACvI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FrH,EAAE,CAAAsH,iBAAA,CAGJb,QAAQ,EAAc,CAAC;IACtGc,IAAI,EAAEtH,UAAU;IAChBuH,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElH,EAAE,CAAC0G;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAE7E,MAAMU,QAAQ,GAAG;EACbC,aAAa,EAAE,EAAE;EACjBC,QAAQ,EAAE,EAAE;EACZC,GAAG,EAAE,SAAS;EACd9G,MAAM,EAAE;AACZ,CAAC;AACD,MAAM+G,oBAAoB,SAAS3H,UAAU,CAAC;EAC1CQ,UAAU;EACVa,OAAO;EACPuG,OAAO;EACPJ,aAAa;EACbC,QAAQ;EACR5H,KAAK,GAAGA,KAAK;EACb4E,WAAWA,CAACjE,UAAU,EAAEa,OAAO,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAACb,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmG,aAAa,GAAGnG,OAAO,CAACmG,aAAa,IAAID,QAAQ,CAACC,aAAa;IACpE,IAAI,CAACC,QAAQ,GAAGpG,OAAO,CAACoG,QAAQ,IAAIF,QAAQ,CAACE,QAAQ;IACrD,IAAI,IAAI,CAACpG,OAAO,CAACqD,gBAAgB,EAAE;MAC/B,IAAI,CAAC7E,KAAK,GAAG,IAAI,CAACwB,OAAO,CAACqD,gBAAgB;IAC9C;IACA,MAAMmD,YAAY,GAAIC,UAAU,IAAK;MACjC,OAAO,IAAI5H,YAAY,CAAEsB,QAAQ,IAAK;QAClC,MAAMN,IAAI,GAAG,IAAI,CAAC6G,UAAU,CAACD,UAAU,CAAC;QACxC,MAAMpE,OAAO,GAAG,IAAI,CAACsE,aAAa,CAACF,UAAU,CAAC;QAC9C,MAAM;UAAElH,MAAM;UAAE8G,GAAG;UAAExC;QAAgB,CAAC,GAAG,IAAI,CAAC+C,aAAa,CAACH,UAAU,CAAC;QACvE,IAAI,OAAOJ,GAAG,KAAK,UAAU,EAAE;UAC3B,MAAM,IAAIhG,KAAK,CAAE,gDAA+C,CAAC;QACrE;QACA,MAAMnB,GAAG,GAAG;UACRK,MAAM;UACNsC,GAAG,EAAEwE,GAAG;UACRxG,IAAI,EAAEA,IAAI;UACVG,OAAO,EAAE;YACL6D,eAAe;YACfxB;UACJ;QACJ,CAAC;QACD,MAAMmC,GAAG,GAAGvF,KAAK,CAACC,GAAG,EAAE,IAAI,CAACC,UAAU,EAAE,MAAM;UAC1C,MAAM,IAAIkB,KAAK,CAAC,0DAA0D,CAAC;QAC/E,CAAC,CAAC,CAACoE,SAAS,CAAC;UACTC,IAAI,EAAEmC,MAAM,IAAI1G,QAAQ,CAACuE,IAAI,CAACmC,MAAM,CAAChH,IAAI,CAAC;UAC1CO,KAAK,EAAEyE,GAAG,IAAI1E,QAAQ,CAACC,KAAK,CAACyE,GAAG,CAAC;UACjCC,QAAQ,EAAEA,CAAA,KAAM3E,QAAQ,CAAC2E,QAAQ,CAAC;QACtC,CAAC,CAAC;QACF,OAAO,MAAM;UACT,IAAI,CAACN,GAAG,CAACO,MAAM,EAAE;YACbP,GAAG,CAACQ,WAAW,CAAC,CAAC;UACrB;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC;IACD,MAAM8B,QAAQ,GAAG9G,OAAO,CAAC8G,QAAQ,KAC3BxD,SAAS,IAAK;MACZ,OAAO,IAAI,CAACyD,cAAc,CAACzD,SAAS,CAAC;IACzC,CAAC,CAAC;IACN,IAAI,CAACiD,OAAO,GAAG,IAAIvH,SAAS,CAAC;MACzBmH,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBU,QAAQ;MACRN;IACJ,CAAC,CAAC;EACN;EACAI,aAAaA,CAACH,UAAU,EAAE;IACtB,MAAM3D,OAAO,GAAG2D,UAAU,CAAC,CAAC,CAAC,CAAClD,UAAU,CAAC,CAAC;IAC1C,OAAO;MACHhE,MAAM,EAAEiD,UAAU,CAACM,OAAO,CAACvD,MAAM,EAAE,IAAI,CAACS,OAAO,CAACT,MAAM,EAAE2G,QAAQ,CAAC3G,MAAM,CAAC;MACxE8G,GAAG,EAAE7D,UAAU,CAACM,OAAO,CAACuD,GAAG,EAAE,IAAI,CAACrG,OAAO,CAACqG,GAAG,EAAEH,QAAQ,CAACG,GAAG,CAAC;MAC5DxC,eAAe,EAAErB,UAAU,CAACM,OAAO,CAACe,eAAe,EAAE,IAAI,CAAC7D,OAAO,CAAC6D,eAAe;IACrF,CAAC;EACL;EACA6C,UAAUA,CAACD,UAAU,EAAE;IACnB,OAAOA,UAAU,CAACpF,GAAG,CAACiC,SAAS,IAAI;MAC/B,MAAMM,iBAAiB,GAAGpB,UAAU,CAACc,SAAS,CAACC,UAAU,CAAC,CAAC,CAACK,iBAAiB,EAAE,IAAI,CAAC5D,OAAO,CAAC4D,iBAAiB,EAAE,KAAK,CAAC;MACrH,MAAMD,YAAY,GAAGnB,UAAU,CAACc,SAAS,CAACC,UAAU,CAAC,CAAC,CAACI,YAAY,EAAE,IAAI,CAAC3D,OAAO,CAAC2D,YAAY,EAAE,IAAI,CAAC;MACrG,MAAM9D,IAAI,GAAG;QACTwE,aAAa,EAAEf,SAAS,CAACe,aAAa;QACtCC,SAAS,EAAEhB,SAAS,CAACgB;MACzB,CAAC;MACD,IAAIV,iBAAiB,EAAE;QACnB/D,IAAI,CAAC0E,UAAU,GAAGjB,SAAS,CAACiB,UAAU;MAC1C;MACA,IAAIZ,YAAY,EAAE;QACd9D,IAAI,CAACmE,KAAK,GAAG,IAAI,CAACxF,KAAK,CAAC8E,SAAS,CAACU,KAAK,CAAC;MAC5C;MACA,OAAOnE,IAAI;IACf,CAAC,CAAC;EACN;EACA8G,aAAaA,CAACF,UAAU,EAAE;IACtB,OAAOA,UAAU,CAAC5F,MAAM,CAAC,CAACwB,OAAO,EAAEiB,SAAS,KAAK;MAC7C,OAAOrB,YAAY,CAACI,OAAO,EAAEiB,SAAS,CAACC,UAAU,CAAC,CAAC,CAAClB,OAAO,CAAC;IAChE,CAAC,EAAEQ,gCAAgC,CAAC;MAChCR,OAAO,EAAE,IAAI,CAACrC,OAAO,CAACqC,OAAO;MAC7BU,eAAe,EAAE0D,UAAU,CAAC,CAAC,CAAC,EAAElD,UAAU,CAAC,CAAC,EAAER;IAClD,CAAC,CAAC,CAAC;EACP;EACAgE,cAAcA,CAACzD,SAAS,EAAE;IACtB,MAAMR,OAAO,GAAGQ,SAAS,CAACC,UAAU,CAAC,CAAC;IACtC,IAAIT,OAAO,CAACkE,YAAY,EAAE;MACtB,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAClD;IACA,MAAM/E,OAAO,GAAGS,OAAO,CAACT,OAAO,IAAIS,OAAO,CAACT,OAAO,CAACzB,IAAI,CAAC,CAAC,CAACS,GAAG,CAAEgG,CAAC,IAAKvE,OAAO,CAACT,OAAO,CAACiF,GAAG,CAACD,CAAC,CAAC,CAAC;IAC5F,MAAME,IAAI,GAAGvG,IAAI,CAACC,SAAS,CAAC;MACxB0C,YAAY,EAAEb,OAAO,CAACa,YAAY;MAClCC,iBAAiB,EAAEd,OAAO,CAACc,iBAAiB;MAC5CvB;IACJ,CAAC,CAAC;IACF,OAAOG,UAAU,CAACM,OAAO,CAACuD,GAAG,EAAE,IAAI,CAACrG,OAAO,CAACqG,GAAG,CAAC,GAAGkB,IAAI;EAC3D;EACA3F,OAAOA,CAACqD,EAAE,EAAE;IACR,OAAO,IAAI,CAACsB,OAAO,CAAC3E,OAAO,CAACqD,EAAE,CAAC;EACnC;AACJ;AACA,MAAMuC,aAAa,CAAC;EAChBrI,UAAU;EACViE,WAAWA,CAACjE,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAgG,MAAMA,CAACnF,OAAO,EAAE;IACZ,OAAO,IAAIsG,oBAAoB,CAAC,IAAI,CAACnH,UAAU,EAAEa,OAAO,CAAC;EAC7D;EACA,OAAOoF,IAAI,YAAAqC,sBAAAnC,CAAA;IAAA,YAAAA,CAAA,IAAwFkC,aAAa,EApIvB/I,EAAE,CAAA8G,QAAA,CAoIuCzG,EAAE,CAAC0G,UAAU;EAAA;EAC/I,OAAOC,KAAK,kBArI6EhH,EAAE,CAAAiH,kBAAA;IAAAC,KAAA,EAqIY6B,aAAa;IAAA5B,OAAA,EAAb4B,aAAa,CAAApC,IAAA;IAAAS,UAAA,EAAc;EAAM;AAC5I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvI6FrH,EAAE,CAAAsH,iBAAA,CAuIJyB,aAAa,EAAc,CAAC;IAC3GxB,IAAI,EAAEtH,UAAU;IAChBuH,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAElH,EAAE,CAAC0G;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE7E;;AAEA;AACA;AACA;;AAEA,SAASgC,aAAa,EAAElB,oBAAoB,EAAEpB,QAAQ,EAAEhC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}