{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { Kind } from \"graphql\";\nimport { getFragmentFromSelection, getDefaultValues, getOperationDefinition, getTypenameFromResult, makeReference, isField, resultKeyNameFromField, isReference, shouldInclude, cloneDeep, addTypenameToDocument, isNonEmptyArray, argumentsObjectFromField, canonicalStringify } from \"../../utilities/index.js\";\nimport { isArray, makeProcessedFieldsMerger, fieldNameFromStoreName, storeValueIsStoreObject, extractFragmentContext } from \"./helpers.js\";\nimport { normalizeReadFieldOptions } from \"./policies.js\";\n// Since there are only four possible combinations of context.clientOnly and\n// context.deferred values, we should need at most four \"flavors\" of any given\n// WriteContext. To avoid creating multiple copies of the same context, we cache\n// the contexts in the context.flavors Map (shared by all flavors) according to\n// their clientOnly and deferred values (always in that order).\nfunction getContextFlavor(context, clientOnly, deferred) {\n  var key = \"\".concat(clientOnly).concat(deferred);\n  var flavored = context.flavors.get(key);\n  if (!flavored) {\n    context.flavors.set(key, flavored = context.clientOnly === clientOnly && context.deferred === deferred ? context : __assign(__assign({}, context), {\n      clientOnly: clientOnly,\n      deferred: deferred\n    }));\n  }\n  return flavored;\n}\nvar StoreWriter = /** @class */function () {\n  function StoreWriter(cache, reader, fragments) {\n    this.cache = cache;\n    this.reader = reader;\n    this.fragments = fragments;\n  }\n  StoreWriter.prototype.writeToStore = function (store, _a) {\n    var _this = this;\n    var query = _a.query,\n      result = _a.result,\n      dataId = _a.dataId,\n      variables = _a.variables,\n      overwrite = _a.overwrite;\n    var operationDefinition = getOperationDefinition(query);\n    var merger = makeProcessedFieldsMerger();\n    variables = __assign(__assign({}, getDefaultValues(operationDefinition)), variables);\n    var context = __assign(__assign({\n      store: store,\n      written: Object.create(null),\n      merge: function (existing, incoming) {\n        return merger.merge(existing, incoming);\n      },\n      variables: variables,\n      varString: canonicalStringify(variables)\n    }, extractFragmentContext(query, this.fragments)), {\n      overwrite: !!overwrite,\n      incomingById: new Map(),\n      clientOnly: false,\n      deferred: false,\n      flavors: new Map()\n    });\n    var ref = this.processSelectionSet({\n      result: result || Object.create(null),\n      dataId: dataId,\n      selectionSet: operationDefinition.selectionSet,\n      mergeTree: {\n        map: new Map()\n      },\n      context: context\n    });\n    if (!isReference(ref)) {\n      throw newInvariantError(12, result);\n    }\n    // So far, the store has not been modified, so now it's time to process\n    // context.incomingById and merge those incoming fields into context.store.\n    context.incomingById.forEach(function (_a, dataId) {\n      var storeObject = _a.storeObject,\n        mergeTree = _a.mergeTree,\n        fieldNodeSet = _a.fieldNodeSet;\n      var entityRef = makeReference(dataId);\n      if (mergeTree && mergeTree.map.size) {\n        var applied = _this.applyMerges(mergeTree, entityRef, storeObject, context);\n        if (isReference(applied)) {\n          // Assume References returned by applyMerges have already been merged\n          // into the store. See makeMergeObjectsFunction in policies.ts for an\n          // example of how this can happen.\n          return;\n        }\n        // Otherwise, applyMerges returned a StoreObject, whose fields we should\n        // merge into the store (see store.merge statement below).\n        storeObject = applied;\n      }\n      if (globalThis.__DEV__ !== false && !context.overwrite) {\n        var fieldsWithSelectionSets_1 = Object.create(null);\n        fieldNodeSet.forEach(function (field) {\n          if (field.selectionSet) {\n            fieldsWithSelectionSets_1[field.name.value] = true;\n          }\n        });\n        var hasSelectionSet_1 = function (storeFieldName) {\n          return fieldsWithSelectionSets_1[fieldNameFromStoreName(storeFieldName)] === true;\n        };\n        var hasMergeFunction_1 = function (storeFieldName) {\n          var childTree = mergeTree && mergeTree.map.get(storeFieldName);\n          return Boolean(childTree && childTree.info && childTree.info.merge);\n        };\n        Object.keys(storeObject).forEach(function (storeFieldName) {\n          // If a merge function was defined for this field, trust that it\n          // did the right thing about (not) clobbering data. If the field\n          // has no selection set, it's a scalar field, so it doesn't need\n          // a merge function (even if it's an object, like JSON data).\n          if (hasSelectionSet_1(storeFieldName) && !hasMergeFunction_1(storeFieldName)) {\n            warnAboutDataLoss(entityRef, storeObject, storeFieldName, context.store);\n          }\n        });\n      }\n      store.merge(dataId, storeObject);\n    });\n    // Any IDs written explicitly to the cache will be retained as\n    // reachable root IDs for garbage collection purposes. Although this\n    // logic includes root IDs like ROOT_QUERY and ROOT_MUTATION, their\n    // retainment counts are effectively ignored because cache.gc() always\n    // includes them in its root ID set.\n    store.retain(ref.__ref);\n    return ref;\n  };\n  StoreWriter.prototype.processSelectionSet = function (_a) {\n    var _this = this;\n    var dataId = _a.dataId,\n      result = _a.result,\n      selectionSet = _a.selectionSet,\n      context = _a.context,\n      // This object allows processSelectionSet to report useful information\n      // to its callers without explicitly returning that information.\n      mergeTree = _a.mergeTree;\n    var policies = this.cache.policies;\n    // This variable will be repeatedly updated using context.merge to\n    // accumulate all fields that need to be written into the store.\n    var incoming = Object.create(null);\n    // If typename was not passed in, infer it. Note that typename is\n    // always passed in for tricky-to-infer cases such as \"Query\" for\n    // ROOT_QUERY.\n    var typename = dataId && policies.rootTypenamesById[dataId] || getTypenameFromResult(result, selectionSet, context.fragmentMap) || dataId && context.store.get(dataId, \"__typename\");\n    if (\"string\" === typeof typename) {\n      incoming.__typename = typename;\n    }\n    // This readField function will be passed as context.readField in the\n    // KeyFieldsContext object created within policies.identify (called below).\n    // In addition to reading from the existing context.store (thanks to the\n    // policies.readField(options, context) line at the very bottom), this\n    // version of readField can read from Reference objects that are currently\n    // pending in context.incomingById, which is important whenever keyFields\n    // need to be extracted from a child object that processSelectionSet has\n    // turned into a Reference.\n    var readField = function () {\n      var options = normalizeReadFieldOptions(arguments, incoming, context.variables);\n      if (isReference(options.from)) {\n        var info = context.incomingById.get(options.from.__ref);\n        if (info) {\n          var result_1 = policies.readField(__assign(__assign({}, options), {\n            from: info.storeObject\n          }), context);\n          if (result_1 !== void 0) {\n            return result_1;\n          }\n        }\n      }\n      return policies.readField(options, context);\n    };\n    var fieldNodeSet = new Set();\n    this.flattenFields(selectionSet, result,\n    // This WriteContext will be the default context value for fields returned\n    // by the flattenFields method, but some fields may be assigned a modified\n    // context, depending on the presence of @client and other directives.\n    context, typename).forEach(function (context, field) {\n      var _a;\n      var resultFieldKey = resultKeyNameFromField(field);\n      var value = result[resultFieldKey];\n      fieldNodeSet.add(field);\n      if (value !== void 0) {\n        var storeFieldName = policies.getStoreFieldName({\n          typename: typename,\n          fieldName: field.name.value,\n          field: field,\n          variables: context.variables\n        });\n        var childTree = getChildMergeTree(mergeTree, storeFieldName);\n        var incomingValue = _this.processFieldValue(value, field,\n        // Reset context.clientOnly and context.deferred to their default\n        // values before processing nested selection sets.\n        field.selectionSet ? getContextFlavor(context, false, false) : context, childTree);\n        // To determine if this field holds a child object with a merge function\n        // defined in its type policy (see PR #7070), we need to figure out the\n        // child object's __typename.\n        var childTypename = void 0;\n        // The field's value can be an object that has a __typename only if the\n        // field has a selection set. Otherwise incomingValue is scalar.\n        if (field.selectionSet && (isReference(incomingValue) || storeValueIsStoreObject(incomingValue))) {\n          childTypename = readField(\"__typename\", incomingValue);\n        }\n        var merge = policies.getMergeFunction(typename, field.name.value, childTypename);\n        if (merge) {\n          childTree.info = {\n            // TODO Check compatibility against any existing childTree.field?\n            field: field,\n            typename: typename,\n            merge: merge\n          };\n        } else {\n          maybeRecycleChildMergeTree(mergeTree, storeFieldName);\n        }\n        incoming = context.merge(incoming, (_a = {}, _a[storeFieldName] = incomingValue, _a));\n      } else if (globalThis.__DEV__ !== false && !context.clientOnly && !context.deferred && !addTypenameToDocument.added(field) &&\n      // If the field has a read function, it may be a synthetic field or\n      // provide a default value, so its absence from the written data should\n      // not be cause for alarm.\n      !policies.getReadFunction(typename, field.name.value)) {\n        globalThis.__DEV__ !== false && invariant.error(13, resultKeyNameFromField(field), result);\n      }\n    });\n    // Identify the result object, even if dataId was already provided,\n    // since we always need keyObject below.\n    try {\n      var _b = policies.identify(result, {\n          typename: typename,\n          selectionSet: selectionSet,\n          fragmentMap: context.fragmentMap,\n          storeObject: incoming,\n          readField: readField\n        }),\n        id = _b[0],\n        keyObject = _b[1];\n      // If dataId was not provided, fall back to the id just generated by\n      // policies.identify.\n      dataId = dataId || id;\n      // Write any key fields that were used during identification, even if\n      // they were not mentioned in the original query.\n      if (keyObject) {\n        // TODO Reverse the order of the arguments?\n        incoming = context.merge(incoming, keyObject);\n      }\n    } catch (e) {\n      // If dataId was provided, tolerate failure of policies.identify.\n      if (!dataId) throw e;\n    }\n    if (\"string\" === typeof dataId) {\n      var dataRef = makeReference(dataId);\n      // Avoid processing the same entity object using the same selection\n      // set more than once. We use an array instead of a Set since most\n      // entity IDs will be written using only one selection set, so the\n      // size of this array is likely to be very small, meaning indexOf is\n      // likely to be faster than Set.prototype.has.\n      var sets = context.written[dataId] || (context.written[dataId] = []);\n      if (sets.indexOf(selectionSet) >= 0) return dataRef;\n      sets.push(selectionSet);\n      // If we're about to write a result object into the store, but we\n      // happen to know that the exact same (===) result object would be\n      // returned if we were to reread the result with the same inputs,\n      // then we can skip the rest of the processSelectionSet work for\n      // this object, and immediately return a Reference to it.\n      if (this.reader && this.reader.isFresh(result, dataRef, selectionSet, context)) {\n        return dataRef;\n      }\n      var previous_1 = context.incomingById.get(dataId);\n      if (previous_1) {\n        previous_1.storeObject = context.merge(previous_1.storeObject, incoming);\n        previous_1.mergeTree = mergeMergeTrees(previous_1.mergeTree, mergeTree);\n        fieldNodeSet.forEach(function (field) {\n          return previous_1.fieldNodeSet.add(field);\n        });\n      } else {\n        context.incomingById.set(dataId, {\n          storeObject: incoming,\n          // Save a reference to mergeTree only if it is not empty, because\n          // empty MergeTrees may be recycled by maybeRecycleChildMergeTree and\n          // reused for entirely different parts of the result tree.\n          mergeTree: mergeTreeIsEmpty(mergeTree) ? void 0 : mergeTree,\n          fieldNodeSet: fieldNodeSet\n        });\n      }\n      return dataRef;\n    }\n    return incoming;\n  };\n  StoreWriter.prototype.processFieldValue = function (value, field, context, mergeTree) {\n    var _this = this;\n    if (!field.selectionSet || value === null) {\n      // In development, we need to clone scalar values so that they can be\n      // safely frozen with maybeDeepFreeze in readFromStore.ts. In production,\n      // it's cheaper to store the scalar values directly in the cache.\n      return globalThis.__DEV__ !== false ? cloneDeep(value) : value;\n    }\n    if (isArray(value)) {\n      return value.map(function (item, i) {\n        var value = _this.processFieldValue(item, field, context, getChildMergeTree(mergeTree, i));\n        maybeRecycleChildMergeTree(mergeTree, i);\n        return value;\n      });\n    }\n    return this.processSelectionSet({\n      result: value,\n      selectionSet: field.selectionSet,\n      context: context,\n      mergeTree: mergeTree\n    });\n  };\n  // Implements https://spec.graphql.org/draft/#sec-Field-Collection, but with\n  // some additions for tracking @client and @defer directives.\n  StoreWriter.prototype.flattenFields = function (selectionSet, result, context, typename) {\n    if (typename === void 0) {\n      typename = getTypenameFromResult(result, selectionSet, context.fragmentMap);\n    }\n    var fieldMap = new Map();\n    var policies = this.cache.policies;\n    var limitingTrie = new Trie(false); // No need for WeakMap, since limitingTrie does not escape.\n    (function flatten(selectionSet, inheritedContext) {\n      var visitedNode = limitingTrie.lookup(selectionSet,\n      // Because we take inheritedClientOnly and inheritedDeferred into\n      // consideration here (in addition to selectionSet), it's possible for\n      // the same selection set to be flattened more than once, if it appears\n      // in the query with different @client and/or @directive configurations.\n      inheritedContext.clientOnly, inheritedContext.deferred);\n      if (visitedNode.visited) return;\n      visitedNode.visited = true;\n      selectionSet.selections.forEach(function (selection) {\n        if (!shouldInclude(selection, context.variables)) return;\n        var clientOnly = inheritedContext.clientOnly,\n          deferred = inheritedContext.deferred;\n        if (\n        // Since the presence of @client or @defer on this field can only\n        // cause clientOnly or deferred to become true, we can skip the\n        // forEach loop if both clientOnly and deferred are already true.\n        !(clientOnly && deferred) && isNonEmptyArray(selection.directives)) {\n          selection.directives.forEach(function (dir) {\n            var name = dir.name.value;\n            if (name === \"client\") clientOnly = true;\n            if (name === \"defer\") {\n              var args = argumentsObjectFromField(dir, context.variables);\n              // The @defer directive takes an optional args.if boolean\n              // argument, similar to @include(if: boolean). Note that\n              // @defer(if: false) does not make context.deferred false, but\n              // instead behaves as if there was no @defer directive.\n              if (!args || args.if !== false) {\n                deferred = true;\n              }\n              // TODO In the future, we may want to record args.label using\n              // context.deferred, if a label is specified.\n            }\n          });\n        }\n\n        if (isField(selection)) {\n          var existing = fieldMap.get(selection);\n          if (existing) {\n            // If this field has been visited along another recursive path\n            // before, the final context should have clientOnly or deferred set\n            // to true only if *all* paths have the directive (hence the &&).\n            clientOnly = clientOnly && existing.clientOnly;\n            deferred = deferred && existing.deferred;\n          }\n          fieldMap.set(selection, getContextFlavor(context, clientOnly, deferred));\n        } else {\n          var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n          if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n            throw newInvariantError(14, selection.name.value);\n          }\n          if (fragment && policies.fragmentMatches(fragment, typename, result, context.variables)) {\n            flatten(fragment.selectionSet, getContextFlavor(context, clientOnly, deferred));\n          }\n        }\n      });\n    })(selectionSet, context);\n    return fieldMap;\n  };\n  StoreWriter.prototype.applyMerges = function (mergeTree, existing, incoming, context, getStorageArgs) {\n    var _a;\n    var _this = this;\n    if (mergeTree.map.size && !isReference(incoming)) {\n      var e_1 =\n      // Items in the same position in different arrays are not\n      // necessarily related to each other, so when incoming is an array\n      // we process its elements as if there was no existing data.\n      !isArray(incoming) && (\n      // Likewise, existing must be either a Reference or a StoreObject\n      // in order for its fields to be safe to merge with the fields of\n      // the incoming object.\n      isReference(existing) || storeValueIsStoreObject(existing)) ? existing : void 0;\n      // This narrowing is implied by mergeTree.map.size > 0 and\n      // !isReference(incoming), though TypeScript understandably cannot\n      // hope to infer this type.\n      var i_1 = incoming;\n      // The options.storage objects provided to read and merge functions\n      // are derived from the identity of the parent object plus a\n      // sequence of storeFieldName strings/numbers identifying the nested\n      // field name path of each field value to be merged.\n      if (e_1 && !getStorageArgs) {\n        getStorageArgs = [isReference(e_1) ? e_1.__ref : e_1];\n      }\n      // It's possible that applying merge functions to this subtree will\n      // not change the incoming data, so this variable tracks the fields\n      // that did change, so we can create a new incoming object when (and\n      // only when) at least one incoming field has changed. We use a Map\n      // to preserve the type of numeric keys.\n      var changedFields_1;\n      var getValue_1 = function (from, name) {\n        return isArray(from) ? typeof name === \"number\" ? from[name] : void 0 : context.store.getFieldValue(from, String(name));\n      };\n      mergeTree.map.forEach(function (childTree, storeFieldName) {\n        var eVal = getValue_1(e_1, storeFieldName);\n        var iVal = getValue_1(i_1, storeFieldName);\n        // If we have no incoming data, leave any existing data untouched.\n        if (void 0 === iVal) return;\n        if (getStorageArgs) {\n          getStorageArgs.push(storeFieldName);\n        }\n        var aVal = _this.applyMerges(childTree, eVal, iVal, context, getStorageArgs);\n        if (aVal !== iVal) {\n          changedFields_1 = changedFields_1 || new Map();\n          changedFields_1.set(storeFieldName, aVal);\n        }\n        if (getStorageArgs) {\n          invariant(getStorageArgs.pop() === storeFieldName);\n        }\n      });\n      if (changedFields_1) {\n        // Shallow clone i so we can add changed fields to it.\n        incoming = isArray(i_1) ? i_1.slice(0) : __assign({}, i_1);\n        changedFields_1.forEach(function (value, name) {\n          incoming[name] = value;\n        });\n      }\n    }\n    if (mergeTree.info) {\n      return this.cache.policies.runMergeFunction(existing, incoming, mergeTree.info, context, getStorageArgs && (_a = context.store).getStorage.apply(_a, getStorageArgs));\n    }\n    return incoming;\n  };\n  return StoreWriter;\n}();\nexport { StoreWriter };\nvar emptyMergeTreePool = [];\nfunction getChildMergeTree(_a, name) {\n  var map = _a.map;\n  if (!map.has(name)) {\n    map.set(name, emptyMergeTreePool.pop() || {\n      map: new Map()\n    });\n  }\n  return map.get(name);\n}\nfunction mergeMergeTrees(left, right) {\n  if (left === right || !right || mergeTreeIsEmpty(right)) return left;\n  if (!left || mergeTreeIsEmpty(left)) return right;\n  var info = left.info && right.info ? __assign(__assign({}, left.info), right.info) : left.info || right.info;\n  var needToMergeMaps = left.map.size && right.map.size;\n  var map = needToMergeMaps ? new Map() : left.map.size ? left.map : right.map;\n  var merged = {\n    info: info,\n    map: map\n  };\n  if (needToMergeMaps) {\n    var remainingRightKeys_1 = new Set(right.map.keys());\n    left.map.forEach(function (leftTree, key) {\n      merged.map.set(key, mergeMergeTrees(leftTree, right.map.get(key)));\n      remainingRightKeys_1.delete(key);\n    });\n    remainingRightKeys_1.forEach(function (key) {\n      merged.map.set(key, mergeMergeTrees(right.map.get(key), left.map.get(key)));\n    });\n  }\n  return merged;\n}\nfunction mergeTreeIsEmpty(tree) {\n  return !tree || !(tree.info || tree.map.size);\n}\nfunction maybeRecycleChildMergeTree(_a, name) {\n  var map = _a.map;\n  var childTree = map.get(name);\n  if (childTree && mergeTreeIsEmpty(childTree)) {\n    emptyMergeTreePool.push(childTree);\n    map.delete(name);\n  }\n}\nvar warnings = new Set();\n// Note that this function is unused in production, and thus should be\n// pruned by any well-configured minifier.\nfunction warnAboutDataLoss(existingRef, incomingObj, storeFieldName, store) {\n  var getChild = function (objOrRef) {\n    var child = store.getFieldValue(objOrRef, storeFieldName);\n    return typeof child === \"object\" && child;\n  };\n  var existing = getChild(existingRef);\n  if (!existing) return;\n  var incoming = getChild(incomingObj);\n  if (!incoming) return;\n  // It's always safe to replace a reference, since it refers to data\n  // safely stored elsewhere.\n  if (isReference(existing)) return;\n  // If the values are structurally equivalent, we do not need to worry\n  // about incoming replacing existing.\n  if (equal(existing, incoming)) return;\n  // If we're replacing every key of the existing object, then the\n  // existing data would be overwritten even if the objects were\n  // normalized, so warning would not be helpful here.\n  if (Object.keys(existing).every(function (key) {\n    return store.getFieldValue(incoming, key) !== void 0;\n  })) {\n    return;\n  }\n  var parentType = store.getFieldValue(existingRef, \"__typename\") || store.getFieldValue(incomingObj, \"__typename\");\n  var fieldName = fieldNameFromStoreName(storeFieldName);\n  var typeDotName = \"\".concat(parentType, \".\").concat(fieldName);\n  // Avoid warning more than once for the same type and field name.\n  if (warnings.has(typeDotName)) return;\n  warnings.add(typeDotName);\n  var childTypenames = [];\n  // Arrays do not have __typename fields, and always need a custom merge\n  // function, even if their elements are normalized entities.\n  if (!isArray(existing) && !isArray(incoming)) {\n    [existing, incoming].forEach(function (child) {\n      var typename = store.getFieldValue(child, \"__typename\");\n      if (typeof typename === \"string\" && !childTypenames.includes(typename)) {\n        childTypenames.push(typename);\n      }\n    });\n  }\n  globalThis.__DEV__ !== false && invariant.warn(15, fieldName, parentType, childTypenames.length ? \"either ensure all objects of type \" + childTypenames.join(\" and \") + \" have an ID or a custom merge function, or \" : \"\", typeDotName, __assign({}, existing), __assign({}, incoming));\n}", "map": {"version": 3, "names": ["__assign", "invariant", "newInvariantError", "equal", "<PERSON><PERSON>", "Kind", "getFragmentFromSelection", "getDefaultValues", "getOperationDefinition", "getTypenameFromResult", "makeReference", "isField", "resultKeyNameFromField", "isReference", "shouldInclude", "cloneDeep", "addTypenameToDocument", "isNonEmptyArray", "argumentsObjectFromField", "canonicalStringify", "isArray", "makeProcessedFieldsMerger", "fieldNameFromStoreName", "storeValueIsStoreObject", "extractFragmentContext", "normalizeReadFieldOptions", "getContextFlavor", "context", "clientOnly", "deferred", "key", "concat", "flavored", "flavors", "get", "set", "StoreWriter", "cache", "reader", "fragments", "prototype", "writeToStore", "store", "_a", "_this", "query", "result", "dataId", "variables", "overwrite", "operationDefinition", "merger", "written", "Object", "create", "merge", "existing", "incoming", "varString", "incomingById", "Map", "ref", "processSelectionSet", "selectionSet", "mergeTree", "map", "for<PERSON>ach", "storeObject", "fieldNodeSet", "entityRef", "size", "applied", "applyMerges", "globalThis", "__DEV__", "fieldsWithSelectionSets_1", "field", "name", "value", "hasSelectionSet_1", "storeFieldName", "hasMergeFunction_1", "childTree", "Boolean", "info", "keys", "warnAboutDataLoss", "retain", "__ref", "policies", "typename", "rootTypenamesById", "fragmentMap", "__typename", "readField", "options", "arguments", "from", "result_1", "Set", "flattenFields", "resultField<PERSON>ey", "add", "getStoreFieldName", "fieldName", "getChildMergeTree", "incomingValue", "processFieldValue", "childTypename", "getMergeFunction", "maybeRecycleChildMergeTree", "added", "getReadFunction", "error", "_b", "identify", "id", "keyObject", "e", "dataRef", "sets", "indexOf", "push", "isFresh", "previous_1", "mergeMergeTrees", "mergeTreeIsEmpty", "item", "i", "fieldMap", "limitingTrie", "flatten", "inheritedContext", "visitedNode", "lookup", "visited", "selections", "selection", "directives", "dir", "args", "if", "fragment", "lookupFragment", "kind", "FRAGMENT_SPREAD", "fragmentMatches", "getStorageArgs", "e_1", "i_1", "changedFields_1", "getValue_1", "getFieldValue", "String", "eVal", "iVal", "aVal", "pop", "slice", "runMergeFunction", "getStorage", "apply", "emptyMergeTreePool", "has", "left", "right", "needToMergeMaps", "merged", "remainingRightKeys_1", "leftTree", "delete", "tree", "warnings", "existingRef", "incomingObj", "<PERSON><PERSON><PERSON><PERSON>", "objOrRef", "child", "every", "parentType", "typeDotName", "childTypenames", "includes", "warn", "length", "join"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/inmemory/writeToStore.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { Kind } from \"graphql\";\nimport { getFragmentFromSelection, getDefaultValues, getOperationDefinition, getTypenameFromResult, makeReference, isField, resultKeyNameFromField, isReference, shouldInclude, cloneDeep, addTypenameToDocument, isNonEmptyArray, argumentsObjectFromField, canonicalStringify, } from \"../../utilities/index.js\";\nimport { isArray, makeProcessedFieldsMerger, fieldNameFromStoreName, storeValueIsStoreObject, extractFragmentContext, } from \"./helpers.js\";\nimport { normalizeReadFieldOptions } from \"./policies.js\";\n// Since there are only four possible combinations of context.clientOnly and\n// context.deferred values, we should need at most four \"flavors\" of any given\n// WriteContext. To avoid creating multiple copies of the same context, we cache\n// the contexts in the context.flavors Map (shared by all flavors) according to\n// their clientOnly and deferred values (always in that order).\nfunction getContextFlavor(context, clientOnly, deferred) {\n    var key = \"\".concat(clientOnly).concat(deferred);\n    var flavored = context.flavors.get(key);\n    if (!flavored) {\n        context.flavors.set(key, (flavored =\n            context.clientOnly === clientOnly && context.deferred === deferred ?\n                context\n                : __assign(__assign({}, context), { clientOnly: clientOnly, deferred: deferred })));\n    }\n    return flavored;\n}\nvar StoreWriter = /** @class */ (function () {\n    function StoreWriter(cache, reader, fragments) {\n        this.cache = cache;\n        this.reader = reader;\n        this.fragments = fragments;\n    }\n    StoreWriter.prototype.writeToStore = function (store, _a) {\n        var _this = this;\n        var query = _a.query, result = _a.result, dataId = _a.dataId, variables = _a.variables, overwrite = _a.overwrite;\n        var operationDefinition = getOperationDefinition(query);\n        var merger = makeProcessedFieldsMerger();\n        variables = __assign(__assign({}, getDefaultValues(operationDefinition)), variables);\n        var context = __assign(__assign({ store: store, written: Object.create(null), merge: function (existing, incoming) {\n                return merger.merge(existing, incoming);\n            }, variables: variables, varString: canonicalStringify(variables) }, extractFragmentContext(query, this.fragments)), { overwrite: !!overwrite, incomingById: new Map(), clientOnly: false, deferred: false, flavors: new Map() });\n        var ref = this.processSelectionSet({\n            result: result || Object.create(null),\n            dataId: dataId,\n            selectionSet: operationDefinition.selectionSet,\n            mergeTree: { map: new Map() },\n            context: context,\n        });\n        if (!isReference(ref)) {\n            throw newInvariantError(12, result);\n        }\n        // So far, the store has not been modified, so now it's time to process\n        // context.incomingById and merge those incoming fields into context.store.\n        context.incomingById.forEach(function (_a, dataId) {\n            var storeObject = _a.storeObject, mergeTree = _a.mergeTree, fieldNodeSet = _a.fieldNodeSet;\n            var entityRef = makeReference(dataId);\n            if (mergeTree && mergeTree.map.size) {\n                var applied = _this.applyMerges(mergeTree, entityRef, storeObject, context);\n                if (isReference(applied)) {\n                    // Assume References returned by applyMerges have already been merged\n                    // into the store. See makeMergeObjectsFunction in policies.ts for an\n                    // example of how this can happen.\n                    return;\n                }\n                // Otherwise, applyMerges returned a StoreObject, whose fields we should\n                // merge into the store (see store.merge statement below).\n                storeObject = applied;\n            }\n            if (globalThis.__DEV__ !== false && !context.overwrite) {\n                var fieldsWithSelectionSets_1 = Object.create(null);\n                fieldNodeSet.forEach(function (field) {\n                    if (field.selectionSet) {\n                        fieldsWithSelectionSets_1[field.name.value] = true;\n                    }\n                });\n                var hasSelectionSet_1 = function (storeFieldName) {\n                    return fieldsWithSelectionSets_1[fieldNameFromStoreName(storeFieldName)] ===\n                        true;\n                };\n                var hasMergeFunction_1 = function (storeFieldName) {\n                    var childTree = mergeTree && mergeTree.map.get(storeFieldName);\n                    return Boolean(childTree && childTree.info && childTree.info.merge);\n                };\n                Object.keys(storeObject).forEach(function (storeFieldName) {\n                    // If a merge function was defined for this field, trust that it\n                    // did the right thing about (not) clobbering data. If the field\n                    // has no selection set, it's a scalar field, so it doesn't need\n                    // a merge function (even if it's an object, like JSON data).\n                    if (hasSelectionSet_1(storeFieldName) &&\n                        !hasMergeFunction_1(storeFieldName)) {\n                        warnAboutDataLoss(entityRef, storeObject, storeFieldName, context.store);\n                    }\n                });\n            }\n            store.merge(dataId, storeObject);\n        });\n        // Any IDs written explicitly to the cache will be retained as\n        // reachable root IDs for garbage collection purposes. Although this\n        // logic includes root IDs like ROOT_QUERY and ROOT_MUTATION, their\n        // retainment counts are effectively ignored because cache.gc() always\n        // includes them in its root ID set.\n        store.retain(ref.__ref);\n        return ref;\n    };\n    StoreWriter.prototype.processSelectionSet = function (_a) {\n        var _this = this;\n        var dataId = _a.dataId, result = _a.result, selectionSet = _a.selectionSet, context = _a.context, \n        // This object allows processSelectionSet to report useful information\n        // to its callers without explicitly returning that information.\n        mergeTree = _a.mergeTree;\n        var policies = this.cache.policies;\n        // This variable will be repeatedly updated using context.merge to\n        // accumulate all fields that need to be written into the store.\n        var incoming = Object.create(null);\n        // If typename was not passed in, infer it. Note that typename is\n        // always passed in for tricky-to-infer cases such as \"Query\" for\n        // ROOT_QUERY.\n        var typename = (dataId && policies.rootTypenamesById[dataId]) ||\n            getTypenameFromResult(result, selectionSet, context.fragmentMap) ||\n            (dataId && context.store.get(dataId, \"__typename\"));\n        if (\"string\" === typeof typename) {\n            incoming.__typename = typename;\n        }\n        // This readField function will be passed as context.readField in the\n        // KeyFieldsContext object created within policies.identify (called below).\n        // In addition to reading from the existing context.store (thanks to the\n        // policies.readField(options, context) line at the very bottom), this\n        // version of readField can read from Reference objects that are currently\n        // pending in context.incomingById, which is important whenever keyFields\n        // need to be extracted from a child object that processSelectionSet has\n        // turned into a Reference.\n        var readField = function () {\n            var options = normalizeReadFieldOptions(arguments, incoming, context.variables);\n            if (isReference(options.from)) {\n                var info = context.incomingById.get(options.from.__ref);\n                if (info) {\n                    var result_1 = policies.readField(__assign(__assign({}, options), { from: info.storeObject }), context);\n                    if (result_1 !== void 0) {\n                        return result_1;\n                    }\n                }\n            }\n            return policies.readField(options, context);\n        };\n        var fieldNodeSet = new Set();\n        this.flattenFields(selectionSet, result, \n        // This WriteContext will be the default context value for fields returned\n        // by the flattenFields method, but some fields may be assigned a modified\n        // context, depending on the presence of @client and other directives.\n        context, typename).forEach(function (context, field) {\n            var _a;\n            var resultFieldKey = resultKeyNameFromField(field);\n            var value = result[resultFieldKey];\n            fieldNodeSet.add(field);\n            if (value !== void 0) {\n                var storeFieldName = policies.getStoreFieldName({\n                    typename: typename,\n                    fieldName: field.name.value,\n                    field: field,\n                    variables: context.variables,\n                });\n                var childTree = getChildMergeTree(mergeTree, storeFieldName);\n                var incomingValue = _this.processFieldValue(value, field, \n                // Reset context.clientOnly and context.deferred to their default\n                // values before processing nested selection sets.\n                field.selectionSet ?\n                    getContextFlavor(context, false, false)\n                    : context, childTree);\n                // To determine if this field holds a child object with a merge function\n                // defined in its type policy (see PR #7070), we need to figure out the\n                // child object's __typename.\n                var childTypename = void 0;\n                // The field's value can be an object that has a __typename only if the\n                // field has a selection set. Otherwise incomingValue is scalar.\n                if (field.selectionSet &&\n                    (isReference(incomingValue) || storeValueIsStoreObject(incomingValue))) {\n                    childTypename = readField(\"__typename\", incomingValue);\n                }\n                var merge = policies.getMergeFunction(typename, field.name.value, childTypename);\n                if (merge) {\n                    childTree.info = {\n                        // TODO Check compatibility against any existing childTree.field?\n                        field: field,\n                        typename: typename,\n                        merge: merge,\n                    };\n                }\n                else {\n                    maybeRecycleChildMergeTree(mergeTree, storeFieldName);\n                }\n                incoming = context.merge(incoming, (_a = {},\n                    _a[storeFieldName] = incomingValue,\n                    _a));\n            }\n            else if (globalThis.__DEV__ !== false &&\n                !context.clientOnly &&\n                !context.deferred &&\n                !addTypenameToDocument.added(field) &&\n                // If the field has a read function, it may be a synthetic field or\n                // provide a default value, so its absence from the written data should\n                // not be cause for alarm.\n                !policies.getReadFunction(typename, field.name.value)) {\n                globalThis.__DEV__ !== false && invariant.error(13, resultKeyNameFromField(field), result);\n            }\n        });\n        // Identify the result object, even if dataId was already provided,\n        // since we always need keyObject below.\n        try {\n            var _b = policies.identify(result, {\n                typename: typename,\n                selectionSet: selectionSet,\n                fragmentMap: context.fragmentMap,\n                storeObject: incoming,\n                readField: readField,\n            }), id = _b[0], keyObject = _b[1];\n            // If dataId was not provided, fall back to the id just generated by\n            // policies.identify.\n            dataId = dataId || id;\n            // Write any key fields that were used during identification, even if\n            // they were not mentioned in the original query.\n            if (keyObject) {\n                // TODO Reverse the order of the arguments?\n                incoming = context.merge(incoming, keyObject);\n            }\n        }\n        catch (e) {\n            // If dataId was provided, tolerate failure of policies.identify.\n            if (!dataId)\n                throw e;\n        }\n        if (\"string\" === typeof dataId) {\n            var dataRef = makeReference(dataId);\n            // Avoid processing the same entity object using the same selection\n            // set more than once. We use an array instead of a Set since most\n            // entity IDs will be written using only one selection set, so the\n            // size of this array is likely to be very small, meaning indexOf is\n            // likely to be faster than Set.prototype.has.\n            var sets = context.written[dataId] || (context.written[dataId] = []);\n            if (sets.indexOf(selectionSet) >= 0)\n                return dataRef;\n            sets.push(selectionSet);\n            // If we're about to write a result object into the store, but we\n            // happen to know that the exact same (===) result object would be\n            // returned if we were to reread the result with the same inputs,\n            // then we can skip the rest of the processSelectionSet work for\n            // this object, and immediately return a Reference to it.\n            if (this.reader &&\n                this.reader.isFresh(result, dataRef, selectionSet, context)) {\n                return dataRef;\n            }\n            var previous_1 = context.incomingById.get(dataId);\n            if (previous_1) {\n                previous_1.storeObject = context.merge(previous_1.storeObject, incoming);\n                previous_1.mergeTree = mergeMergeTrees(previous_1.mergeTree, mergeTree);\n                fieldNodeSet.forEach(function (field) { return previous_1.fieldNodeSet.add(field); });\n            }\n            else {\n                context.incomingById.set(dataId, {\n                    storeObject: incoming,\n                    // Save a reference to mergeTree only if it is not empty, because\n                    // empty MergeTrees may be recycled by maybeRecycleChildMergeTree and\n                    // reused for entirely different parts of the result tree.\n                    mergeTree: mergeTreeIsEmpty(mergeTree) ? void 0 : mergeTree,\n                    fieldNodeSet: fieldNodeSet,\n                });\n            }\n            return dataRef;\n        }\n        return incoming;\n    };\n    StoreWriter.prototype.processFieldValue = function (value, field, context, mergeTree) {\n        var _this = this;\n        if (!field.selectionSet || value === null) {\n            // In development, we need to clone scalar values so that they can be\n            // safely frozen with maybeDeepFreeze in readFromStore.ts. In production,\n            // it's cheaper to store the scalar values directly in the cache.\n            return globalThis.__DEV__ !== false ? cloneDeep(value) : value;\n        }\n        if (isArray(value)) {\n            return value.map(function (item, i) {\n                var value = _this.processFieldValue(item, field, context, getChildMergeTree(mergeTree, i));\n                maybeRecycleChildMergeTree(mergeTree, i);\n                return value;\n            });\n        }\n        return this.processSelectionSet({\n            result: value,\n            selectionSet: field.selectionSet,\n            context: context,\n            mergeTree: mergeTree,\n        });\n    };\n    // Implements https://spec.graphql.org/draft/#sec-Field-Collection, but with\n    // some additions for tracking @client and @defer directives.\n    StoreWriter.prototype.flattenFields = function (selectionSet, result, context, typename) {\n        if (typename === void 0) { typename = getTypenameFromResult(result, selectionSet, context.fragmentMap); }\n        var fieldMap = new Map();\n        var policies = this.cache.policies;\n        var limitingTrie = new Trie(false); // No need for WeakMap, since limitingTrie does not escape.\n        (function flatten(selectionSet, inheritedContext) {\n            var visitedNode = limitingTrie.lookup(selectionSet, \n            // Because we take inheritedClientOnly and inheritedDeferred into\n            // consideration here (in addition to selectionSet), it's possible for\n            // the same selection set to be flattened more than once, if it appears\n            // in the query with different @client and/or @directive configurations.\n            inheritedContext.clientOnly, inheritedContext.deferred);\n            if (visitedNode.visited)\n                return;\n            visitedNode.visited = true;\n            selectionSet.selections.forEach(function (selection) {\n                if (!shouldInclude(selection, context.variables))\n                    return;\n                var clientOnly = inheritedContext.clientOnly, deferred = inheritedContext.deferred;\n                if (\n                // Since the presence of @client or @defer on this field can only\n                // cause clientOnly or deferred to become true, we can skip the\n                // forEach loop if both clientOnly and deferred are already true.\n                !(clientOnly && deferred) &&\n                    isNonEmptyArray(selection.directives)) {\n                    selection.directives.forEach(function (dir) {\n                        var name = dir.name.value;\n                        if (name === \"client\")\n                            clientOnly = true;\n                        if (name === \"defer\") {\n                            var args = argumentsObjectFromField(dir, context.variables);\n                            // The @defer directive takes an optional args.if boolean\n                            // argument, similar to @include(if: boolean). Note that\n                            // @defer(if: false) does not make context.deferred false, but\n                            // instead behaves as if there was no @defer directive.\n                            if (!args || args.if !== false) {\n                                deferred = true;\n                            }\n                            // TODO In the future, we may want to record args.label using\n                            // context.deferred, if a label is specified.\n                        }\n                    });\n                }\n                if (isField(selection)) {\n                    var existing = fieldMap.get(selection);\n                    if (existing) {\n                        // If this field has been visited along another recursive path\n                        // before, the final context should have clientOnly or deferred set\n                        // to true only if *all* paths have the directive (hence the &&).\n                        clientOnly = clientOnly && existing.clientOnly;\n                        deferred = deferred && existing.deferred;\n                    }\n                    fieldMap.set(selection, getContextFlavor(context, clientOnly, deferred));\n                }\n                else {\n                    var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n                    if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n                        throw newInvariantError(14, selection.name.value);\n                    }\n                    if (fragment &&\n                        policies.fragmentMatches(fragment, typename, result, context.variables)) {\n                        flatten(fragment.selectionSet, getContextFlavor(context, clientOnly, deferred));\n                    }\n                }\n            });\n        })(selectionSet, context);\n        return fieldMap;\n    };\n    StoreWriter.prototype.applyMerges = function (mergeTree, existing, incoming, context, getStorageArgs) {\n        var _a;\n        var _this = this;\n        if (mergeTree.map.size && !isReference(incoming)) {\n            var e_1 = \n            // Items in the same position in different arrays are not\n            // necessarily related to each other, so when incoming is an array\n            // we process its elements as if there was no existing data.\n            (!isArray(incoming) &&\n                // Likewise, existing must be either a Reference or a StoreObject\n                // in order for its fields to be safe to merge with the fields of\n                // the incoming object.\n                (isReference(existing) || storeValueIsStoreObject(existing))) ?\n                existing\n                : void 0;\n            // This narrowing is implied by mergeTree.map.size > 0 and\n            // !isReference(incoming), though TypeScript understandably cannot\n            // hope to infer this type.\n            var i_1 = incoming;\n            // The options.storage objects provided to read and merge functions\n            // are derived from the identity of the parent object plus a\n            // sequence of storeFieldName strings/numbers identifying the nested\n            // field name path of each field value to be merged.\n            if (e_1 && !getStorageArgs) {\n                getStorageArgs = [isReference(e_1) ? e_1.__ref : e_1];\n            }\n            // It's possible that applying merge functions to this subtree will\n            // not change the incoming data, so this variable tracks the fields\n            // that did change, so we can create a new incoming object when (and\n            // only when) at least one incoming field has changed. We use a Map\n            // to preserve the type of numeric keys.\n            var changedFields_1;\n            var getValue_1 = function (from, name) {\n                return (isArray(from) ?\n                    typeof name === \"number\" ?\n                        from[name]\n                        : void 0\n                    : context.store.getFieldValue(from, String(name)));\n            };\n            mergeTree.map.forEach(function (childTree, storeFieldName) {\n                var eVal = getValue_1(e_1, storeFieldName);\n                var iVal = getValue_1(i_1, storeFieldName);\n                // If we have no incoming data, leave any existing data untouched.\n                if (void 0 === iVal)\n                    return;\n                if (getStorageArgs) {\n                    getStorageArgs.push(storeFieldName);\n                }\n                var aVal = _this.applyMerges(childTree, eVal, iVal, context, getStorageArgs);\n                if (aVal !== iVal) {\n                    changedFields_1 = changedFields_1 || new Map();\n                    changedFields_1.set(storeFieldName, aVal);\n                }\n                if (getStorageArgs) {\n                    invariant(getStorageArgs.pop() === storeFieldName);\n                }\n            });\n            if (changedFields_1) {\n                // Shallow clone i so we can add changed fields to it.\n                incoming = (isArray(i_1) ? i_1.slice(0) : __assign({}, i_1));\n                changedFields_1.forEach(function (value, name) {\n                    incoming[name] = value;\n                });\n            }\n        }\n        if (mergeTree.info) {\n            return this.cache.policies.runMergeFunction(existing, incoming, mergeTree.info, context, getStorageArgs && (_a = context.store).getStorage.apply(_a, getStorageArgs));\n        }\n        return incoming;\n    };\n    return StoreWriter;\n}());\nexport { StoreWriter };\nvar emptyMergeTreePool = [];\nfunction getChildMergeTree(_a, name) {\n    var map = _a.map;\n    if (!map.has(name)) {\n        map.set(name, emptyMergeTreePool.pop() || { map: new Map() });\n    }\n    return map.get(name);\n}\nfunction mergeMergeTrees(left, right) {\n    if (left === right || !right || mergeTreeIsEmpty(right))\n        return left;\n    if (!left || mergeTreeIsEmpty(left))\n        return right;\n    var info = left.info && right.info ? __assign(__assign({}, left.info), right.info) : left.info || right.info;\n    var needToMergeMaps = left.map.size && right.map.size;\n    var map = needToMergeMaps ? new Map()\n        : left.map.size ? left.map\n            : right.map;\n    var merged = { info: info, map: map };\n    if (needToMergeMaps) {\n        var remainingRightKeys_1 = new Set(right.map.keys());\n        left.map.forEach(function (leftTree, key) {\n            merged.map.set(key, mergeMergeTrees(leftTree, right.map.get(key)));\n            remainingRightKeys_1.delete(key);\n        });\n        remainingRightKeys_1.forEach(function (key) {\n            merged.map.set(key, mergeMergeTrees(right.map.get(key), left.map.get(key)));\n        });\n    }\n    return merged;\n}\nfunction mergeTreeIsEmpty(tree) {\n    return !tree || !(tree.info || tree.map.size);\n}\nfunction maybeRecycleChildMergeTree(_a, name) {\n    var map = _a.map;\n    var childTree = map.get(name);\n    if (childTree && mergeTreeIsEmpty(childTree)) {\n        emptyMergeTreePool.push(childTree);\n        map.delete(name);\n    }\n}\nvar warnings = new Set();\n// Note that this function is unused in production, and thus should be\n// pruned by any well-configured minifier.\nfunction warnAboutDataLoss(existingRef, incomingObj, storeFieldName, store) {\n    var getChild = function (objOrRef) {\n        var child = store.getFieldValue(objOrRef, storeFieldName);\n        return typeof child === \"object\" && child;\n    };\n    var existing = getChild(existingRef);\n    if (!existing)\n        return;\n    var incoming = getChild(incomingObj);\n    if (!incoming)\n        return;\n    // It's always safe to replace a reference, since it refers to data\n    // safely stored elsewhere.\n    if (isReference(existing))\n        return;\n    // If the values are structurally equivalent, we do not need to worry\n    // about incoming replacing existing.\n    if (equal(existing, incoming))\n        return;\n    // If we're replacing every key of the existing object, then the\n    // existing data would be overwritten even if the objects were\n    // normalized, so warning would not be helpful here.\n    if (Object.keys(existing).every(function (key) { return store.getFieldValue(incoming, key) !== void 0; })) {\n        return;\n    }\n    var parentType = store.getFieldValue(existingRef, \"__typename\") ||\n        store.getFieldValue(incomingObj, \"__typename\");\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var typeDotName = \"\".concat(parentType, \".\").concat(fieldName);\n    // Avoid warning more than once for the same type and field name.\n    if (warnings.has(typeDotName))\n        return;\n    warnings.add(typeDotName);\n    var childTypenames = [];\n    // Arrays do not have __typename fields, and always need a custom merge\n    // function, even if their elements are normalized entities.\n    if (!isArray(existing) && !isArray(incoming)) {\n        [existing, incoming].forEach(function (child) {\n            var typename = store.getFieldValue(child, \"__typename\");\n            if (typeof typename === \"string\" && !childTypenames.includes(typename)) {\n                childTypenames.push(typename);\n            }\n        });\n    }\n    globalThis.__DEV__ !== false && invariant.warn(15, fieldName, parentType, childTypenames.length ?\n        \"either ensure all objects of type \" +\n            childTypenames.join(\" and \") +\n            \" have an ID or a custom merge function, or \"\n        : \"\", typeDotName, __assign({}, existing), __assign({}, incoming));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,kCAAkC;AAC/E,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,IAAI,QAAQ,SAAS;AAC9B,SAASC,wBAAwB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,WAAW,EAAEC,aAAa,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,kBAAkB,QAAS,0BAA0B;AAClT,SAASC,OAAO,EAAEC,yBAAyB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,sBAAsB,QAAS,cAAc;AAC3I,SAASC,yBAAyB,QAAQ,eAAe;AACzD;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EACrD,IAAIC,GAAG,GAAG,EAAE,CAACC,MAAM,CAACH,UAAU,CAAC,CAACG,MAAM,CAACF,QAAQ,CAAC;EAChD,IAAIG,QAAQ,GAAGL,OAAO,CAACM,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;EACvC,IAAI,CAACE,QAAQ,EAAE;IACXL,OAAO,CAACM,OAAO,CAACE,GAAG,CAACL,GAAG,EAAGE,QAAQ,GAC9BL,OAAO,CAACC,UAAU,KAAKA,UAAU,IAAID,OAAO,CAACE,QAAQ,KAAKA,QAAQ,GAC9DF,OAAO,GACL3B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2B,OAAO,CAAC,EAAE;MAAEC,UAAU,EAAEA,UAAU;MAAEC,QAAQ,EAAEA;IAAS,CAAC,CAAE,CAAC;EAC/F;EACA,OAAOG,QAAQ;AACnB;AACA,IAAII,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAE;IAC3C,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACAH,WAAW,CAACI,SAAS,CAACC,YAAY,GAAG,UAAUC,KAAK,EAAEC,EAAE,EAAE;IACtD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,KAAK,GAAGF,EAAE,CAACE,KAAK;MAAEC,MAAM,GAAGH,EAAE,CAACG,MAAM;MAAEC,MAAM,GAAGJ,EAAE,CAACI,MAAM;MAAEC,SAAS,GAAGL,EAAE,CAACK,SAAS;MAAEC,SAAS,GAAGN,EAAE,CAACM,SAAS;IAChH,IAAIC,mBAAmB,GAAG1C,sBAAsB,CAACqC,KAAK,CAAC;IACvD,IAAIM,MAAM,GAAG9B,yBAAyB,CAAC,CAAC;IACxC2B,SAAS,GAAGhD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEO,gBAAgB,CAAC2C,mBAAmB,CAAC,CAAC,EAAEF,SAAS,CAAC;IACpF,IAAIrB,OAAO,GAAG3B,QAAQ,CAACA,QAAQ,CAAC;MAAE0C,KAAK,EAAEA,KAAK;MAAEU,OAAO,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAE;QAC3G,OAAON,MAAM,CAACI,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC3C,CAAC;MAAET,SAAS,EAAEA,SAAS;MAAEU,SAAS,EAAEvC,kBAAkB,CAAC6B,SAAS;IAAE,CAAC,EAAExB,sBAAsB,CAACqB,KAAK,EAAE,IAAI,CAACN,SAAS,CAAC,CAAC,EAAE;MAAEU,SAAS,EAAE,CAAC,CAACA,SAAS;MAAEU,YAAY,EAAE,IAAIC,GAAG,CAAC,CAAC;MAAEhC,UAAU,EAAE,KAAK;MAAEC,QAAQ,EAAE,KAAK;MAAEI,OAAO,EAAE,IAAI2B,GAAG,CAAC;IAAE,CAAC,CAAC;IACrO,IAAIC,GAAG,GAAG,IAAI,CAACC,mBAAmB,CAAC;MAC/BhB,MAAM,EAAEA,MAAM,IAAIO,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MACrCP,MAAM,EAAEA,MAAM;MACdgB,YAAY,EAAEb,mBAAmB,CAACa,YAAY;MAC9CC,SAAS,EAAE;QAAEC,GAAG,EAAE,IAAIL,GAAG,CAAC;MAAE,CAAC;MAC7BjC,OAAO,EAAEA;IACb,CAAC,CAAC;IACF,IAAI,CAACd,WAAW,CAACgD,GAAG,CAAC,EAAE;MACnB,MAAM3D,iBAAiB,CAAC,EAAE,EAAE4C,MAAM,CAAC;IACvC;IACA;IACA;IACAnB,OAAO,CAACgC,YAAY,CAACO,OAAO,CAAC,UAAUvB,EAAE,EAAEI,MAAM,EAAE;MAC/C,IAAIoB,WAAW,GAAGxB,EAAE,CAACwB,WAAW;QAAEH,SAAS,GAAGrB,EAAE,CAACqB,SAAS;QAAEI,YAAY,GAAGzB,EAAE,CAACyB,YAAY;MAC1F,IAAIC,SAAS,GAAG3D,aAAa,CAACqC,MAAM,CAAC;MACrC,IAAIiB,SAAS,IAAIA,SAAS,CAACC,GAAG,CAACK,IAAI,EAAE;QACjC,IAAIC,OAAO,GAAG3B,KAAK,CAAC4B,WAAW,CAACR,SAAS,EAAEK,SAAS,EAAEF,WAAW,EAAExC,OAAO,CAAC;QAC3E,IAAId,WAAW,CAAC0D,OAAO,CAAC,EAAE;UACtB;UACA;UACA;UACA;QACJ;QACA;QACA;QACAJ,WAAW,GAAGI,OAAO;MACzB;MACA,IAAIE,UAAU,CAACC,OAAO,KAAK,KAAK,IAAI,CAAC/C,OAAO,CAACsB,SAAS,EAAE;QACpD,IAAI0B,yBAAyB,GAAGtB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACnDc,YAAY,CAACF,OAAO,CAAC,UAAUU,KAAK,EAAE;UAClC,IAAIA,KAAK,CAACb,YAAY,EAAE;YACpBY,yBAAyB,CAACC,KAAK,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,IAAI;UACtD;QACJ,CAAC,CAAC;QACF,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,cAAc,EAAE;UAC9C,OAAOL,yBAAyB,CAACrD,sBAAsB,CAAC0D,cAAc,CAAC,CAAC,KACpE,IAAI;QACZ,CAAC;QACD,IAAIC,kBAAkB,GAAG,SAAAA,CAAUD,cAAc,EAAE;UAC/C,IAAIE,SAAS,GAAGlB,SAAS,IAAIA,SAAS,CAACC,GAAG,CAAC/B,GAAG,CAAC8C,cAAc,CAAC;UAC9D,OAAOG,OAAO,CAACD,SAAS,IAAIA,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACE,IAAI,CAAC7B,KAAK,CAAC;QACvE,CAAC;QACDF,MAAM,CAACgC,IAAI,CAAClB,WAAW,CAAC,CAACD,OAAO,CAAC,UAAUc,cAAc,EAAE;UACvD;UACA;UACA;UACA;UACA,IAAID,iBAAiB,CAACC,cAAc,CAAC,IACjC,CAACC,kBAAkB,CAACD,cAAc,CAAC,EAAE;YACrCM,iBAAiB,CAACjB,SAAS,EAAEF,WAAW,EAAEa,cAAc,EAAErD,OAAO,CAACe,KAAK,CAAC;UAC5E;QACJ,CAAC,CAAC;MACN;MACAA,KAAK,CAACa,KAAK,CAACR,MAAM,EAAEoB,WAAW,CAAC;IACpC,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACAzB,KAAK,CAAC6C,MAAM,CAAC1B,GAAG,CAAC2B,KAAK,CAAC;IACvB,OAAO3B,GAAG;EACd,CAAC;EACDzB,WAAW,CAACI,SAAS,CAACsB,mBAAmB,GAAG,UAAUnB,EAAE,EAAE;IACtD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIG,MAAM,GAAGJ,EAAE,CAACI,MAAM;MAAED,MAAM,GAAGH,EAAE,CAACG,MAAM;MAAEiB,YAAY,GAAGpB,EAAE,CAACoB,YAAY;MAAEpC,OAAO,GAAGgB,EAAE,CAAChB,OAAO;MAChG;MACA;MACAqC,SAAS,GAAGrB,EAAE,CAACqB,SAAS;IACxB,IAAIyB,QAAQ,GAAG,IAAI,CAACpD,KAAK,CAACoD,QAAQ;IAClC;IACA;IACA,IAAIhC,QAAQ,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAClC;IACA;IACA;IACA,IAAIoC,QAAQ,GAAI3C,MAAM,IAAI0C,QAAQ,CAACE,iBAAiB,CAAC5C,MAAM,CAAC,IACxDtC,qBAAqB,CAACqC,MAAM,EAAEiB,YAAY,EAAEpC,OAAO,CAACiE,WAAW,CAAC,IAC/D7C,MAAM,IAAIpB,OAAO,CAACe,KAAK,CAACR,GAAG,CAACa,MAAM,EAAE,YAAY,CAAE;IACvD,IAAI,QAAQ,KAAK,OAAO2C,QAAQ,EAAE;MAC9BjC,QAAQ,CAACoC,UAAU,GAAGH,QAAQ;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAII,SAAS,GAAG,SAAAA,CAAA,EAAY;MACxB,IAAIC,OAAO,GAAGtE,yBAAyB,CAACuE,SAAS,EAAEvC,QAAQ,EAAE9B,OAAO,CAACqB,SAAS,CAAC;MAC/E,IAAInC,WAAW,CAACkF,OAAO,CAACE,IAAI,CAAC,EAAE;QAC3B,IAAIb,IAAI,GAAGzD,OAAO,CAACgC,YAAY,CAACzB,GAAG,CAAC6D,OAAO,CAACE,IAAI,CAACT,KAAK,CAAC;QACvD,IAAIJ,IAAI,EAAE;UACN,IAAIc,QAAQ,GAAGT,QAAQ,CAACK,SAAS,CAAC9F,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE+F,OAAO,CAAC,EAAE;YAAEE,IAAI,EAAEb,IAAI,CAACjB;UAAY,CAAC,CAAC,EAAExC,OAAO,CAAC;UACvG,IAAIuE,QAAQ,KAAK,KAAK,CAAC,EAAE;YACrB,OAAOA,QAAQ;UACnB;QACJ;MACJ;MACA,OAAOT,QAAQ,CAACK,SAAS,CAACC,OAAO,EAAEpE,OAAO,CAAC;IAC/C,CAAC;IACD,IAAIyC,YAAY,GAAG,IAAI+B,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,aAAa,CAACrC,YAAY,EAAEjB,MAAM;IACvC;IACA;IACA;IACAnB,OAAO,EAAE+D,QAAQ,CAAC,CAACxB,OAAO,CAAC,UAAUvC,OAAO,EAAEiD,KAAK,EAAE;MACjD,IAAIjC,EAAE;MACN,IAAI0D,cAAc,GAAGzF,sBAAsB,CAACgE,KAAK,CAAC;MAClD,IAAIE,KAAK,GAAGhC,MAAM,CAACuD,cAAc,CAAC;MAClCjC,YAAY,CAACkC,GAAG,CAAC1B,KAAK,CAAC;MACvB,IAAIE,KAAK,KAAK,KAAK,CAAC,EAAE;QAClB,IAAIE,cAAc,GAAGS,QAAQ,CAACc,iBAAiB,CAAC;UAC5Cb,QAAQ,EAAEA,QAAQ;UAClBc,SAAS,EAAE5B,KAAK,CAACC,IAAI,CAACC,KAAK;UAC3BF,KAAK,EAAEA,KAAK;UACZ5B,SAAS,EAAErB,OAAO,CAACqB;QACvB,CAAC,CAAC;QACF,IAAIkC,SAAS,GAAGuB,iBAAiB,CAACzC,SAAS,EAAEgB,cAAc,CAAC;QAC5D,IAAI0B,aAAa,GAAG9D,KAAK,CAAC+D,iBAAiB,CAAC7B,KAAK,EAAEF,KAAK;QACxD;QACA;QACAA,KAAK,CAACb,YAAY,GACdrC,gBAAgB,CAACC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,GACrCA,OAAO,EAAEuD,SAAS,CAAC;QACzB;QACA;QACA;QACA,IAAI0B,aAAa,GAAG,KAAK,CAAC;QAC1B;QACA;QACA,IAAIhC,KAAK,CAACb,YAAY,KACjBlD,WAAW,CAAC6F,aAAa,CAAC,IAAInF,uBAAuB,CAACmF,aAAa,CAAC,CAAC,EAAE;UACxEE,aAAa,GAAGd,SAAS,CAAC,YAAY,EAAEY,aAAa,CAAC;QAC1D;QACA,IAAInD,KAAK,GAAGkC,QAAQ,CAACoB,gBAAgB,CAACnB,QAAQ,EAAEd,KAAK,CAACC,IAAI,CAACC,KAAK,EAAE8B,aAAa,CAAC;QAChF,IAAIrD,KAAK,EAAE;UACP2B,SAAS,CAACE,IAAI,GAAG;YACb;YACAR,KAAK,EAAEA,KAAK;YACZc,QAAQ,EAAEA,QAAQ;YAClBnC,KAAK,EAAEA;UACX,CAAC;QACL,CAAC,MACI;UACDuD,0BAA0B,CAAC9C,SAAS,EAAEgB,cAAc,CAAC;QACzD;QACAvB,QAAQ,GAAG9B,OAAO,CAAC4B,KAAK,CAACE,QAAQ,GAAGd,EAAE,GAAG,CAAC,CAAC,EACvCA,EAAE,CAACqC,cAAc,CAAC,GAAG0B,aAAa,EAClC/D,EAAE,CAAC,CAAC;MACZ,CAAC,MACI,IAAI8B,UAAU,CAACC,OAAO,KAAK,KAAK,IACjC,CAAC/C,OAAO,CAACC,UAAU,IACnB,CAACD,OAAO,CAACE,QAAQ,IACjB,CAACb,qBAAqB,CAAC+F,KAAK,CAACnC,KAAK,CAAC;MACnC;MACA;MACA;MACA,CAACa,QAAQ,CAACuB,eAAe,CAACtB,QAAQ,EAAEd,KAAK,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE;QACvDL,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIzE,SAAS,CAACgH,KAAK,CAAC,EAAE,EAAErG,sBAAsB,CAACgE,KAAK,CAAC,EAAE9B,MAAM,CAAC;MAC9F;IACJ,CAAC,CAAC;IACF;IACA;IACA,IAAI;MACA,IAAIoE,EAAE,GAAGzB,QAAQ,CAAC0B,QAAQ,CAACrE,MAAM,EAAE;UAC/B4C,QAAQ,EAAEA,QAAQ;UAClB3B,YAAY,EAAEA,YAAY;UAC1B6B,WAAW,EAAEjE,OAAO,CAACiE,WAAW;UAChCzB,WAAW,EAAEV,QAAQ;UACrBqC,SAAS,EAAEA;QACf,CAAC,CAAC;QAAEsB,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;QAAEG,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC;MACjC;MACA;MACAnE,MAAM,GAAGA,MAAM,IAAIqE,EAAE;MACrB;MACA;MACA,IAAIC,SAAS,EAAE;QACX;QACA5D,QAAQ,GAAG9B,OAAO,CAAC4B,KAAK,CAACE,QAAQ,EAAE4D,SAAS,CAAC;MACjD;IACJ,CAAC,CACD,OAAOC,CAAC,EAAE;MACN;MACA,IAAI,CAACvE,MAAM,EACP,MAAMuE,CAAC;IACf;IACA,IAAI,QAAQ,KAAK,OAAOvE,MAAM,EAAE;MAC5B,IAAIwE,OAAO,GAAG7G,aAAa,CAACqC,MAAM,CAAC;MACnC;MACA;MACA;MACA;MACA;MACA,IAAIyE,IAAI,GAAG7F,OAAO,CAACyB,OAAO,CAACL,MAAM,CAAC,KAAKpB,OAAO,CAACyB,OAAO,CAACL,MAAM,CAAC,GAAG,EAAE,CAAC;MACpE,IAAIyE,IAAI,CAACC,OAAO,CAAC1D,YAAY,CAAC,IAAI,CAAC,EAC/B,OAAOwD,OAAO;MAClBC,IAAI,CAACE,IAAI,CAAC3D,YAAY,CAAC;MACvB;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACzB,MAAM,IACX,IAAI,CAACA,MAAM,CAACqF,OAAO,CAAC7E,MAAM,EAAEyE,OAAO,EAAExD,YAAY,EAAEpC,OAAO,CAAC,EAAE;QAC7D,OAAO4F,OAAO;MAClB;MACA,IAAIK,UAAU,GAAGjG,OAAO,CAACgC,YAAY,CAACzB,GAAG,CAACa,MAAM,CAAC;MACjD,IAAI6E,UAAU,EAAE;QACZA,UAAU,CAACzD,WAAW,GAAGxC,OAAO,CAAC4B,KAAK,CAACqE,UAAU,CAACzD,WAAW,EAAEV,QAAQ,CAAC;QACxEmE,UAAU,CAAC5D,SAAS,GAAG6D,eAAe,CAACD,UAAU,CAAC5D,SAAS,EAAEA,SAAS,CAAC;QACvEI,YAAY,CAACF,OAAO,CAAC,UAAUU,KAAK,EAAE;UAAE,OAAOgD,UAAU,CAACxD,YAAY,CAACkC,GAAG,CAAC1B,KAAK,CAAC;QAAE,CAAC,CAAC;MACzF,CAAC,MACI;QACDjD,OAAO,CAACgC,YAAY,CAACxB,GAAG,CAACY,MAAM,EAAE;UAC7BoB,WAAW,EAAEV,QAAQ;UACrB;UACA;UACA;UACAO,SAAS,EAAE8D,gBAAgB,CAAC9D,SAAS,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS;UAC3DI,YAAY,EAAEA;QAClB,CAAC,CAAC;MACN;MACA,OAAOmD,OAAO;IAClB;IACA,OAAO9D,QAAQ;EACnB,CAAC;EACDrB,WAAW,CAACI,SAAS,CAACmE,iBAAiB,GAAG,UAAU7B,KAAK,EAAEF,KAAK,EAAEjD,OAAO,EAAEqC,SAAS,EAAE;IAClF,IAAIpB,KAAK,GAAG,IAAI;IAChB,IAAI,CAACgC,KAAK,CAACb,YAAY,IAAIe,KAAK,KAAK,IAAI,EAAE;MACvC;MACA;MACA;MACA,OAAOL,UAAU,CAACC,OAAO,KAAK,KAAK,GAAG3D,SAAS,CAAC+D,KAAK,CAAC,GAAGA,KAAK;IAClE;IACA,IAAI1D,OAAO,CAAC0D,KAAK,CAAC,EAAE;MAChB,OAAOA,KAAK,CAACb,GAAG,CAAC,UAAU8D,IAAI,EAAEC,CAAC,EAAE;QAChC,IAAIlD,KAAK,GAAGlC,KAAK,CAAC+D,iBAAiB,CAACoB,IAAI,EAAEnD,KAAK,EAAEjD,OAAO,EAAE8E,iBAAiB,CAACzC,SAAS,EAAEgE,CAAC,CAAC,CAAC;QAC1FlB,0BAA0B,CAAC9C,SAAS,EAAEgE,CAAC,CAAC;QACxC,OAAOlD,KAAK;MAChB,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAAChB,mBAAmB,CAAC;MAC5BhB,MAAM,EAAEgC,KAAK;MACbf,YAAY,EAAEa,KAAK,CAACb,YAAY;MAChCpC,OAAO,EAAEA,OAAO;MAChBqC,SAAS,EAAEA;IACf,CAAC,CAAC;EACN,CAAC;EACD;EACA;EACA5B,WAAW,CAACI,SAAS,CAAC4D,aAAa,GAAG,UAAUrC,YAAY,EAAEjB,MAAM,EAAEnB,OAAO,EAAE+D,QAAQ,EAAE;IACrF,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAGjF,qBAAqB,CAACqC,MAAM,EAAEiB,YAAY,EAAEpC,OAAO,CAACiE,WAAW,CAAC;IAAE;IACxG,IAAIqC,QAAQ,GAAG,IAAIrE,GAAG,CAAC,CAAC;IACxB,IAAI6B,QAAQ,GAAG,IAAI,CAACpD,KAAK,CAACoD,QAAQ;IAClC,IAAIyC,YAAY,GAAG,IAAI9H,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC,SAAS+H,OAAOA,CAACpE,YAAY,EAAEqE,gBAAgB,EAAE;MAC9C,IAAIC,WAAW,GAAGH,YAAY,CAACI,MAAM,CAACvE,YAAY;MAClD;MACA;MACA;MACA;MACAqE,gBAAgB,CAACxG,UAAU,EAAEwG,gBAAgB,CAACvG,QAAQ,CAAC;MACvD,IAAIwG,WAAW,CAACE,OAAO,EACnB;MACJF,WAAW,CAACE,OAAO,GAAG,IAAI;MAC1BxE,YAAY,CAACyE,UAAU,CAACtE,OAAO,CAAC,UAAUuE,SAAS,EAAE;QACjD,IAAI,CAAC3H,aAAa,CAAC2H,SAAS,EAAE9G,OAAO,CAACqB,SAAS,CAAC,EAC5C;QACJ,IAAIpB,UAAU,GAAGwG,gBAAgB,CAACxG,UAAU;UAAEC,QAAQ,GAAGuG,gBAAgB,CAACvG,QAAQ;QAClF;QACA;QACA;QACA;QACA,EAAED,UAAU,IAAIC,QAAQ,CAAC,IACrBZ,eAAe,CAACwH,SAAS,CAACC,UAAU,CAAC,EAAE;UACvCD,SAAS,CAACC,UAAU,CAACxE,OAAO,CAAC,UAAUyE,GAAG,EAAE;YACxC,IAAI9D,IAAI,GAAG8D,GAAG,CAAC9D,IAAI,CAACC,KAAK;YACzB,IAAID,IAAI,KAAK,QAAQ,EACjBjD,UAAU,GAAG,IAAI;YACrB,IAAIiD,IAAI,KAAK,OAAO,EAAE;cAClB,IAAI+D,IAAI,GAAG1H,wBAAwB,CAACyH,GAAG,EAAEhH,OAAO,CAACqB,SAAS,CAAC;cAC3D;cACA;cACA;cACA;cACA,IAAI,CAAC4F,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAK,KAAK,EAAE;gBAC5BhH,QAAQ,GAAG,IAAI;cACnB;cACA;cACA;YACJ;UACJ,CAAC,CAAC;QACN;;QACA,IAAIlB,OAAO,CAAC8H,SAAS,CAAC,EAAE;UACpB,IAAIjF,QAAQ,GAAGyE,QAAQ,CAAC/F,GAAG,CAACuG,SAAS,CAAC;UACtC,IAAIjF,QAAQ,EAAE;YACV;YACA;YACA;YACA5B,UAAU,GAAGA,UAAU,IAAI4B,QAAQ,CAAC5B,UAAU;YAC9CC,QAAQ,GAAGA,QAAQ,IAAI2B,QAAQ,CAAC3B,QAAQ;UAC5C;UACAoG,QAAQ,CAAC9F,GAAG,CAACsG,SAAS,EAAE/G,gBAAgB,CAACC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAAC;QAC5E,CAAC,MACI;UACD,IAAIiH,QAAQ,GAAGxI,wBAAwB,CAACmI,SAAS,EAAE9G,OAAO,CAACoH,cAAc,CAAC;UAC1E,IAAI,CAACD,QAAQ,IAAIL,SAAS,CAACO,IAAI,KAAK3I,IAAI,CAAC4I,eAAe,EAAE;YACtD,MAAM/I,iBAAiB,CAAC,EAAE,EAAEuI,SAAS,CAAC5D,IAAI,CAACC,KAAK,CAAC;UACrD;UACA,IAAIgE,QAAQ,IACRrD,QAAQ,CAACyD,eAAe,CAACJ,QAAQ,EAAEpD,QAAQ,EAAE5C,MAAM,EAAEnB,OAAO,CAACqB,SAAS,CAAC,EAAE;YACzEmF,OAAO,CAACW,QAAQ,CAAC/E,YAAY,EAAErC,gBAAgB,CAACC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAAC;UACnF;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,EAAEkC,YAAY,EAAEpC,OAAO,CAAC;IACzB,OAAOsG,QAAQ;EACnB,CAAC;EACD7F,WAAW,CAACI,SAAS,CAACgC,WAAW,GAAG,UAAUR,SAAS,EAAER,QAAQ,EAAEC,QAAQ,EAAE9B,OAAO,EAAEwH,cAAc,EAAE;IAClG,IAAIxG,EAAE;IACN,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIoB,SAAS,CAACC,GAAG,CAACK,IAAI,IAAI,CAACzD,WAAW,CAAC4C,QAAQ,CAAC,EAAE;MAC9C,IAAI2F,GAAG;MACP;MACA;MACA;MACC,CAAChI,OAAO,CAACqC,QAAQ,CAAC;MACf;MACA;MACA;MACC5C,WAAW,CAAC2C,QAAQ,CAAC,IAAIjC,uBAAuB,CAACiC,QAAQ,CAAC,CAAC,GAC5DA,QAAQ,GACN,KAAK,CAAC;MACZ;MACA;MACA;MACA,IAAI6F,GAAG,GAAG5F,QAAQ;MAClB;MACA;MACA;MACA;MACA,IAAI2F,GAAG,IAAI,CAACD,cAAc,EAAE;QACxBA,cAAc,GAAG,CAACtI,WAAW,CAACuI,GAAG,CAAC,GAAGA,GAAG,CAAC5D,KAAK,GAAG4D,GAAG,CAAC;MACzD;MACA;MACA;MACA;MACA;MACA;MACA,IAAIE,eAAe;MACnB,IAAIC,UAAU,GAAG,SAAAA,CAAUtD,IAAI,EAAEpB,IAAI,EAAE;QACnC,OAAQzD,OAAO,CAAC6E,IAAI,CAAC,GACjB,OAAOpB,IAAI,KAAK,QAAQ,GACpBoB,IAAI,CAACpB,IAAI,CAAC,GACR,KAAK,CAAC,GACVlD,OAAO,CAACe,KAAK,CAAC8G,aAAa,CAACvD,IAAI,EAAEwD,MAAM,CAAC5E,IAAI,CAAC,CAAC;MACzD,CAAC;MACDb,SAAS,CAACC,GAAG,CAACC,OAAO,CAAC,UAAUgB,SAAS,EAAEF,cAAc,EAAE;QACvD,IAAI0E,IAAI,GAAGH,UAAU,CAACH,GAAG,EAAEpE,cAAc,CAAC;QAC1C,IAAI2E,IAAI,GAAGJ,UAAU,CAACF,GAAG,EAAErE,cAAc,CAAC;QAC1C;QACA,IAAI,KAAK,CAAC,KAAK2E,IAAI,EACf;QACJ,IAAIR,cAAc,EAAE;UAChBA,cAAc,CAACzB,IAAI,CAAC1C,cAAc,CAAC;QACvC;QACA,IAAI4E,IAAI,GAAGhH,KAAK,CAAC4B,WAAW,CAACU,SAAS,EAAEwE,IAAI,EAAEC,IAAI,EAAEhI,OAAO,EAAEwH,cAAc,CAAC;QAC5E,IAAIS,IAAI,KAAKD,IAAI,EAAE;UACfL,eAAe,GAAGA,eAAe,IAAI,IAAI1F,GAAG,CAAC,CAAC;UAC9C0F,eAAe,CAACnH,GAAG,CAAC6C,cAAc,EAAE4E,IAAI,CAAC;QAC7C;QACA,IAAIT,cAAc,EAAE;UAChBlJ,SAAS,CAACkJ,cAAc,CAACU,GAAG,CAAC,CAAC,KAAK7E,cAAc,CAAC;QACtD;MACJ,CAAC,CAAC;MACF,IAAIsE,eAAe,EAAE;QACjB;QACA7F,QAAQ,GAAIrC,OAAO,CAACiI,GAAG,CAAC,GAAGA,GAAG,CAACS,KAAK,CAAC,CAAC,CAAC,GAAG9J,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,GAAG,CAAE;QAC5DC,eAAe,CAACpF,OAAO,CAAC,UAAUY,KAAK,EAAED,IAAI,EAAE;UAC3CpB,QAAQ,CAACoB,IAAI,CAAC,GAAGC,KAAK;QAC1B,CAAC,CAAC;MACN;IACJ;IACA,IAAId,SAAS,CAACoB,IAAI,EAAE;MAChB,OAAO,IAAI,CAAC/C,KAAK,CAACoD,QAAQ,CAACsE,gBAAgB,CAACvG,QAAQ,EAAEC,QAAQ,EAAEO,SAAS,CAACoB,IAAI,EAAEzD,OAAO,EAAEwH,cAAc,IAAI,CAACxG,EAAE,GAAGhB,OAAO,CAACe,KAAK,EAAEsH,UAAU,CAACC,KAAK,CAACtH,EAAE,EAAEwG,cAAc,CAAC,CAAC;IACzK;IACA,OAAO1F,QAAQ;EACnB,CAAC;EACD,OAAOrB,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,IAAI8H,kBAAkB,GAAG,EAAE;AAC3B,SAASzD,iBAAiBA,CAAC9D,EAAE,EAAEkC,IAAI,EAAE;EACjC,IAAIZ,GAAG,GAAGtB,EAAE,CAACsB,GAAG;EAChB,IAAI,CAACA,GAAG,CAACkG,GAAG,CAACtF,IAAI,CAAC,EAAE;IAChBZ,GAAG,CAAC9B,GAAG,CAAC0C,IAAI,EAAEqF,kBAAkB,CAACL,GAAG,CAAC,CAAC,IAAI;MAAE5F,GAAG,EAAE,IAAIL,GAAG,CAAC;IAAE,CAAC,CAAC;EACjE;EACA,OAAOK,GAAG,CAAC/B,GAAG,CAAC2C,IAAI,CAAC;AACxB;AACA,SAASgD,eAAeA,CAACuC,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAID,IAAI,KAAKC,KAAK,IAAI,CAACA,KAAK,IAAIvC,gBAAgB,CAACuC,KAAK,CAAC,EACnD,OAAOD,IAAI;EACf,IAAI,CAACA,IAAI,IAAItC,gBAAgB,CAACsC,IAAI,CAAC,EAC/B,OAAOC,KAAK;EAChB,IAAIjF,IAAI,GAAGgF,IAAI,CAAChF,IAAI,IAAIiF,KAAK,CAACjF,IAAI,GAAGpF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoK,IAAI,CAAChF,IAAI,CAAC,EAAEiF,KAAK,CAACjF,IAAI,CAAC,GAAGgF,IAAI,CAAChF,IAAI,IAAIiF,KAAK,CAACjF,IAAI;EAC5G,IAAIkF,eAAe,GAAGF,IAAI,CAACnG,GAAG,CAACK,IAAI,IAAI+F,KAAK,CAACpG,GAAG,CAACK,IAAI;EACrD,IAAIL,GAAG,GAAGqG,eAAe,GAAG,IAAI1G,GAAG,CAAC,CAAC,GAC/BwG,IAAI,CAACnG,GAAG,CAACK,IAAI,GAAG8F,IAAI,CAACnG,GAAG,GACpBoG,KAAK,CAACpG,GAAG;EACnB,IAAIsG,MAAM,GAAG;IAAEnF,IAAI,EAAEA,IAAI;IAAEnB,GAAG,EAAEA;EAAI,CAAC;EACrC,IAAIqG,eAAe,EAAE;IACjB,IAAIE,oBAAoB,GAAG,IAAIrE,GAAG,CAACkE,KAAK,CAACpG,GAAG,CAACoB,IAAI,CAAC,CAAC,CAAC;IACpD+E,IAAI,CAACnG,GAAG,CAACC,OAAO,CAAC,UAAUuG,QAAQ,EAAE3I,GAAG,EAAE;MACtCyI,MAAM,CAACtG,GAAG,CAAC9B,GAAG,CAACL,GAAG,EAAE+F,eAAe,CAAC4C,QAAQ,EAAEJ,KAAK,CAACpG,GAAG,CAAC/B,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC;MAClE0I,oBAAoB,CAACE,MAAM,CAAC5I,GAAG,CAAC;IACpC,CAAC,CAAC;IACF0I,oBAAoB,CAACtG,OAAO,CAAC,UAAUpC,GAAG,EAAE;MACxCyI,MAAM,CAACtG,GAAG,CAAC9B,GAAG,CAACL,GAAG,EAAE+F,eAAe,CAACwC,KAAK,CAACpG,GAAG,CAAC/B,GAAG,CAACJ,GAAG,CAAC,EAAEsI,IAAI,CAACnG,GAAG,CAAC/B,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC;EACN;EACA,OAAOyI,MAAM;AACjB;AACA,SAASzC,gBAAgBA,CAAC6C,IAAI,EAAE;EAC5B,OAAO,CAACA,IAAI,IAAI,EAAEA,IAAI,CAACvF,IAAI,IAAIuF,IAAI,CAAC1G,GAAG,CAACK,IAAI,CAAC;AACjD;AACA,SAASwC,0BAA0BA,CAACnE,EAAE,EAAEkC,IAAI,EAAE;EAC1C,IAAIZ,GAAG,GAAGtB,EAAE,CAACsB,GAAG;EAChB,IAAIiB,SAAS,GAAGjB,GAAG,CAAC/B,GAAG,CAAC2C,IAAI,CAAC;EAC7B,IAAIK,SAAS,IAAI4C,gBAAgB,CAAC5C,SAAS,CAAC,EAAE;IAC1CgF,kBAAkB,CAACxC,IAAI,CAACxC,SAAS,CAAC;IAClCjB,GAAG,CAACyG,MAAM,CAAC7F,IAAI,CAAC;EACpB;AACJ;AACA,IAAI+F,QAAQ,GAAG,IAAIzE,GAAG,CAAC,CAAC;AACxB;AACA;AACA,SAASb,iBAAiBA,CAACuF,WAAW,EAAEC,WAAW,EAAE9F,cAAc,EAAEtC,KAAK,EAAE;EACxE,IAAIqI,QAAQ,GAAG,SAAAA,CAAUC,QAAQ,EAAE;IAC/B,IAAIC,KAAK,GAAGvI,KAAK,CAAC8G,aAAa,CAACwB,QAAQ,EAAEhG,cAAc,CAAC;IACzD,OAAO,OAAOiG,KAAK,KAAK,QAAQ,IAAIA,KAAK;EAC7C,CAAC;EACD,IAAIzH,QAAQ,GAAGuH,QAAQ,CAACF,WAAW,CAAC;EACpC,IAAI,CAACrH,QAAQ,EACT;EACJ,IAAIC,QAAQ,GAAGsH,QAAQ,CAACD,WAAW,CAAC;EACpC,IAAI,CAACrH,QAAQ,EACT;EACJ;EACA;EACA,IAAI5C,WAAW,CAAC2C,QAAQ,CAAC,EACrB;EACJ;EACA;EACA,IAAIrD,KAAK,CAACqD,QAAQ,EAAEC,QAAQ,CAAC,EACzB;EACJ;EACA;EACA;EACA,IAAIJ,MAAM,CAACgC,IAAI,CAAC7B,QAAQ,CAAC,CAAC0H,KAAK,CAAC,UAAUpJ,GAAG,EAAE;IAAE,OAAOY,KAAK,CAAC8G,aAAa,CAAC/F,QAAQ,EAAE3B,GAAG,CAAC,KAAK,KAAK,CAAC;EAAE,CAAC,CAAC,EAAE;IACvG;EACJ;EACA,IAAIqJ,UAAU,GAAGzI,KAAK,CAAC8G,aAAa,CAACqB,WAAW,EAAE,YAAY,CAAC,IAC3DnI,KAAK,CAAC8G,aAAa,CAACsB,WAAW,EAAE,YAAY,CAAC;EAClD,IAAItE,SAAS,GAAGlF,sBAAsB,CAAC0D,cAAc,CAAC;EACtD,IAAIoG,WAAW,GAAG,EAAE,CAACrJ,MAAM,CAACoJ,UAAU,EAAE,GAAG,CAAC,CAACpJ,MAAM,CAACyE,SAAS,CAAC;EAC9D;EACA,IAAIoE,QAAQ,CAACT,GAAG,CAACiB,WAAW,CAAC,EACzB;EACJR,QAAQ,CAACtE,GAAG,CAAC8E,WAAW,CAAC;EACzB,IAAIC,cAAc,GAAG,EAAE;EACvB;EACA;EACA,IAAI,CAACjK,OAAO,CAACoC,QAAQ,CAAC,IAAI,CAACpC,OAAO,CAACqC,QAAQ,CAAC,EAAE;IAC1C,CAACD,QAAQ,EAAEC,QAAQ,CAAC,CAACS,OAAO,CAAC,UAAU+G,KAAK,EAAE;MAC1C,IAAIvF,QAAQ,GAAGhD,KAAK,CAAC8G,aAAa,CAACyB,KAAK,EAAE,YAAY,CAAC;MACvD,IAAI,OAAOvF,QAAQ,KAAK,QAAQ,IAAI,CAAC2F,cAAc,CAACC,QAAQ,CAAC5F,QAAQ,CAAC,EAAE;QACpE2F,cAAc,CAAC3D,IAAI,CAAChC,QAAQ,CAAC;MACjC;IACJ,CAAC,CAAC;EACN;EACAjB,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIzE,SAAS,CAACsL,IAAI,CAAC,EAAE,EAAE/E,SAAS,EAAE2E,UAAU,EAAEE,cAAc,CAACG,MAAM,GAC3F,oCAAoC,GAChCH,cAAc,CAACI,IAAI,CAAC,OAAO,CAAC,GAC5B,6CAA6C,GAC/C,EAAE,EAAEL,WAAW,EAAEpL,QAAQ,CAAC,CAAC,CAAC,EAAEwD,QAAQ,CAAC,EAAExD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,QAAQ,CAAC,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}