{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { VoiceRecorderComponent } from '../voice-recorder/voice-recorder.component';\nimport { VoiceMessagePlayerComponent } from '../voice-message-player/voice-message-player.component';\nimport * as i0 from \"@angular/core\";\nexport class VoiceMessageModule {\n  static {\n    this.ɵfac = function VoiceMessageModule_Factory(t) {\n      return new (t || VoiceMessageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VoiceMessageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VoiceMessageModule, {\n    declarations: [VoiceRecorderComponent, VoiceMessagePlayerComponent],\n    imports: [CommonModule],\n    exports: [VoiceRecorderComponent, VoiceMessagePlayerComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "VoiceRecorderComponent", "VoiceMessagePlayerComponent", "VoiceMessageModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\voice-message\\voice-message.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { VoiceRecorderComponent } from '../voice-recorder/voice-recorder.component';\r\nimport { VoiceMessagePlayerComponent } from '../voice-message-player/voice-message-player.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    VoiceRecorderComponent,\r\n    VoiceMessagePlayerComponent\r\n  ],\r\n  imports: [\r\n    CommonModule\r\n  ],\r\n  exports: [\r\n    VoiceRecorderComponent,\r\n    VoiceMessagePlayerComponent\r\n  ]\r\n})\r\nexport class VoiceMessageModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,2BAA2B,QAAQ,wDAAwD;;AAepG,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAP3BH,YAAY;IAAA;EAAA;;;2EAOHG,kBAAkB;IAAAC,YAAA,GAX3BH,sBAAsB,EACtBC,2BAA2B;IAAAG,OAAA,GAG3BL,YAAY;IAAAM,OAAA,GAGZL,sBAAsB,EACtBC,2BAA2B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}