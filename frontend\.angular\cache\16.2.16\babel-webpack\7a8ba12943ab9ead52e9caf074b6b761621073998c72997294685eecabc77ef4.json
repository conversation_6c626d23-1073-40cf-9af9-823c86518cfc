{"ast": null, "code": "import { __extends } from \"tslib\";\nvar MissingFieldError = /** @class */function (_super) {\n  __extends(MissingFieldError, _super);\n  function MissingFieldError(message, path, query, variables) {\n    var _a;\n    // 'Error' breaks prototype chain here\n    var _this = _super.call(this, message) || this;\n    _this.message = message;\n    _this.path = path;\n    _this.query = query;\n    _this.variables = variables;\n    if (Array.isArray(_this.path)) {\n      _this.missing = _this.message;\n      for (var i = _this.path.length - 1; i >= 0; --i) {\n        _this.missing = (_a = {}, _a[_this.path[i]] = _this.missing, _a);\n      }\n    } else {\n      _this.missing = _this.path;\n    }\n    // We're not using `Object.setPrototypeOf` here as it isn't fully supported\n    // on Android (see issue #3236).\n    _this.__proto__ = MissingFieldError.prototype;\n    return _this;\n  }\n  return MissingFieldError;\n}(<PERSON>rror);\nexport { MissingFieldError };", "map": {"version": 3, "names": ["__extends", "Missing<PERSON>ieldE<PERSON>r", "_super", "message", "path", "query", "variables", "_a", "_this", "call", "Array", "isArray", "missing", "i", "length", "__proto__", "prototype", "Error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/core/types/common.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nvar MissingFieldError = /** @class */ (function (_super) {\n    __extends(MissingFieldError, _super);\n    function MissingFieldError(message, path, query, variables) {\n        var _a;\n        // 'Error' breaks prototype chain here\n        var _this = _super.call(this, message) || this;\n        _this.message = message;\n        _this.path = path;\n        _this.query = query;\n        _this.variables = variables;\n        if (Array.isArray(_this.path)) {\n            _this.missing = _this.message;\n            for (var i = _this.path.length - 1; i >= 0; --i) {\n                _this.missing = (_a = {}, _a[_this.path[i]] = _this.missing, _a);\n            }\n        }\n        else {\n            _this.missing = _this.path;\n        }\n        // We're not using `Object.setPrototypeOf` here as it isn't fully supported\n        // on Android (see issue #3236).\n        _this.__proto__ = MissingFieldError.prototype;\n        return _this;\n    }\n    return MissingFieldError;\n}(<PERSON>rror));\nexport { MissingFieldError };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,IAAIC,iBAAiB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrDF,SAAS,CAACC,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;IACxD,IAAIC,EAAE;IACN;IACA,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEN,OAAO,CAAC,IAAI,IAAI;IAC9CK,KAAK,CAACL,OAAO,GAAGA,OAAO;IACvBK,KAAK,CAACJ,IAAI,GAAGA,IAAI;IACjBI,KAAK,CAACH,KAAK,GAAGA,KAAK;IACnBG,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3B,IAAII,KAAK,CAACC,OAAO,CAACH,KAAK,CAACJ,IAAI,CAAC,EAAE;MAC3BI,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACL,OAAO;MAC7B,KAAK,IAAIU,CAAC,GAAGL,KAAK,CAACJ,IAAI,CAACU,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC7CL,KAAK,CAACI,OAAO,IAAIL,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACC,KAAK,CAACJ,IAAI,CAACS,CAAC,CAAC,CAAC,GAAGL,KAAK,CAACI,OAAO,EAAEL,EAAE,CAAC;MACpE;IACJ,CAAC,MACI;MACDC,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACJ,IAAI;IAC9B;IACA;IACA;IACAI,KAAK,CAACO,SAAS,GAAGd,iBAAiB,CAACe,SAAS;IAC7C,OAAOR,KAAK;EAChB;EACA,OAAOP,iBAAiB;AAC5B,CAAC,CAACgB,KAAK,CAAE;AACT,SAAShB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}