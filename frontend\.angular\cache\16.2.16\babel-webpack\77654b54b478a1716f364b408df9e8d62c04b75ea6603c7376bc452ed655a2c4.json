{"ast": null, "code": "import { invariant, newInvariantError } from \"../globals/index.js\";\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc) {\n  invariant(doc && doc.kind === \"Document\", 88);\n  var operations = doc.definitions.filter(function (d) {\n    return d.kind !== \"FragmentDefinition\";\n  }).map(function (definition) {\n    if (definition.kind !== \"OperationDefinition\") {\n      throw newInvariantError(89, definition.kind);\n    }\n    return definition;\n  });\n  invariant(operations.length <= 1, 90, operations.length);\n  return doc;\n}\nexport function getOperationDefinition(doc) {\n  checkDocument(doc);\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"OperationDefinition\";\n  })[0];\n}\nexport function getOperationName(doc) {\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"OperationDefinition\" && !!definition.name;\n  }).map(function (x) {\n    return x.name.value;\n  })[0] || null;\n}\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(doc) {\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"FragmentDefinition\";\n  });\n}\nexport function getQueryDefinition(doc) {\n  var queryDef = getOperationDefinition(doc);\n  invariant(queryDef && queryDef.operation === \"query\", 91);\n  return queryDef;\n}\nexport function getFragmentDefinition(doc) {\n  invariant(doc.kind === \"Document\", 92);\n  invariant(doc.definitions.length <= 1, 93);\n  var fragmentDef = doc.definitions[0];\n  invariant(fragmentDef.kind === \"FragmentDefinition\", 94);\n  return fragmentDef;\n}\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(queryDoc) {\n  checkDocument(queryDoc);\n  var fragmentDefinition;\n  for (var _i = 0, _a = queryDoc.definitions; _i < _a.length; _i++) {\n    var definition = _a[_i];\n    if (definition.kind === \"OperationDefinition\") {\n      var operation = definition.operation;\n      if (operation === \"query\" || operation === \"mutation\" || operation === \"subscription\") {\n        return definition;\n      }\n    }\n    if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n      // we do this because we want to allow multiple fragment definitions\n      // to precede an operation definition.\n      fragmentDefinition = definition;\n    }\n  }\n  if (fragmentDefinition) {\n    return fragmentDefinition;\n  }\n  throw newInvariantError(95);\n}\nexport function getDefaultValues(definition) {\n  var defaultValues = Object.create(null);\n  var defs = definition && definition.variableDefinitions;\n  if (defs && defs.length) {\n    defs.forEach(function (def) {\n      if (def.defaultValue) {\n        valueToObjectRepresentation(defaultValues, def.variable.name, def.defaultValue);\n      }\n    });\n  }\n  return defaultValues;\n}", "map": {"version": 3, "names": ["invariant", "newInvariantError", "valueToObjectRepresentation", "checkDocument", "doc", "kind", "operations", "definitions", "filter", "d", "map", "definition", "length", "getOperationDefinition", "getOperationName", "name", "x", "value", "getFragmentDefinitions", "getQueryDefinition", "queryDef", "operation", "getFragmentDefinition", "fragmentDef", "getMainDefinition", "queryDoc", "fragmentDefinition", "_i", "_a", "getDefaultValues", "defaultValues", "Object", "create", "defs", "variableDefinitions", "for<PERSON>ach", "def", "defaultValue", "variable"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/getFromAST.js"], "sourcesContent": ["import { invariant, newInvariantError } from \"../globals/index.js\";\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc) {\n    invariant(doc && doc.kind === \"Document\", 88);\n    var operations = doc.definitions\n        .filter(function (d) { return d.kind !== \"FragmentDefinition\"; })\n        .map(function (definition) {\n        if (definition.kind !== \"OperationDefinition\") {\n            throw newInvariantError(89, definition.kind);\n        }\n        return definition;\n    });\n    invariant(operations.length <= 1, 90, operations.length);\n    return doc;\n}\nexport function getOperationDefinition(doc) {\n    checkDocument(doc);\n    return doc.definitions.filter(function (definition) {\n        return definition.kind === \"OperationDefinition\";\n    })[0];\n}\nexport function getOperationName(doc) {\n    return (doc.definitions\n        .filter(function (definition) {\n        return definition.kind === \"OperationDefinition\" && !!definition.name;\n    })\n        .map(function (x) { return x.name.value; })[0] || null);\n}\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(doc) {\n    return doc.definitions.filter(function (definition) {\n        return definition.kind === \"FragmentDefinition\";\n    });\n}\nexport function getQueryDefinition(doc) {\n    var queryDef = getOperationDefinition(doc);\n    invariant(queryDef && queryDef.operation === \"query\", 91);\n    return queryDef;\n}\nexport function getFragmentDefinition(doc) {\n    invariant(doc.kind === \"Document\", 92);\n    invariant(doc.definitions.length <= 1, 93);\n    var fragmentDef = doc.definitions[0];\n    invariant(fragmentDef.kind === \"FragmentDefinition\", 94);\n    return fragmentDef;\n}\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(queryDoc) {\n    checkDocument(queryDoc);\n    var fragmentDefinition;\n    for (var _i = 0, _a = queryDoc.definitions; _i < _a.length; _i++) {\n        var definition = _a[_i];\n        if (definition.kind === \"OperationDefinition\") {\n            var operation = definition.operation;\n            if (operation === \"query\" ||\n                operation === \"mutation\" ||\n                operation === \"subscription\") {\n                return definition;\n            }\n        }\n        if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n            // we do this because we want to allow multiple fragment definitions\n            // to precede an operation definition.\n            fragmentDefinition = definition;\n        }\n    }\n    if (fragmentDefinition) {\n        return fragmentDefinition;\n    }\n    throw newInvariantError(95);\n}\nexport function getDefaultValues(definition) {\n    var defaultValues = Object.create(null);\n    var defs = definition && definition.variableDefinitions;\n    if (defs && defs.length) {\n        defs.forEach(function (def) {\n            if (def.defaultValue) {\n                valueToObjectRepresentation(defaultValues, def.variable.name, def.defaultValue);\n            }\n        });\n    }\n    return defaultValues;\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,iBAAiB,QAAQ,qBAAqB;AAClE,SAASC,2BAA2B,QAAQ,iBAAiB;AAC7D;AACA,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC/BJ,SAAS,CAACI,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC;EAC7C,IAAIC,UAAU,GAAGF,GAAG,CAACG,WAAW,CAC3BC,MAAM,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACJ,IAAI,KAAK,oBAAoB;EAAE,CAAC,CAAC,CAChEK,GAAG,CAAC,UAAUC,UAAU,EAAE;IAC3B,IAAIA,UAAU,CAACN,IAAI,KAAK,qBAAqB,EAAE;MAC3C,MAAMJ,iBAAiB,CAAC,EAAE,EAAEU,UAAU,CAACN,IAAI,CAAC;IAChD;IACA,OAAOM,UAAU;EACrB,CAAC,CAAC;EACFX,SAAS,CAACM,UAAU,CAACM,MAAM,IAAI,CAAC,EAAE,EAAE,EAAEN,UAAU,CAACM,MAAM,CAAC;EACxD,OAAOR,GAAG;AACd;AACA,OAAO,SAASS,sBAAsBA,CAACT,GAAG,EAAE;EACxCD,aAAa,CAACC,GAAG,CAAC;EAClB,OAAOA,GAAG,CAACG,WAAW,CAACC,MAAM,CAAC,UAAUG,UAAU,EAAE;IAChD,OAAOA,UAAU,CAACN,IAAI,KAAK,qBAAqB;EACpD,CAAC,CAAC,CAAC,CAAC,CAAC;AACT;AACA,OAAO,SAASS,gBAAgBA,CAACV,GAAG,EAAE;EAClC,OAAQA,GAAG,CAACG,WAAW,CAClBC,MAAM,CAAC,UAAUG,UAAU,EAAE;IAC9B,OAAOA,UAAU,CAACN,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAACM,UAAU,CAACI,IAAI;EACzE,CAAC,CAAC,CACGL,GAAG,CAAC,UAAUM,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACD,IAAI,CAACE,KAAK;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAC9D;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACd,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACG,WAAW,CAACC,MAAM,CAAC,UAAUG,UAAU,EAAE;IAChD,OAAOA,UAAU,CAACN,IAAI,KAAK,oBAAoB;EACnD,CAAC,CAAC;AACN;AACA,OAAO,SAASc,kBAAkBA,CAACf,GAAG,EAAE;EACpC,IAAIgB,QAAQ,GAAGP,sBAAsB,CAACT,GAAG,CAAC;EAC1CJ,SAAS,CAACoB,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAK,OAAO,EAAE,EAAE,CAAC;EACzD,OAAOD,QAAQ;AACnB;AACA,OAAO,SAASE,qBAAqBA,CAAClB,GAAG,EAAE;EACvCJ,SAAS,CAACI,GAAG,CAACC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC;EACtCL,SAAS,CAACI,GAAG,CAACG,WAAW,CAACK,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;EAC1C,IAAIW,WAAW,GAAGnB,GAAG,CAACG,WAAW,CAAC,CAAC,CAAC;EACpCP,SAAS,CAACuB,WAAW,CAAClB,IAAI,KAAK,oBAAoB,EAAE,EAAE,CAAC;EACxD,OAAOkB,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACxCtB,aAAa,CAACsB,QAAQ,CAAC;EACvB,IAAIC,kBAAkB;EACtB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGH,QAAQ,CAAClB,WAAW,EAAEoB,EAAE,GAAGC,EAAE,CAAChB,MAAM,EAAEe,EAAE,EAAE,EAAE;IAC9D,IAAIhB,UAAU,GAAGiB,EAAE,CAACD,EAAE,CAAC;IACvB,IAAIhB,UAAU,CAACN,IAAI,KAAK,qBAAqB,EAAE;MAC3C,IAAIgB,SAAS,GAAGV,UAAU,CAACU,SAAS;MACpC,IAAIA,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,UAAU,IACxBA,SAAS,KAAK,cAAc,EAAE;QAC9B,OAAOV,UAAU;MACrB;IACJ;IACA,IAAIA,UAAU,CAACN,IAAI,KAAK,oBAAoB,IAAI,CAACqB,kBAAkB,EAAE;MACjE;MACA;MACAA,kBAAkB,GAAGf,UAAU;IACnC;EACJ;EACA,IAAIe,kBAAkB,EAAE;IACpB,OAAOA,kBAAkB;EAC7B;EACA,MAAMzB,iBAAiB,CAAC,EAAE,CAAC;AAC/B;AACA,OAAO,SAAS4B,gBAAgBA,CAAClB,UAAU,EAAE;EACzC,IAAImB,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIC,IAAI,GAAGtB,UAAU,IAAIA,UAAU,CAACuB,mBAAmB;EACvD,IAAID,IAAI,IAAIA,IAAI,CAACrB,MAAM,EAAE;IACrBqB,IAAI,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;MACxB,IAAIA,GAAG,CAACC,YAAY,EAAE;QAClBnC,2BAA2B,CAAC4B,aAAa,EAAEM,GAAG,CAACE,QAAQ,CAACvB,IAAI,EAAEqB,GAAG,CAACC,YAAY,CAAC;MACnF;IACJ,CAAC,CAAC;EACN;EACA,OAAOP,aAAa;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}