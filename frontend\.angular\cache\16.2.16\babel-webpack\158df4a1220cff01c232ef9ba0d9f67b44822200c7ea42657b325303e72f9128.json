{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../services/reunion.service\";\nimport * as i3 from \"@angular/common\";\nfunction ReunionDetailComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"routerLink\", \"/reunions/edit/\", ctx_r0.reunion._id, \"\");\n  }\n}\nfunction ReunionDetailComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\", 6);\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails de la r\\u00E9union...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionDetailComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 21);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadReunion());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h3\", 29);\n    i0.ɵɵtext(2, \" Lien de visioconf\\u00E9rence \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 34);\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \" Rejoindre la r\\u00E9union \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.reunion.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/plannings\", a1];\n};\nfunction ReunionDetailComponent_div_16_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h3\", 29);\n    i0.ɵɵtext(2, \" Planning associ\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c0, ctx_r7.reunion.planningId._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.reunion.planningId.titre, \" \");\n  }\n}\nfunction ReunionDetailComponent_div_16_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", participant_r11.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r11.username);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, ReunionDetailComponent_div_16_div_24_div_1_Template, 5, 2, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.reunion.participants);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Aucun participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionDetailComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"h2\", 27);\n    i0.ɵɵtext(4, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 28)(6, \"div\")(7, \"h3\", 29);\n    i0.ɵɵtext(8, \"Date et heure\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 6);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"h3\", 29);\n    i0.ɵɵtext(15, \"Lieu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 6);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ReunionDetailComponent_div_16_div_18_Template, 6, 1, \"div\", 30);\n    i0.ɵɵtemplate(19, ReunionDetailComponent_div_16_div_19_Template, 5, 4, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 31)(21, \"div\", 26)(22, \"h2\", 27);\n    i0.ɵɵtext(23, \" Participants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ReunionDetailComponent_div_16_div_24_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(25, ReunionDetailComponent_div_16_div_25_Template, 2, 0, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(11, 7, ctx_r3.reunion.dateDebut, \"dd/MM/yyyy HH:mm\"), \" - \", i0.ɵɵpipeBind2(12, 10, ctx_r3.reunion.dateFin, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.reunion.lieu || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.lienVisio);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.planningId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.participants && ctx_r3.reunion.participants.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.reunion.participants || ctx_r3.reunion.participants.length === 0);\n  }\n}\nexport class ReunionDetailComponent {\n  constructor(route, reunionService) {\n    this.route = route;\n    this.reunionService = reunionService;\n    this.reunion = null;\n    this.loading = true;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadReunion();\n  }\n  loadReunion() {\n    this.loading = true;\n    this.error = null;\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion manquant';\n      this.loading = false;\n      return;\n    }\n    // Simulation de chargement (à remplacer par un appel au service)\n    setTimeout(() => {\n      // Données fictives pour la démonstration\n      this.reunion = {\n        _id: id,\n        titre: 'Réunion de projet',\n        description: \"Discussion sur l'avancement du projet et les prochaines étapes\",\n        dateDebut: new Date('2023-06-15T10:00:00'),\n        dateFin: new Date('2023-06-15T11:30:00'),\n        lieu: 'Salle de conférence A',\n        lienVisio: 'https://meet.google.com/abc-defg-hij',\n        planningId: {\n          _id: 'planning123',\n          titre: 'Planning du projet X'\n        },\n        participants: [{\n          _id: 'user1',\n          username: 'Jean Dupont',\n          image: 'assets/images/default-avatar.png'\n        }, {\n          _id: 'user2',\n          username: 'Marie Martin',\n          image: 'assets/images/default-avatar.png'\n        }]\n      };\n      this.loading = false;\n    }, 1000);\n  }\n  static {\n    this.ɵfac = function ReunionDetailComponent_Factory(t) {\n      return new (t || ReunionDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ReunionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionDetailComponent,\n      selectors: [[\"app-reunion-detail\"]],\n      decls: 17,\n      vars: 6,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\", \"mb-6\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\"], [1, \"flex\", \"space-x-2\"], [\"class\", \"p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors\", 3, \"routerLink\", 4, \"ngIf\"], [\"routerLink\", \"/reunions\", 1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/10\", \"transition-colors\"], [1, \"fas\", \"fa-arrow-left\"], [\"class\", \"flex flex-col items-center justify-center p-12\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 border border-[#ff6b69] rounded-lg p-4 mb-6\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/10\", \"transition-colors\", 3, \"routerLink\"], [1, \"fas\", \"fa-edit\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"p-12\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"border-t-[#4f5fad]\", \"rounded-full\", \"animate-spin\", \"mb-4\"], [1, \"bg-[#ff6b69]/10\", \"border\", \"border-[#ff6b69]\", \"rounded-lg\", \"p-4\", \"mb-6\"], [1, \"flex\", \"items-start\"], [1, \"fas\", \"fa-exclamation-triangle\", \"text-[#ff6b69]\", \"text-xl\", \"mr-3\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\"], [1, \"mt-3\", \"px-3\", \"py-1\", \"bg-[#ff6b69]\", \"text-white\", \"rounded-md\", \"hover:bg-[#ff6b69]/80\", \"transition-colors\", \"text-sm\", 3, \"click\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"md:col-span-2\", \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\"], [1, \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"space-y-4\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\"], [4, \"ngIf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"text-[#6d6870] italic\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-[#4f5fad]\", \"hover:underline\", \"flex\", \"items-center\", 3, \"href\"], [1, \"fas\", \"fa-video\", \"mr-2\"], [1, \"text-[#4f5fad]\", \"hover:underline\", 3, \"routerLink\"], [1, \"space-y-3\"], [\"class\", \"flex items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\"], [1, \"relative\", \"mr-3\"], [\"alt\", \"Avatar\", 1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\"], [1, \"text-[#6d6870]\", \"italic\"]],\n      template: function ReunionDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h1\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7);\n          i0.ɵɵtemplate(11, ReunionDetailComponent_button_11_Template, 2, 1, \"button\", 8);\n          i0.ɵɵelementStart(12, \"button\", 9);\n          i0.ɵɵelement(13, \"i\", 10);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(14, ReunionDetailComponent_div_14_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(15, ReunionDetailComponent_div_15_Template, 10, 1, \"div\", 12);\n          i0.ɵɵtemplate(16, ReunionDetailComponent_div_16_Template, 26, 13, \"div\", 13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.reunion == null ? null : ctx.reunion.titre) || \"D\\u00E9tails de la r\\u00E9union\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.reunion == null ? null : ctx.reunion.description) || \"Chargement des d\\u00E9tails...\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.reunion);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.reunion && !ctx.loading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.RouterLink, i3.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWRldGFpbC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1kZXRhaWwvcmV1bmlvbi1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵpropertyInterpolate1", "ctx_r0", "reunion", "_id", "ɵɵtext", "ɵɵlistener", "ReunionDetailComponent_div_15_Template_button_click_8_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "loadReunion", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "error", "ɵɵproperty", "ctx_r6", "lienVisio", "ɵɵsanitizeUrl", "ɵɵpureFunction1", "_c0", "ctx_r7", "planningId", "ɵɵtextInterpolate1", "titre", "participant_r11", "image", "username", "ɵɵtemplate", "ReunionDetailComponent_div_16_div_24_div_1_Template", "ctx_r8", "participants", "ReunionDetailComponent_div_16_div_18_Template", "ReunionDetailComponent_div_16_div_19_Template", "ReunionDetailComponent_div_16_div_24_Template", "ReunionDetailComponent_div_16_div_25_Template", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "ctx_r3", "dateDebut", "dateFin", "lieu", "length", "ReunionDetailComponent", "constructor", "route", "reunionService", "loading", "ngOnInit", "id", "snapshot", "paramMap", "get", "setTimeout", "description", "Date", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ReunionService", "selectors", "decls", "vars", "consts", "template", "ReunionDetailComponent_Template", "rf", "ctx", "ReunionDetailComponent_button_11_Template", "ReunionDetailComponent_div_14_Template", "ReunionDetailComponent_div_15_Template", "ReunionDetailComponent_div_16_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ReunionService } from '../../../../services/reunion.service';\r\n\r\ninterface Participant {\r\n  _id: string;\r\n  username: string;\r\n  image?: string;\r\n}\r\n\r\ninterface Planning {\r\n  _id: string;\r\n  titre: string;\r\n}\r\n\r\ninterface Reunion {\r\n  _id: string;\r\n  titre: string;\r\n  description: string;\r\n  dateDebut: Date;\r\n  dateFin: Date;\r\n  lieu?: string;\r\n  lienVisio?: string;\r\n  planningId?: Planning;\r\n  participants?: Participant[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-reunion-detail',\r\n  templateUrl: './reunion-detail.component.html',\r\n  styleUrls: ['./reunion-detail.component.css'],\r\n})\r\nexport class ReunionDetailComponent implements OnInit {\r\n  reunion: Reunion | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private reunionService: ReunionService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadReunion();\r\n  }\r\n\r\n  loadReunion(): void {\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (!id) {\r\n      this.error = 'ID de réunion manquant';\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    // Simulation de chargement (à remplacer par un appel au service)\r\n    setTimeout(() => {\r\n      // Données fictives pour la démonstration\r\n      this.reunion = {\r\n        _id: id,\r\n        titre: 'Réunion de projet',\r\n        description:\r\n          \"Discussion sur l'avancement du projet et les prochaines étapes\",\r\n        dateDebut: new Date('2023-06-15T10:00:00'),\r\n        dateFin: new Date('2023-06-15T11:30:00'),\r\n        lieu: 'Salle de conférence A',\r\n        lienVisio: 'https://meet.google.com/abc-defg-hij',\r\n        planningId: {\r\n          _id: 'planning123',\r\n          titre: 'Planning du projet X',\r\n        },\r\n        participants: [\r\n          {\r\n            _id: 'user1',\r\n            username: 'Jean Dupont',\r\n            image: 'assets/images/default-avatar.png',\r\n          },\r\n          {\r\n            _id: 'user2',\r\n            username: 'Marie Martin',\r\n            image: 'assets/images/default-avatar.png',\r\n          },\r\n        ],\r\n      };\r\n\r\n      this.loading = false;\r\n    }, 1000);\r\n  }\r\n}\r\n", "<div class=\"min-h-screen bg-[#edf1f4] p-4 md:p-6\">\r\n  <div class=\"max-w-4xl mx-auto\">\r\n    <!-- En-tête -->\r\n    <div class=\"bg-white rounded-lg shadow-md overflow-hidden mb-6\">\r\n      <div class=\"border-t-4 border-[#4f5fad] p-6\">\r\n        <div class=\"flex justify-between items-start\">\r\n          <div>\r\n            <h1 class=\"text-2xl font-bold text-[#4f5fad] mb-2\">\r\n              {{ reunion?.titre || \"Détails de la réunion\" }}\r\n            </h1>\r\n            <p class=\"text-[#6d6870]\">\r\n              {{ reunion?.description || \"Chargement des détails...\" }}\r\n            </p>\r\n          </div>\r\n          <div class=\"flex space-x-2\">\r\n            <button\r\n              *ngIf=\"reunion\"\r\n              routerLink=\"/reunions/edit/{{ reunion._id }}\"\r\n              class=\"p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors\"\r\n            >\r\n              <i class=\"fas fa-edit\"></i>\r\n            </button>\r\n            <button\r\n              routerLink=\"/reunions\"\r\n              class=\"p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors\"\r\n            >\r\n              <i class=\"fas fa-arrow-left\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- État de chargement -->\r\n    <div *ngIf=\"loading\" class=\"flex flex-col items-center justify-center p-12\">\r\n      <div\r\n        class=\"w-12 h-12 border-4 border-[#4f5fad]/20 border-t-[#4f5fad] rounded-full animate-spin mb-4\"\r\n      ></div>\r\n      <p class=\"text-[#6d6870]\">Chargement des détails de la réunion...</p>\r\n    </div>\r\n\r\n    <!-- État d'erreur -->\r\n    <div\r\n      *ngIf=\"error\"\r\n      class=\"bg-[#ff6b69]/10 border border-[#ff6b69] rounded-lg p-4 mb-6\"\r\n    >\r\n      <div class=\"flex items-start\">\r\n        <i class=\"fas fa-exclamation-triangle text-[#ff6b69] text-xl mr-3\"></i>\r\n        <div>\r\n          <h3 class=\"font-medium text-[#ff6b69] mb-1\">Erreur de chargement</h3>\r\n          <p class=\"text-sm text-[#6d6870]\">{{ error }}</p>\r\n          <button\r\n            (click)=\"loadReunion()\"\r\n            class=\"mt-3 px-3 py-1 bg-[#ff6b69] text-white rounded-md hover:bg-[#ff6b69]/80 transition-colors text-sm\"\r\n          >\r\n            Réessayer\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Détails de la réunion -->\r\n    <div\r\n      *ngIf=\"reunion && !loading\"\r\n      class=\"grid grid-cols-1 md:grid-cols-3 gap-6\"\r\n    >\r\n      <!-- Informations principales -->\r\n      <div class=\"md:col-span-2 bg-white rounded-lg shadow-md overflow-hidden\">\r\n        <div class=\"p-6\">\r\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-4\">\r\n            Informations\r\n          </h2>\r\n\r\n          <div class=\"space-y-4\">\r\n            <div>\r\n              <h3 class=\"text-sm font-medium text-[#6d6870]\">Date et heure</h3>\r\n              <p class=\"text-[#6d6870]\">\r\n                {{ reunion.dateDebut | date : \"dd/MM/yyyy HH:mm\" }} -\r\n                {{ reunion.dateFin | date : \"HH:mm\" }}\r\n              </p>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 class=\"text-sm font-medium text-[#6d6870]\">Lieu</h3>\r\n              <p class=\"text-[#6d6870]\">{{ reunion.lieu || \"Non spécifié\" }}</p>\r\n            </div>\r\n\r\n            <div *ngIf=\"reunion.lienVisio\">\r\n              <h3 class=\"text-sm font-medium text-[#6d6870]\">\r\n                Lien de visioconférence\r\n              </h3>\r\n              <a\r\n                [href]=\"reunion.lienVisio\"\r\n                target=\"_blank\"\r\n                class=\"text-[#4f5fad] hover:underline flex items-center\"\r\n              >\r\n                <i class=\"fas fa-video mr-2\"></i>\r\n                Rejoindre la réunion\r\n              </a>\r\n            </div>\r\n\r\n            <div *ngIf=\"reunion.planningId\">\r\n              <h3 class=\"text-sm font-medium text-[#6d6870]\">\r\n                Planning associé\r\n              </h3>\r\n              <a\r\n                [routerLink]=\"['/plannings', reunion.planningId._id]\"\r\n                class=\"text-[#4f5fad] hover:underline\"\r\n              >\r\n                {{ reunion.planningId.titre }}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Participants -->\r\n      <div class=\"bg-white rounded-lg shadow-md overflow-hidden\">\r\n        <div class=\"p-6\">\r\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-4\">\r\n            Participants\r\n          </h2>\r\n\r\n          <div\r\n            *ngIf=\"reunion.participants && reunion.participants.length > 0\"\r\n            class=\"space-y-3\"\r\n          >\r\n            <div\r\n              *ngFor=\"let participant of reunion.participants\"\r\n              class=\"flex items-center\"\r\n            >\r\n              <div class=\"relative mr-3\">\r\n                <img\r\n                  [src]=\"\r\n                    participant.image || 'assets/images/default-avatar.png'\r\n                  \"\r\n                  alt=\"Avatar\"\r\n                  class=\"w-8 h-8 rounded-full object-cover\"\r\n                />\r\n              </div>\r\n              <span class=\"text-[#6d6870]\">{{ participant.username }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            *ngIf=\"!reunion.participants || reunion.participants.length === 0\"\r\n            class=\"text-[#6d6870] italic\"\r\n          >\r\n            Aucun participant\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;ICeYA,EAAA,CAAAC,cAAA,iBAIC;IACCD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAI,sBAAA,kCAAAC,MAAA,CAAAC,OAAA,CAAAC,GAAA,KAA6C;;;;;IAiBvDP,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,SAAA,cAEO;IACPF,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAQ,MAAA,wDAAuC;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIvEH,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,YAAuE;IACvEF,EAAA,CAAAC,cAAA,UAAK;IACyCD,EAAA,CAAAQ,MAAA,2BAAoB;IAAAR,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,YAAkC;IAAAD,EAAA,CAAAQ,MAAA,GAAW;IAAAR,EAAA,CAAAG,YAAA,EAAI;IACjDH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAS,UAAA,mBAAAC,+DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBhB,EAAA,CAAAQ,MAAA,uBACF;IAAAR,EAAA,CAAAG,YAAA,EAAS;;;;IANyBH,EAAA,CAAAiB,SAAA,GAAW;IAAXjB,EAAA,CAAAkB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAqC3CpB,EAAA,CAAAC,cAAA,UAA+B;IAE3BD,EAAA,CAAAQ,MAAA,qCACF;IAAAR,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAIC;IACCD,EAAA,CAAAE,SAAA,YAAiC;IACjCF,EAAA,CAAAQ,MAAA,kCACF;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;;IANFH,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAqB,UAAA,SAAAC,MAAA,CAAAhB,OAAA,CAAAiB,SAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAA0B;;;;;;;;IAS9BxB,EAAA,CAAAC,cAAA,UAAgC;IAE5BD,EAAA,CAAAQ,MAAA,8BACF;IAAAR,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAQ,MAAA,GACF;IAAAR,EAAA,CAAAG,YAAA,EAAI;;;;IAJFH,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAArB,OAAA,CAAAsB,UAAA,CAAArB,GAAA,EAAqD;IAGrDP,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA6B,kBAAA,MAAAF,MAAA,CAAArB,OAAA,CAAAsB,UAAA,CAAAE,KAAA,MACF;;;;;IAiBF9B,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAME;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAQ,MAAA,GAA0B;IAAAR,EAAA,CAAAG,YAAA,EAAO;;;;IAP1DH,EAAA,CAAAiB,SAAA,GAEC;IAFDjB,EAAA,CAAAqB,UAAA,QAAAU,eAAA,CAAAC,KAAA,wCAAAhC,EAAA,CAAAwB,aAAA,CAEC;IAKwBxB,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAkB,iBAAA,CAAAa,eAAA,CAAAE,QAAA,CAA0B;;;;;IAjB3DjC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAkC,UAAA,IAAAC,mDAAA,kBAcM;IACRnC,EAAA,CAAAG,YAAA,EAAM;;;;IAdsBH,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAqB,UAAA,YAAAe,MAAA,CAAA9B,OAAA,CAAA+B,YAAA,CAAuB;;;;;IAgBnDrC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,MAAA,0BACF;IAAAR,EAAA,CAAAG,YAAA,EAAM;;;;;IAvFZH,EAAA,CAAAC,cAAA,cAGC;IAKOD,EAAA,CAAAQ,MAAA,qBACF;IAAAR,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAAuB;IAE4BD,EAAA,CAAAQ,MAAA,oBAAa;IAAAR,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,WAA0B;IACxBD,EAAA,CAAAQ,MAAA,IAEF;;;IAAAR,EAAA,CAAAG,YAAA,EAAI;IAGNH,EAAA,CAAAC,cAAA,WAAK;IAC4CD,EAAA,CAAAQ,MAAA,YAAI;IAAAR,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAQ,MAAA,IAAoC;IAAAR,EAAA,CAAAG,YAAA,EAAI;IAGpEH,EAAA,CAAAkC,UAAA,KAAAI,6CAAA,kBAYM;IAENtC,EAAA,CAAAkC,UAAA,KAAAK,6CAAA,kBAUM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2D;IAGrDD,EAAA,CAAAQ,MAAA,sBACF;IAAAR,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAkC,UAAA,KAAAM,6CAAA,kBAmBM;IAENxC,EAAA,CAAAkC,UAAA,KAAAO,6CAAA,kBAKM;IACRzC,EAAA,CAAAG,YAAA,EAAM;;;;IAzEEH,EAAA,CAAAiB,SAAA,IAEF;IAFEjB,EAAA,CAAA0C,kBAAA,MAAA1C,EAAA,CAAA2C,WAAA,QAAAC,MAAA,CAAAtC,OAAA,CAAAuC,SAAA,8BAAA7C,EAAA,CAAA2C,WAAA,SAAAC,MAAA,CAAAtC,OAAA,CAAAwC,OAAA,gBAEF;IAK0B9C,EAAA,CAAAiB,SAAA,GAAoC;IAApCjB,EAAA,CAAAkB,iBAAA,CAAA0B,MAAA,CAAAtC,OAAA,CAAAyC,IAAA,6BAAoC;IAG1D/C,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAqB,UAAA,SAAAuB,MAAA,CAAAtC,OAAA,CAAAiB,SAAA,CAAuB;IAcvBvB,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAqB,UAAA,SAAAuB,MAAA,CAAAtC,OAAA,CAAAsB,UAAA,CAAwB;IAuB7B5B,EAAA,CAAAiB,SAAA,GAA6D;IAA7DjB,EAAA,CAAAqB,UAAA,SAAAuB,MAAA,CAAAtC,OAAA,CAAA+B,YAAA,IAAAO,MAAA,CAAAtC,OAAA,CAAA+B,YAAA,CAAAW,MAAA,KAA6D;IAqB7DhD,EAAA,CAAAiB,SAAA,GAAgE;IAAhEjB,EAAA,CAAAqB,UAAA,UAAAuB,MAAA,CAAAtC,OAAA,CAAA+B,YAAA,IAAAO,MAAA,CAAAtC,OAAA,CAAA+B,YAAA,CAAAW,MAAA,OAAgE;;;ADjH7E,OAAM,MAAOC,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,cAA8B;IAD9B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IANxB,KAAA9C,OAAO,GAAmB,IAAI;IAC9B,KAAA+C,OAAO,GAAG,IAAI;IACd,KAAAjC,KAAK,GAAkB,IAAI;EAKxB;EAEHkC,QAAQA,CAAA;IACN,IAAI,CAACtC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACqC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjC,KAAK,GAAG,IAAI;IAEjB,MAAMmC,EAAE,GAAG,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAACnC,KAAK,GAAG,wBAAwB;MACrC,IAAI,CAACiC,OAAO,GAAG,KAAK;MACpB;;IAGF;IACAM,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACrD,OAAO,GAAG;QACbC,GAAG,EAAEgD,EAAE;QACPzB,KAAK,EAAE,mBAAmB;QAC1B8B,WAAW,EACT,gEAAgE;QAClEf,SAAS,EAAE,IAAIgB,IAAI,CAAC,qBAAqB,CAAC;QAC1Cf,OAAO,EAAE,IAAIe,IAAI,CAAC,qBAAqB,CAAC;QACxCd,IAAI,EAAE,uBAAuB;QAC7BxB,SAAS,EAAE,sCAAsC;QACjDK,UAAU,EAAE;UACVrB,GAAG,EAAE,aAAa;UAClBuB,KAAK,EAAE;SACR;QACDO,YAAY,EAAE,CACZ;UACE9B,GAAG,EAAE,OAAO;UACZ0B,QAAQ,EAAE,aAAa;UACvBD,KAAK,EAAE;SACR,EACD;UACEzB,GAAG,EAAE,OAAO;UACZ0B,QAAQ,EAAE,cAAc;UACxBD,KAAK,EAAE;SACR;OAEJ;MAED,IAAI,CAACqB,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAzDWJ,sBAAsB,EAAAjD,EAAA,CAAA8D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhE,EAAA,CAAA8D,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAtBjB,sBAAsB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCnCzE,EAAA,CAAAC,cAAA,aAAkD;UAQpCD,EAAA,CAAAQ,MAAA,GACF;UAAAR,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA0B;UACxBD,EAAA,CAAAQ,MAAA,GACF;UAAAR,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAkC,UAAA,KAAAyC,yCAAA,oBAMS;UACT3E,EAAA,CAAAC,cAAA,iBAGC;UACCD,EAAA,CAAAE,SAAA,aAAiC;UACnCF,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAkC,UAAA,KAAA0C,sCAAA,kBAKM;UAGN5E,EAAA,CAAAkC,UAAA,KAAA2C,sCAAA,mBAiBM;UAGN7E,EAAA,CAAAkC,UAAA,KAAA4C,sCAAA,oBA0FM;UACR9E,EAAA,CAAAG,YAAA,EAAM;;;UAjJMH,EAAA,CAAAiB,SAAA,GACF;UADEjB,EAAA,CAAA6B,kBAAA,OAAA6C,GAAA,CAAApE,OAAA,kBAAAoE,GAAA,CAAApE,OAAA,CAAAwB,KAAA,4CACF;UAEE9B,EAAA,CAAAiB,SAAA,GACF;UADEjB,EAAA,CAAA6B,kBAAA,OAAA6C,GAAA,CAAApE,OAAA,kBAAAoE,GAAA,CAAApE,OAAA,CAAAsD,WAAA,2CACF;UAIG5D,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAqB,UAAA,SAAAqD,GAAA,CAAApE,OAAA,CAAa;UAkBlBN,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAqB,UAAA,SAAAqD,GAAA,CAAArB,OAAA,CAAa;UAShBrD,EAAA,CAAAiB,SAAA,GAAW;UAAXjB,EAAA,CAAAqB,UAAA,SAAAqD,GAAA,CAAAtD,KAAA,CAAW;UAoBXpB,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAqB,UAAA,SAAAqD,GAAA,CAAApE,OAAA,KAAAoE,GAAA,CAAArB,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}