{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthadminService } from 'src/app/services/authadmin.service';\nexport const guardadminGuard = (route, state) => {\n  const authService = inject(AuthadminService);\n  const router = inject(Router);\n  if (authService.loggedIn() == true) {\n    return true;\n  } else {\n    router.navigate(['/admin/login'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    localStorage.removeItem('token');\n    return false;\n  }\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthadminService", "guardad<PERSON><PERSON><PERSON>", "route", "state", "authService", "router", "loggedIn", "navigate", "queryParams", "returnUrl", "url", "localStorage", "removeItem"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\guards\\guardadmin.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { AuthadminService } from 'src/app/services/authadmin.service';\r\n\r\nexport const guardadminGuard: CanActivateFn = (route, state) => {\r\n  const authService=inject(AuthadminService)\r\n  const router= inject(Router)\r\n  if(authService.loggedIn()==true){\r\n  return true;\r\n}else \r\n{\r\n\r\n  router.navigate(['/admin/login'],{queryParams:{returnUrl:state.url}});\r\n  localStorage.removeItem('token')\r\n  return false;\r\n}\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,gBAAgB,QAAQ,oCAAoC;AAErE,OAAO,MAAMC,eAAe,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EAC7D,MAAMC,WAAW,GAACN,MAAM,CAACE,gBAAgB,CAAC;EAC1C,MAAMK,MAAM,GAAEP,MAAM,CAACC,MAAM,CAAC;EAC5B,IAAGK,WAAW,CAACE,QAAQ,EAAE,IAAE,IAAI,EAAC;IAChC,OAAO,IAAI;GACZ,MACD;IAEED,MAAM,CAACE,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAC;MAACC,WAAW,EAAC;QAACC,SAAS,EAACN,KAAK,CAACO;MAAG;IAAC,CAAC,CAAC;IACrEC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO,KAAK;;AAEd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}