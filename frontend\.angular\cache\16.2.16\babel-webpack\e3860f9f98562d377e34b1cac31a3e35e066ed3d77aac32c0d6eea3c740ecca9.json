{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, take, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    this._detach = () => {\n      this.disable();\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    this._document = document;\n  }\n  static {\n    this.ɵfac = function ScrollStrategyOptions_Factory(t) {\n      return new (t || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollStrategyOptions,\n      factory: ScrollStrategyOptions.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ScrollDispatcher\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.disposeOnNavigation = false;\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  constructor( /** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static {\n    this.ɵfac = function BaseOverlayDispatcher_Factory(t) {\n      return new (t || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BaseOverlayDispatcher,\n      factory: BaseOverlayDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n      this._isAttached = false;\n    }\n  }\n  static {\n    this.ɵfac = function OverlayKeyboardDispatcher_Factory(t) {\n      return new (t || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayKeyboardDispatcher,\n      factory: OverlayKeyboardDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    this._clickListener = event => {\n      const target = _getEventTarget(event);\n      // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n      // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n      this._pointerDownEventTarget = null;\n      // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n      const overlays = this._attachedOverlays.slice();\n      // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        }\n        // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n        if (overlayRef.overlayElement.contains(target) || overlayRef.overlayElement.contains(origin)) {\n          break;\n        }\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      }\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n  static {\n    this.ɵfac = function OverlayOutsideClickDispatcher_Factory(t) {\n      return new (t || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayOutsideClickDispatcher,\n      factory: OverlayOutsideClickDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  static {\n    this.ɵfac = function OverlayContainer_Factory(t) {\n      return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayContainer,\n      factory: OverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    this._outsidePointerEvents = new Subject();\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenStable();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._disposeBackdrop(this._backdropElement);\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._previousHostParent = this._pane = this._host = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropElement, this._host);\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click', this._backdropClickHandler);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n    if (!backdropToDetach) {\n      return;\n    }\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n      return;\n    }\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    });\n    // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n    backdropToDetach.style.pointerEvents = 'none';\n    // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenStable() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._ngZone.onStable.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove();\n      // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollableViewProperties = this._getScrollVisibility();\n      const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n      this._positionChanges.next(changeEvent);\n    }\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the ClientRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._alignItems = '';\n    this._xPosition = '';\n    this._xOffset = '';\n    this._width = '';\n    this._height = '';\n    this._isDisposed = false;\n  }\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n  static {\n    this.ɵfac = function OverlayPositionBuilder_Factory(t) {\n      return new (t || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayPositionBuilder,\n      factory: OverlayPositionBuilder.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: OverlayContainer\n    }];\n  }, null);\n})();\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  constructor( /** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsModuleType = _animationsModuleType;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n  static {\n    this.ɵfac = function Overlay_Factory(t) {\n      return new (t || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Overlay,\n      factory: Overlay.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: ScrollStrategyOptions\n    }, {\n      type: OverlayContainer\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: OverlayPositionBuilder\n    }, {\n      type: OverlayKeyboardDispatcher\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i5.Directionality\n    }, {\n      type: i6.Location\n    }, {\n      type: OverlayOutsideClickDispatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  constructor( /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function CdkOverlayOrigin_Factory(t) {\n      return new (t || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkOverlayOrigin,\n      selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n      exportAs: [\"cdkOverlayOrigin\"],\n      standalone: true\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether or not the overlay should attach a backdrop. */\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /** Whether or not the overlay should be locked when scrolling. */\n  get lockPosition() {\n    return this._lockPosition;\n  }\n  set lockPosition(value) {\n    this._lockPosition = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  get flexibleDimensions() {\n    return this._flexibleDimensions;\n  }\n  set flexibleDimensions(value) {\n    this._flexibleDimensions = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  get growAfterOpen() {\n    return this._growAfterOpen;\n  }\n  set growAfterOpen(value) {\n    this._growAfterOpen = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  get push() {\n    return this._push;\n  }\n  set push(value) {\n    this._push = coerceBooleanProperty(value);\n  }\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._hasBackdrop = false;\n    this._lockPosition = false;\n    this._growAfterOpen = false;\n    this._flexibleDimensions = false;\n    this._push = false;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    /** Margin between the overlay and the viewport edges. */\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    this.disableClose = false;\n    /** Event emitted when the backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this._detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      this.overlayOutsideClick.next(event);\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this.positionChange.emit(position);\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkConnectedOverlay_Factory(t) {\n      return new (t || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkConnectedOverlay,\n      selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n      inputs: {\n        origin: [\"cdkConnectedOverlayOrigin\", \"origin\"],\n        positions: [\"cdkConnectedOverlayPositions\", \"positions\"],\n        positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n        offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n        offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n        width: [\"cdkConnectedOverlayWidth\", \"width\"],\n        height: [\"cdkConnectedOverlayHeight\", \"height\"],\n        minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n        minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n        backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n        panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n        viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n        scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n        open: [\"cdkConnectedOverlayOpen\", \"open\"],\n        disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n        transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n        hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"],\n        lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"],\n        flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"],\n        growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"],\n        push: [\"cdkConnectedOverlayPush\", \"push\"]\n      },\n      outputs: {\n        backdropClick: \"backdropClick\",\n        positionChange: \"positionChange\",\n        attach: \"attach\",\n        detach: \"detach\",\n        overlayKeydown: \"overlayKeydown\",\n        overlayOutsideClick: \"overlayOutsideClick\"\n      },\n      exportAs: [\"cdkConnectedOverlay\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: Overlay\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHasBackdrop']\n    }],\n    lockPosition: [{\n      type: Input,\n      args: ['cdkConnectedOverlayLockPosition']\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayFlexibleDimensions']\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: ['cdkConnectedOverlayGrowAfterOpen']\n    }],\n    push: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPush']\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static {\n    this.ɵfac = function OverlayModule_Factory(t) {\n      return new (t || OverlayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OverlayModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n      imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n  _createContainer() {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n      this._document.addEventListener(eventName, fn);\n      this._fullScreenListener = fn;\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n  static {\n    this.ɵfac = function FullscreenOverlayContainer_Factory(t) {\n      return new (t || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FullscreenOverlayContainer,\n      factory: FullscreenOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };", "map": {"version": 3, "names": ["i1", "ScrollingModule", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "i6", "DOCUMENT", "i0", "Injectable", "Inject", "Optional", "ElementRef", "ApplicationRef", "ANIMATION_MODULE_TYPE", "InjectionToken", "Directive", "EventEmitter", "Input", "Output", "NgModule", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "i1$1", "supportsScrollBehavior", "_getEventTarget", "_isTestEnvironment", "filter", "take", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "i5", "BidiModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "Subject", "Subscription", "merge", "ESCAPE", "hasModifierKey", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_previousHTMLStyles", "top", "left", "_isEnabled", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "ScrollStrategyOptions_Factory", "t", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "OverlayConfig", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "BaseOverlayDispatcher_Factory", "OverlayKeyboardDispatcher", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "keydownEvents", "next", "_isAttached", "runOutsideAngular", "addEventListener", "removeEventListener", "OverlayKeyboardDispatcher_Factory", "OverlayOutsideClickDispatcher", "_platform", "_cursorStyleIsSet", "_pointerDownListener", "_pointerDownEventTarget", "_clickListener", "target", "slice", "_outsidePointerEvents", "outsidePointerEvents", "_addEventListeners", "IOS", "_cursorOriginalV<PERSON>ue", "cursor", "OverlayOutsideClickDispatcher_Factory", "Platform", "OverlayContainer", "_containerElement", "getContainerElement", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "OverlayContainer_Factory", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_backdropElement", "_backdropClick", "_attachments", "_detachments", "_locationChanges", "EMPTY", "_backdropClickHandler", "_backdropTransitionendHandler", "_disposeBackdrop", "_scrollStrategy", "_positionStrategy", "positionStrategy", "backdropElement", "hostElement", "portal", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "onStable", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "dispose", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenStable", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "parentNode", "backdropToDetach", "_backdropTimeout", "setTimeout", "cssClasses", "isAdd", "c", "subscription", "children", "backdrop", "clearTimeout", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "positions", "_preferredPositions", "connectedTo", "_overlayContainer", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_viewportMargin", "_scrollables", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_appliedPanelClasses", "position<PERSON><PERSON>es", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "_getScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "styles", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "for<PERSON>ach", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "parent", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "OverlayPositionBuilder_Factory", "nextUniqueId", "Overlay", "scrollStrategies", "_componentFactoryResolver", "_positionBuilder", "_injector", "_directionality", "_animationsModuleType", "create", "host", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "id", "_appRef", "get", "Overlay_Factory", "ComponentFactoryResolver", "Injector", "Directionality", "Location", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "CdkOverlayOrigin_Factory", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "selectors", "exportAs", "standalone", "CdkConnectedOverlay", "_position", "_updatePositionStrategy", "_hasBackdrop", "lockPosition", "_lockPosition", "_flexibleDimensions", "_push", "_overlay", "templateRef", "viewContainerRef", "scrollStrategyFactory", "_dir", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "viewportMargin", "open", "disableClose", "positionChange", "overlayKeydown", "overlayOutsideClick", "_templatePortal", "_scrollStrategyFactory", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_createPositionStrategy", "currentPosition", "_getFlexibleConnectedPositionStrategyOrigin", "transformOriginSelector", "CdkConnectedOverlay_Factory", "TemplateRef", "ViewContainerRef", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports", "FullscreenOverlayContainer", "platform", "_fullScreenEventName", "_fullScreenListener", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "fullscreenElement", "getFullscreenElement", "fn", "eventName", "_getEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "FullscreenOverlayContainer_Factory"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@angular/cdk/fesm2022/overlay.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, take, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._previousHTMLStyles = { top: '', left: '' };\n        this._isEnabled = false;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const body = this._document.body;\n        const viewport = this._viewportRuler.getViewportSize();\n        return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n        this._scrollSubscription = null;\n        /** Detaches the overlay ref and disables the scroll strategy. */\n        this._detach = () => {\n            this.disable();\n            if (this._overlayRef.hasAttached()) {\n                this._ngZone.run(() => this._overlayRef.detach());\n            }\n        };\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n        this._scrollSubscription = null;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        /** Do nothing on scroll. */\n        this.noop = () => new NoopScrollStrategy();\n        /**\n         * Close the overlay as soon as the user scrolls.\n         * @param config Configuration to be used inside the scroll strategy.\n         */\n        this.close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n        /** Block scrolling. */\n        this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n        /**\n         * Update the overlay's position on scroll.\n         * @param config Configuration to be used inside the scroll strategy.\n         * Allows debouncing the reposition calls.\n         */\n        this.reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n        this._document = document;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollStrategyOptions, deps: [{ token: i1.ScrollDispatcher }, { token: i1.ViewportRuler }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ScrollDispatcher }, { type: i1.ViewportRuler }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    constructor(config) {\n        /** Strategy to be used when handling scroll events while the overlay is open. */\n        this.scrollStrategy = new NoopScrollStrategy();\n        /** Custom class to add to the overlay pane. */\n        this.panelClass = '';\n        /** Whether the overlay has a backdrop. */\n        this.hasBackdrop = false;\n        /** Custom class to add to the backdrop */\n        this.backdropClass = 'cdk-overlay-dark-backdrop';\n        /**\n         * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.disposeOnNavigation = false;\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    constructor(document) {\n        /** Currently attached overlays in the order they were attached. */\n        this._attachedOverlays = [];\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BaseOverlayDispatcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    constructor(document, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._ngZone = _ngZone;\n        /** Keyboard event listener that will be attached to the body. */\n        this._keydownListener = (event) => {\n            const overlays = this._attachedOverlays;\n            for (let i = overlays.length - 1; i > -1; i--) {\n                // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n                // We want to target the most recent overlay, rather than trying to match where the event came\n                // from, because some components might open an overlay, but keep focus on a trigger element\n                // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n                // because we don't want overlays that don't handle keyboard events to block the ones below\n                // them that do.\n                if (overlays[i]._keydownEvents.observers.length > 0) {\n                    const keydownEvents = overlays[i]._keydownEvents;\n                    /** @breaking-change 14.0.0 _ngZone will be required. */\n                    if (this._ngZone) {\n                        this._ngZone.run(() => keydownEvents.next(event));\n                    }\n                    else {\n                        keydownEvents.next(event);\n                    }\n                    break;\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n            }\n            else {\n                this._document.body.addEventListener('keydown', this._keydownListener);\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._document.body.removeEventListener('keydown', this._keydownListener);\n            this._isAttached = false;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: [{ token: DOCUMENT }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    constructor(document, _platform, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._cursorStyleIsSet = false;\n        /** Store pointerdown event target to track origin of click. */\n        this._pointerDownListener = (event) => {\n            this._pointerDownEventTarget = _getEventTarget(event);\n        };\n        /** Click event listener that will be attached to the body propagate phase. */\n        this._clickListener = (event) => {\n            const target = _getEventTarget(event);\n            // In case of a click event, we want to check the origin of the click\n            // (e.g. in case where a user starts a click inside the overlay and\n            // releases the click outside of it).\n            // This is done by using the event target of the preceding pointerdown event.\n            // Every click event caused by a pointer device has a preceding pointerdown\n            // event, unless the click was programmatically triggered (e.g. in a unit test).\n            const origin = event.type === 'click' && this._pointerDownEventTarget\n                ? this._pointerDownEventTarget\n                : target;\n            // Reset the stored pointerdown event target, to avoid having it interfere\n            // in subsequent events.\n            this._pointerDownEventTarget = null;\n            // We copy the array because the original may be modified asynchronously if the\n            // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n            // the for loop.\n            const overlays = this._attachedOverlays.slice();\n            // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n            // We want to target all overlays for which the click could be considered as outside click.\n            // As soon as we reach an overlay for which the click is not outside click we break off\n            // the loop.\n            for (let i = overlays.length - 1; i > -1; i--) {\n                const overlayRef = overlays[i];\n                if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                    continue;\n                }\n                // If it's a click inside the overlay, just break - we should do nothing\n                // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n                // and proceed with the next overlay\n                if (overlayRef.overlayElement.contains(target) ||\n                    overlayRef.overlayElement.contains(origin)) {\n                    break;\n                }\n                const outsidePointerEvents = overlayRef._outsidePointerEvents;\n                /** @breaking-change 14.0.0 _ngZone will be required. */\n                if (this._ngZone) {\n                    this._ngZone.run(() => outsidePointerEvents.next(event));\n                }\n                else {\n                    outsidePointerEvents.next(event);\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n            }\n            else {\n                this._addEventListeners(body);\n            }\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            const body = this._document.body;\n            body.removeEventListener('pointerdown', this._pointerDownListener, true);\n            body.removeEventListener('click', this._clickListener, true);\n            body.removeEventListener('auxclick', this._clickListener, true);\n            body.removeEventListener('contextmenu', this._clickListener, true);\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    _addEventListeners(body) {\n        body.addEventListener('pointerdown', this._pointerDownListener, true);\n        body.addEventListener('click', this._clickListener, true);\n        body.addEventListener('auxclick', this._clickListener, true);\n        body.addEventListener('contextmenu', this._clickListener, true);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    constructor(document, _platform) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayContainer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._backdropElement = null;\n        this._backdropClick = new Subject();\n        this._attachments = new Subject();\n        this._detachments = new Subject();\n        this._locationChanges = Subscription.EMPTY;\n        this._backdropClickHandler = (event) => this._backdropClick.next(event);\n        this._backdropTransitionendHandler = (event) => {\n            this._disposeBackdrop(event.target);\n        };\n        /** Stream of keydown events dispatched to this overlay. */\n        this._keydownEvents = new Subject();\n        /** Stream of mouse outside events dispatched to this overlay. */\n        this._outsidePointerEvents = new Subject();\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropElement;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // Update the position once the zone is stable so that the overlay will be fully rendered\n        // before attempting to position it, as the position may depend on the size of the rendered\n        // content.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            // The overlay could've been detached before the zone has stabilized.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenStable();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._disposeBackdrop(this._backdropElement);\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._previousHostParent = this._pane = this._host = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropElement = this._document.createElement('div');\n        this._backdropElement.classList.add('cdk-overlay-backdrop');\n        if (this._animationsDisabled) {\n            this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropElement, this._host);\n        // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n        // action desired when such a click occurs (usually closing the overlay).\n        this._backdropElement.addEventListener('click', this._backdropClickHandler);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    if (this._backdropElement) {\n                        this._backdropElement.classList.add(showingClass);\n                    }\n                });\n            });\n        }\n        else {\n            this._backdropElement.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        const backdropToDetach = this._backdropElement;\n        if (!backdropToDetach) {\n            return;\n        }\n        if (this._animationsDisabled) {\n            this._disposeBackdrop(backdropToDetach);\n            return;\n        }\n        backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n        this._ngZone.runOutsideAngular(() => {\n            backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n        });\n        // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n        // In this case we make it unclickable and we try to remove it after a delay.\n        backdropToDetach.style.pointerEvents = 'none';\n        // Run this outside the Angular zone because there's nothing that Angular cares about.\n        // If it were to run inside the Angular zone, every test that used Overlay would have to be\n        // either async or fakeAsync.\n        this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n            this._disposeBackdrop(backdropToDetach);\n        }, 500));\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenStable() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._ngZone.onStable\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        if (scrollStrategy) {\n            scrollStrategy.disable();\n            if (scrollStrategy.detach) {\n                scrollStrategy.detach();\n            }\n        }\n    }\n    /** Removes a backdrop element from the DOM. */\n    _disposeBackdrop(backdrop) {\n        if (backdrop) {\n            backdrop.removeEventListener('click', this._backdropClickHandler);\n            backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n            backdrop.remove();\n            // It is possible that a new portal has been attached to this overlay since we started\n            // removing the backdrop. If that is the case, only clear the backdrop reference if it\n            // is still the same instance that we started to remove.\n            if (this._backdropElement === backdrop) {\n                this._backdropElement = null;\n            }\n        }\n        if (this._backdropTimeout) {\n            clearTimeout(this._backdropTimeout);\n            this._backdropTimeout = undefined;\n        }\n    }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n        this._lastBoundingBoxSize = { width: 0, height: 0 };\n        /** Whether the overlay was pushed in a previous positioning. */\n        this._isPushed = false;\n        /** Whether the overlay can be pushed on-screen on the initial open. */\n        this._canPush = true;\n        /** Whether the overlay can grow via flexible width/height after the initial open. */\n        this._growAfterOpen = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this._hasFlexibleDimensions = true;\n        /** Whether the overlay position is locked. */\n        this._positionLocked = false;\n        /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n        this._viewportMargin = 0;\n        /** The Scrollable containers used to check scrollable view properties on position change. */\n        this._scrollables = [];\n        /** Ordered list of preferred positions, from most to least desirable. */\n        this._preferredPositions = [];\n        /** Subject that emits whenever the position changes. */\n        this._positionChanges = new Subject();\n        /** Subscription to viewport size changes. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Default offset for the overlay along the x axis. */\n        this._offsetX = 0;\n        /** Default offset for the overlay along the y axis. */\n        this._offsetY = 0;\n        /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n        this._appliedPanelClasses = [];\n        /** Observable sequence of position changes. */\n        this.positionChanges = this._positionChanges;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollableViewProperties = this._getScrollVisibility();\n            const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n            this._positionChanges.next(changeEvent);\n        }\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the ClientRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    constructor() {\n        this._cssPosition = 'static';\n        this._topOffset = '';\n        this._bottomOffset = '';\n        this._alignItems = '';\n        this._xPosition = '';\n        this._xOffset = '';\n        this._width = '';\n        this._height = '';\n        this._isDisposed = false;\n    }\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n    }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayPositionBuilder, deps: [{ token: i1.ViewportRuler }, { token: DOCUMENT }, { token: i1$1.Platform }, { token: OverlayContainer }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: OverlayContainer }]; } });\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n        this.scrollStrategies = scrollStrategies;\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._positionBuilder = _positionBuilder;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._injector = _injector;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._directionality = _directionality;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsModuleType = _animationsModuleType;\n    }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations');\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = `cdk-overlay-${nextUniqueId++}`;\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Overlay, deps: [{ token: ScrollStrategyOptions }, { token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: OverlayPositionBuilder }, { token: OverlayKeyboardDispatcher }, { token: i0.Injector }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i5.Directionality }, { token: i6.Location }, { token: OverlayOutsideClickDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Overlay, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: ScrollStrategyOptions }, { type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: OverlayPositionBuilder }, { type: OverlayKeyboardDispatcher }, { type: i0.Injector }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i5.Directionality }, { type: i6.Location }, { type: OverlayOutsideClickDispatcher }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkOverlayOrigin, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** Whether or not the overlay should attach a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /** Whether or not the overlay should be locked when scrolling. */\n    get lockPosition() {\n        return this._lockPosition;\n    }\n    set lockPosition(value) {\n        this._lockPosition = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    get flexibleDimensions() {\n        return this._flexibleDimensions;\n    }\n    set flexibleDimensions(value) {\n        this._flexibleDimensions = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    get growAfterOpen() {\n        return this._growAfterOpen;\n    }\n    set growAfterOpen(value) {\n        this._growAfterOpen = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    get push() {\n        return this._push;\n    }\n    set push(value) {\n        this._push = coerceBooleanProperty(value);\n    }\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n        this._overlay = _overlay;\n        this._dir = _dir;\n        this._hasBackdrop = false;\n        this._lockPosition = false;\n        this._growAfterOpen = false;\n        this._flexibleDimensions = false;\n        this._push = false;\n        this._backdropSubscription = Subscription.EMPTY;\n        this._attachSubscription = Subscription.EMPTY;\n        this._detachSubscription = Subscription.EMPTY;\n        this._positionSubscription = Subscription.EMPTY;\n        /** Margin between the overlay and the viewport edges. */\n        this.viewportMargin = 0;\n        /** Whether the overlay is open. */\n        this.open = false;\n        /** Whether the overlay can be closed by user interaction. */\n        this.disableClose = false;\n        /** Event emitted when the backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Event emitted when the position has changed. */\n        this.positionChange = new EventEmitter();\n        /** Event emitted when the overlay has been attached. */\n        this.attach = new EventEmitter();\n        /** Event emitted when the overlay has been detached. */\n        this.detach = new EventEmitter();\n        /** Emits when there are keyboard events that are targeted at the overlay. */\n        this.overlayKeydown = new EventEmitter();\n        /** Emits when there are mouse outside click events that are targeted at the overlay. */\n        this.overlayOutsideClick = new EventEmitter();\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n        }\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this._attachOverlay() : this._detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this._detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            this.overlayOutsideClick.next(event);\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir,\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getFlexibleConnectedPositionStrategyOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getFlexibleConnectedPositionStrategyOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n    _attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this.positionChange.emit(position);\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n    }\n    /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n    _detachOverlay() {\n        if (this._overlayRef) {\n            this._overlayRef.detach();\n        }\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkConnectedOverlay, deps: [{ token: Overlay }, { token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"], push: [\"cdkConnectedOverlayPush\", \"push\"] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                    standalone: true,\n                }]\n        }], ctorParameters: function () { return [{ type: Overlay }, { type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHasBackdrop']\n            }], lockPosition: [{\n                type: Input,\n                args: ['cdkConnectedOverlayLockPosition']\n            }], flexibleDimensions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayFlexibleDimensions']\n            }], growAfterOpen: [{\n                type: Input,\n                args: ['cdkConnectedOverlayGrowAfterOpen']\n            }], push: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPush']\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass OverlayModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    constructor(_document, platform) {\n        super(_document, platform);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._fullScreenEventName && this._fullScreenListener) {\n            this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n        }\n    }\n    _createContainer() {\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n    }\n    _adjustParentForFullscreenChange() {\n        if (!this._containerElement) {\n            return;\n        }\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n    }\n    _addFullscreenChangeListener(fn) {\n        const eventName = this._getEventName();\n        if (eventName) {\n            if (this._fullScreenListener) {\n                this._document.removeEventListener(eventName, this._fullScreenListener);\n            }\n            this._document.addEventListener(eventName, fn);\n            this._fullScreenListener = fn;\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FullscreenOverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,wBAAwB;AACvF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjL,SAASC,mBAAmB,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,uBAAuB;AAC/F,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,sBAAsB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,uBAAuB;AACnG,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACnE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAE9D,MAAMC,uBAAuB,GAAGjB,sBAAsB,CAAC,CAAC;AACxD;AACA;AACA;AACA,MAAMkB,mBAAmB,CAAC;EACtBC,WAAWA,CAACC,cAAc,EAAEC,QAAQ,EAAE;IAClC,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,mBAAmB,GAAG;MAAEC,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IAChD,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAGL,QAAQ;EAC7B;EACA;EACAM,MAAMA,CAAA,EAAG,CAAE;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACK,eAAe;MAC3C,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACZ,cAAc,CAACa,yBAAyB,CAAC,CAAC;MAC9E;MACA,IAAI,CAACX,mBAAmB,CAACE,IAAI,GAAGM,IAAI,CAACI,KAAK,CAACV,IAAI,IAAI,EAAE;MACrD,IAAI,CAACF,mBAAmB,CAACC,GAAG,GAAGO,IAAI,CAACI,KAAK,CAACX,GAAG,IAAI,EAAE;MACnD;MACA;MACAO,IAAI,CAACI,KAAK,CAACV,IAAI,GAAG5B,mBAAmB,CAAC,CAAC,IAAI,CAACoC,uBAAuB,CAACR,IAAI,CAAC;MACzEM,IAAI,CAACI,KAAK,CAACX,GAAG,GAAG3B,mBAAmB,CAAC,CAAC,IAAI,CAACoC,uBAAuB,CAACT,GAAG,CAAC;MACvEO,IAAI,CAACK,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACX,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAY,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACZ,UAAU,EAAE;MACjB,MAAMa,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;MAC3C,MAAMQ,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAK;MAC5B,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAAClB,UAAU,GAAG,KAAK;MACvBe,SAAS,CAAChB,IAAI,GAAG,IAAI,CAACF,mBAAmB,CAACE,IAAI;MAC9CgB,SAAS,CAACjB,GAAG,GAAG,IAAI,CAACD,mBAAmB,CAACC,GAAG;MAC5Ce,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI5B,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACf,uBAAuB,CAACR,IAAI,EAAE,IAAI,CAACQ,uBAAuB,CAACT,GAAG,CAAC;MAClF,IAAIN,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAf,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,MAAMS,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;IAC3C,IAAIO,IAAI,CAACH,SAAS,CAACa,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACvB,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMc,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;IAChC,MAAMU,QAAQ,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,eAAe,CAAC,CAAC;IACtD,OAAOX,IAAI,CAACY,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIb,IAAI,CAACc,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACnF;AACJ;;AAEA;AACA;AACA;AACA,SAASC,wCAAwCA,CAAA,EAAG;EAChD,OAAOC,KAAK,CAAE,4CAA2C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBtC,WAAWA,CAACuC,iBAAiB,EAAEC,OAAO,EAAEvC,cAAc,EAAEwC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACvC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACzB,OAAO,CAAC,CAAC;MACd,IAAI,IAAI,CAAC0B,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;QAChC,IAAI,CAACL,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC;EACL;EACA;EACAvC,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACiC,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACX,iBAAiB,CAACY,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAACpE,MAAM,CAACqE,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACT,WAAW,CAACU,cAAc,CAACzB,QAAQ,CAACwB,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACf,OAAO,IAAI,IAAI,CAACA,OAAO,CAACgB,SAAS,IAAI,IAAI,CAAChB,OAAO,CAACgB,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACzD,cAAc,CAACa,yBAAyB,CAAC,CAAC,CAACV,GAAG;MACjF,IAAI,CAACsC,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC,CAACV,GAAG;QAC1E,IAAIyD,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACF,sBAAsB,CAAC,GAAG,IAAI,CAACjB,OAAO,CAACgB,SAAS,EAAE;UACjF,IAAI,CAACd,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD,IAAI,CAACC,WAAW,CAACmB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACrB,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,IAAI,CAAChB,OAAO,CAAC;IAC7D;EACJ;EACA;EACAzB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMqB,kBAAkB,CAAC;EACrB;EACAxD,MAAMA,CAAA,EAAG,CAAE;EACX;EACAS,OAAOA,CAAA,EAAG,CAAE;EACZ;EACAV,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,4BAA4BA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAAClE,GAAG;IACzD,MAAMqE,YAAY,GAAGN,OAAO,CAAC/D,GAAG,GAAGkE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACjE,IAAI;IACxD,MAAMuE,YAAY,GAAGT,OAAO,CAAC9D,IAAI,GAAGiE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAAC/D,GAAG,GAAG0E,mBAAmB,CAAC1E,GAAG;IAC1D,MAAM4E,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAAC9D,IAAI,GAAGyE,mBAAmB,CAACzE,IAAI;IAC3D,MAAM6E,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BnF,WAAWA,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACnC;EACA;EACAlC,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACiC,mBAAmB,EAAE;MAC3B,MAAM0C,QAAQ,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAAC3C,mBAAmB,GAAG,IAAI,CAACH,iBAAiB,CAACY,QAAQ,CAACiC,QAAQ,CAAC,CAACzB,SAAS,CAAC,MAAM;QACjF,IAAI,CAACf,WAAW,CAACmB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC3C,WAAW,CAACU,cAAc,CAACkC,qBAAqB,CAAC,CAAC;UAC3E,MAAM;YAAErD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAChC,cAAc,CAAC8B,eAAe,CAAC,CAAC;UAC/D;UACA;UACA,MAAM0D,WAAW,GAAG,CAAC;YAAEtD,KAAK;YAAEF,MAAM;YAAEuC,MAAM,EAAEvC,MAAM;YAAE0C,KAAK,EAAExC,KAAK;YAAE/B,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAI6D,4BAA4B,CAACqB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAACvE,OAAO,CAAC,CAAC;YACd,IAAI,CAACsB,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA7B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,qBAAqB,CAAC;EACxB1F,WAAWA,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEtC,QAAQ,EAAE;IAC9D,IAAI,CAACqC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACmD,IAAI,GAAG,MAAM,IAAI1B,kBAAkB,CAAC,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC2B,KAAK,GAAIC,MAAM,IAAK,IAAIvD,mBAAmB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACvC,cAAc,EAAE4F,MAAM,CAAC;IACnH;IACA,IAAI,CAACC,KAAK,GAAG,MAAM,IAAI/F,mBAAmB,CAAC,IAAI,CAACE,cAAc,EAAE,IAAI,CAACM,SAAS,CAAC;IAC/E;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwF,UAAU,GAAIF,MAAM,IAAK,IAAIV,wBAAwB,CAAC,IAAI,CAAC5C,iBAAiB,EAAE,IAAI,CAACtC,cAAc,EAAE,IAAI,CAACuC,OAAO,EAAEqD,MAAM,CAAC;IAC7H,IAAI,CAACtF,SAAS,GAAGL,QAAQ;EAC7B;EACA;IAAS,IAAI,CAAC8F,IAAI,YAAAC,8BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFR,qBAAqB,EAA/B9H,EAAE,CAAAuI,QAAA,CAA+C9I,EAAE,CAACG,gBAAgB,GAApEI,EAAE,CAAAuI,QAAA,CAA+E9I,EAAE,CAACI,aAAa,GAAjGG,EAAE,CAAAuI,QAAA,CAA4GvI,EAAE,CAACwI,MAAM,GAAvHxI,EAAE,CAAAuI,QAAA,CAAkIxI,QAAQ;IAAA,CAA6C;EAAE;EAC3R;IAAS,IAAI,CAAC0I,KAAK,kBAD6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EACYb,qBAAqB;MAAAc,OAAA,EAArBd,qBAAqB,CAAAM,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAHoGrF,EAAE,CAAA8I,iBAAA,CAGXhB,qBAAqB,EAAc,CAAC;IACnHiB,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEtJ,EAAE,CAACG;IAAiB,CAAC,EAAE;MAAEmJ,IAAI,EAAEtJ,EAAE,CAACI;IAAc,CAAC,EAAE;MAAEkJ,IAAI,EAAE/I,EAAE,CAACwI;IAAO,CAAC,EAAE;MAAEO,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9IH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMoJ,aAAa,CAAC;EAChB/G,WAAWA,CAAC6F,MAAM,EAAE;IAChB;IACA,IAAI,CAACmB,cAAc,GAAG,IAAI/C,kBAAkB,CAAC,CAAC;IAC9C;IACA,IAAI,CAACgD,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,aAAa,GAAG,2BAA2B;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAIvB,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAMwB,UAAU,GAAGC,MAAM,CAACC,IAAI,CAAC1B,MAAM,CAAC;MACtC,KAAK,MAAM2B,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAIxB,MAAM,CAAC2B,GAAG,CAAC,KAAKX,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACW,GAAG,CAAC,GAAG3B,MAAM,CAAC2B,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA,MAAMC,sBAAsB,CAAC;EACzBzH,WAAWA,CAAC0H,MAAM,EAAEC,OAAO,EAC3B;EACAC,OAAO,EACP;EACAC,OAAO,EACP;EACAZ,UAAU,EAAE;IACR,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACZ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,OAAO,GAAGJ,MAAM,CAACI,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACK,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACK,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;AAE1B;AACA,MAAMC,8BAA8B,CAAC;EACjCnI,WAAWA,CAAA,CACX;EACAoI,cAAc,EACd;EACAC,wBAAwB,EAAE;IACtB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAMnG,KAAK,CAAE,8BAA6BkG,QAAS,KAAIC,KAAM,KAAI,GAC5D,uCAAsC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAMnG,KAAK,CAAE,8BAA6BkG,QAAS,KAAIC,KAAM,KAAI,GAC5D,sCAAqC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxB1I,WAAWA,CAACE,QAAQ,EAAE;IAClB;IACA,IAAI,CAACyI,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACpI,SAAS,GAAGL,QAAQ;EAC7B;EACA0I,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7F,MAAM,CAAC,CAAC;EACjB;EACA;EACA9B,GAAGA,CAAC+B,UAAU,EAAE;IACZ;IACA,IAAI,CAACtB,MAAM,CAACsB,UAAU,CAAC;IACvB,IAAI,CAAC2F,iBAAiB,CAACE,IAAI,CAAC7F,UAAU,CAAC;EAC3C;EACA;EACAtB,MAAMA,CAACsB,UAAU,EAAE;IACf,MAAM8F,KAAK,GAAG,IAAI,CAACH,iBAAiB,CAACI,OAAO,CAAC/F,UAAU,CAAC;IACxD,IAAI8F,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACH,iBAAiB,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACH,iBAAiB,CAACM,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAClG,MAAM,CAAC,CAAC;IACjB;EACJ;EACA;IAAS,IAAI,CAACiD,IAAI,YAAAkD,8BAAAhD,CAAA;MAAA,YAAAA,CAAA,IAAwFwC,qBAAqB,EAhK/B9K,EAAE,CAAAuI,QAAA,CAgK+CxI,QAAQ;IAAA,CAA6C;EAAE;EACxM;IAAS,IAAI,CAAC0I,KAAK,kBAjK6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EAiKYmC,qBAAqB;MAAAlC,OAAA,EAArBkC,qBAAqB,CAAA1C,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAnKoGrF,EAAE,CAAA8I,iBAAA,CAmKXgC,qBAAqB,EAAc,CAAC;IACnH/B,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAMwL,yBAAyB,SAAST,qBAAqB,CAAC;EAC1D1I,WAAWA,CAACE,QAAQ,EACpB;EACAsC,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAACsC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAC4G,gBAAgB,GAAIC,KAAK,IAAK;MAC/B,MAAMC,QAAQ,GAAG,IAAI,CAACX,iBAAiB;MACvC,KAAK,IAAIY,CAAC,GAAGD,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;UACjD,MAAMS,aAAa,GAAGJ,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc;UAChD;UACA,IAAI,IAAI,CAAChH,OAAO,EAAE;YACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAM4G,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC,CAAC;UACrD,CAAC,MACI;YACDK,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC;UAC7B;UACA;QACJ;MACJ;IACJ,CAAC;EACL;EACA;EACApI,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAAC4G,WAAW,EAAE;MACnB;MACA,IAAI,IAAI,CAACpH,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACqH,iBAAiB,CAAC,MAAM,IAAI,CAACtJ,SAAS,CAACa,IAAI,CAAC0I,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC,CAAC;MAChH,CAAC,MACI;QACD,IAAI,CAAC7I,SAAS,CAACa,IAAI,CAAC0I,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC;MAC1E;MACA,IAAI,CAACQ,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA7G,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC6G,WAAW,EAAE;MAClB,IAAI,CAACrJ,SAAS,CAACa,IAAI,CAAC2I,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACX,gBAAgB,CAAC;MACzE,IAAI,CAACQ,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAAgE,kCAAA9D,CAAA;MAAA,YAAAA,CAAA,IAAwFiD,yBAAyB,EApOnCvL,EAAE,CAAAuI,QAAA,CAoOmDxI,QAAQ,GApO7DC,EAAE,CAAAuI,QAAA,CAoOwEvI,EAAE,CAACwI,MAAM;IAAA,CAA6D;EAAE;EAClP;IAAS,IAAI,CAACC,KAAK,kBArO6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EAqOY4C,yBAAyB;MAAA3C,OAAA,EAAzB2C,yBAAyB,CAAAnD,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACpK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KAvOoGrF,EAAE,CAAA8I,iBAAA,CAuOXyC,yBAAyB,EAAc,CAAC;IACvHxC,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE/I,EAAE,CAACwI,MAAM;MAAEU,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA,MAAMkM,6BAA6B,SAASvB,qBAAqB,CAAC;EAC9D1I,WAAWA,CAACE,QAAQ,EAAEgK,SAAS,EAC/B;EACA1H,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAACgK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC1H,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2H,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACC,oBAAoB,GAAIf,KAAK,IAAK;MACnC,IAAI,CAACgB,uBAAuB,GAAGvL,eAAe,CAACuK,KAAK,CAAC;IACzD,CAAC;IACD;IACA,IAAI,CAACiB,cAAc,GAAIjB,KAAK,IAAK;MAC7B,MAAMkB,MAAM,GAAGzL,eAAe,CAACuK,KAAK,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA,MAAM3B,MAAM,GAAG2B,KAAK,CAAC1C,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC0D,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BE,MAAM;MACZ;MACA;MACA,IAAI,CAACF,uBAAuB,GAAG,IAAI;MACnC;MACA;MACA;MACA,MAAMf,QAAQ,GAAG,IAAI,CAACX,iBAAiB,CAAC6B,KAAK,CAAC,CAAC;MAC/C;MACA;MACA;MACA;MACA,KAAK,IAAIjB,CAAC,GAAGD,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMvG,UAAU,GAAGsG,QAAQ,CAACC,CAAC,CAAC;QAC9B,IAAIvG,UAAU,CAACyH,qBAAqB,CAAChB,SAAS,CAACR,MAAM,GAAG,CAAC,IAAI,CAACjG,UAAU,CAACH,WAAW,CAAC,CAAC,EAAE;UACpF;QACJ;QACA;QACA;QACA;QACA,IAAIG,UAAU,CAACM,cAAc,CAACzB,QAAQ,CAAC0I,MAAM,CAAC,IAC1CvH,UAAU,CAACM,cAAc,CAACzB,QAAQ,CAAC6F,MAAM,CAAC,EAAE;UAC5C;QACJ;QACA,MAAMgD,oBAAoB,GAAG1H,UAAU,CAACyH,qBAAqB;QAC7D;QACA,IAAI,IAAI,CAACjI,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAM4H,oBAAoB,CAACf,IAAI,CAACN,KAAK,CAAC,CAAC;QAC5D,CAAC,MACI;UACDqB,oBAAoB,CAACf,IAAI,CAACN,KAAK,CAAC;QACpC;MACJ;IACJ,CAAC;EACL;EACA;EACApI,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC4G,WAAW,EAAE;MACnB,MAAMxI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC;MACA,IAAI,IAAI,CAACoB,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACqH,iBAAiB,CAAC,MAAM,IAAI,CAACc,kBAAkB,CAACvJ,IAAI,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACuJ,kBAAkB,CAACvJ,IAAI,CAAC;MACjC;MACA;MACA;MACA,IAAI,IAAI,CAAC8I,SAAS,CAACU,GAAG,IAAI,CAAC,IAAI,CAACT,iBAAiB,EAAE;QAC/C,IAAI,CAACU,oBAAoB,GAAGzJ,IAAI,CAACL,KAAK,CAAC+J,MAAM;QAC7C1J,IAAI,CAACL,KAAK,CAAC+J,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACX,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAACP,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA7G,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC6G,WAAW,EAAE;MAClB,MAAMxI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChCA,IAAI,CAAC2I,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACK,oBAAoB,EAAE,IAAI,CAAC;MACxEhJ,IAAI,CAAC2I,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAC5DlJ,IAAI,CAAC2I,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAC/DlJ,IAAI,CAAC2I,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAClE,IAAI,IAAI,CAACJ,SAAS,CAACU,GAAG,IAAI,IAAI,CAACT,iBAAiB,EAAE;QAC9C/I,IAAI,CAACL,KAAK,CAAC+J,MAAM,GAAG,IAAI,CAACD,oBAAoB;QAC7C,IAAI,CAACV,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAACP,WAAW,GAAG,KAAK;IAC5B;EACJ;EACAe,kBAAkBA,CAACvJ,IAAI,EAAE;IACrBA,IAAI,CAAC0I,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACM,oBAAoB,EAAE,IAAI,CAAC;IACrEhJ,IAAI,CAAC0I,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;IACzDlJ,IAAI,CAAC0I,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;IAC5DlJ,IAAI,CAAC0I,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;EACnE;EACA;IAAS,IAAI,CAACtE,IAAI,YAAA+E,sCAAA7E,CAAA;MAAA,YAAAA,CAAA,IAAwF+D,6BAA6B,EAjWvCrM,EAAE,CAAAuI,QAAA,CAiWuDxI,QAAQ,GAjWjEC,EAAE,CAAAuI,QAAA,CAiW4EvH,IAAI,CAACoM,QAAQ,GAjW3FpN,EAAE,CAAAuI,QAAA,CAiWsGvI,EAAE,CAACwI,MAAM;IAAA,CAA6D;EAAE;EAChR;IAAS,IAAI,CAACC,KAAK,kBAlW6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EAkWY0D,6BAA6B;MAAAzD,OAAA,EAA7ByD,6BAA6B,CAAAjE,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACxK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KApWoGrF,EAAE,CAAA8I,iBAAA,CAoWXuD,6BAA6B,EAAc,CAAC;IAC3HtD,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE/H,IAAI,CAACoM;IAAS,CAAC,EAAE;MAAErE,IAAI,EAAE/I,EAAE,CAACwI,MAAM;MAAEU,UAAU,EAAE,CAAC;QAC3DH,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMkN,gBAAgB,CAAC;EACnBjL,WAAWA,CAACE,QAAQ,EAAEgK,SAAS,EAAE;IAC7B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC3J,SAAS,GAAGL,QAAQ;EAC7B;EACA0I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsC,iBAAiB,EAAExJ,MAAM,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyJ,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACD,iBAAiB,EAAE;MACzB,IAAI,CAACE,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACF,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAAA,EAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAACnB,SAAS,CAACoB,SAAS,IAAIvM,kBAAkB,CAAC,CAAC,EAAE;MAClD,MAAMwM,0BAA0B,GAAG,IAAI,CAAChL,SAAS,CAACiL,gBAAgB,CAAE,IAAGH,cAAe,uBAAsB,GAAI,IAAGA,cAAe,mBAAkB,CAAC;MACrJ;MACA;MACA,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,0BAA0B,CAACtC,MAAM,EAAEM,CAAC,EAAE,EAAE;QACxDgC,0BAA0B,CAAChC,CAAC,CAAC,CAAC7H,MAAM,CAAC,CAAC;MAC1C;IACJ;IACA,MAAM+J,SAAS,GAAG,IAAI,CAAClL,SAAS,CAACmL,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAACzK,SAAS,CAACC,GAAG,CAACoK,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAItM,kBAAkB,CAAC,CAAC,EAAE;MACtB0M,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAACzB,SAAS,CAACoB,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAACpL,SAAS,CAACa,IAAI,CAACwK,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACP,iBAAiB,GAAGO,SAAS;EACtC;EACA;IAAS,IAAI,CAACzF,IAAI,YAAA6F,yBAAA3F,CAAA;MAAA,YAAAA,CAAA,IAAwF+E,gBAAgB,EAxa1BrN,EAAE,CAAAuI,QAAA,CAwa0CxI,QAAQ,GAxapDC,EAAE,CAAAuI,QAAA,CAwa+DvH,IAAI,CAACoM,QAAQ;IAAA,CAA6C;EAAE;EAC7N;IAAS,IAAI,CAAC3E,KAAK,kBAza6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EAyaY0E,gBAAgB;MAAAzE,OAAA,EAAhByE,gBAAgB,CAAAjF,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KA3aoGrF,EAAE,CAAA8I,iBAAA,CA2aXuE,gBAAgB,EAAc,CAAC;IAC9GtE,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE/H,IAAI,CAACoM;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjD;AACA;AACA;AACA;AACA,MAAMc,UAAU,CAAC;EACb9L,WAAWA,CAAC+L,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAExJ,OAAO,EAAED,OAAO,EAAE0J,mBAAmB,EAAE3L,SAAS,EAAE4L,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAE;IACxJ,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACxJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0J,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAC3L,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC4L,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAI9M,OAAO,CAAC,CAAC;IACnC,IAAI,CAAC+M,YAAY,GAAG,IAAI/M,OAAO,CAAC,CAAC;IACjC,IAAI,CAACgN,YAAY,GAAG,IAAIhN,OAAO,CAAC,CAAC;IACjC,IAAI,CAACiN,gBAAgB,GAAGhN,YAAY,CAACiN,KAAK;IAC1C,IAAI,CAACC,qBAAqB,GAAIvD,KAAK,IAAK,IAAI,CAACkD,cAAc,CAAC5C,IAAI,CAACN,KAAK,CAAC;IACvE,IAAI,CAACwD,6BAA6B,GAAIxD,KAAK,IAAK;MAC5C,IAAI,CAACyD,gBAAgB,CAACzD,KAAK,CAACkB,MAAM,CAAC;IACvC,CAAC;IACD;IACA,IAAI,CAACf,cAAc,GAAG,IAAI/J,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACgL,qBAAqB,GAAG,IAAIhL,OAAO,CAAC,CAAC;IAC1C,IAAIgD,OAAO,CAACuE,cAAc,EAAE;MACxB,IAAI,CAAC+F,eAAe,GAAGtK,OAAO,CAACuE,cAAc;MAC7C,IAAI,CAAC+F,eAAe,CAACvM,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACwM,iBAAiB,GAAGvK,OAAO,CAACwK,gBAAgB;EACrD;EACA;EACA,IAAI3J,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC2I,KAAK;EACrB;EACA;EACA,IAAIiB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACZ,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIa,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIxL,MAAMA,CAAC4M,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACqB,aAAa,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAAC1B,WAAW,CAAC,IAAI,CAACI,KAAK,CAAC;IACpD;IACA,MAAMuB,YAAY,GAAG,IAAI,CAACxB,aAAa,CAACvL,MAAM,CAAC4M,MAAM,CAAC;IACtD,IAAI,IAAI,CAACJ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACxM,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACgN,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACX,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACtM,MAAM,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI,CAAC+B,OAAO,CAACmL,QAAQ,CAACvK,IAAI,CAACnE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0E,SAAS,CAAC,MAAM;MAChD;MACA,IAAI,IAAI,CAACd,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACkB,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAAC6J,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACnL,OAAO,CAACyE,WAAW,EAAE;MAC1B,IAAI,CAAC2G,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAACpL,OAAO,CAACwE,UAAU,EAAE;MACzB,IAAI,CAAC6G,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAE,IAAI,CAACxJ,OAAO,CAACwE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAACuF,YAAY,CAAC7C,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAACuC,mBAAmB,CAACjL,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAACwB,OAAO,CAAC2E,mBAAmB,EAAE;MAClC,IAAI,CAACsF,gBAAgB,GAAG,IAAI,CAACP,SAAS,CAACxI,SAAS,CAAC,MAAM,IAAI,CAACoK,OAAO,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAAC3B,uBAAuB,CAACnL,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,OAAOsM,YAAY,EAAES,SAAS,KAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAT,YAAY,CAACS,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAACnL,WAAW,CAAC,CAAC,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACL,OAAO,CAACqH,iBAAiB,CAAC,MAAMoE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACpL,MAAM,CAAC,CAAC,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAOwK,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACIxK,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAACuL,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACR,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAACZ,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACjK,MAAM,EAAE;MACzD,IAAI,CAACiK,iBAAiB,CAACjK,MAAM,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACgK,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC7L,OAAO,CAAC,CAAC;IAClC;IACA,MAAMmN,gBAAgB,GAAG,IAAI,CAACtC,aAAa,CAAChJ,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAAC0J,YAAY,CAAC9C,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAACuC,mBAAmB,CAACxK,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAAC4M,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC5B,gBAAgB,CAAC1I,WAAW,CAAC,CAAC;IACnC,IAAI,CAACoI,uBAAuB,CAAC1K,MAAM,CAAC,IAAI,CAAC;IACzC,OAAO2M,gBAAgB;EAC3B;EACA;EACAN,OAAOA,CAAA,EAAG;IACN,MAAMQ,UAAU,GAAG,IAAI,CAAC1L,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAACmK,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACe,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACS,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC1B,gBAAgB,CAAC,IAAI,CAACR,gBAAgB,CAAC;IAC5C,IAAI,CAACI,gBAAgB,CAAC1I,WAAW,CAAC,CAAC;IACnC,IAAI,CAACkI,mBAAmB,CAACxK,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAACqK,aAAa,CAACgC,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACvB,YAAY,CAACiC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAClC,cAAc,CAACkC,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACjF,cAAc,CAACiF,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAChE,qBAAqB,CAACgE,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACrC,uBAAuB,CAAC1K,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI,CAACsK,KAAK,EAAEtK,MAAM,CAAC,CAAC;IACpB,IAAI,CAAC4L,mBAAmB,GAAG,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI;IACzD,IAAIuC,UAAU,EAAE;MACZ,IAAI,CAAC9B,YAAY,CAAC9C,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC8C,YAAY,CAACgC,QAAQ,CAAC,CAAC;EAChC;EACA;EACA5L,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACkJ,aAAa,CAAClJ,WAAW,CAAC,CAAC;EAC3C;EACA;EACA6L,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnC,cAAc;EAC9B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACA/C,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACF,cAAc;EAC9B;EACA;EACAkB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACD,qBAAqB;EACrC;EACA;EACAoE,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACpM,OAAO;EACvB;EACA;EACAsB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACiJ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC8B,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;EACAC,sBAAsBA,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAAChC,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACe,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACf,iBAAiB,GAAGgC,QAAQ;IACjC,IAAI,IAAI,CAACnM,WAAW,CAAC,CAAC,EAAE;MACpBmM,QAAQ,CAACxO,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAACuD,cAAc,CAAC,CAAC;IACzB;EACJ;EACA;EACAkL,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAACzM,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAGyM;IAAW,CAAC;IACjD,IAAI,CAACzB,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA0B,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAAC3M,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE4M,SAAS,EAAED;IAAI,CAAC;IAClD,IAAI,CAAC1B,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA4B,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAEsD,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAEsD,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,MAAMJ,SAAS,GAAG,IAAI,CAAC5M,OAAO,CAAC4M,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC7G,KAAK;EACtE;EACA;EACAkH,oBAAoBA,CAACV,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACjC,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAACyB,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACzB,eAAe,GAAGiC,QAAQ;IAC/B,IAAI,IAAI,CAACnM,WAAW,CAAC,CAAC,EAAE;MACpBmM,QAAQ,CAACxO,MAAM,CAAC,IAAI,CAAC;MACrBwO,QAAQ,CAACvO,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;EACAiN,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC1B,KAAK,CAACL,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC8D,YAAY,CAAC,CAAC,CAAC;EACvD;EACA;EACAhC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxB,KAAK,EAAE;MACb;IACJ;IACA,MAAMlL,KAAK,GAAG,IAAI,CAACkL,KAAK,CAAClL,KAAK;IAC9BA,KAAK,CAACoB,KAAK,GAAG1D,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACN,KAAK,CAAC;IACrDpB,KAAK,CAACkB,MAAM,GAAGxD,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACR,MAAM,CAAC;IACvDlB,KAAK,CAAC4O,QAAQ,GAAGlR,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACkN,QAAQ,CAAC;IAC3D5O,KAAK,CAAC6O,SAAS,GAAGnR,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACmN,SAAS,CAAC;IAC7D7O,KAAK,CAAC8O,QAAQ,GAAGpR,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACoN,QAAQ,CAAC;IAC3D9O,KAAK,CAAC+O,SAAS,GAAGrR,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACqN,SAAS,CAAC;EACjE;EACA;EACAlC,oBAAoBA,CAACmC,aAAa,EAAE;IAChC,IAAI,CAAC9D,KAAK,CAAClL,KAAK,CAACiP,aAAa,GAAGD,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACAlC,eAAeA,CAAA,EAAG;IACd,MAAMoC,YAAY,GAAG,8BAA8B;IACnD,IAAI,CAAC3D,gBAAgB,GAAG,IAAI,CAAC/L,SAAS,CAACmL,aAAa,CAAC,KAAK,CAAC;IAC3D,IAAI,CAACY,gBAAgB,CAACtL,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAC3D,IAAI,IAAI,CAACoL,mBAAmB,EAAE;MAC1B,IAAI,CAACC,gBAAgB,CAACtL,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAC9E;IACA,IAAI,IAAI,CAACwB,OAAO,CAAC0E,aAAa,EAAE;MAC5B,IAAI,CAAC2G,cAAc,CAAC,IAAI,CAACxB,gBAAgB,EAAE,IAAI,CAAC7J,OAAO,CAAC0E,aAAa,EAAE,IAAI,CAAC;IAChF;IACA;IACA;IACA,IAAI,CAAC6E,KAAK,CAACqB,aAAa,CAAC6C,YAAY,CAAC,IAAI,CAAC5D,gBAAgB,EAAE,IAAI,CAACN,KAAK,CAAC;IACxE;IACA;IACA,IAAI,CAACM,gBAAgB,CAACxC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC8C,qBAAqB,CAAC;IAC3E;IACA,IAAI,CAAC,IAAI,CAACP,mBAAmB,IAAI,OAAO8D,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAAC3N,OAAO,CAACqH,iBAAiB,CAAC,MAAM;QACjCsG,qBAAqB,CAAC,MAAM;UACxB,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;YACvB,IAAI,CAACA,gBAAgB,CAACtL,SAAS,CAACC,GAAG,CAACgP,YAAY,CAAC;UACrD;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC3D,gBAAgB,CAACtL,SAAS,CAACC,GAAG,CAACgP,YAAY,CAAC;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIzC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACxB,KAAK,CAACoE,WAAW,EAAE;MACxB,IAAI,CAACpE,KAAK,CAACqE,UAAU,CAACzE,WAAW,CAAC,IAAI,CAACI,KAAK,CAAC;IACjD;EACJ;EACA;EACAoC,cAAcA,CAAA,EAAG;IACb,MAAMkC,gBAAgB,GAAG,IAAI,CAAChE,gBAAgB;IAC9C,IAAI,CAACgE,gBAAgB,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACjE,mBAAmB,EAAE;MAC1B,IAAI,CAACS,gBAAgB,CAACwD,gBAAgB,CAAC;MACvC;IACJ;IACAA,gBAAgB,CAACtP,SAAS,CAACU,MAAM,CAAC,8BAA8B,CAAC;IACjE,IAAI,CAACc,OAAO,CAACqH,iBAAiB,CAAC,MAAM;MACjCyG,gBAAgB,CAACxG,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC+C,6BAA6B,CAAC;IAC1F,CAAC,CAAC;IACF;IACA;IACAyD,gBAAgB,CAACvP,KAAK,CAACiP,aAAa,GAAG,MAAM;IAC7C;IACA;IACA;IACA,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAAC/N,OAAO,CAACqH,iBAAiB,CAAC,MAAM2G,UAAU,CAAC,MAAM;MAC1E,IAAI,CAAC1D,gBAAgB,CAACwD,gBAAgB,CAAC;IAC3C,CAAC,EAAE,GAAG,CAAC,CAAC;EACZ;EACA;EACAxC,cAAcA,CAAC3J,OAAO,EAAEsM,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMnB,OAAO,GAAG7Q,WAAW,CAAC+R,UAAU,IAAI,EAAE,CAAC,CAACzR,MAAM,CAAC2R,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAIpB,OAAO,CAACtG,MAAM,EAAE;MAChByH,KAAK,GAAGvM,OAAO,CAACnD,SAAS,CAACC,GAAG,CAAC,GAAGsO,OAAO,CAAC,GAAGpL,OAAO,CAACnD,SAAS,CAACU,MAAM,CAAC,GAAG6N,OAAO,CAAC;IACpF;EACJ;EACA;EACAjB,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA,IAAI,CAAC9L,OAAO,CAACqH,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAM+G,YAAY,GAAG,IAAI,CAACpO,OAAO,CAACmL,QAAQ,CACrCvK,IAAI,CAAClE,SAAS,CAACS,KAAK,CAAC,IAAI,CAAC6M,YAAY,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAC5D9I,SAAS,CAAC,MAAM;QACjB;QACA;QACA,IAAI,CAAC,IAAI,CAACsI,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAAC4E,QAAQ,CAAC5H,MAAM,KAAK,CAAC,EAAE;UAChE,IAAI,IAAI,CAACgD,KAAK,IAAI,IAAI,CAACxJ,OAAO,CAACwE,UAAU,EAAE;YACvC,IAAI,CAAC6G,cAAc,CAAC,IAAI,CAAC7B,KAAK,EAAE,IAAI,CAACxJ,OAAO,CAACwE,UAAU,EAAE,KAAK,CAAC;UACnE;UACA,IAAI,IAAI,CAAC+E,KAAK,IAAI,IAAI,CAACA,KAAK,CAACqB,aAAa,EAAE;YACxC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACtB,KAAK,CAACqB,aAAa;YACnD,IAAI,CAACrB,KAAK,CAACtK,MAAM,CAAC,CAAC;UACvB;UACAkP,YAAY,CAAC5M,WAAW,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAwK,sBAAsBA,CAAA,EAAG;IACrB,MAAMxH,cAAc,GAAG,IAAI,CAAC+F,eAAe;IAC3C,IAAI/F,cAAc,EAAE;MAChBA,cAAc,CAAC9F,OAAO,CAAC,CAAC;MACxB,IAAI8F,cAAc,CAACjE,MAAM,EAAE;QACvBiE,cAAc,CAACjE,MAAM,CAAC,CAAC;MAC3B;IACJ;EACJ;EACA;EACA+J,gBAAgBA,CAACgE,QAAQ,EAAE;IACvB,IAAIA,QAAQ,EAAE;MACVA,QAAQ,CAAC/G,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC6C,qBAAqB,CAAC;MACjEkE,QAAQ,CAAC/G,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC8C,6BAA6B,CAAC;MACjFiE,QAAQ,CAACpP,MAAM,CAAC,CAAC;MACjB;MACA;MACA;MACA,IAAI,IAAI,CAAC4K,gBAAgB,KAAKwE,QAAQ,EAAE;QACpC,IAAI,CAACxE,gBAAgB,GAAG,IAAI;MAChC;IACJ;IACA,IAAI,IAAI,CAACiE,gBAAgB,EAAE;MACvBQ,YAAY,CAAC,IAAI,CAACR,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG1J,SAAS;IACrC;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAMmK,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EACpC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACApR,WAAWA,CAACqR,WAAW,EAAEpR,cAAc,EAAEM,SAAS,EAAE2J,SAAS,EAAEoH,iBAAiB,EAAE;IAC9E,IAAI,CAACrR,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC2J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoH,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,oBAAoB,GAAG;MAAEpP,KAAK,EAAE,CAAC;MAAEF,MAAM,EAAE;IAAE,CAAC;IACnD;IACA,IAAI,CAACuP,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB;IACA,IAAI,CAACV,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACW,gBAAgB,GAAG,IAAItS,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACuS,mBAAmB,GAAGtS,YAAY,CAACiN,KAAK;IAC7C;IACA,IAAI,CAACsF,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACL,gBAAgB;IAC5C,IAAI,CAACM,SAAS,CAAChB,WAAW,CAAC;EAC/B;EACA;EACA7Q,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,IAChBI,UAAU,KAAK,IAAI,CAACJ,WAAW,KAC9B,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMZ,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACiQ,kBAAkB,CAAC,CAAC;IACzBtP,UAAU,CAACmK,WAAW,CAACnM,SAAS,CAACC,GAAG,CAAC+P,gBAAgB,CAAC;IACtD,IAAI,CAACpO,WAAW,GAAGI,UAAU;IAC7B,IAAI,CAACuP,YAAY,GAAGvP,UAAU,CAACmK,WAAW;IAC1C,IAAI,CAAClB,KAAK,GAAGjJ,UAAU,CAACM,cAAc;IACtC,IAAI,CAACkP,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACV,mBAAmB,CAAChO,WAAW,CAAC,CAAC;IACtC,IAAI,CAACgO,mBAAmB,GAAG,IAAI,CAAC/R,cAAc,CAAC0S,MAAM,CAAC,CAAC,CAAChP,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAAC8O,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC3D,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAAC0D,WAAW,IAAI,CAAC,IAAI,CAACtI,SAAS,CAACoB,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACmH,gBAAgB,IAAI,IAAI,CAACb,eAAe,IAAI,IAAI,CAACc,aAAa,EAAE;MACtE,IAAI,CAACE,mBAAmB,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACpD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnH,KAAK,CAACzG,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAAC6N,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAACnG,mBAAmB,CAAC,CAAC,CAAC3F,qBAAqB,CAAC,CAAC;IAC1F,MAAM8N,UAAU,GAAG,IAAI,CAACJ,WAAW;IACnC,MAAM3N,WAAW,GAAG,IAAI,CAAC6N,YAAY;IACrC,MAAMG,YAAY,GAAG,IAAI,CAACP,aAAa;IACvC,MAAMQ,aAAa,GAAG,IAAI,CAACH,cAAc;IACzC;IACA,MAAMI,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACvC,mBAAmB,EAAE;MACtC;MACA,IAAIwC,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAErO,WAAW,EAAEoO,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAEvO,WAAW,EAAEgO,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAAC1C,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2C,cAAc,CAACR,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACQ,6BAA6B,CAACJ,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAAC5K,IAAI,CAAC;UACdwL,QAAQ,EAAEV,GAAG;UACbjM,MAAM,EAAEkM,WAAW;UACnBrO,WAAW;UACX+O,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACX,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,EAAE;QACvEd,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAES,QAAQ,EAAEV,GAAG;UAAEpO;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAIkO,YAAY,CAACxK,MAAM,EAAE;MACrB,IAAIwL,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAIlB,YAAY,EAAE;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAACnS,KAAK,GAAGwS,GAAG,CAACL,eAAe,CAACrS,MAAM,IAAI0S,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACnD,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC2C,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAAC/M,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC+J,QAAQ,EAAE;MACf;MACA,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC2C,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACO,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;EAChE;EACA7Q,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC8P,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACoC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC9C,mBAAmB,CAAChO,WAAW,CAAC,CAAC;EAC1C;EACA;EACA+J,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyE,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACD,YAAY,EAAE;MACnBwC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACxR,KAAK,EAAE;QAClCX,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRsE,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACVvC,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACT6S,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAChJ,KAAK,EAAE;MACZ,IAAI,CAAC6G,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAClQ,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACuK,WAAW,CAACnM,SAAS,CAACU,MAAM,CAACsP,gBAAgB,CAAC;IACnE;IACA,IAAI,CAACjO,MAAM,CAAC,CAAC;IACb,IAAI,CAACgP,gBAAgB,CAACtD,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC7L,WAAW,GAAG,IAAI,CAAC2P,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACII,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACJ,WAAW,IAAI,CAAC,IAAI,CAACtI,SAAS,CAACoB,SAAS,EAAE;MAC/C;IACJ;IACA,MAAM4J,YAAY,GAAG,IAAI,CAACxC,aAAa;IACvC,IAAIwC,YAAY,EAAE;MACd,IAAI,CAAChC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACnH,KAAK,CAACzG,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAACwN,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACpD,IAAI,CAACI,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAACnG,mBAAmB,CAAC,CAAC,CAAC3F,qBAAqB,CAAC,CAAC;MAC1F,MAAMoO,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACG,cAAc,EAAE6B,YAAY,CAAC;MAC7F,IAAI,CAACf,cAAc,CAACe,YAAY,EAAEtB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC9E,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIqG,wBAAwBA,CAACC,WAAW,EAAE;IAClC,IAAI,CAACtD,YAAY,GAAGsD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAAClE,SAAS,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAGD,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAACpI,OAAO,CAAC,IAAI,CAAC2J,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIgD,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC1D,eAAe,GAAG0D,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAC,sBAAsBA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAAC9D,sBAAsB,GAAG8D,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAC,iBAAiBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACjE,cAAc,GAAGiE,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAC,QAAQA,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACpE,QAAQ,GAAGoE,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkBA,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAACnE,eAAe,GAAGmE,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1D,SAASA,CAAC3K,MAAM,EAAE;IACd,IAAI,CAACsO,OAAO,GAAGtO,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIuO,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACjE,QAAQ,GAAGiE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAI,CAAChE,QAAQ,GAAGgE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACC,wBAAwB,GAAGD,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIxC,eAAeA,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAI4C,CAAC;IACL,IAAI5C,GAAG,CAAC7L,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAyO,CAAC,GAAGjD,UAAU,CAACjT,IAAI,GAAGiT,UAAU,CAACnR,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAMqU,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGnD,UAAU,CAAC3O,KAAK,GAAG2O,UAAU,CAACjT,IAAI;MACjE,MAAMqW,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GAAGnD,UAAU,CAACjT,IAAI,GAAGiT,UAAU,CAAC3O,KAAK;MAC/D4R,CAAC,GAAG5C,GAAG,CAAC7L,OAAO,IAAI,OAAO,GAAG0O,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAIlD,aAAa,CAACnT,IAAI,GAAG,CAAC,EAAE;MACxBkW,CAAC,IAAI/C,aAAa,CAACnT,IAAI;IAC3B;IACA,IAAIsW,CAAC;IACL,IAAIhD,GAAG,CAAC5L,OAAO,IAAI,QAAQ,EAAE;MACzB4O,CAAC,GAAGrD,UAAU,CAAClT,GAAG,GAAGkT,UAAU,CAACrR,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACD0U,CAAC,GAAGhD,GAAG,CAAC5L,OAAO,IAAI,KAAK,GAAGuL,UAAU,CAAClT,GAAG,GAAGkT,UAAU,CAAC9O,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAIgP,aAAa,CAACpT,GAAG,GAAG,CAAC,EAAE;MACvBuW,CAAC,IAAInD,aAAa,CAACpT,GAAG;IAC1B;IACA,OAAO;MAAEmW,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACI5C,gBAAgBA,CAACH,WAAW,EAAErO,WAAW,EAAEoO,GAAG,EAAE;IAC5C;IACA;IACA,IAAIiD,aAAa;IACjB,IAAIjD,GAAG,CAAC3L,QAAQ,IAAI,QAAQ,EAAE;MAC1B4O,aAAa,GAAG,CAACrR,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAIwR,GAAG,CAAC3L,QAAQ,KAAK,OAAO,EAAE;MAC/B4O,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAClR,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACDyU,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAClR,WAAW,CAACpD,KAAK;IAC1D;IACA,IAAI0U,aAAa;IACjB,IAAIlD,GAAG,CAAC1L,QAAQ,IAAI,QAAQ,EAAE;MAC1B4O,aAAa,GAAG,CAACtR,WAAW,CAACtD,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACD4U,aAAa,GAAGlD,GAAG,CAAC1L,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC1C,WAAW,CAACtD,MAAM;IACnE;IACA;IACA,OAAO;MACHsU,CAAC,EAAE3C,WAAW,CAAC2C,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAE/C,WAAW,CAAC+C,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACA5C,cAAcA,CAAC6C,KAAK,EAAEC,cAAc,EAAEjV,QAAQ,EAAEuS,QAAQ,EAAE;IACtD;IACA;IACA,MAAM1M,OAAO,GAAGqP,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAIlP,OAAO,GAAG,IAAI,CAACqP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIxM,OAAO,GAAG,IAAI,CAACoP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAIzM,OAAO,EAAE;MACT2O,CAAC,IAAI3O,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACT8O,CAAC,IAAI9O,OAAO;IAChB;IACA;IACA,IAAIqP,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAG5O,OAAO,CAACxF,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAIiV,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAGhP,OAAO,CAAC1F,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAIqV,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAAC5P,OAAO,CAACxF,KAAK,EAAE+U,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAAC5P,OAAO,CAAC1F,MAAM,EAAEmV,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAI7C,WAAW,GAAG8C,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACHhD,WAAW;MACXN,0BAA0B,EAAEvM,OAAO,CAACxF,KAAK,GAAGwF,OAAO,CAAC1F,MAAM,KAAKuS,WAAW;MAC1EiD,wBAAwB,EAAED,aAAa,KAAK7P,OAAO,CAAC1F,MAAM;MAC1DyV,0BAA0B,EAAEJ,YAAY,IAAI3P,OAAO,CAACxF;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiS,6BAA6BA,CAACO,GAAG,EAAEmC,KAAK,EAAEhV,QAAQ,EAAE;IAChD,IAAI,IAAI,CAAC6P,sBAAsB,EAAE;MAC7B,MAAMgG,eAAe,GAAG7V,QAAQ,CAAC0C,MAAM,GAAGsS,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAG9V,QAAQ,CAAC6C,KAAK,GAAGmS,KAAK,CAACP,CAAC;MAC/C,MAAM3G,SAAS,GAAGiI,aAAa,CAAC,IAAI,CAACjV,WAAW,CAACiM,SAAS,CAAC,CAAC,CAACe,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAGkI,aAAa,CAAC,IAAI,CAACjV,WAAW,CAACiM,SAAS,CAAC,CAAC,CAACc,QAAQ,CAAC;MACrE,MAAMmI,WAAW,GAAGnD,GAAG,CAAC8C,wBAAwB,IAAK7H,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI+H,eAAgB;MACvG,MAAMI,aAAa,GAAGpD,GAAG,CAAC+C,0BAA0B,IAAK/H,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAIiI,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,KAAK,EAAElB,cAAc,EAAEnT,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAACkR,mBAAmB,IAAI,IAAI,CAAClD,eAAe,EAAE;MAClD,OAAO;QACH2E,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACzB,mBAAmB,CAACyB,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC7B,mBAAmB,CAAC6B;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAMhP,OAAO,GAAGqP,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAMjV,QAAQ,GAAG,IAAI,CAACkR,aAAa;IACnC;IACA;IACA,MAAMkF,aAAa,GAAGrU,IAAI,CAACsU,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAG5O,OAAO,CAACxF,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAMiW,cAAc,GAAGvU,IAAI,CAACsU,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAGhP,OAAO,CAAC1F,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAMoW,WAAW,GAAGxU,IAAI,CAACsU,GAAG,CAACrW,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAG6X,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAGzU,IAAI,CAACsU,GAAG,CAACrW,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAG4X,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAI7Q,OAAO,CAACxF,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjCoW,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC1E,eAAe,GAAG/P,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAG4X,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAI5O,OAAO,CAAC1F,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnCuW,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAG/P,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAG6X,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAAC7B,mBAAmB,GAAG;MAAEyB,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIrE,cAAcA,CAACE,QAAQ,EAAET,WAAW,EAAE;IAClC,IAAI,CAAC6E,mBAAmB,CAACpE,QAAQ,CAAC;IAClC,IAAI,CAACqE,wBAAwB,CAAC9E,WAAW,EAAES,QAAQ,CAAC;IACpD,IAAI,CAACsE,qBAAqB,CAAC/E,WAAW,EAAES,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAACpN,UAAU,EAAE;MACrB,IAAI,CAAC2R,gBAAgB,CAACvE,QAAQ,CAACpN,UAAU,CAAC;IAC9C;IACA;IACA,IAAI,CAACyL,aAAa,GAAG2B,QAAQ;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACtC,gBAAgB,CAACtI,SAAS,CAACR,MAAM,EAAE;MACxC,MAAMZ,wBAAwB,GAAG,IAAI,CAACwQ,oBAAoB,CAAC,CAAC;MAC5D,MAAMC,WAAW,GAAG,IAAI3Q,8BAA8B,CAACkM,QAAQ,EAAEhM,wBAAwB,CAAC;MAC1F,IAAI,CAAC0J,gBAAgB,CAACpI,IAAI,CAACmP,WAAW,CAAC;IAC3C;IACA,IAAI,CAACrG,gBAAgB,GAAG,KAAK;EACjC;EACA;EACAgG,mBAAmBA,CAACpE,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACiC,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAMyC,QAAQ,GAAG,IAAI,CAACxG,YAAY,CAAC/G,gBAAgB,CAAC,IAAI,CAAC8K,wBAAwB,CAAC;IAClF,IAAI0C,OAAO;IACX,IAAIC,OAAO,GAAG5E,QAAQ,CAACpM,QAAQ;IAC/B,IAAIoM,QAAQ,CAACrM,QAAQ,KAAK,QAAQ,EAAE;MAChCgR,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAACvC,MAAM,CAAC,CAAC,EAAE;MACpBuC,OAAO,GAAG3E,QAAQ,CAACrM,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACDgR,OAAO,GAAG3E,QAAQ,CAACrM,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwP,QAAQ,CAAC9P,MAAM,EAAEM,CAAC,EAAE,EAAE;MACtCwP,QAAQ,CAACxP,CAAC,CAAC,CAACxI,KAAK,CAACmY,eAAe,GAAI,GAAEF,OAAQ,IAAGC,OAAQ,EAAC;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI1E,yBAAyBA,CAAC7M,MAAM,EAAE2M,QAAQ,EAAE;IACxC,MAAMvS,QAAQ,GAAG,IAAI,CAACkR,aAAa;IACnC,MAAMmG,KAAK,GAAG,IAAI,CAAC1C,MAAM,CAAC,CAAC;IAC3B,IAAIxU,MAAM,EAAE7B,GAAG,EAAEoE,MAAM;IACvB,IAAI6P,QAAQ,CAACpM,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACA7H,GAAG,GAAGsH,MAAM,CAACiP,CAAC;MACd1U,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAG7B,GAAG,GAAG,IAAI,CAACyR,eAAe;IACzD,CAAC,MACI,IAAIwC,QAAQ,CAACpM,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACAzD,MAAM,GAAG1C,QAAQ,CAACG,MAAM,GAAGyF,MAAM,CAACiP,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAG,CAAC;MAC9D5P,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGuC,MAAM,GAAG,IAAI,CAACqN,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMuH,8BAA8B,GAAGvV,IAAI,CAACwV,GAAG,CAACvX,QAAQ,CAAC0C,MAAM,GAAGkD,MAAM,CAACiP,CAAC,GAAG7U,QAAQ,CAAC1B,GAAG,EAAEsH,MAAM,CAACiP,CAAC,CAAC;MACpG,MAAM2C,cAAc,GAAG,IAAI,CAAC/H,oBAAoB,CAACtP,MAAM;MACvDA,MAAM,GAAGmX,8BAA8B,GAAG,CAAC;MAC3ChZ,GAAG,GAAGsH,MAAM,CAACiP,CAAC,GAAGyC,8BAA8B;MAC/C,IAAInX,MAAM,GAAGqX,cAAc,IAAI,CAAC,IAAI,CAAC7G,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QAC3EtR,GAAG,GAAGsH,MAAM,CAACiP,CAAC,GAAG2C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAMC,4BAA4B,GAAIlF,QAAQ,CAACrM,QAAQ,KAAK,OAAO,IAAI,CAACmR,KAAK,IAAM9E,QAAQ,CAACrM,QAAQ,KAAK,KAAK,IAAImR,KAAM;IACxH;IACA,MAAMK,2BAA2B,GAAInF,QAAQ,CAACrM,QAAQ,KAAK,KAAK,IAAI,CAACmR,KAAK,IAAM9E,QAAQ,CAACrM,QAAQ,KAAK,OAAO,IAAImR,KAAM;IACvH,IAAIhX,KAAK,EAAE9B,IAAI,EAAEsE,KAAK;IACtB,IAAI6U,2BAA2B,EAAE;MAC7B7U,KAAK,GAAG7C,QAAQ,CAACK,KAAK,GAAGuF,MAAM,CAAC6O,CAAC,GAAG,IAAI,CAAC1E,eAAe;MACxD1P,KAAK,GAAGuF,MAAM,CAAC6O,CAAC,GAAG,IAAI,CAAC1E,eAAe;IAC3C,CAAC,MACI,IAAI0H,4BAA4B,EAAE;MACnClZ,IAAI,GAAGqH,MAAM,CAAC6O,CAAC;MACfpU,KAAK,GAAGL,QAAQ,CAAC6C,KAAK,GAAG+C,MAAM,CAAC6O,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAM6C,8BAA8B,GAAGvV,IAAI,CAACwV,GAAG,CAACvX,QAAQ,CAAC6C,KAAK,GAAG+C,MAAM,CAAC6O,CAAC,GAAGzU,QAAQ,CAACzB,IAAI,EAAEqH,MAAM,CAAC6O,CAAC,CAAC;MACpG,MAAMkD,aAAa,GAAG,IAAI,CAAClI,oBAAoB,CAACpP,KAAK;MACrDA,KAAK,GAAGiX,8BAA8B,GAAG,CAAC;MAC1C/Y,IAAI,GAAGqH,MAAM,CAAC6O,CAAC,GAAG6C,8BAA8B;MAChD,IAAIjX,KAAK,GAAGsX,aAAa,IAAI,CAAC,IAAI,CAAChH,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QACzErR,IAAI,GAAGqH,MAAM,CAAC6O,CAAC,GAAGkD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAErZ,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEmE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAExC,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0W,qBAAqBA,CAACjR,MAAM,EAAE2M,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAAC7M,MAAM,EAAE2M,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;MAChD4C,eAAe,CAACrS,MAAM,GAAG4B,IAAI,CAACwV,GAAG,CAAC/E,eAAe,CAACrS,MAAM,EAAE,IAAI,CAACsP,oBAAoB,CAACtP,MAAM,CAAC;MAC3FqS,eAAe,CAACnS,KAAK,GAAG0B,IAAI,CAACwV,GAAG,CAAC/E,eAAe,CAACnS,KAAK,EAAE,IAAI,CAACoP,oBAAoB,CAACpP,KAAK,CAAC;IAC5F;IACA,MAAMuX,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC1BD,MAAM,CAACtZ,GAAG,GAAGsZ,MAAM,CAACrZ,IAAI,GAAG,GAAG;MAC9BqZ,MAAM,CAAClV,MAAM,GAAGkV,MAAM,CAAC/U,KAAK,GAAG+U,MAAM,CAAC5J,SAAS,GAAG4J,MAAM,CAAC7J,QAAQ,GAAG,EAAE;MACtE6J,MAAM,CAACvX,KAAK,GAAGuX,MAAM,CAACzX,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAM6N,SAAS,GAAG,IAAI,CAAClN,WAAW,CAACiM,SAAS,CAAC,CAAC,CAACiB,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAACjN,WAAW,CAACiM,SAAS,CAAC,CAAC,CAACgB,QAAQ;MACtD6J,MAAM,CAACzX,MAAM,GAAGxD,mBAAmB,CAAC6V,eAAe,CAACrS,MAAM,CAAC;MAC3DyX,MAAM,CAACtZ,GAAG,GAAG3B,mBAAmB,CAAC6V,eAAe,CAAClU,GAAG,CAAC;MACrDsZ,MAAM,CAAClV,MAAM,GAAG/F,mBAAmB,CAAC6V,eAAe,CAAC9P,MAAM,CAAC;MAC3DkV,MAAM,CAACvX,KAAK,GAAG1D,mBAAmB,CAAC6V,eAAe,CAACnS,KAAK,CAAC;MACzDuX,MAAM,CAACrZ,IAAI,GAAG5B,mBAAmB,CAAC6V,eAAe,CAACjU,IAAI,CAAC;MACvDqZ,MAAM,CAAC/U,KAAK,GAAGlG,mBAAmB,CAAC6V,eAAe,CAAC3P,KAAK,CAAC;MACzD;MACA,IAAI0P,QAAQ,CAACrM,QAAQ,KAAK,QAAQ,EAAE;QAChC0R,MAAM,CAAC1E,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACD0E,MAAM,CAAC1E,UAAU,GAAGX,QAAQ,CAACrM,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAIqM,QAAQ,CAACpM,QAAQ,KAAK,QAAQ,EAAE;QAChCyR,MAAM,CAACzE,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACDyE,MAAM,CAACzE,cAAc,GAAGZ,QAAQ,CAACpM,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAI6H,SAAS,EAAE;QACX4J,MAAM,CAAC5J,SAAS,GAAGrR,mBAAmB,CAACqR,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACV6J,MAAM,CAAC7J,QAAQ,GAAGpR,mBAAmB,CAACoR,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAAC0B,oBAAoB,GAAG+C,eAAe;IAC3CS,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACxR,KAAK,EAAE2Y,MAAM,CAAC;EACjD;EACA;EACA3G,uBAAuBA,CAAA,EAAG;IACtBgC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACxR,KAAK,EAAE;MAClCX,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTsE,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACXvC,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACT6S,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACAnC,0BAA0BA,CAAA,EAAG;IACzBiC,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAAClL,KAAK,EAAE;MAC3BX,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRmE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACT0P,QAAQ,EAAE,EAAE;MACZuF,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACAlB,wBAAwBA,CAAC9E,WAAW,EAAES,QAAQ,EAAE;IAC5C,MAAMqF,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMG,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAACnI,sBAAsB;IACzD,MAAM9L,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACiM,SAAS,CAAC,CAAC;IAC3C,IAAIgL,gBAAgB,EAAE;MAClB,MAAMjW,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC;MACtEiU,YAAY,CAAC2E,MAAM,EAAE,IAAI,CAACK,iBAAiB,CAAC1F,QAAQ,EAAET,WAAW,EAAEhQ,cAAc,CAAC,CAAC;MACnFmR,YAAY,CAAC2E,MAAM,EAAE,IAAI,CAACM,iBAAiB,CAAC3F,QAAQ,EAAET,WAAW,EAAEhQ,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACD8V,MAAM,CAACrF,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI4F,eAAe,GAAG,EAAE;IACxB,IAAIrS,OAAO,GAAG,IAAI,CAACqP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIxM,OAAO,GAAG,IAAI,CAACoP,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIzM,OAAO,EAAE;MACTqS,eAAe,IAAK,cAAarS,OAAQ,MAAK;IAClD;IACA,IAAIC,OAAO,EAAE;MACToS,eAAe,IAAK,cAAapS,OAAQ,KAAI;IACjD;IACA6R,MAAM,CAACE,SAAS,GAAGK,eAAe,CAACC,IAAI,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAIrU,MAAM,CAACiK,SAAS,EAAE;MAClB,IAAI+J,gBAAgB,EAAE;QAClBH,MAAM,CAAC5J,SAAS,GAAGrR,mBAAmB,CAACoH,MAAM,CAACiK,SAAS,CAAC;MAC5D,CAAC,MACI,IAAIgK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC5J,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAIjK,MAAM,CAACgK,QAAQ,EAAE;MACjB,IAAIgK,gBAAgB,EAAE;QAClBH,MAAM,CAAC7J,QAAQ,GAAGpR,mBAAmB,CAACoH,MAAM,CAACgK,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAIiK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC7J,QAAQ,GAAG,EAAE;MACxB;IACJ;IACAkF,YAAY,CAAC,IAAI,CAAC9I,KAAK,CAAClL,KAAK,EAAE2Y,MAAM,CAAC;EAC1C;EACA;EACAK,iBAAiBA,CAAC1F,QAAQ,EAAET,WAAW,EAAEhQ,cAAc,EAAE;IACrD;IACA;IACA,IAAI8V,MAAM,GAAG;MAAEtZ,GAAG,EAAE,EAAE;MAAEoE,MAAM,EAAE;IAAG,CAAC;IACpC,IAAIsP,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAExP,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAIyQ,QAAQ,CAACpM,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAMkS,cAAc,GAAG,IAAI,CAAC5Z,SAAS,CAACK,eAAe,CAACwZ,YAAY;MAClEV,MAAM,CAAClV,MAAM,GAAI,GAAE2V,cAAc,IAAIrG,YAAY,CAAC6C,CAAC,GAAG,IAAI,CAACvD,YAAY,CAACnR,MAAM,CAAE,IAAG;IACvF,CAAC,MACI;MACDyX,MAAM,CAACtZ,GAAG,GAAG3B,mBAAmB,CAACqV,YAAY,CAAC6C,CAAC,CAAC;IACpD;IACA,OAAO+C,MAAM;EACjB;EACA;EACAM,iBAAiBA,CAAC3F,QAAQ,EAAET,WAAW,EAAEhQ,cAAc,EAAE;IACrD;IACA;IACA,IAAI8V,MAAM,GAAG;MAAErZ,IAAI,EAAE,EAAE;MAAEsE,KAAK,EAAE;IAAG,CAAC;IACpC,IAAImP,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAExP,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAIyW,uBAAuB;IAC3B,IAAI,IAAI,CAAC5D,MAAM,CAAC,CAAC,EAAE;MACf4D,uBAAuB,GAAGhG,QAAQ,CAACrM,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACDqS,uBAAuB,GAAGhG,QAAQ,CAACrM,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAIqS,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAAC/Z,SAAS,CAACK,eAAe,CAAC2Z,WAAW;MAChEb,MAAM,CAAC/U,KAAK,GAAI,GAAE2V,aAAa,IAAIxG,YAAY,CAACyC,CAAC,GAAG,IAAI,CAACnD,YAAY,CAACjR,KAAK,CAAE,IAAG;IACpF,CAAC,MACI;MACDuX,MAAM,CAACrZ,IAAI,GAAG5B,mBAAmB,CAACqV,YAAY,CAACyC,CAAC,CAAC;IACrD;IACA,OAAOmD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIb,oBAAoBA,CAAA,EAAG;IACnB;IACA,MAAM2B,YAAY,GAAG,IAAI,CAACrH,cAAc,CAAC,CAAC;IAC1C,MAAMsH,aAAa,GAAG,IAAI,CAACxO,KAAK,CAACzG,qBAAqB,CAAC,CAAC;IACxD;IACA;IACA;IACA,MAAMkV,qBAAqB,GAAG,IAAI,CAAC5I,YAAY,CAAC6I,GAAG,CAACtX,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgC,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO;MACHoV,eAAe,EAAE/V,2BAA2B,CAAC2V,YAAY,EAAEE,qBAAqB,CAAC;MACjFG,mBAAmB,EAAE3W,4BAA4B,CAACsW,YAAY,EAAEE,qBAAqB,CAAC;MACtFI,gBAAgB,EAAEjW,2BAA2B,CAAC4V,aAAa,EAAEC,qBAAqB,CAAC;MACnFK,oBAAoB,EAAE7W,4BAA4B,CAACuW,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACAnD,kBAAkBA,CAACtO,MAAM,EAAE,GAAG+R,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAGrX,IAAI,CAACsU,GAAG,CAACgD,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAElS,MAAM,CAAC;EACd;EACA;EACAgK,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAM9Q,KAAK,GAAG,IAAI,CAAC5B,SAAS,CAACK,eAAe,CAAC2Z,WAAW;IACxD,MAAMtY,MAAM,GAAG,IAAI,CAAC1B,SAAS,CAACK,eAAe,CAACwZ,YAAY;IAC1D,MAAMxW,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC;IACtE,OAAO;MACHV,GAAG,EAAEwD,cAAc,CAACxD,GAAG,GAAG,IAAI,CAACyR,eAAe;MAC9CxR,IAAI,EAAEuD,cAAc,CAACvD,IAAI,GAAG,IAAI,CAACwR,eAAe;MAChDlN,KAAK,EAAEf,cAAc,CAACvD,IAAI,GAAG8B,KAAK,GAAG,IAAI,CAAC0P,eAAe;MACzDrN,MAAM,EAAEZ,cAAc,CAACxD,GAAG,GAAG6B,MAAM,GAAG,IAAI,CAAC4P,eAAe;MAC1D1P,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC0P,eAAe;MACvC5P,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC4P;IAC9B,CAAC;EACL;EACA;EACA4E,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC7T,WAAW,CAAC6M,YAAY,CAAC,CAAC,KAAK,KAAK;EACpD;EACA;EACAkK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAAChI,sBAAsB,IAAI,IAAI,CAACH,SAAS;EACzD;EACA;EACAyF,UAAUA,CAAC5C,QAAQ,EAAE+G,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAO/G,QAAQ,CAACzM,OAAO,IAAI,IAAI,GAAG,IAAI,CAACqK,QAAQ,GAAGoC,QAAQ,CAACzM,OAAO;IACtE;IACA,OAAOyM,QAAQ,CAACxM,OAAO,IAAI,IAAI,GAAG,IAAI,CAACqK,QAAQ,GAAGmC,QAAQ,CAACxM,OAAO;EACtE;EACA;EACAyK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAOrP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAACmO,mBAAmB,CAACnI,MAAM,EAAE;QAClC,MAAM5G,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAAC+O,mBAAmB,CAACiK,OAAO,CAACC,IAAI,IAAI;QACrC7S,0BAA0B,CAAC,SAAS,EAAE6S,IAAI,CAACxT,OAAO,CAAC;QACnDQ,wBAAwB,CAAC,SAAS,EAAEgT,IAAI,CAACvT,OAAO,CAAC;QACjDU,0BAA0B,CAAC,UAAU,EAAE6S,IAAI,CAACtT,QAAQ,CAAC;QACrDM,wBAAwB,CAAC,UAAU,EAAEgT,IAAI,CAACrT,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACA2Q,gBAAgBA,CAACnI,UAAU,EAAE;IACzB,IAAI,IAAI,CAACxE,KAAK,EAAE;MACZvN,WAAW,CAAC+R,UAAU,CAAC,CAAC4K,OAAO,CAACE,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACpJ,oBAAoB,CAACpJ,OAAO,CAACwS,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACpJ,oBAAoB,CAACtJ,IAAI,CAAC0S,QAAQ,CAAC;UACxC,IAAI,CAACtP,KAAK,CAACjL,SAAS,CAACC,GAAG,CAACsa,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA1I,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC5G,KAAK,EAAE;MACZ,IAAI,CAACkG,oBAAoB,CAACkJ,OAAO,CAACE,QAAQ,IAAI;QAC1C,IAAI,CAACtP,KAAK,CAACjL,SAAS,CAACU,MAAM,CAAC6Z,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACpJ,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACAgB,cAAcA,CAAA,EAAG;IACb,MAAMzL,MAAM,GAAG,IAAI,CAACsO,OAAO;IAC3B,IAAItO,MAAM,YAAY1J,UAAU,EAAE;MAC9B,OAAO0J,MAAM,CAAClE,aAAa,CAACgC,qBAAqB,CAAC,CAAC;IACvD;IACA;IACA,IAAIkC,MAAM,YAAY8T,OAAO,EAAE;MAC3B,OAAO9T,MAAM,CAAClC,qBAAqB,CAAC,CAAC;IACzC;IACA,MAAMrD,KAAK,GAAGuF,MAAM,CAACvF,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAGyF,MAAM,CAACzF,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACH7B,GAAG,EAAEsH,MAAM,CAACiP,CAAC;MACbnS,MAAM,EAAEkD,MAAM,CAACiP,CAAC,GAAG1U,MAAM;MACzB5B,IAAI,EAAEqH,MAAM,CAAC6O,CAAC;MACd5R,KAAK,EAAE+C,MAAM,CAAC6O,CAAC,GAAGpU,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAAS4S,YAAYA,CAAC0G,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIlU,GAAG,IAAIkU,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAACnU,GAAG,CAAC,EAAE;MAC5BiU,WAAW,CAACjU,GAAG,CAAC,GAAGkU,MAAM,CAAClU,GAAG,CAAC;IAClC;EACJ;EACA,OAAOiU,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS5D,aAAaA,CAAC+D,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAACpT,KAAK,EAAEqT,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC7K,cAAc,CAAC;IAClD,OAAO,CAAC4K,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAACvT,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOoT,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5E,4BAA4BA,CAACgF,UAAU,EAAE;EAC9C,OAAO;IACH5b,GAAG,EAAEyD,IAAI,CAACoY,KAAK,CAACD,UAAU,CAAC5b,GAAG,CAAC;IAC/BuE,KAAK,EAAEd,IAAI,CAACoY,KAAK,CAACD,UAAU,CAACrX,KAAK,CAAC;IACnCH,MAAM,EAAEX,IAAI,CAACoY,KAAK,CAACD,UAAU,CAACxX,MAAM,CAAC;IACrCnE,IAAI,EAAEwD,IAAI,CAACoY,KAAK,CAACD,UAAU,CAAC3b,IAAI,CAAC;IACjC8B,KAAK,EAAE0B,IAAI,CAACoY,KAAK,CAACD,UAAU,CAAC7Z,KAAK,CAAC;IACnCF,MAAM,EAAE4B,IAAI,CAACoY,KAAK,CAACD,UAAU,CAAC/Z,MAAM;EACxC,CAAC;AACL;AACA,MAAMia,iCAAiC,GAAG,CACtC;EAAEpU,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAMkU,oCAAoC,GAAG,CACzC;EAAErU,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA,MAAMmU,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBrc,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsc,YAAY,GAAG,QAAQ;IAC5B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACrK,WAAW,GAAG,KAAK;EAC5B;EACAhS,MAAMA,CAACwC,UAAU,EAAE;IACf,MAAM6C,MAAM,GAAG7C,UAAU,CAAC6L,SAAS,CAAC,CAAC;IACrC,IAAI,CAACjM,WAAW,GAAGI,UAAU;IAC7B,IAAI,IAAI,CAAC4Z,MAAM,IAAI,CAAC/W,MAAM,CAAC1D,KAAK,EAAE;MAC9Ba,UAAU,CAACiM,UAAU,CAAC;QAAE9M,KAAK,EAAE,IAAI,CAACya;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAAChX,MAAM,CAAC5D,MAAM,EAAE;MAChCe,UAAU,CAACiM,UAAU,CAAC;QAAEhN,MAAM,EAAE,IAAI,CAAC4a;MAAQ,CAAC,CAAC;IACnD;IACA7Z,UAAU,CAACmK,WAAW,CAACnM,SAAS,CAACC,GAAG,CAACmb,YAAY,CAAC;IAClD,IAAI,CAAC5J,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACIpS,GAAGA,CAACoI,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACgU,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,UAAU,GAAG/T,KAAK;IACvB,IAAI,CAACiU,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIpc,IAAIA,CAACmI,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAACmU,QAAQ,GAAGnU,KAAK;IACrB,IAAI,CAACkU,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIlY,MAAMA,CAACgE,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAAC+T,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAGhU,KAAK;IAC1B,IAAI,CAACiU,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI9X,KAAKA,CAAC6D,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACmU,QAAQ,GAAGnU,KAAK;IACrB,IAAI,CAACkU,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIzE,KAAKA,CAACzP,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACmU,QAAQ,GAAGnU,KAAK;IACrB,IAAI,CAACkU,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACtU,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACmU,QAAQ,GAAGnU,KAAK;IACrB,IAAI,CAACkU,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIva,KAAKA,CAACqG,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAAC5F,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACqM,UAAU,CAAC;QAAE9M,KAAK,EAAEqG;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACoU,MAAM,GAAGpU,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvG,MAAMA,CAACuG,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAAC5F,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACqM,UAAU,CAAC;QAAEhN,MAAM,EAAEuG;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACqU,OAAO,GAAGrU,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuU,kBAAkBA,CAAC7G,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAAC7V,IAAI,CAAC6V,MAAM,CAAC;IACjB,IAAI,CAACwG,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,gBAAgBA,CAAC9G,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAAC9V,GAAG,CAAC8V,MAAM,CAAC;IAChB,IAAI,CAACuG,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI3N,KAAKA,CAAA,EAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAClM,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACtD;IACJ;IACA,MAAM6W,MAAM,GAAG,IAAI,CAAC9W,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAMkc,YAAY,GAAG,IAAI,CAACra,WAAW,CAACuK,WAAW,CAACpM,KAAK;IACvD,MAAM8E,MAAM,GAAG,IAAI,CAACjD,WAAW,CAACiM,SAAS,CAAC,CAAC;IAC3C,MAAM;MAAE1M,KAAK;MAAEF,MAAM;MAAE4N,QAAQ;MAAEC;IAAU,CAAC,GAAGjK,MAAM;IACrD,MAAMqX,yBAAyB,GAAG,CAAC/a,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAAC0N,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAMsN,uBAAuB,GAAG,CAAClb,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAAC6N,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAMsN,SAAS,GAAG,IAAI,CAACV,UAAU;IACjC,MAAMW,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC7B,MAAMxD,KAAK,GAAG,IAAI,CAACvW,WAAW,CAACiM,SAAS,CAAC,CAAC,CAACQ,SAAS,KAAK,KAAK;IAC9D,IAAIiO,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAItI,cAAc,GAAG,EAAE;IACvB,IAAIiI,yBAAyB,EAAE;MAC3BjI,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAImI,SAAS,KAAK,QAAQ,EAAE;MAC7BnI,cAAc,GAAG,QAAQ;MACzB,IAAIkE,KAAK,EAAE;QACPoE,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAIlE,KAAK,EAAE;MACZ,IAAIiE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7CnI,cAAc,GAAG,UAAU;QAC3BqI,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrDnI,cAAc,GAAG,YAAY;QAC7BsI,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpDnI,cAAc,GAAG,YAAY;MAC7BqI,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnDnI,cAAc,GAAG,UAAU;MAC3BsI,WAAW,GAAGF,OAAO;IACzB;IACA3D,MAAM,CAACrF,QAAQ,GAAG,IAAI,CAACiI,YAAY;IACnC5C,MAAM,CAAC4D,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChE5D,MAAM,CAAC8D,SAAS,GAAGL,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACZ,UAAU;IAClE7C,MAAM,CAAC+D,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxC9C,MAAM,CAAC6D,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAAChI,cAAc,GAAGA,cAAc;IAC5CgI,YAAY,CAACjI,UAAU,GAAGmI,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACV,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACI1O,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyE,WAAW,IAAI,CAAC,IAAI,CAAC5P,WAAW,EAAE;MACvC;IACJ;IACA,MAAM8W,MAAM,GAAG,IAAI,CAAC9W,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAM2c,MAAM,GAAG,IAAI,CAAC9a,WAAW,CAACuK,WAAW;IAC3C,MAAM8P,YAAY,GAAGS,MAAM,CAAC3c,KAAK;IACjC2c,MAAM,CAAC1c,SAAS,CAACU,MAAM,CAAC0a,YAAY,CAAC;IACrCa,YAAY,CAAChI,cAAc,GACvBgI,YAAY,CAACjI,UAAU,GACnB0E,MAAM,CAAC8D,SAAS,GACZ9D,MAAM,CAAC+D,YAAY,GACf/D,MAAM,CAAC4D,UAAU,GACb5D,MAAM,CAAC6D,WAAW,GACd7D,MAAM,CAACrF,QAAQ,GACX,EAAE;IAC9B,IAAI,CAACzR,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC4P,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMmL,sBAAsB,CAAC;EACzB3d,WAAWA,CAACC,cAAc,EAAEM,SAAS,EAAE2J,SAAS,EAAEoH,iBAAiB,EAAE;IACjE,IAAI,CAACrR,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC2J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoH,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;EACIsM,MAAMA,CAAA,EAAG;IACL,OAAO,IAAIvB,sBAAsB,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIwB,mBAAmBA,CAACnW,MAAM,EAAE;IACxB,OAAO,IAAIwJ,iCAAiC,CAACxJ,MAAM,EAAE,IAAI,CAACzH,cAAc,EAAE,IAAI,CAACM,SAAS,EAAE,IAAI,CAAC2J,SAAS,EAAE,IAAI,CAACoH,iBAAiB,CAAC;EACrI;EACA;IAAS,IAAI,CAACtL,IAAI,YAAA8X,+BAAA5X,CAAA;MAAA,YAAAA,CAAA,IAAwFyX,sBAAsB,EA5hEhC/f,EAAE,CAAAuI,QAAA,CA4hEgD9I,EAAE,CAACI,aAAa,GA5hElEG,EAAE,CAAAuI,QAAA,CA4hE6ExI,QAAQ,GA5hEvFC,EAAE,CAAAuI,QAAA,CA4hEkGvH,IAAI,CAACoM,QAAQ,GA5hEjHpN,EAAE,CAAAuI,QAAA,CA4hE4H8E,gBAAgB;IAAA,CAA6C;EAAE;EAC7R;IAAS,IAAI,CAAC5E,KAAK,kBA7hE6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EA6hEYoX,sBAAsB;MAAAnX,OAAA,EAAtBmX,sBAAsB,CAAA3X,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACjK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KA/hEoGrF,EAAE,CAAA8I,iBAAA,CA+hEXiX,sBAAsB,EAAc,CAAC;IACpHhX,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEtJ,EAAE,CAACI;IAAc,CAAC,EAAE;MAAEkJ,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1FH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE/H,IAAI,CAACoM;IAAS,CAAC,EAAE;MAAErE,IAAI,EAAEsE;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAE7E;AACA,IAAI8S,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVhe,WAAWA,CAAA,CACX;EACAie,gBAAgB,EAAE3M,iBAAiB,EAAE4M,yBAAyB,EAAEC,gBAAgB,EAAEjS,mBAAmB,EAAEkS,SAAS,EAAE5b,OAAO,EAAEjC,SAAS,EAAE8d,eAAe,EAAElS,SAAS,EAAEC,uBAAuB,EAAEkS,qBAAqB,EAAE;IAC9M,IAAI,CAACL,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC3M,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC4M,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjS,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACkS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC5b,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC8d,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAClS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACkS,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAAC1Y,MAAM,EAAE;IACX,MAAM2Y,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACH,IAAI,CAAC;IAC1C,MAAMI,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;IACnD,MAAMI,aAAa,GAAG,IAAI/X,aAAa,CAAClB,MAAM,CAAC;IAC/CiZ,aAAa,CAACzP,SAAS,GAAGyP,aAAa,CAACzP,SAAS,IAAI,IAAI,CAACgP,eAAe,CAAC7V,KAAK;IAC/E,OAAO,IAAIsD,UAAU,CAAC8S,YAAY,EAAEJ,IAAI,EAAEE,IAAI,EAAEI,aAAa,EAAE,IAAI,CAACtc,OAAO,EAAE,IAAI,CAAC0J,mBAAmB,EAAE,IAAI,CAAC3L,SAAS,EAAE,IAAI,CAAC4L,SAAS,EAAE,IAAI,CAACC,uBAAuB,EAAE,IAAI,CAACkS,qBAAqB,KAAK,gBAAgB,CAAC;EACzN;EACA;AACJ;AACA;AACA;AACA;EACIjK,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC8J,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;EACIQ,kBAAkBA,CAACH,IAAI,EAAE;IACrB,MAAME,IAAI,GAAG,IAAI,CAACne,SAAS,CAACmL,aAAa,CAAC,KAAK,CAAC;IAChDgT,IAAI,CAACK,EAAE,GAAI,eAAchB,YAAY,EAAG,EAAC;IACzCW,IAAI,CAAC1d,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtCud,IAAI,CAAC5S,WAAW,CAAC8S,IAAI,CAAC;IACtB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkBA,CAAA,EAAG;IACjB,MAAMD,IAAI,GAAG,IAAI,CAACje,SAAS,CAACmL,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAAC4F,iBAAiB,CAACnG,mBAAmB,CAAC,CAAC,CAACS,WAAW,CAAC4S,IAAI,CAAC;IAC9D,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIK,mBAAmBA,CAACH,IAAI,EAAE;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACZ,SAAS,CAACa,GAAG,CAAChhB,cAAc,CAAC;IACrD;IACA,OAAO,IAAIqB,eAAe,CAACof,IAAI,EAAE,IAAI,CAACR,yBAAyB,EAAE,IAAI,CAACc,OAAO,EAAE,IAAI,CAACZ,SAAS,EAAE,IAAI,CAAC7d,SAAS,CAAC;EAClH;EACA;IAAS,IAAI,CAACyF,IAAI,YAAAkZ,gBAAAhZ,CAAA;MAAA,YAAAA,CAAA,IAAwF8X,OAAO,EA3nEjBpgB,EAAE,CAAAuI,QAAA,CA2nEiCT,qBAAqB,GA3nExD9H,EAAE,CAAAuI,QAAA,CA2nEmE8E,gBAAgB,GA3nErFrN,EAAE,CAAAuI,QAAA,CA2nEgGvI,EAAE,CAACuhB,wBAAwB,GA3nE7HvhB,EAAE,CAAAuI,QAAA,CA2nEwIwX,sBAAsB,GA3nEhK/f,EAAE,CAAAuI,QAAA,CA2nE2KgD,yBAAyB,GA3nEtMvL,EAAE,CAAAuI,QAAA,CA2nEiNvI,EAAE,CAACwhB,QAAQ,GA3nE9NxhB,EAAE,CAAAuI,QAAA,CA2nEyOvI,EAAE,CAACwI,MAAM,GA3nEpPxI,EAAE,CAAAuI,QAAA,CA2nE+PxI,QAAQ,GA3nEzQC,EAAE,CAAAuI,QAAA,CA2nEoR/G,EAAE,CAACigB,cAAc,GA3nEvSzhB,EAAE,CAAAuI,QAAA,CA2nEkTzI,EAAE,CAAC4hB,QAAQ,GA3nE/T1hB,EAAE,CAAAuI,QAAA,CA2nE0U8D,6BAA6B,GA3nEzWrM,EAAE,CAAAuI,QAAA,CA2nEoXjI,qBAAqB;IAAA,CAA6D;EAAE;EAC1iB;IAAS,IAAI,CAACmI,KAAK,kBA5nE6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EA4nEYyX,OAAO;MAAAxX,OAAA,EAAPwX,OAAO,CAAAhY,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AAClJ;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KA9nEoGrF,EAAE,CAAA8I,iBAAA,CA8nEXsX,OAAO,EAAc,CAAC;IACrGrX,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEjB;IAAsB,CAAC,EAAE;MAAEiB,IAAI,EAAEsE;IAAiB,CAAC,EAAE;MAAEtE,IAAI,EAAE/I,EAAE,CAACuhB;IAAyB,CAAC,EAAE;MAAExY,IAAI,EAAEgX;IAAuB,CAAC,EAAE;MAAEhX,IAAI,EAAEwC;IAA0B,CAAC,EAAE;MAAExC,IAAI,EAAE/I,EAAE,CAACwhB;IAAS,CAAC,EAAE;MAAEzY,IAAI,EAAE/I,EAAE,CAACwI;IAAO,CAAC,EAAE;MAAEO,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QACrRH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAEvH,EAAE,CAACigB;IAAe,CAAC,EAAE;MAAE1Y,IAAI,EAAEjJ,EAAE,CAAC4hB;IAAS,CAAC,EAAE;MAAE3Y,IAAI,EAAEsD;IAA8B,CAAC,EAAE;MAAEtD,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC/HH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAAC1I,qBAAqB;MAChC,CAAC,EAAE;QACCyI,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAMwhB,mBAAmB,GAAG,CACxB;EACIzX,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAMuX,qCAAqC,GAAG,IAAIrhB,cAAc,CAAC,uCAAuC,CAAC;AACzG;AACA;AACA;AACA;AACA,MAAMshB,gBAAgB,CAAC;EACnBzf,WAAWA,CAAA,CACX;EACA0f,UAAU,EAAE;IACR,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAAC1Z,IAAI,YAAA2Z,yBAAAzZ,CAAA;MAAA,YAAAA,CAAA,IAAwFuZ,gBAAgB,EAlrE1B7hB,EAAE,CAAAgiB,iBAAA,CAkrE0ChiB,EAAE,CAACI,UAAU;IAAA,CAA4C;EAAE;EACvM;IAAS,IAAI,CAAC6hB,IAAI,kBAnrE8EjiB,EAAE,CAAAkiB,iBAAA;MAAAnZ,IAAA,EAmrEJ8Y,gBAAgB;MAAAM,SAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA,EAA6I;EAAE;AACjQ;AACA;EAAA,QAAAhd,SAAA,oBAAAA,SAAA,KArrEoGrF,EAAE,CAAA8I,iBAAA,CAqrEX+Y,gBAAgB,EAAc,CAAC;IAC9G9Y,IAAI,EAAEvI,SAAS;IACfwI,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE,4DAA4D;MACtE2J,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtZ,IAAI,EAAE/I,EAAE,CAACI;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7E;AACA;AACA;AACA;AACA,MAAMkiB,mBAAmB,CAAC;EACtB;EACA,IAAItY,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqK,QAAQ;EACxB;EACA,IAAIrK,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACqK,QAAQ,GAAGrK,OAAO;IACvB,IAAI,IAAI,CAACuY,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAItY,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACqK,QAAQ;EACxB;EACA,IAAIrK,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACqK,QAAQ,GAAGrK,OAAO;IACvB,IAAI,IAAI,CAACsY,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAIjZ,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACmZ,YAAY;EAC5B;EACA,IAAInZ,WAAWA,CAACsB,KAAK,EAAE;IACnB,IAAI,CAAC6X,YAAY,GAAG1hB,qBAAqB,CAAC6J,KAAK,CAAC;EACpD;EACA;EACA,IAAI8X,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAAC9X,KAAK,EAAE;IACpB,IAAI,CAAC+X,aAAa,GAAG5hB,qBAAqB,CAAC6J,KAAK,CAAC;EACrD;EACA;EACA,IAAIiN,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC+K,mBAAmB;EACnC;EACA,IAAI/K,kBAAkBA,CAACjN,KAAK,EAAE;IAC1B,IAAI,CAACgY,mBAAmB,GAAG7hB,qBAAqB,CAAC6J,KAAK,CAAC;EAC3D;EACA;EACA,IAAImN,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACjE,cAAc;EAC9B;EACA,IAAIiE,aAAaA,CAACnN,KAAK,EAAE;IACrB,IAAI,CAACkJ,cAAc,GAAG/S,qBAAqB,CAAC6J,KAAK,CAAC;EACtD;EACA;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC4X,KAAK;EACrB;EACA,IAAI5X,IAAIA,CAACL,KAAK,EAAE;IACZ,IAAI,CAACiY,KAAK,GAAG9hB,qBAAqB,CAAC6J,KAAK,CAAC;EAC7C;EACA;EACAxI,WAAWA,CAAC0gB,QAAQ,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,IAAI,EAAE;IAC9E,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACT,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC7O,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC8O,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACM,qBAAqB,GAAGrhB,YAAY,CAACiN,KAAK;IAC/C,IAAI,CAACqU,mBAAmB,GAAGthB,YAAY,CAACiN,KAAK;IAC7C,IAAI,CAACsU,mBAAmB,GAAGvhB,YAAY,CAACiN,KAAK;IAC7C,IAAI,CAACuU,qBAAqB,GAAGxhB,YAAY,CAACiN,KAAK;IAC/C;IACA,IAAI,CAACwU,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAAC3S,aAAa,GAAG,IAAIrQ,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAACijB,cAAc,GAAG,IAAIjjB,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACmC,MAAM,GAAG,IAAInC,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC0E,MAAM,GAAG,IAAI1E,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACkjB,cAAc,GAAG,IAAIljB,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACmjB,mBAAmB,GAAG,IAAInjB,YAAY,CAAC,CAAC;IAC7C,IAAI,CAACojB,eAAe,GAAG,IAAIliB,cAAc,CAACohB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAACc,sBAAsB,GAAGb,qBAAqB;IACnD,IAAI,CAAC7Z,cAAc,GAAG,IAAI,CAAC0a,sBAAsB,CAAC,CAAC;EACvD;EACA;EACA,IAAI1e,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,WAAW;EAC3B;EACA;EACA,IAAIwM,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC0R,IAAI,GAAG,IAAI,CAACA,IAAI,CAACtY,KAAK,GAAG,KAAK;EAC9C;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoY,mBAAmB,CAAChd,WAAW,CAAC,CAAC;IACtC,IAAI,CAACid,mBAAmB,CAACjd,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC+c,qBAAqB,CAAC/c,WAAW,CAAC,CAAC;IACxC,IAAI,CAACkd,qBAAqB,CAACld,WAAW,CAAC,CAAC;IACxC,IAAI,IAAI,CAACpB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACmL,OAAO,CAAC,CAAC;IAC9B;EACJ;EACA4T,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACzB,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;MAC5C,IAAI,CAACvd,WAAW,CAACqM,UAAU,CAAC;QACxB9M,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBwN,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvB1N,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB2N,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAIgS,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACR,IAAI,EAAE;QAChC,IAAI,CAACjB,SAAS,CAACrR,KAAK,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI8S,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACR,IAAI,GAAG,IAAI,CAACS,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAC7D;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC5Q,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAClI,MAAM,EAAE;MAC3C,IAAI,CAACkI,SAAS,GAAGoO,mBAAmB;IACxC;IACA,MAAMvc,UAAU,GAAI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAAC8d,QAAQ,CAACnC,MAAM,CAAC,IAAI,CAACyD,YAAY,CAAC,CAAC,CAAE;IACjF,IAAI,CAAChB,mBAAmB,GAAGhe,UAAU,CAAC2L,WAAW,CAAC,CAAC,CAAChL,SAAS,CAAC,MAAM,IAAI,CAACnD,MAAM,CAACyhB,IAAI,CAAC,CAAC,CAAC;IACvF,IAAI,CAAChB,mBAAmB,GAAGje,UAAU,CAAC4L,WAAW,CAAC,CAAC,CAACjL,SAAS,CAAC,MAAM,IAAI,CAACZ,MAAM,CAACkf,IAAI,CAAC,CAAC,CAAC;IACvFjf,UAAU,CAAC0G,aAAa,CAAC,CAAC,CAAC/F,SAAS,CAAE0F,KAAK,IAAK;MAC5C,IAAI,CAACkY,cAAc,CAAC5X,IAAI,CAACN,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAAC6Y,OAAO,KAAKtiB,MAAM,IAAI,CAAC,IAAI,CAACyhB,YAAY,IAAI,CAACxhB,cAAc,CAACwJ,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAAC8Y,cAAc,CAAC,CAAC;QACtB,IAAI,CAACL,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,IAAI,CAAClf,WAAW,CAAC8H,oBAAoB,CAAC,CAAC,CAAC/G,SAAS,CAAE0F,KAAK,IAAK;MACzD,IAAI,CAACmY,mBAAmB,CAAC7X,IAAI,CAACN,KAAK,CAAC;IACxC,CAAC,CAAC;EACN;EACA;EACA2Y,YAAYA,CAAA,EAAG;IACX,MAAM/U,gBAAgB,GAAI,IAAI,CAACkT,SAAS,GACpC,IAAI,CAAClT,gBAAgB,IAAI,IAAI,CAACmV,uBAAuB,CAAC,CAAE;IAC5D,MAAMtD,aAAa,GAAG,IAAI/X,aAAa,CAAC;MACpCsI,SAAS,EAAE,IAAI,CAACyR,IAAI;MACpB7T,gBAAgB;MAChBjG,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCE,WAAW,EAAE,IAAI,CAACA;IACtB,CAAC,CAAC;IACF,IAAI,IAAI,CAAC/E,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChC2c,aAAa,CAAC3c,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClC6c,aAAa,CAAC7c,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAAC0N,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtCmP,aAAa,CAACnP,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxCkP,aAAa,CAAClP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACzI,aAAa,EAAE;MACpB2X,aAAa,CAAC3X,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjB6X,aAAa,CAAC7X,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAO6X,aAAa;EACxB;EACA;EACAsB,uBAAuBA,CAACnT,gBAAgB,EAAE;IACtC,MAAMkE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACwJ,GAAG,CAAC0H,eAAe,KAAK;MACrDva,OAAO,EAAEua,eAAe,CAACva,OAAO;MAChCC,OAAO,EAAEsa,eAAe,CAACta,OAAO;MAChCC,QAAQ,EAAEqa,eAAe,CAACra,QAAQ;MAClCC,QAAQ,EAAEoa,eAAe,CAACpa,QAAQ;MAClCL,OAAO,EAAEya,eAAe,CAACza,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAEwa,eAAe,CAACxa,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDZ,UAAU,EAAEob,eAAe,CAACpb,UAAU,IAAIJ;IAC9C,CAAC,CAAC,CAAC;IACH,OAAOoG,gBAAgB,CAClBoF,SAAS,CAAC,IAAI,CAACiQ,2CAA2C,CAAC,CAAC,CAAC,CAC7DjN,aAAa,CAAClE,SAAS,CAAC,CACxBqE,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CG,QAAQ,CAAC,IAAI,CAAC/M,IAAI,CAAC,CACnB6M,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCL,kBAAkB,CAAC,IAAI,CAAC6L,cAAc,CAAC,CACvCrL,kBAAkB,CAAC,IAAI,CAACwK,YAAY,CAAC,CACrClK,qBAAqB,CAAC,IAAI,CAACmM,uBAAuB,CAAC;EAC5D;EACA;EACAH,uBAAuBA,CAAA,EAAG;IACtB,MAAMpT,QAAQ,GAAG,IAAI,CAAC0R,QAAQ,CACzBrM,QAAQ,CAAC,CAAC,CACVwJ,mBAAmB,CAAC,IAAI,CAACyE,2CAA2C,CAAC,CAAC,CAAC;IAC5E,IAAI,CAAClC,uBAAuB,CAACpR,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACAsT,2CAA2CA,CAAA,EAAG;IAC1C,IAAI,IAAI,CAAC5a,MAAM,YAAY+X,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC/X,MAAM,CAACgY,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAChY,MAAM;IACtB;EACJ;EACA;EACAma,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACjf,WAAW,EAAE;MACnB,IAAI,CAACmf,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAACnf,WAAW,CAACiM,SAAS,CAAC,CAAC,CAAC3H,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAACtE,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACjC,IAAI,CAACD,WAAW,CAACpC,MAAM,CAAC,IAAI,CAACihB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAACva,WAAW,EAAE;MAClB,IAAI,CAAC6Z,qBAAqB,GAAG,IAAI,CAACne,WAAW,CAAC8L,aAAa,CAAC,CAAC,CAAC/K,SAAS,CAAC0F,KAAK,IAAI;QAC7E,IAAI,CAACqF,aAAa,CAACuT,IAAI,CAAC5Y,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC0X,qBAAqB,CAAC/c,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAACkd,qBAAqB,CAACld,WAAW,CAAC,CAAC;IACxC;IACA;IACA,IAAI,IAAI,CAACsd,cAAc,CAAC7X,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACiY,qBAAqB,GAAG,IAAI,CAACf,SAAS,CAAC/N,eAAe,CACtDhP,IAAI,CAACjE,SAAS,CAAC,MAAM,IAAI,CAACmiB,cAAc,CAAC7X,SAAS,CAACR,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/DtF,SAAS,CAAC0Q,QAAQ,IAAI;QACvB,IAAI,CAACiN,cAAc,CAACW,IAAI,CAAC5N,QAAQ,CAAC;QAClC,IAAI,IAAI,CAACiN,cAAc,CAAC7X,SAAS,CAACR,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAACiY,qBAAqB,CAACld,WAAW,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA8d,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAClf,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACG,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACge,qBAAqB,CAAC/c,WAAW,CAAC,CAAC;IACxC,IAAI,CAACkd,qBAAqB,CAACld,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAwc,4BAAAtc,CAAA;MAAA,YAAAA,CAAA,IAAwFga,mBAAmB,EA97E7BtiB,EAAE,CAAAgiB,iBAAA,CA87E6C5B,OAAO,GA97EtDpgB,EAAE,CAAAgiB,iBAAA,CA87EiEhiB,EAAE,CAAC6kB,WAAW,GA97EjF7kB,EAAE,CAAAgiB,iBAAA,CA87E4FhiB,EAAE,CAAC8kB,gBAAgB,GA97EjH9kB,EAAE,CAAAgiB,iBAAA,CA87E4HJ,qCAAqC,GA97EnK5hB,EAAE,CAAAgiB,iBAAA,CA87E8KxgB,EAAE,CAACigB,cAAc;IAAA,CAA4D;EAAE;EAC/V;IAAS,IAAI,CAACQ,IAAI,kBA/7E8EjiB,EAAE,CAAAkiB,iBAAA;MAAAnZ,IAAA,EA+7EJuZ,mBAAmB;MAAAH,SAAA;MAAA4C,MAAA;QAAAjb,MAAA;QAAAyJ,SAAA;QAAAlE,gBAAA;QAAArF,OAAA;QAAAC,OAAA;QAAA1F,KAAA;QAAAF,MAAA;QAAA0N,QAAA;QAAAC,SAAA;QAAAzI,aAAA;QAAAF,UAAA;QAAAka,cAAA;QAAAna,cAAA;QAAAoa,IAAA;QAAAC,YAAA;QAAAkB,uBAAA;QAAArb,WAAA;QAAAoZ,YAAA;QAAA7K,kBAAA;QAAAE,aAAA;QAAA9M,IAAA;MAAA;MAAA+Z,OAAA;QAAAlU,aAAA;QAAA4S,cAAA;QAAA9gB,MAAA;QAAAuC,MAAA;QAAAwe,cAAA;QAAAC,mBAAA;MAAA;MAAAxB,QAAA;MAAAC,UAAA;MAAA4C,QAAA,GA/7EjBjlB,EAAE,CAAAklB,oBAAA;IAAA,EA+7EsqD;EAAE;AAC9wD;AACA;EAAA,QAAA7f,SAAA,oBAAAA,SAAA,KAj8EoGrF,EAAE,CAAA8I,iBAAA,CAi8EXwZ,mBAAmB,EAAc,CAAC;IACjHvZ,IAAI,EAAEvI,SAAS;IACfwI,IAAI,EAAE,CAAC;MACCyP,QAAQ,EAAE,qEAAqE;MAC/E2J,QAAQ,EAAE,qBAAqB;MAC/BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtZ,IAAI,EAAEqX;IAAQ,CAAC,EAAE;MAAErX,IAAI,EAAE/I,EAAE,CAAC6kB;IAAY,CAAC,EAAE;MAAE9b,IAAI,EAAE/I,EAAE,CAAC8kB;IAAiB,CAAC,EAAE;MAAE/b,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC1IH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAAC4Y,qCAAqC;MAChD,CAAC;IAAE,CAAC,EAAE;MAAE7Y,IAAI,EAAEvH,EAAE,CAACigB,cAAc;MAAEvY,UAAU,EAAE,CAAC;QAC1CH,IAAI,EAAE5I;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2J,MAAM,EAAE,CAAC;MACrCf,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEuK,SAAS,EAAE,CAAC;MACZxK,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEqG,gBAAgB,EAAE,CAAC;MACnBtG,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEgB,OAAO,EAAE,CAAC;MACVjB,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEiB,OAAO,EAAE,CAAC;MACVlB,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEzE,KAAK,EAAE,CAAC;MACRwE,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE3E,MAAM,EAAE,CAAC;MACT0E,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE+I,QAAQ,EAAE,CAAC;MACXhJ,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAEgJ,SAAS,EAAE,CAAC;MACZjJ,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEO,aAAa,EAAE,CAAC;MAChBR,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEK,UAAU,EAAE,CAAC;MACbN,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEua,cAAc,EAAE,CAAC;MACjBxa,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEI,cAAc,EAAE,CAAC;MACjBL,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEwa,IAAI,EAAE,CAAC;MACPza,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEya,YAAY,EAAE,CAAC;MACf1a,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAE2b,uBAAuB,EAAE,CAAC;MAC1B5b,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEM,WAAW,EAAE,CAAC;MACdP,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,gCAAgC;IAC3C,CAAC,CAAC;IAAE0Z,YAAY,EAAE,CAAC;MACf3Z,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAE6O,kBAAkB,EAAE,CAAC;MACrB9O,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,uCAAuC;IAClD,CAAC,CAAC;IAAE+O,aAAa,EAAE,CAAC;MAChBhP,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEiC,IAAI,EAAE,CAAC;MACPlC,IAAI,EAAErI,KAAK;MACXsI,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE8H,aAAa,EAAE,CAAC;MAChB/H,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAE+iB,cAAc,EAAE,CAAC;MACjB3a,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAEiC,MAAM,EAAE,CAAC;MACTmG,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAEwE,MAAM,EAAE,CAAC;MACT4D,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAEgjB,cAAc,EAAE,CAAC;MACjB5a,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAEijB,mBAAmB,EAAE,CAAC;MACtB7a,IAAI,EAAEpI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASwkB,sDAAsDA,CAACpb,OAAO,EAAE;EACrE,OAAO,MAAMA,OAAO,CAACsW,gBAAgB,CAAClY,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAMid,8CAA8C,GAAG;EACnDC,OAAO,EAAEzD,qCAAqC;EAC9C0D,IAAI,EAAE,CAAClF,OAAO,CAAC;EACfmF,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,aAAa,CAAC;EAChB;IAAS,IAAI,CAACpd,IAAI,YAAAqd,sBAAAnd,CAAA;MAAA,YAAAA,CAAA,IAAwFkd,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAtiF8E1lB,EAAE,CAAA2lB,gBAAA;MAAA5c,IAAA,EAsiFSyc;IAAa,EAAmK;EAAE;EAC7R;IAAS,IAAI,CAACI,IAAI,kBAviF8E5lB,EAAE,CAAA6lB,gBAAA;MAAAC,SAAA,EAuiFmC,CAAC1F,OAAO,EAAEgF,8CAA8C,CAAC;MAAAW,OAAA,GAAYtkB,UAAU,EAAEG,YAAY,EAAElC,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9Q;AACA;EAAA,QAAA2F,SAAA,oBAAAA,SAAA,KAziFoGrF,EAAE,CAAA8I,iBAAA,CAyiFX0c,aAAa,EAAc,CAAC;IAC3Gzc,IAAI,EAAEnI,QAAQ;IACdoI,IAAI,EAAE,CAAC;MACC+c,OAAO,EAAE,CAACtkB,UAAU,EAAEG,YAAY,EAAElC,eAAe,EAAE4iB,mBAAmB,EAAET,gBAAgB,CAAC;MAC3FmE,OAAO,EAAE,CAAC1D,mBAAmB,EAAET,gBAAgB,EAAEniB,eAAe,CAAC;MACjEomB,SAAS,EAAE,CAAC1F,OAAO,EAAEgF,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,0BAA0B,SAAS5Y,gBAAgB,CAAC;EACtDjL,WAAWA,CAACO,SAAS,EAAEujB,QAAQ,EAAE;IAC7B,KAAK,CAACvjB,SAAS,EAAEujB,QAAQ,CAAC;EAC9B;EACAlb,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACmb,oBAAoB,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACzjB,SAAS,CAACwJ,mBAAmB,CAAC,IAAI,CAACga,oBAAoB,EAAE,IAAI,CAACC,mBAAmB,CAAC;IAC3F;EACJ;EACA5Y,gBAAgBA,CAAA,EAAG;IACf,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB,IAAI,CAAC6Y,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAACC,4BAA4B,CAAC,MAAM,IAAI,CAACD,gCAAgC,CAAC,CAAC,CAAC;EACpF;EACAA,gCAAgCA,CAAA,EAAG;IAC/B,IAAI,CAAC,IAAI,CAAC/Y,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAMiZ,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACrD,MAAM1G,MAAM,GAAGyG,iBAAiB,IAAI,IAAI,CAAC5jB,SAAS,CAACa,IAAI;IACvDsc,MAAM,CAAC9R,WAAW,CAAC,IAAI,CAACV,iBAAiB,CAAC;EAC9C;EACAgZ,4BAA4BA,CAACG,EAAE,EAAE;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACtC,IAAID,SAAS,EAAE;MACX,IAAI,IAAI,CAACN,mBAAmB,EAAE;QAC1B,IAAI,CAACzjB,SAAS,CAACwJ,mBAAmB,CAACua,SAAS,EAAE,IAAI,CAACN,mBAAmB,CAAC;MAC3E;MACA,IAAI,CAACzjB,SAAS,CAACuJ,gBAAgB,CAACwa,SAAS,EAAED,EAAE,CAAC;MAC9C,IAAI,CAACL,mBAAmB,GAAGK,EAAE;IACjC;EACJ;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACR,oBAAoB,EAAE;MAC5B,MAAMxjB,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,CAACikB,iBAAiB,EAAE;QAC7B,IAAI,CAACT,oBAAoB,GAAG,kBAAkB;MAClD,CAAC,MACI,IAAIxjB,SAAS,CAACkkB,uBAAuB,EAAE;QACxC,IAAI,CAACV,oBAAoB,GAAG,wBAAwB;MACxD,CAAC,MACI,IAAIxjB,SAAS,CAACmkB,oBAAoB,EAAE;QACrC,IAAI,CAACX,oBAAoB,GAAG,qBAAqB;MACrD,CAAC,MACI,IAAIxjB,SAAS,CAACokB,mBAAmB,EAAE;QACpC,IAAI,CAACZ,oBAAoB,GAAG,oBAAoB;MACpD;IACJ;IACA,OAAO,IAAI,CAACA,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;EACIK,oBAAoBA,CAAA,EAAG;IACnB,MAAM7jB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,OAAQA,SAAS,CAAC4jB,iBAAiB,IAC/B5jB,SAAS,CAACqkB,uBAAuB,IACjCrkB,SAAS,CAACskB,oBAAoB,IAC9BtkB,SAAS,CAACukB,mBAAmB,IAC7B,IAAI;EACZ;EACA;IAAS,IAAI,CAAC9e,IAAI,YAAA+e,mCAAA7e,CAAA;MAAA,YAAAA,CAAA,IAAwF2d,0BAA0B,EAxnFpCjmB,EAAE,CAAAuI,QAAA,CAwnFoDxI,QAAQ,GAxnF9DC,EAAE,CAAAuI,QAAA,CAwnFyEvH,IAAI,CAACoM,QAAQ;IAAA,CAA6C;EAAE;EACvO;IAAS,IAAI,CAAC3E,KAAK,kBAznF6EzI,EAAE,CAAA0I,kBAAA;MAAAC,KAAA,EAynFYsd,0BAA0B;MAAArd,OAAA,EAA1Bqd,0BAA0B,CAAA7d,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAAE;AACrK;AACA;EAAA,QAAAxD,SAAA,oBAAAA,SAAA,KA3nFoGrF,EAAE,CAAA8I,iBAAA,CA2nFXmd,0BAA0B,EAAc,CAAC;IACxHld,IAAI,EAAE9I,UAAU;IAChB+I,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEE,IAAI,EAAEE,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DH,IAAI,EAAE7I,MAAM;QACZ8I,IAAI,EAAE,CAACjJ,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEgJ,IAAI,EAAE/H,IAAI,CAACoM;IAAS,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEjD;AACA;AACA;;AAEA,SAASjL,mBAAmB,EAAEmgB,mBAAmB,EAAET,gBAAgB,EAAEnd,mBAAmB,EAAE6F,8BAA8B,EAAEV,sBAAsB,EAAEyJ,iCAAiC,EAAE2S,0BAA0B,EAAExH,sBAAsB,EAAEpY,kBAAkB,EAAE+Z,OAAO,EAAEjX,aAAa,EAAEkE,gBAAgB,EAAE9B,yBAAyB,EAAEia,aAAa,EAAEnZ,6BAA6B,EAAE0T,sBAAsB,EAAE7R,UAAU,EAAE3G,wBAAwB,EAAEgX,oCAAoC,EAAED,iCAAiC,EAAExW,qBAAqB,EAAEwC,mBAAmB,EAAEO,0BAA0B,EAAEH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}