{"ast": null, "code": "import \"../../utilities/globals/index.js\";\nexport { parseAndCheckHttpResponse } from \"./parseAndCheckHttpResponse.js\";\nexport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nexport { fallbackHttpConfig, defaultPrinter, selectHttpOptionsAndBody, selectHttpOptionsAndBodyInternal // needed by ../batch-http but not public\n} from \"./selectHttpOptionsAndBody.js\";\nexport { checkFetcher } from \"./checkFetcher.js\";\nexport { createSignalIfSupported } from \"./createSignalIfSupported.js\";\nexport { selectURI } from \"./selectURI.js\";\nexport { createHttpLink } from \"./createHttpLink.js\";\nexport { HttpLink } from \"./HttpLink.js\";\nexport { rewriteURIForGET } from \"./rewriteURIForGET.js\";", "map": {"version": 3, "names": ["parseAndCheckHttpResponse", "serializeFetchParameter", "fallbackHttpConfig", "defaultPrinter", "selectHttpOptionsAndBody", "selectHttpOptionsAndBodyInternal", "checkFetcher", "createSignalIfSupported", "selectURI", "createHttpLink", "HttpLink", "rewriteURIForGET"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/http/index.js"], "sourcesContent": ["import \"../../utilities/globals/index.js\";\nexport { parseAndCheckHttpResponse } from \"./parseAndCheckHttpResponse.js\";\nexport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nexport { fallbackHttpConfig, defaultPrinter, selectHttpOptionsAndBody, selectHttpOptionsAndBodyInternal, // needed by ../batch-http but not public\n } from \"./selectHttpOptionsAndBody.js\";\nexport { checkFetcher } from \"./checkFetcher.js\";\nexport { createSignalIfSupported } from \"./createSignalIfSupported.js\";\nexport { selectURI } from \"./selectURI.js\";\nexport { createHttpLink } from \"./createHttpLink.js\";\nexport { HttpLink } from \"./HttpLink.js\";\nexport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\n"], "mappings": "AAAA,OAAO,kCAAkC;AACzC,SAASA,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,wBAAwB,EAAEC,gCAAgC,CAAE;AAAA,OACjG,+BAA+B;AACvC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,gBAAgB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}