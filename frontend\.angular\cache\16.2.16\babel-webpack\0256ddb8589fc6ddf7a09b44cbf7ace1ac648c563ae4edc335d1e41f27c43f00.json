{"ast": null, "code": "export { default as createUploadLink } from \"./createUploadLink.js\";\nexport { default as formDataAppendFile } from \"./formDataAppendFile.js\";\nexport { default as isExtractableFile } from \"./isExtractableFile.js\";\nexport { default as ReactNativeFile } from \"./ReactNativeFile.js\";", "map": {"version": 3, "names": ["default", "createUploadLink", "formDataAppendFile", "isExtractableFile", "ReactNativeFile"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/apollo-upload-client/public/index.mjs"], "sourcesContent": ["export { default as createUploadLink } from \"./createUploadLink.js\";\nexport { default as formDataAppendFile } from \"./formDataAppendFile.js\";\nexport { default as isExtractableFile } from \"./isExtractableFile.js\";\nexport { default as ReactNativeFile } from \"./ReactNativeFile.js\";\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,SAASD,OAAO,IAAIE,kBAAkB,QAAQ,yBAAyB;AACvE,SAASF,OAAO,IAAIG,iBAAiB,QAAQ,wBAAwB;AACrE,SAASH,OAAO,IAAII,eAAe,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}