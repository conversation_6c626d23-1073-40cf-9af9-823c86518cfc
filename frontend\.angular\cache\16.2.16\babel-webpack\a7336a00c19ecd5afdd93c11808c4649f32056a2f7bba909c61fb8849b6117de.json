{"ast": null, "code": "import { map, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"src/app/services/logger.service\";\nimport * as i6 from \"@app/services/theme.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nfunction MessagesListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r6 = ctx.ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", count_r6, \" \");\n  }\n}\nfunction MessagesListComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"h3\", 41);\n    i0.ɵɵtext(5, \" Erreur de chargement des conversations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_div_39_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.loadConversations());\n    });\n    i0.ɵɵelement(9, \"i\", 44);\n    i0.ɵɵtext(10, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction MessagesListComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 48);\n    i0.ɵɵtext(4, \"Aucune conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 49);\n    i0.ɵɵtext(6, \" D\\u00E9marrez une nouvelle conversation pour communiquer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_div_40_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.startNewConversation());\n    });\n    i0.ɵɵelement(8, \"i\", 51);\n    i0.ɵɵtext(9, \" Nouvelle Conversation \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 48);\n    i0.ɵɵtext(4, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 49);\n    i0.ɵɵtext(6, \"Essayez un autre terme de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 69);\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵtext(1, \"Vous: \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conv_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", conv_r12.unreadCount, \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"futuristic-conversation-selected\": a0\n  };\n};\nfunction MessagesListComponent_ul_42_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 56);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_ul_42_li_1_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const conv_r12 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.openConversation(conv_r12.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 57)(2, \"div\", 58);\n    i0.ɵɵelement(3, \"img\", 59);\n    i0.ɵɵtemplate(4, MessagesListComponent_ul_42_li_1_div_4_Template, 1, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61)(6, \"div\", 62)(7, \"h3\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 64);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 65)(13, \"p\", 66);\n    i0.ɵɵtemplate(14, MessagesListComponent_ul_42_li_1_span_14_Template, 2, 0, \"span\", 67);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, MessagesListComponent_ul_42_li_1_div_16_Template, 2, 1, \"div\", 68);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conv_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx_r11.selectedConversationId === conv_r12.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (conv_r12.participants ? (tmp_1_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_1_0.image : null) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conv_r12.participants && ((tmp_2_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_2_0.isOnline));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (conv_r12.participants ? (tmp_3_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_3_0.username : null) || \"Utilisateur inconnu\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 8, conv_r12.lastMessage == null ? null : conv_r12.lastMessage.timestamp, \"shortTime\") || \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.sender == null ? null : conv_r12.lastMessage.sender.id) === ctx_r11.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.content) || \"Pas encore de messages\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conv_r12.unreadCount && conv_r12.unreadCount > 0);\n  }\n}\nfunction MessagesListComponent_ul_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 54);\n    i0.ɵɵtemplate(1, MessagesListComponent_ul_42_li_1_Template, 17, 13, \"li\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredConversations);\n  }\n}\nexport class MessagesListComponent {\n  constructor(MessageService, authService, router, route, toastService, logger, themeService) {\n    this.MessageService = MessageService;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.toastService = toastService;\n    this.logger = logger;\n    this.themeService = themeService;\n    this.conversations = [];\n    this.filteredConversations = [];\n    this.loading = true;\n    this.currentUserId = null;\n    this.searchQuery = '';\n    this.selectedConversationId = null;\n    this.unreadCount = new BehaviorSubject(0);\n    this.unreadCount$ = this.unreadCount.asObservable();\n    this.subscriptions = [];\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    if (!this.currentUserId) {\n      this.handleError('User not authenticated');\n      return;\n    }\n    this.loadConversations();\n    this.subscribeToUserStatus();\n    this.subscribeToConversationUpdates();\n    // Check for active conversation from route\n    this.route.firstChild?.params.subscribe(params => {\n      this.selectedConversationId = params['conversationId'] || null;\n    });\n  }\n  loadConversations() {\n    this.logger.info('MessagesList', `Loading conversations`);\n    this.loading = true;\n    this.error = null;\n    const sub = this.MessageService.getConversations().subscribe({\n      next: conversations => {\n        this.logger.info('MessagesList', `Received ${conversations.length} conversations from service`);\n        this.conversations = Array.isArray(conversations) ? [...conversations] : [];\n        this.logger.debug('MessagesList', `Filtering conversations`);\n        this.filterConversations();\n        this.logger.debug('MessagesList', `Updating unread count`);\n        this.updateUnreadCount();\n        this.logger.debug('MessagesList', `Sorting conversations`);\n        this.sortConversations();\n        this.loading = false;\n        this.logger.info('MessagesList', `Conversations loaded successfully`);\n      },\n      error: error => {\n        this.logger.error('MessagesList', `Error loading conversations:`, error);\n        this.error = error;\n        this.loading = false;\n        this.toastService.showError('Failed to load conversations');\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('MessagesList', `Conversation subscription added`);\n  }\n  filterConversations() {\n    if (!this.searchQuery) {\n      this.filteredConversations = [...this.conversations];\n      return;\n    }\n    const query = this.searchQuery.toLowerCase();\n    this.filteredConversations = this.conversations.filter(conv => {\n      const otherParticipant = conv.participants ? this.getOtherParticipant(conv.participants) : undefined;\n      return otherParticipant?.username.toLowerCase().includes(query) || conv.lastMessage?.content?.toLowerCase().includes(query) || false;\n    });\n  }\n  updateUnreadCount() {\n    const count = this.conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);\n    this.unreadCount.next(count);\n  }\n  sortConversations() {\n    this.conversations.sort((a, b) => {\n      const dateA = this.getConversationDate(a);\n      const dateB = this.getConversationDate(b);\n      return dateB.getTime() - dateA.getTime();\n    });\n    this.filterConversations();\n  }\n  getConversationDate(conv) {\n    // Utiliser une date par défaut si aucune date n'est disponible\n    const defaultDate = new Date(0); // 1970-01-01\n    if (conv.lastMessage?.timestamp) {\n      return typeof conv.lastMessage.timestamp === 'string' ? new Date(conv.lastMessage.timestamp) : conv.lastMessage.timestamp;\n    }\n    if (conv.updatedAt) {\n      return typeof conv.updatedAt === 'string' ? new Date(conv.updatedAt) : conv.updatedAt;\n    }\n    if (conv.createdAt) {\n      return typeof conv.createdAt === 'string' ? new Date(conv.createdAt) : conv.createdAt;\n    }\n    return defaultDate;\n  }\n  getOtherParticipant(participants) {\n    if (!participants || !Array.isArray(participants)) {\n      return undefined;\n    }\n    return participants.find(p => p._id !== this.currentUserId && p.id !== this.currentUserId);\n  }\n  subscribeToUserStatus() {\n    const sub = this.MessageService.subscribeToUserStatus().pipe(map(user => this.MessageService.normalizeUser(user))).subscribe({\n      next: user => {\n        if (user) {\n          this.updateUserStatus(user);\n        }\n      },\n      error: error => {\n        this.logger.error('MessagesList', 'Error in status subscription:', error);\n        this.toastService.showError('Connection to status updates lost');\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n  subscribeToConversationUpdates() {\n    const sub = this.MessageService.subscribeToConversationUpdates('global').subscribe({\n      next: updatedConv => {\n        const index = this.conversations.findIndex(c => c.id === updatedConv.id);\n        if (index >= 0) {\n          this.conversations[index] = updatedConv;\n        } else {\n          this.conversations.unshift(updatedConv);\n        }\n        this.sortConversations();\n      },\n      error: error => {\n        this.logger.error('MessagesList', 'Conversation update error:', error);\n      }\n    });\n    this.subscriptions.push(sub);\n  }\n  updateUserStatus(updatedUser) {\n    this.conversations = this.conversations.map(conv => {\n      if (!conv.participants) {\n        return conv;\n      }\n      const participants = conv.participants.map(p => {\n        const userIdMatches = p._id === updatedUser._id || p.id === updatedUser._id;\n        return userIdMatches ? {\n          ...p,\n          isOnline: updatedUser.isOnline,\n          lastActive: updatedUser.lastActive\n        } : p;\n      });\n      return {\n        ...conv,\n        participants\n      };\n    });\n    this.filterConversations();\n  }\n  openConversation(conversationId) {\n    if (!conversationId) {\n      this.logger.error('MessagesList', 'Cannot open conversation: conversationId is undefined');\n      return;\n    }\n    this.logger.info('MessagesList', `Opening conversation: ${conversationId}`);\n    this.selectedConversationId = conversationId;\n    // Trouver la conversation pour les logs\n    const conversation = this.conversations.find(c => c.id === conversationId);\n    if (conversation) {\n      const otherParticipant = conversation.participants ? this.getOtherParticipant(conversation.participants) : undefined;\n      this.logger.debug('MessagesList', `Conversation with: ${otherParticipant?.username || 'Unknown'}`);\n      this.logger.debug('MessagesList', `Unread messages: ${conversation.unreadCount || 0}`);\n    }\n    this.logger.debug('MessagesList', `Navigating to chat route with conversationId: ${conversationId}`);\n    this.router.navigate(['chat', conversationId], {\n      relativeTo: this.route\n    });\n  }\n  startNewConversation() {\n    this.logger.info('MessagesList', 'Starting new conversation, navigating to users list');\n    this.router.navigate(['/messages/users']);\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } else {\n      return `Active ${lastActiveDate.toLocaleDateString()}`;\n    }\n  }\n  handleError(message, error) {\n    this.error = message;\n    this.loading = false;\n    this.logger.error('MessagesList', message, error);\n    this.toastService.showError(message);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function MessagesListComponent_Factory(t) {\n      return new (t || MessagesListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.LoggerService), i0.ɵɵdirectiveInject(i6.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessagesListComponent,\n      selectors: [[\"app-messages-list\"]],\n      decls: 45,\n      vars: 13,\n      consts: [[1, \"flex\", \"h-screen\", \"futuristic-messages-page\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"md:w-80\", \"lg:w-96\", \"futuristic-sidebar\", \"flex\", \"flex-col\", \"relative\", \"z-10\", \"backdrop-blur-sm\"], [1, \"futuristic-header\", \"sticky\", \"top-0\", \"z-10\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"futuristic-title\", \"text-xl\", \"font-bold\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Nouvelle conversation\", 1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-full\", \"blur-md\"], [1, \"fas\", \"fa-edit\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\"], [\"class\", \"relative\", 4, \"ngIf\"], [1, \"relative\", \"group\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des conversations...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-search\", \"text-[#bdc6cc]\", \"dark:text-[#6d6870]\", \"group-focus-within:text-[#4f5fad]\", \"dark:group-focus-within:text-[#6d78c9]\", \"transition-colors\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"flex-1\", \"overflow-y-auto\", \"futuristic-conversations-list\"], [\"class\", \"flex flex-col items-center justify-center h-full p-4\", 4, \"ngIf\"], [\"class\", \"futuristic-error-container\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-no-results\", 4, \"ngIf\"], [\"class\", \"futuristic-conversations\", 4, \"ngIf\"], [1, \"flex-1\", \"hidden\", \"md:flex\", \"flex-col\", \"futuristic-main-area\"], [1, \"relative\"], [1, \"futuristic-badge\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/30\", \"dark:bg-[#6d78c9]/30\", \"rounded-full\", \"blur-md\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"h-full\", \"p-4\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-container\"], [1, \"futuristic-error-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-message\"], [1, \"futuristic-retry-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-comments\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-start-button\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"futuristic-no-results\"], [1, \"fas\", \"fa-search\"], [1, \"futuristic-conversations\"], [\"class\", \"futuristic-conversation-item\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-conversation-item\", 3, \"ngClass\", \"click\"], [1, \"flex\", \"items-center\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"futuristic-online-indicator\", 4, \"ngIf\"], [1, \"futuristic-conversation-details\"], [1, \"futuristic-conversation-header\"], [1, \"futuristic-conversation-name\"], [1, \"futuristic-conversation-time\"], [1, \"futuristic-conversation-preview\"], [1, \"futuristic-conversation-message\"], [\"class\", \"futuristic-you-prefix\", 4, \"ngIf\"], [\"class\", \"futuristic-unread-badge\", 4, \"ngIf\"], [1, \"futuristic-online-indicator\"], [1, \"futuristic-you-prefix\"], [1, \"futuristic-unread-badge\"]],\n      template: function MessagesListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelement(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6)(17, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8);\n          i0.ɵɵelement(20, \"div\", 9)(21, \"div\", 10);\n          i0.ɵɵelementStart(22, \"div\", 11)(23, \"h1\", 12);\n          i0.ɵɵtext(24, \"Messages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 13)(26, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function MessagesListComponent_Template_button_click_26_listener() {\n            return ctx.startNewConversation();\n          });\n          i0.ɵɵelement(27, \"div\", 15)(28, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, MessagesListComponent_div_29_Template, 4, 1, \"div\", 17);\n          i0.ɵɵpipe(30, \"async\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function MessagesListComponent_Template_input_ngModelChange_32_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function MessagesListComponent_Template_input_ngModelChange_32_listener() {\n            return ctx.filterConversations();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 20);\n          i0.ɵɵelement(34, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 22);\n          i0.ɵɵelement(36, \"div\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 24);\n          i0.ɵɵtemplate(38, MessagesListComponent_div_38_Template, 4, 0, \"div\", 25);\n          i0.ɵɵtemplate(39, MessagesListComponent_div_39_Template, 11, 1, \"div\", 26);\n          i0.ɵɵtemplate(40, MessagesListComponent_div_40_Template, 10, 0, \"div\", 27);\n          i0.ɵɵtemplate(41, MessagesListComponent_div_41_Template, 7, 0, \"div\", 28);\n          i0.ɵɵtemplate(42, MessagesListComponent_ul_42_Template, 2, 1, \"ul\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 30);\n          i0.ɵɵelement(44, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 9, ctx.isDarkMode$));\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(30, 11, ctx.unreadCount$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length === 0 && !ctx.searchQuery);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length === 0 && ctx.searchQuery);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length > 0);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i3.RouterOutlet, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i7.AsyncPipe, i7.DatePipe],\n      styles: [\"\\n\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%] {\\n  background-color: #f0f4f8;\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-color: rgba(79, 95, 173, 0.2);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-color: rgba(0, 247, 255, 0.2);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.2);\\n  padding: 15px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%] {\\n  background-color: var(--medium-bg);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n  padding: 15px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  color: white;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: 9999px;\\n  padding: 2px 8px;\\n  min-width: 1.5rem;\\n  text-align: center;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%] {\\n  background: var(--primary-gradient);\\n  color: white;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border-radius: 9999px;\\n  padding: 2px 8px;\\n  min-width: 1.5rem;\\n  text-align: center;\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: #4f5fad #f0f4f8;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 5px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f0f4f8;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #4f5fad;\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--accent-color) var(--medium-bg);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 5px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--medium-bg);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 3px solid rgba(79, 95, 173, 0.1);\\n  border-top-color: #4f5fad;\\n  animation: _ngcontent-%COMP%_spin-light 1.5s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin-light {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  font-size: 0.875rem;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 3px solid rgba(0, 247, 255, 0.1);\\n  border-top-color: var(--accent-color);\\n  animation: _ngcontent-%COMP%_spin-dark 1.5s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin-dark {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n  font-size: 0.875rem;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%] {\\n  margin: 15px;\\n  padding: 15px;\\n  background: rgba(255, 107, 105, 0.1);\\n  border-left: 3px solid #ff6b69;\\n  border-radius: 5px;\\n  display: flex;\\n  align-items: flex-start;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%] {\\n  color: #ff6b69;\\n  font-size: 1.25rem;\\n  margin-right: 15px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%] {\\n  color: #ff6b69;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%] {\\n  color: #6d6870;\\n  font-size: 0.8125rem;\\n  margin-bottom: 10px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%] {\\n  background: rgba(255, 107, 105, 0.2);\\n  color: #ff6b69;\\n  border: none;\\n  border-radius: 5px;\\n  padding: 5px 10px;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 107, 105, 0.3);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%] {\\n  margin: 15px;\\n  padding: 15px;\\n  background: rgba(255, 0, 0, 0.1);\\n  border-left: 3px solid #ff3b30;\\n  border-radius: 5px;\\n  display: flex;\\n  align-items: flex-start;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%] {\\n  color: #ff3b30;\\n  font-size: 1.25rem;\\n  margin-right: 15px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%] {\\n  color: #ff3b30;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.8125rem;\\n  margin-bottom: 10px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%] {\\n  background: rgba(255, 0, 0, 0.2);\\n  color: #ff3b30;\\n  border: none;\\n  border-radius: 5px;\\n  padding: 5px 10px;\\n  font-size: 0.75rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 0, 0, 0.3);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%] {\\n  color: #6d6870;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: #4f5fad;\\n  margin-bottom: 20px;\\n  opacity: 0.7;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin-bottom: 5px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: #6d6870;\\n  font-size: 0.875rem;\\n  margin-bottom: 20px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: all var(--transition-fast);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: var(--accent-color);\\n  margin-bottom: 20px;\\n  opacity: 0.7;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin-bottom: 5px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  margin-bottom: 20px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%] {\\n  background: var(--primary-gradient);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: all var(--transition-fast);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 15px;\\n  cursor: pointer;\\n  transition: background-color var(--transition-fast);\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.05);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.05);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.1) !important;\\n  border-left: 3px solid #4f5fad;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 2px solid rgba(79, 95, 173, 0.3);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.2);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  background: #4caf50;\\n  border: 2px solid #ffffff;\\n  box-shadow: 0 0 5px rgba(76, 175, 80, 0.8);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 15px;\\n  cursor: pointer;\\n  transition: background-color var(--transition-fast);\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.05);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.05);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1) !important;\\n  border-left: 3px solid var(--accent-color);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  background: #00ff9d;\\n  border: 2px solid var(--medium-bg);\\n  box-shadow: 0 0 5px rgba(0, 255, 157, 0.8);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: baseline;\\n  margin-bottom: 4px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%] {\\n  font-size: 0.9375rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6d6870;\\n  white-space: nowrap;\\n  margin-left: 8px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%] {\\n  font-size: 0.8125rem;\\n  color: #6d6870;\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%] {\\n  color: #4f5fad;\\n  font-weight: 500;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  color: white;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 8px;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: baseline;\\n  margin-bottom: 4px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%] {\\n  font-size: 0.9375rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-dim);\\n  white-space: nowrap;\\n  margin-left: 8px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%] {\\n  font-size: 0.8125rem;\\n  color: var(--text-dim);\\n  margin: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%] {\\n  color: rgba(0, 247, 255, 0.7);\\n  font-weight: 500;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%] {\\n  background: var(--accent-color);\\n  color: var(--dark-bg);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-left: 8px;\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%] {\\n  background-color: #f0f4f8;\\n  position: relative;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(\\n      rgba(79, 95, 173, 0.03) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(90deg, rgba(79, 95, 173, 0.03) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  position: relative;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(\\n      rgba(0, 247, 255, 0.03) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BehaviorSubject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "count_r6", "ɵɵlistener", "MessagesListComponent_div_39_Template_button_click_8_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "loadConversations", "ctx_r2", "error", "MessagesListComponent_div_40_Template_button_click_7_listener", "_r10", "ctx_r9", "startNewConversation", "conv_r12", "unreadCount", "MessagesListComponent_ul_42_li_1_Template_li_click_0_listener", "restoredCtx", "_r18", "$implicit", "ctx_r17", "openConversation", "id", "ɵɵtemplate", "MessagesListComponent_ul_42_li_1_div_4_Template", "MessagesListComponent_ul_42_li_1_span_14_Template", "MessagesListComponent_ul_42_li_1_div_16_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r11", "selectedConversationId", "participants", "tmp_1_0", "getOtherParticipant", "image", "ɵɵsanitizeUrl", "tmp_2_0", "isOnline", "tmp_3_0", "username", "ɵɵpipeBind2", "lastMessage", "timestamp", "sender", "currentUserId", "content", "MessagesListComponent_ul_42_li_1_Template", "ctx_r5", "filteredConversations", "MessagesListComponent", "constructor", "MessageService", "authService", "router", "route", "toastService", "logger", "themeService", "conversations", "loading", "searchQuery", "unreadCount$", "asObservable", "subscriptions", "isDarkMode$", "darkMode$", "ngOnInit", "getCurrentUserId", "handleError", "subscribeToUserStatus", "subscribeToConversationUpdates", "<PERSON><PERSON><PERSON><PERSON>", "params", "subscribe", "info", "sub", "getConversations", "next", "length", "Array", "isArray", "debug", "filterConversations", "updateUnreadCount", "sortConversations", "showError", "push", "query", "toLowerCase", "filter", "conv", "otherParticipant", "undefined", "includes", "count", "reduce", "sum", "sort", "a", "b", "dateA", "getConversationDate", "dateB", "getTime", "defaultDate", "Date", "updatedAt", "createdAt", "find", "p", "_id", "pipe", "user", "normalizeUser", "updateUserStatus", "updatedConv", "index", "findIndex", "c", "unshift", "updatedUser", "userIdMatches", "lastActive", "conversationId", "conversation", "navigate", "relativeTo", "formatLastActive", "lastActiveDate", "now", "diffHours", "Math", "abs", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "message", "ngOnDestroy", "for<PERSON>ach", "unsubscribe", "ɵɵdirectiveInject", "i1", "i2", "AuthuserService", "i3", "Router", "ActivatedRoute", "i4", "ToastService", "i5", "LoggerService", "i6", "ThemeService", "selectors", "decls", "vars", "consts", "template", "MessagesListComponent_Template", "rf", "ctx", "MessagesListComponent_Template_button_click_26_listener", "MessagesListComponent_div_29_Template", "MessagesListComponent_Template_input_ngModelChange_32_listener", "$event", "MessagesListComponent_div_38_Template", "MessagesListComponent_div_39_Template", "MessagesListComponent_div_40_Template", "MessagesListComponent_div_41_Template", "MessagesListComponent_ul_42_Template", "ɵɵclassProp", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-list\\messages-list.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-list\\messages-list.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { map, Subscription, BehaviorSubject, Observable } from 'rxjs';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { Conversation, Message } from 'src/app/models/message.model';\r\nimport { User } from '@app/models/user.model';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\nimport { MessageService } from '@app/services/message.service';\r\nimport { LoggerService } from 'src/app/services/logger.service';\r\nimport { ThemeService } from '@app/services/theme.service';\r\n@Component({\r\n  selector: 'app-messages-list',\r\n  templateUrl: './messages-list.component.html',\r\n  styleUrls: ['./messages-list.component.css'],\r\n})\r\nexport class MessagesListComponent implements OnInit, OnDestroy {\r\n  conversations: Conversation[] = [];\r\n  filteredConversations: Conversation[] = [];\r\n  loading = true;\r\n  error: any;\r\n  currentUserId: string | null = null;\r\n  searchQuery = '';\r\n  selectedConversationId: string | null = null;\r\n  isDarkMode$: Observable<boolean>;\r\n\r\n  private unreadCount = new BehaviorSubject<number>(0);\r\n  public unreadCount$ = this.unreadCount.asObservable();\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private MessageService: MessageService,\r\n    private authService: AuthuserService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toastService: ToastService,\r\n    private logger: LoggerService,\r\n    private themeService: ThemeService\r\n  ) {\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.currentUserId = this.authService.getCurrentUserId();\r\n    if (!this.currentUserId) {\r\n      this.handleError('User not authenticated');\r\n      return;\r\n    }\r\n\r\n    this.loadConversations();\r\n    this.subscribeToUserStatus();\r\n    this.subscribeToConversationUpdates();\r\n\r\n    // Check for active conversation from route\r\n    this.route.firstChild?.params.subscribe((params) => {\r\n      this.selectedConversationId = params['conversationId'] || null;\r\n    });\r\n  }\r\n\r\n  loadConversations(): void {\r\n    this.logger.info('MessagesList', `Loading conversations`);\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    const sub = this.MessageService.getConversations().subscribe({\r\n      next: (conversations) => {\r\n        this.logger.info(\r\n          'MessagesList',\r\n          `Received ${conversations.length} conversations from service`\r\n        );\r\n        this.conversations = Array.isArray(conversations)\r\n          ? [...conversations]\r\n          : [];\r\n\r\n        this.logger.debug('MessagesList', `Filtering conversations`);\r\n        this.filterConversations();\r\n\r\n        this.logger.debug('MessagesList', `Updating unread count`);\r\n        this.updateUnreadCount();\r\n\r\n        this.logger.debug('MessagesList', `Sorting conversations`);\r\n        this.sortConversations();\r\n\r\n        this.loading = false;\r\n        this.logger.info('MessagesList', `Conversations loaded successfully`);\r\n      },\r\n      error: (error) => {\r\n        this.logger.error(\r\n          'MessagesList',\r\n          `Error loading conversations:`,\r\n          error\r\n        );\r\n        this.error = error;\r\n        this.loading = false;\r\n        this.toastService.showError('Failed to load conversations');\r\n      },\r\n    });\r\n    this.subscriptions.push(sub);\r\n    this.logger.debug('MessagesList', `Conversation subscription added`);\r\n  }\r\n\r\n  filterConversations(): void {\r\n    if (!this.searchQuery) {\r\n      this.filteredConversations = [...this.conversations];\r\n      return;\r\n    }\r\n\r\n    const query = this.searchQuery.toLowerCase();\r\n    this.filteredConversations = this.conversations.filter((conv) => {\r\n      const otherParticipant = conv.participants\r\n        ? this.getOtherParticipant(conv.participants)\r\n        : undefined;\r\n      return (\r\n        otherParticipant?.username.toLowerCase().includes(query) ||\r\n        conv.lastMessage?.content?.toLowerCase().includes(query) ||\r\n        false\r\n      );\r\n    });\r\n  }\r\n\r\n  private updateUnreadCount(): void {\r\n    const count = this.conversations.reduce(\r\n      (sum, conv) => sum + (conv.unreadCount || 0),\r\n      0\r\n    );\r\n    this.unreadCount.next(count);\r\n  }\r\n\r\n  sortConversations(): void {\r\n    this.conversations.sort((a, b) => {\r\n      const dateA = this.getConversationDate(a);\r\n      const dateB = this.getConversationDate(b);\r\n      return dateB.getTime() - dateA.getTime();\r\n    });\r\n    this.filterConversations();\r\n  }\r\n\r\n  private getConversationDate(conv: Conversation): Date {\r\n    // Utiliser une date par défaut si aucune date n'est disponible\r\n    const defaultDate = new Date(0); // 1970-01-01\r\n\r\n    if (conv.lastMessage?.timestamp) {\r\n      return typeof conv.lastMessage.timestamp === 'string'\r\n        ? new Date(conv.lastMessage.timestamp)\r\n        : conv.lastMessage.timestamp;\r\n    }\r\n\r\n    if (conv.updatedAt) {\r\n      return typeof conv.updatedAt === 'string'\r\n        ? new Date(conv.updatedAt)\r\n        : conv.updatedAt;\r\n    }\r\n\r\n    if (conv.createdAt) {\r\n      return typeof conv.createdAt === 'string'\r\n        ? new Date(conv.createdAt)\r\n        : conv.createdAt;\r\n    }\r\n\r\n    return defaultDate;\r\n  }\r\n\r\n  getOtherParticipant(participants: User[] | undefined): User | undefined {\r\n    if (!participants || !Array.isArray(participants)) {\r\n      return undefined;\r\n    }\r\n    return participants.find(\r\n      (p) => p._id !== this.currentUserId && p.id !== this.currentUserId\r\n    );\r\n  }\r\n\r\n  subscribeToUserStatus(): void {\r\n    const sub = this.MessageService.subscribeToUserStatus()\r\n      .pipe(map((user) => this.MessageService.normalizeUser(user)))\r\n      .subscribe({\r\n        next: (user: User) => {\r\n          if (user) {\r\n            this.updateUserStatus(user);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.logger.error(\r\n            'MessagesList',\r\n            'Error in status subscription:',\r\n            error\r\n          );\r\n          this.toastService.showError('Connection to status updates lost');\r\n        },\r\n      });\r\n    this.subscriptions.push(sub);\r\n  }\r\n\r\n  subscribeToConversationUpdates(): void {\r\n    const sub = this.MessageService.subscribeToConversationUpdates(\r\n      'global'\r\n    ).subscribe({\r\n      next: (updatedConv) => {\r\n        const index = this.conversations.findIndex(\r\n          (c) => c.id === updatedConv.id\r\n        );\r\n        if (index >= 0) {\r\n          this.conversations[index] = updatedConv;\r\n        } else {\r\n          this.conversations.unshift(updatedConv);\r\n        }\r\n        this.sortConversations();\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('MessagesList', 'Conversation update error:', error);\r\n      },\r\n    });\r\n    this.subscriptions.push(sub);\r\n  }\r\n\r\n  updateUserStatus(updatedUser: User): void {\r\n    this.conversations = this.conversations.map((conv) => {\r\n      if (!conv.participants) {\r\n        return conv;\r\n      }\r\n      const participants = conv.participants.map((p) => {\r\n        const userIdMatches =\r\n          p._id === updatedUser._id || p.id === updatedUser._id;\r\n        return userIdMatches\r\n          ? {\r\n              ...p,\r\n              isOnline: updatedUser.isOnline,\r\n              lastActive: updatedUser.lastActive,\r\n            }\r\n          : p;\r\n      });\r\n      return { ...conv, participants };\r\n    });\r\n    this.filterConversations();\r\n  }\r\n\r\n  openConversation(conversationId: string | undefined): void {\r\n    if (!conversationId) {\r\n      this.logger.error(\r\n        'MessagesList',\r\n        'Cannot open conversation: conversationId is undefined'\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.logger.info('MessagesList', `Opening conversation: ${conversationId}`);\r\n    this.selectedConversationId = conversationId;\r\n\r\n    // Trouver la conversation pour les logs\r\n    const conversation = this.conversations.find(\r\n      (c) => c.id === conversationId\r\n    );\r\n    if (conversation) {\r\n      const otherParticipant = conversation.participants\r\n        ? this.getOtherParticipant(conversation.participants)\r\n        : undefined;\r\n      this.logger.debug(\r\n        'MessagesList',\r\n        `Conversation with: ${otherParticipant?.username || 'Unknown'}`\r\n      );\r\n      this.logger.debug(\r\n        'MessagesList',\r\n        `Unread messages: ${conversation.unreadCount || 0}`\r\n      );\r\n    }\r\n\r\n    this.logger.debug(\r\n      'MessagesList',\r\n      `Navigating to chat route with conversationId: ${conversationId}`\r\n    );\r\n    this.router.navigate(['chat', conversationId], { relativeTo: this.route });\r\n  }\r\n\r\n  startNewConversation(): void {\r\n    this.logger.info(\r\n      'MessagesList',\r\n      'Starting new conversation, navigating to users list'\r\n    );\r\n    this.router.navigate(['/messages/users']);\r\n  }\r\n\r\n  formatLastActive(lastActive: string): string {\r\n    if (!lastActive) return 'Offline';\r\n    const lastActiveDate = new Date(lastActive);\r\n    const now = new Date();\r\n    const diffHours =\r\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\r\n\r\n    if (diffHours < 24) {\r\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      })}`;\r\n    } else {\r\n      return `Active ${lastActiveDate.toLocaleDateString()}`;\r\n    }\r\n  }\r\n\r\n  private handleError(message: string, error?: any): void {\r\n    this.error = message;\r\n    this.loading = false;\r\n    this.logger.error('MessagesList', message, error);\r\n    this.toastService.showError(message);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n  }\r\n}\r\n", "<div\r\n  class=\"flex h-screen futuristic-messages-page relative\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Sidebar -->\r\n  <div\r\n    class=\"w-full md:w-80 lg:w-96 futuristic-sidebar flex flex-col relative z-10 backdrop-blur-sm\"\r\n  >\r\n    <!-- Header -->\r\n    <div class=\"futuristic-header sticky top-0 z-10 backdrop-blur-sm\">\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <div class=\"flex justify-between items-center mb-4\">\r\n        <h1 class=\"futuristic-title text-xl font-bold\">Messages</h1>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <button\r\n            (click)=\"startNewConversation()\"\r\n            class=\"p-2 rounded-full text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-all relative group overflow-hidden\"\r\n            title=\"Nouvelle conversation\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md\"\r\n            ></div>\r\n            <i\r\n              class=\"fas fa-edit relative z-10 group-hover:scale-110 transition-transform\"\r\n            ></i>\r\n          </button>\r\n          <div *ngIf=\"unreadCount$ | async as count\" class=\"relative\">\r\n            <span class=\"futuristic-badge\">\r\n              {{ count }}\r\n            </span>\r\n            <!-- Glow effect -->\r\n            <div\r\n              class=\"absolute inset-0 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30 rounded-full blur-md transform scale-150 -z-10\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Recherche -->\r\n      <div class=\"relative group\">\r\n        <input\r\n          [(ngModel)]=\"searchQuery\"\r\n          (ngModelChange)=\"filterConversations()\"\r\n          type=\"text\"\r\n          placeholder=\"Rechercher des conversations...\"\r\n          class=\"w-full pl-10 pr-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n        />\r\n        <div\r\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n        >\r\n          <i\r\n            class=\"fas fa-search text-[#bdc6cc] dark:text-[#6d6870] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\r\n          ></i>\r\n        </div>\r\n        <div\r\n          class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n        >\r\n          <div\r\n            class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Liste des conversations -->\r\n    <div class=\"flex-1 overflow-y-auto futuristic-conversations-list\">\r\n      <!-- État de chargement -->\r\n      <div\r\n        *ngIf=\"loading\"\r\n        class=\"flex flex-col items-center justify-center h-full p-4\"\r\n      >\r\n        <div class=\"futuristic-loading-circle\"></div>\r\n        <p class=\"futuristic-loading-text\">Chargement des conversations...</p>\r\n      </div>\r\n\r\n      <!-- État d'erreur -->\r\n      <div *ngIf=\"error\" class=\"futuristic-error-container\">\r\n        <div class=\"futuristic-error-icon\">\r\n          <i class=\"fas fa-exclamation-triangle\"></i>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <h3 class=\"futuristic-error-title\">\r\n            Erreur de chargement des conversations\r\n          </h3>\r\n          <p class=\"futuristic-error-message\">\r\n            {{ error }}\r\n          </p>\r\n          <button (click)=\"loadConversations()\" class=\"futuristic-retry-button\">\r\n            <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- État vide -->\r\n      <div\r\n        *ngIf=\"!loading && filteredConversations.length === 0 && !searchQuery\"\r\n        class=\"futuristic-empty-state\"\r\n      >\r\n        <div class=\"futuristic-empty-icon\">\r\n          <i class=\"fas fa-comments\"></i>\r\n        </div>\r\n\r\n        <h3 class=\"futuristic-empty-title\">Aucune conversation</h3>\r\n\r\n        <p class=\"futuristic-empty-text\">\r\n          Démarrez une nouvelle conversation pour communiquer\r\n        </p>\r\n\r\n        <button\r\n          (click)=\"startNewConversation()\"\r\n          class=\"futuristic-start-button\"\r\n        >\r\n          <i class=\"fas fa-plus-circle mr-2\"></i>\r\n          Nouvelle Conversation\r\n        </button>\r\n      </div>\r\n\r\n      <!-- État sans résultats -->\r\n      <div\r\n        *ngIf=\"!loading && filteredConversations.length === 0 && searchQuery\"\r\n        class=\"futuristic-no-results\"\r\n      >\r\n        <div class=\"futuristic-empty-icon\">\r\n          <i class=\"fas fa-search\"></i>\r\n        </div>\r\n\r\n        <h3 class=\"futuristic-empty-title\">Aucun résultat trouvé</h3>\r\n\r\n        <p class=\"futuristic-empty-text\">Essayez un autre terme de recherche</p>\r\n      </div>\r\n\r\n      <!-- Conversations -->\r\n      <ul\r\n        *ngIf=\"!loading && filteredConversations.length > 0\"\r\n        class=\"futuristic-conversations\"\r\n      >\r\n        <li\r\n          *ngFor=\"let conv of filteredConversations\"\r\n          (click)=\"openConversation(conv.id)\"\r\n          class=\"futuristic-conversation-item\"\r\n          [ngClass]=\"{\r\n            'futuristic-conversation-selected':\r\n              selectedConversationId === conv.id\r\n          }\"\r\n        >\r\n          <div class=\"flex items-center\">\r\n            <!-- Avatar avec indicateur de statut -->\r\n            <div class=\"futuristic-avatar\">\r\n              <img\r\n                [src]=\"\r\n                  (conv.participants\r\n                    ? getOtherParticipant(conv.participants)?.image\r\n                    : null) || 'assets/images/default-avatar.png'\r\n                \"\r\n                alt=\"User avatar\"\r\n              />\r\n\r\n              <!-- Online indicator with glow -->\r\n              <div\r\n                *ngIf=\"\r\n                  conv.participants &&\r\n                  getOtherParticipant(conv.participants)?.isOnline\r\n                \"\r\n                class=\"futuristic-online-indicator\"\r\n              ></div>\r\n            </div>\r\n\r\n            <!-- Détails de la conversation -->\r\n            <div class=\"futuristic-conversation-details\">\r\n              <div class=\"futuristic-conversation-header\">\r\n                <h3 class=\"futuristic-conversation-name\">\r\n                  {{\r\n                    (conv.participants\r\n                      ? getOtherParticipant(conv.participants)?.username\r\n                      : null) || \"Utilisateur inconnu\"\r\n                  }}\r\n                </h3>\r\n                <span class=\"futuristic-conversation-time\">\r\n                  {{ (conv.lastMessage?.timestamp | date : \"shortTime\") || \"\" }}\r\n                </span>\r\n              </div>\r\n\r\n              <div class=\"futuristic-conversation-preview\">\r\n                <p class=\"futuristic-conversation-message\">\r\n                  <span\r\n                    *ngIf=\"conv.lastMessage?.sender?.id === currentUserId\"\r\n                    class=\"futuristic-you-prefix\"\r\n                    >Vous:\r\n                  </span>\r\n                  {{ conv.lastMessage?.content || \"Pas encore de messages\" }}\r\n                </p>\r\n                <div\r\n                  *ngIf=\"conv.unreadCount && conv.unreadCount > 0\"\r\n                  class=\"futuristic-unread-badge\"\r\n                >\r\n                  {{ conv.unreadCount }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Zone de contenu principal -->\r\n  <div class=\"flex-1 hidden md:flex flex-col futuristic-main-area\">\r\n    <!-- Router outlet pour le composant de chat -->\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,GAAG,EAAgBC,eAAe,QAAoB,MAAM;;;;;;;;;;;;IC2D3DC,EAAA,CAAAC,cAAA,cAA4D;IAExDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEPH,EAAA,CAAAI,SAAA,cAEO;IACTJ,EAAA,CAAAG,YAAA,EAAM;;;;IANFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,QAAA,MACF;;;;;IAsCNP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,SAAA,cAA6C;IAC7CJ,EAAA,CAAAC,cAAA,YAAmC;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIxEH,EAAA,CAAAC,cAAA,cAAsD;IAElDD,EAAA,CAAAI,SAAA,YAA2C;IAC7CJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,iBAAsE;IAA9DD,EAAA,CAAAQ,UAAA,mBAAAC,8DAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCf,EAAA,CAAAI,SAAA,YAAsC;IAACJ,EAAA,CAAAE,MAAA,wBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAU,MAAA,CAAAC,KAAA,MACF;;;;;;IAQJjB,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAI,SAAA,YAA+B;IACjCJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3DH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAE,MAAA,iEACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,iBAGC;IAFCD,EAAA,CAAAQ,UAAA,mBAAAU,8DAAA;MAAAlB,EAAA,CAAAU,aAAA,CAAAS,IAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAM,MAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCrB,EAAA,CAAAI,SAAA,YAAuC;IACvCJ,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAIXH,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAI,SAAA,YAA6B;IAC/BJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,sCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7DH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IA8BlEH,EAAA,CAAAI,SAAA,cAMO;;;;;IAoBHJ,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,aACH;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGTH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgB,QAAA,CAAAC,WAAA,MACF;;;;;;;;;;;IA5DRvB,EAAA,CAAAC,cAAA,aAQC;IANCD,EAAA,CAAAQ,UAAA,mBAAAgB,8DAAA;MAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAAU,aAAA,CAAAgB,IAAA;MAAA,MAAAJ,QAAA,GAAAG,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAc,OAAA,CAAAC,gBAAA,CAAAP,QAAA,CAAAQ,EAAA,CAAyB;IAAA,EAAC;IAOnC9B,EAAA,CAAAC,cAAA,cAA+B;IAG3BD,EAAA,CAAAI,SAAA,cAOE;IAGFJ,EAAA,CAAA+B,UAAA,IAAAC,+CAAA,kBAMO;IACThC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA6C;IAGvCD,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGTH,EAAA,CAAAC,cAAA,eAA6C;IAEzCD,EAAA,CAAA+B,UAAA,KAAAE,iDAAA,mBAIO;IACPjC,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAA+B,UAAA,KAAAG,gDAAA,kBAKM;IACRlC,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAzDVH,EAAA,CAAAmC,UAAA,YAAAnC,EAAA,CAAAoC,eAAA,KAAAC,GAAA,EAAAC,OAAA,CAAAC,sBAAA,KAAAjB,QAAA,CAAAQ,EAAA,EAGE;IAMI9B,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAmC,UAAA,SAAAb,QAAA,CAAAkB,YAAA,IAAAC,OAAA,GAAAH,OAAA,CAAAI,mBAAA,CAAApB,QAAA,CAAAkB,YAAA,oBAAAC,OAAA,CAAAE,KAAA,gDAAA3C,EAAA,CAAA4C,aAAA,CAIC;IAMA5C,EAAA,CAAAK,SAAA,GAGD;IAHCL,EAAA,CAAAmC,UAAA,SAAAb,QAAA,CAAAkB,YAAA,MAAAK,OAAA,GAAAP,OAAA,CAAAI,mBAAA,CAAApB,QAAA,CAAAkB,YAAA,oBAAAK,OAAA,CAAAC,QAAA,EAGD;IASE9C,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,OAAAgB,QAAA,CAAAkB,YAAA,IAAAO,OAAA,GAAAT,OAAA,CAAAI,mBAAA,CAAApB,QAAA,CAAAkB,YAAA,oBAAAO,OAAA,CAAAC,QAAA,uCAKF;IAEEhD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAiD,WAAA,QAAA3B,QAAA,CAAA4B,WAAA,kBAAA5B,QAAA,CAAA4B,WAAA,CAAAC,SAAA,0BACF;IAMKnD,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAmC,UAAA,UAAAb,QAAA,CAAA4B,WAAA,kBAAA5B,QAAA,CAAA4B,WAAA,CAAAE,MAAA,kBAAA9B,QAAA,CAAA4B,WAAA,CAAAE,MAAA,CAAAtB,EAAA,MAAAQ,OAAA,CAAAe,aAAA,CAAoD;IAIvDrD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAgB,QAAA,CAAA4B,WAAA,kBAAA5B,QAAA,CAAA4B,WAAA,CAAAI,OAAA,mCACF;IAEGtD,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAmC,UAAA,SAAAb,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,KAA8C;;;;;IA5D3DvB,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAA+B,UAAA,IAAAwB,yCAAA,mBAgEK;IACPvD,EAAA,CAAAG,YAAA,EAAK;;;;IAhEgBH,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAmC,UAAA,YAAAqB,MAAA,CAAAC,qBAAA,CAAwB;;;AD5JnD,OAAM,MAAOC,qBAAqB;EAchCC,YACUC,cAA8B,EAC9BC,WAA4B,EAC5BC,MAAc,EACdC,KAAqB,EACrBC,YAA0B,EAC1BC,MAAqB,EACrBC,YAA0B;IAN1B,KAAAN,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IApBtB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAV,qBAAqB,GAAmB,EAAE;IAC1C,KAAAW,OAAO,GAAG,IAAI;IAEd,KAAAf,aAAa,GAAkB,IAAI;IACnC,KAAAgB,WAAW,GAAG,EAAE;IAChB,KAAA9B,sBAAsB,GAAkB,IAAI;IAGpC,KAAAhB,WAAW,GAAG,IAAIxB,eAAe,CAAS,CAAC,CAAC;IAC7C,KAAAuE,YAAY,GAAG,IAAI,CAAC/C,WAAW,CAACgD,YAAY,EAAE;IAC7C,KAAAC,aAAa,GAAmB,EAAE;IAWxC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACP,YAAY,CAACQ,SAAS;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtB,aAAa,GAAG,IAAI,CAACQ,WAAW,CAACe,gBAAgB,EAAE;IACxD,IAAI,CAAC,IAAI,CAACvB,aAAa,EAAE;MACvB,IAAI,CAACwB,WAAW,CAAC,wBAAwB,CAAC;MAC1C;;IAGF,IAAI,CAAC9D,iBAAiB,EAAE;IACxB,IAAI,CAAC+D,qBAAqB,EAAE;IAC5B,IAAI,CAACC,8BAA8B,EAAE;IAErC;IACA,IAAI,CAAChB,KAAK,CAACiB,UAAU,EAAEC,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACjD,IAAI,CAAC1C,sBAAsB,GAAG0C,MAAM,CAAC,gBAAgB,CAAC,IAAI,IAAI;IAChE,CAAC,CAAC;EACJ;EAEAlE,iBAAiBA,CAAA;IACf,IAAI,CAACkD,MAAM,CAACkB,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC;IACzD,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnD,KAAK,GAAG,IAAI;IAEjB,MAAMmE,GAAG,GAAG,IAAI,CAACxB,cAAc,CAACyB,gBAAgB,EAAE,CAACH,SAAS,CAAC;MAC3DI,IAAI,EAAGnB,aAAa,IAAI;QACtB,IAAI,CAACF,MAAM,CAACkB,IAAI,CACd,cAAc,EACd,YAAYhB,aAAa,CAACoB,MAAM,6BAA6B,CAC9D;QACD,IAAI,CAACpB,aAAa,GAAGqB,KAAK,CAACC,OAAO,CAACtB,aAAa,CAAC,GAC7C,CAAC,GAAGA,aAAa,CAAC,GAClB,EAAE;QAEN,IAAI,CAACF,MAAM,CAACyB,KAAK,CAAC,cAAc,EAAE,yBAAyB,CAAC;QAC5D,IAAI,CAACC,mBAAmB,EAAE;QAE1B,IAAI,CAAC1B,MAAM,CAACyB,KAAK,CAAC,cAAc,EAAE,uBAAuB,CAAC;QAC1D,IAAI,CAACE,iBAAiB,EAAE;QAExB,IAAI,CAAC3B,MAAM,CAACyB,KAAK,CAAC,cAAc,EAAE,uBAAuB,CAAC;QAC1D,IAAI,CAACG,iBAAiB,EAAE;QAExB,IAAI,CAACzB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACH,MAAM,CAACkB,IAAI,CAAC,cAAc,EAAE,mCAAmC,CAAC;MACvE,CAAC;MACDlE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgD,MAAM,CAAChD,KAAK,CACf,cAAc,EACd,8BAA8B,EAC9BA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACmD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACJ,YAAY,CAAC8B,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;IACF,IAAI,CAACtB,aAAa,CAACuB,IAAI,CAACX,GAAG,CAAC;IAC5B,IAAI,CAACnB,MAAM,CAACyB,KAAK,CAAC,cAAc,EAAE,iCAAiC,CAAC;EACtE;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACtB,WAAW,EAAE;MACrB,IAAI,CAACZ,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACU,aAAa,CAAC;MACpD;;IAGF,MAAM6B,KAAK,GAAG,IAAI,CAAC3B,WAAW,CAAC4B,WAAW,EAAE;IAC5C,IAAI,CAACxC,qBAAqB,GAAG,IAAI,CAACU,aAAa,CAAC+B,MAAM,CAAEC,IAAI,IAAI;MAC9D,MAAMC,gBAAgB,GAAGD,IAAI,CAAC3D,YAAY,GACtC,IAAI,CAACE,mBAAmB,CAACyD,IAAI,CAAC3D,YAAY,CAAC,GAC3C6D,SAAS;MACb,OACED,gBAAgB,EAAEpD,QAAQ,CAACiD,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC,IACxDG,IAAI,CAACjD,WAAW,EAAEI,OAAO,EAAE2C,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC,IACxD,KAAK;IAET,CAAC,CAAC;EACJ;EAEQJ,iBAAiBA,CAAA;IACvB,MAAMW,KAAK,GAAG,IAAI,CAACpC,aAAa,CAACqC,MAAM,CACrC,CAACC,GAAG,EAAEN,IAAI,KAAKM,GAAG,IAAIN,IAAI,CAAC5E,WAAW,IAAI,CAAC,CAAC,EAC5C,CAAC,CACF;IACD,IAAI,CAACA,WAAW,CAAC+D,IAAI,CAACiB,KAAK,CAAC;EAC9B;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAAC1B,aAAa,CAACuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,mBAAmB,CAACH,CAAC,CAAC;MACzC,MAAMI,KAAK,GAAG,IAAI,CAACD,mBAAmB,CAACF,CAAC,CAAC;MACzC,OAAOG,KAAK,CAACC,OAAO,EAAE,GAAGH,KAAK,CAACG,OAAO,EAAE;IAC1C,CAAC,CAAC;IACF,IAAI,CAACrB,mBAAmB,EAAE;EAC5B;EAEQmB,mBAAmBA,CAACX,IAAkB;IAC5C;IACA,MAAMc,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAIf,IAAI,CAACjD,WAAW,EAAEC,SAAS,EAAE;MAC/B,OAAO,OAAOgD,IAAI,CAACjD,WAAW,CAACC,SAAS,KAAK,QAAQ,GACjD,IAAI+D,IAAI,CAACf,IAAI,CAACjD,WAAW,CAACC,SAAS,CAAC,GACpCgD,IAAI,CAACjD,WAAW,CAACC,SAAS;;IAGhC,IAAIgD,IAAI,CAACgB,SAAS,EAAE;MAClB,OAAO,OAAOhB,IAAI,CAACgB,SAAS,KAAK,QAAQ,GACrC,IAAID,IAAI,CAACf,IAAI,CAACgB,SAAS,CAAC,GACxBhB,IAAI,CAACgB,SAAS;;IAGpB,IAAIhB,IAAI,CAACiB,SAAS,EAAE;MAClB,OAAO,OAAOjB,IAAI,CAACiB,SAAS,KAAK,QAAQ,GACrC,IAAIF,IAAI,CAACf,IAAI,CAACiB,SAAS,CAAC,GACxBjB,IAAI,CAACiB,SAAS;;IAGpB,OAAOH,WAAW;EACpB;EAEAvE,mBAAmBA,CAACF,YAAgC;IAClD,IAAI,CAACA,YAAY,IAAI,CAACgD,KAAK,CAACC,OAAO,CAACjD,YAAY,CAAC,EAAE;MACjD,OAAO6D,SAAS;;IAElB,OAAO7D,YAAY,CAAC6E,IAAI,CACrBC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAK,IAAI,CAAClE,aAAa,IAAIiE,CAAC,CAACxF,EAAE,KAAK,IAAI,CAACuB,aAAa,CACnE;EACH;EAEAyB,qBAAqBA,CAAA;IACnB,MAAMM,GAAG,GAAG,IAAI,CAACxB,cAAc,CAACkB,qBAAqB,EAAE,CACpD0C,IAAI,CAAC1H,GAAG,CAAE2H,IAAI,IAAK,IAAI,CAAC7D,cAAc,CAAC8D,aAAa,CAACD,IAAI,CAAC,CAAC,CAAC,CAC5DvC,SAAS,CAAC;MACTI,IAAI,EAAGmC,IAAU,IAAI;QACnB,IAAIA,IAAI,EAAE;UACR,IAAI,CAACE,gBAAgB,CAACF,IAAI,CAAC;;MAE/B,CAAC;MACDxG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgD,MAAM,CAAChD,KAAK,CACf,cAAc,EACd,+BAA+B,EAC/BA,KAAK,CACN;QACD,IAAI,CAAC+C,YAAY,CAAC8B,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;IACJ,IAAI,CAACtB,aAAa,CAACuB,IAAI,CAACX,GAAG,CAAC;EAC9B;EAEAL,8BAA8BA,CAAA;IAC5B,MAAMK,GAAG,GAAG,IAAI,CAACxB,cAAc,CAACmB,8BAA8B,CAC5D,QAAQ,CACT,CAACG,SAAS,CAAC;MACVI,IAAI,EAAGsC,WAAW,IAAI;QACpB,MAAMC,KAAK,GAAG,IAAI,CAAC1D,aAAa,CAAC2D,SAAS,CACvCC,CAAC,IAAKA,CAAC,CAACjG,EAAE,KAAK8F,WAAW,CAAC9F,EAAE,CAC/B;QACD,IAAI+F,KAAK,IAAI,CAAC,EAAE;UACd,IAAI,CAAC1D,aAAa,CAAC0D,KAAK,CAAC,GAAGD,WAAW;SACxC,MAAM;UACL,IAAI,CAACzD,aAAa,CAAC6D,OAAO,CAACJ,WAAW,CAAC;;QAEzC,IAAI,CAAC/B,iBAAiB,EAAE;MAC1B,CAAC;MACD5E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgD,MAAM,CAAChD,KAAK,CAAC,cAAc,EAAE,4BAA4B,EAAEA,KAAK,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACuD,aAAa,CAACuB,IAAI,CAACX,GAAG,CAAC;EAC9B;EAEAuC,gBAAgBA,CAACM,WAAiB;IAChC,IAAI,CAAC9D,aAAa,GAAG,IAAI,CAACA,aAAa,CAACrE,GAAG,CAAEqG,IAAI,IAAI;MACnD,IAAI,CAACA,IAAI,CAAC3D,YAAY,EAAE;QACtB,OAAO2D,IAAI;;MAEb,MAAM3D,YAAY,GAAG2D,IAAI,CAAC3D,YAAY,CAAC1C,GAAG,CAAEwH,CAAC,IAAI;QAC/C,MAAMY,aAAa,GACjBZ,CAAC,CAACC,GAAG,KAAKU,WAAW,CAACV,GAAG,IAAID,CAAC,CAACxF,EAAE,KAAKmG,WAAW,CAACV,GAAG;QACvD,OAAOW,aAAa,GAChB;UACE,GAAGZ,CAAC;UACJxE,QAAQ,EAAEmF,WAAW,CAACnF,QAAQ;UAC9BqF,UAAU,EAAEF,WAAW,CAACE;SACzB,GACDb,CAAC;MACP,CAAC,CAAC;MACF,OAAO;QAAE,GAAGnB,IAAI;QAAE3D;MAAY,CAAE;IAClC,CAAC,CAAC;IACF,IAAI,CAACmD,mBAAmB,EAAE;EAC5B;EAEA9D,gBAAgBA,CAACuG,cAAkC;IACjD,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACnE,MAAM,CAAChD,KAAK,CACf,cAAc,EACd,uDAAuD,CACxD;MACD;;IAGF,IAAI,CAACgD,MAAM,CAACkB,IAAI,CAAC,cAAc,EAAE,yBAAyBiD,cAAc,EAAE,CAAC;IAC3E,IAAI,CAAC7F,sBAAsB,GAAG6F,cAAc;IAE5C;IACA,MAAMC,YAAY,GAAG,IAAI,CAAClE,aAAa,CAACkD,IAAI,CACzCU,CAAC,IAAKA,CAAC,CAACjG,EAAE,KAAKsG,cAAc,CAC/B;IACD,IAAIC,YAAY,EAAE;MAChB,MAAMjC,gBAAgB,GAAGiC,YAAY,CAAC7F,YAAY,GAC9C,IAAI,CAACE,mBAAmB,CAAC2F,YAAY,CAAC7F,YAAY,CAAC,GACnD6D,SAAS;MACb,IAAI,CAACpC,MAAM,CAACyB,KAAK,CACf,cAAc,EACd,sBAAsBU,gBAAgB,EAAEpD,QAAQ,IAAI,SAAS,EAAE,CAChE;MACD,IAAI,CAACiB,MAAM,CAACyB,KAAK,CACf,cAAc,EACd,oBAAoB2C,YAAY,CAAC9G,WAAW,IAAI,CAAC,EAAE,CACpD;;IAGH,IAAI,CAAC0C,MAAM,CAACyB,KAAK,CACf,cAAc,EACd,iDAAiD0C,cAAc,EAAE,CAClE;IACD,IAAI,CAACtE,MAAM,CAACwE,QAAQ,CAAC,CAAC,MAAM,EAAEF,cAAc,CAAC,EAAE;MAAEG,UAAU,EAAE,IAAI,CAACxE;IAAK,CAAE,CAAC;EAC5E;EAEA1C,oBAAoBA,CAAA;IAClB,IAAI,CAAC4C,MAAM,CAACkB,IAAI,CACd,cAAc,EACd,qDAAqD,CACtD;IACD,IAAI,CAACrB,MAAM,CAACwE,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAE,gBAAgBA,CAACL,UAAkB;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMM,cAAc,GAAG,IAAIvB,IAAI,CAACiB,UAAU,CAAC;IAC3C,MAAMO,GAAG,GAAG,IAAIxB,IAAI,EAAE;IACtB,MAAMyB,SAAS,GACbC,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC1B,OAAO,EAAE,GAAGyB,cAAc,CAACzB,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAI2B,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUF,cAAc,CAACK,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,MAAM;MACL,OAAO,UAAUP,cAAc,CAACQ,kBAAkB,EAAE,EAAE;;EAE1D;EAEQpE,WAAWA,CAACqE,OAAe,EAAEjI,KAAW;IAC9C,IAAI,CAACA,KAAK,GAAGiI,OAAO;IACpB,IAAI,CAAC9E,OAAO,GAAG,KAAK;IACpB,IAAI,CAACH,MAAM,CAAChD,KAAK,CAAC,cAAc,EAAEiI,OAAO,EAAEjI,KAAK,CAAC;IACjD,IAAI,CAAC+C,YAAY,CAAC8B,SAAS,CAACoD,OAAO,CAAC;EACtC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3E,aAAa,CAAC4E,OAAO,CAAEhE,GAAG,IAAKA,GAAG,CAACiE,WAAW,EAAE,CAAC;EACxD;;;uBAlSW3F,qBAAqB,EAAA1D,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAA3F,cAAA,GAAA5D,EAAA,CAAAsJ,iBAAA,CAAAE,EAAA,CAAAC,eAAA,GAAAzJ,EAAA,CAAAsJ,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAhK,EAAA,CAAAsJ,iBAAA,CAAAW,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAArBxG,qBAAqB;MAAAyG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCflCzK,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAC,cAAA,aAAkE;UAChED,EAAA,CAAAI,SAAA,aAEO;UAMPJ,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAI,SAAA,aAAmE;UAWrEJ,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAEC;UAIGD,EAAA,CAAAI,SAAA,cAEO;UAKPJ,EAAA,CAAAC,cAAA,eAAoD;UACHD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAQ,UAAA,mBAAAmK,wDAAA;YAAA,OAASD,GAAA,CAAArJ,oBAAA,EAAsB;UAAA,EAAC;UAIhCrB,EAAA,CAAAI,SAAA,eAEO;UAITJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA+B,UAAA,KAAA6I,qCAAA,kBAQM;;UACR5K,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAQ,UAAA,2BAAAqK,+DAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAArG,WAAA,GAAAyG,MAAA;UAAA,EAAyB,2BAAAD,+DAAA;YAAA,OACRH,GAAA,CAAA/E,mBAAA,EAAqB;UAAA,EADb;UAD3B3F,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAI,SAAA,aAEK;UACPJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAI,SAAA,eAEO;UACTJ,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAkE;UAEhED,EAAA,CAAA+B,UAAA,KAAAgJ,qCAAA,kBAMM;UAGN/K,EAAA,CAAA+B,UAAA,KAAAiJ,qCAAA,mBAeM;UAGNhL,EAAA,CAAA+B,UAAA,KAAAkJ,qCAAA,mBAqBM;UAGNjL,EAAA,CAAA+B,UAAA,KAAAmJ,qCAAA,kBAWM;UAGNlL,EAAA,CAAA+B,UAAA,KAAAoJ,oCAAA,iBAqEK;UACPnL,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAiE;UAE/DD,EAAA,CAAAI,SAAA,qBAA+B;UACjCJ,EAAA,CAAAG,YAAA,EAAM;;;UAjPNH,EAAA,CAAAoL,WAAA,SAAApL,EAAA,CAAAqL,WAAA,OAAAX,GAAA,CAAAjG,WAAA,EAAkC;UA0DpBzE,EAAA,CAAAK,SAAA,IAA2B;UAA3BL,EAAA,CAAAmC,UAAA,SAAAnC,EAAA,CAAAqL,WAAA,SAAAX,GAAA,CAAApG,YAAA,EAA2B;UAejCtE,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAmC,UAAA,YAAAuI,GAAA,CAAArG,WAAA,CAAyB;UA2B1BrE,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAmC,UAAA,SAAAuI,GAAA,CAAAtG,OAAA,CAAa;UAQVpE,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAmC,UAAA,SAAAuI,GAAA,CAAAzJ,KAAA,CAAW;UAmBdjB,EAAA,CAAAK,SAAA,GAAoE;UAApEL,EAAA,CAAAmC,UAAA,UAAAuI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAAjH,qBAAA,CAAA8B,MAAA,WAAAmF,GAAA,CAAArG,WAAA,CAAoE;UAwBpErE,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAmC,UAAA,UAAAuI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAAjH,qBAAA,CAAA8B,MAAA,UAAAmF,GAAA,CAAArG,WAAA,CAAmE;UAcnErE,EAAA,CAAAK,SAAA,GAAkD;UAAlDL,EAAA,CAAAmC,UAAA,UAAAuI,GAAA,CAAAtG,OAAA,IAAAsG,GAAA,CAAAjH,qBAAA,CAAA8B,MAAA,KAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}