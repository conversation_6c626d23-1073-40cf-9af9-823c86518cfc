{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ProjectsRoutingModule } from './projects-routing.module';\nimport { ListProjectComponent } from './list-project/list-project.component';\nimport { AddProjectComponent } from './add-project/add-project.component';\nimport { UpdateProjectComponent } from './update-project/update-project.component';\nimport { DetailProjectComponent } from './detail-project/detail-project.component';\nimport { ListRendusComponent } from './list-rendus/list-rendus.component';\nimport { ProjectEvaluationComponent } from './project-evaluation/project-evaluation.component';\nimport { EvaluationDetailsComponent } from './evaluation-details/evaluation-details.component';\nimport { EvaluationsListComponent } from './evaluations-list/evaluations-list.component';\nimport { EditEvaluationComponent } from './edit-evaluation/edit-evaluation.component';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class ProjectsModule {\n  static {\n    this.ɵfac = function ProjectsModule_Factory(t) {\n      return new (t || ProjectsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProjectsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ProjectsRoutingModule, FormsModule, ReactiveFormsModule, RouterModule, MatDialogModule, MatButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProjectsModule, {\n    declarations: [ListProjectComponent, AddProjectComponent, UpdateProjectComponent, DetailProjectComponent, ListRendusComponent, ProjectEvaluationComponent, EvaluationDetailsComponent, EvaluationsListComponent, EditEvaluationComponent],\n    imports: [CommonModule, ProjectsRoutingModule, FormsModule, ReactiveFormsModule, RouterModule, MatDialogModule, MatButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "ProjectsRoutingModule", "ListProjectComponent", "AddProjectComponent", "UpdateProjectComponent", "DetailProjectComponent", "ListRendusComponent", "ProjectEvaluationComponent", "EvaluationDetailsComponent", "EvaluationsListComponent", "EditEvaluationComponent", "MatDialogModule", "MatButtonModule", "RouterModule", "ProjectsModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\projects\\projects.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { ProjectsRoutingModule } from './projects-routing.module';\r\nimport { ListProjectComponent } from './list-project/list-project.component';\r\nimport { AddProjectComponent } from './add-project/add-project.component';\r\nimport { UpdateProjectComponent } from './update-project/update-project.component';\r\nimport { DetailProjectComponent } from './detail-project/detail-project.component';\r\nimport { ListRendusComponent } from './list-rendus/list-rendus.component';\r\nimport { ProjectEvaluationComponent } from './project-evaluation/project-evaluation.component';\r\nimport { EvaluationDetailsComponent } from './evaluation-details/evaluation-details.component';\r\nimport { EvaluationsListComponent } from './evaluations-list/evaluations-list.component';\r\nimport { EditEvaluationComponent } from './edit-evaluation/edit-evaluation.component';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ListProjectComponent,\r\n    AddProjectComponent,\r\n    UpdateProjectComponent,\r\n    DetailProjectComponent,\r\n    ListRendusComponent,\r\n    ProjectEvaluationComponent,\r\n    EvaluationDetailsComponent,\r\n    EvaluationsListComponent,\r\n    EditEvaluationComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ProjectsRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    RouterModule,\r\n    MatDialogModule,\r\n    MatButtonModule,\r\n  ],\r\n})\r\nexport class ProjectsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;;AAwB9C,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBATvBhB,YAAY,EACZG,qBAAqB,EACrBF,WAAW,EACXC,mBAAmB,EACnBa,YAAY,EACZF,eAAe,EACfC,eAAe;IAAA;EAAA;;;2EAGNE,cAAc;IAAAC,YAAA,GApBvBb,oBAAoB,EACpBC,mBAAmB,EACnBC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,wBAAwB,EACxBC,uBAAuB;IAAAM,OAAA,GAGvBlB,YAAY,EACZG,qBAAqB,EACrBF,WAAW,EACXC,mBAAmB,EACnBa,YAAY,EACZF,eAAe,EACfC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}