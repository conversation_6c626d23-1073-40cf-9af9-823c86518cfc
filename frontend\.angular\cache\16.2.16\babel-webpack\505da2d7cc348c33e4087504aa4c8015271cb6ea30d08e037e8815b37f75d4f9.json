{"ast": null, "code": "// This file is adapted from the graphql-ws npm package:\n// https://github.com/enisdenjo/graphql-ws\n//\n// Most of the file comes from that package's README; some other parts (such as\n// isLikeCloseEvent) come from its source.\n//\n// Here's the license of the original code:\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2020-2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\nimport { __assign, __extends } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { isNonNullObject, Observable } from \"../../utilities/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/close_event\nfunction isLikeCloseEvent(val) {\n  return isNonNullObject(val) && \"code\" in val && \"reason\" in val;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/error_event\nfunction isLikeErrorEvent(err) {\n  var _a;\n  return isNonNullObject(err) && ((_a = err.target) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.CLOSED;\n}\nvar GraphQLWsLink = /** @class */function (_super) {\n  __extends(GraphQLWsLink, _super);\n  function GraphQLWsLink(client) {\n    var _this = _super.call(this) || this;\n    _this.client = client;\n    return _this;\n  }\n  GraphQLWsLink.prototype.request = function (operation) {\n    var _this = this;\n    return new Observable(function (observer) {\n      return _this.client.subscribe(__assign(__assign({}, operation), {\n        query: print(operation.query)\n      }), {\n        next: observer.next.bind(observer),\n        complete: observer.complete.bind(observer),\n        error: function (err) {\n          if (err instanceof Error) {\n            return observer.error(err);\n          }\n          var likeClose = isLikeCloseEvent(err);\n          if (likeClose || isLikeErrorEvent(err)) {\n            return observer.error(\n            // reason will be available on clean closes\n            new Error(\"Socket closed\".concat(likeClose ? \" with event \".concat(err.code) : \"\").concat(likeClose ? \" \".concat(err.reason) : \"\")));\n          }\n          return observer.error(new ApolloError({\n            graphQLErrors: Array.isArray(err) ? err : [err]\n          }));\n        }\n        // casting around a wrong type in graphql-ws, which incorrectly expects `Sink<ExecutionResult>`\n      });\n    });\n  };\n\n  return GraphQLWsLink;\n}(ApolloLink);\nexport { GraphQLWsLink };", "map": {"version": 3, "names": ["__assign", "__extends", "print", "ApolloLink", "isNonNullObject", "Observable", "ApolloError", "isLikeCloseEvent", "val", "isLikeErrorEvent", "err", "_a", "target", "readyState", "WebSocket", "CLOSED", "GraphQLWsLink", "_super", "client", "_this", "call", "prototype", "request", "operation", "observer", "subscribe", "query", "next", "bind", "complete", "error", "Error", "likeClose", "concat", "code", "reason", "graphQLErrors", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/subscriptions/index.js"], "sourcesContent": ["// This file is adapted from the graphql-ws npm package:\n// https://github.com/enisdenjo/graphql-ws\n//\n// Most of the file comes from that package's README; some other parts (such as\n// isLikeCloseEvent) come from its source.\n//\n// Here's the license of the original code:\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2020-2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\nimport { __assign, __extends } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { isNonNullObject, Observable } from \"../../utilities/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/close_event\nfunction isLikeCloseEvent(val) {\n    return isNonNullObject(val) && \"code\" in val && \"reason\" in val;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/error_event\nfunction isLikeErrorEvent(err) {\n    var _a;\n    return isNonNullObject(err) && ((_a = err.target) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.CLOSED;\n}\nvar GraphQLWsLink = /** @class */ (function (_super) {\n    __extends(GraphQLWsLink, _super);\n    function GraphQLWsLink(client) {\n        var _this = _super.call(this) || this;\n        _this.client = client;\n        return _this;\n    }\n    GraphQLWsLink.prototype.request = function (operation) {\n        var _this = this;\n        return new Observable(function (observer) {\n            return _this.client.subscribe(__assign(__assign({}, operation), { query: print(operation.query) }), {\n                next: observer.next.bind(observer),\n                complete: observer.complete.bind(observer),\n                error: function (err) {\n                    if (err instanceof Error) {\n                        return observer.error(err);\n                    }\n                    var likeClose = isLikeCloseEvent(err);\n                    if (likeClose || isLikeErrorEvent(err)) {\n                        return observer.error(\n                        // reason will be available on clean closes\n                        new Error(\"Socket closed\".concat(likeClose ? \" with event \".concat(err.code) : \"\").concat(likeClose ? \" \".concat(err.reason) : \"\")));\n                    }\n                    return observer.error(new ApolloError({\n                        graphQLErrors: Array.isArray(err) ? err : [err],\n                    }));\n                },\n                // casting around a wrong type in graphql-ws, which incorrectly expects `Sink<ExecutionResult>`\n            });\n        });\n    };\n    return GraphQLWsLink;\n}(ApolloLink));\nexport { GraphQLWsLink };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,eAAe,EAAEC,UAAU,QAAQ,0BAA0B;AACtE,SAASC,WAAW,QAAQ,uBAAuB;AACnD;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,OAAOJ,eAAe,CAACI,GAAG,CAAC,IAAI,MAAM,IAAIA,GAAG,IAAI,QAAQ,IAAIA,GAAG;AACnE;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIC,EAAE;EACN,OAAOP,eAAe,CAACM,GAAG,CAAC,IAAI,CAAC,CAACC,EAAE,GAAGD,GAAG,CAACE,MAAM,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,MAAMC,SAAS,CAACC,MAAM;AAC9H;AACA,IAAIC,aAAa,GAAG,aAAe,UAAUC,MAAM,EAAE;EACjDhB,SAAS,CAACe,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAACE,MAAM,EAAE;IAC3B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,MAAM,GAAGA,MAAM;IACrB,OAAOC,KAAK;EAChB;EACAH,aAAa,CAACK,SAAS,CAACC,OAAO,GAAG,UAAUC,SAAS,EAAE;IACnD,IAAIJ,KAAK,GAAG,IAAI;IAChB,OAAO,IAAId,UAAU,CAAC,UAAUmB,QAAQ,EAAE;MACtC,OAAOL,KAAK,CAACD,MAAM,CAACO,SAAS,CAACzB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuB,SAAS,CAAC,EAAE;QAAEG,KAAK,EAAExB,KAAK,CAACqB,SAAS,CAACG,KAAK;MAAE,CAAC,CAAC,EAAE;QAChGC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC;QAClCK,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,CAACD,IAAI,CAACJ,QAAQ,CAAC;QAC1CM,KAAK,EAAE,SAAAA,CAAUpB,GAAG,EAAE;UAClB,IAAIA,GAAG,YAAYqB,KAAK,EAAE;YACtB,OAAOP,QAAQ,CAACM,KAAK,CAACpB,GAAG,CAAC;UAC9B;UACA,IAAIsB,SAAS,GAAGzB,gBAAgB,CAACG,GAAG,CAAC;UACrC,IAAIsB,SAAS,IAAIvB,gBAAgB,CAACC,GAAG,CAAC,EAAE;YACpC,OAAOc,QAAQ,CAACM,KAAK;YACrB;YACA,IAAIC,KAAK,CAAC,eAAe,CAACE,MAAM,CAACD,SAAS,GAAG,cAAc,CAACC,MAAM,CAACvB,GAAG,CAACwB,IAAI,CAAC,GAAG,EAAE,CAAC,CAACD,MAAM,CAACD,SAAS,GAAG,GAAG,CAACC,MAAM,CAACvB,GAAG,CAACyB,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;UACxI;UACA,OAAOX,QAAQ,CAACM,KAAK,CAAC,IAAIxB,WAAW,CAAC;YAClC8B,aAAa,EAAEC,KAAK,CAACC,OAAO,CAAC5B,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG;UAClD,CAAC,CAAC,CAAC;QACP;QACA;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;;EACD,OAAOM,aAAa;AACxB,CAAC,CAACb,UAAU,CAAE;AACd,SAASa,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}