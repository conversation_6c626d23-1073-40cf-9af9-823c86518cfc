{"ast": null, "code": "import { finalize } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/authadmin.service\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProfileComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_7_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 79);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.user.profileImage && ctx_r4.user.profileImage !== \"null\" && ctx_r4.user.profileImage.trim() !== \"\" ? ctx_r4.user.profileImage : \"assets/images/default-profile.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_7_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 80);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_7_button_50_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 42);\n    i0.ɵɵelement(2, \"path\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Upload \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_50_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 87);\n    i0.ɵɵelement(2, \"circle\", 88)(3, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Uploading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onUpload());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_7_button_50_span_1_Template, 4, 0, \"span\", 82);\n    i0.ɵɵtemplate(2, ProfileComponent_div_7_button_50_span_2_Template, 5, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.uploadLoading);\n  }\n}\nfunction ProfileComponent_div_7_button_51_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 42);\n    i0.ɵɵelement(2, \"path\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Remove \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_51_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 87);\n    i0.ɵɵelement(2, \"circle\", 88)(3, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Removing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_button_51_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.removeProfileImage());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_7_button_51_span_1_Template, 4, 0, \"span\", 82);\n    i0.ɵɵtemplate(2, ProfileComponent_div_7_button_51_span_2_Template, 5, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.removeLoading);\n  }\n}\nfunction ProfileComponent_div_7_div_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 84);\n    i0.ɵɵelement(2, \"div\", 93);\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 95);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 96);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r17 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", activity_r17.action, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", activity_r17.target, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.formatDate(activity_r17.timestamp), \" \");\n  }\n}\nfunction ProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵelement(3, \"img\", 14);\n    i0.ɵɵelementStart(4, \"div\", 15)(5, \"div\", 16);\n    i0.ɵɵtemplate(6, ProfileComponent_div_7_img_6_Template, 1, 1, \"img\", 17);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_img_7_Template, 1, 1, \"img\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"label\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 20);\n    i0.ɵɵelement(10, \"path\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_7_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"h2\", 24);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 25)(16, \"span\", 26);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 27)(20, \"h3\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 29);\n    i0.ɵɵelement(22, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Account Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"div\", 32)(26, \"div\", 33);\n    i0.ɵɵtext(27, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 35)(31, \"div\", 36)(32, \"div\", 33);\n    i0.ɵɵtext(33, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"span\", 37);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 36)(38, \"div\", 33);\n    i0.ɵɵtext(39, \" Verification \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"span\", 37);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 32)(44, \"div\", 33);\n    i0.ɵɵtext(45, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 34);\n    i0.ɵɵtext(47);\n    i0.ɵɵpipe(48, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 38);\n    i0.ɵɵtemplate(50, ProfileComponent_div_7_button_50_Template, 3, 3, \"button\", 39);\n    i0.ɵɵtemplate(51, ProfileComponent_div_7_button_51_Template, 3, 3, \"button\", 40);\n    i0.ɵɵelementStart(52, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateTo(\"/change-password\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(53, \"svg\", 42);\n    i0.ɵɵelement(54, \"path\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(56, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.logout());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(57, \"svg\", 42);\n    i0.ɵɵelement(58, \"path\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Logout \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(60, \"div\", 46)(61, \"div\", 12)(62, \"div\", 47)(63, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(64, \"svg\", 29);\n    i0.ɵɵelement(65, \"path\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(66, \" User Statistics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(67, \"div\", 50)(68, \"div\", 51)(69, \"div\")(70, \"div\", 52)(71, \"div\", 33);\n    i0.ɵɵtext(72, \" Total Users \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 53);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 54);\n    i0.ɵɵelement(76, \"div\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\")(78, \"div\", 52)(79, \"div\", 33);\n    i0.ɵɵtext(80, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 56)(82, \"span\", 57);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"span\", 58);\n    i0.ɵɵtext(85, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 59);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(88, \"div\", 60);\n    i0.ɵɵelement(89, \"div\", 61)(90, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(91, \"div\")(92, \"div\", 52)(93, \"div\", 33);\n    i0.ɵɵtext(94, \"User Roles\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"div\", 56)(96, \"span\", 63);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"span\", 58);\n    i0.ɵɵtext(99, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"span\", 64);\n    i0.ɵɵtext(101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"span\", 58);\n    i0.ɵɵtext(103, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"span\", 65);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 60);\n    i0.ɵɵelement(107, \"div\", 66)(108, \"div\", 67)(109, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"div\", 69)(111, \"span\");\n    i0.ɵɵtext(112, \"Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(113, \"span\");\n    i0.ɵɵtext(114, \"Teachers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"span\");\n    i0.ɵɵtext(116, \"Admins\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(117, \"div\", 12)(118, \"div\", 47)(119, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(120, \"svg\", 29);\n    i0.ɵɵelement(121, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \" Recent Activity \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(123, \"div\", 50)(124, \"div\", 71);\n    i0.ɵɵtemplate(125, ProfileComponent_div_7_div_125_Template, 10, 3, \"div\", 72);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(126, \"div\", 12)(127, \"div\", 47)(128, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(129, \"svg\", 29);\n    i0.ɵɵelement(130, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(131, \" Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(132, \"div\", 50)(133, \"div\", 74)(134, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_134_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.navigateTo(\"/admin/dashboard\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(135, \"svg\", 42);\n    i0.ɵɵelement(136, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(137, \" Users \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(138, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_138_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.navigateTo(\"/\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(139, \"svg\", 42);\n    i0.ɵɵelement(140, \"path\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(141, \" Home \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewUrl || ctx_r3.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewUrl && !ctx_r3.uploadLoading);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 29, ctx_r3.user.role), \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r3.user.email);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.user.isActive !== false ? \"bg-[#afcf75]/10 text-[#2a5a03]\" : \"bg-[#ff6b69]/10 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.isActive !== false ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.user.verified ? \"bg-[#afcf75]/10 text-[#2a5a03]\" : \"bg-[#ff6b69]/10 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.verified ? \"Verified\" : \"Not Verified\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(48, 31, ctx_r3.user.createdAt, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.user.profileImage);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.stats.totalUsers, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.stats.activeUsers, \" Active\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.stats.inactiveUsers, \" Inactive\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.activeUsers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.inactiveUsers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.students);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.teachers);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.admins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.students / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.teachers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.admins / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.recentActivity);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, dataService, authAdminService, authUserService, router) {\n    this.authService = authService;\n    this.dataService = dataService;\n    this.authAdminService = authAdminService;\n    this.authUserService = authUserService;\n    this.router = router;\n    this.user = null;\n    this.selectedImage = null;\n    this.message = '';\n    this.error = '';\n    this.loading = true;\n    this.uploadLoading = false;\n    this.removeLoading = false;\n    this.stats = {\n      totalUsers: 0,\n      activeUsers: 0,\n      inactiveUsers: 0,\n      students: 0,\n      teachers: 0,\n      admins: 0\n    };\n    this.recentActivity = [{\n      action: 'User Deactivated',\n      target: 'John Doe',\n      timestamp: new Date(Date.now() - 3600000)\n    }, {\n      action: 'Role Changed',\n      target: 'Jane Smith',\n      timestamp: new Date(Date.now() - 7200000)\n    }, {\n      action: 'User Added',\n      target: 'Robert Johnson',\n      timestamp: new Date(Date.now() - 86400000)\n    }];\n    this.previewUrl = null;\n  }\n  ngOnInit() {\n    this.loadUserData();\n    this.loadStats();\n  }\n  loadUserData() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.router.navigate(['/admin/login']);\n      return;\n    }\n    this.loading = true;\n    this.authService.getProfile(token).subscribe({\n      next: res => {\n        this.user = res;\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to load profile.';\n        this.loading = false;\n      }\n    });\n  }\n  loadStats() {\n    const token = localStorage.getItem('token');\n    if (!token) return;\n    this.authService.getAllUsers(token).subscribe({\n      next: res => {\n        const users = res;\n        this.stats.totalUsers = users.length;\n        this.stats.activeUsers = users.filter(u => u.isActive !== false).length;\n        this.stats.inactiveUsers = users.filter(u => u.isActive === false).length;\n        this.stats.students = users.filter(u => u.role === 'student').length;\n        this.stats.teachers = users.filter(u => u.role === 'teacher').length;\n        this.stats.admins = users.filter(u => u.role === 'admin').length;\n      },\n      error: () => {\n        // Silently fail, stats are not critical\n      }\n    });\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files?.length) {\n      const file = input.files[0];\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n      if (!validTypes.includes(file.type)) {\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n        this.resetFileInput();\n        return;\n      }\n      if (file.size > 2 * 1024 * 1024) {\n        this.error = \"L'image ne doit pas dépasser 2MB\";\n        this.resetFileInput();\n        return;\n      }\n      this.selectedImage = file;\n      this.error = '';\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.previewUrl = e.target?.result || null;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  resetFileInput() {\n    this.selectedImage = null;\n    this.previewUrl = null;\n    const fileInput = document.getElementById('profile-upload');\n    if (fileInput) fileInput.value = '';\n  }\n  onUpload() {\n    if (!this.selectedImage) return;\n    this.uploadLoading = true; // Activer l'état de chargement\n    this.message = '';\n    this.error = '';\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\n    this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n      this.uploadLoading = false;\n      console.log('Upload finished, uploadLoading:', this.uploadLoading);\n    })).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile updated successfully';\n        // Update both properties to ensure consistency\n        this.user.profileImageURL = response.imageUrl;\n        this.user.profileImage = response.imageUrl;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: response.imageUrl,\n          image: response.imageUrl\n        });\n        this.selectedImage = null;\n        this.previewUrl = null;\n        this.resetFileInput();\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Upload failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  removeProfileImage() {\n    if (!confirm('Are you sure you want to remove your profile picture?')) return;\n    this.removeLoading = true;\n    this.message = '';\n    this.error = '';\n    this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n      next: response => {\n        this.message = response.message || 'Profile picture removed successfully';\n        // Update both properties to ensure consistency\n        this.user.profileImageURL = null;\n        this.user.profileImage = null;\n        // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n        this.dataService.updateCurrentUser({\n          profileImage: 'assets/images/default-profile.png',\n          image: 'assets/images/default-profile.png'\n        });\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Removal failed';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  navigateTo(path) {\n    this.router.navigate([path]);\n  }\n  logout() {\n    this.authUserService.logout().subscribe({\n      next: () => {\n        this.authUserService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authUserService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {});\n        }, 100);\n      }\n    });\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleString();\n  }\n  getInitials(name) {\n    if (!name) return 'A';\n    return name.split(' ').map(n => n[0]).join('').toUpperCase();\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.AuthadminService), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 8,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\", \"md:mb-0\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 border border-[#ff6b69] text-[#ff6b69] px-4 py-3 rounded-lg mb-6 animate-pulse\", 4, \"ngIf\"], [\"class\", \"bg-[#afcf75]/10 border border-[#afcf75] text-[#2a5a03] px-4 py-3 rounded-lg mb-6 animate-pulse\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"bg-[#ff6b69]/10\", \"border\", \"border-[#ff6b69]\", \"text-[#ff6b69]\", \"px-4\", \"py-3\", \"rounded-lg\", \"mb-6\", \"animate-pulse\"], [1, \"bg-[#afcf75]/10\", \"border\", \"border-[#afcf75]\", \"text-[#2a5a03]\", \"px-4\", \"py-3\", \"rounded-lg\", \"mb-6\", \"animate-pulse\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"relative\"], [\"src\", \"https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80\", \"alt\", \"Cover\", 1, \"w-full\", \"h-32\", \"object-cover\"], [1, \"absolute\", \"left-0\", \"right-0\", \"-bottom-12\", \"flex\", \"justify-center\"], [1, \"h-24\", \"w-24\", \"rounded-full\", \"border-4\", \"border-white\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", 2, \"min-height\", \"96px\", \"min-width\", \"96px\"], [\"alt\", \"Profile\", \"class\", \"h-full w-full object-cover\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Preview\", \"class\", \"h-full w-full object-cover\", 3, \"src\", 4, \"ngIf\"], [\"for\", \"profile-upload\", 1, \"absolute\", \"bottom-0\", \"right-0\", \"bg-[#7826b5]\", \"text-white\", \"p-1.5\", \"rounded-full\", \"cursor-pointer\", \"hover:bg-[#5f1d8f]\", \"transition-colors\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z\", \"clip-rule\", \"evenodd\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [1, \"p-5\", \"pt-16\", \"text-center\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-1\"], [1, \"mb-4\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"rounded-full\", \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"font-medium\"], [1, \"mt-6\", \"border-t\", \"border-[#edf1f4]\", \"pt-4\"], [1, \"flex\", \"items-center\", \"justify-center\", \"text-[#4f5fad]\", \"font-semibold\", \"mb-4\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\", \"clip-rule\", \"evenodd\"], [1, \"space-y-4\"], [1, \"text-left\", \"px-2\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\"], [1, \"flex\", \"space-x-4\"], [1, \"flex-1\", \"text-left\", \"px-2\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"rounded-full\", 3, \"ngClass\"], [1, \"mt-6\", \"flex\", \"flex-wrap\", \"justify-center\", \"gap-3\"], [\"class\", \"inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"inline-flex\", \"items-center\", \"bg-[#4f5fad]\", \"hover:bg-[#3d4a85]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\", \"clip-rule\", \"evenodd\"], [1, \"inline-flex\", \"items-center\", \"bg-[#ff6b69]\", \"hover:bg-[#e05554]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"fill-rule\", \"evenodd\", \"d\", \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-4-4H3zm6 11a1 1 0 11-2 0 1 1 0 012 0zm2-5.5a.5.5 0 00-.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 010-1H9V9.5A1.5 1.5 0 0110.5 8h.5a.5.5 0 01.5.5z\", \"clip-rule\", \"evenodd\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"p-5\", \"border-b\", \"border-[#bdc6cc]\"], [1, \"flex\", \"items-center\", \"font-bold\", \"text-[#4f5fad]\"], [\"d\", \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"], [1, \"p-5\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\", \"mb-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"rounded-full\", \"h-2.5\"], [1, \"bg-[#4f5fad]\", \"h-2.5\", \"rounded-full\", 2, \"width\", \"100%\"], [1, \"flex\", \"space-x-3\", \"text-sm\"], [1, \"text-[#afcf75]\", \"font-medium\"], [1, \"text-[#bdc6cc]\"], [1, \"text-[#ff6b69]\", \"font-medium\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"rounded-full\", \"h-2.5\", \"overflow-hidden\", \"flex\"], [1, \"bg-[#afcf75]\", \"h-2.5\"], [1, \"bg-[#ff6b69]\", \"h-2.5\"], [1, \"text-[#4a89ce]\", \"font-medium\"], [1, \"text-[#7826b5]\", \"font-medium\"], [1, \"text-[#4f5fad]\", \"font-medium\"], [1, \"bg-[#4a89ce]\", \"h-2.5\"], [1, \"bg-[#7826b5]\", \"h-2.5\"], [1, \"bg-[#4f5fad]\", \"h-2.5\"], [1, \"flex\", \"justify-between\", \"mt-2\", \"text-xs\", \"text-[#6d6870]\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\", \"clip-rule\", \"evenodd\"], [1, \"divide-y\", \"divide-[#edf1f4]\"], [\"class\", \"py-3 flex justify-between items-center\", 4, \"ngFor\", \"ngForOf\"], [\"fill-rule\", \"evenodd\", \"d\", \"M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [1, \"inline-flex\", \"items-center\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"d\", \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z\"], [1, \"inline-flex\", \"items-center\", \"bg-[#4a89ce]\", \"hover:bg-[#3a6ca3]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"d\", \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"alt\", \"Preview\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [1, \"inline-flex\", \"items-center\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"disabled\", \"click\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"fill-rule\", \"evenodd\", \"d\", \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"inline-flex\", \"items-center\", \"bg-[#ff6b69]\", \"hover:bg-[#e05554]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"disabled\", \"click\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [1, \"py-3\", \"flex\", \"justify-between\", \"items-center\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4f5fad]\", \"mr-3\"], [1, \"text-sm\", \"text-[#4f5fad]\"], [1, \"text-xs\", \"text-[#6d6870]\"], [1, \"text-xs\", \"text-[#bdc6cc]\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"My Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, ProfileComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProfileComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ProfileComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, ProfileComponent_div_7_Template, 142, 34, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.user);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.TitleCasePipe, i6.DatePipe],\n      styles: [\".profile-card[_ngcontent-%COMP%] {\\n\\n    overflow: hidden;\\n\\n    border-radius: 0.5rem;\\n\\n    border-width: 1px;\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n\\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n\\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\\n}\\n\\n.profile-card[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1))\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n\\n    position: relative\\n}\\n.profile-header[_ngcontent-%COMP%]   .cover-photo[_ngcontent-%COMP%] {\\n\\n    height: 8rem;\\n\\n    width: 100%;\\n\\n    object-fit: cover\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%] {\\n\\n    position: absolute;\\n\\n    bottom: 0px;\\n\\n    left: 1.5rem;\\n\\n    --tw-translate-y: 50%;\\n\\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .profile-photo[_ngcontent-%COMP%] {\\n\\n    display: flex;\\n\\n    height: 5rem;\\n\\n    width: 5rem;\\n\\n    align-items: center;\\n\\n    justify-content: center;\\n\\n    border-radius: 9999px;\\n\\n    border-width: 2px;\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(218 196 234 / var(--tw-bg-opacity, 1));\\n\\n    object-fit: cover;\\n\\n    font-size: 1.5rem;\\n\\n    line-height: 2rem;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n\\n    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n\\n    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n\\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .profile-photo[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(120 38 181 / var(--tw-bg-opacity, 1))\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%] {\\n\\n    position: absolute;\\n\\n    inset: 0px;\\n\\n    display: flex;\\n\\n    cursor: pointer;\\n\\n    align-items: center;\\n\\n    justify-content: center;\\n\\n    border-radius: 9999px;\\n\\n    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n\\n    --tw-bg-opacity: 0.5;\\n\\n    opacity: 0;\\n\\n    transition-property: opacity;\\n\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    transition-duration: 150ms\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%]:hover {\\n\\n    opacity: 1\\n}\\n.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n\\n    height: 1.25rem;\\n\\n    width: 1.25rem;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1))\\n}\\n\\n.profile-content[_ngcontent-%COMP%] {\\n\\n    padding-left: 1.5rem;\\n\\n    padding-right: 1.5rem;\\n\\n    padding-top: 3.5rem;\\n\\n    padding-bottom: 1.5rem\\n}\\n.profile-content[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n\\n    font-size: 1.25rem;\\n\\n    line-height: 1.75rem;\\n\\n    font-weight: 600;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(17 24 39 / var(--tw-text-opacity, 1))\\n}\\n.profile-content[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1))\\n}\\n.profile-content[_ngcontent-%COMP%]   .profile-role[_ngcontent-%COMP%] {\\n\\n    margin-bottom: 1rem;\\n\\n    font-size: 0.75rem;\\n\\n    line-height: 1rem;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(107 114 128 / var(--tw-text-opacity, 1))\\n}\\n.profile-content[_ngcontent-%COMP%]   .profile-role[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(156 163 175 / var(--tw-text-opacity, 1))\\n}\\n\\n.profile-section[_ngcontent-%COMP%] {\\n\\n    margin-top: 1.5rem\\n}\\n.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n\\n    margin-bottom: 0.75rem;\\n\\n    display: flex;\\n\\n    align-items: center;\\n\\n    font-size: 0.875rem;\\n\\n    line-height: 1.25rem;\\n\\n    font-weight: 500;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(55 65 81 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(209 213 219 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n\\n    margin-right: 0.375rem;\\n\\n    height: 1rem;\\n\\n    width: 1rem;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(218 196 234 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(120 38 181 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%] {\\n\\n    display: grid;\\n\\n    grid-template-columns: repeat(1, minmax(0, 1fr));\\n\\n    gap: 0.75rem\\n}\\n@media (min-width: 768px) {\\n\\n    .profile-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%] {\\n\\n        grid-template-columns: repeat(2, minmax(0, 1fr))\\n    }\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n\\n    border-radius: 0.375rem;\\n\\n    border-width: 1px;\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n\\n    padding: 0.75rem\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n\\n    background-color: rgb(55 65 81 / 0.5)\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%] {\\n\\n    font-size: 0.75rem;\\n\\n    line-height: 1rem;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(107 114 128 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(156 163 175 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%] {\\n\\n    font-size: 0.875rem;\\n\\n    line-height: 1.25rem;\\n\\n    font-weight: 500;\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(17 24 39 / var(--tw-text-opacity, 1))\\n}\\n.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1))\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n\\n    margin-top: 1.5rem;\\n\\n    display: flex;\\n\\n    flex-direction: column;\\n\\n    gap: 0.5rem\\n}\\n\\n@media (min-width: 640px) {\\n\\n    .action-buttons[_ngcontent-%COMP%] {\\n\\n        flex-direction: row\\n    }\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n\\n    border-radius: 0.375rem;\\n\\n    padding-left: 0.75rem;\\n\\n    padding-right: 0.75rem;\\n\\n    padding-top: 0.375rem;\\n\\n    padding-bottom: 0.375rem;\\n\\n    font-size: 0.875rem;\\n\\n    line-height: 1.25rem;\\n\\n    font-weight: 500;\\n\\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n\\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    transition-duration: 150ms\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n\\n    outline: 2px solid transparent;\\n\\n    outline-offset: 2px;\\n\\n    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n\\n    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n\\n    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n\\n    --tw-ring-offset-width: 1px\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(218 196 234 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n\\n    --tw-bg-opacity: 0.9\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(218 196 234 / var(--tw-ring-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(120 38 181 / var(--tw-bg-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(120 38 181 / var(--tw-ring-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n\\n    border-width: 1px;\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(55 65 81 / var(--tw-text-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:focus {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(229 231 235 / var(--tw-text-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n\\n    border-width: 1px;\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(220 38 38 / var(--tw-text-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:focus {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-border-opacity: 1;\\n\\n    border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\\n\\n    background-color: rgb(127 29 29 / 0.2);\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(248 113 113 / var(--tw-text-opacity, 1))\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    background-color: rgb(127 29 29 / 0.3)\\n}\\n.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-ring-opacity: 1;\\n\\n    --tw-ring-color: rgb(153 27 27 / var(--tw-ring-opacity, 1))\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n\\n    display: inline-flex;\\n\\n    align-items: center;\\n\\n    border-radius: 9999px;\\n\\n    padding-left: 0.375rem;\\n\\n    padding-right: 0.375rem;\\n\\n    padding-top: 0.125rem;\\n\\n    padding-bottom: 0.125rem;\\n\\n    font-size: 0.75rem;\\n\\n    line-height: 1rem;\\n\\n    font-weight: 500\\n}\\n.badge.verified[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(22 101 52 / var(--tw-text-opacity, 1))\\n}\\n.badge.verified[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    background-color: rgb(20 83 45 / 0.2);\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(134 239 172 / var(--tw-text-opacity, 1))\\n}\\n.badge.not-verified[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(31 41 55 / var(--tw-text-opacity, 1))\\n}\\n.badge.not-verified[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(209 213 219 / var(--tw-text-opacity, 1))\\n}\\n.badge.active[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(30 64 175 / var(--tw-text-opacity, 1))\\n}\\n.badge.active[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    background-color: rgb(30 58 138 / 0.2);\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(147 197 253 / var(--tw-text-opacity, 1))\\n}\\n.badge.inactive[_ngcontent-%COMP%] {\\n\\n    --tw-bg-opacity: 1;\\n\\n    background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(153 27 27 / var(--tw-text-opacity, 1))\\n}\\n.badge.inactive[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%] {\\n\\n    background-color: rgb(127 29 29 / 0.2);\\n\\n    --tw-text-opacity: 1;\\n\\n    color: rgb(252 165 165 / var(--tw-text-opacity, 1))\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r2", "message", "ɵɵproperty", "ctx_r4", "user", "profileImage", "trim", "ɵɵsanitizeUrl", "ctx_r5", "previewUrl", "ɵɵnamespaceSVG", "ɵɵlistener", "ProfileComponent_div_7_button_50_Template_button_click_0_listener", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "onUpload", "ɵɵtemplate", "ProfileComponent_div_7_button_50_span_1_Template", "ProfileComponent_div_7_button_50_span_2_Template", "ctx_r6", "uploadLoading", "ProfileComponent_div_7_button_51_Template_button_click_0_listener", "_r16", "ctx_r15", "removeProfileImage", "ProfileComponent_div_7_button_51_span_1_Template", "ProfileComponent_div_7_button_51_span_2_Template", "ctx_r7", "removeLoading", "activity_r17", "action", "target", "ctx_r8", "formatDate", "timestamp", "ProfileComponent_div_7_img_6_Template", "ProfileComponent_div_7_img_7_Template", "ɵɵnamespaceHTML", "ProfileComponent_div_7_Template_input_change_11_listener", "$event", "_r19", "ctx_r18", "onFileSelected", "ProfileComponent_div_7_button_50_Template", "ProfileComponent_div_7_button_51_Template", "ProfileComponent_div_7_Template_button_click_52_listener", "ctx_r20", "navigateTo", "ProfileComponent_div_7_Template_button_click_56_listener", "ctx_r21", "logout", "ProfileComponent_div_7_div_125_Template", "ProfileComponent_div_7_Template_button_click_134_listener", "ctx_r22", "ProfileComponent_div_7_Template_button_click_138_listener", "ctx_r23", "ctx_r3", "fullName", "ɵɵpipeBind1", "role", "ɵɵtextInterpolate", "email", "isActive", "verified", "ɵɵpipeBind2", "createdAt", "selectedImage", "stats", "totalUsers", "activeUsers", "inactiveUsers", "ɵɵstyleProp", "students", "teachers", "admins", "recentActivity", "ProfileComponent", "constructor", "authService", "dataService", "authAdminService", "authUserService", "router", "loading", "Date", "now", "ngOnInit", "loadUserData", "loadStats", "token", "localStorage", "getItem", "navigate", "getProfile", "subscribe", "next", "res", "err", "getAllUsers", "users", "length", "filter", "u", "event", "input", "files", "file", "validTypes", "includes", "type", "resetFileInput", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "fileInput", "document", "getElementById", "value", "console", "log", "uploadProfileImage", "pipe", "response", "profileImageURL", "imageUrl", "updateCurrentUser", "image", "setItem", "setTimeout", "confirm", "path", "clearAuthData", "queryParams", "replaceUrl", "date", "toLocaleString", "getInitials", "name", "split", "map", "n", "join", "toUpperCase", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "DataService", "i3", "AuthadminService", "i4", "AuthuserService", "i5", "Router", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_4_Template", "ProfileComponent_div_5_Template", "ProfileComponent_div_6_Template", "ProfileComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { AuthadminService } from '@app/services/authadmin.service';\r\nimport { AuthuserService } from '@app/services/authuser.service';\r\nimport { DataService } from '@app/services/data.service';\r\nimport { finalize } from 'rxjs';\r\nimport { AuthService } from 'src/app/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.css'],\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  user: any = null;\r\n  selectedImage: File | null = null;\r\n  message = '';\r\n  error = '';\r\n  loading = true;\r\n  uploadLoading = false;\r\n  removeLoading = false;\r\n  stats = {\r\n    totalUsers: 0,\r\n    activeUsers: 0,\r\n    inactiveUsers: 0,\r\n    students: 0,\r\n    teachers: 0,\r\n    admins: 0,\r\n  };\r\n  recentActivity = [\r\n    {\r\n      action: 'User Deactivated',\r\n      target: '<PERSON>',\r\n      timestamp: new Date(Date.now() - 3600000),\r\n    },\r\n    {\r\n      action: 'Role Changed',\r\n      target: '<PERSON>',\r\n      timestamp: new Date(Date.now() - 7200000),\r\n    },\r\n    {\r\n      action: 'User Added',\r\n      target: '<PERSON>',\r\n      timestamp: new Date(Date.now() - 86400000),\r\n    },\r\n  ];\r\n  previewUrl: string | ArrayBuffer | null = null;\r\n  constructor(\r\n    private authService: AuthService,\r\n    private dataService: DataService,\r\n    private authAdminService: AuthadminService,\r\n    private authUserService: AuthuserService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadUserData();\r\n    this.loadStats();\r\n  }\r\n\r\n  loadUserData(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (!token) {\r\n      this.router.navigate(['/admin/login']);\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.authService.getProfile(token).subscribe({\r\n      next: (res: any) => {\r\n        this.user = res;\r\n        this.loading = false;\r\n      },\r\n      error: (err: { error: { message: string } }) => {\r\n        this.error = err.error?.message || 'Failed to load profile.';\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  loadStats(): void {\r\n    const token = localStorage.getItem('token');\r\n    if (!token) return;\r\n\r\n    this.authService.getAllUsers(token).subscribe({\r\n      next: (res: any) => {\r\n        const users = res as any[];\r\n        this.stats.totalUsers = users.length;\r\n        this.stats.activeUsers = users.filter(\r\n          (u) => u.isActive !== false\r\n        ).length;\r\n        this.stats.inactiveUsers = users.filter(\r\n          (u) => u.isActive === false\r\n        ).length;\r\n        this.stats.students = users.filter((u) => u.role === 'student').length;\r\n        this.stats.teachers = users.filter((u) => u.role === 'teacher').length;\r\n        this.stats.admins = users.filter((u) => u.role === 'admin').length;\r\n      },\r\n      error: () => {\r\n        // Silently fail, stats are not critical\r\n      },\r\n    });\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files?.length) {\r\n      const file = input.files[0];\r\n\r\n      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n      if (!validTypes.includes(file.type)) {\r\n        this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      if (file.size > 2 * 1024 * 1024) {\r\n        this.error = \"L'image ne doit pas dépasser 2MB\";\r\n        this.resetFileInput();\r\n        return;\r\n      }\r\n\r\n      this.selectedImage = file;\r\n      this.error = '';\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        this.previewUrl = (e.target?.result as string) || null;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n  private resetFileInput(): void {\r\n    this.selectedImage = null;\r\n    this.previewUrl = null;\r\n    const fileInput = document.getElementById(\r\n      'profile-upload'\r\n    ) as HTMLInputElement;\r\n    if (fileInput) fileInput.value = '';\r\n  }\r\n\r\n  onUpload(): void {\r\n    if (!this.selectedImage) return;\r\n\r\n    this.uploadLoading = true; // Activer l'état de chargement\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    console.log('Upload started, uploadLoading:', this.uploadLoading);\r\n\r\n    this.dataService\r\n      .uploadProfileImage(this.selectedImage)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.uploadLoading = false;\r\n          console.log('Upload finished, uploadLoading:', this.uploadLoading);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message = response.message || 'Profile updated successfully';\r\n\r\n          // Update both properties to ensure consistency\r\n          this.user.profileImageURL = response.imageUrl;\r\n          this.user.profileImage = response.imageUrl;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: response.imageUrl,\r\n            image: response.imageUrl,\r\n          });\r\n\r\n          this.selectedImage = null;\r\n          this.previewUrl = null;\r\n          this.resetFileInput();\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Upload failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n  removeProfileImage(): void {\r\n    if (!confirm('Are you sure you want to remove your profile picture?'))\r\n      return;\r\n\r\n    this.removeLoading = true;\r\n    this.message = '';\r\n    this.error = '';\r\n\r\n    this.dataService\r\n      .removeProfileImage()\r\n      .pipe(finalize(() => (this.removeLoading = false)))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.message =\r\n            response.message || 'Profile picture removed successfully';\r\n\r\n          // Update both properties to ensure consistency\r\n          this.user.profileImageURL = null;\r\n          this.user.profileImage = null;\r\n\r\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\r\n          this.dataService.updateCurrentUser({\r\n            profileImage: 'assets/images/default-profile.png',\r\n            image: 'assets/images/default-profile.png',\r\n          });\r\n\r\n          if (response.token) {\r\n            localStorage.setItem('token', response.token);\r\n          }\r\n\r\n          // Auto-hide message after 3 seconds\r\n          setTimeout(() => {\r\n            this.message = '';\r\n          }, 3000);\r\n        },\r\n        error: (err: { error: { message: string } }) => {\r\n          this.error = err.error?.message || 'Removal failed';\r\n          // Auto-hide error after 3 seconds\r\n          setTimeout(() => {\r\n            this.error = '';\r\n          }, 3000);\r\n        },\r\n      });\r\n  }\r\n\r\n  navigateTo(path: string): void {\r\n    this.router.navigate([path]);\r\n  }\r\n\r\n  logout(): void {\r\n    this.authUserService.logout().subscribe({\r\n      next: () => {\r\n        this.authUserService.clearAuthData();\r\n        this.authAdminService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/admin/login'], {\r\n            queryParams: { message: 'Déconnexion réussie' },\r\n            replaceUrl: true,\r\n          });\r\n        }, 100);\r\n      },\r\n      error: (err: any) => {\r\n        console.error('Logout error:', err);\r\n        this.authUserService.clearAuthData();\r\n        this.authAdminService.clearAuthData();\r\n        setTimeout(() => {\r\n          this.router.navigate(['/admin/login'], {});\r\n        }, 100);\r\n      },\r\n    });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    return new Date(date).toLocaleString();\r\n  }\r\n\r\n  getInitials(name: string): string {\r\n    if (!name) return 'A';\r\n    return name\r\n      .split(' ')\r\n      .map((n) => n[0])\r\n      .join('')\r\n      .toUpperCase();\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] min-h-screen\">\r\n  <!-- Page Heading -->\r\n  <div\r\n    class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-6\"\r\n  >\r\n    <h1 class=\"text-2xl font-bold text-[#4f5fad] mb-2 md:mb-0\">My Profile</h1>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"loading\" class=\"flex justify-center items-center py-20\">\r\n    <div\r\n      class=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad]\"\r\n    ></div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div\r\n    *ngIf=\"error\"\r\n    class=\"bg-[#ff6b69]/10 border border-[#ff6b69] text-[#ff6b69] px-4 py-3 rounded-lg mb-6 animate-pulse\"\r\n  >\r\n    {{ error }}\r\n  </div>\r\n\r\n  <!-- Success Message -->\r\n  <div\r\n    *ngIf=\"message\"\r\n    class=\"bg-[#afcf75]/10 border border-[#afcf75] text-[#2a5a03] px-4 py-3 rounded-lg mb-6 animate-pulse\"\r\n  >\r\n    {{ message }}\r\n  </div>\r\n\r\n  <!-- Admin Profile -->\r\n  <div\r\n    *ngIf=\"!loading && user\"\r\n    class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\"\r\n  >\r\n    <!-- Profile Card -->\r\n    <div class=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n      <div class=\"relative\">\r\n        <img\r\n          src=\"https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80\"\r\n          alt=\"Cover\"\r\n          class=\"w-full h-32 object-cover\"\r\n        />\r\n        <div class=\"absolute left-0 right-0 -bottom-12 flex justify-center\">\r\n          <div\r\n            class=\"h-24 w-24 rounded-full border-4 border-white overflow-hidden flex items-center justify-center\"\r\n            style=\"min-height: 96px; min-width: 96px\"\r\n          >\r\n            <img\r\n              *ngIf=\"!previewUrl || uploadLoading\"\r\n              [src]=\"\r\n                user.profileImage &&\r\n                user.profileImage !== 'null' &&\r\n                user.profileImage.trim() !== ''\r\n                  ? user.profileImage\r\n                  : 'assets/images/default-profile.png'\r\n              \"\r\n              alt=\"Profile\"\r\n              class=\"h-full w-full object-cover\"\r\n            />\r\n            <img\r\n              *ngIf=\"previewUrl && !uploadLoading\"\r\n              [src]=\"previewUrl\"\r\n              alt=\"Preview\"\r\n              class=\"h-full w-full object-cover\"\r\n            />\r\n          </div>\r\n          <label\r\n            for=\"profile-upload\"\r\n            class=\"absolute bottom-0 right-0 bg-[#7826b5] text-white p-1.5 rounded-full cursor-pointer hover:bg-[#5f1d8f] transition-colors\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </label>\r\n          <input\r\n            type=\"file\"\r\n            id=\"profile-upload\"\r\n            class=\"hidden\"\r\n            accept=\"image/*\"\r\n            (change)=\"onFileSelected($event)\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"p-5 pt-16 text-center\">\r\n        <h2 class=\"text-xl font-bold text-[#4f5fad] mb-1\">\r\n          {{ user.fullName }}\r\n        </h2>\r\n        <p class=\"mb-4\">\r\n          <span\r\n            class=\"px-2 py-1 text-xs rounded-full bg-[#4f5fad]/10 text-[#4f5fad] font-medium\"\r\n          >\r\n            {{ user.role | titlecase }}\r\n          </span>\r\n        </p>\r\n\r\n        <!-- Personal Information -->\r\n        <div class=\"mt-6 border-t border-[#edf1f4] pt-4\">\r\n          <h3\r\n            class=\"flex items-center justify-center text-[#4f5fad] font-semibold mb-4\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-5 w-5 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Account Information\r\n          </h3>\r\n          <div class=\"space-y-4\">\r\n            <div class=\"text-left px-2\">\r\n              <div class=\"text-sm font-medium text-[#6d6870]\">Email</div>\r\n              <div class=\"text-[#4f5fad]\">{{ user.email }}</div>\r\n            </div>\r\n            <div class=\"flex space-x-4\">\r\n              <div class=\"flex-1 text-left px-2\">\r\n                <div class=\"text-sm font-medium text-[#6d6870]\">Status</div>\r\n                <div>\r\n                  <span\r\n                    class=\"px-2 py-1 text-xs rounded-full\"\r\n                    [ngClass]=\"\r\n                      user.isActive !== false\r\n                        ? 'bg-[#afcf75]/10 text-[#2a5a03]'\r\n                        : 'bg-[#ff6b69]/10 text-[#ff6b69]'\r\n                    \"\r\n                  >\r\n                    {{ user.isActive !== false ? \"Active\" : \"Inactive\" }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"flex-1 text-left px-2\">\r\n                <div class=\"text-sm font-medium text-[#6d6870]\">\r\n                  Verification\r\n                </div>\r\n                <div>\r\n                  <span\r\n                    class=\"px-2 py-1 text-xs rounded-full\"\r\n                    [ngClass]=\"\r\n                      user.verified\r\n                        ? 'bg-[#afcf75]/10 text-[#2a5a03]'\r\n                        : 'bg-[#ff6b69]/10 text-[#ff6b69]'\r\n                    \"\r\n                  >\r\n                    {{ user.verified ? \"Verified\" : \"Not Verified\" }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"text-left px-2\">\r\n              <div class=\"text-sm font-medium text-[#6d6870]\">Member Since</div>\r\n              <div class=\"text-[#4f5fad]\">\r\n                {{ user.createdAt | date : \"mediumDate\" }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"mt-6 flex flex-wrap justify-center gap-3\">\r\n          <!-- upload image -->\r\n          <button\r\n            *ngIf=\"selectedImage\"\r\n            (click)=\"onUpload()\"\r\n            class=\"inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n            [disabled]=\"uploadLoading\"\r\n          >\r\n            <span *ngIf=\"!uploadLoading\" class=\"flex items-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-4 w-4 mr-2\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  fill-rule=\"evenodd\"\r\n                  d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\"\r\n                  clip-rule=\"evenodd\"\r\n                />\r\n              </svg>\r\n              Upload\r\n            </span>\r\n            <!-- État de chargement -->\r\n            <span\r\n              *ngIf=\"uploadLoading\"\r\n              class=\"flex items-center justify-center\"\r\n            >\r\n              <svg\r\n                class=\"animate-spin mr-2 h-4 w-4 text-white\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <circle\r\n                  class=\"opacity-25\"\r\n                  cx=\"12\"\r\n                  cy=\"12\"\r\n                  r=\"10\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"4\"\r\n                ></circle>\r\n                <path\r\n                  class=\"opacity-75\"\r\n                  fill=\"currentColor\"\r\n                  d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                ></path>\r\n              </svg>\r\n              Uploading...\r\n            </span>\r\n          </button>\r\n          <!-- remove image -->\r\n          <button\r\n            *ngIf=\"user.profileImage\"\r\n            (click)=\"removeProfileImage()\"\r\n            class=\"inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n            [disabled]=\"removeLoading\"\r\n          >\r\n            <span *ngIf=\"!removeLoading\" class=\"flex items-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-4 w-4 mr-2\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  fill-rule=\"evenodd\"\r\n                  d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\"\r\n                  clip-rule=\"evenodd\"\r\n                />\r\n              </svg>\r\n              Remove\r\n            </span>\r\n            <span\r\n              *ngIf=\"removeLoading\"\r\n              class=\"flex items-center justify-center\"\r\n            >\r\n              <svg\r\n                class=\"animate-spin mr-2 h-4 w-4 text-white\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <circle\r\n                  class=\"opacity-25\"\r\n                  cx=\"12\"\r\n                  cy=\"12\"\r\n                  r=\"10\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"4\"\r\n                ></circle>\r\n                <path\r\n                  class=\"opacity-75\"\r\n                  fill=\"currentColor\"\r\n                  d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                ></path>\r\n              </svg>\r\n              Removing\r\n            </span>\r\n          </button>\r\n          <!-- change password -->\r\n          <button\r\n            (click)=\"navigateTo('/change-password')\"\r\n            class=\"inline-flex items-center bg-[#4f5fad] hover:bg-[#3d4a85] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Password\r\n          </button>\r\n          <!-- logout -->\r\n          <button\r\n            (click)=\"logout()\"\r\n            class=\"inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-4-4H3zm6 11a1 1 0 11-2 0 1 1 0 012 0zm2-5.5a.5.5 0 00-.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 010-1H9V9.5A1.5 1.5 0 0110.5 8h.5a.5.5 0 01.5.5z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Logout\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Admin Dashboard Summary -->\r\n    <div class=\"lg:col-span-2 space-y-6\">\r\n      <!-- Stats Cards -->\r\n      <div class=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n        <div class=\"p-5 border-b border-[#bdc6cc]\">\r\n          <h3 class=\"flex items-center font-bold text-[#4f5fad]\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-5 w-5 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                d=\"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\r\n              />\r\n            </svg>\r\n            User Statistics\r\n          </h3>\r\n        </div>\r\n        <div class=\"p-5\">\r\n          <!-- User Status -->\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\r\n            <div>\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"text-sm font-medium text-[#6d6870]\">\r\n                  Total Users\r\n                </div>\r\n                <div class=\"text-xl font-semibold text-[#4f5fad]\">\r\n                  {{ stats.totalUsers }}\r\n                </div>\r\n              </div>\r\n              <div class=\"w-full bg-[#edf1f4] rounded-full h-2.5\">\r\n                <div\r\n                  class=\"bg-[#4f5fad] h-2.5 rounded-full\"\r\n                  style=\"width: 100%\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"text-sm font-medium text-[#6d6870]\">Status</div>\r\n                <div class=\"flex space-x-3 text-sm\">\r\n                  <span class=\"text-[#afcf75] font-medium\"\r\n                    >{{ stats.activeUsers }} Active</span\r\n                  >\r\n                  <span class=\"text-[#bdc6cc]\">|</span>\r\n                  <span class=\"text-[#ff6b69] font-medium\"\r\n                    >{{ stats.inactiveUsers }} Inactive</span\r\n                  >\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"w-full bg-[#edf1f4] rounded-full h-2.5 overflow-hidden flex\"\r\n              >\r\n                <div\r\n                  class=\"bg-[#afcf75] h-2.5\"\r\n                  [style.width.%]=\"\r\n                    stats.totalUsers\r\n                      ? (stats.activeUsers / stats.totalUsers) * 100\r\n                      : 0\r\n                  \"\r\n                ></div>\r\n                <div\r\n                  class=\"bg-[#ff6b69] h-2.5\"\r\n                  [style.width.%]=\"\r\n                    stats.totalUsers\r\n                      ? (stats.inactiveUsers / stats.totalUsers) * 100\r\n                      : 0\r\n                  \"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- User Roles -->\r\n          <div>\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <div class=\"text-sm font-medium text-[#6d6870]\">User Roles</div>\r\n              <div class=\"flex space-x-3 text-sm\">\r\n                <span class=\"text-[#4a89ce] font-medium\">{{\r\n                  stats.students\r\n                }}</span>\r\n                <span class=\"text-[#bdc6cc]\">|</span>\r\n                <span class=\"text-[#7826b5] font-medium\">{{\r\n                  stats.teachers\r\n                }}</span>\r\n                <span class=\"text-[#bdc6cc]\">|</span>\r\n                <span class=\"text-[#4f5fad] font-medium\">{{\r\n                  stats.admins\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n            <div\r\n              class=\"w-full bg-[#edf1f4] rounded-full h-2.5 overflow-hidden flex\"\r\n            >\r\n              <div\r\n                class=\"bg-[#4a89ce] h-2.5\"\r\n                [style.width.%]=\"\r\n                  stats.totalUsers\r\n                    ? (stats.students / stats.totalUsers) * 100\r\n                    : 0\r\n                \"\r\n              ></div>\r\n              <div\r\n                class=\"bg-[#7826b5] h-2.5\"\r\n                [style.width.%]=\"\r\n                  stats.totalUsers\r\n                    ? (stats.teachers / stats.totalUsers) * 100\r\n                    : 0\r\n                \"\r\n              ></div>\r\n              <div\r\n                class=\"bg-[#4f5fad] h-2.5\"\r\n                [style.width.%]=\"\r\n                  stats.totalUsers ? (stats.admins / stats.totalUsers) * 100 : 0\r\n                \"\r\n              ></div>\r\n            </div>\r\n            <div class=\"flex justify-between mt-2 text-xs text-[#6d6870]\">\r\n              <span>Students</span>\r\n              <span>Teachers</span>\r\n              <span>Admins</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Recent Activity -->\r\n      <div class=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n        <div class=\"p-5 border-b border-[#bdc6cc]\">\r\n          <h3 class=\"flex items-center font-bold text-[#4f5fad]\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-5 w-5 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Recent Activity\r\n          </h3>\r\n        </div>\r\n        <div class=\"p-5\">\r\n          <div class=\"divide-y divide-[#edf1f4]\">\r\n            <div\r\n              *ngFor=\"let activity of recentActivity\"\r\n              class=\"py-3 flex justify-between items-center\"\r\n            >\r\n              <div class=\"flex items-center\">\r\n                <div class=\"w-2 h-2 rounded-full bg-[#4f5fad] mr-3\"></div>\r\n                <div>\r\n                  <div class=\"text-sm text-[#4f5fad]\">\r\n                    {{ activity.action }}\r\n                  </div>\r\n                  <div class=\"text-xs text-[#6d6870]\">\r\n                    {{ activity.target }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-xs text-[#bdc6cc]\">\r\n                {{ formatDate(activity.timestamp) }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Quick Actions -->\r\n      <div class=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n        <div class=\"p-5 border-b border-[#bdc6cc]\">\r\n          <h3 class=\"flex items-center font-bold text-[#4f5fad]\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-5 w-5 mr-2\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fill-rule=\"evenodd\"\r\n                d=\"M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z\"\r\n                clip-rule=\"evenodd\"\r\n              />\r\n            </svg>\r\n            Quick Actions\r\n          </h3>\r\n        </div>\r\n        <div class=\"p-5\">\r\n          <div class=\"flex flex-wrap gap-3\">\r\n            <button\r\n              (click)=\"navigateTo('/admin/dashboard')\"\r\n              class=\"inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-4 w-4 mr-2\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  d=\"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z\"\r\n                />\r\n              </svg>\r\n              Users\r\n            </button>\r\n            <button\r\n              (click)=\"navigateTo('/')\"\r\n              class=\"inline-flex items-center bg-[#4a89ce] hover:bg-[#3a6ca3] text-white px-4 py-2 rounded-lg shadow transition-all\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                class=\"h-4 w-4 mr-2\"\r\n                viewBox=\"0 0 20 20\"\r\n                fill=\"currentColor\"\r\n              >\r\n                <path\r\n                  d=\"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\r\n                />\r\n              </svg>\r\n              Home\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n<!-- /.container-fluid -->\r\n"], "mappings": "AAKA,SAASA,QAAQ,QAAQ,MAAM;;;;;;;;;;ICK7BC,EAAA,CAAAC,cAAA,aAAoE;IAClED,EAAA,CAAAE,SAAA,aAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,aAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAGAR,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;;;IAoBUV,EAAA,CAAAE,SAAA,cAWE;;;;IATAF,EAAA,CAAAW,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,YAAA,IAAAF,MAAA,CAAAC,IAAA,CAAAC,YAAA,eAAAF,MAAA,CAAAC,IAAA,CAAAC,YAAA,CAAAC,IAAA,YAAAH,MAAA,CAAAC,IAAA,CAAAC,YAAA,wCAAAd,EAAA,CAAAgB,aAAA,CAMC;;;;;IAIHhB,EAAA,CAAAE,SAAA,cAKE;;;;IAHAF,EAAA,CAAAW,UAAA,QAAAM,MAAA,CAAAC,UAAA,EAAAlB,EAAA,CAAAgB,aAAA,CAAkB;;;;;IAuHpBhB,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,eAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAEPH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,iBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA/CTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAoB,UAAA,mBAAAC,kEAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAIpB3B,EAAA,CAAA4B,UAAA,IAAAC,gDAAA,mBAcO;IAEP7B,EAAA,CAAA4B,UAAA,IAAAE,gDAAA,mBAyBO;IACT9B,EAAA,CAAAG,YAAA,EAAS;;;;IA5CPH,EAAA,CAAAW,UAAA,aAAAoB,MAAA,CAAAC,aAAA,CAA0B;IAEnBhC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAW,UAAA,UAAAoB,MAAA,CAAAC,aAAA,CAAoB;IAiBxBhC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,SAAAoB,MAAA,CAAAC,aAAA,CAAmB;;;;;IAiCtBhC,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,eAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,iBAOU;IAMZF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA9CTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAoB,UAAA,mBAAAa,kEAAA;MAAAjC,EAAA,CAAAsB,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAS,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAI9BpC,EAAA,CAAA4B,UAAA,IAAAS,gDAAA,mBAcO;IACPrC,EAAA,CAAA4B,UAAA,IAAAU,gDAAA,mBAyBO;IACTtC,EAAA,CAAAG,YAAA,EAAS;;;;IA3CPH,EAAA,CAAAW,UAAA,aAAA4B,MAAA,CAAAC,aAAA,CAA0B;IAEnBxC,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAW,UAAA,UAAA4B,MAAA,CAAAC,aAAA,CAAoB;IAgBxBxC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,SAAA4B,MAAA,CAAAC,aAAA,CAAmB;;;;;IAyNtBxC,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,SAAA,cAA0D;IAC1DF,EAAA,CAAAC,cAAA,UAAK;IAEDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IATAH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmC,YAAA,CAAAC,MAAA,MACF;IAEE1C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmC,YAAA,CAAAE,MAAA,MACF;IAIF3C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsC,MAAA,CAAAC,UAAA,CAAAJ,YAAA,CAAAK,SAAA,OACF;;;;;;IAlcZ9C,EAAA,CAAAC,cAAA,cAGC;IAIKD,EAAA,CAAAE,SAAA,cAIE;IACFF,EAAA,CAAAC,cAAA,cAAoE;IAKhED,EAAA,CAAA4B,UAAA,IAAAmB,qCAAA,kBAWE;IACF/C,EAAA,CAAA4B,UAAA,IAAAoB,qCAAA,kBAKE;IACJhD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,gBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAiD,eAAA,EAME;IANFjD,EAAA,CAAAC,cAAA,iBAME;IADAD,EAAA,CAAAoB,UAAA,oBAAA8B,yDAAAC,MAAA;MAAAnD,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAyB,aAAA;MAAA,OAAUzB,EAAA,CAAA0B,WAAA,CAAA2B,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IALnCnD,EAAA,CAAAG,YAAA,EAME;IAGNH,EAAA,CAAAC,cAAA,eAAmC;IAE/BD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAgB;IAIZD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAAiD;IAI7CD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,gBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAiD,eAAA,EAAuB;IAAvBjD,EAAA,CAAAC,cAAA,eAAuB;IAE6BD,EAAA,CAAAI,MAAA,aAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEpDH,EAAA,CAAAC,cAAA,eAA4B;IAEwBD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,WAAK;IASDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGXH,EAAA,CAAAC,cAAA,eAAmC;IAE/BD,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IASDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIbH,EAAA,CAAAC,cAAA,eAA4B;IACsBD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAI,MAAA,IACF;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsD;IAEpDD,EAAA,CAAA4B,UAAA,KAAA2B,yCAAA,qBAgDS;IAETvD,EAAA,CAAA4B,UAAA,KAAA4B,yCAAA,qBA+CS;IAETxD,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAoB,UAAA,mBAAAqC,yDAAA;MAAAzD,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAM,OAAA,GAAA1D,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAgC,OAAA,CAAAC,UAAA,CAAW,kBAAkB,CAAC;IAAA,EAAC;IAGxC3D,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,gBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAiD,eAAA,EAGC;IAHDjD,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAoB,UAAA,mBAAAwC,yDAAA;MAAA5D,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAS,OAAA,GAAA7D,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAmC,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlB9D,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,gBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAAiD,eAAA,EAAqC;IAArCjD,EAAA,CAAAC,cAAA,eAAqC;IAK7BD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,SAAA,gBAEE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAiD,eAAA,EAAiB;IAAjBjD,EAAA,CAAAC,cAAA,eAAiB;IAMPD,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAE,SAAA,eAGO;IACTF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,WAAK;IAE+CD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,eAAoC;IAE/BD,EAAA,CAAAI,MAAA,IAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAChC;IACDH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBACG;IAAAD,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EACpC;IAGLH,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAE,SAAA,eAOO;IASTF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,WAAK;IAE+CD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,eAAoC;IACOD,EAAA,CAAAI,MAAA,IAEvC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAI,MAAA,KAEvC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAI,MAAA,UAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAI,MAAA,KAEvC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGbH,EAAA,CAAAC,cAAA,gBAEC;IACCD,EAAA,CAAAE,SAAA,gBAOO;IAeTF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA8D;IACtDD,EAAA,CAAAI,MAAA,iBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAI,MAAA,iBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAI,MAAA,eAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAO3BH,EAAA,CAAAC,cAAA,gBAA2D;IAGrDD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAE,SAAA,iBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAiD,eAAA,EAAiB;IAAjBjD,EAAA,CAAAC,cAAA,gBAAiB;IAEbD,EAAA,CAAA4B,UAAA,MAAAmC,uCAAA,mBAkBM;IACR/D,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,gBAA2D;IAGrDD,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAE,SAAA,iBAIE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAiD,eAAA,EAAiB;IAAjBjD,EAAA,CAAAC,cAAA,gBAAiB;IAGXD,EAAA,CAAAoB,UAAA,mBAAA4C,0DAAA;MAAAhE,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAa,OAAA,GAAAjE,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAuC,OAAA,CAAAN,UAAA,CAAW,kBAAkB,CAAC;IAAA,EAAC;IAGxC3D,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAE,SAAA,iBAEE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAiD,eAAA,EAGC;IAHDjD,EAAA,CAAAC,cAAA,mBAGC;IAFCD,EAAA,CAAAoB,UAAA,mBAAA8C,0DAAA;MAAAlE,EAAA,CAAAsB,aAAA,CAAA8B,IAAA;MAAA,MAAAe,OAAA,GAAAnE,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAyC,OAAA,CAAAR,UAAA,CAAW,GAAG,CAAC;IAAA,EAAC;IAGzB3D,EAAA,CAAAmB,cAAA,EAKC;IALDnB,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAE,SAAA,iBAEE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IA1eNH,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAW,UAAA,UAAAyD,MAAA,CAAAlD,UAAA,IAAAkD,MAAA,CAAApC,aAAA,CAAkC;IAYlChC,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAW,UAAA,SAAAyD,MAAA,CAAAlD,UAAA,KAAAkD,MAAA,CAAApC,aAAA,CAAkC;IAkCvChC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8D,MAAA,CAAAvD,IAAA,CAAAwD,QAAA,MACF;IAKIrE,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAsE,WAAA,SAAAF,MAAA,CAAAvD,IAAA,CAAA0D,IAAA,OACF;IAyBgCvE,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAwE,iBAAA,CAAAJ,MAAA,CAAAvD,IAAA,CAAA4D,KAAA,CAAgB;IAQtCzE,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAW,UAAA,YAAAyD,MAAA,CAAAvD,IAAA,CAAA6D,QAAA,iFAIC;IAED1E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8D,MAAA,CAAAvD,IAAA,CAAA6D,QAAA,wCACF;IAUE1E,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAW,UAAA,YAAAyD,MAAA,CAAAvD,IAAA,CAAA8D,QAAA,uEAIC;IAED3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8D,MAAA,CAAAvD,IAAA,CAAA8D,QAAA,oCACF;IAOF3E,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA4E,WAAA,SAAAR,MAAA,CAAAvD,IAAA,CAAAgE,SAAA,qBACF;IASD7E,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,SAAAyD,MAAA,CAAAU,aAAA,CAAmB;IAkDnB9E,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAW,UAAA,SAAAyD,MAAA,CAAAvD,IAAA,CAAAC,YAAA,CAAuB;IAqHlBd,EAAA,CAAAK,SAAA,IACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8D,MAAA,CAAAW,KAAA,CAAAC,UAAA,MACF;IAeKhF,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,kBAAA,KAAA8D,MAAA,CAAAW,KAAA,CAAAE,WAAA,YAA8B;IAI9BjF,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,kBAAA,KAAA8D,MAAA,CAAAW,KAAA,CAAAG,aAAA,cAAkC;IASrClF,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAmF,WAAA,UAAAf,MAAA,CAAAW,KAAA,CAAAC,UAAA,GAAAZ,MAAA,CAAAW,KAAA,CAAAE,WAAA,GAAAb,MAAA,CAAAW,KAAA,CAAAC,UAAA,gBAIC;IAIDhF,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAmF,WAAA,UAAAf,MAAA,CAAAW,KAAA,CAAAC,UAAA,GAAAZ,MAAA,CAAAW,KAAA,CAAAG,aAAA,GAAAd,MAAA,CAAAW,KAAA,CAAAC,UAAA,gBAIC;IAWsChF,EAAA,CAAAK,SAAA,GAEvC;IAFuCL,EAAA,CAAAwE,iBAAA,CAAAJ,MAAA,CAAAW,KAAA,CAAAK,QAAA,CAEvC;IAEuCpF,EAAA,CAAAK,SAAA,GAEvC;IAFuCL,EAAA,CAAAwE,iBAAA,CAAAJ,MAAA,CAAAW,KAAA,CAAAM,QAAA,CAEvC;IAEuCrF,EAAA,CAAAK,SAAA,GAEvC;IAFuCL,EAAA,CAAAwE,iBAAA,CAAAJ,MAAA,CAAAW,KAAA,CAAAO,MAAA,CAEvC;IAQFtF,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAmF,WAAA,UAAAf,MAAA,CAAAW,KAAA,CAAAC,UAAA,GAAAZ,MAAA,CAAAW,KAAA,CAAAK,QAAA,GAAAhB,MAAA,CAAAW,KAAA,CAAAC,UAAA,gBAIC;IAIDhF,EAAA,CAAAK,SAAA,GAIC;IAJDL,EAAA,CAAAmF,WAAA,UAAAf,MAAA,CAAAW,KAAA,CAAAC,UAAA,GAAAZ,MAAA,CAAAW,KAAA,CAAAM,QAAA,GAAAjB,MAAA,CAAAW,KAAA,CAAAC,UAAA,gBAIC;IAIDhF,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAmF,WAAA,UAAAf,MAAA,CAAAW,KAAA,CAAAC,UAAA,GAAAZ,MAAA,CAAAW,KAAA,CAAAO,MAAA,GAAAlB,MAAA,CAAAW,KAAA,CAAAC,UAAA,gBAEC;IAkCkBhF,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAAW,UAAA,YAAAyD,MAAA,CAAAmB,cAAA,CAAiB;;;ADtcpD,OAAM,MAAOC,gBAAgB;EAkC3BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,gBAAkC,EAClCC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAtChB,KAAAjF,IAAI,GAAQ,IAAI;IAChB,KAAAiE,aAAa,GAAgB,IAAI;IACjC,KAAApE,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IACV,KAAAuF,OAAO,GAAG,IAAI;IACd,KAAA/D,aAAa,GAAG,KAAK;IACrB,KAAAQ,aAAa,GAAG,KAAK;IACrB,KAAAuC,KAAK,GAAG;MACNC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBE,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;KACT;IACD,KAAAC,cAAc,GAAG,CACf;MACE7C,MAAM,EAAE,kBAAkB;MAC1BC,MAAM,EAAE,UAAU;MAClBG,SAAS,EAAE,IAAIkD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO;KACzC,EACD;MACEvD,MAAM,EAAE,cAAc;MACtBC,MAAM,EAAE,YAAY;MACpBG,SAAS,EAAE,IAAIkD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO;KACzC,EACD;MACEvD,MAAM,EAAE,YAAY;MACpBC,MAAM,EAAE,gBAAgB;MACxBG,SAAS,EAAE,IAAIkD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,QAAQ;KAC1C,CACF;IACD,KAAA/E,UAAU,GAAgC,IAAI;EAO3C;EAEHgF,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAD,YAAYA,CAAA;IACV,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACV,IAAI,CAACP,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC;;IAGF,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,WAAW,CAACe,UAAU,CAACJ,KAAK,CAAC,CAACK,SAAS,CAAC;MAC3CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC/F,IAAI,GAAG+F,GAAG;QACf,IAAI,CAACb,OAAO,GAAG,KAAK;MACtB,CAAC;MACDvF,KAAK,EAAGqG,GAAmC,IAAI;QAC7C,IAAI,CAACrG,KAAK,GAAGqG,GAAG,CAACrG,KAAK,EAAEE,OAAO,IAAI,yBAAyB;QAC5D,IAAI,CAACqF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAK,SAASA,CAAA;IACP,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;IAEZ,IAAI,CAACX,WAAW,CAACoB,WAAW,CAACT,KAAK,CAAC,CAACK,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMG,KAAK,GAAGH,GAAY;QAC1B,IAAI,CAAC7B,KAAK,CAACC,UAAU,GAAG+B,KAAK,CAACC,MAAM;QACpC,IAAI,CAACjC,KAAK,CAACE,WAAW,GAAG8B,KAAK,CAACE,MAAM,CAClCC,CAAC,IAAKA,CAAC,CAACxC,QAAQ,KAAK,KAAK,CAC5B,CAACsC,MAAM;QACR,IAAI,CAACjC,KAAK,CAACG,aAAa,GAAG6B,KAAK,CAACE,MAAM,CACpCC,CAAC,IAAKA,CAAC,CAACxC,QAAQ,KAAK,KAAK,CAC5B,CAACsC,MAAM;QACR,IAAI,CAACjC,KAAK,CAACK,QAAQ,GAAG2B,KAAK,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,IAAI,KAAK,SAAS,CAAC,CAACyC,MAAM;QACtE,IAAI,CAACjC,KAAK,CAACM,QAAQ,GAAG0B,KAAK,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,IAAI,KAAK,SAAS,CAAC,CAACyC,MAAM;QACtE,IAAI,CAACjC,KAAK,CAACO,MAAM,GAAGyB,KAAK,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,IAAI,KAAK,OAAO,CAAC,CAACyC,MAAM;MACpE,CAAC;MACDxG,KAAK,EAAEA,CAAA,KAAK;QACV;MAAA;KAEH,CAAC;EACJ;EAEA8C,cAAcA,CAAC6D,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACxE,MAA0B;IAC9C,IAAIyE,KAAK,CAACC,KAAK,EAAEL,MAAM,EAAE;MACvB,MAAMM,IAAI,GAAGF,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;MAE3B,MAAME,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;MAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACnC,IAAI,CAACjH,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAACkH,cAAc,EAAE;QACrB;;MAGF,IAAIJ,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B,IAAI,CAACnH,KAAK,GAAG,kCAAkC;QAC/C,IAAI,CAACkH,cAAc,EAAE;QACrB;;MAGF,IAAI,CAAC5C,aAAa,GAAGwC,IAAI;MACzB,IAAI,CAAC9G,KAAK,GAAG,EAAE;MAEf,MAAMoH,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;QACpB,IAAI,CAAC7G,UAAU,GAAI6G,CAAC,CAACpF,MAAM,EAAEqF,MAAiB,IAAI,IAAI;MACxD,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;EAE9B;EACQI,cAAcA,CAAA;IACpB,IAAI,CAAC5C,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC5D,UAAU,GAAG,IAAI;IACtB,MAAMgH,SAAS,GAAGC,QAAQ,CAACC,cAAc,CACvC,gBAAgB,CACG;IACrB,IAAIF,SAAS,EAAEA,SAAS,CAACG,KAAK,GAAG,EAAE;EACrC;EAEA1G,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACmD,aAAa,EAAE;IAEzB,IAAI,CAAC9C,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACtB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACF,KAAK,GAAG,EAAE;IAEf8H,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACvG,aAAa,CAAC;IAEjE,IAAI,CAAC2D,WAAW,CACb6C,kBAAkB,CAAC,IAAI,CAAC1D,aAAa,CAAC,CACtC2D,IAAI,CACH1I,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACiC,aAAa,GAAG,KAAK;MAC1BsG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACvG,aAAa,CAAC;IACpE,CAAC,CAAC,CACH,CACA0E,SAAS,CAAC;MACTC,IAAI,EAAG+B,QAAa,IAAI;QACtB,IAAI,CAAChI,OAAO,GAAGgI,QAAQ,CAAChI,OAAO,IAAI,8BAA8B;QAEjE;QACA,IAAI,CAACG,IAAI,CAAC8H,eAAe,GAAGD,QAAQ,CAACE,QAAQ;QAC7C,IAAI,CAAC/H,IAAI,CAACC,YAAY,GAAG4H,QAAQ,CAACE,QAAQ;QAE1C;QACA,IAAI,CAACjD,WAAW,CAACkD,iBAAiB,CAAC;UACjC/H,YAAY,EAAE4H,QAAQ,CAACE,QAAQ;UAC/BE,KAAK,EAAEJ,QAAQ,CAACE;SACjB,CAAC;QAEF,IAAI,CAAC9D,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC5D,UAAU,GAAG,IAAI;QACtB,IAAI,CAACwG,cAAc,EAAE;QAErB,IAAIgB,QAAQ,CAACrC,KAAK,EAAE;UAClBC,YAAY,CAACyC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACrC,KAAK,CAAC;;QAG/C;QACA2C,UAAU,CAAC,MAAK;UACd,IAAI,CAACtI,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDF,KAAK,EAAGqG,GAAmC,IAAI;QAC7C,IAAI,CAACrG,KAAK,GAAGqG,GAAG,CAACrG,KAAK,EAAEE,OAAO,IAAI,eAAe;QAClD;QACAsI,UAAU,CAAC,MAAK;UACd,IAAI,CAACxI,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EACA4B,kBAAkBA,CAAA;IAChB,IAAI,CAAC6G,OAAO,CAAC,uDAAuD,CAAC,EACnE;IAEF,IAAI,CAACzG,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC9B,OAAO,GAAG,EAAE;IACjB,IAAI,CAACF,KAAK,GAAG,EAAE;IAEf,IAAI,CAACmF,WAAW,CACbvD,kBAAkB,EAAE,CACpBqG,IAAI,CAAC1I,QAAQ,CAAC,MAAO,IAAI,CAACyC,aAAa,GAAG,KAAM,CAAC,CAAC,CAClDkE,SAAS,CAAC;MACTC,IAAI,EAAG+B,QAAa,IAAI;QACtB,IAAI,CAAChI,OAAO,GACVgI,QAAQ,CAAChI,OAAO,IAAI,sCAAsC;QAE5D;QACA,IAAI,CAACG,IAAI,CAAC8H,eAAe,GAAG,IAAI;QAChC,IAAI,CAAC9H,IAAI,CAACC,YAAY,GAAG,IAAI;QAE7B;QACA,IAAI,CAAC6E,WAAW,CAACkD,iBAAiB,CAAC;UACjC/H,YAAY,EAAE,mCAAmC;UACjDgI,KAAK,EAAE;SACR,CAAC;QAEF,IAAIJ,QAAQ,CAACrC,KAAK,EAAE;UAClBC,YAAY,CAACyC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACrC,KAAK,CAAC;;QAG/C;QACA2C,UAAU,CAAC,MAAK;UACd,IAAI,CAACtI,OAAO,GAAG,EAAE;QACnB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDF,KAAK,EAAGqG,GAAmC,IAAI;QAC7C,IAAI,CAACrG,KAAK,GAAGqG,GAAG,CAACrG,KAAK,EAAEE,OAAO,IAAI,gBAAgB;QACnD;QACAsI,UAAU,CAAC,MAAK;UACd,IAAI,CAACxI,KAAK,GAAG,EAAE;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;KACD,CAAC;EACN;EAEAmD,UAAUA,CAACuF,IAAY;IACrB,IAAI,CAACpD,MAAM,CAACU,QAAQ,CAAC,CAAC0C,IAAI,CAAC,CAAC;EAC9B;EAEApF,MAAMA,CAAA;IACJ,IAAI,CAAC+B,eAAe,CAAC/B,MAAM,EAAE,CAAC4C,SAAS,CAAC;MACtCC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACd,eAAe,CAACsD,aAAa,EAAE;QACpC,IAAI,CAACvD,gBAAgB,CAACuD,aAAa,EAAE;QACrCH,UAAU,CAAC,MAAK;UACd,IAAI,CAAClD,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACrC4C,WAAW,EAAE;cAAE1I,OAAO,EAAE;YAAqB,CAAE;YAC/C2I,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD7I,KAAK,EAAGqG,GAAQ,IAAI;QAClByB,OAAO,CAAC9H,KAAK,CAAC,eAAe,EAAEqG,GAAG,CAAC;QACnC,IAAI,CAAChB,eAAe,CAACsD,aAAa,EAAE;QACpC,IAAI,CAACvD,gBAAgB,CAACuD,aAAa,EAAE;QACrCH,UAAU,CAAC,MAAK;UACd,IAAI,CAAClD,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;QAC5C,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;EAEA3D,UAAUA,CAACyG,IAAU;IACnB,OAAO,IAAItD,IAAI,CAACsD,IAAI,CAAC,CAACC,cAAc,EAAE;EACxC;EAEAC,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;IACrB,OAAOA,IAAI,CACRC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAChBC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,EAAE;EAClB;;;uBAxQWtE,gBAAgB,EAAAxF,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjK,EAAA,CAAA+J,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnK,EAAA,CAAA+J,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAArK,EAAA,CAAA+J,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAvK,EAAA,CAAA+J,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBjF,gBAAgB;MAAAkF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ7BhL,EAAA,CAAAC,cAAA,aAAkE;UAKHD,EAAA,CAAAI,MAAA,iBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAI5EH,EAAA,CAAA4B,UAAA,IAAAsJ,+BAAA,iBAIM;UAGNlL,EAAA,CAAA4B,UAAA,IAAAuJ,+BAAA,iBAKM;UAGNnL,EAAA,CAAA4B,UAAA,IAAAwJ,+BAAA,iBAKM;UAGNpL,EAAA,CAAA4B,UAAA,IAAAyJ,+BAAA,oBAigBM;UACRrL,EAAA,CAAAG,YAAA,EAAM;;;UAzhBEH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAsK,GAAA,CAAAlF,OAAA,CAAa;UAQhB/F,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAW,UAAA,SAAAsK,GAAA,CAAAzK,KAAA,CAAW;UAQXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAsK,GAAA,CAAAvK,OAAA,CAAa;UAQbV,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAW,UAAA,UAAAsK,GAAA,CAAAlF,OAAA,IAAAkF,GAAA,CAAApK,IAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}