{"ast": null, "code": "import { print as origPrint } from \"graphql\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\nvar printCache;\nexport var print = Object.assign(function (ast) {\n  var result = printCache.get(ast);\n  if (!result) {\n    result = origPrint(ast);\n    printCache.set(ast, result);\n  }\n  return result;\n}, {\n  reset: function () {\n    printCache = new AutoCleanedWeakCache(cacheSizes.print || 2000 /* defaultCacheSizes.print */);\n  }\n});\n\nprint.reset();\nif (globalThis.__DEV__ !== false) {\n  registerGlobalCache(\"print\", function () {\n    return printCache ? printCache.size : 0;\n  });\n}", "map": {"version": 3, "names": ["print", "origPrint", "AutoCleanedWeakCache", "cacheSizes", "registerGlobalCache", "printCache", "Object", "assign", "ast", "result", "get", "set", "reset", "globalThis", "__DEV__", "size"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/graphql/print.js"], "sourcesContent": ["import { print as origPrint } from \"graphql\";\nimport { AutoCleanedWeakCache, cacheSizes, } from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\nvar printCache;\nexport var print = Object.assign(function (ast) {\n    var result = printCache.get(ast);\n    if (!result) {\n        result = origPrint(ast);\n        printCache.set(ast, result);\n    }\n    return result;\n}, {\n    reset: function () {\n        printCache = new AutoCleanedWeakCache(cacheSizes.print || 2000 /* defaultCacheSizes.print */);\n    },\n});\nprint.reset();\nif (globalThis.__DEV__ !== false) {\n    registerGlobalCache(\"print\", function () { return (printCache ? printCache.size : 0); });\n}\n"], "mappings": "AAAA,SAASA,KAAK,IAAIC,SAAS,QAAQ,SAAS;AAC5C,SAASC,oBAAoB,EAAEC,UAAU,QAAS,qBAAqB;AACvE,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,IAAIC,UAAU;AACd,OAAO,IAAIL,KAAK,GAAGM,MAAM,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;EAC5C,IAAIC,MAAM,GAAGJ,UAAU,CAACK,GAAG,CAACF,GAAG,CAAC;EAChC,IAAI,CAACC,MAAM,EAAE;IACTA,MAAM,GAAGR,SAAS,CAACO,GAAG,CAAC;IACvBH,UAAU,CAACM,GAAG,CAACH,GAAG,EAAEC,MAAM,CAAC;EAC/B;EACA,OAAOA,MAAM;AACjB,CAAC,EAAE;EACCG,KAAK,EAAE,SAAAA,CAAA,EAAY;IACfP,UAAU,GAAG,IAAIH,oBAAoB,CAACC,UAAU,CAACH,KAAK,IAAI,IAAI,CAAC,6BAA6B,CAAC;EACjG;AACJ,CAAC,CAAC;;AACFA,KAAK,CAACY,KAAK,CAAC,CAAC;AACb,IAAIC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;EAC9BV,mBAAmB,CAAC,OAAO,EAAE,YAAY;IAAE,OAAQC,UAAU,GAAGA,UAAU,CAACU,IAAI,GAAG,CAAC;EAAG,CAAC,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}