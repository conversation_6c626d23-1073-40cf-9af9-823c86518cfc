{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/notification.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"bi-check-circle-fill\": a0,\n    \"bi-exclamation-triangle-fill\": a1,\n    \"bi-info-circle-fill\": a2,\n    \"bi-x-circle-fill\": a3\n  };\n};\nfunction NotificationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"i\", 3);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeNotification());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"alert-\" + ctx_r0.notification.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(3, _c0, ctx_r0.notification.type === \"success\", ctx_r0.notification.type === \"warning\", ctx_r0.notification.type === \"info\", ctx_r0.notification.type === \"error\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.notification.message);\n  }\n}\nexport class NotificationComponent {\n  constructor(notificationService) {\n    this.notificationService = notificationService;\n    this.notification = null;\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.subscription = this.notificationService.getNotifications().subscribe(notification => {\n      this.notification = notification;\n    });\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  closeNotification() {\n    this.notificationService.clear();\n  }\n  static {\n    this.ɵfac = function NotificationComponent_Factory(t) {\n      return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationComponent,\n      selectors: [[\"app-notification\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"notification-container\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"notification-container\", 3, \"ngClass\"], [1, \"notification-content\"], [1, \"bi\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"]],\n      template: function NotificationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NotificationComponent_div_0_Template, 6, 8, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.notification);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf],\n      styles: [\".notification-container[_ngcontent-%COMP%] {\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      min-width: 300px;\\n      z-index: 9999;\\n      padding: 15px;\\n      border-radius: 4px;\\n      box-shadow: 0 4px 8px rgba(0,0,0,0.1);\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n    }\\n    .notification-content[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 10px;\\n    }\\n    .alert-success[_ngcontent-%COMP%] {\\n      background-color: #d4edda;\\n      border-color: #c3e6cb;\\n      color: #155724;\\n    }\\n    .alert-error[_ngcontent-%COMP%] {\\n      background-color: #f8d7da;\\n      border-color: #f5c6cb;\\n      color: #721c24;\\n    }\\n    .alert-info[_ngcontent-%COMP%] {\\n      background-color: #d1ecf1;\\n      border-color: #bee5eb;\\n      color: #0c5460;\\n    }\\n    .alert-warning[_ngcontent-%COMP%] {\\n      background-color: #fff3cd;\\n      border-color: #ffeeba;\\n      color: #856404;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsZUFBZTtNQUNmLFNBQVM7TUFDVCxXQUFXO01BQ1gsZ0JBQWdCO01BQ2hCLGFBQWE7TUFDYixhQUFhO01BQ2Isa0JBQWtCO01BQ2xCLHFDQUFxQztNQUNyQyxhQUFhO01BQ2IsOEJBQThCO01BQzlCLG1CQUFtQjtJQUNyQjtJQUNBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixTQUFTO0lBQ1g7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQjtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCO0lBQ0E7TUFDRSx5QkFBeUI7TUFDekIscUJBQXFCO01BQ3JCLGNBQWM7SUFDaEI7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQiIsImZpbGUiOiJub3RpZmljYXRpb24uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm5vdGlmaWNhdGlvbi1jb250YWluZXIge1xuICAgICAgcG9zaXRpb246IGZpeGVkO1xuICAgICAgdG9wOiAyMHB4O1xuICAgICAgcmlnaHQ6IDIwcHg7XG4gICAgICBtaW4td2lkdGg6IDMwMHB4O1xuICAgICAgei1pbmRleDogOTk5OTtcbiAgICAgIHBhZGRpbmc6IDE1cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjEpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgfVxuICAgIC5ub3RpZmljYXRpb24tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTBweDtcbiAgICB9XG4gICAgLmFsZXJ0LXN1Y2Nlc3Mge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Q0ZWRkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2MzZTZjYjtcbiAgICAgIGNvbG9yOiAjMTU1NzI0O1xuICAgIH1cbiAgICAuYWxlcnQtZXJyb3Ige1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZDdkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2Y1YzZjYjtcbiAgICAgIGNvbG9yOiAjNzIxYzI0O1xuICAgIH1cbiAgICAuYWxlcnQtaW5mbyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDFlY2YxO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjYmVlNWViO1xuICAgICAgY29sb3I6ICMwYzU0NjA7XG4gICAgfVxuICAgIC5hbGVydC13YXJuaW5nIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzY2Q7XG4gICAgICBib3JkZXItY29sb3I6ICNmZmVlYmE7XG4gICAgICBjb2xvcjogIzg1NjQwNDtcbiAgICB9XG4gICJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vZXF1aXBlcy9ub3RpZmljYXRpb24vbm90aWZpY2F0aW9uLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSxlQUFlO01BQ2YsU0FBUztNQUNULFdBQVc7TUFDWCxnQkFBZ0I7TUFDaEIsYUFBYTtNQUNiLGFBQWE7TUFDYixrQkFBa0I7TUFDbEIscUNBQXFDO01BQ3JDLGFBQWE7TUFDYiw4QkFBOEI7TUFDOUIsbUJBQW1CO0lBQ3JCO0lBQ0E7TUFDRSxhQUFhO01BQ2IsbUJBQW1CO01BQ25CLFNBQVM7SUFDWDtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCO0lBQ0E7TUFDRSx5QkFBeUI7TUFDekIscUJBQXFCO01BQ3JCLGNBQWM7SUFDaEI7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQjtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCOztBQUVKLGc0REFBZzREIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm5vdGlmaWNhdGlvbi1jb250YWluZXIge1xuICAgICAgcG9zaXRpb246IGZpeGVkO1xuICAgICAgdG9wOiAyMHB4O1xuICAgICAgcmlnaHQ6IDIwcHg7XG4gICAgICBtaW4td2lkdGg6IDMwMHB4O1xuICAgICAgei1pbmRleDogOTk5OTtcbiAgICAgIHBhZGRpbmc6IDE1cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjEpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgfVxuICAgIC5ub3RpZmljYXRpb24tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTBweDtcbiAgICB9XG4gICAgLmFsZXJ0LXN1Y2Nlc3Mge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Q0ZWRkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2MzZTZjYjtcbiAgICAgIGNvbG9yOiAjMTU1NzI0O1xuICAgIH1cbiAgICAuYWxlcnQtZXJyb3Ige1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZDdkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2Y1YzZjYjtcbiAgICAgIGNvbG9yOiAjNzIxYzI0O1xuICAgIH1cbiAgICAuYWxlcnQtaW5mbyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDFlY2YxO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjYmVlNWViO1xuICAgICAgY29sb3I6ICMwYzU0NjA7XG4gICAgfVxuICAgIC5hbGVydC13YXJuaW5nIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzY2Q7XG4gICAgICBib3JkZXItY29sb3I6ICNmZmVlYmE7XG4gICAgICBjb2xvcjogIzg1NjQwNDtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NotificationComponent_div_0_Template_button_click_5_listener", "ɵɵrestoreView", "_r2", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "closeNotification", "ɵɵproperty", "ctx_r0", "notification", "type", "ɵɵadvance", "ɵɵpureFunction4", "_c0", "ɵɵtextInterpolate", "message", "NotificationComponent", "constructor", "notificationService", "subscription", "ngOnInit", "getNotifications", "subscribe", "ngOnDestroy", "unsubscribe", "clear", "ɵɵdirectiveInject", "i1", "NotificationService", "selectors", "decls", "vars", "consts", "template", "NotificationComponent_Template", "rf", "ctx", "ɵɵtemplate", "NotificationComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\admin\\equipes\\notification\\notification.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { Subscription } from 'rxjs';\r\nimport { NotificationService, Notification } from 'src/app/services/notification.service';\r\n\r\n@Component({\r\n  selector: 'app-notification',\r\n  template: `\r\n    <div *ngIf=\"notification\"\r\n         class=\"notification-container\"\r\n         [ngClass]=\"'alert-' + notification.type\">\r\n      <div class=\"notification-content\">\r\n        <i class=\"bi\"\r\n           [ngClass]=\"{\r\n             'bi-check-circle-fill': notification.type === 'success',\r\n             'bi-exclamation-triangle-fill': notification.type === 'warning',\r\n             'bi-info-circle-fill': notification.type === 'info',\r\n             'bi-x-circle-fill': notification.type === 'error'\r\n           }\"></i>\r\n        <span>{{ notification.message }}</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn-close\" (click)=\"closeNotification()\"></button>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .notification-container {\r\n      position: fixed;\r\n      top: 20px;\r\n      right: 20px;\r\n      min-width: 300px;\r\n      z-index: 9999;\r\n      padding: 15px;\r\n      border-radius: 4px;\r\n      box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n    }\r\n    .notification-content {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n    .alert-success {\r\n      background-color: #d4edda;\r\n      border-color: #c3e6cb;\r\n      color: #155724;\r\n    }\r\n    .alert-error {\r\n      background-color: #f8d7da;\r\n      border-color: #f5c6cb;\r\n      color: #721c24;\r\n    }\r\n    .alert-info {\r\n      background-color: #d1ecf1;\r\n      border-color: #bee5eb;\r\n      color: #0c5460;\r\n    }\r\n    .alert-warning {\r\n      background-color: #fff3cd;\r\n      border-color: #ffeeba;\r\n      color: #856404;\r\n    }\r\n  `]\r\n})\r\nexport class NotificationComponent implements OnInit, OnDestroy {\r\n  notification: Notification | null = null;\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  constructor(private notificationService: NotificationService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.subscription = this.notificationService.getNotifications().subscribe(notification => {\r\n      this.notification = notification;\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscription.unsubscribe();\r\n  }\r\n\r\n  closeNotification(): void {\r\n    this.notificationService.clear();\r\n  }\r\n}"], "mappings": "AACA,SAASA,YAAY,QAAQ,MAAM;;;;;;;;;;;;;;;IAM/BC,EAAA,CAAAC,cAAA,aAE8C;IAE1CD,EAAA,CAAAE,SAAA,WAMU;IACVF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEzCJ,EAAA,CAAAC,cAAA,gBAAsE;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAACZ,EAAA,CAAAI,YAAA,EAAS;;;;IAX5EJ,EAAA,CAAAa,UAAA,uBAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAAwC;IAGtChB,EAAA,CAAAiB,SAAA,GAKE;IALFjB,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAAL,MAAA,CAAAC,YAAA,CAAAC,IAAA,gBAAAF,MAAA,CAAAC,YAAA,CAAAC,IAAA,gBAAAF,MAAA,CAAAC,YAAA,CAAAC,IAAA,aAAAF,MAAA,CAAAC,YAAA,CAAAC,IAAA,cAKE;IACChB,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAoB,iBAAA,CAAAN,MAAA,CAAAC,YAAA,CAAAM,OAAA,CAA0B;;;AA8CxC,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAHvC,KAAAT,YAAY,GAAwB,IAAI;IAChC,KAAAU,YAAY,GAAiB,IAAI1B,YAAY,EAAE;EAEQ;EAE/D2B,QAAQA,CAAA;IACN,IAAI,CAACD,YAAY,GAAG,IAAI,CAACD,mBAAmB,CAACG,gBAAgB,EAAE,CAACC,SAAS,CAACb,YAAY,IAAG;MACvF,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC,CAAC,CAAC;EACJ;EAEAc,WAAWA,CAAA;IACT,IAAI,CAACJ,YAAY,CAACK,WAAW,EAAE;EACjC;EAEAlB,iBAAiBA,CAAA;IACf,IAAI,CAACY,mBAAmB,CAACO,KAAK,EAAE;EAClC;;;uBAlBWT,qBAAqB,EAAAtB,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBZ,qBAAqB;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzD9BzC,EAAA,CAAA2C,UAAA,IAAAC,oCAAA,iBAcM;;;UAdA5C,EAAA,CAAAa,UAAA,SAAA6B,GAAA,CAAA3B,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}