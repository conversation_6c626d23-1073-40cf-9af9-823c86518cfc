{"ast": null, "code": "import { ApolloLink } from \"./ApolloLink.js\";\nexport var execute = ApolloLink.execute;", "map": {"version": 3, "names": ["ApolloLink", "execute"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/core/execute.js"], "sourcesContent": ["import { ApolloLink } from \"./ApolloLink.js\";\nexport var execute = ApolloLink.execute;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,OAAO,GAAGD,UAAU,CAACC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}