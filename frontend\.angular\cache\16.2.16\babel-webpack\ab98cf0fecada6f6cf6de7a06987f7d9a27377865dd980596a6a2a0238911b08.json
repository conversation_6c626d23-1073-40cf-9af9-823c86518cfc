{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { global } from \"../globals/index.js\";\nvar cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport var cacheSizes = __assign({}, global[cacheSizeSymbol]);", "map": {"version": 3, "names": ["__assign", "global", "cacheSizeSymbol", "Symbol", "for", "cacheSizes"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/caching/sizes.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { global } from \"../globals/index.js\";\nvar cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport var cacheSizes = __assign({}, global[cacheSizeSymbol]);\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,IAAIC,eAAe,GAAGC,MAAM,CAACC,GAAG,CAAC,kBAAkB,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAGL,QAAQ,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}