{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { graphQLResultHasProtocolErrors, PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nexport function onError(errorHandler) {\n  return new ApolloLink(function (operation, forward) {\n    return new Observable(function (observer) {\n      var sub;\n      var retriedSub;\n      var retriedResult;\n      try {\n        sub = forward(operation).subscribe({\n          next: function (result) {\n            if (result.errors) {\n              retriedResult = errorHandler({\n                graphQLErrors: result.errors,\n                response: result,\n                operation: operation,\n                forward: forward\n              });\n            } else if (graphQLResultHasProtocolErrors(result)) {\n              retriedResult = errorHandler({\n                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                response: result,\n                operation: operation,\n                forward: forward\n              });\n            }\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer)\n              });\n              return;\n            }\n            observer.next(result);\n          },\n          error: function (networkError) {\n            retriedResult = errorHandler({\n              operation: operation,\n              networkError: networkError,\n              //Network errors can return GraphQL errors on for example a 403\n              graphQLErrors: networkError && networkError.result && networkError.result.errors || void 0,\n              forward: forward\n            });\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer)\n              });\n              return;\n            }\n            observer.error(networkError);\n          },\n          complete: function () {\n            // disable the previous sub from calling complete on observable\n            // if retry is in flight.\n            if (!retriedResult) {\n              observer.complete.bind(observer)();\n            }\n          }\n        });\n      } catch (e) {\n        errorHandler({\n          networkError: e,\n          operation: operation,\n          forward: forward\n        });\n        observer.error(e);\n      }\n      return function () {\n        if (sub) sub.unsubscribe();\n        if (retriedSub) sub.unsubscribe();\n      };\n    });\n  });\n}\nvar ErrorLink = /** @class */function (_super) {\n  __extends(ErrorLink, _super);\n  function ErrorLink(errorHandler) {\n    var _this = _super.call(this) || this;\n    _this.link = onError(errorHandler);\n    return _this;\n  }\n  ErrorLink.prototype.request = function (operation, forward) {\n    return this.link.request(operation, forward);\n  };\n  return ErrorLink;\n}(ApolloLink);\nexport { ErrorLink };", "map": {"version": 3, "names": ["__extends", "graphQLResultHasProtocolErrors", "PROTOCOL_ERRORS_SYMBOL", "Observable", "ApolloLink", "onError", "<PERSON><PERSON><PERSON><PERSON>", "operation", "forward", "observer", "sub", "retried<PERSON>ub", "retried<PERSON><PERSON><PERSON>", "subscribe", "next", "result", "errors", "graphQLErrors", "response", "protocolErrors", "extensions", "bind", "error", "complete", "networkError", "e", "unsubscribe", "ErrorLink", "_super", "_this", "call", "link", "prototype", "request"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/error/index.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { graphQLResultHasProtocolErrors, PROTOCOL_ERRORS_SYMBOL, } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nexport function onError(errorHandler) {\n    return new ApolloLink(function (operation, forward) {\n        return new Observable(function (observer) {\n            var sub;\n            var retriedSub;\n            var retriedResult;\n            try {\n                sub = forward(operation).subscribe({\n                    next: function (result) {\n                        if (result.errors) {\n                            retriedResult = errorHandler({\n                                graphQLErrors: result.errors,\n                                response: result,\n                                operation: operation,\n                                forward: forward,\n                            });\n                        }\n                        else if (graphQLResultHasProtocolErrors(result)) {\n                            retriedResult = errorHandler({\n                                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                                response: result,\n                                operation: operation,\n                                forward: forward,\n                            });\n                        }\n                        if (retriedResult) {\n                            retriedSub = retriedResult.subscribe({\n                                next: observer.next.bind(observer),\n                                error: observer.error.bind(observer),\n                                complete: observer.complete.bind(observer),\n                            });\n                            return;\n                        }\n                        observer.next(result);\n                    },\n                    error: function (networkError) {\n                        retriedResult = errorHandler({\n                            operation: operation,\n                            networkError: networkError,\n                            //Network errors can return GraphQL errors on for example a 403\n                            graphQLErrors: (networkError &&\n                                networkError.result &&\n                                networkError.result.errors) ||\n                                void 0,\n                            forward: forward,\n                        });\n                        if (retriedResult) {\n                            retriedSub = retriedResult.subscribe({\n                                next: observer.next.bind(observer),\n                                error: observer.error.bind(observer),\n                                complete: observer.complete.bind(observer),\n                            });\n                            return;\n                        }\n                        observer.error(networkError);\n                    },\n                    complete: function () {\n                        // disable the previous sub from calling complete on observable\n                        // if retry is in flight.\n                        if (!retriedResult) {\n                            observer.complete.bind(observer)();\n                        }\n                    },\n                });\n            }\n            catch (e) {\n                errorHandler({ networkError: e, operation: operation, forward: forward });\n                observer.error(e);\n            }\n            return function () {\n                if (sub)\n                    sub.unsubscribe();\n                if (retriedSub)\n                    sub.unsubscribe();\n            };\n        });\n    });\n}\nvar ErrorLink = /** @class */ (function (_super) {\n    __extends(ErrorLink, _super);\n    function ErrorLink(errorHandler) {\n        var _this = _super.call(this) || this;\n        _this.link = onError(errorHandler);\n        return _this;\n    }\n    ErrorLink.prototype.request = function (operation, forward) {\n        return this.link.request(operation, forward);\n    };\n    return ErrorLink;\n}(ApolloLink));\nexport { ErrorLink };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,8BAA8B,EAAEC,sBAAsB,QAAS,uBAAuB;AAC/F,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,OAAO,SAASC,OAAOA,CAACC,YAAY,EAAE;EAClC,OAAO,IAAIF,UAAU,CAAC,UAAUG,SAAS,EAAEC,OAAO,EAAE;IAChD,OAAO,IAAIL,UAAU,CAAC,UAAUM,QAAQ,EAAE;MACtC,IAAIC,GAAG;MACP,IAAIC,UAAU;MACd,IAAIC,aAAa;MACjB,IAAI;QACAF,GAAG,GAAGF,OAAO,CAACD,SAAS,CAAC,CAACM,SAAS,CAAC;UAC/BC,IAAI,EAAE,SAAAA,CAAUC,MAAM,EAAE;YACpB,IAAIA,MAAM,CAACC,MAAM,EAAE;cACfJ,aAAa,GAAGN,YAAY,CAAC;gBACzBW,aAAa,EAAEF,MAAM,CAACC,MAAM;gBAC5BE,QAAQ,EAAEH,MAAM;gBAChBR,SAAS,EAAEA,SAAS;gBACpBC,OAAO,EAAEA;cACb,CAAC,CAAC;YACN,CAAC,MACI,IAAIP,8BAA8B,CAACc,MAAM,CAAC,EAAE;cAC7CH,aAAa,GAAGN,YAAY,CAAC;gBACzBa,cAAc,EAAEJ,MAAM,CAACK,UAAU,CAAClB,sBAAsB,CAAC;gBACzDgB,QAAQ,EAAEH,MAAM;gBAChBR,SAAS,EAAEA,SAAS;gBACpBC,OAAO,EAAEA;cACb,CAAC,CAAC;YACN;YACA,IAAII,aAAa,EAAE;cACfD,UAAU,GAAGC,aAAa,CAACC,SAAS,CAAC;gBACjCC,IAAI,EAAEL,QAAQ,CAACK,IAAI,CAACO,IAAI,CAACZ,QAAQ,CAAC;gBAClCa,KAAK,EAAEb,QAAQ,CAACa,KAAK,CAACD,IAAI,CAACZ,QAAQ,CAAC;gBACpCc,QAAQ,EAAEd,QAAQ,CAACc,QAAQ,CAACF,IAAI,CAACZ,QAAQ;cAC7C,CAAC,CAAC;cACF;YACJ;YACAA,QAAQ,CAACK,IAAI,CAACC,MAAM,CAAC;UACzB,CAAC;UACDO,KAAK,EAAE,SAAAA,CAAUE,YAAY,EAAE;YAC3BZ,aAAa,GAAGN,YAAY,CAAC;cACzBC,SAAS,EAAEA,SAAS;cACpBiB,YAAY,EAAEA,YAAY;cAC1B;cACAP,aAAa,EAAGO,YAAY,IACxBA,YAAY,CAACT,MAAM,IACnBS,YAAY,CAACT,MAAM,CAACC,MAAM,IAC1B,KAAK,CAAC;cACVR,OAAO,EAAEA;YACb,CAAC,CAAC;YACF,IAAII,aAAa,EAAE;cACfD,UAAU,GAAGC,aAAa,CAACC,SAAS,CAAC;gBACjCC,IAAI,EAAEL,QAAQ,CAACK,IAAI,CAACO,IAAI,CAACZ,QAAQ,CAAC;gBAClCa,KAAK,EAAEb,QAAQ,CAACa,KAAK,CAACD,IAAI,CAACZ,QAAQ,CAAC;gBACpCc,QAAQ,EAAEd,QAAQ,CAACc,QAAQ,CAACF,IAAI,CAACZ,QAAQ;cAC7C,CAAC,CAAC;cACF;YACJ;YACAA,QAAQ,CAACa,KAAK,CAACE,YAAY,CAAC;UAChC,CAAC;UACDD,QAAQ,EAAE,SAAAA,CAAA,EAAY;YAClB;YACA;YACA,IAAI,CAACX,aAAa,EAAE;cAChBH,QAAQ,CAACc,QAAQ,CAACF,IAAI,CAACZ,QAAQ,CAAC,CAAC,CAAC;YACtC;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CACD,OAAOgB,CAAC,EAAE;QACNnB,YAAY,CAAC;UAAEkB,YAAY,EAAEC,CAAC;UAAElB,SAAS,EAAEA,SAAS;UAAEC,OAAO,EAAEA;QAAQ,CAAC,CAAC;QACzEC,QAAQ,CAACa,KAAK,CAACG,CAAC,CAAC;MACrB;MACA,OAAO,YAAY;QACf,IAAIf,GAAG,EACHA,GAAG,CAACgB,WAAW,CAAC,CAAC;QACrB,IAAIf,UAAU,EACVD,GAAG,CAACgB,WAAW,CAAC,CAAC;MACzB,CAAC;IACL,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7C5B,SAAS,CAAC2B,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACrB,YAAY,EAAE;IAC7B,IAAIuB,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,IAAI,GAAG1B,OAAO,CAACC,YAAY,CAAC;IAClC,OAAOuB,KAAK;EAChB;EACAF,SAAS,CAACK,SAAS,CAACC,OAAO,GAAG,UAAU1B,SAAS,EAAEC,OAAO,EAAE;IACxD,OAAO,IAAI,CAACuB,IAAI,CAACE,OAAO,CAAC1B,SAAS,EAAEC,OAAO,CAAC;EAChD,CAAC;EACD,OAAOmB,SAAS;AACpB,CAAC,CAACvB,UAAU,CAAE;AACd,SAASuB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}