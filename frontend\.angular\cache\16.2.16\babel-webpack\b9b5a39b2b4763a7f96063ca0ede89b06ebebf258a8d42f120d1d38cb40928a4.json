{"ast": null, "code": "import { omitDeep } from \"./omitDeep.js\";\nexport function stripTypename(value) {\n  return omitDeep(value, \"__typename\");\n}", "map": {"version": 3, "names": ["omitDeep", "stripTypename", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/stripTypename.js"], "sourcesContent": ["import { omitDeep } from \"./omitDeep.js\";\nexport function stripTypename(value) {\n    return omitDeep(value, \"__typename\");\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACjC,OAAOF,QAAQ,CAACE,KAAK,EAAE,YAAY,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}