{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ProfileCompletionComponent } from './profile-completion.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileCompletionComponent\n}];\nexport class ProfileCompletionModule {\n  static {\n    this.ɵfac = function ProfileCompletionModule_Factory(t) {\n      return new (t || ProfileCompletionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileCompletionModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileCompletionModule, {\n    declarations: [ProfileCompletionComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "ProfileCompletionComponent", "routes", "path", "component", "ProfileCompletionModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule, Routes } from '@angular/router';\n\nimport { ProfileCompletionComponent } from './profile-completion.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileCompletionComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileCompletionComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProfileCompletionModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,0BAA0B,QAAQ,gCAAgC;;;AAE3E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAYD,OAAM,MAAOI,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBALhCP,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,uBAAuB;IAAAE,YAAA,GARhCN,0BAA0B;IAAAO,OAAA,GAG1BV,YAAY,EACZC,mBAAmB,EAAAU,EAAA,CAAAT,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}