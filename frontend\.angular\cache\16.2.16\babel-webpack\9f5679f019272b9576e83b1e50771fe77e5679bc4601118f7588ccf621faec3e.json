{"ast": null, "code": "/* global window */\nimport ponyfill from './ponyfill.js';\nvar root;\nif (typeof self !== 'undefined') {\n  root = self;\n} else if (typeof window !== 'undefined') {\n  root = window;\n} else if (typeof global !== 'undefined') {\n  root = global;\n} else if (typeof module !== 'undefined') {\n  root = module;\n} else {\n  root = Function('return this')();\n}\nvar result = ponyfill(root);\nexport default result;", "map": {"version": 3, "names": ["ponyfill", "root", "self", "window", "global", "module", "Function", "result"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/symbol-observable/es/index.js"], "sourcesContent": ["/* global window */\nimport ponyfill from './ponyfill.js';\n\nvar root;\n\nif (typeof self !== 'undefined') {\n  root = self;\n} else if (typeof window !== 'undefined') {\n  root = window;\n} else if (typeof global !== 'undefined') {\n  root = global;\n} else if (typeof module !== 'undefined') {\n  root = module;\n} else {\n  root = Function('return this')();\n}\n\nvar result = ponyfill(root);\nexport default result;\n"], "mappings": "AAAA;AACA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,IAAIC,IAAI;AAER,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC/BD,IAAI,GAAGC,IAAI;AACb,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACxCF,IAAI,GAAGE,MAAM;AACf,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACxCH,IAAI,GAAGG,MAAM;AACf,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACxCJ,IAAI,GAAGI,MAAM;AACf,CAAC,MAAM;EACLJ,IAAI,GAAGK,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AAClC;AAEA,IAAIC,MAAM,GAAGP,QAAQ,CAACC,IAAI,CAAC;AAC3B,eAAeM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}