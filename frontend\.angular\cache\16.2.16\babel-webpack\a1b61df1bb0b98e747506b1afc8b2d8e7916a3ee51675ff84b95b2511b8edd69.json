{"ast": null, "code": "import { Observable } from \"../../utilities/index.js\";\nexport function fromError(errorValue) {\n  return new Observable(function (observer) {\n    observer.error(errorValue);\n  });\n}", "map": {"version": 3, "names": ["Observable", "fromError", "errorValue", "observer", "error"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/link/utils/fromError.js"], "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\nexport function fromError(errorValue) {\n    return new Observable(function (observer) {\n        observer.error(errorValue);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,0BAA0B;AACrD,OAAO,SAASC,SAASA,CAACC,UAAU,EAAE;EAClC,OAAO,IAAIF,UAAU,CAAC,UAAUG,QAAQ,EAAE;IACtCA,QAAQ,CAACC,KAAK,CAACF,UAAU,CAAC;EAC9B,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}