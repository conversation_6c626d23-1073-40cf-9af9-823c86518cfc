{"ast": null, "code": "import { __rest } from \"tslib\";\nimport equal from \"@wry/equality\";\nimport { createFragmentMap, getFragmentDefinitions, getFragmentFromSelection, getMainDefinition, isField, resultKeyNameFromField, shouldInclude } from \"../utilities/index.js\";\n// Returns true if aResult and bResult are deeply equal according to the fields\n// selected by the given query, ignoring any fields marked as @nonreactive.\nexport function equalByQuery(query, _a, _b, variables) {\n  var aData = _a.data,\n    aRest = __rest(_a, [\"data\"]);\n  var bData = _b.data,\n    bRest = __rest(_b, [\"data\"]);\n  return equal(aRest, bRest) && equalBySelectionSet(getMainDefinition(query).selectionSet, aData, bData, {\n    fragmentMap: createFragmentMap(getFragmentDefinitions(query)),\n    variables: variables\n  });\n}\nfunction equalBySelectionSet(selectionSet, aResult, bResult, context) {\n  if (aResult === bResult) {\n    return true;\n  }\n  var seenSelections = new Set();\n  // Returning true from this Array.prototype.every callback function skips the\n  // current field/subtree. Returning false aborts the entire traversal\n  // immediately, causing equalBySelectionSet to return false.\n  return selectionSet.selections.every(function (selection) {\n    // Avoid re-processing the same selection at the same level of recursion, in\n    // case the same field gets included via multiple indirect fragment spreads.\n    if (seenSelections.has(selection)) return true;\n    seenSelections.add(selection);\n    // Ignore @skip(if: true) and @include(if: false) fields.\n    if (!shouldInclude(selection, context.variables)) return true;\n    // If the field or (named) fragment spread has a @nonreactive directive on\n    // it, we don't care if it's different, so we pretend it's the same.\n    if (selectionHasNonreactiveDirective(selection)) return true;\n    if (isField(selection)) {\n      var resultKey = resultKeyNameFromField(selection);\n      var aResultChild = aResult && aResult[resultKey];\n      var bResultChild = bResult && bResult[resultKey];\n      var childSelectionSet = selection.selectionSet;\n      if (!childSelectionSet) {\n        // These are scalar values, so we can compare them with deep equal\n        // without redoing the main recursive work.\n        return equal(aResultChild, bResultChild);\n      }\n      var aChildIsArray = Array.isArray(aResultChild);\n      var bChildIsArray = Array.isArray(bResultChild);\n      if (aChildIsArray !== bChildIsArray) return false;\n      if (aChildIsArray && bChildIsArray) {\n        var length_1 = aResultChild.length;\n        if (bResultChild.length !== length_1) {\n          return false;\n        }\n        for (var i = 0; i < length_1; ++i) {\n          if (!equalBySelectionSet(childSelectionSet, aResultChild[i], bResultChild[i], context)) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return equalBySelectionSet(childSelectionSet, aResultChild, bResultChild, context);\n    } else {\n      var fragment = getFragmentFromSelection(selection, context.fragmentMap);\n      if (fragment) {\n        // The fragment might === selection if it's an inline fragment, but\n        // could be !== if it's a named fragment ...spread.\n        if (selectionHasNonreactiveDirective(fragment)) return true;\n        return equalBySelectionSet(fragment.selectionSet,\n        // Notice that we reuse the same aResult and bResult values here,\n        // since the fragment ...spread does not specify a field name, but\n        // consists of multiple fields (within the fragment's selection set)\n        // that should be applied to the current result value(s).\n        aResult, bResult, context);\n      }\n    }\n  });\n}\nfunction selectionHasNonreactiveDirective(selection) {\n  return !!selection.directives && selection.directives.some(directiveIsNonreactive);\n}\nfunction directiveIsNonreactive(dir) {\n  return dir.name.value === \"nonreactive\";\n}", "map": {"version": 3, "names": ["__rest", "equal", "createFragmentMap", "getFragmentDefinitions", "getFragmentFromSelection", "getMainDefinition", "isField", "resultKeyNameFromField", "shouldInclude", "equalBy<PERSON>uery", "query", "_a", "_b", "variables", "aData", "data", "aRest", "bData", "bRest", "equalBySelectionSet", "selectionSet", "fragmentMap", "aResult", "bResult", "context", "seenSelections", "Set", "selections", "every", "selection", "has", "add", "selectionHasNonreactiveDirective", "<PERSON><PERSON><PERSON>", "aResultChild", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childSelectionSet", "aChildIsArray", "Array", "isArray", "bChildIsArray", "length_1", "length", "i", "fragment", "directives", "some", "directiveIsNonreactive", "dir", "name", "value"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/core/equalByQuery.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport equal from \"@wry/equality\";\nimport { createFragmentMap, getFragmentDefinitions, getFragmentFromSelection, getMainDefinition, isField, resultKeyNameFromField, shouldInclude, } from \"../utilities/index.js\";\n// Returns true if aResult and bResult are deeply equal according to the fields\n// selected by the given query, ignoring any fields marked as @nonreactive.\nexport function equalByQuery(query, _a, _b, variables) {\n    var aData = _a.data, aRest = __rest(_a, [\"data\"]);\n    var bData = _b.data, bRest = __rest(_b, [\"data\"]);\n    return (equal(aRest, bRest) &&\n        equalBySelectionSet(getMainDefinition(query).selectionSet, aData, bData, {\n            fragmentMap: createFragmentMap(getFragmentDefinitions(query)),\n            variables: variables,\n        }));\n}\nfunction equalBySelectionSet(selectionSet, aResult, bResult, context) {\n    if (aResult === bResult) {\n        return true;\n    }\n    var seenSelections = new Set();\n    // Returning true from this Array.prototype.every callback function skips the\n    // current field/subtree. Returning false aborts the entire traversal\n    // immediately, causing equalBySelectionSet to return false.\n    return selectionSet.selections.every(function (selection) {\n        // Avoid re-processing the same selection at the same level of recursion, in\n        // case the same field gets included via multiple indirect fragment spreads.\n        if (seenSelections.has(selection))\n            return true;\n        seenSelections.add(selection);\n        // Ignore @skip(if: true) and @include(if: false) fields.\n        if (!shouldInclude(selection, context.variables))\n            return true;\n        // If the field or (named) fragment spread has a @nonreactive directive on\n        // it, we don't care if it's different, so we pretend it's the same.\n        if (selectionHasNonreactiveDirective(selection))\n            return true;\n        if (isField(selection)) {\n            var resultKey = resultKeyNameFromField(selection);\n            var aResultChild = aResult && aResult[resultKey];\n            var bResultChild = bResult && bResult[resultKey];\n            var childSelectionSet = selection.selectionSet;\n            if (!childSelectionSet) {\n                // These are scalar values, so we can compare them with deep equal\n                // without redoing the main recursive work.\n                return equal(aResultChild, bResultChild);\n            }\n            var aChildIsArray = Array.isArray(aResultChild);\n            var bChildIsArray = Array.isArray(bResultChild);\n            if (aChildIsArray !== bChildIsArray)\n                return false;\n            if (aChildIsArray && bChildIsArray) {\n                var length_1 = aResultChild.length;\n                if (bResultChild.length !== length_1) {\n                    return false;\n                }\n                for (var i = 0; i < length_1; ++i) {\n                    if (!equalBySelectionSet(childSelectionSet, aResultChild[i], bResultChild[i], context)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            return equalBySelectionSet(childSelectionSet, aResultChild, bResultChild, context);\n        }\n        else {\n            var fragment = getFragmentFromSelection(selection, context.fragmentMap);\n            if (fragment) {\n                // The fragment might === selection if it's an inline fragment, but\n                // could be !== if it's a named fragment ...spread.\n                if (selectionHasNonreactiveDirective(fragment))\n                    return true;\n                return equalBySelectionSet(fragment.selectionSet, \n                // Notice that we reuse the same aResult and bResult values here,\n                // since the fragment ...spread does not specify a field name, but\n                // consists of multiple fields (within the fragment's selection set)\n                // that should be applied to the current result value(s).\n                aResult, bResult, context);\n            }\n        }\n    });\n}\nfunction selectionHasNonreactiveDirective(selection) {\n    return (!!selection.directives && selection.directives.some(directiveIsNonreactive));\n}\nfunction directiveIsNonreactive(dir) {\n    return dir.name.value === \"nonreactive\";\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,iBAAiB,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,aAAa,QAAS,uBAAuB;AAC/K;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;EACnD,IAAIC,KAAK,GAAGH,EAAE,CAACI,IAAI;IAAEC,KAAK,GAAGhB,MAAM,CAACW,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;EACjD,IAAIM,KAAK,GAAGL,EAAE,CAACG,IAAI;IAAEG,KAAK,GAAGlB,MAAM,CAACY,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;EACjD,OAAQX,KAAK,CAACe,KAAK,EAAEE,KAAK,CAAC,IACvBC,mBAAmB,CAACd,iBAAiB,CAACK,KAAK,CAAC,CAACU,YAAY,EAAEN,KAAK,EAAEG,KAAK,EAAE;IACrEI,WAAW,EAAEnB,iBAAiB,CAACC,sBAAsB,CAACO,KAAK,CAAC,CAAC;IAC7DG,SAAS,EAAEA;EACf,CAAC,CAAC;AACV;AACA,SAASM,mBAAmBA,CAACC,YAAY,EAAEE,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAClE,IAAIF,OAAO,KAAKC,OAAO,EAAE;IACrB,OAAO,IAAI;EACf;EACA,IAAIE,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACA;EACA;EACA,OAAON,YAAY,CAACO,UAAU,CAACC,KAAK,CAAC,UAAUC,SAAS,EAAE;IACtD;IACA;IACA,IAAIJ,cAAc,CAACK,GAAG,CAACD,SAAS,CAAC,EAC7B,OAAO,IAAI;IACfJ,cAAc,CAACM,GAAG,CAACF,SAAS,CAAC;IAC7B;IACA,IAAI,CAACrB,aAAa,CAACqB,SAAS,EAAEL,OAAO,CAACX,SAAS,CAAC,EAC5C,OAAO,IAAI;IACf;IACA;IACA,IAAImB,gCAAgC,CAACH,SAAS,CAAC,EAC3C,OAAO,IAAI;IACf,IAAIvB,OAAO,CAACuB,SAAS,CAAC,EAAE;MACpB,IAAII,SAAS,GAAG1B,sBAAsB,CAACsB,SAAS,CAAC;MACjD,IAAIK,YAAY,GAAGZ,OAAO,IAAIA,OAAO,CAACW,SAAS,CAAC;MAChD,IAAIE,YAAY,GAAGZ,OAAO,IAAIA,OAAO,CAACU,SAAS,CAAC;MAChD,IAAIG,iBAAiB,GAAGP,SAAS,CAACT,YAAY;MAC9C,IAAI,CAACgB,iBAAiB,EAAE;QACpB;QACA;QACA,OAAOnC,KAAK,CAACiC,YAAY,EAAEC,YAAY,CAAC;MAC5C;MACA,IAAIE,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACL,YAAY,CAAC;MAC/C,IAAIM,aAAa,GAAGF,KAAK,CAACC,OAAO,CAACJ,YAAY,CAAC;MAC/C,IAAIE,aAAa,KAAKG,aAAa,EAC/B,OAAO,KAAK;MAChB,IAAIH,aAAa,IAAIG,aAAa,EAAE;QAChC,IAAIC,QAAQ,GAAGP,YAAY,CAACQ,MAAM;QAClC,IAAIP,YAAY,CAACO,MAAM,KAAKD,QAAQ,EAAE;UAClC,OAAO,KAAK;QAChB;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAE,EAAEE,CAAC,EAAE;UAC/B,IAAI,CAACxB,mBAAmB,CAACiB,iBAAiB,EAAEF,YAAY,CAACS,CAAC,CAAC,EAAER,YAAY,CAACQ,CAAC,CAAC,EAAEnB,OAAO,CAAC,EAAE;YACpF,OAAO,KAAK;UAChB;QACJ;QACA,OAAO,IAAI;MACf;MACA,OAAOL,mBAAmB,CAACiB,iBAAiB,EAAEF,YAAY,EAAEC,YAAY,EAAEX,OAAO,CAAC;IACtF,CAAC,MACI;MACD,IAAIoB,QAAQ,GAAGxC,wBAAwB,CAACyB,SAAS,EAAEL,OAAO,CAACH,WAAW,CAAC;MACvE,IAAIuB,QAAQ,EAAE;QACV;QACA;QACA,IAAIZ,gCAAgC,CAACY,QAAQ,CAAC,EAC1C,OAAO,IAAI;QACf,OAAOzB,mBAAmB,CAACyB,QAAQ,CAACxB,YAAY;QAChD;QACA;QACA;QACA;QACAE,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC;MAC9B;IACJ;EACJ,CAAC,CAAC;AACN;AACA,SAASQ,gCAAgCA,CAACH,SAAS,EAAE;EACjD,OAAQ,CAAC,CAACA,SAAS,CAACgB,UAAU,IAAIhB,SAAS,CAACgB,UAAU,CAACC,IAAI,CAACC,sBAAsB,CAAC;AACvF;AACA,SAASA,sBAAsBA,CAACC,GAAG,EAAE;EACjC,OAAOA,GAAG,CAACC,IAAI,CAACC,KAAK,KAAK,aAAa;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}