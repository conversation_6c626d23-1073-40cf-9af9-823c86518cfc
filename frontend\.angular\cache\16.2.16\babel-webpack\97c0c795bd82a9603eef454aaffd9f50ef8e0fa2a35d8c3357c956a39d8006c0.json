{"ast": null, "code": "import { Subscription, interval } from 'rxjs';\nimport { CallType } from 'src/app/models/message.model';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"src/app/services/logger.service\";\nimport * as i6 from \"@app/services/theme.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nfunction UserListComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Affichage de \", ctx_r0.users.length, \" sur \", ctx_r0.totalUsers, \" utilisateurs\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r0.currentPage, \" sur \", ctx_r0.totalPages, \"\");\n  }\n}\nfunction UserListComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 51);\n    i0.ɵɵtext(4, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6, \" Essayez un autre terme de recherche ou effacez les filtres \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_ul_61_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.startAudioCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 68);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startVideoCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 55)(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const user_r7 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.startConversation(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵelement(3, \"img\", 58);\n    i0.ɵɵtemplate(4, UserListComponent_ul_61_li_1_span_4_Template, 1, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"h3\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 62);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵtemplate(11, UserListComponent_ul_61_li_1_button_11_Template, 2, 0, \"button\", 64);\n    i0.ɵɵtemplate(12, UserListComponent_ul_61_li_1_button_12_Template, 2, 0, \"button\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r7.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r7.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r7.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n  }\n}\nfunction UserListComponent_ul_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 53);\n    i0.ɵɵtemplate(1, UserListComponent_ul_61_li_1_Template, 13, 6, \"li\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.users);\n  }\n}\nfunction UserListComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"div\", 73)(3, \"div\", 74)(4, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47);\n    i0.ɵɵtext(6, \" Chargement de plus d'utilisateurs... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_63_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNextPage());\n    });\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵtext(3, \" Charger plus d'utilisateurs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class UserListComponent {\n  constructor(MessageService, router, route, authService, toastService, logger, themeService) {\n    this.MessageService = MessageService;\n    this.router = router;\n    this.route = route;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.logger = logger;\n    this.themeService = themeService;\n    this.users = [];\n    this.loading = true;\n    this.currentUserId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.pageSize = 10;\n    this.totalUsers = 0;\n    this.totalPages = 0;\n    this.hasNextPage = false;\n    this.hasPreviousPage = false;\n    // Sorting and filtering\n    this.sortBy = 'username';\n    this.sortOrder = 'asc';\n    this.filterForm = new FormGroup({\n      searchQuery: new FormControl(''),\n      isOnline: new FormControl(null)\n    });\n    // Auto-refresh\n    this.autoRefreshEnabled = true;\n    this.autoRefreshInterval = 30000; // 30 seconds\n    this.loadingMore = false;\n    this.subscriptions = new Subscription();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n  setupFilterListeners() {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm.get('searchQuery').valueChanges.subscribe(() => {\n      this.resetPagination();\n      this.loadUsers();\n    });\n    this.subscriptions.add(searchSub);\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm.get('isOnline').valueChanges.subscribe(() => {\n      this.resetPagination();\n      this.loadUsers();\n    });\n    this.subscriptions.add(onlineSub);\n  }\n  setupAutoRefresh() {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(this.autoRefreshInterval).subscribe(() => {\n        // Only refresh if not currently loading and no active search\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.logger.debug('Auto-refreshing user list');\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n  toggleAutoRefresh() {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n      this.logger.info('Auto-refresh enabled');\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n      this.logger.info('Auto-refresh disabled');\n    }\n  }\n  resetPagination() {\n    this.currentPage = 1;\n  }\n  // Get searchQuery from the form\n  get searchQuery() {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n  // Set searchQuery in the form\n  set searchQuery(value) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n  // Helper function for template type casting\n  $any(item) {\n    return item;\n  }\n  loadUsers(forceRefresh = false) {\n    if (this.loadingMore) return;\n    this.loading = true;\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n    this.logger.info('Loading users', {\n      searchQuery: searchQuery || '(empty)',\n      currentUserId: this.currentUserId || '(not logged in)',\n      page: this.currentPage,\n      limit: this.pageSize,\n      sortBy: this.sortBy,\n      sortOrder: this.sortOrder,\n      isOnline: isOnline\n    });\n    if (!this.currentUserId) {\n      this.logger.warn('Loading users without being logged in');\n    }\n    const sub = this.MessageService.getAllUsers(forceRefresh, searchQuery, this.currentPage, this.pageSize, this.sortBy, this.sortOrder, isOnline === true).subscribe({\n      next: users => {\n        this.logger.debug(`Received ${users.length} users from service`);\n        if (!Array.isArray(users)) {\n          this.logger.error('Received invalid users data (not an array)');\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter(user => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter(newUser => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return userId !== this.currentUserId && !this.users.some(existingUser => (existingUser.id || existingUser._id) === userId);\n          });\n          this.users = [...this.users, ...newUsers];\n        }\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n        this.loading = false;\n        this.loadingMore = false;\n        this.logger.info('Users loaded successfully', {\n          totalUsers: this.totalUsers,\n          filteredUsers: this.users.length,\n          currentPage: this.currentPage,\n          totalPages: this.totalPages\n        });\n      },\n      error: error => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.logger.error('Failed to load users', error);\n        this.toastService.showError(`Failed to load users: ${error.message || 'Unknown error'}`);\n        // Reset the users list in case of error\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        // Ensure loading indicator is disabled even in case of completion without data\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  startConversation(userId) {\n    if (!userId) {\n      this.logger.error('Cannot start conversation: userId is undefined');\n      this.toastService.showError('Cannot start conversation with undefined user');\n      return;\n    }\n    this.logger.info('Creating conversation with user', {\n      userId\n    });\n    // Afficher un indicateur de chargement\n    this.toastService.showInfo('Creating conversation...');\n    this.MessageService.createConversation(userId).subscribe({\n      next: conversation => {\n        if (!conversation || !conversation.id) {\n          this.logger.error('Received invalid conversation object');\n          this.toastService.showError('Failed to create conversation: Invalid response');\n          return;\n        }\n        this.logger.info('Conversation created successfully', {\n          conversationId: conversation.id,\n          participantCount: conversation.participants?.length || 0\n        });\n        // Naviguer vers la conversation avec un chemin absolu\n        this.router.navigate(['/messages/conversations/chat', conversation.id]).then(success => {\n          if (!success) {\n            this.logger.error(`Failed to navigate to conversation ${conversation.id}`);\n            this.toastService.showError('Failed to open conversation');\n          }\n        });\n      },\n      error: error => {\n        this.logger.error('Error creating conversation', error);\n        this.toastService.showError(`Failed to create conversation: ${error.message || 'Unknown error'}`);\n      }\n    });\n  }\n  // Initier un appel audio\n  startAudioCall(userId) {\n    if (!userId) return;\n    this.logger.debug('Starting audio call with user', {\n      userId\n    });\n    this.MessageService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: call => {\n        this.logger.debug('Audio call initiated successfully', {\n          callId: call.id\n        });\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: error => {\n        this.logger.error('Error initiating audio call', error);\n        this.toastService.showError('Failed to initiate audio call');\n      }\n    });\n  }\n  // Initier un appel vidéo\n  startVideoCall(userId) {\n    if (!userId) return;\n    this.logger.debug('Starting video call with user', {\n      userId\n    });\n    this.MessageService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: call => {\n        this.logger.debug('Video call initiated successfully', {\n          callId: call.id\n        });\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: error => {\n        this.logger.error('Error initiating video call', error);\n        this.toastService.showError('Failed to initiate video call');\n      }\n    });\n  }\n  loadNextPage() {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n  loadPreviousPage() {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n  refreshUsers() {\n    this.logger.info('Manually refreshing user list');\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  clearFilters() {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  changeSortOrder(field) {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.logger.info('UserList', 'Navigating back to conversations list');\n    this.router.navigate(['/messages/conversations']);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function UserListComponent_Factory(t) {\n      return new (t || UserListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.LoggerService), i0.ɵɵdirectiveInject(i6.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserListComponent,\n      selectors: [[\"app-user-list\"]],\n      decls: 64,\n      vars: 18,\n      consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"futuristic-users-container\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"top-[40%]\", \"right-[30%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-br\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"bottom-[60%]\", \"left-[25%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-0\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"futuristic-users-header\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"futuristic-title\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir la liste\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"space-y-3\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des utilisateurs...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-2\", \"rounded-lg\", \"futuristic-input-field\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"futuristic-checkbox-container\"], [\"type\", \"checkbox\", \"id\", \"onlineFilter\", 1, \"futuristic-checkbox\", 3, \"checked\", \"change\"], [1, \"futuristic-checkbox-checkmark\"], [\"for\", \"onlineFilter\", 1, \"futuristic-label\"], [1, \"futuristic-label\"], [1, \"futuristic-select\", 3, \"change\"], [\"value\", \"username\", 3, \"selected\"], [\"value\", \"email\", 3, \"selected\"], [\"value\", \"lastActive\", 3, \"selected\"], [1, \"futuristic-sort-button\", 3, \"title\", \"click\"], [1, \"futuristic-clear-button\", 3, \"click\"], [\"class\", \"flex justify-between items-center futuristic-pagination-info\", 4, \"ngIf\"], [1, \"futuristic-users-list\", 3, \"scroll\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-users-grid\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [\"class\", \"futuristic-load-more-container\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"futuristic-pagination-info\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-users\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-users-grid\"], [\"class\", \"futuristic-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-user-card\"], [1, \"futuristic-user-content\", 3, \"click\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"futuristic-online-indicator\", 4, \"ngIf\"], [1, \"futuristic-user-info\"], [1, \"futuristic-username\"], [1, \"futuristic-user-email\"], [1, \"futuristic-call-buttons\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel audio\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel vid\\u00E9o\", 3, \"click\", 4, \"ngIf\"], [1, \"futuristic-online-indicator\"], [\"title\", \"Appel audio\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-dots\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"futuristic-load-more-container\"], [1, \"futuristic-load-more-button\", 3, \"click\"], [1, \"fas\", \"fa-chevron-down\", \"mr-2\"]],\n      template: function UserListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8)(17, \"div\", 8)(18, \"div\", 8)(19, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 9);\n          i0.ɵɵelement(21, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 12)(24, \"h1\", 13);\n          i0.ɵɵtext(25, \"Nouvelle Conversation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_27_listener() {\n            return ctx.refreshUsers();\n          });\n          i0.ɵɵelement(28, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_29_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 19)(32, \"div\", 20)(33, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"div\", 24)(37, \"div\", 25)(38, \"label\", 26)(39, \"input\", 27);\n          i0.ɵɵlistener(\"change\", function UserListComponent_Template_input_change_39_listener($event) {\n            let tmp_b_0;\n            return (tmp_b_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_b_0.setValue($event.target.checked ? true : null);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"label\", 29);\n          i0.ɵɵtext(42, \"En ligne uniquement\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 25)(44, \"span\", 30);\n          i0.ɵɵtext(45, \"Trier par:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 31);\n          i0.ɵɵlistener(\"change\", function UserListComponent_Template_select_change_46_listener($event) {\n            return ctx.changeSortOrder($event.target.value);\n          });\n          i0.ɵɵelementStart(47, \"option\", 32);\n          i0.ɵɵtext(48, \" Nom \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 33);\n          i0.ɵɵtext(50, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 34);\n          i0.ɵɵtext(52, \" Derni\\u00E8re activit\\u00E9 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_53_listener() {\n            ctx.sortOrder = ctx.sortOrder === \"asc\" ? \"desc\" : \"asc\";\n            return ctx.loadUsers(true);\n          });\n          i0.ɵɵelement(54, \"i\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_55_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵtext(56, \" Effacer les filtres \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(57, UserListComponent_div_57_Template, 5, 4, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 38);\n          i0.ɵɵlistener(\"scroll\", function UserListComponent_Template_div_scroll_58_listener($event) {\n            return $event.target.scrollTop + $event.target.clientHeight >= $event.target.scrollHeight - 200 && ctx.loadNextPage();\n          });\n          i0.ɵɵtemplate(59, UserListComponent_div_59_Template, 4, 0, \"div\", 39);\n          i0.ɵɵtemplate(60, UserListComponent_div_60_Template, 7, 0, \"div\", 40);\n          i0.ɵɵtemplate(61, UserListComponent_ul_61_Template, 2, 1, \"ul\", 41);\n          i0.ɵɵtemplate(62, UserListComponent_div_62_Template, 7, 0, \"div\", 42);\n          i0.ɵɵtemplate(63, UserListComponent_div_63_Template, 4, 0, \"div\", 43);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"checked\", ((tmp_2_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_2_0.value) === true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"username\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"email\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"lastActive\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"title\", ctx.sortOrder === \"asc\" ? \"Ordre croissant\" : \"Ordre d\\u00E9croissant\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.sortOrder === \"asc\" ? \"fas fa-sort-up\" : \"fas fa-sort-down\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalUsers > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.users.length);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.users.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.users.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.users.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasNextPage && !ctx.loading);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.NgSelectOption, i8.ɵNgSelectMultipleOption, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i7.AsyncPipe],\n      styles: [\"\\n\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%] {\\n  background-color: #f0f4f8;\\n  color: #6d6870;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_grid-pulse {\\n  0% {\\n    opacity: 0.3;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n  100% {\\n    opacity: 0.3;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_scan {\\n  0% {\\n    top: -10%;\\n    opacity: 0.5;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n  100% {\\n    top: 110%;\\n    opacity: 0.5;\\n  }\\n}\\n\\n.animate-scan[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_scan 8s linear infinite;\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(\\n      rgba(79, 95, 173, 0.03) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(90deg, rgba(79, 95, 173, 0.03) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: linear-gradient(\\n      rgba(0, 247, 255, 0.07) 1px,\\n      transparent 1px\\n    ),\\n    linear-gradient(90deg, rgba(0, 247, 255, 0.07) 1px, transparent 1px),\\n    linear-gradient(rgba(0, 247, 255, 0.03) 1px, transparent 1px),\\n    linear-gradient(90deg, rgba(0, 247, 255, 0.03) 1px, transparent 1px);\\n  background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;\\n  pointer-events: none;\\n  z-index: 0;\\n  animation: _ngcontent-%COMP%_grid-pulse 4s infinite ease-in-out;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::after, .dark   [_nghost-%COMP%]   .futuristic-users-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: repeating-linear-gradient(\\n    to bottom,\\n    transparent,\\n    transparent 50px,\\n    rgba(0, 247, 255, 0.03) 50px,\\n    rgba(0, 247, 255, 0.03) 51px\\n  );\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid rgba(79, 95, 173, 0.2);\\n  background-color: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\\n  background-color: rgba(30, 30, 30, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: 0 0 10px rgba(79, 95, 173, 0.5);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  background: linear-gradient(\\n    135deg,\\n    var(--accent-color),\\n    var(--secondary-color)\\n  );\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  color: transparent;\\n  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  border-radius: var(--border-radius-md);\\n  color: #6d6870;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n  outline: none;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, :not(.dark)   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: var(--border-radius-md);\\n  color: var(--text-light);\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-color: var(--accent-color);\\n  box-shadow: var(--glow-effect);\\n  outline: none;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   .futuristic-input-field[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-dim);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 18px;\\n  width: 18px;\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  border-radius: 4px;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\\n  background-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n  left: 6px;\\n  top: 2px;\\n  width: 5px;\\n  height: 10px;\\n  border: solid white;\\n  border-width: 0 2px 2px 0;\\n  transform: rotate(45deg);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%] {\\n  color: #6d6870;\\n  font-size: 0.875rem;\\n  transition: color var(--transition-fast);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  height: 0;\\n  width: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 18px;\\n  width: 18px;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: 4px;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  display: none;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:checked    ~ .futuristic-checkbox-checkmark[_ngcontent-%COMP%]:after {\\n  display: block;\\n  left: 6px;\\n  top: 2px;\\n  width: 5px;\\n  height: 10px;\\n  border: solid white;\\n  border-width: 0 2px 2px 0;\\n  transform: rotate(45deg);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-label[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  transition: color var(--transition-fast);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  border-radius: var(--border-radius-md);\\n  color: #6d6870;\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n  appearance: none;\\n  background-image: url(\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f5fad' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 0.5rem center;\\n  background-size: 1em;\\n  padding-right: 2rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n  outline: none;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: var(--border-radius-md);\\n  color: var(--text-light);\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n  appearance: none;\\n  background-image: url(\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300f7ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 0.5rem center;\\n  background-size: 1em;\\n  padding-right: 2rem;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]:focus {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-color: var(--accent-color);\\n  box-shadow: var(--glow-effect);\\n  outline: none;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n  color: var(--text-light);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(79, 95, 173, 0.05);\\n  color: #4f5fad;\\n  border: none;\\n  border-radius: var(--border-radius-sm);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.1);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: var(--border-radius-sm);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-sort-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #4f5fad;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: var(--border-radius-sm);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover {\\n  color: #7826b5;\\n  background-color: rgba(79, 95, 173, 0.05);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--accent-color);\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: var(--border-radius-sm);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-clear-button[_ngcontent-%COMP%]:hover {\\n  color: var(--secondary-color);\\n  background-color: rgba(0, 247, 255, 0.05);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6d6870;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-dim);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: thin;\\n  scrollbar-color: #4f5fad transparent;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #4f5fad;\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--accent-color) transparent;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-users-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: 2rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 2px solid transparent;\\n  border-top-color: #4f5fad;\\n  border-bottom-color: #7826b5;\\n  animation: _ngcontent-%COMP%_futuristic-spin-light 1.2s linear infinite;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_futuristic-spin-light {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: #6d6870;\\n  font-size: 0.875rem;\\n  text-align: center;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: 2rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  border: 2px solid transparent;\\n  border-top-color: var(--accent-color);\\n  border-bottom-color: var(--secondary-color);\\n  animation: _ngcontent-%COMP%_futuristic-spin-dark 1.2s linear infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_futuristic-spin-dark {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n  text-align: center;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: 2rem;\\n  text-align: center;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #4f5fad;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin-bottom: 0.5rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: #6d6870;\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: 2rem;\\n  text-align: center;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: var(--accent-color);\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1rem;\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%] {\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border: 1px solid rgba(79, 95, 173, 0.1);\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  transition: all var(--transition-fast);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n  background-color: rgba(79, 95, 173, 0.1);\\n  border-color: rgba(79, 95, 173, 0.3);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  cursor: pointer;\\n  flex: 1;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  flex-shrink: 0;\\n  margin-right: 1rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  border: 2px solid rgba(79, 95, 173, 0.3);\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-color: #4f5fad;\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.5);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 12px;\\n  height: 12px;\\n  background-color: #4caf50;\\n  border-radius: 50%;\\n  border: 2px solid #ffffff;\\n  box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);\\n  animation: _ngcontent-%COMP%_pulse-light 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-light {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);\\n  }\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #4f5fad;\\n  margin-bottom: 0.25rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6d6870;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(79, 95, 173, 0.05);\\n  border-top: 1px solid rgba(79, 95, 173, 0.1);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(79, 95, 173, 0.1);\\n  color: #4f5fad;\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  margin-left: 0.5rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(79, 95, 173, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-users-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1rem;\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border: 1px solid rgba(0, 247, 255, 0.1);\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  transition: all var(--transition-fast);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: var(--glow-effect);\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-color: rgba(0, 247, 255, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  cursor: pointer;\\n  flex: 1;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  flex-shrink: 0;\\n  margin-right: 1rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.5);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 12px;\\n  height: 12px;\\n  background-color: var(--success-color);\\n  border-radius: 50%;\\n  border: 2px solid var(--dark-bg);\\n  box-shadow: 0 0 8px rgba(0, 255, 128, 0.8);\\n  animation: _ngcontent-%COMP%_pulse-dark 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-dark {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0.4);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 6px rgba(0, 255, 128, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0);\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-username[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--text-light);\\n  margin-bottom: 0.25rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-user-email[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-dim);\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  padding: 0.5rem 1rem;\\n  background-color: rgba(0, 0, 0, 0.2);\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n  margin-left: 0.5rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-call-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 1rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 0.5rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  margin: 0 4px;\\n  background-color: #4f5fad;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_dot-pulse-light 1.4s infinite ease-in-out;\\n  box-shadow: 0 0 8px rgba(79, 95, 173, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse-light {\\n  0%,\\n  100% {\\n    transform: scale(0.5);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 1rem;\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.1),\\n    rgba(79, 95, 173, 0.2)\\n  );\\n  color: #4f5fad;\\n  border: 1px solid rgba(79, 95, 173, 0.3);\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(79, 95, 173, 0.2),\\n    rgba(79, 95, 173, 0.3)\\n  );\\n  transform: translateY(-2px);\\n  box-shadow: 0 0 15px rgba(79, 95, 173, 0.4);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-more[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 1rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  margin: 0 4px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_dot-pulse-dark 1.4s infinite ease-in-out;\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse-dark {\\n  0%,\\n  100% {\\n    transform: scale(0.5);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: 1rem;\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 0.5rem 1rem;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.1),\\n    rgba(0, 247, 255, 0.2)\\n  );\\n  color: var(--accent-color);\\n  border: 1px solid rgba(0, 247, 255, 0.3);\\n  border-radius: var(--border-radius-md);\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.dark[_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-load-more-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.2),\\n    rgba(0, 247, 255, 0.3)\\n  );\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subscription", "interval", "CallType", "FormControl", "FormGroup", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "users", "length", "totalUsers", "currentPage", "totalPages", "ɵɵelement", "ɵɵlistener", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r13", "user_r7", "ɵɵnextContext", "$implicit", "ctx_r11", "ɵɵresetView", "startAudioCall", "id", "_id", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "ctx_r14", "startVideoCall", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "restoredCtx", "_r18", "ctx_r17", "startConversation", "ɵɵtemplate", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "isOnline", "ɵɵtextInterpolate1", "username", "ɵɵtextInterpolate", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNextPage", "UserListComponent", "constructor", "MessageService", "router", "route", "authService", "toastService", "logger", "themeService", "loading", "currentUserId", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "searchQuery", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "subscriptions", "isDarkMode$", "darkMode$", "ngOnInit", "getCurrentUserId", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "get", "valueChanges", "subscribe", "resetPagination", "add", "onlineSub", "autoRefreshSubscription", "value", "debug", "toggleAutoRefresh", "info", "unsubscribe", "undefined", "setValue", "$any", "item", "forceRefresh", "page", "limit", "warn", "sub", "getAllUsers", "next", "Array", "isArray", "error", "showError", "filter", "user", "userId", "newUsers", "newUser", "some", "existingUser", "pagination", "currentUserPagination", "totalCount", "filteredUsers", "message", "complete", "showInfo", "createConversation", "conversation", "conversationId", "participantCount", "participants", "navigate", "then", "success", "initiateCall", "AUDIO", "call", "callId", "showSuccess", "VIDEO", "loadPreviousPage", "refreshUsers", "clearFilters", "reset", "changeSortOrder", "field", "goBackToConversations", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "i2", "Router", "ActivatedRoute", "i3", "AuthuserService", "i4", "ToastService", "i5", "LoggerService", "i6", "ThemeService", "selectors", "decls", "vars", "consts", "template", "UserListComponent_Template", "rf", "ctx", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "$event", "UserListComponent_Template_input_change_39_listener", "tmp_b_0", "target", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "scrollTop", "clientHeight", "scrollHeight", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "ɵɵclassProp", "ɵɵpipeBind1", "tmp_2_0", "ɵɵclassMap"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\user-list\\user-list.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\messages\\user-list\\user-list.component.html"], "sourcesContent": ["// user-list.component.ts\r\nimport { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { Subscription, interval, Observable } from 'rxjs';\r\nimport { User } from 'src/app/models/user.model';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { ToastService } from 'src/app/services/toast.service';\r\nimport { MessageService } from 'src/app/services/message.service';\r\nimport { CallType } from 'src/app/models/message.model';\r\nimport { LoggerService } from 'src/app/services/logger.service';\r\nimport { FormControl, FormGroup } from '@angular/forms';\r\nimport { ThemeService } from '@app/services/theme.service';\r\n\r\n@Component({\r\n  selector: 'app-user-list',\r\n  templateUrl: './user-list.component.html',\r\n  styleUrls: ['./user-list.component.css'],\r\n})\r\nexport class UserListComponent implements OnInit, OnDestroy {\r\n  users: User[] = [];\r\n  loading = true;\r\n  currentUserId: string | null = null;\r\n  isDarkMode$: Observable<boolean>;\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  pageSize = 10;\r\n  totalUsers = 0;\r\n  totalPages = 0;\r\n  hasNextPage = false;\r\n  hasPreviousPage = false;\r\n\r\n  // Sorting and filtering\r\n  sortBy = 'username';\r\n  sortOrder = 'asc';\r\n  filterForm = new FormGroup({\r\n    searchQuery: new FormControl(''),\r\n    isOnline: new FormControl<boolean | null>(null),\r\n  });\r\n\r\n  // Auto-refresh\r\n  autoRefreshEnabled = true;\r\n  autoRefreshInterval = 30000; // 30 seconds\r\n  private autoRefreshSubscription?: Subscription;\r\n\r\n  private loadingMore = false;\r\n  private subscriptions: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private MessageService: MessageService,\r\n    public router: Router,\r\n    public route: ActivatedRoute,\r\n    private authService: AuthuserService,\r\n    private toastService: ToastService,\r\n    private logger: LoggerService,\r\n    private themeService: ThemeService\r\n  ) {\r\n    this.isDarkMode$ = this.themeService.darkMode$;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.currentUserId = this.authService.getCurrentUserId();\r\n    this.setupFilterListeners();\r\n    this.setupAutoRefresh();\r\n    this.loadUsers();\r\n  }\r\n\r\n  private setupFilterListeners(): void {\r\n    // Subscribe to search query changes\r\n    const searchSub = this.filterForm\r\n      .get('searchQuery')!\r\n      .valueChanges.subscribe(() => {\r\n        this.resetPagination();\r\n        this.loadUsers();\r\n      });\r\n\r\n    this.subscriptions.add(searchSub);\r\n\r\n    // Subscribe to online status filter changes\r\n    const onlineSub = this.filterForm\r\n      .get('isOnline')!\r\n      .valueChanges.subscribe(() => {\r\n        this.resetPagination();\r\n        this.loadUsers();\r\n      });\r\n\r\n    this.subscriptions.add(onlineSub);\r\n  }\r\n\r\n  private setupAutoRefresh(): void {\r\n    if (this.autoRefreshEnabled) {\r\n      this.autoRefreshSubscription = interval(\r\n        this.autoRefreshInterval\r\n      ).subscribe(() => {\r\n        // Only refresh if not currently loading and no active search\r\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\r\n          this.logger.debug('Auto-refreshing user list');\r\n          this.loadUsers(true);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  toggleAutoRefresh(): void {\r\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\r\n\r\n    if (this.autoRefreshEnabled) {\r\n      this.setupAutoRefresh();\r\n      this.logger.info('Auto-refresh enabled');\r\n    } else if (this.autoRefreshSubscription) {\r\n      this.autoRefreshSubscription.unsubscribe();\r\n      this.autoRefreshSubscription = undefined;\r\n      this.logger.info('Auto-refresh disabled');\r\n    }\r\n  }\r\n\r\n  resetPagination(): void {\r\n    this.currentPage = 1;\r\n  }\r\n\r\n  // Get searchQuery from the form\r\n  get searchQuery(): string {\r\n    return this.filterForm.get('searchQuery')?.value || '';\r\n  }\r\n\r\n  // Set searchQuery in the form\r\n  set searchQuery(value: string) {\r\n    this.filterForm.get('searchQuery')?.setValue(value);\r\n  }\r\n\r\n  // Helper function for template type casting\r\n  $any(item: any): any {\r\n    return item;\r\n  }\r\n\r\n  loadUsers(forceRefresh = false): void {\r\n    if (this.loadingMore) return;\r\n\r\n    this.loading = true;\r\n\r\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\r\n    const isOnline = this.filterForm.get('isOnline')?.value;\r\n\r\n    this.logger.info('Loading users', {\r\n      searchQuery: searchQuery || '(empty)',\r\n      currentUserId: this.currentUserId || '(not logged in)',\r\n      page: this.currentPage,\r\n      limit: this.pageSize,\r\n      sortBy: this.sortBy,\r\n      sortOrder: this.sortOrder,\r\n      isOnline: isOnline,\r\n    });\r\n\r\n    if (!this.currentUserId) {\r\n      this.logger.warn('Loading users without being logged in');\r\n    }\r\n\r\n    const sub = this.MessageService.getAllUsers(\r\n      forceRefresh,\r\n      searchQuery,\r\n      this.currentPage,\r\n      this.pageSize,\r\n      this.sortBy,\r\n      this.sortOrder,\r\n      isOnline === true\r\n    ).subscribe({\r\n      next: (users) => {\r\n        this.logger.debug(`Received ${users.length} users from service`);\r\n\r\n        if (!Array.isArray(users)) {\r\n          this.logger.error('Received invalid users data (not an array)');\r\n          this.users = [];\r\n          this.loading = false;\r\n          this.loadingMore = false;\r\n          this.toastService.showError('Failed to load users: Invalid data');\r\n          return;\r\n        }\r\n\r\n        // If first page, replace users array; otherwise append\r\n        if (this.currentPage === 1) {\r\n          // Filter out current user\r\n          this.users = users.filter((user) => {\r\n            if (!user) return false;\r\n            const userId = user.id || user._id;\r\n            return userId !== this.currentUserId;\r\n          });\r\n        } else {\r\n          // Append new users to existing array, avoiding duplicates and filtering out current user\r\n          const newUsers = users.filter((newUser) => {\r\n            if (!newUser) return false;\r\n            const userId = newUser.id || newUser._id;\r\n            return (\r\n              userId !== this.currentUserId &&\r\n              !this.users.some(\r\n                (existingUser) =>\r\n                  (existingUser.id || existingUser._id) === userId\r\n              )\r\n            );\r\n          });\r\n\r\n          this.users = [...this.users, ...newUsers];\r\n        }\r\n\r\n        // Update pagination metadata from service\r\n        const pagination = this.MessageService.currentUserPagination;\r\n        this.totalUsers = pagination.totalCount;\r\n        this.totalPages = pagination.totalPages;\r\n        this.hasNextPage = pagination.hasNextPage;\r\n        this.hasPreviousPage = pagination.hasPreviousPage;\r\n\r\n        this.loading = false;\r\n        this.loadingMore = false;\r\n\r\n        this.logger.info('Users loaded successfully', {\r\n          totalUsers: this.totalUsers,\r\n          filteredUsers: this.users.length,\r\n          currentPage: this.currentPage,\r\n          totalPages: this.totalPages,\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        this.loadingMore = false;\r\n        this.logger.error('Failed to load users', error);\r\n        this.toastService.showError(\r\n          `Failed to load users: ${error.message || 'Unknown error'}`\r\n        );\r\n\r\n        // Reset the users list in case of error\r\n        if (this.currentPage === 1) {\r\n          this.users = [];\r\n        }\r\n      },\r\n      complete: () => {\r\n        // Ensure loading indicator is disabled even in case of completion without data\r\n        this.loading = false;\r\n        this.loadingMore = false;\r\n      },\r\n    });\r\n\r\n    this.subscriptions.add(sub);\r\n  }\r\n\r\n  startConversation(userId: string | undefined) {\r\n    if (!userId) {\r\n      this.logger.error('Cannot start conversation: userId is undefined');\r\n      this.toastService.showError(\r\n        'Cannot start conversation with undefined user'\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.logger.info('Creating conversation with user', { userId });\r\n\r\n    // Afficher un indicateur de chargement\r\n    this.toastService.showInfo('Creating conversation...');\r\n\r\n    this.MessageService.createConversation(userId).subscribe({\r\n      next: (conversation) => {\r\n        if (!conversation || !conversation.id) {\r\n          this.logger.error('Received invalid conversation object');\r\n          this.toastService.showError(\r\n            'Failed to create conversation: Invalid response'\r\n          );\r\n          return;\r\n        }\r\n\r\n        this.logger.info('Conversation created successfully', {\r\n          conversationId: conversation.id,\r\n          participantCount: conversation.participants?.length || 0,\r\n        });\r\n\r\n        // Naviguer vers la conversation avec un chemin absolu\r\n        this.router\r\n          .navigate(['/messages/conversations/chat', conversation.id])\r\n          .then((success) => {\r\n            if (!success) {\r\n              this.logger.error(\r\n                `Failed to navigate to conversation ${conversation.id}`\r\n              );\r\n              this.toastService.showError('Failed to open conversation');\r\n            }\r\n          });\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('Error creating conversation', error);\r\n        this.toastService.showError(\r\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  // Initier un appel audio\r\n  startAudioCall(userId: string): void {\r\n    if (!userId) return;\r\n\r\n    this.logger.debug('Starting audio call with user', { userId });\r\n\r\n    this.MessageService.initiateCall(userId, CallType.AUDIO).subscribe({\r\n      next: (call) => {\r\n        this.logger.debug('Audio call initiated successfully', {\r\n          callId: call.id,\r\n        });\r\n        this.toastService.showSuccess('Audio call initiated');\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('Error initiating audio call', error);\r\n        this.toastService.showError('Failed to initiate audio call');\r\n      },\r\n    });\r\n  }\r\n\r\n  // Initier un appel vidéo\r\n  startVideoCall(userId: string): void {\r\n    if (!userId) return;\r\n\r\n    this.logger.debug('Starting video call with user', { userId });\r\n\r\n    this.MessageService.initiateCall(userId, CallType.VIDEO).subscribe({\r\n      next: (call) => {\r\n        this.logger.debug('Video call initiated successfully', {\r\n          callId: call.id,\r\n        });\r\n        this.toastService.showSuccess('Video call initiated');\r\n      },\r\n      error: (error) => {\r\n        this.logger.error('Error initiating video call', error);\r\n        this.toastService.showError('Failed to initiate video call');\r\n      },\r\n    });\r\n  }\r\n\r\n  loadNextPage(): void {\r\n    if (this.hasNextPage && !this.loading) {\r\n      this.loadingMore = true;\r\n      this.currentPage++;\r\n      this.loadUsers();\r\n    }\r\n  }\r\n\r\n  loadPreviousPage(): void {\r\n    if (this.hasPreviousPage && !this.loading) {\r\n      this.loadingMore = true;\r\n      this.currentPage--;\r\n      this.loadUsers();\r\n    }\r\n  }\r\n\r\n  refreshUsers(): void {\r\n    this.logger.info('Manually refreshing user list');\r\n    this.resetPagination();\r\n    this.loadUsers(true);\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.filterForm.reset({\r\n      searchQuery: '',\r\n      isOnline: null,\r\n    });\r\n    this.resetPagination();\r\n    this.loadUsers(true);\r\n  }\r\n\r\n  changeSortOrder(field: string): void {\r\n    if (this.sortBy === field) {\r\n      // Toggle sort order if clicking the same field\r\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      // Set new sort field with default ascending order\r\n      this.sortBy = field;\r\n      this.sortOrder = 'asc';\r\n    }\r\n\r\n    this.resetPagination();\r\n    this.loadUsers(true);\r\n  }\r\n\r\n  /**\r\n   * Navigue vers la liste des conversations\r\n   */\r\n  goBackToConversations(): void {\r\n    this.logger.info('UserList', 'Navigating back to conversations list');\r\n    this.router.navigate(['/messages/conversations']);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n    if (this.autoRefreshSubscription) {\r\n      this.autoRefreshSubscription.unsubscribe();\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  class=\"flex flex-col h-full futuristic-users-container\"\r\n  [class.dark]=\"isDarkMode$ | async\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <!-- Gradient orbs -->\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Additional glow effects for dark mode -->\r\n    <div\r\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern for light mode -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n        <div class=\"border-r border-[#4f5fad]\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Horizontal scan line effect for dark mode -->\r\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\r\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\r\n    </div>\r\n  </div>\r\n  <!-- En-tête -->\r\n  <div class=\"futuristic-users-header\">\r\n    <div class=\"flex justify-between items-center mb-4\">\r\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\r\n      <div class=\"flex space-x-2\">\r\n        <button\r\n          (click)=\"refreshUsers()\"\r\n          class=\"futuristic-action-button\"\r\n          title=\"Rafraîchir la liste\"\r\n        >\r\n          <i class=\"fas fa-sync-alt\"></i>\r\n        </button>\r\n        <button\r\n          (click)=\"goBackToConversations()\"\r\n          class=\"futuristic-action-button\"\r\n        >\r\n          <i class=\"fas fa-arrow-left\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Recherche et filtres -->\r\n    <div class=\"space-y-3\">\r\n      <!-- Recherche -->\r\n      <div class=\"relative\">\r\n        <input\r\n          [ngModel]=\"searchQuery\"\r\n          (ngModelChange)=\"searchQuery = $event\"\r\n          type=\"text\"\r\n          placeholder=\"Rechercher des utilisateurs...\"\r\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\r\n        />\r\n        <i\r\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\r\n        ></i>\r\n      </div>\r\n\r\n      <!-- Filtres -->\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- Filtre en ligne -->\r\n          <div class=\"flex items-center space-x-2\">\r\n            <label class=\"futuristic-checkbox-container\">\r\n              <input\r\n                type=\"checkbox\"\r\n                id=\"onlineFilter\"\r\n                class=\"futuristic-checkbox\"\r\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\r\n                (change)=\"\r\n                  filterForm\r\n                    .get('isOnline')\r\n                    ?.setValue($any($event.target).checked ? true : null)\r\n                \"\r\n              />\r\n              <span class=\"futuristic-checkbox-checkmark\"></span>\r\n            </label>\r\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\r\n              >En ligne uniquement</label\r\n            >\r\n          </div>\r\n\r\n          <!-- Options de tri -->\r\n          <div class=\"flex items-center space-x-2\">\r\n            <span class=\"futuristic-label\">Trier par:</span>\r\n            <select\r\n              (change)=\"changeSortOrder($any($event.target).value)\"\r\n              class=\"futuristic-select\"\r\n            >\r\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\r\n                Nom\r\n              </option>\r\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\r\n                Email\r\n              </option>\r\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\r\n                Dernière activité\r\n              </option>\r\n            </select>\r\n            <button\r\n              (click)=\"\r\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\r\n                loadUsers(true)\r\n              \"\r\n              class=\"futuristic-sort-button\"\r\n              [title]=\"\r\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\r\n              \"\r\n            >\r\n              <i\r\n                [class]=\"\r\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\r\n                \"\r\n              ></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Effacer les filtres -->\r\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\r\n          Effacer les filtres\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Info pagination -->\r\n      <div\r\n        *ngIf=\"totalUsers > 0\"\r\n        class=\"flex justify-between items-center futuristic-pagination-info\"\r\n      >\r\n        <span\r\n          >Affichage de {{ users.length }} sur\r\n          {{ totalUsers }} utilisateurs</span\r\n        >\r\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Liste des utilisateurs -->\r\n  <div\r\n    class=\"futuristic-users-list\"\r\n    (scroll)=\"\r\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\r\n        $any($event.target).scrollHeight - 200 && loadNextPage()\r\n    \"\r\n  >\r\n    <!-- État de chargement -->\r\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\r\n      <div class=\"futuristic-loading-circle\"></div>\r\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\r\n    </div>\r\n\r\n    <!-- État vide -->\r\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\r\n      <div class=\"futuristic-empty-icon\">\r\n        <i class=\"fas fa-users\"></i>\r\n      </div>\r\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\r\n      <p class=\"futuristic-empty-text\">\r\n        Essayez un autre terme de recherche ou effacez les filtres\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Liste des utilisateurs -->\r\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\r\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\r\n        <div\r\n          class=\"futuristic-user-content\"\r\n          (click)=\"startConversation(user.id || user._id)\"\r\n        >\r\n          <div class=\"futuristic-avatar\">\r\n            <img\r\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\r\n              alt=\"User avatar\"\r\n            />\r\n            <span\r\n              *ngIf=\"user.isOnline\"\r\n              class=\"futuristic-online-indicator\"\r\n            ></span>\r\n          </div>\r\n          <div class=\"futuristic-user-info\">\r\n            <h3 class=\"futuristic-username\">\r\n              {{ user.username }}\r\n            </h3>\r\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Boutons d'appel -->\r\n        <div class=\"futuristic-call-buttons\">\r\n          <button\r\n            *ngIf=\"user.isOnline\"\r\n            (click)=\"startAudioCall(user.id || user._id)\"\r\n            class=\"futuristic-call-button\"\r\n            title=\"Appel audio\"\r\n          >\r\n            <i class=\"fas fa-phone\"></i>\r\n          </button>\r\n          <button\r\n            *ngIf=\"user.isOnline\"\r\n            (click)=\"startVideoCall(user.id || user._id)\"\r\n            class=\"futuristic-call-button\"\r\n            title=\"Appel vidéo\"\r\n          >\r\n            <i class=\"fas fa-video\"></i>\r\n          </button>\r\n        </div>\r\n      </li>\r\n    </ul>\r\n\r\n    <!-- Indicateur de chargement supplémentaire -->\r\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\r\n      <div class=\"futuristic-loading-dots\">\r\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\r\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\r\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\r\n      </div>\r\n      <div class=\"futuristic-loading-text\">\r\n        Chargement de plus d'utilisateurs...\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bouton de chargement supplémentaire -->\r\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\r\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\r\n        <i class=\"fas fa-chevron-down mr-2\"></i>\r\n        Charger plus d'utilisateurs\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,QAAQ,QAAoB,MAAM;AAMzD,SAASC,QAAQ,QAAQ,8BAA8B;AAEvD,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;IC0IjDC,EAAA,CAAAC,cAAA,cAGC;IAEID,EAAA,CAAAE,MAAA,GAC4B;IAAAF,EAAA,CAAAG,YAAA,EAC9B;IACDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHrDH,EAAA,CAAAI,SAAA,GAC4B;IAD5BJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAC,KAAA,CAAAC,MAAA,WAAAF,MAAA,CAAAG,UAAA,kBAC4B;IAEzBT,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAK,kBAAA,UAAAC,MAAA,CAAAI,WAAA,WAAAJ,MAAA,CAAAK,UAAA,KAA2C;;;;;IAcrDX,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,cAAqC;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAI3EH,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,oCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAeEH,EAAA,CAAAY,SAAA,eAGQ;;;;;;IAYVZ,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAa,UAAA,mBAAAC,wEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAD,OAAA,CAAAE,cAAA,CAAAL,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAmC;IAAA,EAAC;IAI7CxB,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAa,UAAA,mBAAAY,wEAAA;MAAAzB,EAAA,CAAAe,aAAA,CAAAW,IAAA;MAAA,MAAAT,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,GAAAC,SAAA;MAAA,MAAAQ,OAAA,GAAA3B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAM,OAAA,CAAAC,cAAA,CAAAX,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAmC;IAAA,EAAC;IAI7CxB,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAxCbH,EAAA,CAAAC,cAAA,aAA4D;IAGxDD,EAAA,CAAAa,UAAA,mBAAAgB,2DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAe,aAAA,CAAAgB,IAAA;MAAA,MAAAd,OAAA,GAAAa,WAAA,CAAAX,SAAA;MAAA,MAAAa,OAAA,GAAAhC,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAW,OAAA,CAAAC,iBAAA,CAAAhB,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAsC;IAAA,EAAC;IAEhDxB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAY,SAAA,cAGE;IACFZ,EAAA,CAAAkC,UAAA,IAAAC,4CAAA,mBAGQ;IACVnC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAkC;IAE9BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKzDH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAkC,UAAA,KAAAE,+CAAA,qBAOS;IACTpC,EAAA,CAAAkC,UAAA,KAAAG,+CAAA,qBAOS;IACXrC,EAAA,CAAAG,YAAA,EAAM;;;;IAlCAH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAsC,UAAA,QAAArB,OAAA,CAAAsB,KAAA,wCAAAvC,EAAA,CAAAwC,aAAA,CAAwD;IAIvDxC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;IAMpBzC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0C,kBAAA,MAAAzB,OAAA,CAAA0B,QAAA,MACF;IACiC3C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4C,iBAAA,CAAA3B,OAAA,CAAA4B,KAAA,CAAgB;IAOhD7C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;IAQnBzC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;;;;;IAnC5BzC,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAkC,UAAA,IAAAY,qCAAA,kBA0CK;IACP9C,EAAA,CAAAG,YAAA,EAAK;;;;IA3CkBH,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAsC,UAAA,YAAAS,MAAA,CAAAxC,KAAA,CAAQ;;;;;IA8C/BP,EAAA,CAAAC,cAAA,cAAyE;IAErED,EAAA,CAAAY,SAAA,cAAsE;IAGxEZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIRH,EAAA,CAAAC,cAAA,cAA4E;IAClED,EAAA,CAAAa,UAAA,mBAAAmC,0DAAA;MAAAhD,EAAA,CAAAe,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAA6B,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9BnD,EAAA,CAAAY,SAAA,YAAwC;IACxCZ,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADxOf,OAAM,MAAOiD,iBAAiB;EA8B5BC,YACUC,cAA8B,EAC/BC,MAAc,EACdC,KAAqB,EACpBC,WAA4B,EAC5BC,YAA0B,EAC1BC,MAAqB,EACrBC,YAA0B;IAN1B,KAAAN,cAAc,GAAdA,cAAc;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IApCtB,KAAArD,KAAK,GAAW,EAAE;IAClB,KAAAsD,OAAO,GAAG,IAAI;IACd,KAAAC,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAApD,WAAW,GAAG,CAAC;IACf,KAAAqD,QAAQ,GAAG,EAAE;IACb,KAAAtD,UAAU,GAAG,CAAC;IACd,KAAAE,UAAU,GAAG,CAAC;IACd,KAAAqD,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAIrE,SAAS,CAAC;MACzBsE,WAAW,EAAE,IAAIvE,WAAW,CAAC,EAAE,CAAC;MAChC2C,QAAQ,EAAE,IAAI3C,WAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAAwE,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,aAAa,GAAiB,IAAI9E,YAAY,EAAE;IAWtD,IAAI,CAAC+E,WAAW,GAAG,IAAI,CAACd,YAAY,CAACe,SAAS;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,aAAa,GAAG,IAAI,CAACL,WAAW,CAACoB,gBAAgB,EAAE;IACxD,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACb,UAAU,CAC9Bc,GAAG,CAAC,aAAa,CAAE,CACnBC,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACL,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACP,aAAa,CAACa,GAAG,CAACL,SAAS,CAAC;IAEjC;IACA,MAAMM,SAAS,GAAG,IAAI,CAACnB,UAAU,CAC9Bc,GAAG,CAAC,UAAU,CAAE,CAChBC,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACL,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACP,aAAa,CAACa,GAAG,CAACC,SAAS,CAAC;EACnC;EAEQR,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACT,kBAAkB,EAAE;MAC3B,IAAI,CAACkB,uBAAuB,GAAG5F,QAAQ,CACrC,IAAI,CAAC2E,mBAAmB,CACzB,CAACa,SAAS,CAAC,MAAK;QACf;QACA,IAAI,CAAC,IAAI,CAACvB,OAAO,IAAI,CAAC,IAAI,CAACO,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,EAAE;UAC/D,IAAI,CAAC9B,MAAM,CAAC+B,KAAK,CAAC,2BAA2B,CAAC;UAC9C,IAAI,CAACV,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAW,iBAAiBA,CAAA;IACf,IAAI,CAACrB,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACS,gBAAgB,EAAE;MACvB,IAAI,CAACpB,MAAM,CAACiC,IAAI,CAAC,sBAAsB,CAAC;KACzC,MAAM,IAAI,IAAI,CAACJ,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAACK,WAAW,EAAE;MAC1C,IAAI,CAACL,uBAAuB,GAAGM,SAAS;MACxC,IAAI,CAACnC,MAAM,CAACiC,IAAI,CAAC,uBAAuB,CAAC;;EAE7C;EAEAP,eAAeA,CAAA;IACb,IAAI,CAAC3E,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAI2D,WAAWA,CAAA;IACb,OAAO,IAAI,CAACD,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAIpB,WAAWA,CAACoB,KAAa;IAC3B,IAAI,CAACrB,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEa,QAAQ,CAACN,KAAK,CAAC;EACrD;EAEA;EACAO,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAjB,SAASA,CAACkB,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAAC1B,WAAW,EAAE;IAEtB,IAAI,CAACX,OAAO,GAAG,IAAI;IAEnB,MAAMQ,WAAW,GAAG,IAAI,CAACD,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,IAAI,EAAE;IACnE,MAAMhD,QAAQ,GAAG,IAAI,CAAC2B,UAAU,CAACc,GAAG,CAAC,UAAU,CAAC,EAAEO,KAAK;IAEvD,IAAI,CAAC9B,MAAM,CAACiC,IAAI,CAAC,eAAe,EAAE;MAChCvB,WAAW,EAAEA,WAAW,IAAI,SAAS;MACrCP,aAAa,EAAE,IAAI,CAACA,aAAa,IAAI,iBAAiB;MACtDqC,IAAI,EAAE,IAAI,CAACzF,WAAW;MACtB0F,KAAK,EAAE,IAAI,CAACrC,QAAQ;MACpBG,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB1B,QAAQ,EAAEA;KACX,CAAC;IAEF,IAAI,CAAC,IAAI,CAACqB,aAAa,EAAE;MACvB,IAAI,CAACH,MAAM,CAAC0C,IAAI,CAAC,uCAAuC,CAAC;;IAG3D,MAAMC,GAAG,GAAG,IAAI,CAAChD,cAAc,CAACiD,WAAW,CACzCL,YAAY,EACZ7B,WAAW,EACX,IAAI,CAAC3D,WAAW,EAChB,IAAI,CAACqD,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACd1B,QAAQ,KAAK,IAAI,CAClB,CAAC2C,SAAS,CAAC;MACVoB,IAAI,EAAGjG,KAAK,IAAI;QACd,IAAI,CAACoD,MAAM,CAAC+B,KAAK,CAAC,YAAYnF,KAAK,CAACC,MAAM,qBAAqB,CAAC;QAEhE,IAAI,CAACiG,KAAK,CAACC,OAAO,CAACnG,KAAK,CAAC,EAAE;UACzB,IAAI,CAACoD,MAAM,CAACgD,KAAK,CAAC,4CAA4C,CAAC;UAC/D,IAAI,CAACpG,KAAK,GAAG,EAAE;UACf,IAAI,CAACsD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACW,WAAW,GAAG,KAAK;UACxB,IAAI,CAACd,YAAY,CAACkD,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAAClG,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAACH,KAAK,GAAGA,KAAK,CAACsG,MAAM,CAAEC,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMC,MAAM,GAAGD,IAAI,CAACvF,EAAE,IAAIuF,IAAI,CAACtF,GAAG;YAClC,OAAOuF,MAAM,KAAK,IAAI,CAACjD,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMkD,QAAQ,GAAGzG,KAAK,CAACsG,MAAM,CAAEI,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAMF,MAAM,GAAGE,OAAO,CAAC1F,EAAE,IAAI0F,OAAO,CAACzF,GAAG;YACxC,OACEuF,MAAM,KAAK,IAAI,CAACjD,aAAa,IAC7B,CAAC,IAAI,CAACvD,KAAK,CAAC2G,IAAI,CACbC,YAAY,IACX,CAACA,YAAY,CAAC5F,EAAE,IAAI4F,YAAY,CAAC3F,GAAG,MAAMuF,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAACxG,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGyG,QAAQ,CAAC;;QAG3C;QACA,MAAMI,UAAU,GAAG,IAAI,CAAC9D,cAAc,CAAC+D,qBAAqB;QAC5D,IAAI,CAAC5G,UAAU,GAAG2G,UAAU,CAACE,UAAU;QACvC,IAAI,CAAC3G,UAAU,GAAGyG,UAAU,CAACzG,UAAU;QACvC,IAAI,CAACqD,WAAW,GAAGoD,UAAU,CAACpD,WAAW;QACzC,IAAI,CAACC,eAAe,GAAGmD,UAAU,CAACnD,eAAe;QAEjD,IAAI,CAACJ,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;QAExB,IAAI,CAACb,MAAM,CAACiC,IAAI,CAAC,2BAA2B,EAAE;UAC5CnF,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3B8G,aAAa,EAAE,IAAI,CAAChH,KAAK,CAACC,MAAM;UAChCE,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BC,UAAU,EAAE,IAAI,CAACA;SAClB,CAAC;MACJ,CAAC;MACDgG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;QACxB,IAAI,CAACb,MAAM,CAACgD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACjD,YAAY,CAACkD,SAAS,CACzB,yBAAyBD,KAAK,CAACa,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED;QACA,IAAI,IAAI,CAAC9G,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAACH,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACDkH,QAAQ,EAAEA,CAAA,KAAK;QACb;QACA,IAAI,CAAC5D,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAACC,aAAa,CAACa,GAAG,CAACgB,GAAG,CAAC;EAC7B;EAEArE,iBAAiBA,CAAC8E,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAACpD,MAAM,CAACgD,KAAK,CAAC,gDAAgD,CAAC;MACnE,IAAI,CAACjD,YAAY,CAACkD,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAACjD,MAAM,CAACiC,IAAI,CAAC,iCAAiC,EAAE;MAAEmB;IAAM,CAAE,CAAC;IAE/D;IACA,IAAI,CAACrD,YAAY,CAACgE,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAACpE,cAAc,CAACqE,kBAAkB,CAACZ,MAAM,CAAC,CAAC3B,SAAS,CAAC;MACvDoB,IAAI,EAAGoB,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACrG,EAAE,EAAE;UACrC,IAAI,CAACoC,MAAM,CAACgD,KAAK,CAAC,sCAAsC,CAAC;UACzD,IAAI,CAACjD,YAAY,CAACkD,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAACjD,MAAM,CAACiC,IAAI,CAAC,mCAAmC,EAAE;UACpDiC,cAAc,EAAED,YAAY,CAACrG,EAAE;UAC/BuG,gBAAgB,EAAEF,YAAY,CAACG,YAAY,EAAEvH,MAAM,IAAI;SACxD,CAAC;QAEF;QACA,IAAI,CAAC+C,MAAM,CACRyE,QAAQ,CAAC,CAAC,8BAA8B,EAAEJ,YAAY,CAACrG,EAAE,CAAC,CAAC,CAC3D0G,IAAI,CAAEC,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAACvE,MAAM,CAACgD,KAAK,CACf,sCAAsCiB,YAAY,CAACrG,EAAE,EAAE,CACxD;YACD,IAAI,CAACmC,YAAY,CAACkD,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACjD,YAAY,CAACkD,SAAS,CACzB,kCAAkCD,KAAK,CAACa,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEA;EACAlG,cAAcA,CAACyF,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACpD,MAAM,CAAC+B,KAAK,CAAC,+BAA+B,EAAE;MAAEqB;IAAM,CAAE,CAAC;IAE9D,IAAI,CAACzD,cAAc,CAAC6E,YAAY,CAACpB,MAAM,EAAElH,QAAQ,CAACuI,KAAK,CAAC,CAAChD,SAAS,CAAC;MACjEoB,IAAI,EAAG6B,IAAI,IAAI;QACb,IAAI,CAAC1E,MAAM,CAAC+B,KAAK,CAAC,mCAAmC,EAAE;UACrD4C,MAAM,EAAED,IAAI,CAAC9G;SACd,CAAC;QACF,IAAI,CAACmC,YAAY,CAAC6E,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACD5B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACjD,YAAY,CAACkD,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA;EACAhF,cAAcA,CAACmF,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACpD,MAAM,CAAC+B,KAAK,CAAC,+BAA+B,EAAE;MAAEqB;IAAM,CAAE,CAAC;IAE9D,IAAI,CAACzD,cAAc,CAAC6E,YAAY,CAACpB,MAAM,EAAElH,QAAQ,CAAC2I,KAAK,CAAC,CAACpD,SAAS,CAAC;MACjEoB,IAAI,EAAG6B,IAAI,IAAI;QACb,IAAI,CAAC1E,MAAM,CAAC+B,KAAK,CAAC,mCAAmC,EAAE;UACrD4C,MAAM,EAAED,IAAI,CAAC9G;SACd,CAAC;QACF,IAAI,CAACmC,YAAY,CAAC6E,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACD5B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,MAAM,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACjD,YAAY,CAACkD,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAzD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACa,WAAW,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACrC,IAAI,CAACW,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC9D,WAAW,EAAE;MAClB,IAAI,CAACsE,SAAS,EAAE;;EAEpB;EAEAyD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACxE,eAAe,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;MACzC,IAAI,CAACW,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC9D,WAAW,EAAE;MAClB,IAAI,CAACsE,SAAS,EAAE;;EAEpB;EAEA0D,YAAYA,CAAA;IACV,IAAI,CAAC/E,MAAM,CAACiC,IAAI,CAAC,+BAA+B,CAAC;IACjD,IAAI,CAACP,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA2D,YAAYA,CAAA;IACV,IAAI,CAACvE,UAAU,CAACwE,KAAK,CAAC;MACpBvE,WAAW,EAAE,EAAE;MACf5B,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAAC4C,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA6D,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAAC5E,MAAM,KAAK4E,KAAK,EAAE;MACzB;MACA,IAAI,CAAC3E,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAG4E,KAAK;MACnB,IAAI,CAAC3E,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACkB,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGA+D,qBAAqBA,CAAA;IACnB,IAAI,CAACpF,MAAM,CAACiC,IAAI,CAAC,UAAU,EAAE,uCAAuC,CAAC;IACrE,IAAI,CAACrC,MAAM,CAACyE,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACvE,aAAa,CAACoB,WAAW,EAAE;IAChC,IAAI,IAAI,CAACL,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACK,WAAW,EAAE;;EAE9C;;;uBArXWzC,iBAAiB,EAAApD,EAAA,CAAAiJ,iBAAA,CAAAC,EAAA,CAAA5F,cAAA,GAAAtD,EAAA,CAAAiJ,iBAAA,CAAAE,EAAA,CAAAC,MAAA,GAAApJ,EAAA,CAAAiJ,iBAAA,CAAAE,EAAA,CAAAE,cAAA,GAAArJ,EAAA,CAAAiJ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAvJ,EAAA,CAAAiJ,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAzJ,EAAA,CAAAiJ,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAA3J,EAAA,CAAAiJ,iBAAA,CAAAW,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAjBzG,iBAAiB;MAAA0G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB9BpK,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAC,cAAA,aAAkE;UAEhED,EAAA,CAAAY,SAAA,aAEO;UAcPZ,EAAA,CAAAC,cAAA,aAAuD;UAEnDD,EAAA,CAAAY,SAAA,aAA6C;UAW/CZ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAyE;UACvED,EAAA,CAAAY,SAAA,eAAqE;UACvEZ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAqC;UAEJD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAa,UAAA,mBAAAyJ,oDAAA;YAAA,OAASD,GAAA,CAAA3B,YAAA,EAAc;UAAA,EAAC;UAIxB1I,EAAA,CAAAY,SAAA,aAA+B;UACjCZ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAa,UAAA,mBAAA0J,oDAAA;YAAA,OAASF,GAAA,CAAAtB,qBAAA,EAAuB;UAAA,EAAC;UAGjC/I,EAAA,CAAAY,SAAA,aAAiC;UACnCZ,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAuB;UAKjBD,EAAA,CAAAa,UAAA,2BAAA2J,2DAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAhG,WAAA,GAAAoG,MAAA;UAAA,EAAsC;UAFxCzK,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA+C;UAUrCD,EAAA,CAAAa,UAAA,oBAAA6J,oDAAAD,MAAA;YAAA,IAAAE,OAAA;YAAA,QAAAA,OAAA,GAETN,GAAA,CAAAjG,UAAA,CAAAc,GAAA,CACF,UAAU,CAAC,mBADTyF,OAAA,CAAA5E,QAAA,CAAA0E,MAAA,CAAAG,MAAA,CAAAC,OAAA,GAEJ,IAAI,GAAG,IAAI,CAAC;UAAA,EAAE;UATH7K,EAAA,CAAAG,YAAA,EAUE;UACFH,EAAA,CAAAY,SAAA,gBAAmD;UACrDZ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,iBACG;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EACrB;UAIHH,EAAA,CAAAC,cAAA,eAAyC;UACRD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAa,UAAA,oBAAAiK,qDAAAL,MAAA;YAAA,OAAUJ,GAAA,CAAAxB,eAAA,CAAA4B,MAAA,CAAAG,MAAA,CAAAnF,KAAA,CAA0C;UAAA,EAAC;UAGrDzF,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAAE,MAAA,aACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgE;UAC9DD,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,kBASC;UARCD,EAAA,CAAAa,UAAA,mBAAAkK,oDAAA;YAAAV,GAAA,CAAAlG,SAAA,GAAAkG,GAAA,CAAAlG,SAAA,KAC6C,KAAK,GAChE,MAAM,GAAG,KAAK;YAAA,OAAkBkG,GAAA,CAAArF,SAAA,CACvB,IAAI,CAAC;UAAA,EAAC;UAMDhF,EAAA,CAAAY,SAAA,SAIK;UACPZ,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,kBAAiE;UAAzDD,EAAA,CAAAa,UAAA,mBAAAmK,oDAAA;YAAA,OAASX,GAAA,CAAA1B,YAAA,EAAc;UAAA,EAAC;UAC9B3I,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAkC,UAAA,KAAA+I,iCAAA,kBASM;UACRjL,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAMC;UAJCD,EAAA,CAAAa,UAAA,oBAAAqK,kDAAAT,MAAA;YAAA,OAAAA,MAAA,CAAAG,MAAA,CAAAO,SAAA,GAAAV,MAAA,CAAAG,MAAA,CAAAQ,YAAA,IAAAX,MAAA,CAAAG,MAAA,CAAAS,YAAA,GAE6C,GAAG,IAAIhB,GAAA,CAAAlH,YAAA,EACpD;UAAA,EAAC;UAGDnD,EAAA,CAAAkC,UAAA,KAAAoJ,iCAAA,kBAGM;UAGNtL,EAAA,CAAAkC,UAAA,KAAAqJ,iCAAA,kBAQM;UAGNvL,EAAA,CAAAkC,UAAA,KAAAsJ,gCAAA,iBA4CK;UAGLxL,EAAA,CAAAkC,UAAA,KAAAuJ,iCAAA,kBASM;UAGNzL,EAAA,CAAAkC,UAAA,KAAAwJ,iCAAA,kBAKM;UACR1L,EAAA,CAAAG,YAAA,EAAM;;;;UA1PNH,EAAA,CAAA2L,WAAA,SAAA3L,EAAA,CAAA4L,WAAA,QAAAvB,GAAA,CAAA3F,WAAA,EAAkC;UAoE1B1E,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAsC,UAAA,YAAA+H,GAAA,CAAAhG,WAAA,CAAuB;UAqBjBrE,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAsC,UAAA,cAAAuJ,OAAA,GAAAxB,GAAA,CAAAjG,UAAA,CAAAc,GAAA,+BAAA2G,OAAA,CAAApG,KAAA,WAAsD;UAqBhDzF,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAsC,UAAA,aAAA+H,GAAA,CAAAnG,MAAA,gBAAkC;UAGlClE,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAsC,UAAA,aAAA+H,GAAA,CAAAnG,MAAA,aAA+B;UAG/BlE,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAsC,UAAA,aAAA+H,GAAA,CAAAnG,MAAA,kBAAoC;UAU5ClE,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAsC,UAAA,UAAA+H,GAAA,CAAAlG,SAAA,0DAEC;UAGCnE,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAA8L,UAAA,CAAAzB,GAAA,CAAAlG,SAAA,mDAEC;UAcRnE,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAsC,UAAA,SAAA+H,GAAA,CAAA5J,UAAA,KAAoB;UAqBnBT,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAsC,UAAA,SAAA+H,GAAA,CAAAxG,OAAA,KAAAwG,GAAA,CAAA9J,KAAA,CAAAC,MAAA,CAA8B;UAM9BR,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAsC,UAAA,UAAA+H,GAAA,CAAAxG,OAAA,IAAAwG,GAAA,CAAA9J,KAAA,CAAAC,MAAA,OAAoC;UAWrCR,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAsC,UAAA,SAAA+H,GAAA,CAAA9J,KAAA,CAAAC,MAAA,KAAsB;UA+CrBR,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAsC,UAAA,SAAA+H,GAAA,CAAAxG,OAAA,IAAAwG,GAAA,CAAA9J,KAAA,CAAAC,MAAA,KAAiC;UAYjCR,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAsC,UAAA,SAAA+H,GAAA,CAAArG,WAAA,KAAAqG,GAAA,CAAAxG,OAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}