{"ast": null, "code": "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n  return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n  return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n  return isExecutionPatchIncrementalResult(value) || isExecutionPatchInitialResult(value);\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n  return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n  var mergedData = prevResult;\n  var merger = new DeepMerger();\n  if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n    result.incremental.forEach(function (_a) {\n      var data = _a.data,\n        path = _a.path;\n      for (var i = path.length - 1; i >= 0; --i) {\n        var key = path[i];\n        var isNumericKey = !isNaN(+key);\n        var parent_1 = isNumericKey ? [] : {};\n        parent_1[key] = data;\n        data = parent_1;\n      }\n      mergedData = merger.merge(mergedData, data);\n    });\n  }\n  return mergedData;\n}", "map": {"version": 3, "names": ["isNonNullObject", "isNonEmptyArray", "DeepMerger", "isExecutionPatchIncrementalResult", "value", "isExecutionPatchInitialResult", "isExecutionPatchResult", "isApolloPayloadResult", "mergeIncrementalData", "prevResult", "result", "mergedData", "merger", "incremental", "for<PERSON>ach", "_a", "data", "path", "i", "length", "key", "isNumericKey", "isNaN", "parent_1", "merge"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/utilities/common/incrementalResult.js"], "sourcesContent": ["import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n    return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n    return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n    return (isExecutionPatchIncrementalResult(value) ||\n        isExecutionPatchInitialResult(value));\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n    return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n    var mergedData = prevResult;\n    var merger = new DeepMerger();\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (_a) {\n            var data = _a.data, path = _a.path;\n            for (var i = path.length - 1; i >= 0; --i) {\n                var key = path[i];\n                var isNumericKey = !isNaN(+key);\n                var parent_1 = isNumericKey ? [] : {};\n                parent_1[key] = data;\n                data = parent_1;\n            }\n            mergedData = merger.merge(mergedData, data);\n        });\n    }\n    return mergedData;\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,cAAc;AAC9C,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,SAASC,iCAAiCA,CAACC,KAAK,EAAE;EACrD,OAAO,aAAa,IAAIA,KAAK;AACjC;AACA,OAAO,SAASC,6BAA6BA,CAACD,KAAK,EAAE;EACjD,OAAO,SAAS,IAAIA,KAAK,IAAI,MAAM,IAAIA,KAAK;AAChD;AACA,OAAO,SAASE,sBAAsBA,CAACF,KAAK,EAAE;EAC1C,OAAQD,iCAAiC,CAACC,KAAK,CAAC,IAC5CC,6BAA6B,CAACD,KAAK,CAAC;AAC5C;AACA;AACA;AACA;AACA,OAAO,SAASG,qBAAqBA,CAACH,KAAK,EAAE;EACzC,OAAOJ,eAAe,CAACI,KAAK,CAAC,IAAI,SAAS,IAAIA,KAAK;AACvD;AACA,OAAO,SAASI,oBAAoBA,CAACC,UAAU,EAAEC,MAAM,EAAE;EACrD,IAAIC,UAAU,GAAGF,UAAU;EAC3B,IAAIG,MAAM,GAAG,IAAIV,UAAU,CAAC,CAAC;EAC7B,IAAIC,iCAAiC,CAACO,MAAM,CAAC,IACzCT,eAAe,CAACS,MAAM,CAACG,WAAW,CAAC,EAAE;IACrCH,MAAM,CAACG,WAAW,CAACC,OAAO,CAAC,UAAUC,EAAE,EAAE;MACrC,IAAIC,IAAI,GAAGD,EAAE,CAACC,IAAI;QAAEC,IAAI,GAAGF,EAAE,CAACE,IAAI;MAClC,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACvC,IAAIE,GAAG,GAAGH,IAAI,CAACC,CAAC,CAAC;QACjB,IAAIG,YAAY,GAAG,CAACC,KAAK,CAAC,CAACF,GAAG,CAAC;QAC/B,IAAIG,QAAQ,GAAGF,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC;QACrCE,QAAQ,CAACH,GAAG,CAAC,GAAGJ,IAAI;QACpBA,IAAI,GAAGO,QAAQ;MACnB;MACAZ,UAAU,GAAGC,MAAM,CAACY,KAAK,CAACb,UAAU,EAAEK,IAAI,CAAC;IAC/C,CAAC,CAAC;EACN;EACA,OAAOL,UAAU;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}