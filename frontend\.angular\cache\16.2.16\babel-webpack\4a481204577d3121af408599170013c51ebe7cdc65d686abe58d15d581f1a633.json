{"ast": null, "code": "import { fromEvent, merge, of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/logger.service\";\nimport * as i2 from \"@angular/common\";\nfunction ConnectionStatusComponent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    online: a0,\n    offline: a1\n  };\n};\nfunction ConnectionStatusComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"i\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ConnectionStatusComponent_div_0_div_6_Template, 1, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r0.isOnline, !ctx_r0.isOnline));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isOnline ? \"fa-wifi\" : \"fa-exclamation-triangle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isOnline ? \"Connexion r\\u00E9tablie\" : \"Connexion perdue\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isOnline);\n  }\n}\nexport class ConnectionStatusComponent {\n  constructor(logger) {\n    this.logger = logger;\n    this.isOnline = navigator.onLine;\n    this.showStatus = false;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Créer un observable qui combine les événements online et offline\n    const online$ = fromEvent(window, 'online').pipe(map(() => true));\n    const offline$ = fromEvent(window, 'offline').pipe(map(() => false));\n    const initialStatus$ = of(navigator.onLine);\n    // S'abonner aux changements d'état de connexion\n    const connectionSub = merge(initialStatus$, online$, offline$).subscribe(isOnline => {\n      this.isOnline = isOnline;\n      this.showStatus = true;\n      this.logger.debug(`Connection status changed: ${isOnline ? 'online' : 'offline'}`);\n      // Masquer le statut après 5 secondes\n      setTimeout(() => {\n        this.showStatus = false;\n      }, 5000);\n    });\n    this.subscriptions.push(connectionSub);\n  }\n  ngOnDestroy() {\n    // Nettoyer les abonnements\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  static {\n    this.ɵfac = function ConnectionStatusComponent_Factory(t) {\n      return new (t || ConnectionStatusComponent)(i0.ɵɵdirectiveInject(i1.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConnectionStatusComponent,\n      selectors: [[\"app-connection-status\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"connection-status\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"connection-status\", 3, \"ngClass\"], [1, \"status-content\"], [1, \"status-icon\"], [1, \"fas\", 3, \"ngClass\"], [1, \"status-text\"], [\"class\", \"glow-effect\", 4, \"ngIf\"], [1, \"glow-effect\"]],\n      template: function ConnectionStatusComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ConnectionStatusComponent_div_0_Template, 7, 7, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showStatus);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf],\n      styles: [\".connection-status[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 20px;\\n  border-radius: 30px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out, _ngcontent-%COMP%_fadeOut 0.3s ease-out 4.7s;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  position: relative;\\n}\\n\\n.online[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff8c00, #ff6b00);\\n  color: white;\\n  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);\\n  }\\n  50% {\\n    box-shadow: 0 0 25px rgba(255, 140, 0, 0.8);\\n  }\\n  100% {\\n    box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);\\n  }\\n}\\n\\n.offline[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n\\n.status-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.status-icon[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  font-size: 18px;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n\\n.glow-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -50%;\\n  left: -50%;\\n  width: 200%;\\n  height: 200%;\\n  background: radial-gradient(\\n    circle,\\n    rgba(255, 140, 0, 0.4) 0%,\\n    rgba(255, 107, 0, 0) 70%\\n  );\\n  animation: _ngcontent-%COMP%_rotate 8s linear infinite;\\n  z-index: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    transform: translate(-50%, -100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translate(-50%, 0);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeOut {\\n  from {\\n    opacity: 1;\\n  }\\n  to {\\n    opacity: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["fromEvent", "merge", "of", "map", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "ConnectionStatusComponent_div_0_div_6_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r0", "isOnline", "ɵɵadvance", "ɵɵtextInterpolate1", "ConnectionStatusComponent", "constructor", "logger", "navigator", "onLine", "showStatus", "subscriptions", "ngOnInit", "online$", "window", "pipe", "offline$", "initialStatus$", "connectionSub", "subscribe", "debug", "setTimeout", "push", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "ɵɵdirectiveInject", "i1", "LoggerService", "selectors", "decls", "vars", "consts", "template", "ConnectionStatusComponent_Template", "rf", "ctx", "ConnectionStatusComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\connection-status\\connection-status.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\components\\connection-status\\connection-status.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { Subscription, fromEvent, merge, of } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { LoggerService } from 'src/app/services/logger.service';\r\n\r\n@Component({\r\n  selector: 'app-connection-status',\r\n  templateUrl: './connection-status.component.html',\r\n  styleUrls: ['./connection-status.component.css'],\r\n})\r\nexport class ConnectionStatusComponent implements OnInit, OnDestroy {\r\n  isOnline: boolean = navigator.onLine;\r\n  showStatus: boolean = false;\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(private logger: LoggerService) {}\r\n\r\n  ngOnInit(): void {\r\n    // Créer un observable qui combine les événements online et offline\r\n    const online$ = fromEvent(window, 'online').pipe(map(() => true));\r\n    const offline$ = fromEvent(window, 'offline').pipe(map(() => false));\r\n    const initialStatus$ = of(navigator.onLine);\r\n\r\n    // S'abonner aux changements d'état de connexion\r\n    const connectionSub = merge(initialStatus$, online$, offline$).subscribe(\r\n      (isOnline) => {\r\n        this.isOnline = isOnline;\r\n        this.showStatus = true;\r\n\r\n        this.logger.debug(\r\n          `Connection status changed: ${isOnline ? 'online' : 'offline'}`\r\n        );\r\n\r\n        // Masquer le statut après 5 secondes\r\n        setTimeout(() => {\r\n          this.showStatus = false;\r\n        }, 5000);\r\n      }\r\n    );\r\n\r\n    this.subscriptions.push(connectionSub);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Nettoyer les abonnements\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n  }\r\n}\r\n", "<div\r\n  *ngIf=\"showStatus\"\r\n  class=\"connection-status\"\r\n  [ngClass]=\"{ online: isOnline, offline: !isOnline }\"\r\n>\r\n  <div class=\"status-content\">\r\n    <div class=\"status-icon\">\r\n      <i\r\n        class=\"fas\"\r\n        [ngClass]=\"isOnline ? 'fa-wifi' : 'fa-exclamation-triangle'\"\r\n      ></i>\r\n    </div>\r\n    <div class=\"status-text\">\r\n      {{ isOnline ? \"Connexion rétablie\" : \"Connexion perdue\" }}\r\n    </div>\r\n  </div>\r\n  <div *ngIf=\"isOnline\" class=\"glow-effect\"></div>\r\n</div>\r\n"], "mappings": "AACA,SAAuBA,SAAS,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzD,SAASC,GAAG,QAAQ,gBAAgB;;;;;;ICclCC,EAAA,CAAAC,SAAA,aAAgD;;;;;;;;;;;IAhBlDD,EAAA,CAAAE,cAAA,aAIC;IAGKF,EAAA,CAAAC,SAAA,WAGK;IACPD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,aAAyB;IACvBF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,UAAA,IAAAC,8CAAA,iBAAgD;IAClDN,EAAA,CAAAG,YAAA,EAAM;;;;IAdJH,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,GAAAD,MAAA,CAAAC,QAAA,EAAoD;IAM9CX,EAAA,CAAAY,SAAA,GAA4D;IAA5DZ,EAAA,CAAAO,UAAA,YAAAG,MAAA,CAAAC,QAAA,yCAA4D;IAI9DX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAH,MAAA,CAAAC,QAAA,uDACF;IAEIX,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAO,UAAA,SAAAG,MAAA,CAAAC,QAAA,CAAc;;;ADNtB,OAAM,MAAOG,yBAAyB;EAKpCC,YAAoBC,MAAqB;IAArB,KAAAA,MAAM,GAANA,MAAM;IAJ1B,KAAAL,QAAQ,GAAYM,SAAS,CAACC,MAAM;IACpC,KAAAC,UAAU,GAAY,KAAK;IACnB,KAAAC,aAAa,GAAmB,EAAE;EAEE;EAE5CC,QAAQA,CAAA;IACN;IACA,MAAMC,OAAO,GAAG1B,SAAS,CAAC2B,MAAM,EAAE,QAAQ,CAAC,CAACC,IAAI,CAACzB,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IACjE,MAAM0B,QAAQ,GAAG7B,SAAS,CAAC2B,MAAM,EAAE,SAAS,CAAC,CAACC,IAAI,CAACzB,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IACpE,MAAM2B,cAAc,GAAG5B,EAAE,CAACmB,SAAS,CAACC,MAAM,CAAC;IAE3C;IACA,MAAMS,aAAa,GAAG9B,KAAK,CAAC6B,cAAc,EAAEJ,OAAO,EAAEG,QAAQ,CAAC,CAACG,SAAS,CACrEjB,QAAQ,IAAI;MACX,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACQ,UAAU,GAAG,IAAI;MAEtB,IAAI,CAACH,MAAM,CAACa,KAAK,CACf,8BAA8BlB,QAAQ,GAAG,QAAQ,GAAG,SAAS,EAAE,CAChE;MAED;MACAmB,UAAU,CAAC,MAAK;QACd,IAAI,CAACX,UAAU,GAAG,KAAK;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CACF;IAED,IAAI,CAACC,aAAa,CAACW,IAAI,CAACJ,aAAa,CAAC;EACxC;EAEAK,WAAWA,CAAA;IACT;IACA,IAAI,CAACZ,aAAa,CAACa,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;;;uBApCWrB,yBAAyB,EAAAd,EAAA,CAAAoC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAzBxB,yBAAyB;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVtC7C,EAAA,CAAAK,UAAA,IAAA0C,wCAAA,iBAiBM;;;UAhBH/C,EAAA,CAAAO,UAAA,SAAAuC,GAAA,CAAA3B,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}