{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { PlanningCalendarComponent } from './planning-calendar/planning-calendar.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"angular-calendar\";\nexport class PlanningsModule {\n  static {\n    this.ɵfac = function PlanningsModule_Factory(t) {\n      return new (t || PlanningsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlanningsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule.forRoot({\n        provide: DateAdapter,\n        useFactory: adapterFactory\n      })]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlanningsModule, {\n    declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningCalendarComponent, PlanningEditComponent],\n    imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, i1.CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "PlanningsRoutingModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "PlanningCalendarComponent", "FormsModule", "ReactiveFormsModule", "PlanningEditComponent", "CalendarModule", "DateAdapter", "adapterFactory", "PlanningsModule", "forRoot", "provide", "useFactory", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\plannings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { PlanningsRoutingModule } from './plannings-routing.module';\r\nimport { PlanningListComponent } from './planning-list/planning-list.component';\r\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\r\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\r\nimport { PlanningCalendarComponent } from './planning-calendar/planning-calendar.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\r\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\r\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    PlanningListComponent,\r\n    PlanningDetailComponent,\r\n    PlanningFormComponent,\r\n    PlanningCalendarComponent,\r\n    PlanningEditComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    PlanningsRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CalendarModule.forRoot({\r\n      provide: DateAdapter,\r\n      useFactory: adapterFactory,\r\n    }),\r\n  ],\r\n})\r\nexport class PlanningsModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;;;AAqBxE,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAVxBZ,YAAY,EACZC,sBAAsB,EACtBK,WAAW,EACXC,mBAAmB,EACnBE,cAAc,CAACI,OAAO,CAAC;QACrBC,OAAO,EAAEJ,WAAW;QACpBK,UAAU,EAAEJ;OACb,CAAC;IAAA;EAAA;;;2EAGOC,eAAe;IAAAI,YAAA,GAjBxBd,qBAAqB,EACrBC,uBAAuB,EACvBC,qBAAqB,EACrBC,yBAAyB,EACzBG,qBAAqB;IAAAS,OAAA,GAGrBjB,YAAY,EACZC,sBAAsB,EACtBK,WAAW,EACXC,mBAAmB,EAAAW,EAAA,CAAAT,cAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}