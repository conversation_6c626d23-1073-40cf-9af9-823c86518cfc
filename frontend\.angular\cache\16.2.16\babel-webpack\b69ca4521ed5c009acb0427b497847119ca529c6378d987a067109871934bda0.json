{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ThemeService {\n  constructor() {\n    this.darkMode = new BehaviorSubject(false);\n    this.darkMode$ = this.darkMode.asObservable();\n    // Check if user has a theme preference in localStorage\n    const savedTheme = localStorage.getItem('darkMode');\n    if (savedTheme) {\n      this.darkMode.next(savedTheme === 'true');\n      this.applyTheme(savedTheme === 'true');\n    } else {\n      // Check if user prefers dark mode at OS level\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      this.darkMode.next(prefersDark);\n      this.applyTheme(prefersDark);\n    }\n  }\n  toggleDarkMode() {\n    const newValue = !this.darkMode.value;\n    this.darkMode.next(newValue);\n    localStorage.setItem('darkMode', String(newValue));\n    this.applyTheme(newValue);\n  }\n  applyTheme(isDark) {\n    if (isDark) {\n      document.documentElement.classList.add('dark');\n      console.log('Dark mode enabled');\n    } else {\n      document.documentElement.classList.remove('dark');\n      console.log('Dark mode disabled');\n    }\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(t) {\n      return new (t || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "ThemeService", "constructor", "darkMode", "darkMode$", "asObservable", "savedTheme", "localStorage", "getItem", "next", "applyTheme", "prefersDark", "window", "matchMedia", "matches", "toggleDarkMode", "newValue", "value", "setItem", "String", "isDark", "document", "documentElement", "classList", "add", "console", "log", "remove", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ThemeService {\r\n  private darkMode = new BehaviorSubject<boolean>(false);\r\n  darkMode$ = this.darkMode.asObservable();\r\n\r\n  constructor() {\r\n    // Check if user has a theme preference in localStorage\r\n    const savedTheme = localStorage.getItem('darkMode');\r\n    if (savedTheme) {\r\n      this.darkMode.next(savedTheme === 'true');\r\n      this.applyTheme(savedTheme === 'true');\r\n    } else {\r\n      // Check if user prefers dark mode at OS level\r\n      const prefersDark = window.matchMedia(\r\n        '(prefers-color-scheme: dark)'\r\n      ).matches;\r\n      this.darkMode.next(prefersDark);\r\n      this.applyTheme(prefersDark);\r\n    }\r\n  }\r\n\r\n  toggleDarkMode(): void {\r\n    const newValue = !this.darkMode.value;\r\n    this.darkMode.next(newValue);\r\n    localStorage.setItem('darkMode', String(newValue));\r\n    this.applyTheme(newValue);\r\n  }\r\n\r\n  private applyTheme(isDark: boolean): void {\r\n    if (isDark) {\r\n      document.documentElement.classList.add('dark');\r\n      console.log('Dark mode enabled');\r\n    } else {\r\n      document.documentElement.classList.remove('dark');\r\n      console.log('Dark mode disabled');\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAKtC,OAAM,MAAOC,YAAY;EAIvBC,YAAA;IAHQ,KAAAC,QAAQ,GAAG,IAAIH,eAAe,CAAU,KAAK,CAAC;IACtD,KAAAI,SAAS,GAAG,IAAI,CAACD,QAAQ,CAACE,YAAY,EAAE;IAGtC;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACnD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACH,QAAQ,CAACM,IAAI,CAACH,UAAU,KAAK,MAAM,CAAC;MACzC,IAAI,CAACI,UAAU,CAACJ,UAAU,KAAK,MAAM,CAAC;KACvC,MAAM;MACL;MACA,MAAMK,WAAW,GAAGC,MAAM,CAACC,UAAU,CACnC,8BAA8B,CAC/B,CAACC,OAAO;MACT,IAAI,CAACX,QAAQ,CAACM,IAAI,CAACE,WAAW,CAAC;MAC/B,IAAI,CAACD,UAAU,CAACC,WAAW,CAAC;;EAEhC;EAEAI,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACb,QAAQ,CAACc,KAAK;IACrC,IAAI,CAACd,QAAQ,CAACM,IAAI,CAACO,QAAQ,CAAC;IAC5BT,YAAY,CAACW,OAAO,CAAC,UAAU,EAAEC,MAAM,CAACH,QAAQ,CAAC,CAAC;IAClD,IAAI,CAACN,UAAU,CAACM,QAAQ,CAAC;EAC3B;EAEQN,UAAUA,CAACU,MAAe;IAChC,IAAIA,MAAM,EAAE;MACVC,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MAC9CC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;KACjC,MAAM;MACLL,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACI,MAAM,CAAC,MAAM,CAAC;MACjDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;EAErC;;;uBAnCWzB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA2B,OAAA,EAAZ3B,YAAY,CAAA4B,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}