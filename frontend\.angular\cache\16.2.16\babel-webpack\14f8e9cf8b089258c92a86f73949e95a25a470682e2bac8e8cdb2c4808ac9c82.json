{"ast": null, "code": "import \"../utilities/globals/index.js\";\nexport { ApolloCache } from \"./core/cache.js\";\nexport { Cache } from \"./core/types/Cache.js\";\nexport { MissingFieldError } from \"./core/types/common.js\";\nexport { isReference, makeReference, canonicalStringify } from \"../utilities/index.js\";\nexport { EntityStore } from \"./inmemory/entityStore.js\";\nexport { fieldNameFromStoreName, defaultDataIdFromObject } from \"./inmemory/helpers.js\";\nexport { InMemoryCache } from \"./inmemory/inMemoryCache.js\";\nexport { makeVar, cacheSlot } from \"./inmemory/reactiveVars.js\";\nexport { Policies } from \"./inmemory/policies.js\";\nexport { createFragmentRegistry } from \"./inmemory/fragmentRegistry.js\";", "map": {"version": 3, "names": ["Apollo<PERSON>ache", "<PERSON><PERSON>", "Missing<PERSON>ieldE<PERSON>r", "isReference", "makeReference", "canonicalStringify", "EntityStore", "fieldNameFromStoreName", "defaultDataIdFromObject", "InMemoryCache", "makeVar", "cacheSlot", "Policies", "createFragmentRegistry"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/index.js"], "sourcesContent": ["import \"../utilities/globals/index.js\";\nexport { ApolloCache } from \"./core/cache.js\";\nexport { Cache } from \"./core/types/Cache.js\";\nexport { MissingFieldError } from \"./core/types/common.js\";\nexport { isReference, makeReference, canonicalStringify, } from \"../utilities/index.js\";\nexport { EntityStore } from \"./inmemory/entityStore.js\";\nexport { fieldNameFromStoreName, defaultDataIdFromObject, } from \"./inmemory/helpers.js\";\nexport { InMemoryCache } from \"./inmemory/inMemoryCache.js\";\nexport { makeVar, cacheSlot } from \"./inmemory/reactiveVars.js\";\nexport { Policies } from \"./inmemory/policies.js\";\nexport { createFragmentRegistry } from \"./inmemory/fragmentRegistry.js\";\n"], "mappings": "AAAA,OAAO,+BAA+B;AACtC,SAASA,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,WAAW,EAAEC,aAAa,EAAEC,kBAAkB,QAAS,uBAAuB;AACvF,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAS,uBAAuB;AACxF,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,OAAO,EAAEC,SAAS,QAAQ,4BAA4B;AAC/D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,sBAAsB,QAAQ,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}