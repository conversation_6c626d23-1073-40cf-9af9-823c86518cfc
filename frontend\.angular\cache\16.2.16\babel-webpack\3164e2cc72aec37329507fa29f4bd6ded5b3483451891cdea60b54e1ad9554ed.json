{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { wrap } from \"optimism\";\nimport { Observable, cacheSizes, getFragmentDefinition, getFragmentQueryDocument, mergeDeepArray } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { getApolloCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nimport { equalByQuery } from \"../../core/equalByQuery.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { maskFragment } from \"../../masking/index.js\";\nvar ApolloCache = /** @class */function () {\n  function ApolloCache() {\n    this.assumeImmutableResults = false;\n    // Make sure we compute the same (===) fragment query document every\n    // time we receive the same fragment in readFragment.\n    this.getFragmentDoc = wrap(getFragmentQueryDocument, {\n      max: cacheSizes[\"cache.fragmentQueryDocuments\"] || 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n      cache: WeakCache\n    });\n  }\n  // Function used to lookup a fragment when a fragment definition is not part\n  // of the GraphQL document. This is useful for caches, such as InMemoryCache,\n  // that register fragments ahead of time so they can be referenced by name.\n  ApolloCache.prototype.lookupFragment = function (fragmentName) {\n    return null;\n  };\n  // Transactional API\n  // The batch method is intended to replace/subsume both performTransaction\n  // and recordOptimisticTransaction, but performTransaction came first, so we\n  // provide a default batch implementation that's just another way of calling\n  // performTransaction. Subclasses of ApolloCache (such as InMemoryCache) can\n  // override the batch method to do more interesting things with its options.\n  ApolloCache.prototype.batch = function (options) {\n    var _this = this;\n    var optimisticId = typeof options.optimistic === \"string\" ? options.optimistic : options.optimistic === false ? null : void 0;\n    var updateResult;\n    this.performTransaction(function () {\n      return updateResult = options.update(_this);\n    }, optimisticId);\n    return updateResult;\n  };\n  ApolloCache.prototype.recordOptimisticTransaction = function (transaction, optimisticId) {\n    this.performTransaction(transaction, optimisticId);\n  };\n  // Optional API\n  // Called once per input document, allowing the cache to make static changes\n  // to the query, such as adding __typename fields.\n  ApolloCache.prototype.transformDocument = function (document) {\n    return document;\n  };\n  // Called before each ApolloLink request, allowing the cache to make dynamic\n  // changes to the query, such as filling in missing fragment definitions.\n  ApolloCache.prototype.transformForLink = function (document) {\n    return document;\n  };\n  ApolloCache.prototype.identify = function (object) {\n    return;\n  };\n  ApolloCache.prototype.gc = function () {\n    return [];\n  };\n  ApolloCache.prototype.modify = function (options) {\n    return false;\n  };\n  // DataProxy API\n  ApolloCache.prototype.readQuery = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = !!options.optimistic;\n    }\n    return this.read(__assign(__assign({}, options), {\n      rootId: options.id || \"ROOT_QUERY\",\n      optimistic: optimistic\n    }));\n  };\n  /** {@inheritDoc @apollo/client!ApolloClient#watchFragment:member(1)} */\n  ApolloCache.prototype.watchFragment = function (options) {\n    var _this = this;\n    var fragment = options.fragment,\n      fragmentName = options.fragmentName,\n      from = options.from,\n      _a = options.optimistic,\n      optimistic = _a === void 0 ? true : _a,\n      otherOptions = __rest(options, [\"fragment\", \"fragmentName\", \"from\", \"optimistic\"]);\n    var query = this.getFragmentDoc(fragment, fragmentName);\n    // While our TypeScript types do not allow for `undefined` as a valid\n    // `from`, its possible `useFragment` gives us an `undefined` since it\n    // calls` cache.identify` and provides that value to `from`. We are\n    // adding this fix here however to ensure those using plain JavaScript\n    // and using `cache.identify` themselves will avoid seeing the obscure\n    // warning.\n    var id = typeof from === \"undefined\" || typeof from === \"string\" ? from : this.identify(from);\n    var dataMasking = !!options[Symbol.for(\"apollo.dataMasking\")];\n    if (globalThis.__DEV__ !== false) {\n      var actualFragmentName = fragmentName || getFragmentDefinition(fragment).name.value;\n      if (!id) {\n        globalThis.__DEV__ !== false && invariant.warn(1, actualFragmentName);\n      }\n    }\n    var diffOptions = __assign(__assign({}, otherOptions), {\n      returnPartialData: true,\n      id: id,\n      query: query,\n      optimistic: optimistic\n    });\n    var latestDiff;\n    return new Observable(function (observer) {\n      return _this.watch(__assign(__assign({}, diffOptions), {\n        immediate: true,\n        callback: function (diff) {\n          var data = dataMasking ? maskFragment(diff.result, fragment, _this, fragmentName) : diff.result;\n          if (\n          // Always ensure we deliver the first result\n          latestDiff && equalByQuery(query, {\n            data: latestDiff.result\n          }, {\n            data: data\n          },\n          // TODO: Fix the type on WatchFragmentOptions so that TVars\n          // extends OperationVariables\n          options.variables)) {\n            return;\n          }\n          var result = {\n            data: data,\n            complete: !!diff.complete\n          };\n          if (diff.missing) {\n            result.missing = mergeDeepArray(diff.missing.map(function (error) {\n              return error.missing;\n            }));\n          }\n          latestDiff = __assign(__assign({}, diff), {\n            result: data\n          });\n          observer.next(result);\n        }\n      }));\n    });\n  };\n  ApolloCache.prototype.readFragment = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = !!options.optimistic;\n    }\n    return this.read(__assign(__assign({}, options), {\n      query: this.getFragmentDoc(options.fragment, options.fragmentName),\n      rootId: options.id,\n      optimistic: optimistic\n    }));\n  };\n  ApolloCache.prototype.writeQuery = function (_a) {\n    var id = _a.id,\n      data = _a.data,\n      options = __rest(_a, [\"id\", \"data\"]);\n    return this.write(Object.assign(options, {\n      dataId: id || \"ROOT_QUERY\",\n      result: data\n    }));\n  };\n  ApolloCache.prototype.writeFragment = function (_a) {\n    var id = _a.id,\n      data = _a.data,\n      fragment = _a.fragment,\n      fragmentName = _a.fragmentName,\n      options = __rest(_a, [\"id\", \"data\", \"fragment\", \"fragmentName\"]);\n    return this.write(Object.assign(options, {\n      query: this.getFragmentDoc(fragment, fragmentName),\n      dataId: id,\n      result: data\n    }));\n  };\n  ApolloCache.prototype.updateQuery = function (options, update) {\n    return this.batch({\n      update: function (cache) {\n        var value = cache.readQuery(options);\n        var data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeQuery(__assign(__assign({}, options), {\n          data: data\n        }));\n        return data;\n      }\n    });\n  };\n  ApolloCache.prototype.updateFragment = function (options, update) {\n    return this.batch({\n      update: function (cache) {\n        var value = cache.readFragment(options);\n        var data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeFragment(__assign(__assign({}, options), {\n          data: data\n        }));\n        return data;\n      }\n    });\n  };\n  return ApolloCache;\n}();\nexport { ApolloCache };\nif (globalThis.__DEV__ !== false) {\n  ApolloCache.prototype.getMemoryInternals = getApolloCacheMemoryInternals;\n}", "map": {"version": 3, "names": ["__assign", "__rest", "wrap", "Observable", "cacheSizes", "getFragmentDefinition", "getFragmentQueryDocument", "mergeDeepArray", "<PERSON>ak<PERSON><PERSON>", "getApolloCacheMemoryInternals", "equalBy<PERSON>uery", "invariant", "maskFragment", "Apollo<PERSON>ache", "assumeImmutableResults", "getFragmentDoc", "max", "cache", "prototype", "lookupFragment", "fragmentName", "batch", "options", "_this", "optimisticId", "optimistic", "updateResult", "performTransaction", "update", "recordOptimisticTransaction", "transaction", "transformDocument", "document", "transformForLink", "identify", "object", "gc", "modify", "readQuery", "read", "rootId", "id", "watchFragment", "fragment", "from", "_a", "otherOptions", "query", "dataMasking", "Symbol", "for", "globalThis", "__DEV__", "actualFragmentName", "name", "value", "warn", "diffOptions", "returnPartialData", "latestDiff", "observer", "watch", "immediate", "callback", "diff", "data", "result", "variables", "complete", "missing", "map", "error", "next", "readFragment", "writeQuery", "write", "Object", "assign", "dataId", "writeFragment", "updateQuery", "updateFragment", "getMemoryInternals"], "sources": ["C:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@apollo/client/cache/core/cache.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport { wrap } from \"optimism\";\nimport { Observable, cacheSizes, getFragmentDefinition, getFragmentQueryDocument, mergeDeepArray, } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { getApolloCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nimport { equalByQuery } from \"../../core/equalByQuery.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { maskFragment } from \"../../masking/index.js\";\nvar ApolloCache = /** @class */ (function () {\n    function ApolloCache() {\n        this.assumeImmutableResults = false;\n        // Make sure we compute the same (===) fragment query document every\n        // time we receive the same fragment in readFragment.\n        this.getFragmentDoc = wrap(getFragmentQueryDocument, {\n            max: cacheSizes[\"cache.fragmentQueryDocuments\"] ||\n                1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n            cache: WeakCache,\n        });\n    }\n    // Function used to lookup a fragment when a fragment definition is not part\n    // of the GraphQL document. This is useful for caches, such as InMemoryCache,\n    // that register fragments ahead of time so they can be referenced by name.\n    ApolloCache.prototype.lookupFragment = function (fragmentName) {\n        return null;\n    };\n    // Transactional API\n    // The batch method is intended to replace/subsume both performTransaction\n    // and recordOptimisticTransaction, but performTransaction came first, so we\n    // provide a default batch implementation that's just another way of calling\n    // performTransaction. Subclasses of ApolloCache (such as InMemoryCache) can\n    // override the batch method to do more interesting things with its options.\n    ApolloCache.prototype.batch = function (options) {\n        var _this = this;\n        var optimisticId = typeof options.optimistic === \"string\" ? options.optimistic\n            : options.optimistic === false ? null\n                : void 0;\n        var updateResult;\n        this.performTransaction(function () { return (updateResult = options.update(_this)); }, optimisticId);\n        return updateResult;\n    };\n    ApolloCache.prototype.recordOptimisticTransaction = function (transaction, optimisticId) {\n        this.performTransaction(transaction, optimisticId);\n    };\n    // Optional API\n    // Called once per input document, allowing the cache to make static changes\n    // to the query, such as adding __typename fields.\n    ApolloCache.prototype.transformDocument = function (document) {\n        return document;\n    };\n    // Called before each ApolloLink request, allowing the cache to make dynamic\n    // changes to the query, such as filling in missing fragment definitions.\n    ApolloCache.prototype.transformForLink = function (document) {\n        return document;\n    };\n    ApolloCache.prototype.identify = function (object) {\n        return;\n    };\n    ApolloCache.prototype.gc = function () {\n        return [];\n    };\n    ApolloCache.prototype.modify = function (options) {\n        return false;\n    };\n    // DataProxy API\n    ApolloCache.prototype.readQuery = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = !!options.optimistic; }\n        return this.read(__assign(__assign({}, options), { rootId: options.id || \"ROOT_QUERY\", optimistic: optimistic }));\n    };\n    /** {@inheritDoc @apollo/client!ApolloClient#watchFragment:member(1)} */\n    ApolloCache.prototype.watchFragment = function (options) {\n        var _this = this;\n        var fragment = options.fragment, fragmentName = options.fragmentName, from = options.from, _a = options.optimistic, optimistic = _a === void 0 ? true : _a, otherOptions = __rest(options, [\"fragment\", \"fragmentName\", \"from\", \"optimistic\"]);\n        var query = this.getFragmentDoc(fragment, fragmentName);\n        // While our TypeScript types do not allow for `undefined` as a valid\n        // `from`, its possible `useFragment` gives us an `undefined` since it\n        // calls` cache.identify` and provides that value to `from`. We are\n        // adding this fix here however to ensure those using plain JavaScript\n        // and using `cache.identify` themselves will avoid seeing the obscure\n        // warning.\n        var id = typeof from === \"undefined\" || typeof from === \"string\" ?\n            from\n            : this.identify(from);\n        var dataMasking = !!options[Symbol.for(\"apollo.dataMasking\")];\n        if (globalThis.__DEV__ !== false) {\n            var actualFragmentName = fragmentName || getFragmentDefinition(fragment).name.value;\n            if (!id) {\n                globalThis.__DEV__ !== false && invariant.warn(1, actualFragmentName);\n            }\n        }\n        var diffOptions = __assign(__assign({}, otherOptions), { returnPartialData: true, id: id, query: query, optimistic: optimistic });\n        var latestDiff;\n        return new Observable(function (observer) {\n            return _this.watch(__assign(__assign({}, diffOptions), { immediate: true, callback: function (diff) {\n                    var data = dataMasking ?\n                        maskFragment(diff.result, fragment, _this, fragmentName)\n                        : diff.result;\n                    if (\n                    // Always ensure we deliver the first result\n                    latestDiff &&\n                        equalByQuery(query, { data: latestDiff.result }, { data: data }, \n                        // TODO: Fix the type on WatchFragmentOptions so that TVars\n                        // extends OperationVariables\n                        options.variables)) {\n                        return;\n                    }\n                    var result = {\n                        data: data,\n                        complete: !!diff.complete,\n                    };\n                    if (diff.missing) {\n                        result.missing = mergeDeepArray(diff.missing.map(function (error) { return error.missing; }));\n                    }\n                    latestDiff = __assign(__assign({}, diff), { result: data });\n                    observer.next(result);\n                } }));\n        });\n    };\n    ApolloCache.prototype.readFragment = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = !!options.optimistic; }\n        return this.read(__assign(__assign({}, options), { query: this.getFragmentDoc(options.fragment, options.fragmentName), rootId: options.id, optimistic: optimistic }));\n    };\n    ApolloCache.prototype.writeQuery = function (_a) {\n        var id = _a.id, data = _a.data, options = __rest(_a, [\"id\", \"data\"]);\n        return this.write(Object.assign(options, {\n            dataId: id || \"ROOT_QUERY\",\n            result: data,\n        }));\n    };\n    ApolloCache.prototype.writeFragment = function (_a) {\n        var id = _a.id, data = _a.data, fragment = _a.fragment, fragmentName = _a.fragmentName, options = __rest(_a, [\"id\", \"data\", \"fragment\", \"fragmentName\"]);\n        return this.write(Object.assign(options, {\n            query: this.getFragmentDoc(fragment, fragmentName),\n            dataId: id,\n            result: data,\n        }));\n    };\n    ApolloCache.prototype.updateQuery = function (options, update) {\n        return this.batch({\n            update: function (cache) {\n                var value = cache.readQuery(options);\n                var data = update(value);\n                if (data === void 0 || data === null)\n                    return value;\n                cache.writeQuery(__assign(__assign({}, options), { data: data }));\n                return data;\n            },\n        });\n    };\n    ApolloCache.prototype.updateFragment = function (options, update) {\n        return this.batch({\n            update: function (cache) {\n                var value = cache.readFragment(options);\n                var data = update(value);\n                if (data === void 0 || data === null)\n                    return value;\n                cache.writeFragment(__assign(__assign({}, options), { data: data }));\n                return data;\n            },\n        });\n    };\n    return ApolloCache;\n}());\nexport { ApolloCache };\nif (globalThis.__DEV__ !== false) {\n    ApolloCache.prototype.getMemoryInternals = getApolloCacheMemoryInternals;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,UAAU,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,wBAAwB,EAAEC,cAAc,QAAS,0BAA0B;AACnI,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,6BAA6B,QAAQ,+CAA+C;AAC7F,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,YAAY,QAAQ,wBAAwB;AACrD,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG;IACnB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC;IACA;IACA,IAAI,CAACC,cAAc,GAAGb,IAAI,CAACI,wBAAwB,EAAE;MACjDU,GAAG,EAAEZ,UAAU,CAAC,8BAA8B,CAAC,IAC3C,IAAI,CAAC;MACTa,KAAK,EAAET;IACX,CAAC,CAAC;EACN;EACA;EACA;EACA;EACAK,WAAW,CAACK,SAAS,CAACC,cAAc,GAAG,UAAUC,YAAY,EAAE;IAC3D,OAAO,IAAI;EACf,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACAP,WAAW,CAACK,SAAS,CAACG,KAAK,GAAG,UAAUC,OAAO,EAAE;IAC7C,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,OAAOF,OAAO,CAACG,UAAU,KAAK,QAAQ,GAAGH,OAAO,CAACG,UAAU,GACxEH,OAAO,CAACG,UAAU,KAAK,KAAK,GAAG,IAAI,GAC/B,KAAK,CAAC;IAChB,IAAIC,YAAY;IAChB,IAAI,CAACC,kBAAkB,CAAC,YAAY;MAAE,OAAQD,YAAY,GAAGJ,OAAO,CAACM,MAAM,CAACL,KAAK,CAAC;IAAG,CAAC,EAAEC,YAAY,CAAC;IACrG,OAAOE,YAAY;EACvB,CAAC;EACDb,WAAW,CAACK,SAAS,CAACW,2BAA2B,GAAG,UAAUC,WAAW,EAAEN,YAAY,EAAE;IACrF,IAAI,CAACG,kBAAkB,CAACG,WAAW,EAAEN,YAAY,CAAC;EACtD,CAAC;EACD;EACA;EACA;EACAX,WAAW,CAACK,SAAS,CAACa,iBAAiB,GAAG,UAAUC,QAAQ,EAAE;IAC1D,OAAOA,QAAQ;EACnB,CAAC;EACD;EACA;EACAnB,WAAW,CAACK,SAAS,CAACe,gBAAgB,GAAG,UAAUD,QAAQ,EAAE;IACzD,OAAOA,QAAQ;EACnB,CAAC;EACDnB,WAAW,CAACK,SAAS,CAACgB,QAAQ,GAAG,UAAUC,MAAM,EAAE;IAC/C;EACJ,CAAC;EACDtB,WAAW,CAACK,SAAS,CAACkB,EAAE,GAAG,YAAY;IACnC,OAAO,EAAE;EACb,CAAC;EACDvB,WAAW,CAACK,SAAS,CAACmB,MAAM,GAAG,UAAUf,OAAO,EAAE;IAC9C,OAAO,KAAK;EAChB,CAAC;EACD;EACAT,WAAW,CAACK,SAAS,CAACoB,SAAS,GAAG,UAAUhB,OAAO,EAAEG,UAAU,EAAE;IAC7D,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,CAAC,CAACH,OAAO,CAACG,UAAU;IAAE;IAChE,OAAO,IAAI,CAACc,IAAI,CAACvC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC,EAAE;MAAEkB,MAAM,EAAElB,OAAO,CAACmB,EAAE,IAAI,YAAY;MAAEhB,UAAU,EAAEA;IAAW,CAAC,CAAC,CAAC;EACrH,CAAC;EACD;EACAZ,WAAW,CAACK,SAAS,CAACwB,aAAa,GAAG,UAAUpB,OAAO,EAAE;IACrD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIoB,QAAQ,GAAGrB,OAAO,CAACqB,QAAQ;MAAEvB,YAAY,GAAGE,OAAO,CAACF,YAAY;MAAEwB,IAAI,GAAGtB,OAAO,CAACsB,IAAI;MAAEC,EAAE,GAAGvB,OAAO,CAACG,UAAU;MAAEA,UAAU,GAAGoB,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;MAAEC,YAAY,GAAG7C,MAAM,CAACqB,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC9O,IAAIyB,KAAK,GAAG,IAAI,CAAChC,cAAc,CAAC4B,QAAQ,EAAEvB,YAAY,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,IAAIqB,EAAE,GAAG,OAAOG,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAC5DA,IAAI,GACF,IAAI,CAACV,QAAQ,CAACU,IAAI,CAAC;IACzB,IAAII,WAAW,GAAG,CAAC,CAAC1B,OAAO,CAAC2B,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC7D,IAAIC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;MAC9B,IAAIC,kBAAkB,GAAGjC,YAAY,IAAIf,qBAAqB,CAACsC,QAAQ,CAAC,CAACW,IAAI,CAACC,KAAK;MACnF,IAAI,CAACd,EAAE,EAAE;QACLU,UAAU,CAACC,OAAO,KAAK,KAAK,IAAIzC,SAAS,CAAC6C,IAAI,CAAC,CAAC,EAAEH,kBAAkB,CAAC;MACzE;IACJ;IACA,IAAII,WAAW,GAAGzD,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE8C,YAAY,CAAC,EAAE;MAAEY,iBAAiB,EAAE,IAAI;MAAEjB,EAAE,EAAEA,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEtB,UAAU,EAAEA;IAAW,CAAC,CAAC;IACjI,IAAIkC,UAAU;IACd,OAAO,IAAIxD,UAAU,CAAC,UAAUyD,QAAQ,EAAE;MACtC,OAAOrC,KAAK,CAACsC,KAAK,CAAC7D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyD,WAAW,CAAC,EAAE;QAAEK,SAAS,EAAE,IAAI;QAAEC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAE;UAC5F,IAAIC,IAAI,GAAGjB,WAAW,GAClBpC,YAAY,CAACoD,IAAI,CAACE,MAAM,EAAEvB,QAAQ,EAAEpB,KAAK,EAAEH,YAAY,CAAC,GACtD4C,IAAI,CAACE,MAAM;UACjB;UACA;UACAP,UAAU,IACNjD,YAAY,CAACqC,KAAK,EAAE;YAAEkB,IAAI,EAAEN,UAAU,CAACO;UAAO,CAAC,EAAE;YAAED,IAAI,EAAEA;UAAK,CAAC;UAC/D;UACA;UACA3C,OAAO,CAAC6C,SAAS,CAAC,EAAE;YACpB;UACJ;UACA,IAAID,MAAM,GAAG;YACTD,IAAI,EAAEA,IAAI;YACVG,QAAQ,EAAE,CAAC,CAACJ,IAAI,CAACI;UACrB,CAAC;UACD,IAAIJ,IAAI,CAACK,OAAO,EAAE;YACdH,MAAM,CAACG,OAAO,GAAG9D,cAAc,CAACyD,IAAI,CAACK,OAAO,CAACC,GAAG,CAAC,UAAUC,KAAK,EAAE;cAAE,OAAOA,KAAK,CAACF,OAAO;YAAE,CAAC,CAAC,CAAC;UACjG;UACAV,UAAU,GAAG3D,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgE,IAAI,CAAC,EAAE;YAAEE,MAAM,EAAED;UAAK,CAAC,CAAC;UAC3DL,QAAQ,CAACY,IAAI,CAACN,MAAM,CAAC;QACzB;MAAE,CAAC,CAAC,CAAC;IACb,CAAC,CAAC;EACN,CAAC;EACDrD,WAAW,CAACK,SAAS,CAACuD,YAAY,GAAG,UAAUnD,OAAO,EAAEG,UAAU,EAAE;IAChE,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG,CAAC,CAACH,OAAO,CAACG,UAAU;IAAE;IAChE,OAAO,IAAI,CAACc,IAAI,CAACvC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC,EAAE;MAAEyB,KAAK,EAAE,IAAI,CAAChC,cAAc,CAACO,OAAO,CAACqB,QAAQ,EAAErB,OAAO,CAACF,YAAY,CAAC;MAAEoB,MAAM,EAAElB,OAAO,CAACmB,EAAE;MAAEhB,UAAU,EAAEA;IAAW,CAAC,CAAC,CAAC;EACzK,CAAC;EACDZ,WAAW,CAACK,SAAS,CAACwD,UAAU,GAAG,UAAU7B,EAAE,EAAE;IAC7C,IAAIJ,EAAE,GAAGI,EAAE,CAACJ,EAAE;MAAEwB,IAAI,GAAGpB,EAAE,CAACoB,IAAI;MAAE3C,OAAO,GAAGrB,MAAM,CAAC4C,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpE,OAAO,IAAI,CAAC8B,KAAK,CAACC,MAAM,CAACC,MAAM,CAACvD,OAAO,EAAE;MACrCwD,MAAM,EAAErC,EAAE,IAAI,YAAY;MAC1ByB,MAAM,EAAED;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EACDpD,WAAW,CAACK,SAAS,CAAC6D,aAAa,GAAG,UAAUlC,EAAE,EAAE;IAChD,IAAIJ,EAAE,GAAGI,EAAE,CAACJ,EAAE;MAAEwB,IAAI,GAAGpB,EAAE,CAACoB,IAAI;MAAEtB,QAAQ,GAAGE,EAAE,CAACF,QAAQ;MAAEvB,YAAY,GAAGyB,EAAE,CAACzB,YAAY;MAAEE,OAAO,GAAGrB,MAAM,CAAC4C,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACxJ,OAAO,IAAI,CAAC8B,KAAK,CAACC,MAAM,CAACC,MAAM,CAACvD,OAAO,EAAE;MACrCyB,KAAK,EAAE,IAAI,CAAChC,cAAc,CAAC4B,QAAQ,EAAEvB,YAAY,CAAC;MAClD0D,MAAM,EAAErC,EAAE;MACVyB,MAAM,EAAED;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EACDpD,WAAW,CAACK,SAAS,CAAC8D,WAAW,GAAG,UAAU1D,OAAO,EAAEM,MAAM,EAAE;IAC3D,OAAO,IAAI,CAACP,KAAK,CAAC;MACdO,MAAM,EAAE,SAAAA,CAAUX,KAAK,EAAE;QACrB,IAAIsC,KAAK,GAAGtC,KAAK,CAACqB,SAAS,CAAChB,OAAO,CAAC;QACpC,IAAI2C,IAAI,GAAGrC,MAAM,CAAC2B,KAAK,CAAC;QACxB,IAAIU,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,KAAK,IAAI,EAChC,OAAOV,KAAK;QAChBtC,KAAK,CAACyD,UAAU,CAAC1E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC,EAAE;UAAE2C,IAAI,EAAEA;QAAK,CAAC,CAAC,CAAC;QACjE,OAAOA,IAAI;MACf;IACJ,CAAC,CAAC;EACN,CAAC;EACDpD,WAAW,CAACK,SAAS,CAAC+D,cAAc,GAAG,UAAU3D,OAAO,EAAEM,MAAM,EAAE;IAC9D,OAAO,IAAI,CAACP,KAAK,CAAC;MACdO,MAAM,EAAE,SAAAA,CAAUX,KAAK,EAAE;QACrB,IAAIsC,KAAK,GAAGtC,KAAK,CAACwD,YAAY,CAACnD,OAAO,CAAC;QACvC,IAAI2C,IAAI,GAAGrC,MAAM,CAAC2B,KAAK,CAAC;QACxB,IAAIU,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,KAAK,IAAI,EAChC,OAAOV,KAAK;QAChBtC,KAAK,CAAC8D,aAAa,CAAC/E,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC,EAAE;UAAE2C,IAAI,EAAEA;QAAK,CAAC,CAAC,CAAC;QACpE,OAAOA,IAAI;MACf;IACJ,CAAC,CAAC;EACN,CAAC;EACD,OAAOpD,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,IAAIsC,UAAU,CAACC,OAAO,KAAK,KAAK,EAAE;EAC9BvC,WAAW,CAACK,SAAS,CAACgE,kBAAkB,GAAGzE,6BAA6B;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}